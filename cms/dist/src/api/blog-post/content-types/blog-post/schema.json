{"kind": "collectionType", "collectionName": "blog_posts", "info": {"singularName": "blog-post", "pluralName": "blog-posts", "displayName": "Blog Post", "description": "Blog posts for the website"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"title": {"type": "string", "required": true, "maxLength": 255}, "slug": {"type": "uid", "targetField": "title", "required": true}, "excerpt": {"type": "text", "maxLength": 500}, "content": {"type": "richtext", "required": true}, "featuredImage": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images"]}, "author": {"type": "string", "default": "Admin"}, "tags": {"type": "json"}, "metaTitle": {"type": "string", "maxLength": 60}, "metaDescription": {"type": "text", "maxLength": 160}, "readingTime": {"type": "integer", "min": 1}}}