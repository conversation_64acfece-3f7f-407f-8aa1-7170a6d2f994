{"version": 3, "sources": ["../../../@strapi/admin/admin/src/hooks/useControllableState.ts", "../../../@strapi/admin/admin/src/components/Table.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { useCallbackRef } from '@strapi/design-system';\n\ntype UseControllableStateParams<T> = {\n  prop?: T | undefined;\n  defaultProp?: T | undefined;\n  onChange?: (state: T) => void;\n};\n\ntype SetStateFn<T> = (prevState?: T) => T;\n\nfunction useControllableState<T>({\n  prop,\n  defaultProp,\n  onChange = () => {},\n}: UseControllableStateParams<T>) {\n  const [uncontrolledProp, setUncontrolledProp] = useUncontrolledState({ defaultProp, onChange });\n  const isControlled = prop !== undefined;\n  const value = isControlled ? prop : uncontrolledProp;\n  const handleChange = useCallbackRef(onChange);\n\n  const setValue: React.Dispatch<React.SetStateAction<T | undefined>> = React.useCallback(\n    (nextValue) => {\n      if (isControlled) {\n        const setter = nextValue as SetStateFn<T>;\n        const value = typeof nextValue === 'function' ? setter(prop) : nextValue;\n        if (value !== prop) handleChange(value as T);\n      } else {\n        setUncontrolledProp(nextValue);\n      }\n    },\n    [isControlled, prop, setUncontrolledProp, handleChange]\n  );\n\n  return [value, setValue] as const;\n}\n\nfunction useUncontrolledState<T>({\n  defaultProp,\n  onChange,\n}: Omit<UseControllableStateParams<T>, 'prop'>) {\n  const uncontrolledState = React.useState<T | undefined>(defaultProp);\n  const [value] = uncontrolledState;\n  const prevValueRef = React.useRef(value);\n  const handleChange = useCallbackRef(onChange);\n\n  React.useEffect(() => {\n    if (prevValueRef.current !== value) {\n      handleChange(value as T);\n      prevValueRef.current = value;\n    }\n  }, [value, prevValueRef, handleChange]);\n\n  return uncontrolledState;\n}\n\nexport { useControllableState };\n", "/**\n * TODO: honestly, half of this stuff should come straight from\n * the design-system and then we can just wrap round the bits for\n * the i18n & router interactions.\n *\n * So we'll do that in v2 of the DS.\n */\n\nimport * as React from 'react';\n\nimport {\n  Flex,\n  Typography,\n  Th,\n  Tbody,\n  Td,\n  Tooltip,\n  IconButton,\n  Thead,\n  Tr,\n  RawTrProps,\n  Checkbox,\n  Loader,\n  Table as DSTable,\n  EmptyStateLayout,\n  EmptyStateLayoutProps,\n  TableProps,\n  RawTdProps,\n} from '@strapi/design-system';\nimport { CaretDown } from '@strapi/icons';\nimport { EmptyDocuments } from '@strapi/icons/symbols';\nimport { useIntl } from 'react-intl';\nimport { styled } from 'styled-components';\n\nimport { useControllableState } from '../hooks/useControllableState';\nimport { useQueryParams } from '../hooks/useQueryParams';\n\nimport { createContext } from './Context';\n\n/* -------------------------------------------------------------------------------------------------\n * Root\n * -----------------------------------------------------------------------------------------------*/\n\ninterface BaseRow {\n  id: string | number;\n  [key: string]: any;\n}\n\ninterface TableHeader<TData = object, THeader = object> {\n  /**\n   * Typically used by plugins to render a custom cell\n   */\n  cellFormatter?: (data: TData, header: Omit<THeader, 'cellFormatter'>) => React.ReactNode;\n  label: string;\n  name: string;\n  searchable?: boolean;\n  sortable?: boolean;\n}\n\ninterface TableContextValue<TRow extends BaseRow, THeader extends TableHeader<TRow, THeader>>\n  extends Pick<TableProps, 'footer'> {\n  colCount: number;\n  hasHeaderCheckbox: boolean;\n  headers: THeader[];\n  isLoading: boolean;\n  rowCount: number;\n  rows: TRow[];\n  setHasHeaderCheckbox: (value: boolean) => void;\n  selectedRows: TRow[];\n  selectRow: (row: TRow | TRow[]) => void;\n}\n\nconst [TableProvider, useTable] = createContext<TableContextValue<any, any>>('Table');\n\ninterface RootProps<TRow extends BaseRow, THeader extends TableHeader<TRow, THeader>>\n  extends Partial<\n    Pick<\n      TableContextValue<TRow, THeader>,\n      'footer' | 'headers' | 'isLoading' | 'rows' | 'selectedRows'\n    >\n  > {\n  children?: React.ReactNode;\n  defaultSelectedRows?: TRow[];\n  onSelectedRowsChange?: (selectedRows: TRow[]) => void;\n}\n\nconst Root = <TRow extends BaseRow, THeader extends TableHeader<TRow, THeader>>({\n  children,\n  defaultSelectedRows,\n  footer,\n  headers = [],\n  isLoading = false,\n  onSelectedRowsChange,\n  rows = [],\n  selectedRows: selectedRowsProps,\n}: RootProps<TRow, THeader>) => {\n  const [selectedRows = [], setSelectedRows] = useControllableState({\n    prop: selectedRowsProps,\n    defaultProp: defaultSelectedRows,\n    onChange: onSelectedRowsChange,\n  });\n  const [hasHeaderCheckbox, setHasHeaderCheckbox] = React.useState(false);\n\n  const rowCount = rows.length + 1;\n  const colCount = hasHeaderCheckbox ? headers.length + 1 : headers.length;\n\n  const selectRow: TableContextValue<TRow, THeader>['selectRow'] = (row) => {\n    if (Array.isArray(row)) {\n      setSelectedRows(row);\n    } else {\n      setSelectedRows((prev = []) => {\n        const currentRowIndex = prev.findIndex((r) => r.id === row.id);\n        if (currentRowIndex > -1) {\n          return prev.toSpliced(currentRowIndex, 1);\n        }\n\n        return [...prev, row];\n      });\n    }\n  };\n\n  return (\n    <TableProvider\n      colCount={colCount}\n      hasHeaderCheckbox={hasHeaderCheckbox}\n      setHasHeaderCheckbox={setHasHeaderCheckbox}\n      footer={footer}\n      headers={headers}\n      isLoading={isLoading}\n      rowCount={rowCount}\n      rows={rows}\n      selectedRows={selectedRows}\n      selectRow={selectRow}\n    >\n      {children}\n    </TableProvider>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Content\n * -----------------------------------------------------------------------------------------------*/\n\nconst Content = ({ children }: Table.ContentProps) => {\n  const rowCount = useTable('Content', (state) => state.rowCount);\n  const colCount = useTable('Content', (state) => state.colCount);\n  const footer = useTable('Content', (state) => state.footer);\n\n  return (\n    <DSTable rowCount={rowCount} colCount={colCount} footer={footer}>\n      {children}\n    </DSTable>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Head\n * -----------------------------------------------------------------------------------------------*/\n\nconst Head = ({ children }: Table.HeadProps) => {\n  return (\n    <Thead>\n      <Tr>{children}</Tr>\n    </Thead>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * HeaderCell\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * @description A header cell in your table, internally will set the query params for sorting to\n * be passed to your data-fetching function.\n */\nconst HeaderCell = <TData, THead>({ name, label, sortable }: TableHeader<TData, THead>) => {\n  const [{ query }, setQuery] = useQueryParams<{ sort?: `${string}:${'ASC' | 'DESC'}` }>();\n  const sort = query?.sort ?? '';\n  const [sortBy, sortOrder] = sort.split(':');\n  const { formatMessage } = useIntl();\n  const isSorted = sortBy === name;\n\n  const sortLabel = formatMessage(\n    { id: 'components.TableHeader.sort', defaultMessage: 'Sort on {label}' },\n    { label }\n  );\n\n  const handleClickSort = () => {\n    if (sortable) {\n      setQuery({\n        sort: `${name}:${isSorted && sortOrder === 'ASC' ? 'DESC' : 'ASC'}`,\n      });\n    }\n  };\n\n  return (\n    <Th\n      action={\n        isSorted &&\n        sortable && (\n          <IconButton label={sortLabel} onClick={handleClickSort} variant=\"ghost\">\n            <SortIcon $isUp={sortOrder === 'ASC'} />\n          </IconButton>\n        )\n      }\n    >\n      <Tooltip label={sortable ? sortLabel : label}>\n        <Typography\n          textColor=\"neutral600\"\n          tag={!isSorted && sortable ? 'button' : 'span'}\n          onClick={handleClickSort}\n          variant=\"sigma\"\n        >\n          {label}\n        </Typography>\n      </Tooltip>\n    </Th>\n  );\n};\n\nconst SortIcon = styled(CaretDown)<{\n  $isUp: boolean;\n}>`\n  transform: ${({ $isUp }) => `rotate(${$isUp ? '180' : '0'}deg)`};\n`;\n\n/* -------------------------------------------------------------------------------------------------\n * ActionBar\n * -----------------------------------------------------------------------------------------------*/\n\nconst ActionBar = ({ children }: Table.ActionBarProps) => {\n  const { formatMessage } = useIntl();\n  const selectedRows = useTable('ActionBar', (state) => state.selectedRows);\n\n  if (selectedRows.length === 0) return null;\n\n  return (\n    <Flex gap={2}>\n      <Typography variant=\"omega\" textColor=\"neutral500\">\n        {formatMessage(\n          {\n            id: 'content-manager.components.TableDelete.label',\n            defaultMessage: '{number, plural, one {# row} other {# rows}} selected',\n          },\n          { number: selectedRows.length }\n        )}\n      </Typography>\n      {children}\n    </Flex>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * HeaderCheckboxCell\n * -----------------------------------------------------------------------------------------------*/\n\nconst HeaderCheckboxCell = () => {\n  const rows = useTable('HeaderCheckboxCell', (state) => state.rows);\n  const selectedRows = useTable('HeaderCheckboxCell', (state) => state.selectedRows);\n  const selectRow = useTable('HeaderCheckboxCell', (state) => state.selectRow);\n  const setHasHeaderCheckbox = useTable(\n    'HeaderCheckboxCell',\n    (state) => state.setHasHeaderCheckbox\n  );\n\n  const { formatMessage } = useIntl();\n\n  const areAllEntriesSelected = selectedRows.length === rows.length && rows.length > 0;\n  const isIndeterminate = !areAllEntriesSelected && selectedRows.length > 0;\n\n  React.useEffect(() => {\n    setHasHeaderCheckbox(true);\n\n    return () => setHasHeaderCheckbox(false);\n  }, [setHasHeaderCheckbox]);\n\n  const handleSelectAll = () => {\n    if (!areAllEntriesSelected) {\n      selectRow(rows);\n    } else {\n      selectRow([]);\n    }\n  };\n\n  return (\n    <Th>\n      <Checkbox\n        aria-label={formatMessage({\n          id: 'global.select-all-entries',\n          defaultMessage: 'Select all entries',\n        })}\n        disabled={rows.length === 0}\n        checked={isIndeterminate ? 'indeterminate' : areAllEntriesSelected}\n        onCheckedChange={handleSelectAll}\n      />\n    </Th>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Empty\n * -----------------------------------------------------------------------------------------------*/\n\nconst Empty = (props: Table.EmptyProps) => {\n  const { formatMessage } = useIntl();\n\n  const rows = useTable('Empty', (state) => state.rows);\n  const isLoading = useTable('Empty', (state) => state.isLoading);\n  const colCount = useTable('Empty', (state) => state.colCount);\n\n  /**\n   * If we're loading or we have some data, we don't show the empty state.\n   */\n  if (rows.length > 0 || isLoading) {\n    return null;\n  }\n\n  return (\n    <Tbody>\n      <Tr>\n        <Td colSpan={colCount}>\n          <EmptyStateLayout\n            content={formatMessage({\n              id: 'app.components.EmptyStateLayout.content-document',\n              defaultMessage: 'No content found',\n            })}\n            hasRadius\n            icon={<EmptyDocuments width=\"16rem\" />}\n            {...props}\n          />\n        </Td>\n      </Tr>\n    </Tbody>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * LoadingBody\n * -----------------------------------------------------------------------------------------------*/\n\nconst Loading = ({ children = 'Loading content' }: Table.LoadingProps) => {\n  const isLoading = useTable('Loading', (state) => state.isLoading);\n  const colCount = useTable('Loading', (state) => state.colCount);\n\n  if (!isLoading) {\n    return null;\n  }\n\n  return (\n    <Tbody>\n      <Tr>\n        <Td colSpan={colCount}>\n          <Flex justifyContent=\"center\" padding={11} background=\"neutral0\">\n            <Loader>{children}</Loader>\n          </Flex>\n        </Td>\n      </Tr>\n    </Tbody>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Body\n * -----------------------------------------------------------------------------------------------*/\n\nconst Body = ({ children }: Table.BodyProps) => {\n  const isLoading = useTable('Body', (state) => state.isLoading);\n  const rows = useTable('Body', (state) => state.rows);\n\n  if (isLoading || rows.length === 0) {\n    return null;\n  }\n\n  return <Tbody>{children}</Tbody>;\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Row\n * -----------------------------------------------------------------------------------------------*/\nconst Row = (props: Table.RowProps) => {\n  return <Tr {...props} />;\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Cell\n * -----------------------------------------------------------------------------------------------*/\nconst Cell = (props: Table.CellProps) => {\n  return <Td {...props} />;\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Row\n * -----------------------------------------------------------------------------------------------*/\nconst CheckboxCell = ({ id, ...props }: Table.CheckboxCellProps) => {\n  const rows = useTable('CheckboxCell', (state) => state.rows);\n  const selectedRows = useTable('CheckboxCell', (state) => state.selectedRows);\n  const selectRow = useTable('CheckboxCell', (state) => state.selectRow);\n\n  const { formatMessage } = useIntl();\n\n  const handleSelectRow = () => {\n    selectRow(rows.find((row) => row.id === id));\n  };\n\n  const isChecked = selectedRows.findIndex((row) => row.id === id) > -1;\n\n  return (\n    <Cell {...props} onClick={(e) => e.stopPropagation()}>\n      <Checkbox\n        aria-label={formatMessage(\n          {\n            id: 'app.component.table.select.one-entry',\n            defaultMessage: `Select {target}`,\n          },\n          { target: id }\n        )}\n        disabled={rows.length === 0}\n        checked={isChecked}\n        onCheckedChange={handleSelectRow}\n      />\n    </Cell>\n  );\n};\n/* -------------------------------------------------------------------------------------------------\n * Exports\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * @alpha we may move this component to the design-system.\n * @public\n * @description A generic table component composition. Internally handles the state of the table\n * such as selected rows, loading state, and more assuming the correct pieces are put togther.\n * @example\n * ```tsx\n * interace Data {\n *  id: string;\n *  name: string;\n *  email: string;\n * }\n *\n * const ListView = () => {\n *  const { data, isLoading } = useGetData<Data>();\n *\n *  const headers: Table.Header<Data>[] = [\n *    {\n *      label: 'Name',\n *      name: 'name',\n *      sortable: true,\n *    },\n *    {\n *      label: 'Email',\n *      name: 'email',\n *      sortable: true,\n *    },\n *  ];\n *\n *  return (\n *    <Table.Root rows={data} headers={headers} isLoading={isLoading}>\n *      <Table.Content>\n *        <Table.Head>\n *          {headers.map((head) => (\n *            <Table.HeaderCell key={head.name} {...head} />\n *          ))}\n *        </Table.Head>\n *        <Table.Body>\n *          <Table.Loading />\n *          <Table.Empty />\n *          {data.map((row) => (\n *            <Table.Row key={row.id}>\n *              <Table.Cell>{row.name}</Table.Cell>\n *              <Table.Cell>{row.email}</Table.Cell>\n *            </Table.Row>\n *          ))}\n *        </Table.Body>\n *      </Table.Content>\n *    </Table.Root>\n *  );\n * };\n * ```\n */\nconst Table = {\n  Root,\n  Content,\n  ActionBar,\n  Head,\n  HeaderCell,\n  HeaderCheckboxCell,\n  Body,\n  CheckboxCell,\n  Cell,\n  Row,\n  Loading,\n  Empty,\n};\n\n// eslint-disable-next-line @typescript-eslint/no-namespace\nnamespace Table {\n  export type Props<\n    TData extends BaseRow,\n    THeader extends TableHeader<TData, THeader> = TableHeader<TData, TableHeader>,\n  > = RootProps<TData, THeader>;\n  export interface ActionBarProps {\n    children?: React.ReactNode;\n  }\n\n  export interface ContentProps {\n    children: React.ReactNode;\n  }\n\n  export type Header<TData, THeader> = TableHeader<TData, THeader>;\n\n  export interface HeadProps {\n    children: React.ReactNode;\n  }\n\n  export interface EmptyProps extends Partial<EmptyStateLayoutProps> {}\n\n  export interface LoadingProps {\n    children?: React.ReactNode;\n  }\n\n  export interface BodyProps {\n    children: React.ReactNode;\n  }\n\n  export interface RowProps extends RawTrProps {}\n\n  export interface CellProps extends RawTdProps {}\n\n  export interface CheckboxCellProps extends Pick<BaseRow, 'id'>, Omit<RawTdProps, 'id'> {}\n}\n\nexport { Table, useTable };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYA,SAASA,qBAAwB,EAC/BC,MACAC,aACAC,WAAW,MAAO;AAAA,EAAC,GACW;AAC9B,QAAM,CAACC,kBAAkBC,mBAAoB,IAAGC,qBAAqB;IAAEJ;IAAaC;EAAS,CAAA;AAC7F,QAAMI,eAAeN,SAASO;AAC9B,QAAMC,QAAQF,eAAeN,OAAOG;AACpC,QAAMM,eAAeC,eAAeR,QAAAA;AAEpC,QAAMS,WAAsEC,kBAC1E,CAACC,cAAAA;AACC,QAAIP,cAAc;AAChB,YAAMQ,SAASD;AACf,YAAML,SAAQ,OAAOK,cAAc,aAAaC,OAAOd,IAAQa,IAAAA;AAC/D,UAAIL,WAAUR,KAAMS,cAAaD,MAAAA;WAC5B;AACLJ,0BAAoBS,SAAAA;IACtB;KAEF;IAACP;IAAcN;IAAMI;IAAqBK;EAAa,CAAA;AAGzD,SAAO;IAACD;IAAOG;EAAS;AAC1B;AAEA,SAASN,qBAAwB,EAC/BJ,aACAC,SAAQ,GACoC;AAC5C,QAAMa,oBAA0BC,eAAwBf,WAAAA;AACxD,QAAM,CAACO,KAAAA,IAASO;AAChB,QAAME,eAAqBC,aAAOV,KAAAA;AAClC,QAAMC,eAAeC,eAAeR,QAAAA;AAEpCiB,EAAMC,gBAAU,MAAA;AACd,QAAIH,aAAaI,YAAYb,OAAO;AAClCC,mBAAaD,KAAAA;AACbS,mBAAaI,UAAUb;IACzB;KACC;IAACA;IAAOS;IAAcR;EAAa,CAAA;AAEtC,SAAOM;AACT;;;ACiBA,IAAM,CAACO,eAAeC,QAAS,IAAGC,cAA2C,OAAA;AAc7E,IAAMC,OAAO,CAAmE,EAC9EC,UACAC,qBACAC,QACAC,UAAU,CAAA,GACVC,YAAY,OACZC,sBACAC,OAAO,CAAA,GACPC,cAAcC,kBAAiB,MACN;AACzB,QAAM,CAACD,eAAe,CAAA,GAAIE,eAAAA,IAAmBC,qBAAqB;IAChEC,MAAMH;IACNI,aAAaX;IACbY,UAAUR;EACZ,CAAA;AACA,QAAM,CAACS,mBAAmBC,oBAAAA,IAA8BC,gBAAS,KAAA;AAEjE,QAAMC,WAAWX,KAAKY,SAAS;AAC/B,QAAMC,WAAWL,oBAAoBX,QAAQe,SAAS,IAAIf,QAAQe;AAElE,QAAME,YAA2D,CAACC,QAAAA;AAChE,QAAIC,MAAMC,QAAQF,GAAM,GAAA;AACtBZ,sBAAgBY,GAAAA;WACX;AACLZ,sBAAgB,CAACe,OAAO,CAAA,MAAE;AACxB,cAAMC,kBAAkBD,KAAKE,UAAU,CAACC,MAAMA,EAAEC,OAAOP,IAAIO,EAAE;AAC7D,YAAIH,kBAAkB,IAAI;AACxB,iBAAOD,KAAKK,UAAUJ,iBAAiB,CAAA;QACzC;AAEA,eAAO;UAAID,GAAAA;UAAMH;QAAI;MACvB,CAAA;IACF;EACF;AAEA,aACES,wBAAClC,eAAAA;IACCuB;IACAL;IACAC;IACAb;IACAC;IACAC;IACAa;IACAX;IACAC;IACAa;IAECpB;;AAGP;AAMA,IAAM+B,UAAU,CAAC,EAAE/B,SAAQ,MAAsB;AAC/C,QAAMiB,WAAWpB,SAAS,WAAW,CAACmC,UAAUA,MAAMf,QAAQ;AAC9D,QAAME,WAAWtB,SAAS,WAAW,CAACmC,UAAUA,MAAMb,QAAQ;AAC9D,QAAMjB,SAASL,SAAS,WAAW,CAACmC,UAAUA,MAAM9B,MAAM;AAE1D,aACE4B,wBAACG,OAAAA;IAAQhB;IAAoBE;IAAoBjB;IAC9CF;;AAGP;AAMA,IAAMkC,OAAO,CAAC,EAAElC,SAAQ,MAAmB;AACzC,aACE8B,wBAACK,OAAAA;IACC,cAAAL,wBAACM,IAAAA;MAAIpC;;;AAGX;AAUA,IAAMqC,aAAa,CAAe,EAAEC,MAAMC,OAAOC,SAAQ,MAA6B;AACpF,QAAM,CAAC,EAAEC,MAAK,GAAIC,QAAAA,IAAYC,eAAAA;AAC9B,QAAMC,QAAOH,+BAAOG,SAAQ;AAC5B,QAAM,CAACC,QAAQC,SAAAA,IAAaF,KAAKG,MAAM,GAAA;AACvC,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAMC,WAAWL,WAAWP;AAE5B,QAAMa,YAAYH,cAChB;IAAEpB,IAAI;IAA+BwB,gBAAgB;KACrD;IAAEb;EAAM,CAAA;AAGV,QAAMc,kBAAkB,MAAA;AACtB,QAAIb,UAAU;AACZE,eAAS;QACPE,MAAM,GAAGN,IAAAA,IAAQY,YAAYJ,cAAc,QAAQ,SAAS,KAAA;MAC9D,CAAA;IACF;EACF;AAEA,aACEhB,wBAACwB,IAAAA;IACCC,QACEL,YACAV,gBACEV,wBAAC0B,YAAAA;MAAWjB,OAAOY;MAAWM,SAASJ;MAAiBK,SAAQ;MAC9D,cAAA5B,wBAAC6B,UAAAA;QAASC,OAAOd,cAAc;;;IAKrC,cAAAhB,wBAAC+B,aAAAA;MAAQtB,OAAOC,WAAWW,YAAYZ;MACrC,cAAAT,wBAACgC,YAAAA;QACCC,WAAU;QACVC,KAAK,CAACd,YAAYV,WAAW,WAAW;QACxCiB,SAASJ;QACTK,SAAQ;QAEPnB,UAAAA;;;;AAKX;AAEA,IAAMoB,WAAWM,GAAOC,aAAAA;eAGT,CAAC,EAAEN,MAAK,MAAO,UAAUA,QAAQ,QAAQ,GAAI,MAAK;;AAOjE,IAAMO,YAAY,CAAC,EAAEnE,SAAQ,MAAwB;AACnD,QAAM,EAAEgD,cAAa,IAAKC,QAAAA;AAC1B,QAAM1C,eAAeV,SAAS,aAAa,CAACmC,UAAUA,MAAMzB,YAAY;AAExE,MAAIA,aAAaW,WAAW,EAAG,QAAO;AAEtC,aACEkD,yBAACC,MAAAA;IAAKC,KAAK;;UACTxC,wBAACgC,YAAAA;QAAWJ,SAAQ;QAAQK,WAAU;kBACnCf,cACC;UACEpB,IAAI;UACJwB,gBAAgB;WAElB;UAAEmB,QAAQhE,aAAaW;QAAO,CAAA;;MAGjClB;;;AAGP;AAMA,IAAMwE,qBAAqB,MAAA;AACzB,QAAMlE,OAAOT,SAAS,sBAAsB,CAACmC,UAAUA,MAAM1B,IAAI;AACjE,QAAMC,eAAeV,SAAS,sBAAsB,CAACmC,UAAUA,MAAMzB,YAAY;AACjF,QAAMa,YAAYvB,SAAS,sBAAsB,CAACmC,UAAUA,MAAMZ,SAAS;AAC3E,QAAML,uBAAuBlB,SAC3B,sBACA,CAACmC,UAAUA,MAAMjB,oBAAoB;AAGvC,QAAM,EAAEiC,cAAa,IAAKC,QAAAA;AAE1B,QAAMwB,wBAAwBlE,aAAaW,WAAWZ,KAAKY,UAAUZ,KAAKY,SAAS;AACnF,QAAMwD,kBAAkB,CAACD,yBAAyBlE,aAAaW,SAAS;AAExEyD,EAAMC,iBAAU,MAAA;AACd7D,yBAAqB,IAAA;AAErB,WAAO,MAAMA,qBAAqB,KAAA;KACjC;IAACA;EAAqB,CAAA;AAEzB,QAAM8D,kBAAkB,MAAA;AACtB,QAAI,CAACJ,uBAAuB;AAC1BrD,gBAAUd,IAAAA;WACL;AACLc,gBAAU,CAAA,CAAE;IACd;EACF;AAEA,aACEU,wBAACwB,IAAAA;IACC,cAAAxB,wBAACgD,cAAAA;MACCC,cAAY/B,cAAc;QACxBpB,IAAI;QACJwB,gBAAgB;MAClB,CAAA;MACA4B,UAAU1E,KAAKY,WAAW;MAC1B+D,SAASP,kBAAkB,kBAAkBD;MAC7CS,iBAAiBL;;;AAIzB;AAMA,IAAMM,QAAQ,CAACC,UAAAA;AACb,QAAM,EAAEpC,cAAa,IAAKC,QAAAA;AAE1B,QAAM3C,OAAOT,SAAS,SAAS,CAACmC,UAAUA,MAAM1B,IAAI;AACpD,QAAMF,YAAYP,SAAS,SAAS,CAACmC,UAAUA,MAAM5B,SAAS;AAC9D,QAAMe,WAAWtB,SAAS,SAAS,CAACmC,UAAUA,MAAMb,QAAQ;AAK5D,MAAIb,KAAKY,SAAS,KAAKd,WAAW;AAChC,WAAO;EACT;AAEA,aACE0B,wBAACuD,OAAAA;IACC,cAAAvD,wBAACM,IAAAA;MACC,cAAAN,wBAACwD,IAAAA;QAAGC,SAASpE;QACX,cAAAW,wBAAC0D,kBAAAA;UACCC,SAASzC,cAAc;YACrBpB,IAAI;YACJwB,gBAAgB;UAClB,CAAA;UACAsC,WAAS;UACTC,UAAM7D,wBAAC8D,cAAAA;YAAeC,OAAM;;UAC3B,GAAGT;;;;;AAMhB;AAMA,IAAMU,UAAU,CAAC,EAAE9F,WAAW,kBAAiB,MAAsB;AACnE,QAAMI,YAAYP,SAAS,WAAW,CAACmC,UAAUA,MAAM5B,SAAS;AAChE,QAAMe,WAAWtB,SAAS,WAAW,CAACmC,UAAUA,MAAMb,QAAQ;AAE9D,MAAI,CAACf,WAAW;AACd,WAAO;EACT;AAEA,aACE0B,wBAACuD,OAAAA;IACC,cAAAvD,wBAACM,IAAAA;MACC,cAAAN,wBAACwD,IAAAA;QAAGC,SAASpE;QACX,cAAAW,wBAACuC,MAAAA;UAAK0B,gBAAe;UAASC,SAAS;UAAIC,YAAW;UACpD,cAAAnE,wBAACoE,QAAAA;YAAQlG;;;;;;AAMrB;AAMA,IAAMmG,OAAO,CAAC,EAAEnG,SAAQ,MAAmB;AACzC,QAAMI,YAAYP,SAAS,QAAQ,CAACmC,UAAUA,MAAM5B,SAAS;AAC7D,QAAME,OAAOT,SAAS,QAAQ,CAACmC,UAAUA,MAAM1B,IAAI;AAEnD,MAAIF,aAAaE,KAAKY,WAAW,GAAG;AAClC,WAAO;EACT;AAEA,aAAOY,wBAACuD,OAAAA;IAAOrF;;AACjB;AAKA,IAAMoG,MAAM,CAAChB,UAAAA;AACX,aAAOtD,wBAACM,IAAAA;IAAI,GAAGgD;;AACjB;AAKA,IAAMiB,OAAO,CAACjB,UAAAA;AACZ,aAAOtD,wBAACwD,IAAAA;IAAI,GAAGF;;AACjB;AAKA,IAAMkB,eAAe,CAAC,EAAE1E,IAAI,GAAGwD,MAAgC,MAAA;AAC7D,QAAM9E,OAAOT,SAAS,gBAAgB,CAACmC,UAAUA,MAAM1B,IAAI;AAC3D,QAAMC,eAAeV,SAAS,gBAAgB,CAACmC,UAAUA,MAAMzB,YAAY;AAC3E,QAAMa,YAAYvB,SAAS,gBAAgB,CAACmC,UAAUA,MAAMZ,SAAS;AAErE,QAAM,EAAE4B,cAAa,IAAKC,QAAAA;AAE1B,QAAMsD,kBAAkB,MAAA;AACtBnF,cAAUd,KAAKkG,KAAK,CAACnF,QAAQA,IAAIO,OAAOA,EAAAA,CAAAA;EAC1C;AAEA,QAAM6E,YAAYlG,aAAamB,UAAU,CAACL,QAAQA,IAAIO,OAAOA,EAAAA,IAAM;AAEnE,aACEE,wBAACuE,MAAAA;IAAM,GAAGjB;IAAO3B,SAAS,CAACiD,MAAMA,EAAEC,gBAAe;IAChD,cAAA7E,wBAACgD,cAAAA;MACCC,cAAY/B,cACV;QACEpB,IAAI;QACJwB,gBAAgB;SAElB;QAAEwD,QAAQhF;MAAG,CAAA;MAEfoD,UAAU1E,KAAKY,WAAW;MAC1B+D,SAASwB;MACTvB,iBAAiBqB;;;AAIzB;AAyDC,IACKM,SAAQ;EACZ9G;EACAgC;EACAoC;EACAjC;EACAG;EACAmC;EACA2B;EACAG;EACAD;EACAD;EACAN;EACAX;AACF;", "names": ["useControllableState", "prop", "defaultProp", "onChange", "uncontrolledProp", "setUncontrolledProp", "useUncontrolledState", "isControlled", "undefined", "value", "handleChange", "useCallbackRef", "setValue", "useCallback", "nextValue", "setter", "uncontrolledState", "useState", "prevValueRef", "useRef", "React", "useEffect", "current", "TableProvider", "useTable", "createContext", "Root", "children", "defaultSelectedRows", "footer", "headers", "isLoading", "onSelectedRowsChange", "rows", "selectedRows", "selectedRowsProps", "setSelectedRows", "useControllableState", "prop", "defaultProp", "onChange", "hasHeaderCheckbox", "setHasHeaderCheckbox", "useState", "rowCount", "length", "col<PERSON>ount", "selectRow", "row", "Array", "isArray", "prev", "currentRowIndex", "findIndex", "r", "id", "toSpliced", "_jsx", "Content", "state", "DSTable", "Head", "<PERSON><PERSON>", "Tr", "<PERSON><PERSON><PERSON><PERSON>", "name", "label", "sortable", "query", "<PERSON><PERSON><PERSON><PERSON>", "useQueryParams", "sort", "sortBy", "sortOrder", "split", "formatMessage", "useIntl", "isSorted", "sortLabel", "defaultMessage", "handleClickSort", "Th", "action", "IconButton", "onClick", "variant", "SortIcon", "$isUp", "<PERSON><PERSON><PERSON>", "Typography", "textColor", "tag", "styled", "CaretDown", "ActionBar", "_jsxs", "Flex", "gap", "number", "HeaderCheckboxCell", "areAllEntriesSelected", "isIndeterminate", "React", "useEffect", "handleSelectAll", "Checkbox", "aria-label", "disabled", "checked", "onCheckedChange", "Empty", "props", "Tbody", "Td", "colSpan", "EmptyStateLayout", "content", "hasRadius", "icon", "EmptyDocuments", "width", "Loading", "justifyContent", "padding", "background", "Loader", "Body", "Row", "Cell", "CheckboxCell", "handleSelectRow", "find", "isChecked", "e", "stopPropagation", "target", "Table"]}