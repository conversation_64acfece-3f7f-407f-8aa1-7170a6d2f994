{"version": 3, "sources": ["../../../@strapi/admin/ee/admin/src/hooks/useLicenseLimitNotification.ts"], "sourcesContent": ["/**\n *\n * useLicenseLimitNotification\n *\n */\nimport * as React from 'react';\n\nimport isNil from 'lodash/isNil';\nimport { useIntl } from 'react-intl';\nimport { useLocation } from 'react-router-dom';\n\nimport { NotificationConfig, useNotification } from '../../../../admin/src/features/Notifications';\n\nimport { useLicenseLimits } from './useLicenseLimits';\n\nconst STORAGE_KEY_PREFIX = 'strapi-notification-seat-limit';\n\nconst BILLING_SELF_HOSTED_URL = 'https://strapi.io/billing/request-seats';\nconst MANAGE_SEATS_URL = 'https://strapi.io/billing/manage-seats';\n\nexport const useLicenseLimitNotification = () => {\n  const { formatMessage } = useIntl();\n  const { license, isError, isLoading } = useLicenseLimits();\n  const { toggleNotification } = useNotification();\n  const { pathname } = useLocation();\n\n  const { enforcementUserCount, permittedSeats, licenseLimitStatus, type } = license ?? {};\n\n  React.useEffect(() => {\n    if (isError || isLoading) {\n      return;\n    }\n\n    const shouldDisplayNotification =\n      !isNil(permittedSeats) &&\n      !window.sessionStorage.getItem(`${STORAGE_KEY_PREFIX}-${pathname}`) &&\n      licenseLimitStatus === 'OVER_LIMIT';\n\n    let notificationType: NotificationConfig['type'];\n\n    if (licenseLimitStatus === 'OVER_LIMIT') {\n      notificationType = 'danger';\n    }\n\n    if (shouldDisplayNotification) {\n      toggleNotification({\n        type: notificationType,\n        message: formatMessage(\n          {\n            id: 'notification.ee.warning.over-.message',\n            defaultMessage:\n              \"Add seats to {licenseLimitStatus, select, OVER_LIMIT {invite} other {re-enable}} Users. If you already did it but it's not reflected in Strapi yet, make sure to restart your app.\",\n          },\n          { licenseLimitStatus }\n        ),\n        title: formatMessage(\n          {\n            id: 'notification.ee.warning.at-seat-limit.title',\n            defaultMessage:\n              '{licenseLimitStatus, select, OVER_LIMIT {Over} other {At}} seat limit ({enforcementUserCount}/{permittedSeats})',\n          },\n          {\n            licenseLimitStatus,\n            enforcementUserCount,\n            permittedSeats,\n          }\n        ),\n        link: {\n          url: type === 'gold' ? BILLING_SELF_HOSTED_URL : MANAGE_SEATS_URL,\n          label: formatMessage({\n            id: 'notification.ee.warning.seat-limit.link',\n            defaultMessage: type === 'gold' ? 'Contact sales' : 'Manage seats',\n          }),\n        },\n        blockTransition: true,\n        onClose() {\n          window.sessionStorage.setItem(`${STORAGE_KEY_PREFIX}-${pathname}`, 'true');\n        },\n      });\n    }\n  }, [\n    toggleNotification,\n    license,\n    pathname,\n    formatMessage,\n    isLoading,\n    permittedSeats,\n    licenseLimitStatus,\n    enforcementUserCount,\n    isError,\n    type,\n  ]);\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAeA,IAAMA,qBAAqB;AAE3B,IAAMC,0BAA0B;AAChC,IAAMC,mBAAmB;IAEZC,8BAA8B,MAAA;AACzC,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,SAASC,SAASC,UAAS,IAAKC,iBAAAA;AACxC,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,EAAEC,SAAQ,IAAKC,YAAAA;AAErB,QAAM,EAAEC,sBAAsBC,gBAAgBC,oBAAoBC,KAAI,IAAKX,WAAW,CAAA;AAEtFY,EAAMC,gBAAU,MAAA;AACd,QAAIZ,WAAWC,WAAW;AACxB;IACF;AAEA,UAAMY,4BACJ,KAACC,aAAAA,SAAMN,cAAAA,KACP,CAACO,OAAOC,eAAeC,QAAQ,GAAGxB,kBAAmB,IAAGY,QAAS,EAAC,KAClEI,uBAAuB;AAEzB,QAAIS;AAEJ,QAAIT,uBAAuB,cAAc;AACvCS,yBAAmB;IACrB;AAEA,QAAIL,2BAA2B;AAC7BV,yBAAmB;QACjBO,MAAMQ;QACNC,SAAStB,cACP;UACEuB,IAAI;UACJC,gBACE;WAEJ;UAAEZ;QAAmB,CAAA;QAEvBa,OAAOzB,cACL;UACEuB,IAAI;UACJC,gBACE;WAEJ;UACEZ;UACAF;UACAC;QACF,CAAA;QAEFe,MAAM;UACJC,KAAKd,SAAS,SAAShB,0BAA0BC;UACjD8B,OAAO5B,cAAc;YACnBuB,IAAI;YACJC,gBAAgBX,SAAS,SAAS,kBAAkB;UACtD,CAAA;QACF;QACAgB,iBAAiB;QACjBC,UAAAA;AACEZ,iBAAOC,eAAeY,QAAQ,GAAGnC,kBAAAA,IAAsBY,QAAS,IAAG,MAAA;QACrE;MACF,CAAA;IACF;KACC;IACDF;IACAJ;IACAM;IACAR;IACAI;IACAO;IACAC;IACAF;IACAP;IACAU;EACD,CAAA;AACH;", "names": ["STORAGE_KEY_PREFIX", "BILLING_SELF_HOSTED_URL", "MANAGE_SEATS_URL", "useLicenseLimitNotification", "formatMessage", "useIntl", "license", "isError", "isLoading", "useLicenseLimits", "toggleNotification", "useNotification", "pathname", "useLocation", "enforcementUserCount", "permittedSeats", "licenseLimitStatus", "type", "React", "useEffect", "shouldDisplayNotification", "isNil", "window", "sessionStorage", "getItem", "notificationType", "message", "id", "defaultMessage", "title", "link", "url", "label", "blockTransition", "onClose", "setItem"]}