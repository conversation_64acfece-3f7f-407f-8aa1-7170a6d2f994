import {
  adminApi
} from "./chunk-XU64YSEO.js";

// node_modules/@strapi/content-releases/dist/admin/services/release.mjs
var extendInvalidatesTags = (endpoint, extraTags) => {
  if (!endpoint) {
    return;
  }
  const originalInvalidatesTags = endpoint.invalidatesTags;
  const newInvalidatesTags = (result, err, args, meta) => {
    const originalTags = typeof originalInvalidatesTags === "function" ? originalInvalidatesTags(result, err, args, meta) : originalInvalidatesTags;
    return [
      ...originalTags ?? [],
      ...extraTags
    ];
  };
  Object.assign(endpoint, {
    invalidatesTags: newInvalidatesTags
  });
};
var releaseApi = adminApi.enhanceEndpoints({
  addTagTypes: [
    "Release",
    "ReleaseAction",
    "EntriesInRelease",
    "ReleaseSettings",
    "Document"
  ],
  endpoints: {
    updateDocument(endpoint) {
      extendInvalidatesTags(endpoint, [
        {
          type: "Release",
          id: "LIST"
        },
        {
          type: "ReleaseAction",
          id: "LIST"
        }
      ]);
    },
    deleteDocument(endpoint) {
      extendInvalidatesTags(endpoint, [
        {
          type: "Release",
          id: "LIST"
        },
        {
          type: "ReleaseAction",
          id: "LIST"
        }
      ]);
    },
    deleteManyDocuments(endpoint) {
      extendInvalidatesTags(endpoint, [
        {
          type: "Release",
          id: "LIST"
        },
        {
          type: "ReleaseAction",
          id: "LIST"
        }
      ]);
    },
    discardDocument(endpoint) {
      extendInvalidatesTags(endpoint, [
        {
          type: "Release",
          id: "LIST"
        },
        {
          type: "ReleaseAction",
          id: "LIST"
        }
      ]);
    },
    createWorkflow(endpoint) {
      extendInvalidatesTags(endpoint, [
        {
          type: "Release",
          id: "LIST"
        },
        {
          type: "ReleaseAction",
          id: "LIST"
        }
      ]);
    },
    updateWorkflow(endpoint) {
      extendInvalidatesTags(endpoint, [
        {
          type: "Release",
          id: "LIST"
        },
        {
          type: "ReleaseAction",
          id: "LIST"
        }
      ]);
    },
    deleteWorkflow(endpoint) {
      extendInvalidatesTags(endpoint, [
        {
          type: "Release",
          id: "LIST"
        },
        {
          type: "ReleaseAction",
          id: "LIST"
        }
      ]);
    }
  }
}).injectEndpoints({
  endpoints: (build) => {
    return {
      getReleasesForEntry: build.query({
        query(params) {
          return {
            url: "/content-releases/getByDocumentAttached",
            method: "GET",
            config: {
              params
            }
          };
        },
        providesTags: (result) => result ? [
          ...result.data.map(({ id }) => ({
            type: "Release",
            id
          })),
          {
            type: "Release",
            id: "LIST"
          }
        ] : []
      }),
      getReleases: build.query({
        query({ page, pageSize, filters } = {
          page: 1,
          pageSize: 16,
          filters: {
            releasedAt: {
              $notNull: false
            }
          }
        }) {
          return {
            url: "/content-releases",
            method: "GET",
            config: {
              params: {
                page: page || 1,
                pageSize: pageSize || 16,
                filters: filters || {
                  releasedAt: {
                    $notNull: false
                  }
                }
              }
            }
          };
        },
        transformResponse(response, meta, arg) {
          var _a, _b;
          const releasedAtValue = (_b = (_a = arg == null ? void 0 : arg.filters) == null ? void 0 : _a.releasedAt) == null ? void 0 : _b.$notNull;
          const isActiveDoneTab = releasedAtValue === "true";
          const newResponse = {
            ...response,
            meta: {
              ...response.meta,
              activeTab: isActiveDoneTab ? "done" : "pending"
            }
          };
          return newResponse;
        },
        providesTags: (result) => result ? [
          ...result.data.map(({ id }) => ({
            type: "Release",
            id
          })),
          {
            type: "Release",
            id: "LIST"
          }
        ] : [
          {
            type: "Release",
            id: "LIST"
          }
        ]
      }),
      getRelease: build.query({
        query({ id }) {
          return {
            url: `/content-releases/${id}`,
            method: "GET"
          };
        },
        providesTags: (result, error, arg) => [
          {
            type: "Release",
            id: "LIST"
          },
          {
            type: "Release",
            id: arg.id
          }
        ]
      }),
      getReleaseActions: build.query({
        query({ releaseId, ...params }) {
          return {
            url: `/content-releases/${releaseId}/actions`,
            method: "GET",
            config: {
              params
            }
          };
        },
        providesTags: [
          {
            type: "ReleaseAction",
            id: "LIST"
          }
        ]
      }),
      createRelease: build.mutation({
        query(data) {
          return {
            url: "/content-releases",
            method: "POST",
            data
          };
        },
        invalidatesTags: [
          {
            type: "Release",
            id: "LIST"
          }
        ]
      }),
      updateRelease: build.mutation({
        query({ id, ...data }) {
          return {
            url: `/content-releases/${id}`,
            method: "PUT",
            data
          };
        },
        invalidatesTags: (result, error, arg) => [
          {
            type: "Release",
            id: arg.id
          }
        ]
      }),
      createReleaseAction: build.mutation({
        query({ body, params }) {
          return {
            url: `/content-releases/${params.releaseId}/actions`,
            method: "POST",
            data: body
          };
        },
        invalidatesTags: [
          {
            type: "Release",
            id: "LIST"
          },
          {
            type: "ReleaseAction",
            id: "LIST"
          }
        ]
      }),
      createManyReleaseActions: build.mutation({
        query({ body, params }) {
          return {
            url: `/content-releases/${params.releaseId}/actions/bulk`,
            method: "POST",
            data: body
          };
        },
        invalidatesTags: [
          {
            type: "Release",
            id: "LIST"
          },
          {
            type: "ReleaseAction",
            id: "LIST"
          },
          {
            type: "EntriesInRelease"
          }
        ]
      }),
      updateReleaseAction: build.mutation({
        query({ body, params }) {
          return {
            url: `/content-releases/${params.releaseId}/actions/${params.actionId}`,
            method: "PUT",
            data: body
          };
        },
        invalidatesTags: (res, error, arg) => [
          {
            type: "ReleaseAction",
            id: "LIST"
          },
          {
            type: "Release",
            id: "LIST"
          },
          {
            type: "Release",
            id: arg.params.releaseId
          }
        ],
        async onQueryStarted({ body, params, query, actionPath }, { dispatch, queryFulfilled }) {
          const paramsWithoutActionId = {
            releaseId: params.releaseId,
            ...query
          };
          const patchResult = dispatch(releaseApi.util.updateQueryData("getReleaseActions", paramsWithoutActionId, (draft) => {
            const [key, index] = actionPath;
            const action = draft.data[key][index];
            if (action) {
              action.type = body.type;
            }
          }));
          try {
            await queryFulfilled;
          } catch {
            patchResult.undo();
          }
        }
      }),
      deleteReleaseAction: build.mutation({
        query({ params }) {
          return {
            url: `/content-releases/${params.releaseId}/actions/${params.actionId}`,
            method: "DELETE"
          };
        },
        invalidatesTags: (result, error, arg) => [
          {
            type: "Release",
            id: "LIST"
          },
          {
            type: "Release",
            id: arg.params.releaseId
          },
          {
            type: "ReleaseAction",
            id: "LIST"
          },
          {
            type: "EntriesInRelease"
          }
        ]
      }),
      publishRelease: build.mutation({
        query({ id }) {
          return {
            url: `/content-releases/${id}/publish`,
            method: "POST"
          };
        },
        invalidatesTags: (result, error, arg) => [
          {
            type: "Release",
            id: arg.id
          },
          {
            type: "Document",
            id: `ALL_LIST`
          }
        ]
      }),
      deleteRelease: build.mutation({
        query({ id }) {
          return {
            url: `/content-releases/${id}`,
            method: "DELETE"
          };
        },
        invalidatesTags: () => [
          {
            type: "Release",
            id: "LIST"
          },
          {
            type: "EntriesInRelease"
          }
        ]
      }),
      getMappedEntriesInReleases: build.query({
        query(params) {
          return {
            url: "/content-releases/mapEntriesToReleases",
            method: "GET",
            config: {
              params
            }
          };
        },
        transformResponse(response) {
          return response.data;
        },
        providesTags: [
          {
            type: "EntriesInRelease"
          }
        ]
      }),
      getReleaseSettings: build.query({
        query: () => "/content-releases/settings",
        providesTags: [
          {
            type: "ReleaseSettings"
          }
        ]
      }),
      updateReleaseSettings: build.mutation({
        query(data) {
          return {
            url: "/content-releases/settings",
            method: "PUT",
            data
          };
        },
        invalidatesTags: [
          {
            type: "ReleaseSettings"
          }
        ]
      })
    };
  }
});
var { useGetReleasesQuery, useGetReleasesForEntryQuery, useGetReleaseQuery, useGetReleaseActionsQuery, useCreateReleaseMutation, useCreateReleaseActionMutation, useCreateManyReleaseActionsMutation, useUpdateReleaseMutation, useUpdateReleaseActionMutation, usePublishReleaseMutation, useDeleteReleaseActionMutation, useDeleteReleaseMutation, useGetMappedEntriesInReleasesQuery, useGetReleaseSettingsQuery, useUpdateReleaseSettingsMutation } = releaseApi;

// node_modules/@strapi/content-releases/dist/admin/utils/time.mjs
var getTimezoneOffset = (timezone, date) => {
  try {
    const offsetPart = new Intl.DateTimeFormat("en", {
      timeZone: timezone,
      timeZoneName: "longOffset"
    }).formatToParts(date).find((part) => part.type === "timeZoneName");
    const offset = offsetPart ? offsetPart.value : "";
    let utcOffset = offset.replace("GMT", "UTC");
    if (!utcOffset.includes("+") && !utcOffset.includes("-")) {
      utcOffset = `${utcOffset}+00:00`;
    }
    return utcOffset;
  } catch (error) {
    return "";
  }
};
var getTimezones = (selectedDate) => {
  const timezoneList = Intl.supportedValuesOf("timeZone").map((timezone) => {
    const utcOffset = getTimezoneOffset(timezone, selectedDate);
    return {
      offset: utcOffset,
      value: `${utcOffset}&${timezone}`
    };
  });
  const systemTimezone = timezoneList.find((timezone) => timezone.value.split("&")[1] === Intl.DateTimeFormat().resolvedOptions().timeZone);
  return {
    timezoneList,
    systemTimezone
  };
};

export {
  releaseApi,
  useGetReleasesQuery,
  useGetReleasesForEntryQuery,
  useGetReleaseQuery,
  useGetReleaseActionsQuery,
  useCreateReleaseMutation,
  useCreateReleaseActionMutation,
  useCreateManyReleaseActionsMutation,
  useUpdateReleaseMutation,
  useUpdateReleaseActionMutation,
  usePublishReleaseMutation,
  useDeleteReleaseActionMutation,
  useDeleteReleaseMutation,
  useGetMappedEntriesInReleasesQuery,
  useGetReleaseSettingsQuery,
  useUpdateReleaseSettingsMutation,
  getTimezoneOffset,
  getTimezones
};
//# sourceMappingURL=chunk-7KXIIYJL.js.map
