{"version": 3, "sources": ["../../../@strapi/content-releases/admin/src/services/release.ts", "../../../@strapi/content-releases/admin/src/utils/time.ts"], "sourcesContent": ["import { adminApi } from '@strapi/admin/strapi-admin';\n\nimport {\n  CreateReleaseAction,\n  CreateManyReleaseActions,\n  DeleteReleaseAction,\n} from '../../../shared/contracts/release-actions';\n\nimport type {\n  GetReleaseActions,\n  UpdateReleaseAction,\n  ReleaseActionGroupBy,\n} from '../../../shared/contracts/release-actions';\nimport type {\n  CreateRelease,\n  DeleteRelease,\n  GetReleases,\n  GetReleasesByDocumentAttached,\n  UpdateRelease,\n  GetRelease,\n  PublishRelease,\n  MapEntriesToReleases,\n} from '../../../shared/contracts/releases';\nimport type { GetSettings, UpdateSettings } from '../../../shared/contracts/settings';\nimport type { EndpointDefinition } from '@reduxjs/toolkit/query';\n\nexport interface GetReleasesQueryParams {\n  page?: number;\n  pageSize?: number;\n  filters?: {\n    releasedAt?: {\n      // TODO: this should be a boolean, find a way to avoid strings\n      $notNull?: boolean | 'true' | 'false';\n    };\n  };\n}\n\nexport interface GetReleaseActionsQueryParams {\n  page?: number;\n  pageSize?: number;\n  groupBy?: ReleaseActionGroupBy;\n}\n\ntype GetReleasesTabResponse = GetReleases.Response & {\n  meta: {\n    activeTab: 'pending' | 'done';\n  };\n};\n\ntype AnyEndpointDefinition = EndpointDefinition<any, any, any, any>;\n\n// TODO: move this into the admin code & expose an improved version of enhanceEndpoints or a new function\nconst extendInvalidatesTags = (\n  endpoint: AnyEndpointDefinition,\n  extraTags: string[] | { type: string; id: string }[]\n) => {\n  if (!endpoint) {\n    return;\n  }\n\n  const originalInvalidatesTags = endpoint.invalidatesTags;\n\n  const newInvalidatesTags: AnyEndpointDefinition['invalidatesTags'] = (\n    result,\n    err,\n    args,\n    meta\n  ) => {\n    const originalTags =\n      typeof originalInvalidatesTags === 'function'\n        ? originalInvalidatesTags(result, err, args, meta)\n        : originalInvalidatesTags;\n\n    return [...(originalTags ?? []), ...extraTags];\n  };\n\n  Object.assign(endpoint, { invalidatesTags: newInvalidatesTags });\n};\n\nconst releaseApi = adminApi\n  .enhanceEndpoints({\n    addTagTypes: ['Release', 'ReleaseAction', 'EntriesInRelease', 'ReleaseSettings', 'Document'],\n    endpoints: {\n      updateDocument(endpoint: AnyEndpointDefinition) {\n        extendInvalidatesTags(endpoint, [\n          { type: 'Release', id: 'LIST' },\n          { type: 'ReleaseAction', id: 'LIST' },\n        ]);\n      },\n      deleteDocument(endpoint: AnyEndpointDefinition) {\n        extendInvalidatesTags(endpoint, [\n          { type: 'Release', id: 'LIST' },\n          { type: 'ReleaseAction', id: 'LIST' },\n        ]);\n      },\n      deleteManyDocuments(endpoint: AnyEndpointDefinition) {\n        extendInvalidatesTags(endpoint, [\n          { type: 'Release', id: 'LIST' },\n          { type: 'ReleaseAction', id: 'LIST' },\n        ]);\n      },\n      discardDocument(endpoint: AnyEndpointDefinition) {\n        extendInvalidatesTags(endpoint, [\n          { type: 'Release', id: 'LIST' },\n          { type: 'ReleaseAction', id: 'LIST' },\n        ]);\n      },\n      createWorkflow(endpoint: AnyEndpointDefinition) {\n        extendInvalidatesTags(endpoint, [\n          { type: 'Release', id: 'LIST' },\n          { type: 'ReleaseAction', id: 'LIST' },\n        ]);\n      },\n      updateWorkflow(endpoint: AnyEndpointDefinition) {\n        extendInvalidatesTags(endpoint, [\n          { type: 'Release', id: 'LIST' },\n          { type: 'ReleaseAction', id: 'LIST' },\n        ]);\n      },\n      deleteWorkflow(endpoint: AnyEndpointDefinition) {\n        extendInvalidatesTags(endpoint, [\n          { type: 'Release', id: 'LIST' },\n          { type: 'ReleaseAction', id: 'LIST' },\n        ]);\n      },\n    },\n  })\n  .injectEndpoints({\n    endpoints: (build) => {\n      return {\n        getReleasesForEntry: build.query<\n          GetReleasesByDocumentAttached.Response,\n          Partial<GetReleasesByDocumentAttached.Request['query']>\n        >({\n          query(params) {\n            return {\n              url: '/content-releases/getByDocumentAttached',\n              method: 'GET',\n              config: {\n                params,\n              },\n            };\n          },\n          providesTags: (result) =>\n            result\n              ? [\n                  ...result.data.map(({ id }) => ({ type: 'Release' as const, id })),\n                  { type: 'Release', id: 'LIST' },\n                ]\n              : [],\n        }),\n        getReleases: build.query<GetReleasesTabResponse, GetReleasesQueryParams | void>({\n          query(\n            { page, pageSize, filters } = {\n              page: 1,\n              pageSize: 16,\n              filters: {\n                releasedAt: {\n                  $notNull: false,\n                },\n              },\n            }\n          ) {\n            return {\n              url: '/content-releases',\n              method: 'GET',\n              config: {\n                params: {\n                  page: page || 1,\n                  pageSize: pageSize || 16,\n                  filters: filters || {\n                    releasedAt: {\n                      $notNull: false,\n                    },\n                  },\n                },\n              },\n            };\n          },\n          transformResponse(response: GetReleasesTabResponse, meta, arg) {\n            const releasedAtValue = arg?.filters?.releasedAt?.$notNull;\n            const isActiveDoneTab = releasedAtValue === 'true';\n            const newResponse: GetReleasesTabResponse = {\n              ...response,\n              meta: {\n                ...response.meta,\n                activeTab: isActiveDoneTab ? 'done' : 'pending',\n              },\n            };\n\n            return newResponse;\n          },\n          providesTags: (result) =>\n            result\n              ? [\n                  ...result.data.map(({ id }) => ({ type: 'Release' as const, id })),\n                  { type: 'Release', id: 'LIST' },\n                ]\n              : [{ type: 'Release', id: 'LIST' }],\n        }),\n        getRelease: build.query<GetRelease.Response, GetRelease.Request['params']>({\n          query({ id }) {\n            return {\n              url: `/content-releases/${id}`,\n              method: 'GET',\n            };\n          },\n          providesTags: (result, error, arg) => [\n            { type: 'Release', id: 'LIST' },\n            { type: 'Release' as const, id: arg.id },\n          ],\n        }),\n        getReleaseActions: build.query<\n          GetReleaseActions.Response,\n          GetReleaseActions.Request['params'] & GetReleaseActions.Request['query']\n        >({\n          query({ releaseId, ...params }) {\n            return {\n              url: `/content-releases/${releaseId}/actions`,\n              method: 'GET',\n              config: {\n                params,\n              },\n            };\n          },\n          providesTags: [{ type: 'ReleaseAction', id: 'LIST' }],\n        }),\n        createRelease: build.mutation<CreateRelease.Response, CreateRelease.Request['body']>({\n          query(data) {\n            return {\n              url: '/content-releases',\n              method: 'POST',\n              data,\n            };\n          },\n          invalidatesTags: [{ type: 'Release', id: 'LIST' }],\n        }),\n        updateRelease: build.mutation<\n          void,\n          UpdateRelease.Request['params'] & UpdateRelease.Request['body']\n        >({\n          query({ id, ...data }) {\n            return {\n              url: `/content-releases/${id}`,\n              method: 'PUT',\n              data,\n            };\n          },\n          invalidatesTags: (result, error, arg) => [{ type: 'Release', id: arg.id }],\n        }),\n        createReleaseAction: build.mutation<\n          CreateReleaseAction.Response,\n          CreateReleaseAction.Request\n        >({\n          query({ body, params }) {\n            return {\n              url: `/content-releases/${params.releaseId}/actions`,\n              method: 'POST',\n              data: body,\n            };\n          },\n          invalidatesTags: [\n            { type: 'Release', id: 'LIST' },\n            { type: 'ReleaseAction', id: 'LIST' },\n          ],\n        }),\n        createManyReleaseActions: build.mutation<\n          CreateManyReleaseActions.Response,\n          CreateManyReleaseActions.Request\n        >({\n          query({ body, params }) {\n            return {\n              url: `/content-releases/${params.releaseId}/actions/bulk`,\n              method: 'POST',\n              data: body,\n            };\n          },\n          invalidatesTags: [\n            { type: 'Release', id: 'LIST' },\n            { type: 'ReleaseAction', id: 'LIST' },\n            { type: 'EntriesInRelease' },\n          ],\n        }),\n        updateReleaseAction: build.mutation<\n          UpdateReleaseAction.Response,\n          UpdateReleaseAction.Request & { query: GetReleaseActions.Request['query'] } & {\n            actionPath: [string, number];\n          }\n        >({\n          query({ body, params }) {\n            return {\n              url: `/content-releases/${params.releaseId}/actions/${params.actionId}`,\n              method: 'PUT',\n              data: body,\n            };\n          },\n          invalidatesTags: (res, error, arg) => [\n            { type: 'ReleaseAction', id: 'LIST' },\n            { type: 'Release', id: 'LIST' },\n            { type: 'Release', id: arg.params.releaseId },\n          ],\n          async onQueryStarted({ body, params, query, actionPath }, { dispatch, queryFulfilled }) {\n            // We need to mimic the same params received by the getReleaseActions query\n            const paramsWithoutActionId = {\n              releaseId: params.releaseId,\n              ...query,\n            };\n\n            const patchResult = dispatch(\n              releaseApi.util.updateQueryData(\n                'getReleaseActions',\n                paramsWithoutActionId,\n                (draft) => {\n                  const [key, index] = actionPath;\n                  const action = draft.data[key][index];\n\n                  if (action) {\n                    action.type = body.type;\n                  }\n                }\n              )\n            );\n\n            try {\n              await queryFulfilled;\n            } catch {\n              patchResult.undo();\n            }\n          },\n        }),\n        deleteReleaseAction: build.mutation<\n          DeleteReleaseAction.Response,\n          DeleteReleaseAction.Request\n        >({\n          query({ params }) {\n            return {\n              url: `/content-releases/${params.releaseId}/actions/${params.actionId}`,\n              method: 'DELETE',\n            };\n          },\n          invalidatesTags: (result, error, arg) => [\n            { type: 'Release', id: 'LIST' },\n            { type: 'Release', id: arg.params.releaseId },\n            { type: 'ReleaseAction', id: 'LIST' },\n            { type: 'EntriesInRelease' },\n          ],\n        }),\n        publishRelease: build.mutation<PublishRelease.Response, PublishRelease.Request['params']>({\n          query({ id }) {\n            return {\n              url: `/content-releases/${id}/publish`,\n              method: 'POST',\n            };\n          },\n          invalidatesTags: (result, error, arg) => [\n            { type: 'Release', id: arg.id },\n            { type: 'Document', id: `ALL_LIST` },\n          ],\n        }),\n        deleteRelease: build.mutation<DeleteRelease.Response, DeleteRelease.Request['params']>({\n          query({ id }) {\n            return {\n              url: `/content-releases/${id}`,\n              method: 'DELETE',\n            };\n          },\n          invalidatesTags: () => [{ type: 'Release', id: 'LIST' }, { type: 'EntriesInRelease' }],\n        }),\n        getMappedEntriesInReleases: build.query<\n          MapEntriesToReleases.Response['data'],\n          MapEntriesToReleases.Request['query']\n        >({\n          query(params) {\n            return {\n              url: '/content-releases/mapEntriesToReleases',\n              method: 'GET',\n              config: {\n                params,\n              },\n            };\n          },\n          transformResponse(response: MapEntriesToReleases.Response) {\n            return response.data;\n          },\n          providesTags: [{ type: 'EntriesInRelease' }],\n        }),\n        getReleaseSettings: build.query<GetSettings.Response, GetSettings.Request | void>({\n          query: () => '/content-releases/settings',\n          providesTags: [{ type: 'ReleaseSettings' }],\n        }),\n        updateReleaseSettings: build.mutation<void, UpdateSettings.Request['body']>({\n          query(data) {\n            return {\n              url: '/content-releases/settings',\n              method: 'PUT',\n              data,\n            };\n          },\n          invalidatesTags: [{ type: 'ReleaseSettings' }],\n        }),\n      };\n    },\n  });\n\nconst {\n  useGetReleasesQuery,\n  useGetReleasesForEntryQuery,\n  useGetReleaseQuery,\n  useGetReleaseActionsQuery,\n  useCreateReleaseMutation,\n  useCreateReleaseActionMutation,\n  useCreateManyReleaseActionsMutation,\n  useUpdateReleaseMutation,\n  useUpdateReleaseActionMutation,\n  usePublishReleaseMutation,\n  useDeleteReleaseActionMutation,\n  useDeleteReleaseMutation,\n  useGetMappedEntriesInReleasesQuery,\n  useGetReleaseSettingsQuery,\n  useUpdateReleaseSettingsMutation,\n} = releaseApi;\n\nexport {\n  useGetReleasesQuery,\n  useGetReleasesForEntryQuery,\n  useGetReleaseQuery,\n  useGetReleaseActionsQuery,\n  useCreateReleaseMutation,\n  useCreateReleaseActionMutation,\n  useCreateManyReleaseActionsMutation,\n  useUpdateReleaseMutation,\n  useUpdateReleaseActionMutation,\n  usePublishReleaseMutation,\n  useDeleteReleaseActionMutation,\n  useDeleteReleaseMutation,\n  useGetMappedEntriesInReleasesQuery,\n  useGetReleaseSettingsQuery,\n  useUpdateReleaseSettingsMutation,\n  releaseApi,\n};\n", "export const getTimezoneOffset = (timezone: string, date: Date) => {\n  try {\n    const offsetPart = new Intl.DateTimeFormat('en', {\n      timeZone: timezone,\n      timeZoneName: 'longOffset',\n    })\n      .formatToParts(date)\n      .find((part) => part.type === 'timeZoneName');\n\n    const offset = offsetPart ? offsetPart.value : '';\n\n    // We want to show time based on UTC, not GMT so we swap that.\n    let utcOffset = offset.replace('GMT', 'UTC');\n\n    // For perfect UTC (UTC+0:00) we only get the string UTC, So we need to append the 0's.\n    if (!utcOffset.includes('+') && !utcOffset.includes('-')) {\n      utcOffset = `${utcOffset}+00:00`;\n    }\n\n    return utcOffset;\n  } catch (error) {\n    // When timezone is invalid we catch the error and return empty to don't break the app\n    return '';\n  }\n};\n\ninterface ITimezoneOption {\n  offset: string;\n  value: string;\n}\n\nexport const getTimezones = (selectedDate: Date) => {\n  const timezoneList: ITimezoneOption[] = Intl.supportedValuesOf('timeZone').map((timezone) => {\n    // Timezone will be in the format GMT${OFFSET} where offset could be nothing,\n    // a four digit string e.g. +05:00 or -08:00\n    const utcOffset = getTimezoneOffset(timezone, selectedDate);\n\n    // Offset and timezone are concatenated with '&', so to split and save the required timezone in DB\n    return { offset: utcOffset, value: `${utcOffset}&${timezone}` } satisfies ITimezoneOption;\n  });\n\n  const systemTimezone = timezoneList.find(\n    (timezone) => timezone.value.split('&')[1] === Intl.DateTimeFormat().resolvedOptions().timeZone\n  );\n\n  return { timezoneList, systemTimezone };\n};\n"], "mappings": ";;;;;AAoDA,IAAMA,wBAAwB,CAC5BC,UACAC,cAAAA;AAEA,MAAI,CAACD,UAAU;AACb;EACF;AAEA,QAAME,0BAA0BF,SAASG;AAEzC,QAAMC,qBAA+D,CACnEC,QACAC,KACAC,MACAC,SAAAA;AAEA,UAAMC,eACJ,OAAOP,4BAA4B,aAC/BA,wBAAwBG,QAAQC,KAAKC,MAAMC,IAC3CN,IAAAA;AAEN,WAAO;MAAKO,GAAAA,gBAAgB,CAAA;MAAQR,GAAAA;IAAU;EAChD;AAEAS,SAAOC,OAAOX,UAAU;IAAEG,iBAAiBC;EAAmB,CAAA;AAChE;AAEMQ,IAAAA,aAAaC,SAChBC,iBAAiB;EAChBC,aAAa;IAAC;IAAW;IAAiB;IAAoB;IAAmB;EAAW;EAC5FC,WAAW;IACTC,eAAejB,UAA+B;AAC5CD,4BAAsBC,UAAU;QAC9B;UAAEkB,MAAM;UAAWC,IAAI;QAAO;QAC9B;UAAED,MAAM;UAAiBC,IAAI;QAAO;MACrC,CAAA;IACH;IACAC,eAAepB,UAA+B;AAC5CD,4BAAsBC,UAAU;QAC9B;UAAEkB,MAAM;UAAWC,IAAI;QAAO;QAC9B;UAAED,MAAM;UAAiBC,IAAI;QAAO;MACrC,CAAA;IACH;IACAE,oBAAoBrB,UAA+B;AACjDD,4BAAsBC,UAAU;QAC9B;UAAEkB,MAAM;UAAWC,IAAI;QAAO;QAC9B;UAAED,MAAM;UAAiBC,IAAI;QAAO;MACrC,CAAA;IACH;IACAG,gBAAgBtB,UAA+B;AAC7CD,4BAAsBC,UAAU;QAC9B;UAAEkB,MAAM;UAAWC,IAAI;QAAO;QAC9B;UAAED,MAAM;UAAiBC,IAAI;QAAO;MACrC,CAAA;IACH;IACAI,eAAevB,UAA+B;AAC5CD,4BAAsBC,UAAU;QAC9B;UAAEkB,MAAM;UAAWC,IAAI;QAAO;QAC9B;UAAED,MAAM;UAAiBC,IAAI;QAAO;MACrC,CAAA;IACH;IACAK,eAAexB,UAA+B;AAC5CD,4BAAsBC,UAAU;QAC9B;UAAEkB,MAAM;UAAWC,IAAI;QAAO;QAC9B;UAAED,MAAM;UAAiBC,IAAI;QAAO;MACrC,CAAA;IACH;IACAM,eAAezB,UAA+B;AAC5CD,4BAAsBC,UAAU;QAC9B;UAAEkB,MAAM;UAAWC,IAAI;QAAO;QAC9B;UAAED,MAAM;UAAiBC,IAAI;QAAO;MACrC,CAAA;IACH;EACF;AACF,CAAA,EACCO,gBAAgB;EACfV,WAAW,CAACW,UAAAA;AACV,WAAO;MACLC,qBAAqBD,MAAME,MAGzB;QACAA,MAAMC,QAAM;AACV,iBAAO;YACLC,KAAK;YACLC,QAAQ;YACRC,QAAQ;cACNH;YACF;UACF;QACF;QACAI,cAAc,CAAC7B,WACbA,SACI;aACKA,OAAO8B,KAAKC,IAAI,CAAC,EAAEjB,GAAE,OAAQ;YAAED,MAAM;YAAoBC;YAAG;UAC/D;YAAED,MAAM;YAAWC,IAAI;UAAO;QAC/B,IACD,CAAA;MACR,CAAA;MACAkB,aAAaV,MAAME,MAA6D;QAC9EA,MACE,EAAES,MAAMC,UAAUC,QAAO,IAAK;UAC5BF,MAAM;UACNC,UAAU;UACVC,SAAS;YACPC,YAAY;cACVC,UAAU;YACZ;UACF;WACD;AAED,iBAAO;YACLX,KAAK;YACLC,QAAQ;YACRC,QAAQ;cACNH,QAAQ;gBACNQ,MAAMA,QAAQ;gBACdC,UAAUA,YAAY;gBACtBC,SAASA,WAAW;kBAClBC,YAAY;oBACVC,UAAU;kBACZ;gBACF;cACF;YACF;UACF;QACF;QACAC,kBAAkBC,UAAkCpC,MAAMqC,KAAG;;AAC3D,gBAAMC,mBAAkBD,sCAAKL,YAALK,mBAAcJ,eAAdI,mBAA0BH;AAClD,gBAAMK,kBAAkBD,oBAAoB;AAC5C,gBAAME,cAAsC;YAC1C,GAAGJ;YACHpC,MAAM;cACJ,GAAGoC,SAASpC;cACZyC,WAAWF,kBAAkB,SAAS;YACxC;UACF;AAEA,iBAAOC;QACT;QACAd,cAAc,CAAC7B,WACbA,SACI;aACKA,OAAO8B,KAAKC,IAAI,CAAC,EAAEjB,GAAE,OAAQ;YAAED,MAAM;YAAoBC;YAAG;UAC/D;YAAED,MAAM;YAAWC,IAAI;UAAO;YAEhC;UAAC;YAAED,MAAM;YAAWC,IAAI;UAAO;QAAE;MACzC,CAAA;MACA+B,YAAYvB,MAAME,MAAyD;QACzEA,MAAM,EAAEV,GAAE,GAAE;AACV,iBAAO;YACLY,KAAK,qBAAqBZ,EAAAA;YAC1Ba,QAAQ;UACV;QACF;QACAE,cAAc,CAAC7B,QAAQ8C,OAAON,QAAQ;UACpC;YAAE3B,MAAM;YAAWC,IAAI;UAAO;UAC9B;YAAED,MAAM;YAAoBC,IAAI0B,IAAI1B;UAAG;QACxC;MACH,CAAA;MACAiC,mBAAmBzB,MAAME,MAGvB;QACAA,MAAM,EAAEwB,WAAW,GAAGvB,OAAQ,GAAA;AAC5B,iBAAO;YACLC,KAAK,qBAAqBsB,SAAAA;YAC1BrB,QAAQ;YACRC,QAAQ;cACNH;YACF;UACF;QACF;QACAI,cAAc;UAAC;YAAEhB,MAAM;YAAiBC,IAAI;UAAO;QAAE;MACvD,CAAA;MACAmC,eAAe3B,MAAM4B,SAAgE;QACnF1B,MAAMM,MAAI;AACR,iBAAO;YACLJ,KAAK;YACLC,QAAQ;YACRG;UACF;QACF;QACAhC,iBAAiB;UAAC;YAAEe,MAAM;YAAWC,IAAI;UAAO;QAAE;MACpD,CAAA;MACAqC,eAAe7B,MAAM4B,SAGnB;QACA1B,MAAM,EAAEV,IAAI,GAAGgB,KAAM,GAAA;AACnB,iBAAO;YACLJ,KAAK,qBAAqBZ,EAAAA;YAC1Ba,QAAQ;YACRG;UACF;QACF;QACAhC,iBAAiB,CAACE,QAAQ8C,OAAON,QAAQ;UAAC;YAAE3B,MAAM;YAAWC,IAAI0B,IAAI1B;UAAG;QAAE;MAC5E,CAAA;MACAsC,qBAAqB9B,MAAM4B,SAGzB;QACA1B,MAAM,EAAE6B,MAAM5B,OAAM,GAAE;AACpB,iBAAO;YACLC,KAAK,qBAAqBD,OAAOuB,SAAS;YAC1CrB,QAAQ;YACRG,MAAMuB;UACR;QACF;QACAvD,iBAAiB;UACf;YAAEe,MAAM;YAAWC,IAAI;UAAO;UAC9B;YAAED,MAAM;YAAiBC,IAAI;UAAO;QACrC;MACH,CAAA;MACAwC,0BAA0BhC,MAAM4B,SAG9B;QACA1B,MAAM,EAAE6B,MAAM5B,OAAM,GAAE;AACpB,iBAAO;YACLC,KAAK,qBAAqBD,OAAOuB,SAAS;YAC1CrB,QAAQ;YACRG,MAAMuB;UACR;QACF;QACAvD,iBAAiB;UACf;YAAEe,MAAM;YAAWC,IAAI;UAAO;UAC9B;YAAED,MAAM;YAAiBC,IAAI;UAAO;UACpC;YAAED,MAAM;UAAmB;QAC5B;MACH,CAAA;MACA0C,qBAAqBjC,MAAM4B,SAKzB;QACA1B,MAAM,EAAE6B,MAAM5B,OAAM,GAAE;AACpB,iBAAO;YACLC,KAAK,qBAAqBD,OAAOuB,SAAS,YAAYvB,OAAO+B,QAAQ;YACrE7B,QAAQ;YACRG,MAAMuB;UACR;QACF;QACAvD,iBAAiB,CAAC2D,KAAKX,OAAON,QAAQ;UACpC;YAAE3B,MAAM;YAAiBC,IAAI;UAAO;UACpC;YAAED,MAAM;YAAWC,IAAI;UAAO;UAC9B;YAAED,MAAM;YAAWC,IAAI0B,IAAIf,OAAOuB;UAAU;QAC7C;QACD,MAAMU,eAAe,EAAEL,MAAM5B,QAAQD,OAAOmC,WAAU,GAAI,EAAEC,UAAUC,eAAc,GAAE;AAEpF,gBAAMC,wBAAwB;YAC5Bd,WAAWvB,OAAOuB;YAClB,GAAGxB;UACL;AAEA,gBAAMuC,cAAcH,SAClBrD,WAAWyD,KAAKC,gBACd,qBACAH,uBACA,CAACI,UAAAA;AACC,kBAAM,CAACC,KAAKC,KAAAA,IAAST;AACrB,kBAAMU,SAASH,MAAMpC,KAAKqC,GAAAA,EAAKC,KAAM;AAErC,gBAAIC,QAAQ;AACVA,qBAAOxD,OAAOwC,KAAKxC;YACrB;UACF,CAAA,CAAA;AAIJ,cAAI;AACF,kBAAMgD;UACR,QAAQ;AACNE,wBAAYO,KAAI;UAClB;QACF;MACF,CAAA;MACAC,qBAAqBjD,MAAM4B,SAGzB;QACA1B,MAAM,EAAEC,OAAM,GAAE;AACd,iBAAO;YACLC,KAAK,qBAAqBD,OAAOuB,SAAS,YAAYvB,OAAO+B,QAAQ;YACrE7B,QAAQ;UACV;QACF;QACA7B,iBAAiB,CAACE,QAAQ8C,OAAON,QAAQ;UACvC;YAAE3B,MAAM;YAAWC,IAAI;UAAO;UAC9B;YAAED,MAAM;YAAWC,IAAI0B,IAAIf,OAAOuB;UAAU;UAC5C;YAAEnC,MAAM;YAAiBC,IAAI;UAAO;UACpC;YAAED,MAAM;UAAmB;QAC5B;MACH,CAAA;MACA2D,gBAAgBlD,MAAM4B,SAAoE;QACxF1B,MAAM,EAAEV,GAAE,GAAE;AACV,iBAAO;YACLY,KAAK,qBAAqBZ,EAAAA;YAC1Ba,QAAQ;UACV;QACF;QACA7B,iBAAiB,CAACE,QAAQ8C,OAAON,QAAQ;UACvC;YAAE3B,MAAM;YAAWC,IAAI0B,IAAI1B;UAAG;UAC9B;YAAED,MAAM;YAAYC,IAAI;UAAW;QACpC;MACH,CAAA;MACA2D,eAAenD,MAAM4B,SAAkE;QACrF1B,MAAM,EAAEV,GAAE,GAAE;AACV,iBAAO;YACLY,KAAK,qBAAqBZ,EAAAA;YAC1Ba,QAAQ;UACV;QACF;QACA7B,iBAAiB,MAAM;UAAC;YAAEe,MAAM;YAAWC,IAAI;UAAO;UAAG;YAAED,MAAM;UAAmB;QAAE;MACxF,CAAA;MACA6D,4BAA4BpD,MAAME,MAGhC;QACAA,MAAMC,QAAM;AACV,iBAAO;YACLC,KAAK;YACLC,QAAQ;YACRC,QAAQ;cACNH;YACF;UACF;QACF;QACAa,kBAAkBC,UAAuC;AACvD,iBAAOA,SAAST;QAClB;QACAD,cAAc;UAAC;YAAEhB,MAAM;UAAmB;QAAE;MAC9C,CAAA;MACA8D,oBAAoBrD,MAAME,MAAwD;QAChFA,OAAO,MAAM;QACbK,cAAc;UAAC;YAAEhB,MAAM;UAAkB;QAAE;MAC7C,CAAA;MACA+D,uBAAuBtD,MAAM4B,SAA+C;QAC1E1B,MAAMM,MAAI;AACR,iBAAO;YACLJ,KAAK;YACLC,QAAQ;YACRG;UACF;QACF;QACAhC,iBAAiB;UAAC;YAAEe,MAAM;UAAkB;QAAE;MAChD,CAAA;IACF;EACF;AACF,CAAA;AAEF,IAAM,EACJgE,qBACAC,6BACAC,oBACAC,2BACAC,0BACAC,gCACAC,qCACAC,0BACAC,gCACAC,2BACAC,gCACAC,0BACAC,oCACAC,4BACAC,iCAAgC,IAC9BpF;;;ACpaG,IAAMqF,oBAAoB,CAACC,UAAkBC,SAAAA;AAClD,MAAI;AACF,UAAMC,aAAa,IAAIC,KAAKC,eAAe,MAAM;MAC/CC,UAAUL;MACVM,cAAc;KAEbC,EAAAA,cAAcN,IACdO,EAAAA,KAAK,CAACC,SAASA,KAAKC,SAAS,cAAA;AAEhC,UAAMC,SAAST,aAAaA,WAAWU,QAAQ;AAG/C,QAAIC,YAAYF,OAAOG,QAAQ,OAAO,KAAA;AAGtC,QAAI,CAACD,UAAUE,SAAS,GAAA,KAAQ,CAACF,UAAUE,SAAS,GAAM,GAAA;AACxDF,kBAAY,GAAGA,SAAU;IAC3B;AAEA,WAAOA;EACT,SAASG,OAAO;AAEd,WAAO;EACT;AACF;AAOO,IAAMC,eAAe,CAACC,iBAAAA;AAC3B,QAAMC,eAAkChB,KAAKiB,kBAAkB,UAAYC,EAAAA,IAAI,CAACrB,aAAAA;AAG9E,UAAMa,YAAYd,kBAAkBC,UAAUkB,YAAAA;AAG9C,WAAO;MAAEP,QAAQE;MAAWD,OAAO,GAAGC,SAAAA,IAAab,QAAAA;IAAW;EAChE,CAAA;AAEA,QAAMsB,iBAAiBH,aAAaX,KAClC,CAACR,aAAaA,SAASY,MAAMW,MAAM,GAAI,EAAC,CAAA,MAAOpB,KAAKC,eAAc,EAAGoB,gBAAe,EAAGnB,QAAQ;AAGjG,SAAO;IAAEc;IAAcG;EAAe;AACxC;", "names": ["extendInvalidatesTags", "endpoint", "extraTags", "originalInvalidatesTags", "invalidatesTags", "newInvalidatesTags", "result", "err", "args", "meta", "originalTags", "Object", "assign", "releaseApi", "adminApi", "enhanceEndpoints", "addTagTypes", "endpoints", "updateDocument", "type", "id", "deleteDocument", "deleteManyDocuments", "discardDocument", "createWorkflow", "updateWorkflow", "deleteWorkflow", "injectEndpoints", "build", "getReleasesForEntry", "query", "params", "url", "method", "config", "providesTags", "data", "map", "getReleases", "page", "pageSize", "filters", "releasedAt", "$notNull", "transformResponse", "response", "arg", "releasedAtValue", "isActiveDoneTab", "newResponse", "activeTab", "getRelease", "error", "getReleaseActions", "releaseId", "createRelease", "mutation", "updateRelease", "createReleaseAction", "body", "createManyReleaseActions", "updateReleaseAction", "actionId", "res", "onQueryStarted", "actionPath", "dispatch", "queryFulfilled", "paramsWithoutActionId", "patchResult", "util", "updateQueryData", "draft", "key", "index", "action", "undo", "deleteReleaseAction", "publishRelease", "deleteRelease", "getMappedEntriesInReleases", "getReleaseSettings", "updateReleaseSettings", "useGetReleasesQuery", "useGetReleasesForEntryQuery", "useGetReleaseQuery", "useGetReleaseActionsQuery", "useCreateReleaseMutation", "useCreateReleaseActionMutation", "useCreateManyReleaseActionsMutation", "useUpdateReleaseMutation", "useUpdateReleaseActionMutation", "usePublishReleaseMutation", "useDeleteReleaseActionMutation", "useDeleteReleaseMutation", "useGetMappedEntriesInReleasesQuery", "useGetReleaseSettingsQuery", "useUpdateReleaseSettingsMutation", "getTimezoneOffset", "timezone", "date", "offsetPart", "Intl", "DateTimeFormat", "timeZone", "timeZoneName", "formatToParts", "find", "part", "type", "offset", "value", "utcOffset", "replace", "includes", "error", "getTimezones", "selectedDate", "timezoneList", "supportedValuesOf", "map", "systemTimezone", "split", "resolvedOptions"]}