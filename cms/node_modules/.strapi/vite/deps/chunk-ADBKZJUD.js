import {
  Flex,
  Typography
} from "./chunk-4YYUDJZ2.js";
import {
  ForwardRef$1v,
  ForwardRef$3T,
  ForwardRef$45
} from "./chunk-QW5ZDDTU.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  dt
} from "./chunk-3CQBCJ3G.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/content-manager/dist/admin/components/DragPreviews/CardDragPreview.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var CardDragPreview = ({ label, isSibling = false }) => {
  return (0, import_jsx_runtime.jsxs)(FieldContainer, {
    background: isSibling ? "neutral100" : "primary100",
    display: "inline-flex",
    gap: 3,
    hasRadius: true,
    justifyContent: "space-between",
    $isSibling: isSibling,
    "max-height": `3.2rem`,
    maxWidth: "min-content",
    children: [
      (0, import_jsx_runtime.jsxs)(Flex, {
        gap: 3,
        children: [
          (0, import_jsx_runtime.jsx)(DragButton, {
            alignItems: "center",
            cursor: "all-scroll",
            padding: 3,
            children: (0, import_jsx_runtime.jsx)(ForwardRef$3T, {})
          }),
          (0, import_jsx_runtime.jsx)(Typography, {
            textColor: isSibling ? void 0 : "primary600",
            fontWeight: "bold",
            ellipsis: true,
            maxWidth: "7.2rem",
            children: label
          })
        ]
      }),
      (0, import_jsx_runtime.jsxs)(Flex, {
        children: [
          (0, import_jsx_runtime.jsx)(ActionBox, {
            alignItems: "center",
            children: (0, import_jsx_runtime.jsx)(ForwardRef$1v, {})
          }),
          (0, import_jsx_runtime.jsx)(ActionBox, {
            alignItems: "center",
            children: (0, import_jsx_runtime.jsx)(ForwardRef$45, {})
          })
        ]
      })
    ]
  });
};
var ActionBox = dt(Flex)`
  height: ${({ theme }) => theme.spaces[7]};

  &:last-child {
    padding: 0 ${({ theme }) => theme.spaces[3]};
  }
`;
var DragButton = dt(ActionBox)`
  border-right: 1px solid ${({ theme }) => theme.colors.primary200};

  svg {
    width: 1.2rem;
    height: 1.2rem;
  }
`;
var FieldContainer = dt(Flex)`
  border: 1px solid
    ${({ theme, $isSibling }) => $isSibling ? theme.colors.neutral150 : theme.colors.primary200};

  svg {
    width: 1rem;
    height: 1rem;

    path {
      fill: ${({ theme, $isSibling }) => $isSibling ? void 0 : theme.colors.primary600};
    }
  }
`;

export {
  CardDragPreview
};
//# sourceMappingURL=chunk-ADBKZJUD.js.map
