{"version": 3, "sources": ["../../../@strapi/admin/admin/src/core/store/hooks.ts"], "sourcesContent": ["import { createSelector, Selector } from '@reduxjs/toolkit';\nimport { useDispatch, useStore, TypedUseSelectorHook, useSelector } from 'react-redux';\n\nimport type { RootState, Store } from './configure';\n\ntype AppDispatch = Store['dispatch'];\n\nconst useTypedDispatch: () => AppDispatch = useDispatch;\nconst useTypedStore = useStore as () => Store;\nconst useTypedSelector: TypedUseSelectorHook<RootState> = useSelector;\n\nconst createTypedSelector = <TResult>(selector: Selector<RootState, TResult>) =>\n  createSelector((state: RootState) => state, selector);\n\nexport { useTypedDispatch, useTypedStore, useTypedSelector, createTypedSelector };\n"], "mappings": ";;;;;;;;AAOA,IAAMA,mBAAsCC;AAC5C,IAAMC,gBAAgBC;AACtB,IAAMC,mBAAoDC;AAE1D,IAAMC,sBAAsB,CAAUC,aACpCC,eAAe,CAACC,UAAqBA,OAAOF,QAAAA;", "names": ["useTypedDispatch", "useDispatch", "useTypedStore", "useStore", "useTypedSelector", "useSelector", "createTypedSelector", "selector", "createSelector", "state"]}