import {
  require_clone,
  require_toPath
} from "./chunk-CE4VABH2.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/admin/src/utils/objects.mjs
var import_clone = __toESM(require_clone(), 1);
var import_toPath = __toESM(require_toPath(), 1);
function getIn(obj, key, def, pathStartIndex = 0) {
  const path = (0, import_toPath.default)(key);
  while (obj && pathStartIndex < path.length) {
    obj = obj[path[pathStartIndex++]];
  }
  if (pathStartIndex !== path.length && !obj) {
    return def;
  }
  return obj === void 0 ? def : obj;
}
var isObject = (obj) => obj !== null && typeof obj === "object" && !Array.isArray(obj);
var isInteger = (obj) => String(Math.floor(Number(obj))) === obj;
function setIn(obj, path, value) {
  const res = (0, import_clone.default)(obj);
  let resVal = res;
  let i = 0;
  const pathArray = (0, import_toPath.default)(path);
  for (; i < pathArray.length - 1; i++) {
    const currentPath = pathArray[i];
    const currentObj = getIn(obj, pathArray.slice(0, i + 1));
    if (currentObj && (isObject(currentObj) || Array.isArray(currentObj))) {
      resVal = resVal[currentPath] = (0, import_clone.default)(currentObj);
    } else {
      const nextPath = pathArray[i + 1];
      resVal = resVal[currentPath] = isInteger(nextPath) && Number(nextPath) >= 0 ? [] : {};
    }
  }
  if ((i === 0 ? obj : resVal)[pathArray[i]] === value) {
    return obj;
  }
  if (value === void 0) {
    delete resVal[pathArray[i]];
  } else {
    resVal[pathArray[i]] = value;
  }
  if (i === 0 && value === void 0) {
    delete res[pathArray[i]];
  }
  return res;
}

export {
  getIn,
  isObject,
  setIn
};
//# sourceMappingURL=chunk-BHLYCXQ7.js.map
