{"version": 3, "sources": ["../../../@strapi/admin/admin/src/utils/objects.ts"], "sourcesContent": ["import clone from 'lodash/clone';\nimport toPath from 'lodash/toPath';\n\n/**\n * Deeply get a value from an object via its path.\n */\nexport function getIn(obj: any, key: string | string[], def?: any, pathStartIndex: number = 0) {\n  const path = toPath(key);\n  while (obj && pathStartIndex < path.length) {\n    obj = obj[path[pathStartIndex++]];\n  }\n\n  // check if path is not in the end\n  if (pathStartIndex !== path.length && !obj) {\n    return def;\n  }\n\n  return obj === undefined ? def : obj;\n}\n\n/** @internal is the given object an Object? */\nexport const isObject = (obj: any): obj is object =>\n  obj !== null && typeof obj === 'object' && !Array.isArray(obj);\n\n/** @internal is the given object an integer? */\nexport const isInteger = (obj: any): boolean => String(Math.floor(Number(obj))) === obj;\n\n/**\n * Deeply set a value from in object via its path. If the value at `path`\n * has changed, return a shallow copy of obj with `value` set at `path`.\n * If `value` has not changed, return the original `obj`.\n *\n * Existing objects / arrays along `path` are also shallow copied. Sibling\n * objects along path retain the same internal js reference. Since new\n * objects / arrays are only created along `path`, we can test if anything\n * changed in a nested structure by comparing the object's reference in\n * the old and new object, similar to how russian doll cache invalidation\n * works.\n *\n * In earlier versions of this function, which used cloneDeep, there were\n * issues whereby settings a nested value would mutate the parent\n * instead of creating a new object. `clone` avoids that bug making a\n * shallow copy of the objects along the update path\n * so no object is mutated in place.\n *\n * Before changing this function, please read through the following\n * discussions.\n *\n * @see https://github.com/developit/linkstate\n * @see https://github.com/jaredpalmer/formik/pull/123\n */\nexport function setIn(obj: any, path: string, value: any): any {\n  const res: any = clone(obj); // this keeps inheritance when obj is a class\n  let resVal: any = res;\n  let i = 0;\n  const pathArray = toPath(path);\n\n  for (; i < pathArray.length - 1; i++) {\n    const currentPath: string = pathArray[i];\n    const currentObj: any = getIn(obj, pathArray.slice(0, i + 1));\n\n    if (currentObj && (isObject(currentObj) || Array.isArray(currentObj))) {\n      resVal = resVal[currentPath] = clone(currentObj);\n    } else {\n      const nextPath: string = pathArray[i + 1];\n      resVal = resVal[currentPath] = isInteger(nextPath) && Number(nextPath) >= 0 ? [] : {};\n    }\n  }\n\n  // Return original object if new value is the same as current\n  if ((i === 0 ? obj : resVal)[pathArray[i]] === value) {\n    return obj;\n  }\n\n  if (value === undefined) {\n    delete resVal[pathArray[i]];\n  } else {\n    resVal[pathArray[i]] = value;\n  }\n\n  // If the path array has a single element, the loop did not run.\n  // Deleting on `resVal` had no effect in this scenario, so we delete on the result instead.\n  if (i === 0 && value === undefined) {\n    delete res[pathArray[i]];\n  }\n\n  return res;\n}\n"], "mappings": ";;;;;;;;;;;AAMO,SAASA,MAAMC,KAAUC,KAAwBC,KAAWC,iBAAyB,GAAC;AAC3F,QAAMC,WAAOC,cAAAA,SAAOJ,GAAAA;AACpB,SAAOD,OAAOG,iBAAiBC,KAAKE,QAAQ;AAC1CN,UAAMA,IAAII,KAAKD,gBAAAA,CAAiB;EAClC;AAGA,MAAIA,mBAAmBC,KAAKE,UAAU,CAACN,KAAK;AAC1C,WAAOE;EACT;AAEA,SAAOF,QAAQO,SAAYL,MAAMF;AACnC;AAGaQ,IAAAA,WAAW,CAACR,QACvBA,QAAQ,QAAQ,OAAOA,QAAQ,YAAY,CAACS,MAAMC,QAAQV,GAAK;AAGpDW,IAAAA,YAAY,CAACX,QAAsBY,OAAOC,KAAKC,MAAMC,OAAOf,GAAAA,CAAAA,CAAAA,MAAWA;AA0B7E,SAASgB,MAAMhB,KAAUI,MAAca,OAAU;AACtD,QAAMC,UAAWC,aAAAA,SAAMnB,GAAAA;AACvB,MAAIoB,SAAcF;AAClB,MAAIG,IAAI;AACR,QAAMC,gBAAYjB,cAAAA,SAAOD,IAAAA;AAEzB,SAAOiB,IAAIC,UAAUhB,SAAS,GAAGe,KAAK;AACpC,UAAME,cAAsBD,UAAUD,CAAE;AACxC,UAAMG,aAAkBzB,MAAMC,KAAKsB,UAAUG,MAAM,GAAGJ,IAAI,CAAA,CAAA;AAE1D,QAAIG,eAAehB,SAASgB,UAAAA,KAAef,MAAMC,QAAQc,UAAAA,IAAc;AACrEJ,eAASA,OAAOG,WAAY,QAAGJ,aAAAA,SAAMK,UAAAA;WAChC;AACL,YAAME,WAAmBJ,UAAUD,IAAI,CAAE;AACzCD,eAASA,OAAOG,WAAAA,IAAeZ,UAAUe,QAAaX,KAAAA,OAAOW,QAAa,KAAA,IAAI,CAAA,IAAK,CAAA;IACrF;EACF;AAGA,OAAKL,MAAM,IAAIrB,MAAMoB,QAAQE,UAAUD,CAAE,CAAA,MAAMJ,OAAO;AACpD,WAAOjB;EACT;AAEA,MAAIiB,UAAUV,QAAW;AACvB,WAAOa,OAAOE,UAAUD,CAAAA,CAAE;SACrB;AACLD,WAAOE,UAAUD,CAAAA,CAAE,IAAIJ;EACzB;AAIA,MAAII,MAAM,KAAKJ,UAAUV,QAAW;AAClC,WAAOW,IAAII,UAAUD,CAAAA,CAAE;EACzB;AAEA,SAAOH;AACT;", "names": ["getIn", "obj", "key", "def", "pathStartIndex", "path", "to<PERSON><PERSON>", "length", "undefined", "isObject", "Array", "isArray", "isInteger", "String", "Math", "floor", "Number", "setIn", "value", "res", "clone", "resVal", "i", "pathArray", "currentPath", "currentObj", "slice", "nextPath"]}