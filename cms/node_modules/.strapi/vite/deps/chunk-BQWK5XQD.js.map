{"version": 3, "sources": ["../../../@strapi/admin/admin/src/pages/Settings/pages/Users/<USER>/validation.ts"], "sourcesContent": ["import * as yup from 'yup';\n\nimport { translatedErrors } from '../../../../../utils/translatedErrors';\n\n/**\n * @description This needs wrapping in `yup.object().shape()` before use.\n */\nconst COMMON_USER_SCHEMA = {\n  firstname: yup.string().trim().required({\n    id: translatedErrors.required.id,\n    defaultMessage: 'This field is required',\n  }),\n  lastname: yup.string().nullable(),\n  email: yup.string().email(translatedErrors.email).lowercase().required({\n    id: translatedErrors.required.id,\n    defaultMessage: 'This field is required',\n  }),\n  username: yup\n    .string()\n    .transform((value) => (value === '' ? undefined : value))\n    .nullable(),\n  password: yup\n    .string()\n    .transform((value) => (value === '' || value === null ? undefined : value))\n    .nullable()\n    .min(8, {\n      ...translatedErrors.minLength,\n      values: { min: 8 },\n    })\n    .test(\n      'max-bytes',\n      {\n        id: 'components.Input.error.contain.maxBytes',\n        defaultMessage: 'Password must be less than 73 bytes',\n      },\n      function (value) {\n        if (!value) return true;\n        return new TextEncoder().encode(value).length <= 72;\n      }\n    )\n    .matches(/[a-z]/, {\n      id: 'components.Input.error.contain.lowercase',\n      defaultMessage: 'Password must contain at least one lowercase character',\n    })\n    .matches(/[A-Z]/, {\n      id: 'components.Input.error.contain.uppercase',\n      defaultMessage: 'Password must contain at least one uppercase character',\n    })\n    .matches(/\\d/, {\n      id: 'components.Input.error.contain.number',\n      defaultMessage: 'Password must contain at least one number',\n    }),\n  confirmPassword: yup\n    .string()\n    .transform((value) => (value === '' ? null : value))\n    .nullable()\n    .min(8, {\n      ...translatedErrors.minLength,\n      values: { min: 8 },\n    })\n    .oneOf([yup.ref('password'), null], {\n      id: 'components.Input.error.password.noMatch',\n      defaultMessage: 'Passwords must match',\n    })\n    .when('password', (password, passSchema) => {\n      return password\n        ? passSchema\n            .required({\n              id: translatedErrors.required.id,\n              defaultMessage: 'This field is required',\n            })\n            .nullable()\n        : passSchema;\n    }),\n};\n\nexport { COMMON_USER_SCHEMA };\n"], "mappings": ";;;;;;;;;AAMC,IACKA,qBAAqB;EACzBC,WAAeC,QAAM,EAAGC,KAAI,EAAGC,SAAS;IACtCC,IAAIC,YAAiBF,SAASC;IAC9BE,gBAAgB;EAClB,CAAA;EACAC,UAAcN,QAAM,EAAGO,SAAQ;EAC/BC,OAAWR,QAAM,EAAGQ,MAAMJ,YAAiBI,KAAK,EAAEC,UAAS,EAAGP,SAAS;IACrEC,IAAIC,YAAiBF,SAASC;IAC9BE,gBAAgB;EAClB,CAAA;EACAK,UACGV,QAAM,EACNW,UAAU,CAACC,UAAWA,UAAU,KAAKC,SAAYD,KAAAA,EACjDL,SAAQ;EACXO,UACGd,QAAM,EACNW,UAAU,CAACC,UAAWA,UAAU,MAAMA,UAAU,OAAOC,SAAYD,KAAAA,EACnEL,SAAQ,EACRQ,IAAI,GAAG;IACN,GAAGX,YAAiBY;IACpBC,QAAQ;MAAEF,KAAK;IAAE;GAElBG,EAAAA,KACC,aACA;IACEf,IAAI;IACJE,gBAAgB;EAClB,GACA,SAAUO,OAAK;AACb,QAAI,CAACA,MAAO,QAAO;AACnB,WAAO,IAAIO,YAAcC,EAAAA,OAAOR,KAAAA,EAAOS,UAAU;GAGpDC,EAAAA,QAAQ,SAAS;IAChBnB,IAAI;IACJE,gBAAgB;GAEjBiB,EAAAA,QAAQ,SAAS;IAChBnB,IAAI;IACJE,gBAAgB;GAEjBiB,EAAAA,QAAQ,MAAM;IACbnB,IAAI;IACJE,gBAAgB;EAClB,CAAA;EACFkB,iBACGvB,QAAM,EACNW,UAAU,CAACC,UAAWA,UAAU,KAAK,OAAOA,KAC5CL,EAAAA,SAAQ,EACRQ,IAAI,GAAG;IACN,GAAGX,YAAiBY;IACpBC,QAAQ;MAAEF,KAAK;IAAE;EACnB,CAAA,EACCS,MAAM;IAAKC,OAAI,UAAA;IAAa;KAAO;IAClCtB,IAAI;IACJE,gBAAgB;EAClB,CAAA,EACCqB,KAAK,YAAY,CAACZ,UAAUa,eAAAA;AAC3B,WAAOb,WACHa,WACGzB,SAAS;MACRC,IAAIC,YAAiBF,SAASC;MAC9BE,gBAAgB;IAClB,CAAA,EACCE,SAAQ,IACXoB;EACN,CAAA;AACJ;", "names": ["COMMON_USER_SCHEMA", "firstname", "string", "trim", "required", "id", "translatedErrors", "defaultMessage", "lastname", "nullable", "email", "lowercase", "username", "transform", "value", "undefined", "password", "min", "<PERSON><PERSON><PERSON><PERSON>", "values", "test", "TextEncoder", "encode", "length", "matches", "confirmPassword", "oneOf", "ref", "when", "passSchema"]}