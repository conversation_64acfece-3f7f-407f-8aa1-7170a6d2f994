import {
  createContext
} from "./chunk-BLJUR3JO.js";
import {
  fn
} from "./chunk-QOUV5O5E.js";
import {
  Link,
  useIntl
} from "./chunk-4YYUDJZ2.js";
import {
  NavLink,
  useLocation,
  useNavigate,
  useNavigationType
} from "./chunk-S65ZWNEO.js";
import {
  ForwardRef$5j
} from "./chunk-QW5ZDDTU.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/admin/src/features/BackButton.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var [Provider, useHistory] = createContext("History", {
  history: [],
  currentLocationIndex: 0,
  currentLocation: "",
  canGoBack: false,
  pushState: () => {
    throw new Error("You must use the `HistoryProvider` to access the `pushState` function.");
  },
  goBack: () => {
    throw new Error("You must use the `HistoryProvider` to access the `goBack` function.");
  }
});
var HistoryProvider = ({ children }) => {
  const location = useLocation();
  const navigationType = useNavigationType();
  const navigate = useNavigate();
  const [state, dispatch] = React.useReducer(reducer, {
    history: [],
    currentLocationIndex: 0,
    currentLocation: "",
    canGoBack: false
  });
  const isGoingBack = React.useRef(false);
  const pushState = React.useCallback((path) => {
    dispatch({
      type: "PUSH_STATE",
      payload: typeof path === "string" ? {
        to: path,
        search: ""
      } : path
    });
  }, []);
  const goBack = React.useCallback(() => {
    navigate(-1);
    dispatch({
      type: "GO_BACK"
    });
    isGoingBack.current = true;
  }, [
    navigate
  ]);
  const prevIndex = React.useRef(state.currentLocationIndex);
  React.useEffect(() => {
    if (state.currentLocationIndex !== prevIndex.current) {
      dispatch({
        type: "SET_CAN_GO_BACK",
        payload: state.currentLocationIndex > 1 && state.history.length > 1
      });
      prevIndex.current = state.currentLocationIndex;
    }
  }, [
    prevIndex,
    state.currentLocationIndex,
    state.history.length
  ]);
  React.useLayoutEffect(() => {
    if (isGoingBack.current) {
      isGoingBack.current = false;
    } else if (navigationType === "REPLACE") {
      dispatch({
        type: "REPLACE_STATE",
        payload: {
          to: location.pathname,
          search: location.search
        }
      });
    } else {
      dispatch({
        type: "PUSH_STATE",
        payload: {
          to: location.pathname,
          search: location.search
        }
      });
    }
  }, [
    dispatch,
    location.pathname,
    location.search,
    navigationType
  ]);
  return (0, import_jsx_runtime.jsx)(Provider, {
    pushState,
    goBack,
    ...state,
    children
  });
};
var reducer = (state, action) => fn(state, (draft) => {
  switch (action.type) {
    case "PUSH_STATE": {
      const path = `${action.payload.to}${action.payload.search}`;
      if (state.currentLocationIndex === state.history.length) {
        draft.history = [
          ...state.history,
          path
        ];
      } else {
        draft.history = [
          ...state.history.slice(0, state.currentLocationIndex),
          path
        ];
      }
      draft.currentLocation = path;
      draft.currentLocationIndex += 1;
      break;
    }
    case "REPLACE_STATE": {
      const path = `${action.payload.to}${action.payload.search}`;
      draft.history = [
        ...state.history.slice(0, state.currentLocationIndex - 1),
        path
      ];
      draft.currentLocation = path;
      break;
    }
    case "GO_BACK": {
      const newIndex = state.currentLocationIndex - 1;
      draft.currentLocation = state.history[newIndex - 1];
      draft.currentLocationIndex = newIndex;
      break;
    }
    case "SET_CAN_GO_BACK": {
      draft.canGoBack = action.payload;
      break;
    }
  }
});
var BackButton = React.forwardRef(({ disabled, fallback = "" }, ref) => {
  const { formatMessage } = useIntl();
  const navigate = useNavigate();
  const canGoBack = useHistory("BackButton", (state) => state.canGoBack);
  const goBack = useHistory("BackButton", (state) => state.goBack);
  const history = useHistory("BackButton", (state) => state.history);
  const currentLocationIndex = useHistory("BackButton", (state) => state.currentLocationIndex);
  const hasFallback = fallback !== "";
  const shouldBeDisabled = disabled || !canGoBack && !hasFallback;
  const handleClick = (e) => {
    e.preventDefault();
    if (canGoBack) {
      goBack();
    } else if (hasFallback) {
      navigate(fallback);
    }
  };
  const historyTo = canGoBack ? history.at(currentLocationIndex - 2) : void 0;
  const toWithFallback = historyTo ?? fallback;
  return (0, import_jsx_runtime.jsx)(Link, {
    ref,
    tag: NavLink,
    to: toWithFallback,
    onClick: handleClick,
    disabled: shouldBeDisabled,
    "aria-disabled": shouldBeDisabled,
    startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$5j, {}),
    children: formatMessage({
      id: "global.back",
      defaultMessage: "Back"
    })
  });
});

export {
  useHistory,
  HistoryProvider,
  BackButton
};
//# sourceMappingURL=chunk-COX6EF2B.js.map
