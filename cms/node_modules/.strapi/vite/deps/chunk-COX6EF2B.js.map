{"version": 3, "sources": ["../../../@strapi/admin/admin/src/features/BackButton.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { <PERSON>, LinkProps } from '@strapi/design-system';\nimport { ArrowLeft } from '@strapi/icons';\nimport { produce } from 'immer';\nimport { useIntl } from 'react-intl';\nimport { NavLink, type To, useLocation, useNavigate, useNavigationType } from 'react-router-dom';\n\nimport { createContext } from '../components/Context';\n\n/* -------------------------------------------------------------------------------------------------\n * HistoryProvider\n * -----------------------------------------------------------------------------------------------*/\ninterface HistoryState {\n  /**\n   * The history of the user's navigation within our application\n   * during their current session.\n   */\n  history: string[];\n  /**\n   * The index of the current location in the history array.\n   */\n  currentLocationIndex: number;\n  /**\n   * The current location of the user within our application.\n   */\n  currentLocation: string;\n  /**\n   * Whether the user can go back in the history.\n   */\n  canGoBack: boolean;\n}\n\ninterface HistoryContextValue extends HistoryState {\n  /**\n   * @description Push a new state to the history. You can\n   * either pass a string or an object.\n   */\n  pushState: (\n    path:\n      | {\n          to: string;\n          search: string;\n        }\n      | string\n  ) => void;\n  /**\n   * @description Go back in the history. This calls `navigate(-1)` internally\n   * to keep the browser in sync with the application state.\n   */\n  goBack: () => void;\n}\n\nconst [Provider, useHistory] = createContext<HistoryContextValue>('History', {\n  history: [],\n  currentLocationIndex: 0,\n  currentLocation: '',\n  canGoBack: false,\n  pushState: () => {\n    throw new Error('You must use the `HistoryProvider` to access the `pushState` function.');\n  },\n  goBack: () => {\n    throw new Error('You must use the `HistoryProvider` to access the `goBack` function.');\n  },\n});\n\ninterface HistoryProviderProps {\n  children: React.ReactNode;\n}\n\nconst HistoryProvider = ({ children }: HistoryProviderProps) => {\n  const location = useLocation();\n  const navigationType = useNavigationType();\n  const navigate = useNavigate();\n  const [state, dispatch] = React.useReducer(reducer, {\n    history: [],\n    currentLocationIndex: 0,\n    currentLocation: '',\n    canGoBack: false,\n  });\n\n  const isGoingBack = React.useRef(false);\n\n  const pushState: HistoryContextValue['pushState'] = React.useCallback((path) => {\n    dispatch({\n      type: 'PUSH_STATE',\n      payload: typeof path === 'string' ? { to: path, search: '' } : path,\n    });\n  }, []);\n\n  const goBack: HistoryContextValue['goBack'] = React.useCallback(() => {\n    /**\n     * Perform the browser back action, dispatch the goBack action to keep the state in sync\n     * and set the ref to avoid an infinite loop and incorrect state pushing\n     */\n    navigate(-1);\n    dispatch({ type: 'GO_BACK' });\n    isGoingBack.current = true;\n  }, [navigate]);\n\n  /**\n   * This is a semi-listener pattern to keep the `canGoBack` state in sync.\n   */\n  const prevIndex = React.useRef(state.currentLocationIndex);\n  React.useEffect(() => {\n    if (state.currentLocationIndex !== prevIndex.current) {\n      dispatch({\n        type: 'SET_CAN_GO_BACK',\n        payload: state.currentLocationIndex > 1 && state.history.length > 1,\n      });\n      prevIndex.current = state.currentLocationIndex;\n    }\n  }, [prevIndex, state.currentLocationIndex, state.history.length]);\n\n  /**\n   * This effect is responsible for pushing the new state to the history\n   * when the user navigates to a new location assuming they're not going back.\n   */\n  React.useLayoutEffect(() => {\n    if (isGoingBack.current) {\n      isGoingBack.current = false;\n    } else if (navigationType === 'REPLACE') {\n      // Prevent appending to the history when the location changes via a replace:true navigation\n      dispatch({\n        type: 'REPLACE_STATE',\n        payload: { to: location.pathname, search: location.search },\n      });\n    } else {\n      // this should only occur on link movements, not back/forward clicks\n      dispatch({\n        type: 'PUSH_STATE',\n        payload: { to: location.pathname, search: location.search },\n      });\n    }\n  }, [dispatch, location.pathname, location.search, navigationType]);\n\n  return (\n    <Provider pushState={pushState} goBack={goBack} {...state}>\n      {children}\n    </Provider>\n  );\n};\n\ntype HistoryActions =\n  | {\n      type: 'PUSH_STATE';\n      payload: {\n        to: string;\n        search: string;\n      };\n    }\n  | {\n      type: 'REPLACE_STATE';\n      payload: {\n        to: string;\n        search: string;\n      };\n    }\n  | {\n      type: 'GO_BACK';\n    }\n  | {\n      type: 'SET_CAN_GO_BACK';\n      payload: boolean;\n    };\n\nconst reducer = (state: HistoryState, action: HistoryActions) =>\n  produce(state, (draft) => {\n    switch (action.type) {\n      case 'PUSH_STATE': {\n        const path = `${action.payload.to}${action.payload.search}`;\n        if (state.currentLocationIndex === state.history.length) {\n          // add the new place\n          draft.history = [...state.history, path];\n        } else {\n          // delete all the history after the current place and then add the new place\n          draft.history = [...state.history.slice(0, state.currentLocationIndex), path];\n        }\n\n        draft.currentLocation = path;\n        draft.currentLocationIndex += 1;\n\n        break;\n      }\n      case 'REPLACE_STATE': {\n        const path = `${action.payload.to}${action.payload.search}`;\n        draft.history = [...state.history.slice(0, state.currentLocationIndex - 1), path];\n        draft.currentLocation = path;\n        break;\n      }\n      case 'GO_BACK': {\n        const newIndex = state.currentLocationIndex - 1;\n\n        draft.currentLocation = state.history[newIndex - 1];\n        draft.currentLocationIndex = newIndex;\n        break;\n      }\n      case 'SET_CAN_GO_BACK': {\n        draft.canGoBack = action.payload;\n        break;\n      }\n      default:\n        break;\n    }\n  });\n\n/* -------------------------------------------------------------------------------------------------\n * BackButton\n * -----------------------------------------------------------------------------------------------*/\ninterface BackButtonProps extends Pick<LinkProps, 'disabled'> {\n  fallback?: To;\n}\n\n/**\n * @beta\n * @description The universal back button for the Strapi application. This uses the internal history\n * context to navigate the user back to the previous location. It can be completely disabled in a\n * specific user case. When no history is available, you can provide a fallback destination,\n * otherwise the link will be disabled.\n */\nconst BackButton = React.forwardRef<HTMLAnchorElement, BackButtonProps>(\n  ({ disabled, fallback = '' }, ref) => {\n    const { formatMessage } = useIntl();\n    const navigate = useNavigate();\n\n    const canGoBack = useHistory('BackButton', (state) => state.canGoBack);\n    const goBack = useHistory('BackButton', (state) => state.goBack);\n    const history = useHistory('BackButton', (state) => state.history);\n    const currentLocationIndex = useHistory('BackButton', (state) => state.currentLocationIndex);\n    const hasFallback = fallback !== '';\n    const shouldBeDisabled = disabled || (!canGoBack && !hasFallback);\n\n    const handleClick = (e: React.MouseEvent<HTMLAnchorElement>) => {\n      e.preventDefault();\n\n      if (canGoBack) {\n        goBack();\n      } else if (hasFallback) {\n        navigate(fallback);\n      }\n    };\n\n    // The link destination from the history. Undefined if there is only 1 location in the history.\n    const historyTo = canGoBack ? history.at(currentLocationIndex - 2) : undefined;\n    // If no link destination from the history, use the fallback.\n    const toWithFallback = historyTo ?? fallback;\n\n    return (\n      <Link\n        ref={ref}\n        tag={NavLink}\n        to={toWithFallback}\n        onClick={handleClick}\n        disabled={shouldBeDisabled}\n        aria-disabled={shouldBeDisabled}\n        startIcon={<ArrowLeft />}\n      >\n        {formatMessage({\n          id: 'global.back',\n          defaultMessage: 'Back',\n        })}\n      </Link>\n    );\n  }\n);\n\nexport { BackButton, HistoryProvider, useHistory };\nexport type { BackButtonProps, HistoryProviderProps, HistoryContextValue, HistoryState };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqDA,IAAM,CAACA,UAAUC,UAAW,IAAGC,cAAmC,WAAW;EAC3EC,SAAS,CAAA;EACTC,sBAAsB;EACtBC,iBAAiB;EACjBC,WAAW;EACXC,WAAW,MAAA;AACT,UAAM,IAAIC,MAAM,wEAAA;EAClB;EACAC,QAAQ,MAAA;AACN,UAAM,IAAID,MAAM,qEAAA;EAClB;AACF,CAAA;AAMA,IAAME,kBAAkB,CAAC,EAAEC,SAAQ,MAAwB;AACzD,QAAMC,WAAWC,YAAAA;AACjB,QAAMC,iBAAiBC,kBAAAA;AACvB,QAAMC,WAAWC,YAAAA;AACjB,QAAM,CAACC,OAAOC,QAAAA,IAAkBC,iBAAWC,SAAS;IAClDlB,SAAS,CAAA;IACTC,sBAAsB;IACtBC,iBAAiB;IACjBC,WAAW;EACb,CAAA;AAEA,QAAMgB,cAAoBC,aAAO,KAAA;AAEjC,QAAMhB,YAAoDiB,kBAAY,CAACC,SAAAA;AACrEN,aAAS;MACPO,MAAM;MACNC,SAAS,OAAOF,SAAS,WAAW;QAAEG,IAAIH;QAAMI,QAAQ;UAAOJ;IACjE,CAAA;EACF,GAAG,CAAA,CAAE;AAEL,QAAMhB,SAA8Ce,kBAAY,MAAA;AAK9DR,aAAS,EAAC;AACVG,aAAS;MAAEO,MAAM;IAAU,CAAA;AAC3BJ,gBAAYQ,UAAU;KACrB;IAACd;EAAS,CAAA;AAKb,QAAMe,YAAkBR,aAAOL,MAAMd,oBAAoB;AACzD4B,EAAMC,gBAAU,MAAA;AACd,QAAIf,MAAMd,yBAAyB2B,UAAUD,SAAS;AACpDX,eAAS;QACPO,MAAM;QACNC,SAAST,MAAMd,uBAAuB,KAAKc,MAAMf,QAAQ+B,SAAS;MACpE,CAAA;AACAH,gBAAUD,UAAUZ,MAAMd;IAC5B;KACC;IAAC2B;IAAWb,MAAMd;IAAsBc,MAAMf,QAAQ+B;EAAO,CAAA;AAMhEF,EAAMG,sBAAgB,MAAA;AACpB,QAAIb,YAAYQ,SAAS;AACvBR,kBAAYQ,UAAU;eACbhB,mBAAmB,WAAW;AAEvCK,eAAS;QACPO,MAAM;QACNC,SAAS;UAAEC,IAAIhB,SAASwB;UAAUP,QAAQjB,SAASiB;QAAO;MAC5D,CAAA;WACK;AAELV,eAAS;QACPO,MAAM;QACNC,SAAS;UAAEC,IAAIhB,SAASwB;UAAUP,QAAQjB,SAASiB;QAAO;MAC5D,CAAA;IACF;KACC;IAACV;IAAUP,SAASwB;IAAUxB,SAASiB;IAAQf;EAAe,CAAA;AAEjE,aACEuB,wBAACrC,UAAAA;IAASO;IAAsBE;IAAiB,GAAGS;IACjDP;;AAGP;AAyBA,IAAMU,UAAU,CAACH,OAAqBoB,WACpCC,GAAQrB,OAAO,CAACsB,UAAAA;AACd,UAAQF,OAAOZ,MAAI;IACjB,KAAK,cAAc;AACjB,YAAMD,OAAO,GAAGa,OAAOX,QAAQC,EAAE,GAAGU,OAAOX,QAAQE,MAAM;AACzD,UAAIX,MAAMd,yBAAyBc,MAAMf,QAAQ+B,QAAQ;AAEvDM,cAAMrC,UAAU;UAAIe,GAAAA,MAAMf;UAASsB;QAAK;aACnC;AAELe,cAAMrC,UAAU;UAAIe,GAAAA,MAAMf,QAAQsC,MAAM,GAAGvB,MAAMd,oBAAoB;UAAGqB;QAAK;MAC/E;AAEAe,YAAMnC,kBAAkBoB;AACxBe,YAAMpC,wBAAwB;AAE9B;IACF;IACA,KAAK,iBAAiB;AACpB,YAAMqB,OAAO,GAAGa,OAAOX,QAAQC,EAAE,GAAGU,OAAOX,QAAQE,MAAM;AACzDW,YAAMrC,UAAU;QAAIe,GAAAA,MAAMf,QAAQsC,MAAM,GAAGvB,MAAMd,uBAAuB,CAAA;QAAIqB;MAAK;AACjFe,YAAMnC,kBAAkBoB;AACxB;IACF;IACA,KAAK,WAAW;AACd,YAAMiB,WAAWxB,MAAMd,uBAAuB;AAE9CoC,YAAMnC,kBAAkBa,MAAMf,QAAQuC,WAAW,CAAE;AACnDF,YAAMpC,uBAAuBsC;AAC7B;IACF;IACA,KAAK,mBAAmB;AACtBF,YAAMlC,YAAYgC,OAAOX;AACzB;IACF;EAGF;AACF,CAAA;AAgBIgB,IAAAA,aAAmBC,iBACvB,CAAC,EAAEC,UAAUC,WAAW,GAAE,GAAIC,QAAAA;AAC5B,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAMjC,WAAWC,YAAAA;AAEjB,QAAMX,YAAYL,WAAW,cAAc,CAACiB,UAAUA,MAAMZ,SAAS;AACrE,QAAMG,SAASR,WAAW,cAAc,CAACiB,UAAUA,MAAMT,MAAM;AAC/D,QAAMN,UAAUF,WAAW,cAAc,CAACiB,UAAUA,MAAMf,OAAO;AACjE,QAAMC,uBAAuBH,WAAW,cAAc,CAACiB,UAAUA,MAAMd,oBAAoB;AAC3F,QAAM8C,cAAcJ,aAAa;AACjC,QAAMK,mBAAmBN,YAAa,CAACvC,aAAa,CAAC4C;AAErD,QAAME,cAAc,CAACC,MAAAA;AACnBA,MAAEC,eAAc;AAEhB,QAAIhD,WAAW;AACbG,aAAAA;IACF,WAAWyC,aAAa;AACtBlC,eAAS8B,QAAAA;IACX;EACF;AAGA,QAAMS,YAAYjD,YAAYH,QAAQqD,GAAGpD,uBAAuB,CAAKqD,IAAAA;AAErE,QAAMC,iBAAiBH,aAAaT;AAEpC,aACET,wBAACsB,MAAAA;IACCZ;IACAa,KAAKC;IACLjC,IAAI8B;IACJI,SAASV;IACTP,UAAUM;IACVY,iBAAeZ;IACfa,eAAW3B,wBAAC4B,eAAAA,CAAAA,CAAAA;cAEXjB,cAAc;MACbkB,IAAI;MACJC,gBAAgB;IAClB,CAAA;;AAGN,CAAA;", "names": ["Provider", "useHistory", "createContext", "history", "currentLocationIndex", "currentLocation", "canGoBack", "pushState", "Error", "goBack", "HistoryProvider", "children", "location", "useLocation", "navigationType", "useNavigationType", "navigate", "useNavigate", "state", "dispatch", "useReducer", "reducer", "isGoingBack", "useRef", "useCallback", "path", "type", "payload", "to", "search", "current", "prevIndex", "React", "useEffect", "length", "useLayoutEffect", "pathname", "_jsx", "action", "produce", "draft", "slice", "newIndex", "BackButton", "forwardRef", "disabled", "fallback", "ref", "formatMessage", "useIntl", "<PERSON><PERSON><PERSON><PERSON>", "shouldBeDisabled", "handleClick", "e", "preventDefault", "historyTo", "at", "undefined", "to<PERSON><PERSON><PERSON><PERSON>back", "Link", "tag", "NavLink", "onClick", "aria-disabled", "startIcon", "ArrowLeft", "id", "defaultMessage"]}