import {
  useNpsSurveySettings
} from "./chunk-YTYOKH3J.js";
import {
  QueryClient,
  QueryClientProvider,
  img
} from "./chunk-BI5NNIZJ.js";
import {
  ADMIN_PERMISSIONS_CE,
  HOOKS
} from "./chunk-NG2C3VVD.js";
import {
  GuidedTourProvider,
  useGuidedTour
} from "./chunk-HTIJXKV5.js";
import {
  require_pick
} from "./chunk-U2WQMOGI.js";
import {
  useEnterprise
} from "./chunk-Y7ATVFIR.js";
import {
  Login
} from "./chunk-SWRLCKDU.js";
import {
  Column,
  LayoutContent,
  Logo,
  UnauthenticatedLayout
} from "./chunk-QQYLVHWD.js";
import {
  ConfigurationProvider
} from "./chunk-QU5YEK3U.js";
import {
  require_baseMerge,
  require_createAssigner,
  require_merge,
  require_omit
} from "./chunk-X26IT6UA.js";
import {
  require_baseRest
} from "./chunk-MYUJRQKL.js";
import {
  HistoryProvider
} from "./chunk-COX6EF2B.js";
import {
  errorsTrads
} from "./chunk-IFOFBKTA.js";
import {
  ValidationError,
  create,
  create4 as create2,
  create6 as create3
} from "./chunk-4WX3TY7V.js";
import {
  TrackingProvider,
  useTracking
} from "./chunk-USP7YHPW.js";
import {
  createAbsoluteUrl,
  useGetGuidedTourMetaQuery,
  useInitQuery
} from "./chunk-LLJ4ZRUJ.js";
import {
  getBasename
} from "./chunk-QIJGNK42.js";
import {
  useClipboard
} from "./chunk-X6QBIBK4.js";
import {
  ADMIN_PERMISSIONS_EE,
  getEERoutes
} from "./chunk-YMKQOWME.js";
import {
  MemoizedInputRenderer
} from "./chunk-QQ4VIATE.js";
import {
  Form
} from "./chunk-VWIQ6S3Y.js";
import {
  Layouts
} from "./chunk-IL5LQ6CF.js";
import {
  getByteSize
} from "./chunk-PQINNV4N.js";
import {
  require_isEqual
} from "./chunk-VYSYYPOB.js";
import {
  ForwardRef$F,
  Page,
  useAPIErrorHandler
} from "./chunk-3UVBSCPH.js";
import {
  AuthProvider,
  LANGUAGE_LOCAL_STORAGE_KEY,
  StrapiAppProvider,
  THEME_LOCAL_STORAGE_KEY,
  getStoredToken,
  login,
  logout,
  reducer,
  setAvailableThemes,
  useAuth,
  useForgotPasswordMutation,
  useGetRegistrationInfoQuery,
  useRegisterAdminMutation,
  useRegisterUserMutation,
  useResetPasswordMutation
} from "./chunk-JXTOCMQW.js";
import {
  useTypedDispatch,
  useTypedSelector,
  useTypedStore
} from "./chunk-B33LMLYV.js";
import {
  adminApi,
  getFetchClient,
  isBaseQueryError,
  require_apply,
  require_toNumber
} from "./chunk-XU64YSEO.js";
import {
  Provider_default,
  combineReducers,
  configureStore,
  isRejected,
  useDispatch
} from "./chunk-IHIZNGTI.js";
import {
  createContext
} from "./chunk-BLJUR3JO.js";
import {
  require_isFunction,
  require_isObject,
  require_root
} from "./chunk-CE4VABH2.js";
import {
  L,
  fn
} from "./chunk-QOUV5O5E.js";
import {
  NotificationsProvider,
  useNotification
} from "./chunk-4EY5FDQG.js";
import {
  Alert,
  Box,
  Button,
  DesignSystemProvider,
  EmptyStateLayout,
  Flex,
  Grid,
  Link as Link2,
  LinkButton,
  Main,
  Popover,
  Typography,
  darkTheme,
  lightTheme,
  message_default,
  provider_default,
  useIntl
} from "./chunk-4YYUDJZ2.js";
import {
  require_client
} from "./chunk-5ZC4PE57.js";
import {
  Link,
  NavLink,
  Navigate,
  Outlet,
  RouterProvider,
  createBrowserRouter,
  createMemoryRouter,
  useLocation,
  useMatch,
  useNavigate,
  useRouteError
} from "./chunk-S65ZWNEO.js";
import {
  ForwardRef$3,
  ForwardRef$3R,
  ForwardRef$5d
} from "./chunk-QW5ZDDTU.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  dt,
  ft
} from "./chunk-3CQBCJ3G.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __commonJS,
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/invariant/browser.js
var require_browser = __commonJS({
  "node_modules/invariant/browser.js"(exports, module2) {
    "use strict";
    var invariant5 = function(condition, format, a, b, c, d, e, f) {
      if (true) {
        if (format === void 0) {
          throw new Error("invariant requires an error message argument");
        }
      }
      if (!condition) {
        var error;
        if (format === void 0) {
          error = new Error(
            "Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings."
          );
        } else {
          var args = [a, b, c, d, e, f];
          var argIndex = 0;
          error = new Error(
            format.replace(/%s/g, function() {
              return args[argIndex++];
            })
          );
          error.name = "Invariant Violation";
        }
        error.framesToPop = 1;
        throw error;
      }
    };
    module2.exports = invariant5;
  }
});

// node_modules/lodash/_customDefaultsMerge.js
var require_customDefaultsMerge = __commonJS({
  "node_modules/lodash/_customDefaultsMerge.js"(exports, module2) {
    var baseMerge = require_baseMerge();
    var isObject = require_isObject();
    function customDefaultsMerge(objValue, srcValue, key, object, source, stack) {
      if (isObject(objValue) && isObject(srcValue)) {
        stack.set(srcValue, objValue);
        baseMerge(objValue, srcValue, void 0, customDefaultsMerge, stack);
        stack["delete"](srcValue);
      }
      return objValue;
    }
    module2.exports = customDefaultsMerge;
  }
});

// node_modules/lodash/mergeWith.js
var require_mergeWith = __commonJS({
  "node_modules/lodash/mergeWith.js"(exports, module2) {
    var baseMerge = require_baseMerge();
    var createAssigner = require_createAssigner();
    var mergeWith = createAssigner(function(object, source, srcIndex, customizer) {
      baseMerge(object, source, srcIndex, customizer);
    });
    module2.exports = mergeWith;
  }
});

// node_modules/lodash/defaultsDeep.js
var require_defaultsDeep = __commonJS({
  "node_modules/lodash/defaultsDeep.js"(exports, module2) {
    var apply = require_apply();
    var baseRest = require_baseRest();
    var customDefaultsMerge = require_customDefaultsMerge();
    var mergeWith = require_mergeWith();
    var defaultsDeep2 = baseRest(function(args) {
      args.push(void 0, customDefaultsMerge);
      return apply(mergeWith, void 0, args);
    });
    module2.exports = defaultsDeep2;
  }
});

// node_modules/lodash/now.js
var require_now = __commonJS({
  "node_modules/lodash/now.js"(exports, module2) {
    var root = require_root();
    var now = function() {
      return root.Date.now();
    };
    module2.exports = now;
  }
});

// node_modules/lodash/debounce.js
var require_debounce = __commonJS({
  "node_modules/lodash/debounce.js"(exports, module2) {
    var isObject = require_isObject();
    var now = require_now();
    var toNumber = require_toNumber();
    var FUNC_ERROR_TEXT = "Expected a function";
    var nativeMax = Math.max;
    var nativeMin = Math.min;
    function debounce(func, wait, options) {
      var lastArgs, lastThis, maxWait, result, timerId, lastCallTime, lastInvokeTime = 0, leading = false, maxing = false, trailing = true;
      if (typeof func != "function") {
        throw new TypeError(FUNC_ERROR_TEXT);
      }
      wait = toNumber(wait) || 0;
      if (isObject(options)) {
        leading = !!options.leading;
        maxing = "maxWait" in options;
        maxWait = maxing ? nativeMax(toNumber(options.maxWait) || 0, wait) : maxWait;
        trailing = "trailing" in options ? !!options.trailing : trailing;
      }
      function invokeFunc(time) {
        var args = lastArgs, thisArg = lastThis;
        lastArgs = lastThis = void 0;
        lastInvokeTime = time;
        result = func.apply(thisArg, args);
        return result;
      }
      function leadingEdge(time) {
        lastInvokeTime = time;
        timerId = setTimeout(timerExpired, wait);
        return leading ? invokeFunc(time) : result;
      }
      function remainingWait(time) {
        var timeSinceLastCall = time - lastCallTime, timeSinceLastInvoke = time - lastInvokeTime, timeWaiting = wait - timeSinceLastCall;
        return maxing ? nativeMin(timeWaiting, maxWait - timeSinceLastInvoke) : timeWaiting;
      }
      function shouldInvoke(time) {
        var timeSinceLastCall = time - lastCallTime, timeSinceLastInvoke = time - lastInvokeTime;
        return lastCallTime === void 0 || timeSinceLastCall >= wait || timeSinceLastCall < 0 || maxing && timeSinceLastInvoke >= maxWait;
      }
      function timerExpired() {
        var time = now();
        if (shouldInvoke(time)) {
          return trailingEdge(time);
        }
        timerId = setTimeout(timerExpired, remainingWait(time));
      }
      function trailingEdge(time) {
        timerId = void 0;
        if (trailing && lastArgs) {
          return invokeFunc(time);
        }
        lastArgs = lastThis = void 0;
        return result;
      }
      function cancel() {
        if (timerId !== void 0) {
          clearTimeout(timerId);
        }
        lastInvokeTime = 0;
        lastArgs = lastCallTime = lastThis = timerId = void 0;
      }
      function flush() {
        return timerId === void 0 ? result : trailingEdge(now());
      }
      function debounced() {
        var time = now(), isInvoking = shouldInvoke(time);
        lastArgs = arguments;
        lastThis = this;
        lastCallTime = time;
        if (isInvoking) {
          if (timerId === void 0) {
            return leadingEdge(lastCallTime);
          }
          if (maxing) {
            clearTimeout(timerId);
            timerId = setTimeout(timerExpired, wait);
            return invokeFunc(lastCallTime);
          }
        }
        if (timerId === void 0) {
          timerId = setTimeout(timerExpired, wait);
        }
        return result;
      }
      debounced.cancel = cancel;
      debounced.flush = flush;
      return debounced;
    }
    module2.exports = debounce;
  }
});

// node_modules/lodash/throttle.js
var require_throttle = __commonJS({
  "node_modules/lodash/throttle.js"(exports, module2) {
    var debounce = require_debounce();
    var isObject = require_isObject();
    var FUNC_ERROR_TEXT = "Expected a function";
    function throttle2(func, wait, options) {
      var leading = true, trailing = true;
      if (typeof func != "function") {
        throw new TypeError(FUNC_ERROR_TEXT);
      }
      if (isObject(options)) {
        leading = "leading" in options ? !!options.leading : leading;
        trailing = "trailing" in options ? !!options.trailing : trailing;
      }
      return debounce(func, wait, {
        "leading": leading,
        "maxWait": wait,
        "trailing": trailing
      });
    }
    module2.exports = throttle2;
  }
});

// node_modules/json-logic-js/logic.js
var require_logic = __commonJS({
  "node_modules/json-logic-js/logic.js"(exports, module2) {
    (function(root, factory) {
      if (typeof define === "function" && define.amd) {
        define(factory);
      } else if (typeof exports === "object") {
        module2.exports = factory();
      } else {
        root.jsonLogic = factory();
      }
    })(exports, function() {
      "use strict";
      if (!Array.isArray) {
        Array.isArray = function(arg) {
          return Object.prototype.toString.call(arg) === "[object Array]";
        };
      }
      function arrayUnique(array) {
        var a = [];
        for (var i = 0, l = array.length; i < l; i++) {
          if (a.indexOf(array[i]) === -1) {
            a.push(array[i]);
          }
        }
        return a;
      }
      var jsonLogic2 = {};
      var operations = {
        "==": function(a, b) {
          return a == b;
        },
        "===": function(a, b) {
          return a === b;
        },
        "!=": function(a, b) {
          return a != b;
        },
        "!==": function(a, b) {
          return a !== b;
        },
        ">": function(a, b) {
          return a > b;
        },
        ">=": function(a, b) {
          return a >= b;
        },
        "<": function(a, b, c) {
          return c === void 0 ? a < b : a < b && b < c;
        },
        "<=": function(a, b, c) {
          return c === void 0 ? a <= b : a <= b && b <= c;
        },
        "!!": function(a) {
          return jsonLogic2.truthy(a);
        },
        "!": function(a) {
          return !jsonLogic2.truthy(a);
        },
        "%": function(a, b) {
          return a % b;
        },
        "log": function(a) {
          console.log(a);
          return a;
        },
        "in": function(a, b) {
          if (!b || typeof b.indexOf === "undefined") return false;
          return b.indexOf(a) !== -1;
        },
        "cat": function() {
          return Array.prototype.join.call(arguments, "");
        },
        "substr": function(source, start, end) {
          if (end < 0) {
            var temp = String(source).substr(start);
            return temp.substr(0, temp.length + end);
          }
          return String(source).substr(start, end);
        },
        "+": function() {
          return Array.prototype.reduce.call(arguments, function(a, b) {
            return parseFloat(a, 10) + parseFloat(b, 10);
          }, 0);
        },
        "*": function() {
          return Array.prototype.reduce.call(arguments, function(a, b) {
            return parseFloat(a, 10) * parseFloat(b, 10);
          });
        },
        "-": function(a, b) {
          if (b === void 0) {
            return -a;
          } else {
            return a - b;
          }
        },
        "/": function(a, b) {
          return a / b;
        },
        "min": function() {
          return Math.min.apply(this, arguments);
        },
        "max": function() {
          return Math.max.apply(this, arguments);
        },
        "merge": function() {
          return Array.prototype.reduce.call(arguments, function(a, b) {
            return a.concat(b);
          }, []);
        },
        "var": function(a, b) {
          var not_found = b === void 0 ? null : b;
          var data = this;
          if (typeof a === "undefined" || a === "" || a === null) {
            return data;
          }
          var sub_props = String(a).split(".");
          for (var i = 0; i < sub_props.length; i++) {
            if (data === null || data === void 0) {
              return not_found;
            }
            data = data[sub_props[i]];
            if (data === void 0) {
              return not_found;
            }
          }
          return data;
        },
        "missing": function() {
          var missing = [];
          var keys = Array.isArray(arguments[0]) ? arguments[0] : arguments;
          for (var i = 0; i < keys.length; i++) {
            var key = keys[i];
            var value = jsonLogic2.apply({ "var": key }, this);
            if (value === null || value === "") {
              missing.push(key);
            }
          }
          return missing;
        },
        "missing_some": function(need_count, options) {
          var are_missing = jsonLogic2.apply({ "missing": options }, this);
          if (options.length - are_missing.length >= need_count) {
            return [];
          } else {
            return are_missing;
          }
        }
      };
      jsonLogic2.is_logic = function(logic) {
        return typeof logic === "object" && // An object
        logic !== null && // but not null
        !Array.isArray(logic) && // and not an array
        Object.keys(logic).length === 1;
      };
      jsonLogic2.truthy = function(value) {
        if (Array.isArray(value) && value.length === 0) {
          return false;
        }
        return !!value;
      };
      jsonLogic2.get_operator = function(logic) {
        return Object.keys(logic)[0];
      };
      jsonLogic2.get_values = function(logic) {
        return logic[jsonLogic2.get_operator(logic)];
      };
      jsonLogic2.apply = function(logic, data) {
        if (Array.isArray(logic)) {
          return logic.map(function(l) {
            return jsonLogic2.apply(l, data);
          });
        }
        if (!jsonLogic2.is_logic(logic)) {
          return logic;
        }
        var op = jsonLogic2.get_operator(logic);
        var values = logic[op];
        var i;
        var current;
        var scopedLogic;
        var scopedData;
        var initial;
        if (!Array.isArray(values)) {
          values = [values];
        }
        if (op === "if" || op == "?:") {
          for (i = 0; i < values.length - 1; i += 2) {
            if (jsonLogic2.truthy(jsonLogic2.apply(values[i], data))) {
              return jsonLogic2.apply(values[i + 1], data);
            }
          }
          if (values.length === i + 1) {
            return jsonLogic2.apply(values[i], data);
          }
          return null;
        } else if (op === "and") {
          for (i = 0; i < values.length; i += 1) {
            current = jsonLogic2.apply(values[i], data);
            if (!jsonLogic2.truthy(current)) {
              return current;
            }
          }
          return current;
        } else if (op === "or") {
          for (i = 0; i < values.length; i += 1) {
            current = jsonLogic2.apply(values[i], data);
            if (jsonLogic2.truthy(current)) {
              return current;
            }
          }
          return current;
        } else if (op === "filter") {
          scopedData = jsonLogic2.apply(values[0], data);
          scopedLogic = values[1];
          if (!Array.isArray(scopedData)) {
            return [];
          }
          return scopedData.filter(function(datum) {
            return jsonLogic2.truthy(jsonLogic2.apply(scopedLogic, datum));
          });
        } else if (op === "map") {
          scopedData = jsonLogic2.apply(values[0], data);
          scopedLogic = values[1];
          if (!Array.isArray(scopedData)) {
            return [];
          }
          return scopedData.map(function(datum) {
            return jsonLogic2.apply(scopedLogic, datum);
          });
        } else if (op === "reduce") {
          scopedData = jsonLogic2.apply(values[0], data);
          scopedLogic = values[1];
          initial = typeof values[2] !== "undefined" ? jsonLogic2.apply(values[2], data) : null;
          if (!Array.isArray(scopedData)) {
            return initial;
          }
          return scopedData.reduce(
            function(accumulator, current2) {
              return jsonLogic2.apply(
                scopedLogic,
                { current: current2, accumulator }
              );
            },
            initial
          );
        } else if (op === "all") {
          scopedData = jsonLogic2.apply(values[0], data);
          scopedLogic = values[1];
          if (!Array.isArray(scopedData) || !scopedData.length) {
            return false;
          }
          for (i = 0; i < scopedData.length; i += 1) {
            if (!jsonLogic2.truthy(jsonLogic2.apply(scopedLogic, scopedData[i]))) {
              return false;
            }
          }
          return true;
        } else if (op === "none") {
          scopedData = jsonLogic2.apply(values[0], data);
          scopedLogic = values[1];
          if (!Array.isArray(scopedData) || !scopedData.length) {
            return true;
          }
          for (i = 0; i < scopedData.length; i += 1) {
            if (jsonLogic2.truthy(jsonLogic2.apply(scopedLogic, scopedData[i]))) {
              return false;
            }
          }
          return true;
        } else if (op === "some") {
          scopedData = jsonLogic2.apply(values[0], data);
          scopedLogic = values[1];
          if (!Array.isArray(scopedData) || !scopedData.length) {
            return false;
          }
          for (i = 0; i < scopedData.length; i += 1) {
            if (jsonLogic2.truthy(jsonLogic2.apply(scopedLogic, scopedData[i]))) {
              return true;
            }
          }
          return false;
        }
        values = values.map(function(val) {
          return jsonLogic2.apply(val, data);
        });
        if (operations.hasOwnProperty(op) && typeof operations[op] === "function") {
          return operations[op].apply(data, values);
        } else if (op.indexOf(".") > 0) {
          var sub_ops = String(op).split(".");
          var operation = operations;
          for (i = 0; i < sub_ops.length; i++) {
            if (!operation.hasOwnProperty(sub_ops[i])) {
              throw new Error("Unrecognized operation " + op + " (failed at " + sub_ops.slice(0, i + 1).join(".") + ")");
            }
            operation = operation[sub_ops[i]];
          }
          return operation.apply(data, values);
        }
        throw new Error("Unrecognized operation " + op);
      };
      jsonLogic2.uses_data = function(logic) {
        var collection = [];
        if (jsonLogic2.is_logic(logic)) {
          var op = jsonLogic2.get_operator(logic);
          var values = logic[op];
          if (!Array.isArray(values)) {
            values = [values];
          }
          if (op === "var") {
            collection.push(values[0]);
          } else {
            values.forEach(function(val) {
              collection.push.apply(collection, jsonLogic2.uses_data(val));
            });
          }
        }
        return arrayUnique(collection);
      };
      jsonLogic2.add_operation = function(name, code) {
        operations[name] = code;
      };
      jsonLogic2.rm_operation = function(name) {
        delete operations[name];
      };
      jsonLogic2.rule_like = function(rule, pattern) {
        if (pattern === rule) {
          return true;
        }
        if (pattern === "@") {
          return true;
        }
        if (pattern === "number") {
          return typeof rule === "number";
        }
        if (pattern === "string") {
          return typeof rule === "string";
        }
        if (pattern === "array") {
          return Array.isArray(rule) && !jsonLogic2.is_logic(rule);
        }
        if (jsonLogic2.is_logic(pattern)) {
          if (jsonLogic2.is_logic(rule)) {
            var pattern_op = jsonLogic2.get_operator(pattern);
            var rule_op = jsonLogic2.get_operator(rule);
            if (pattern_op === "@" || pattern_op === rule_op) {
              return jsonLogic2.rule_like(
                jsonLogic2.get_values(rule, false),
                jsonLogic2.get_values(pattern, false)
              );
            }
          }
          return false;
        }
        if (Array.isArray(pattern)) {
          if (Array.isArray(rule)) {
            if (pattern.length !== rule.length) {
              return false;
            }
            for (var i = 0; i < pattern.length; i += 1) {
              if (!jsonLogic2.rule_like(rule[i], pattern[i])) {
                return false;
              }
            }
            return true;
          } else {
            return false;
          }
        }
        return false;
      };
      return jsonLogic2;
    });
  }
});

// node_modules/@strapi/admin/dist/admin/admin/src/components/UnstableGuidedTour/Tours.mjs
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var React3 = __toESM(require_react(), 1);

// node_modules/@strapi/admin/dist/admin/admin/src/components/UnstableGuidedTour/Context.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var [GuidedTourProviderImpl, unstableUseGuidedTour] = createContext("UnstableGuidedTour");
function reducer2(state, action) {
  return fn(state, (draft) => {
    if (action.type === "next_step") {
      const nextStep = draft.tours[action.payload].currentStep + 1;
      draft.tours[action.payload].currentStep = nextStep;
      draft.tours[action.payload].isCompleted = nextStep === draft.tours[action.payload].length;
    }
    if (action.type === "skip_tour") {
      draft.tours[action.payload].isCompleted = true;
    }
  });
}
var UnstableGuidedTourContext = ({ children, tours: registeredTours }) => {
  const tours2 = Object.keys(registeredTours).reduce((acc, tourName) => {
    const tourLength = Object.keys(registeredTours[tourName]).length;
    acc[tourName] = {
      currentStep: 0,
      length: tourLength,
      isCompleted: false
    };
    return acc;
  }, {});
  const [state, dispatch] = React.useReducer(reducer2, {
    tours: tours2
  });
  return (0, import_jsx_runtime.jsx)(GuidedTourProviderImpl, {
    state,
    dispatch,
    children
  });
};

// node_modules/@strapi/admin/dist/admin/admin/src/components/UnstableGuidedTour/Step.mjs
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var React2 = __toESM(require_react(), 1);
var ActionsContainer = dt(Flex)`
  border-top: ${({ theme }) => `1px solid ${theme.colors.neutral150}`};
`;
var createStepComponents = (tourName) => ({
  Root: React2.forwardRef((props, ref) => (0, import_jsx_runtime2.jsx)(Popover.Content, {
    ref,
    side: "top",
    align: "center",
    ...props,
    children: (0, import_jsx_runtime2.jsx)(Flex, {
      width: "360px",
      direction: "column",
      alignItems: "start",
      children: props.children
    })
  })),
  Title: (props) => {
    return (0, import_jsx_runtime2.jsx)(Box, {
      paddingTop: 5,
      paddingLeft: 5,
      paddingRight: 5,
      paddingBottom: 1,
      width: "100%",
      children: "children" in props ? props.children : (0, import_jsx_runtime2.jsx)(Typography, {
        tag: "div",
        variant: "omega",
        fontWeight: "bold",
        children: (0, import_jsx_runtime2.jsx)(message_default, {
          tagName: "h1",
          id: props.id,
          defaultMessage: props.defaultMessage
        })
      })
    });
  },
  Content: (props) => (0, import_jsx_runtime2.jsx)(Box, {
    paddingBottom: 5,
    paddingLeft: 5,
    paddingRight: 5,
    width: "100%",
    children: "children" in props ? props.children : (0, import_jsx_runtime2.jsx)(Typography, {
      tag: "div",
      variant: "omega",
      children: (0, import_jsx_runtime2.jsx)(message_default, {
        tagName: "p",
        id: props.id,
        defaultMessage: props.defaultMessage
      })
    })
  }),
  Actions: ({ showStepCount = true, showSkip = false, ...props }) => {
    const dispatch = unstableUseGuidedTour("GuidedTourPopover", (s) => s.dispatch);
    const state = unstableUseGuidedTour("GuidedTourPopover", (s) => s.state);
    const currentStep = state.tours[tourName].currentStep + 1;
    const tourLength = state.tours[tourName].length;
    return (0, import_jsx_runtime2.jsx)(ActionsContainer, {
      width: "100%",
      padding: 3,
      paddingLeft: 5,
      children: "children" in props ? props.children : (0, import_jsx_runtime2.jsxs)(Flex, {
        flex: 1,
        justifyContent: showStepCount ? "space-between" : "flex-end",
        children: [
          showStepCount && (0, import_jsx_runtime2.jsx)(Typography, {
            variant: "omega",
            fontSize: "12px",
            children: (0, import_jsx_runtime2.jsx)(message_default, {
              id: "tours.stepCount",
              defaultMessage: "Step {currentStep} of {tourLength}",
              values: {
                currentStep,
                tourLength
              }
            })
          }),
          (0, import_jsx_runtime2.jsxs)(Flex, {
            gap: 2,
            children: [
              showSkip && (0, import_jsx_runtime2.jsx)(Button, {
                variant: "tertiary",
                onClick: () => dispatch({
                  type: "skip_tour",
                  payload: tourName
                }),
                children: (0, import_jsx_runtime2.jsx)(message_default, {
                  id: "tours.skip",
                  defaultMessage: "Skip"
                })
              }),
              (0, import_jsx_runtime2.jsx)(Button, {
                onClick: () => dispatch({
                  type: "next_step",
                  payload: tourName
                }),
                children: (0, import_jsx_runtime2.jsx)(message_default, {
                  id: "tours.next",
                  defaultMessage: "Next"
                })
              })
            ]
          })
        ]
      })
    });
  }
});

// node_modules/@strapi/admin/dist/admin/admin/src/components/UnstableGuidedTour/Tours.mjs
var tours = {
  TEST: createTour("TEST", [
    {
      name: "Introduction",
      content: (Step) => (0, import_jsx_runtime3.jsxs)(Step.Root, {
        sideOffset: -36,
        children: [
          (0, import_jsx_runtime3.jsx)(Step.Title, {
            id: "tours.contentManager.Introduction.title",
            defaultMessage: "Content manager"
          }),
          (0, import_jsx_runtime3.jsx)(Step.Content, {
            id: "tours.contentManager.Introduction.content",
            defaultMessage: "Create and manage content from your collection types and single types."
          }),
          (0, import_jsx_runtime3.jsx)(Step.Actions, {
            showSkip: true
          })
        ]
      })
    },
    {
      name: "Done",
      requiredActions: [
        "didCreateApiToken"
      ],
      content: (Step) => (0, import_jsx_runtime3.jsxs)(Step.Root, {
        align: "start",
        children: [
          (0, import_jsx_runtime3.jsx)(Step.Title, {
            id: "tours.contentManager.CreateEntry.title",
            defaultMessage: "Create entry"
          }),
          (0, import_jsx_runtime3.jsx)(Step.Content, {
            id: "tours.contentManager.CreateEntry.content",
            defaultMessage: "Click this button to create an entry"
          }),
          (0, import_jsx_runtime3.jsx)(Step.Actions, {})
        ]
      })
    }
  ])
};
var GuidedTourOverlay = dt(Box)`
  position: fixed;
  inset: 0;
  background-color: rgba(50, 50, 77, 0.2);
  z-index: 10;
`;
var UnstableGuidedTourTooltip = ({ children, content, tourName, step, requiredActions }) => {
  var _a;
  const { data: guidedTourMeta } = useGetGuidedTourMetaQuery();
  const state = unstableUseGuidedTour("UnstableGuidedTourTooltip", (s) => s.state);
  const dispatch = unstableUseGuidedTour("UnstableGuidedTourTooltip", (s) => s.dispatch);
  const Step = React3.useMemo(() => createStepComponents(tourName), [
    tourName
  ]);
  const isCurrentStep = state.tours[tourName].currentStep === step;
  const hasCompletedRequiredActions = (requiredActions == null ? void 0 : requiredActions.every((action) => {
    var _a2;
    return (_a2 = guidedTourMeta == null ? void 0 : guidedTourMeta.data) == null ? void 0 : _a2.completedActions.includes(action);
  })) ?? true;
  const hasFutureFlag = window.strapi.future.isEnabled("unstableGuidedTour");
  const isEnabled = ((_a = guidedTourMeta == null ? void 0 : guidedTourMeta.data) == null ? void 0 : _a.isFirstSuperAdminUser) && !state.tours[tourName].isCompleted && hasFutureFlag;
  const isPopoverOpen = isEnabled && isCurrentStep && hasCompletedRequiredActions;
  React3.useEffect(() => {
    if (!isPopoverOpen) return;
    const originalStyle = window.getComputedStyle(document.body).overflow;
    document.body.style.overflow = "hidden";
    return () => {
      document.body.style.overflow = originalStyle;
    };
  }, [
    isPopoverOpen
  ]);
  return (0, import_jsx_runtime3.jsxs)(import_jsx_runtime3.Fragment, {
    children: [
      isPopoverOpen && (0, import_jsx_runtime3.jsx)(GuidedTourOverlay, {}),
      (0, import_jsx_runtime3.jsxs)(Popover.Root, {
        open: isPopoverOpen,
        children: [
          (0, import_jsx_runtime3.jsx)(Popover.Anchor, {
            children
          }),
          content(Step, {
            state,
            dispatch
          })
        ]
      })
    ]
  });
};
function createTour(tourName, steps) {
  const tour = steps.reduce((acc, step, index) => {
    if (step.name in acc) {
      throw Error(`The tour: ${tourName} with step: ${step.name} has already been registered`);
    }
    acc[step.name] = ({ children }) => (0, import_jsx_runtime3.jsx)(UnstableGuidedTourTooltip, {
      tourName,
      step: index,
      content: step.content,
      requiredActions: step.requiredActions,
      children
    });
    return acc;
  }, {});
  return tour;
}

// node_modules/@strapi/admin/dist/admin/admin/src/render.mjs
var import_client = __toESM(require_client(), 1);

// node_modules/@strapi/admin/dist/admin/admin/src/StrapiApp.mjs
var import_jsx_runtime18 = __toESM(require_jsx_runtime(), 1);
var import_react6 = __toESM(require_react(), 1);
var import_invariant4 = __toESM(require_browser(), 1);
var import_isFunction = __toESM(require_isFunction(), 1);
var import_merge = __toESM(require_merge(), 1);
var import_pick = __toESM(require_pick(), 1);

// node_modules/@strapi/admin/dist/admin/admin/src/core/apis/CustomFields.mjs
var import_invariant = __toESM(require_browser(), 1);
var ALLOWED_TYPES = [
  "biginteger",
  "boolean",
  "date",
  "datetime",
  "decimal",
  "email",
  "enumeration",
  "float",
  "integer",
  "json",
  "password",
  "richtext",
  "string",
  "text",
  "time",
  "uid"
];
var ALLOWED_ROOT_LEVEL_OPTIONS = [
  "min",
  "minLength",
  "max",
  "maxLength",
  "required",
  "regex",
  "enum",
  "unique",
  "private",
  "default"
];
var CustomFields = class {
  constructor() {
    this.register = (customFields) => {
      if (Array.isArray(customFields)) {
        customFields.forEach((customField) => {
          this.register(customField);
        });
      } else {
        const { name, pluginId, type, intlLabel, intlDescription, components, options } = customFields;
        (0, import_invariant.default)(name, "A name must be provided");
        (0, import_invariant.default)(type, "A type must be provided");
        (0, import_invariant.default)(intlLabel, "An intlLabel must be provided");
        (0, import_invariant.default)(intlDescription, "An intlDescription must be provided");
        (0, import_invariant.default)(components, "A components object must be provided");
        (0, import_invariant.default)(components.Input, "An Input component must be provided");
        (0, import_invariant.default)(ALLOWED_TYPES.includes(type), `Custom field type: '${type}' is not a valid Strapi type or it can't be used with a Custom Field`);
        const isValidObjectKey = /^(?![0-9])[a-zA-Z0-9$_-]+$/g;
        (0, import_invariant.default)(isValidObjectKey.test(name), `Custom field name: '${name}' is not a valid object key`);
        const allFormOptions = [
          ...(options == null ? void 0 : options.base) || [],
          ...(options == null ? void 0 : options.advanced) || []
        ];
        if (allFormOptions.length) {
          const optionPathValidations = allFormOptions.reduce(optionsValidationReducer, []);
          optionPathValidations.forEach(({ isValidOptionPath, errorMessage }) => {
            (0, import_invariant.default)(isValidOptionPath, errorMessage);
          });
        }
        const uid = pluginId ? `plugin::${pluginId}.${name}` : `global::${name}`;
        const uidAlreadyUsed = Object.prototype.hasOwnProperty.call(this.customFields, uid);
        (0, import_invariant.default)(!uidAlreadyUsed, `Custom field: '${uid}' has already been registered`);
        this.customFields[uid] = customFields;
      }
    };
    this.getAll = () => {
      return this.customFields;
    };
    this.get = (uid) => {
      return this.customFields[uid];
    };
    this.customFields = {};
  }
};
var optionsValidationReducer = (acc, option) => {
  if ("items" in option) {
    return option.items.reduce(optionsValidationReducer, acc);
  }
  if (!option.name) {
    acc.push({
      isValidOptionPath: false,
      errorMessage: "The 'name' property is required on an options object"
    });
  } else {
    acc.push({
      isValidOptionPath: option.name.startsWith("options") || ALLOWED_ROOT_LEVEL_OPTIONS.includes(option.name),
      errorMessage: `'${option.name}' must be prefixed with 'options.'`
    });
  }
  return acc;
};

// node_modules/@strapi/admin/dist/admin/admin/src/core/apis/Plugin.mjs
var import_react = __toESM(require_react(), 1);
var Plugin = class {
  getInjectedComponents(containerName, blockName) {
    try {
      return this.injectionZones[containerName][blockName] || [];
    } catch (err) {
      console.error("Cannot get injected component", err);
      return [];
    }
  }
  injectComponent(containerName, blockName, component) {
    try {
      this.injectionZones[containerName][blockName].push(component);
    } catch (err) {
      console.error("Cannot inject component", err);
    }
  }
  constructor(pluginConf) {
    this[L] = true;
    this.apis = pluginConf.apis || {};
    this.initializer = pluginConf.initializer || null;
    this.injectionZones = pluginConf.injectionZones || {};
    this.isReady = pluginConf.isReady !== void 0 ? pluginConf.isReady : true;
    this.name = pluginConf.name;
    this.pluginId = pluginConf.id;
  }
};

// node_modules/@strapi/admin/dist/admin/admin/src/core/apis/rbac.mjs
var RBAC = class {
  use(middleware) {
    if (Array.isArray(middleware)) {
      this.middlewares.push(...middleware);
    } else {
      this.middlewares.push(middleware);
    }
  }
  constructor() {
    this.middlewares = [];
    this.run = async (ctx, permissions) => {
      let index = 0;
      const middlewaresToRun = this.middlewares.map((middleware) => middleware(ctx));
      const next = async (permissions2) => {
        if (index < this.middlewares.length) {
          return middlewaresToRun[index++](next)(permissions2);
        }
        return permissions2;
      };
      return next(permissions);
    };
  }
};

// node_modules/@strapi/admin/dist/admin/admin/src/core/apis/router.mjs
var import_jsx_runtime17 = __toESM(require_jsx_runtime(), 1);
var import_react5 = __toESM(require_react(), 1);
var import_invariant2 = __toESM(require_browser(), 1);

// node_modules/@strapi/admin/dist/admin/admin/src/App.mjs
var import_jsx_runtime7 = __toESM(require_jsx_runtime(), 1);
var import_react4 = __toESM(require_react(), 1);

// node_modules/@strapi/admin/dist/admin/admin/src/components/Providers.mjs
var import_jsx_runtime6 = __toESM(require_jsx_runtime(), 1);
var import_react3 = __toESM(require_react(), 1);

// node_modules/@strapi/admin/dist/admin/admin/src/components/LanguageProvider.mjs
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_defaultsDeep = __toESM(require_defaultsDeep(), 1);
var LanguageProvider = ({ children, messages }) => {
  const locale = useTypedSelector((state) => state.admin_app.language.locale);
  const appMessages = (0, import_defaultsDeep.default)(messages[locale], messages.en);
  return (0, import_jsx_runtime4.jsx)(provider_default, {
    locale,
    defaultLocale: "en",
    messages: appMessages,
    textComponent: "span",
    children
  });
};

// node_modules/@strapi/admin/dist/admin/admin/src/components/Theme.mjs
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
var React4 = __toESM(require_react(), 1);
var Theme = ({ children, themes }) => {
  const { currentTheme } = useTypedSelector((state) => state.admin_app.theme);
  const [systemTheme, setSystemTheme] = React4.useState();
  const { locale } = useIntl();
  const dispatch = useDispatch();
  React4.useEffect(() => {
    const themeWatcher = window.matchMedia("(prefers-color-scheme: dark)");
    setSystemTheme(themeWatcher.matches ? "dark" : "light");
    const listener = (event) => {
      setSystemTheme(event.matches ? "dark" : "light");
    };
    themeWatcher.addEventListener("change", listener);
    return () => {
      themeWatcher.removeEventListener("change", listener);
    };
  }, []);
  React4.useEffect(() => {
    dispatch(setAvailableThemes(Object.keys(themes)));
  }, [
    dispatch,
    themes
  ]);
  const computedThemeName = currentTheme === "system" ? systemTheme : currentTheme;
  return (0, import_jsx_runtime5.jsxs)(DesignSystemProvider, {
    locale,
    /**
    * TODO: could we make this neater i.e. by setting up the context to throw
    * if it can't find it, that way the type is always fully defined and we're
    * not checking it all the time...
    */
    theme: themes == null ? void 0 : themes[computedThemeName || "light"],
    children: [
      children,
      (0, import_jsx_runtime5.jsx)(GlobalStyle, {})
    ]
  });
};
var GlobalStyle = ft`
  body {
    background: ${({ theme }) => theme.colors.neutral100};
  }
`;

// node_modules/@strapi/admin/dist/admin/admin/src/components/Providers.mjs
var queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false
    }
  }
});
var Providers = ({ children, strapi, store }) => {
  return (0, import_jsx_runtime6.jsx)(StrapiAppProvider, {
    components: strapi.library.components,
    customFields: strapi.customFields,
    widgets: strapi.widgets,
    fields: strapi.library.fields,
    menu: strapi.router.menu,
    getAdminInjectedComponents: strapi.getAdminInjectedComponents,
    getPlugin: strapi.getPlugin,
    plugins: strapi.plugins,
    rbac: strapi.rbac,
    runHookParallel: strapi.runHookParallel,
    runHookWaterfall: (name, initialValue) => strapi.runHookWaterfall(name, initialValue, store),
    runHookSeries: strapi.runHookSeries,
    settings: strapi.router.settings,
    children: (0, import_jsx_runtime6.jsx)(Provider_default, {
      store,
      children: (0, import_jsx_runtime6.jsx)(QueryClientProvider, {
        client: queryClient,
        children: (0, import_jsx_runtime6.jsx)(AuthProvider, {
          children: (0, import_jsx_runtime6.jsx)(HistoryProvider, {
            children: (0, import_jsx_runtime6.jsx)(LanguageProvider, {
              messages: strapi.configurations.translations,
              children: (0, import_jsx_runtime6.jsx)(Theme, {
                themes: strapi.configurations.themes,
                children: (0, import_jsx_runtime6.jsx)(NotificationsProvider, {
                  children: (0, import_jsx_runtime6.jsx)(TrackingProvider, {
                    children: (0, import_jsx_runtime6.jsx)(GuidedTourProvider, {
                      children: (0, import_jsx_runtime6.jsx)(UnstableGuidedTourContext, {
                        tours,
                        children: (0, import_jsx_runtime6.jsx)(ConfigurationProvider, {
                          defaultAuthLogo: strapi.configurations.authLogo,
                          defaultMenuLogo: strapi.configurations.menuLogo,
                          showReleaseNotification: strapi.configurations.notifications.releases,
                          children
                        })
                      })
                    })
                  })
                })
              })
            })
          })
        })
      })
    })
  });
};

// node_modules/@strapi/admin/dist/admin/admin/src/App.mjs
var App = ({ strapi, store }) => {
  (0, import_react4.useEffect)(() => {
    const language = localStorage.getItem(LANGUAGE_LOCAL_STORAGE_KEY) || "en";
    if (language) {
      document.documentElement.lang = language;
    }
  }, []);
  return (0, import_jsx_runtime7.jsx)(Providers, {
    strapi,
    store,
    children: (0, import_jsx_runtime7.jsx)(import_react4.Suspense, {
      fallback: (0, import_jsx_runtime7.jsx)(Page.Loading, {}),
      children: (0, import_jsx_runtime7.jsx)(Outlet, {})
    })
  });
};

// node_modules/@strapi/admin/dist/admin/admin/src/components/ErrorElement.mjs
var import_jsx_runtime8 = __toESM(require_jsx_runtime(), 1);
var ErrorElement = () => {
  const error = useRouteError();
  const { formatMessage } = useIntl();
  const { copy } = useClipboard();
  if (error instanceof Error) {
    console.error(error);
    const handleClick = async () => {
      await copy(`
\`\`\`
${error.stack}
\`\`\`
      `);
    };
    return (0, import_jsx_runtime8.jsx)(Main, {
      height: "100%",
      children: (0, import_jsx_runtime8.jsx)(Flex, {
        alignItems: "center",
        height: "100%",
        justifyContent: "center",
        children: (0, import_jsx_runtime8.jsxs)(Flex, {
          gap: 7,
          padding: 7,
          direction: "column",
          width: "35%",
          shadow: "tableShadow",
          borderColor: "neutral150",
          background: "neutral0",
          hasRadius: true,
          maxWidth: "512px",
          children: [
            (0, import_jsx_runtime8.jsxs)(Flex, {
              direction: "column",
              gap: 2,
              children: [
                (0, import_jsx_runtime8.jsx)(ForwardRef$3, {
                  width: "32px",
                  height: "32px",
                  fill: "danger600"
                }),
                (0, import_jsx_runtime8.jsx)(Typography, {
                  fontSize: 4,
                  fontWeight: "bold",
                  textAlign: "center",
                  children: formatMessage({
                    id: "app.error",
                    defaultMessage: "Something went wrong"
                  })
                }),
                (0, import_jsx_runtime8.jsx)(Typography, {
                  variant: "omega",
                  textAlign: "center",
                  children: formatMessage({
                    id: "app.error.message",
                    defaultMessage: `It seems like there is a bug in your instance, but we've got you covered. Please notify your technical team so they can investigate the source of the problem and report the issue to us by opening a bug report on {link}.`
                  }, {
                    link: (0, import_jsx_runtime8.jsx)(Link2, {
                      isExternal: true,
                      // hack to get rid of the current endIcon, which should be removable by using `null`.
                      endIcon: true,
                      href: "https://github.com/strapi/strapi/issues/new?assignees=&labels=&projects=&template=BUG_REPORT.md",
                      children: `Strapi's GitHub`
                    })
                  })
                })
              ]
            }),
            (0, import_jsx_runtime8.jsxs)(Flex, {
              gap: 4,
              direction: "column",
              width: "100%",
              children: [
                (0, import_jsx_runtime8.jsx)(StyledAlert, {
                  onClose: () => {
                  },
                  width: "100%",
                  closeLabel: "",
                  variant: "danger",
                  children: (0, import_jsx_runtime8.jsx)(ErrorType, {
                    children: error.message
                  })
                }),
                (0, import_jsx_runtime8.jsx)(Button, {
                  onClick: handleClick,
                  variant: "tertiary",
                  startIcon: (0, import_jsx_runtime8.jsx)(ForwardRef$3R, {}),
                  children: formatMessage({
                    id: "app.error.copy",
                    defaultMessage: "Copy to clipboard"
                  })
                })
              ]
            })
          ]
        })
      })
    });
  }
  throw error;
};
var StyledAlert = dt(Alert)`
  & > div:first-child {
    display: none;
  }

  & > button {
    display: none;
  }
`;
var ErrorType = dt(Typography)`
  word-break: break-all;
  color: ${({ theme }) => theme.colors.danger600};
`;

// node_modules/@strapi/admin/dist/admin/admin/src/pages/NotFoundPage.mjs
var import_jsx_runtime9 = __toESM(require_jsx_runtime(), 1);
var NotFoundPage = () => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime9.jsxs)(Page.Main, {
    labelledBy: "title",
    children: [
      (0, import_jsx_runtime9.jsx)(Layouts.Header, {
        id: "title",
        title: formatMessage({
          id: "content-manager.pageNotFound",
          defaultMessage: "Page not found"
        })
      }),
      (0, import_jsx_runtime9.jsx)(Layouts.Content, {
        children: (0, import_jsx_runtime9.jsx)(EmptyStateLayout, {
          action: (0, import_jsx_runtime9.jsx)(LinkButton, {
            tag: Link,
            variant: "secondary",
            endIcon: (0, import_jsx_runtime9.jsx)(ForwardRef$5d, {}),
            to: "/",
            children: formatMessage({
              id: "app.components.NotFoundPage.back",
              defaultMessage: "Back to homepage"
            })
          }),
          content: formatMessage({
            id: "app.page.not.found",
            defaultMessage: "Oops! We can't seem to find the page you're looging for..."
          }),
          hasRadius: true,
          icon: (0, import_jsx_runtime9.jsx)(ForwardRef$F, {
            width: "16rem"
          }),
          shadow: "tableShadow"
        })
      })
    ]
  });
};

// node_modules/@strapi/admin/dist/admin/admin/src/router.mjs
var import_jsx_runtime16 = __toESM(require_jsx_runtime(), 1);

// node_modules/@strapi/admin/dist/admin/ee/admin/src/pages/SettingsPage/constants.mjs
var getEERoutes2 = () => [
  ...window.strapi.features.isEnabled(window.strapi.features.AUDIT_LOGS) ? [
    {
      path: "audit-logs",
      lazy: async () => {
        const { ProtectedListPage } = await import("./ListPage-O4HIS5TM.js");
        return {
          Component: ProtectedListPage
        };
      }
    }
  ] : [],
  ...window.strapi.features.isEnabled(window.strapi.features.SSO) ? [
    {
      path: "single-sign-on",
      lazy: async () => {
        const { ProtectedSSO } = await import("./SingleSignOnPage-BEG5TQ2M.js");
        return {
          Component: ProtectedSSO
        };
      }
    }
  ] : []
];

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Auth/AuthPage.mjs
var import_jsx_runtime15 = __toESM(require_jsx_runtime(), 1);

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Auth/components/ForgotPassword.mjs
var import_jsx_runtime10 = __toESM(require_jsx_runtime(), 1);
var ForgotPassword = () => {
  const navigate = useNavigate();
  const { formatMessage } = useIntl();
  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();
  const [forgotPassword, { error }] = useForgotPasswordMutation();
  return (0, import_jsx_runtime10.jsx)(UnauthenticatedLayout, {
    children: (0, import_jsx_runtime10.jsxs)(Main, {
      children: [
        (0, import_jsx_runtime10.jsxs)(LayoutContent, {
          children: [
            (0, import_jsx_runtime10.jsxs)(Column, {
              children: [
                (0, import_jsx_runtime10.jsx)(Logo, {}),
                (0, import_jsx_runtime10.jsx)(Box, {
                  paddingTop: 6,
                  paddingBottom: 7,
                  children: (0, import_jsx_runtime10.jsx)(Typography, {
                    tag: "h1",
                    variant: "alpha",
                    children: formatMessage({
                      id: "Auth.form.button.password-recovery",
                      defaultMessage: "Password Recovery"
                    })
                  })
                }),
                error ? (0, import_jsx_runtime10.jsx)(Typography, {
                  id: "global-form-error",
                  role: "alert",
                  tabIndex: -1,
                  textColor: "danger600",
                  children: isBaseQueryError(error) ? formatAPIError(error) : formatMessage({
                    id: "notification.error",
                    defaultMessage: "An error occurred"
                  })
                }) : null
              ]
            }),
            (0, import_jsx_runtime10.jsx)(Form, {
              method: "POST",
              initialValues: {
                email: ""
              },
              onSubmit: async (body) => {
                const res = await forgotPassword(body);
                if (!("error" in res)) {
                  navigate("/auth/forgot-password-success");
                }
              },
              validationSchema: create3().shape({
                email: create2().email(errorsTrads.email).required({
                  id: errorsTrads.required.id,
                  defaultMessage: "This field is required."
                }).nullable()
              }),
              children: (0, import_jsx_runtime10.jsxs)(Flex, {
                direction: "column",
                alignItems: "stretch",
                gap: 6,
                children: [
                  [
                    {
                      label: formatMessage({
                        id: "Auth.form.email.label",
                        defaultMessage: "Email"
                      }),
                      name: "email",
                      placeholder: formatMessage({
                        id: "Auth.form.email.placeholder",
                        defaultMessage: "<EMAIL>"
                      }),
                      required: true,
                      type: "string"
                    }
                  ].map((field) => (0, import_jsx_runtime10.jsx)(MemoizedInputRenderer, {
                    ...field
                  }, field.name)),
                  (0, import_jsx_runtime10.jsx)(Button, {
                    type: "submit",
                    fullWidth: true,
                    children: formatMessage({
                      id: "Auth.form.button.forgot-password",
                      defaultMessage: "Send Email"
                    })
                  })
                ]
              })
            })
          ]
        }),
        (0, import_jsx_runtime10.jsx)(Flex, {
          justifyContent: "center",
          children: (0, import_jsx_runtime10.jsx)(Box, {
            paddingTop: 4,
            children: (0, import_jsx_runtime10.jsx)(Link2, {
              tag: NavLink,
              to: "/auth/login",
              children: formatMessage({
                id: "Auth.link.ready",
                defaultMessage: "Ready to sign in?"
              })
            })
          })
        })
      ]
    })
  });
};

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Auth/components/ForgotPasswordSuccess.mjs
var import_jsx_runtime11 = __toESM(require_jsx_runtime(), 1);
var ForgotPasswordSuccess = () => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime11.jsx)(UnauthenticatedLayout, {
    children: (0, import_jsx_runtime11.jsxs)(Main, {
      children: [
        (0, import_jsx_runtime11.jsx)(LayoutContent, {
          children: (0, import_jsx_runtime11.jsxs)(Column, {
            children: [
              (0, import_jsx_runtime11.jsx)(Logo, {}),
              (0, import_jsx_runtime11.jsx)(Box, {
                paddingTop: 6,
                paddingBottom: 7,
                children: (0, import_jsx_runtime11.jsx)(Typography, {
                  tag: "h1",
                  variant: "alpha",
                  children: formatMessage({
                    id: "app.containers.AuthPage.ForgotPasswordSuccess.title",
                    defaultMessage: "Email sent"
                  })
                })
              }),
              (0, import_jsx_runtime11.jsx)(Typography, {
                children: formatMessage({
                  id: "app.containers.AuthPage.ForgotPasswordSuccess.text.email",
                  defaultMessage: "It can take a few minutes to receive your password recovery link."
                })
              }),
              (0, import_jsx_runtime11.jsx)(Box, {
                paddingTop: 4,
                children: (0, import_jsx_runtime11.jsx)(Typography, {
                  children: formatMessage({
                    id: "app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin",
                    defaultMessage: "If you do not receive this link, please contact your administrator."
                  })
                })
              })
            ]
          })
        }),
        (0, import_jsx_runtime11.jsx)(Flex, {
          justifyContent: "center",
          children: (0, import_jsx_runtime11.jsx)(Box, {
            paddingTop: 4,
            children: (0, import_jsx_runtime11.jsx)(Link2, {
              tag: NavLink,
              to: "/auth/login",
              children: formatMessage({
                id: "Auth.link.signin",
                defaultMessage: "Sign in"
              })
            })
          })
        })
      ]
    })
  });
};

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Auth/components/Oops.mjs
var import_jsx_runtime12 = __toESM(require_jsx_runtime(), 1);
var React5 = __toESM(require_react(), 1);
var Oops = () => {
  const { formatMessage } = useIntl();
  const { search: searchString } = useLocation();
  const query = React5.useMemo(() => new URLSearchParams(searchString), [
    searchString
  ]);
  const message = query.get("info") || formatMessage({
    id: "Auth.components.Oops.text",
    defaultMessage: "Your account has been suspended."
  });
  return (0, import_jsx_runtime12.jsx)(UnauthenticatedLayout, {
    children: (0, import_jsx_runtime12.jsxs)(Main, {
      children: [
        (0, import_jsx_runtime12.jsx)(LayoutContent, {
          children: (0, import_jsx_runtime12.jsxs)(Column, {
            children: [
              (0, import_jsx_runtime12.jsx)(Logo, {}),
              (0, import_jsx_runtime12.jsx)(Box, {
                paddingTop: 6,
                paddingBottom: 7,
                children: (0, import_jsx_runtime12.jsx)(Typography, {
                  tag: "h1",
                  variant: "alpha",
                  children: formatMessage({
                    id: "Auth.components.Oops.title",
                    defaultMessage: "Oops..."
                  })
                })
              }),
              (0, import_jsx_runtime12.jsx)(Typography, {
                children: message
              }),
              (0, import_jsx_runtime12.jsx)(Box, {
                paddingTop: 4,
                children: (0, import_jsx_runtime12.jsx)(Typography, {
                  children: formatMessage({
                    id: "Auth.components.Oops.text.admin",
                    defaultMessage: "If this is a mistake, please contact your administrator."
                  })
                })
              })
            ]
          })
        }),
        (0, import_jsx_runtime12.jsx)(Flex, {
          justifyContent: "center",
          children: (0, import_jsx_runtime12.jsx)(Box, {
            paddingTop: 4,
            children: (0, import_jsx_runtime12.jsx)(Link2, {
              tag: NavLink,
              to: "/auth/login",
              children: formatMessage({
                id: "Auth.link.signin",
                defaultMessage: "Sign in"
              })
            })
          })
        })
      ]
    })
  });
};

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Auth/components/Register.mjs
var import_jsx_runtime13 = __toESM(require_jsx_runtime(), 1);
var React6 = __toESM(require_react(), 1);
var import_omit = __toESM(require_omit(), 1);
var REGISTER_USER_SCHEMA = create3().shape({
  firstname: create2().trim().required(errorsTrads.required).nullable(),
  lastname: create2().nullable(),
  password: create2().min(8, {
    id: errorsTrads.minLength.id,
    defaultMessage: "Password must be at least 8 characters",
    values: {
      min: 8
    }
  }).test("max-bytes", {
    id: "components.Input.error.contain.maxBytes",
    defaultMessage: "Password must be less than 73 bytes"
  }, function(value) {
    if (!value || typeof value !== "string") return true;
    const byteSize = getByteSize(value);
    return byteSize <= 72;
  }).matches(/[a-z]/, {
    message: {
      id: "components.Input.error.contain.lowercase",
      defaultMessage: "Password must contain at least 1 lowercase letter"
    }
  }).matches(/[A-Z]/, {
    message: {
      id: "components.Input.error.contain.uppercase",
      defaultMessage: "Password must contain at least 1 uppercase letter"
    }
  }).matches(/\d/, {
    message: {
      id: "components.Input.error.contain.number",
      defaultMessage: "Password must contain at least 1 number"
    }
  }).required({
    id: errorsTrads.required.id,
    defaultMessage: "Password is required"
  }).nullable(),
  confirmPassword: create2().required({
    id: errorsTrads.required.id,
    defaultMessage: "Confirm password is required"
  }).oneOf([
    create("password"),
    null
  ], {
    id: "components.Input.error.password.noMatch",
    defaultMessage: "Passwords must match"
  }).nullable(),
  registrationToken: create2().required({
    id: errorsTrads.required.id,
    defaultMessage: "Registration token is required"
  })
});
var REGISTER_ADMIN_SCHEMA = create3().shape({
  firstname: create2().trim().required({
    id: errorsTrads.required.id,
    defaultMessage: "Firstname is required"
  }).nullable(),
  lastname: create2().nullable(),
  password: create2().min(8, {
    id: errorsTrads.minLength.id,
    defaultMessage: "Password must be at least 8 characters",
    values: {
      min: 8
    }
  }).test("max-bytes", {
    id: "components.Input.error.contain.maxBytes",
    defaultMessage: "Password must be less than 73 bytes"
  }, function(value) {
    if (!value) return true;
    return new TextEncoder().encode(value).length <= 72;
  }).matches(/[a-z]/, {
    message: {
      id: "components.Input.error.contain.lowercase",
      defaultMessage: "Password must contain at least 1 lowercase letter"
    }
  }).matches(/[A-Z]/, {
    message: {
      id: "components.Input.error.contain.uppercase",
      defaultMessage: "Password must contain at least 1 uppercase letter"
    }
  }).matches(/\d/, {
    message: {
      id: "components.Input.error.contain.number",
      defaultMessage: "Password must contain at least 1 number"
    }
  }).required({
    id: errorsTrads.required.id,
    defaultMessage: "Password is required"
  }).nullable(),
  confirmPassword: create2().required({
    id: errorsTrads.required.id,
    defaultMessage: "Confirm password is required"
  }).nullable().oneOf([
    create("password"),
    null
  ], {
    id: "components.Input.error.password.noMatch",
    defaultMessage: "Passwords must match"
  }),
  email: create2().email({
    id: errorsTrads.email.id,
    defaultMessage: "Not a valid email"
  }).strict().lowercase({
    id: errorsTrads.lowercase.id,
    defaultMessage: "Email must be lowercase"
  }).required({
    id: errorsTrads.required.id,
    defaultMessage: "Email is required"
  }).nullable()
});
var Register = ({ hasAdmin }) => {
  const { toggleNotification } = useNotification();
  const navigate = useNavigate();
  const [submitCount, setSubmitCount] = React6.useState(0);
  const [apiError, setApiError] = React6.useState();
  const { trackUsage } = useTracking();
  const { formatMessage } = useIntl();
  const setSkipped = useGuidedTour("Register", (state) => state.setSkipped);
  const { search: searchString } = useLocation();
  const query = React6.useMemo(() => new URLSearchParams(searchString), [
    searchString
  ]);
  const match = useMatch("/auth/:authType");
  const { _unstableFormatAPIError: formatAPIError, _unstableFormatValidationErrors: formatValidationErrors } = useAPIErrorHandler();
  const { setNpsSurveySettings } = useNpsSurveySettings();
  const registrationToken = query.get("registrationToken");
  const { data: userInfo, error } = useGetRegistrationInfoQuery(registrationToken, {
    skip: !registrationToken
  });
  React6.useEffect(() => {
    if (error) {
      const message = isBaseQueryError(error) ? formatAPIError(error) : error.message ?? "";
      toggleNotification({
        type: "danger",
        message
      });
      navigate(`/auth/oops?info=${encodeURIComponent(message)}`);
    }
  }, [
    error,
    formatAPIError,
    navigate,
    toggleNotification
  ]);
  const [registerAdmin] = useRegisterAdminMutation();
  const [registerUser] = useRegisterUserMutation();
  const dispatch = useTypedDispatch();
  const handleRegisterAdmin = async ({ news, ...body }, setFormErrors) => {
    const res = await registerAdmin(body);
    if ("data" in res) {
      dispatch(login({
        token: res.data.token
      }));
      const { roles } = res.data.user;
      if (roles) {
        const isUserSuperAdmin = roles.find(({ code }) => code === "strapi-super-admin");
        if (isUserSuperAdmin) {
          localStorage.setItem("GUIDED_TOUR_SKIPPED", JSON.stringify(false));
          setSkipped(false);
          trackUsage("didLaunchGuidedtour");
        }
      }
      if (news) {
        setNpsSurveySettings((s) => ({
          ...s,
          enabled: true
        }));
        navigate({
          pathname: "/usecase",
          search: `?hasAdmin=${true}`
        });
      } else {
        navigate("/");
      }
    } else {
      if (isBaseQueryError(res.error)) {
        trackUsage("didNotCreateFirstAdmin");
        if (res.error.name === "ValidationError") {
          setFormErrors(formatValidationErrors(res.error));
          return;
        }
        setApiError(formatAPIError(res.error));
      }
    }
  };
  const handleRegisterUser = async ({ news, ...body }, setFormErrors) => {
    const res = await registerUser(body);
    if ("data" in res) {
      dispatch(login({
        token: res.data.token
      }));
      if (news) {
        setNpsSurveySettings((s) => ({
          ...s,
          enabled: true
        }));
        navigate({
          pathname: "/usecase",
          search: `?hasAdmin=${hasAdmin}`
        });
      } else {
        navigate("/");
      }
    } else {
      if (isBaseQueryError(res.error)) {
        trackUsage("didNotCreateFirstAdmin");
        if (res.error.name === "ValidationError") {
          setFormErrors(formatValidationErrors(res.error));
          return;
        }
        setApiError(formatAPIError(res.error));
      }
    }
  };
  if (!match || match.params.authType !== "register" && match.params.authType !== "register-admin") {
    return (0, import_jsx_runtime13.jsx)(Navigate, {
      to: "/"
    });
  }
  const isAdminRegistration = match.params.authType === "register-admin";
  const schema = isAdminRegistration ? REGISTER_ADMIN_SCHEMA : REGISTER_USER_SCHEMA;
  return (0, import_jsx_runtime13.jsx)(UnauthenticatedLayout, {
    children: (0, import_jsx_runtime13.jsxs)(LayoutContent, {
      children: [
        (0, import_jsx_runtime13.jsxs)(Flex, {
          direction: "column",
          alignItems: "center",
          gap: 3,
          children: [
            (0, import_jsx_runtime13.jsx)(Logo, {}),
            (0, import_jsx_runtime13.jsx)(Typography, {
              tag: "h1",
              variant: "alpha",
              textAlign: "center",
              children: formatMessage({
                id: "Auth.form.welcome.title",
                defaultMessage: "Welcome to Strapi!"
              })
            }),
            (0, import_jsx_runtime13.jsx)(Typography, {
              variant: "epsilon",
              textColor: "neutral600",
              textAlign: "center",
              children: formatMessage({
                id: "Auth.form.register.subtitle",
                defaultMessage: "Credentials are only used to authenticate in Strapi. All saved data will be stored in your database."
              })
            }),
            apiError ? (0, import_jsx_runtime13.jsx)(Typography, {
              id: "global-form-error",
              role: "alert",
              tabIndex: -1,
              textColor: "danger600",
              children: apiError
            }) : null
          ]
        }),
        (0, import_jsx_runtime13.jsx)(Form, {
          method: "POST",
          initialValues: {
            firstname: (userInfo == null ? void 0 : userInfo.firstname) || "",
            lastname: (userInfo == null ? void 0 : userInfo.lastname) || "",
            email: (userInfo == null ? void 0 : userInfo.email) || "",
            password: "",
            confirmPassword: "",
            registrationToken: registrationToken || void 0,
            news: false
          },
          onSubmit: async (data, helpers) => {
            const normalizedData = normalizeData(data);
            try {
              await schema.validate(normalizedData, {
                abortEarly: false
              });
              if (submitCount > 0 && isAdminRegistration) {
                trackUsage("didSubmitWithErrorsFirstAdmin", {
                  count: submitCount.toString()
                });
              }
              if (normalizedData.registrationToken) {
                handleRegisterUser({
                  userInfo: (0, import_omit.default)(normalizedData, [
                    "registrationToken",
                    "confirmPassword",
                    "email",
                    "news"
                  ]),
                  registrationToken: normalizedData.registrationToken,
                  news: normalizedData.news
                }, helpers.setErrors);
              } else {
                await handleRegisterAdmin((0, import_omit.default)(normalizedData, [
                  "registrationToken",
                  "confirmPassword"
                ]), helpers.setErrors);
              }
            } catch (err) {
              if (err instanceof ValidationError) {
                helpers.setErrors(err.inner.reduce((acc, { message, path }) => {
                  if (path && typeof message === "object") {
                    acc[path] = formatMessage(message);
                  }
                  return acc;
                }, {}));
              }
              setSubmitCount(submitCount + 1);
            }
          },
          children: (0, import_jsx_runtime13.jsxs)(Flex, {
            direction: "column",
            alignItems: "stretch",
            gap: 6,
            marginTop: 7,
            children: [
              (0, import_jsx_runtime13.jsx)(Grid.Root, {
                gap: 4,
                children: [
                  {
                    label: formatMessage({
                      id: "Auth.form.firstname.label",
                      defaultMessage: "Firstname"
                    }),
                    name: "firstname",
                    required: true,
                    size: 6,
                    type: "string"
                  },
                  {
                    label: formatMessage({
                      id: "Auth.form.lastname.label",
                      defaultMessage: "Lastname"
                    }),
                    name: "lastname",
                    size: 6,
                    type: "string"
                  },
                  {
                    disabled: !isAdminRegistration,
                    label: formatMessage({
                      id: "Auth.form.email.label",
                      defaultMessage: "Email"
                    }),
                    name: "email",
                    required: true,
                    size: 12,
                    type: "email"
                  },
                  {
                    hint: formatMessage({
                      id: "Auth.form.password.hint",
                      defaultMessage: "Must be at least 8 characters, 1 uppercase, 1 lowercase & 1 number"
                    }),
                    label: formatMessage({
                      id: "global.password",
                      defaultMessage: "Password"
                    }),
                    name: "password",
                    required: true,
                    size: 12,
                    type: "password"
                  },
                  {
                    label: formatMessage({
                      id: "Auth.form.confirmPassword.label",
                      defaultMessage: "Confirm Password"
                    }),
                    name: "confirmPassword",
                    required: true,
                    size: 12,
                    type: "password"
                  },
                  {
                    label: formatMessage({
                      id: "Auth.form.register.news.label",
                      defaultMessage: "Keep me updated about new features & upcoming improvements (by doing this you accept the {terms} and the {policy})."
                    }, {
                      terms: (0, import_jsx_runtime13.jsx)(A, {
                        target: "_blank",
                        href: "https://strapi.io/terms",
                        rel: "noreferrer",
                        children: formatMessage({
                          id: "Auth.privacy-policy-agreement.terms",
                          defaultMessage: "terms"
                        })
                      }),
                      policy: (0, import_jsx_runtime13.jsx)(A, {
                        target: "_blank",
                        href: "https://strapi.io/privacy",
                        rel: "noreferrer",
                        children: formatMessage({
                          id: "Auth.privacy-policy-agreement.policy",
                          defaultMessage: "policy"
                        })
                      })
                    }),
                    name: "news",
                    size: 12,
                    type: "checkbox"
                  }
                ].map(({ size, ...field }) => (0, import_jsx_runtime13.jsx)(Grid.Item, {
                  col: size,
                  direction: "column",
                  alignItems: "stretch",
                  children: (0, import_jsx_runtime13.jsx)(MemoizedInputRenderer, {
                    ...field
                  })
                }, field.name))
              }),
              (0, import_jsx_runtime13.jsx)(Button, {
                fullWidth: true,
                size: "L",
                type: "submit",
                children: formatMessage({
                  id: "Auth.form.button.register",
                  defaultMessage: "Let's start"
                })
              })
            ]
          })
        }),
        (match == null ? void 0 : match.params.authType) === "register" && (0, import_jsx_runtime13.jsx)(Box, {
          paddingTop: 4,
          children: (0, import_jsx_runtime13.jsx)(Flex, {
            justifyContent: "center",
            children: (0, import_jsx_runtime13.jsx)(Link2, {
              tag: NavLink,
              to: "/auth/login",
              children: formatMessage({
                id: "Auth.link.signin.account",
                defaultMessage: "Already have an account?"
              })
            })
          })
        })
      ]
    })
  });
};
function normalizeData(data) {
  return Object.entries(data).reduce((acc, [key, value]) => {
    if (![
      "password",
      "confirmPassword"
    ].includes(key) && typeof value === "string") {
      acc[key] = value.trim();
      if (key === "lastname") {
        acc[key] = value || void 0;
      }
    } else {
      acc[key] = value;
    }
    return acc;
  }, {});
}
var A = dt.a`
  color: ${({ theme }) => theme.colors.primary600};
`;

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Auth/components/ResetPassword.mjs
var import_jsx_runtime14 = __toESM(require_jsx_runtime(), 1);
var React7 = __toESM(require_react(), 1);
var RESET_PASSWORD_SCHEMA = create3().shape({
  password: create2().min(8, {
    id: errorsTrads.minLength.id,
    defaultMessage: "Password must be at least 8 characters",
    values: {
      min: 8
    }
  }).test("required-byte-size", {
    id: "components.Input.error.contain.maxBytes",
    defaultMessage: "Password must be less than 73 bytes"
  }, function(value) {
    if (!value || typeof value !== "string") return true;
    const byteSize = getByteSize(value);
    return byteSize <= 72;
  }).matches(/[a-z]/, {
    message: {
      id: "components.Input.error.contain.lowercase",
      defaultMessage: "Password must contain at least 1 lowercase letter"
    }
  }).matches(/[A-Z]/, {
    message: {
      id: "components.Input.error.contain.uppercase",
      defaultMessage: "Password must contain at least 1 uppercase letter"
    }
  }).matches(/\d/, {
    message: {
      id: "components.Input.error.contain.number",
      defaultMessage: "Password must contain at least 1 number"
    }
  }).required({
    id: errorsTrads.required.id,
    defaultMessage: "Password is required"
  }).nullable(),
  confirmPassword: create2().required({
    id: errorsTrads.required.id,
    defaultMessage: "Confirm password is required"
  }).oneOf([
    create("password"),
    null
  ], {
    id: "components.Input.error.password.noMatch",
    defaultMessage: "Passwords must match"
  }).nullable()
});
var ResetPassword = () => {
  const { formatMessage } = useIntl();
  const dispatch = useTypedDispatch();
  const navigate = useNavigate();
  const { search: searchString } = useLocation();
  const query = React7.useMemo(() => new URLSearchParams(searchString), [
    searchString
  ]);
  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();
  const [resetPassword, { error }] = useResetPasswordMutation();
  const handleSubmit = async (body) => {
    const res = await resetPassword(body);
    if ("data" in res) {
      dispatch(login({
        token: res.data.token
      }));
      navigate("/");
    }
  };
  if (!query.get("code")) {
    return (0, import_jsx_runtime14.jsx)(Navigate, {
      to: "/auth/login"
    });
  }
  return (0, import_jsx_runtime14.jsx)(UnauthenticatedLayout, {
    children: (0, import_jsx_runtime14.jsxs)(Main, {
      children: [
        (0, import_jsx_runtime14.jsxs)(LayoutContent, {
          children: [
            (0, import_jsx_runtime14.jsxs)(Column, {
              children: [
                (0, import_jsx_runtime14.jsx)(Logo, {}),
                (0, import_jsx_runtime14.jsx)(Box, {
                  paddingTop: 6,
                  paddingBottom: 7,
                  children: (0, import_jsx_runtime14.jsx)(Typography, {
                    tag: "h1",
                    variant: "alpha",
                    children: formatMessage({
                      id: "global.reset-password",
                      defaultMessage: "Reset password"
                    })
                  })
                }),
                error ? (0, import_jsx_runtime14.jsx)(Typography, {
                  id: "global-form-error",
                  role: "alert",
                  tabIndex: -1,
                  textColor: "danger600",
                  children: isBaseQueryError(error) ? formatAPIError(error) : formatMessage({
                    id: "notification.error",
                    defaultMessage: "An error occurred"
                  })
                }) : null
              ]
            }),
            (0, import_jsx_runtime14.jsx)(Form, {
              method: "POST",
              initialValues: {
                password: "",
                confirmPassword: ""
              },
              onSubmit: (values) => {
                handleSubmit({
                  password: values.password,
                  resetPasswordToken: query.get("code")
                });
              },
              validationSchema: RESET_PASSWORD_SCHEMA,
              children: (0, import_jsx_runtime14.jsxs)(Flex, {
                direction: "column",
                alignItems: "stretch",
                gap: 6,
                children: [
                  [
                    {
                      hint: formatMessage({
                        id: "Auth.form.password.hint",
                        defaultMessage: "Password must contain at least 8 characters, 1 uppercase, 1 lowercase and 1 number"
                      }),
                      label: formatMessage({
                        id: "global.password",
                        defaultMessage: "Password"
                      }),
                      name: "password",
                      required: true,
                      type: "password"
                    },
                    {
                      label: formatMessage({
                        id: "Auth.form.confirmPassword.label",
                        defaultMessage: "Confirm Password"
                      }),
                      name: "confirmPassword",
                      required: true,
                      type: "password"
                    }
                  ].map((field) => (0, import_jsx_runtime14.jsx)(MemoizedInputRenderer, {
                    ...field
                  }, field.name)),
                  (0, import_jsx_runtime14.jsx)(Button, {
                    fullWidth: true,
                    type: "submit",
                    children: formatMessage({
                      id: "global.change-password",
                      defaultMessage: "Change password"
                    })
                  })
                ]
              })
            })
          ]
        }),
        (0, import_jsx_runtime14.jsx)(Flex, {
          justifyContent: "center",
          children: (0, import_jsx_runtime14.jsx)(Box, {
            paddingTop: 4,
            children: (0, import_jsx_runtime14.jsx)(Link2, {
              tag: NavLink,
              to: "/auth/login",
              children: formatMessage({
                id: "Auth.link.ready",
                defaultMessage: "Ready to sign in?"
              })
            })
          })
        })
      ]
    })
  });
};

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Auth/constants.mjs
var FORMS = {
  "forgot-password": ForgotPassword,
  "forgot-password-success": ForgotPasswordSuccess,
  // the `Component` attribute is set after all forms and CE/EE components are loaded, but since we
  // are here outside of a React component we can not use the hook directly
  login: () => null,
  oops: Oops,
  register: Register,
  "register-admin": Register,
  "reset-password": ResetPassword,
  providers: () => null
};

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Auth/AuthPage.mjs
var AuthPage = () => {
  const { search } = useLocation();
  const match = useMatch("/auth/:authType");
  const authType = match == null ? void 0 : match.params.authType;
  const { data } = useInitQuery();
  const { hasAdmin } = data ?? {};
  const Login$1 = useEnterprise(Login, async () => (await import("./Login-SS5RKGOW.js")).LoginEE);
  const forms = useEnterprise(FORMS, async () => (await import("./constants-UAS5NWZ3.js")).FORMS, {
    combine(ceForms, eeForms) {
      return {
        ...ceForms,
        ...eeForms
      };
    },
    defaultValue: FORMS
  });
  const { token } = useAuth("AuthPage", (auth) => auth);
  if (!authType || !forms) {
    return (0, import_jsx_runtime15.jsx)(Navigate, {
      to: "/"
    });
  }
  const Component = forms[authType];
  if (!Component) {
    return (0, import_jsx_runtime15.jsx)(Navigate, {
      to: "/"
    });
  }
  if (authType !== "register-admin" && authType !== "register" && token) {
    return (0, import_jsx_runtime15.jsx)(Navigate, {
      to: "/"
    });
  }
  if (hasAdmin && authType === "register-admin" && token) {
    return (0, import_jsx_runtime15.jsx)(Navigate, {
      to: "/"
    });
  }
  if (!hasAdmin && authType !== "register-admin") {
    return (0, import_jsx_runtime15.jsx)(Navigate, {
      to: {
        pathname: "/auth/register-admin",
        // Forward the `?redirectTo` from /auth/login
        // /abc => /auth/login?redirectTo=%2Fabc => /auth/register-admin?redirectTo=%2Fabc
        search
      }
    });
  }
  if (Login$1 && authType === "login") {
    return (0, import_jsx_runtime15.jsx)(Login$1, {});
  } else if (authType === "login" && !Login$1) {
    return null;
  }
  return (0, import_jsx_runtime15.jsx)(Component, {
    hasAdmin
  });
};

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/constants.mjs
var ROUTES_CE = [
  {
    lazy: async () => {
      const { ProtectedListPage } = await import("./ListPage-KFJXM22B.js");
      return {
        Component: ProtectedListPage
      };
    },
    path: "roles"
  },
  {
    lazy: async () => {
      const { ProtectedCreatePage } = await import("./CreatePage-S2I2JMXB.js");
      return {
        Component: ProtectedCreatePage
      };
    },
    path: "roles/duplicate/:id"
  },
  {
    lazy: async () => {
      const { ProtectedCreatePage } = await import("./CreatePage-S2I2JMXB.js");
      return {
        Component: ProtectedCreatePage
      };
    },
    path: "roles/new"
  },
  {
    lazy: async () => {
      const { ProtectedEditPage } = await import("./EditPage-LDJ45J4E.js");
      return {
        Component: ProtectedEditPage
      };
    },
    path: "roles/:id"
  },
  {
    lazy: async () => {
      const { ProtectedListPage } = await import("./ListPage-SK5KI4MN.js");
      return {
        Component: ProtectedListPage
      };
    },
    path: "users"
  },
  {
    lazy: async () => {
      const { ProtectedEditPage } = await import("./EditPage-56ZECZOE.js");
      return {
        Component: ProtectedEditPage
      };
    },
    path: "users/:id"
  },
  {
    lazy: async () => {
      const { ProtectedCreatePage } = await import("./CreatePage-ET5RXGZQ.js");
      return {
        Component: ProtectedCreatePage
      };
    },
    path: "webhooks/create"
  },
  {
    lazy: async () => {
      const editWebhook = await import("./EditPage-RAXUL2L5.js");
      return {
        Component: editWebhook.ProtectedEditPage
      };
    },
    path: "webhooks/:id"
  },
  {
    lazy: async () => {
      const { ProtectedListPage } = await import("./ListPage-UNSQAUBE.js");
      return {
        Component: ProtectedListPage
      };
    },
    path: "webhooks"
  },
  {
    lazy: async () => {
      const { ProtectedListView } = await import("./ListView-RT5PCQBF.js");
      return {
        Component: ProtectedListView
      };
    },
    path: "api-tokens"
  },
  {
    lazy: async () => {
      const { ProtectedCreateView } = await import("./CreateView-I34FKHXT.js");
      return {
        Component: ProtectedCreateView
      };
    },
    path: "api-tokens/create"
  },
  {
    lazy: async () => {
      const { ProtectedEditView } = await import("./EditViewPage-BPCFM6D7.js");
      return {
        Component: ProtectedEditView
      };
    },
    path: "api-tokens/:id"
  },
  {
    lazy: async () => {
      const { ProtectedCreateView } = await import("./CreateView-ZHYKDJ2A.js");
      return {
        Component: ProtectedCreateView
      };
    },
    path: "transfer-tokens/create"
  },
  {
    lazy: async () => {
      const { ProtectedListView } = await import("./ListView-47BINPYK.js");
      return {
        Component: ProtectedListView
      };
    },
    path: "transfer-tokens"
  },
  {
    lazy: async () => {
      const { ProtectedEditView } = await import("./EditView-NU75CN2L.js");
      return {
        Component: ProtectedEditView
      };
    },
    path: "transfer-tokens/:id"
  },
  {
    lazy: async () => {
      const { ProtectedInstalledPlugins } = await import("./InstalledPlugins-XDXG2BZ2.js");
      return {
        Component: ProtectedInstalledPlugins
      };
    },
    path: "list-plugins"
  },
  {
    lazy: async () => {
      const { PurchaseAuditLogs } = await import("./PurchaseAuditLogs-TGJB2YME.js");
      return {
        Component: PurchaseAuditLogs
      };
    },
    path: "purchase-audit-logs"
  },
  {
    lazy: async () => {
      const { PurchaseSingleSignOn } = await import("./PurchaseSingleSignOn-2JJSOXC4.js");
      return {
        Component: PurchaseSingleSignOn
      };
    },
    path: "purchase-single-sign-on"
  },
  {
    lazy: async () => {
      const { PurchaseContentHistory } = await import("./PurchaseContentHistory-L4QAAO3D.js");
      return {
        Component: PurchaseContentHistory
      };
    },
    path: "purchase-content-history"
  }
];

// node_modules/@strapi/admin/dist/admin/admin/src/router.mjs
var getImmutableRoutes = () => [
  {
    path: "usecase",
    lazy: async () => {
      const { PrivateUseCasePage } = await import("./UseCasePage-I5KERY46.js");
      return {
        Component: PrivateUseCasePage
      };
    }
  },
  // this needs to go before auth/:authType because otherwise it won't match the route
  ...getEERoutes(),
  {
    path: "auth/:authType",
    element: (0, import_jsx_runtime16.jsx)(AuthPage, {})
  }
];
var getInitialRoutes = () => [
  {
    index: true,
    lazy: async () => {
      const { HomePage } = await import("./HomePage-ATLWXRYQ.js");
      return {
        Component: HomePage
      };
    }
  },
  {
    path: "me",
    lazy: async () => {
      const { ProfilePage } = await import("./ProfilePage-RMLGOXS3.js");
      return {
        Component: ProfilePage
      };
    }
  },
  {
    path: "marketplace",
    lazy: async () => {
      const { ProtectedMarketplacePage } = await import("./MarketplacePage-KNOBBN3G.js");
      return {
        Component: ProtectedMarketplacePage
      };
    }
  },
  {
    path: "settings/*",
    lazy: async () => {
      const { Layout } = await import("./Layout-GBV7XGUF.js");
      return {
        Component: Layout
      };
    },
    children: [
      {
        path: "application-infos",
        lazy: async () => {
          const { ApplicationInfoPage } = await import("./ApplicationInfoPage-OLMRVHWE.js");
          return {
            Component: ApplicationInfoPage
          };
        }
      },
      // ...Object.values(this.settings).flatMap(({ links }) =>
      //   links.map(({ to, Component }) => ({
      //     path: `${to}/*`,
      //     element: (
      //       <React.Suspense fallback={<Page.Loading />}>
      //         <Component />
      //       </React.Suspense>
      //     ),
      //   }))
      // ),
      ...[
        ...getEERoutes2(),
        ...ROUTES_CE
      ].filter((route, index, refArray) => refArray.findIndex((obj) => obj.path === route.path) === index)
    ]
  }
];

// node_modules/@strapi/admin/dist/admin/admin/src/core/apis/router.mjs
var Router = class {
  get routes() {
    return this._routes;
  }
  get menu() {
    return this._menu;
  }
  get settings() {
    return this._settings;
  }
  /**
  * @internal This method is used internally by Strapi to create the router.
  * It should not be used by plugins, doing so will likely break the application.
  */
  createRouter(strapi, { memory, ...opts } = {}) {
    const routes = [
      {
        path: "/*",
        errorElement: (0, import_jsx_runtime17.jsx)(Provider_default, {
          store: strapi.store,
          children: (0, import_jsx_runtime17.jsx)(LanguageProvider, {
            messages: strapi.configurations.translations,
            children: (0, import_jsx_runtime17.jsx)(Theme, {
              themes: strapi.configurations.themes,
              children: (0, import_jsx_runtime17.jsx)(ErrorElement, {})
            })
          })
        }),
        element: (0, import_jsx_runtime17.jsx)(App, {
          strapi,
          store: strapi.store
        }),
        children: [
          ...getImmutableRoutes(),
          {
            path: "/*",
            lazy: async () => {
              const { PrivateAdminLayout } = await import("./AuthenticatedLayout-JHNUBFV6.js");
              return {
                Component: PrivateAdminLayout
              };
            },
            children: [
              ...this.routes,
              {
                path: "*",
                element: (0, import_jsx_runtime17.jsx)(NotFoundPage, {})
              }
            ]
          }
        ]
      }
    ];
    if (memory) {
      this.router = createMemoryRouter(routes, opts);
    } else {
      this.router = createBrowserRouter(routes, opts);
    }
    return this.router;
  }
  addSettingsLink(section, link) {
    var _a, _b, _c, _d;
    if (typeof section === "object" && "links" in section) {
      (0, import_invariant2.default)(section.id, "section.id should be defined");
      (0, import_invariant2.default)(((_a = section.intlLabel) == null ? void 0 : _a.id) && ((_b = section.intlLabel) == null ? void 0 : _b.defaultMessage), "section.intlLabel should be defined");
      (0, import_invariant2.default)(this.settings[section.id] === void 0, "A similar section already exists");
      (0, import_invariant2.default)(Array.isArray(section.links), "TypeError expected links to be an array");
      this.settings[section.id] = {
        ...section,
        links: []
      };
      section.links.forEach((link2) => {
        this.createSettingsLink(section.id, link2);
      });
    } else if (typeof section === "object" && link) {
      (0, import_invariant2.default)(section.id, "section.id should be defined");
      (0, import_invariant2.default)(((_c = section.intlLabel) == null ? void 0 : _c.id) && ((_d = section.intlLabel) == null ? void 0 : _d.defaultMessage), "section.intlLabel should be defined");
      (0, import_invariant2.default)(this.settings[section.id] === void 0, "A similar section already exists");
      this.settings[section.id] = {
        ...section,
        links: []
      };
      if (Array.isArray(link)) {
        link.forEach((l) => this.createSettingsLink(section.id, l));
      } else {
        this.createSettingsLink(section.id, link);
      }
    } else if (typeof section === "string" && link) {
      if (Array.isArray(link)) {
        link.forEach((l) => this.createSettingsLink(section, l));
      } else {
        this.createSettingsLink(section, link);
      }
    } else {
      throw new Error("Invalid arguments provided to addSettingsLink, at minimum a sectionId and link are required.");
    }
  }
  /**
  * @alpha
  * @description Adds a route or an array of routes to the router.
  * Otherwise, pass a function that receives the current routes and
  * returns the new routes in a reducer like fashion.
  */
  addRoute(route) {
    if (Array.isArray(route)) {
      this._routes = [
        ...this._routes,
        ...route
      ];
    } else if (typeof route === "object" && route !== null) {
      this._routes.push(route);
    } else if (typeof route === "function") {
      this._routes = route(this._routes);
    } else {
      throw new Error(`Expected the \`route\` passed to \`addRoute\` to be an array or a function, but received ${getPrintableType(route)}`);
    }
  }
  constructor(initialRoutes) {
    this._routes = [];
    this.router = null;
    this._menu = [];
    this._settings = {
      global: {
        id: "global",
        intlLabel: {
          id: "Settings.global",
          defaultMessage: "Global Settings"
        },
        links: []
      }
    };
    this.addMenuLink = (link) => {
      var _a, _b;
      (0, import_invariant2.default)(link.to, `[${link.intlLabel.defaultMessage}]: link.to should be defined`);
      (0, import_invariant2.default)(typeof link.to === "string", `[${link.intlLabel.defaultMessage}]: Expected link.to to be a string instead received ${typeof link.to}`);
      (0, import_invariant2.default)(((_a = link.intlLabel) == null ? void 0 : _a.id) && ((_b = link.intlLabel) == null ? void 0 : _b.defaultMessage), `[${link.intlLabel.defaultMessage}]: link.intlLabel.id & link.intlLabel.defaultMessage should be defined`);
      (0, import_invariant2.default)(!link.Component || link.Component && typeof link.Component === "function", `[${link.intlLabel.defaultMessage}]: link.Component must be a function returning a Promise that returns a default component. Please use: \`Component: () => import(path)\` instead.`);
      if (!link.Component || link.Component && typeof link.Component === "function" && // @ts-expect-error – shh
      link.Component[Symbol.toStringTag] === "AsyncFunction") {
        console.warn(`
      [${link.intlLabel.defaultMessage}]: [deprecated] addMenuLink() was called with an async Component from the plugin "${link.intlLabel.defaultMessage}". This will be removed in the future. Please use: \`Component: () => import(path)\` ensuring you return a default export instead.
      `.trim());
      }
      if (link.to.startsWith("/")) {
        console.warn(`[${link.intlLabel.defaultMessage}]: the \`to\` property of your menu link is an absolute path, it should be relative to the root of the application. This has been corrected for you but will be removed in a future version of Strapi.`);
        link.to = link.to.slice(1);
      }
      const { Component, ...restLink } = link;
      if (Component) {
        this._routes.push({
          path: `${link.to}/*`,
          lazy: async () => {
            const mod = await Component();
            if ("default" in mod) {
              return {
                Component: mod.default
              };
            } else {
              return {
                Component: mod
              };
            }
          }
        });
      }
      this.menu.push(restLink);
    };
    this.createSettingsLink = (sectionId, link) => {
      var _a, _b;
      (0, import_invariant2.default)(this._settings[sectionId], "The section does not exist");
      (0, import_invariant2.default)(link.id, `[${link.intlLabel.defaultMessage}]: link.id should be defined`);
      (0, import_invariant2.default)(((_a = link.intlLabel) == null ? void 0 : _a.id) && ((_b = link.intlLabel) == null ? void 0 : _b.defaultMessage), `[${link.intlLabel.defaultMessage}]: link.intlLabel.id & link.intlLabel.defaultMessage`);
      (0, import_invariant2.default)(link.to, `[${link.intlLabel.defaultMessage}]: link.to should be defined`);
      (0, import_invariant2.default)(!link.Component || link.Component && typeof link.Component === "function", `[${link.intlLabel.defaultMessage}]: link.Component must be a function returning a Promise. Please use: \`Component: () => import(path)\` instead.`);
      if (!link.Component || link.Component && typeof link.Component === "function" && // @ts-expect-error – shh
      link.Component[Symbol.toStringTag] === "AsyncFunction") {
        console.warn(`
      [${link.intlLabel.defaultMessage}]: [deprecated] addSettingsLink() was called with an async Component from the plugin "${link.intlLabel.defaultMessage}". This will be removed in the future. Please use: \`Component: () => import(path)\` ensuring you return a default export instead.
      `.trim());
      }
      if (link.to.startsWith("/")) {
        console.warn(`[${link.intlLabel.defaultMessage}]: the \`to\` property of your settings link is an absolute path. It should be relative to \`/settings\`. This has been corrected for you but will be removed in a future version of Strapi.`);
        link.to = link.to.slice(1);
      }
      if (link.to.split("/")[0] === "settings") {
        console.warn(`[${link.intlLabel.defaultMessage}]: the \`to\` property of your settings link has \`settings\` as the first part of it's path. It should be relative to \`settings\` and therefore, not include it. This has been corrected for you but will be removed in a future version of Strapi.`);
        link.to = link.to.split("/").slice(1).join("/");
      }
      const { Component, ...restLink } = link;
      const settingsIndex = this._routes.findIndex((route) => route.path === "settings/*");
      if (!settingsIndex) {
        console.warn("A third party plugin has removed the settings section, the settings link cannot be added.");
        return;
      } else if (!this._routes[settingsIndex].children) {
        this._routes[settingsIndex].children = [];
      }
      if (Component) {
        this._routes[settingsIndex].children.push({
          path: `${link.to}/*`,
          lazy: async () => {
            const mod = await Component();
            if ("default" in mod) {
              return {
                Component: mod.default
              };
            } else {
              return {
                Component: mod
              };
            }
          }
        });
      }
      this._settings[sectionId].links.push(restLink);
    };
    this._routes = initialRoutes;
  }
};
var getPrintableType = (value) => {
  const nativeType = typeof value;
  if (nativeType === "object") {
    if (value === null) return "null";
    if (Array.isArray(value)) return "array";
    if (value instanceof Object && value.constructor.name !== "Object") {
      return value.constructor.name;
    }
  }
  return nativeType;
};

// node_modules/@strapi/admin/dist/admin/admin/src/core/apis/Widgets.mjs
var import_invariant3 = __toESM(require_browser(), 1);
var Widgets = class {
  constructor() {
    this.register = (widget) => {
      if (Array.isArray(widget)) {
        widget.forEach((newWidget) => {
          this.register(newWidget);
        });
      } else {
        (0, import_invariant3.default)(widget.id, "An id must be provided");
        (0, import_invariant3.default)(widget.component, "A component must be provided");
        (0, import_invariant3.default)(widget.title, "A title must be provided");
        (0, import_invariant3.default)(widget.icon, "An icon must be provided");
        const { id, pluginId, ...widgetToStore } = widget;
        const uid = pluginId ? `plugin::${pluginId}.${id}` : `global::${id}`;
        this.widgets[uid] = {
          ...widgetToStore,
          uid
        };
      }
    };
    this.getAll = () => {
      return Object.values(this.widgets);
    };
    this.widgets = {};
  }
};

// node_modules/@strapi/admin/dist/admin/admin/src/core/store/configure.mjs
var staticReducers = {
  [adminApi.reducerPath]: adminApi.reducer,
  admin_app: reducer
};
var injectReducerStoreEnhancer = (appReducers) => (next) => (...args) => {
  const store = next(...args);
  const asyncReducers = {};
  return {
    ...store,
    asyncReducers,
    injectReducer: (key, asyncReducer) => {
      asyncReducers[key] = asyncReducer;
      store.replaceReducer(
        // @ts-expect-error we dynamically add reducers which makes the types uncomfortable.
        combineReducers({
          ...appReducers,
          ...asyncReducers
        })
      );
    }
  };
};
var configureStoreImpl = (preloadedState = {}, appMiddlewares = [], injectedReducers = {}) => {
  const coreReducers = {
    ...staticReducers,
    ...injectedReducers
  };
  const defaultMiddlewareOptions = {};
  if (false) {
    defaultMiddlewareOptions.serializableCheck = false;
    defaultMiddlewareOptions.immutableCheck = false;
  }
  const store = configureStore({
    preloadedState: {
      admin_app: preloadedState.admin_app
    },
    reducer: coreReducers,
    devTools: true,
    middleware: (getDefaultMiddleware) => [
      ...getDefaultMiddleware(defaultMiddlewareOptions),
      rtkQueryUnauthorizedMiddleware,
      adminApi.middleware,
      ...appMiddlewares.map((m) => m())
    ],
    enhancers: [
      injectReducerStoreEnhancer(coreReducers)
    ]
  });
  return store;
};
var rtkQueryUnauthorizedMiddleware = ({ dispatch }) => (next) => (action) => {
  var _a;
  if (isRejected(action) && ((_a = action.payload) == null ? void 0 : _a.status) === 401) {
    dispatch(logout());
    window.location.href = "/admin/auth/login";
    return;
  }
  return next(action);
};

// node_modules/@strapi/admin/dist/admin/admin/src/core/utils/createHook.mjs
var createHook = () => {
  const _handlers = [];
  return {
    register(fn2) {
      _handlers.push(fn2);
    },
    delete(handler) {
      _handlers.splice(_handlers.indexOf(handler), 1);
    },
    runWaterfall(args, store) {
      return _handlers.reduce((acc, fn2) => fn2(acc, store), args);
    },
    async runWaterfallAsync(args, store) {
      let result = args;
      for (const fn2 of _handlers) {
        result = await fn2(result, store);
      }
      return result;
    },
    runSeries(...args) {
      return _handlers.map((fn2) => fn2(...args));
    },
    async runSeriesAsync(...args) {
      const result = [];
      for (const fn2 of _handlers) {
        result.push(await fn2(...args));
      }
      return result;
    },
    runParallel(...args) {
      return Promise.all(_handlers.map((fn2) => {
        return fn2(...args);
      }));
    }
  };
};

// node_modules/@strapi/admin/dist/admin/admin/src/translations/languageNativeNames.mjs
var languageNativeNames = {
  ar: "العربية",
  ca: "Català",
  cs: "Čeština",
  de: "Deutsch",
  dk: "Dansk",
  en: "English",
  "en-GB": "English (United Kingdom)",
  es: "Español",
  eu: "Euskara",
  uz: "O`zbekcha",
  ro: "Română",
  fr: "Français",
  gu: "Gujarati",
  he: "עברית",
  hu: "Magyar",
  id: "Indonesian",
  it: "Italiano",
  ja: "日本語",
  ko: "한국어",
  ml: "Malayalam",
  ms: "Melayu",
  nl: "Nederlands",
  no: "Norwegian",
  pl: "Polski",
  "pt-BR": "Português (Brasil)",
  pt: "Português (Portugal)",
  ru: "Русский",
  sk: "Slovenčina",
  sv: "Swedish",
  th: "ไทย",
  tr: "Türkçe",
  uk: "Українська",
  vi: "Tiếng Việt",
  "zh-Hans": "中文 (简体)",
  zh: "中文 (繁體)",
  sa: "संस्कृत",
  hi: "हिन्दी"
};

// node_modules/@strapi/admin/dist/admin/admin/src/StrapiApp.mjs
function __variableDynamicImportRuntime1__(path) {
  switch (path) {
    case "./translations/ar.json":
      return import("./ar.json-2GIGG7UX.js");
    case "./translations/ca.json":
      return import("./ca.json-MOLPDGE7.js");
    case "./translations/cs.json":
      return import("./cs.json-NLYIZIFN.js");
    case "./translations/de.json":
      return import("./de.json-3UNT74EP.js");
    case "./translations/dk.json":
      return import("./dk.json-6OBWELMR.js");
    case "./translations/en.json":
      return import("./en.json-SGZ3QUVX.js");
    case "./translations/es.json":
      return import("./es.json-RYQMXOUR.js");
    case "./translations/eu.json":
      return import("./eu.json-SAKEWMWA.js");
    case "./translations/fr.json":
      return import("./fr.json-LVX7VFHE.js");
    case "./translations/gu.json":
      return import("./gu.json-EO2GIQID.js");
    case "./translations/he.json":
      return import("./he.json-VZB4I3FT.js");
    case "./translations/hi.json":
      return import("./hi.json-UERK2VXE.js");
    case "./translations/hu.json":
      return import("./hu.json-5OKKJ7RC.js");
    case "./translations/id.json":
      return import("./id.json-GVRRKDG7.js");
    case "./translations/it.json":
      return import("./it.json-DH2UR2AM.js");
    case "./translations/ja.json":
      return import("./ja.json-W3KU6X2O.js");
    case "./translations/ko.json":
      return import("./ko.json-G2XUQFSS.js");
    case "./translations/ml.json":
      return import("./ml.json-KWMY42MA.js");
    case "./translations/ms.json":
      return import("./ms.json-3Z4426VX.js");
    case "./translations/nl.json":
      return import("./nl.json-EKKOHRM2.js");
    case "./translations/no.json":
      return import("./no.json-AGXVR7HE.js");
    case "./translations/pl.json":
      return import("./pl.json-TPY472UD.js");
    case "./translations/pt-BR.json":
      return import("./pt-BR.json-PE2O2NB5.js");
    case "./translations/pt.json":
      return import("./pt.json-FWKE762M.js");
    case "./translations/ru.json":
      return import("./ru.json-V3LIQVZ4.js");
    case "./translations/sa.json":
      return import("./sa.json-XETW5ZUF.js");
    case "./translations/sk.json":
      return import("./sk.json-IC3GPYUO.js");
    case "./translations/sv.json":
      return import("./sv.json-FOM4PBBI.js");
    case "./translations/th.json":
      return import("./th.json-FY5UDF3C.js");
    case "./translations/tr.json":
      return import("./tr.json-NLVL37ZT.js");
    case "./translations/uk.json":
      return import("./uk.json-SXBWOQYX.js");
    case "./translations/vi.json":
      return import("./vi.json-KMBMC32Z.js");
    case "./translations/zh-Hans.json":
      return import("./zh-Hans.json-WM6UZJRM.js");
    case "./translations/zh.json":
      return import("./zh.json-AOAZL3X5.js");
    default:
      return new Promise(function(resolve, reject) {
        (typeof queueMicrotask === "function" ? queueMicrotask : setTimeout)(
          reject.bind(null, new Error("Unknown variable dynamic import: " + path))
        );
      });
  }
}
function __variableDynamicImportRuntime0__(path) {
  switch (path) {
    case "./translations/en-GB.js":
      return import("./en-GB-DSPLX5QL.js");
    default:
      return new Promise(function(resolve, reject) {
        (typeof queueMicrotask === "function" ? queueMicrotask : setTimeout)(
          reject.bind(null, new Error("Unknown variable dynamic import: " + path))
        );
      });
  }
}
var { INJECT_COLUMN_IN_TABLE, MUTATE_COLLECTION_TYPES_LINKS, MUTATE_EDIT_VIEW_LAYOUT, MUTATE_SINGLE_TYPES_LINKS } = HOOKS;
var StrapiApp = class {
  async bootstrap(customBootstrap) {
    Object.keys(this.appPlugins).forEach((plugin) => {
      const bootstrap = this.appPlugins[plugin].bootstrap;
      if (bootstrap) {
        bootstrap({
          addSettingsLink: this.addSettingsLink,
          addSettingsLinks: this.addSettingsLinks,
          getPlugin: this.getPlugin,
          registerHook: this.registerHook
        });
      }
    });
    if ((0, import_isFunction.default)(customBootstrap)) {
      customBootstrap({
        addComponents: this.addComponents,
        addFields: this.addFields,
        addMenuLink: this.addMenuLink,
        addReducers: this.addReducers,
        addSettingsLink: this.addSettingsLink,
        addSettingsLinks: this.addSettingsLinks,
        getPlugin: this.getPlugin,
        registerHook: this.registerHook
      });
    }
  }
  async register(customRegister) {
    Object.keys(this.appPlugins).forEach((plugin) => {
      this.appPlugins[plugin].register(this);
    });
    if ((0, import_isFunction.default)(customRegister)) {
      customRegister(this);
    }
  }
  async loadAdminTrads() {
    const adminLocales = await Promise.all(this.configurations.locales.map(async (locale) => {
      try {
        const { default: data } = await __variableDynamicImportRuntime0__(`./translations/${locale}.js`);
        return {
          data,
          locale
        };
      } catch {
        try {
          const { default: data } = await __variableDynamicImportRuntime1__(`./translations/${locale}.json`);
          return {
            data,
            locale
          };
        } catch {
          return {
            data: null,
            locale
          };
        }
      }
    }));
    return adminLocales.reduce((acc, current) => {
      if (current.data) {
        acc[current.locale] = current.data;
      }
      return acc;
    }, {});
  }
  /**
  * Load the application's translations and merged the custom translations
  * with the default ones.
  */
  async loadTrads(customTranslations = {}) {
    const adminTranslations = await this.loadAdminTrads();
    const arrayOfPromises = Object.keys(this.appPlugins).map((plugin) => {
      const registerTrads = this.appPlugins[plugin].registerTrads;
      if (registerTrads) {
        return registerTrads({
          locales: this.configurations.locales
        });
      }
      return null;
    }).filter((a) => a);
    const pluginsTrads = await Promise.all(arrayOfPromises);
    const mergedTrads = pluginsTrads.reduce((acc, currentPluginTrads) => {
      const pluginTrads = currentPluginTrads.reduce((acc1, current) => {
        acc1[current.locale] = current.data;
        return acc1;
      }, {});
      Object.keys(pluginTrads).forEach((locale) => {
        acc[locale] = {
          ...acc[locale],
          ...pluginTrads[locale]
        };
      });
      return acc;
    }, {});
    const translations = this.configurations.locales.reduce((acc, current) => {
      acc[current] = {
        ...adminTranslations[current],
        ...mergedTrads[current] || {},
        ...customTranslations[current] ?? {}
      };
      return acc;
    }, {});
    this.configurations.translations = translations;
    return Promise.resolve();
  }
  render() {
    const localeNames = (0, import_pick.default)(languageNativeNames, this.configurations.locales || []);
    const locale = localStorage.getItem(LANGUAGE_LOCAL_STORAGE_KEY) || "en";
    this.store = configureStoreImpl({
      admin_app: {
        permissions: (0, import_merge.default)({}, ADMIN_PERMISSIONS_CE, ADMIN_PERMISSIONS_EE),
        theme: {
          availableThemes: [],
          currentTheme: localStorage.getItem(THEME_LOCAL_STORAGE_KEY) || "system"
        },
        language: {
          locale: localeNames[locale] ? locale : "en",
          localeNames
        },
        token: getStoredToken()
      }
    }, this.middlewares, this.reducers);
    const router = this.router.createRouter(this, {
      basename: getBasename()
    });
    return (0, import_jsx_runtime18.jsx)(RouterProvider, {
      router
    });
  }
  constructor({ config, appPlugins } = {}) {
    this.plugins = {};
    this.hooksDict = {};
    this.admin = {
      injectionZones: {}
    };
    this.translations = {};
    this.configurations = {
      authLogo: img,
      head: {
        favicon: ""
      },
      locales: [
        "en"
      ],
      menuLogo: img,
      notifications: {
        releases: true
      },
      themes: {
        light: lightTheme,
        dark: darkTheme
      },
      translations: {},
      tutorials: true
    };
    this.rbac = new RBAC();
    this.library = {
      components: {},
      fields: {}
    };
    this.middlewares = [];
    this.reducers = {};
    this.store = null;
    this.customFields = new CustomFields();
    this.widgets = new Widgets();
    this.addComponents = (components) => {
      if (Array.isArray(components)) {
        components.map((comp) => {
          (0, import_invariant4.default)(comp.Component, "A Component must be provided");
          (0, import_invariant4.default)(comp.name, "A type must be provided");
          this.library.components[comp.name] = comp.Component;
        });
      } else {
        (0, import_invariant4.default)(components.Component, "A Component must be provided");
        (0, import_invariant4.default)(components.name, "A type must be provided");
        this.library.components[components.name] = components.Component;
      }
    };
    this.addFields = (fields) => {
      if (Array.isArray(fields)) {
        fields.map((field) => {
          (0, import_invariant4.default)(field.Component, "A Component must be provided");
          (0, import_invariant4.default)(field.type, "A type must be provided");
          this.library.fields[field.type] = field.Component;
        });
      } else {
        (0, import_invariant4.default)(fields.Component, "A Component must be provided");
        (0, import_invariant4.default)(fields.type, "A type must be provided");
        this.library.fields[fields.type] = fields.Component;
      }
    };
    this.addMiddlewares = (middlewares) => {
      middlewares.forEach((middleware) => {
        this.middlewares.push(middleware);
      });
    };
    this.addRBACMiddleware = (m) => {
      if (Array.isArray(m)) {
        this.rbac.use(m);
      } else {
        this.rbac.use(m);
      }
    };
    this.addReducers = (reducers) => {
      Object.entries(reducers).forEach(([name, reducer3]) => {
        this.reducers[name] = reducer3;
      });
    };
    this.addMenuLink = (link) => this.router.addMenuLink(link);
    this.addSettingsLinks = (sectionId, links) => {
      (0, import_invariant4.default)(Array.isArray(links), "TypeError expected links to be an array");
      this.router.addSettingsLink(sectionId, links);
    };
    this.createSettingSection = (section, links) => this.router.addSettingsLink(section, links);
    this.addSettingsLink = (sectionId, link) => {
      this.router.addSettingsLink(sectionId, link);
    };
    this.createCustomConfigurations = (customConfig) => {
      var _a, _b, _c, _d, _e;
      if (customConfig.locales) {
        this.configurations.locales = [
          "en",
          ...((_a = customConfig.locales) == null ? void 0 : _a.filter((loc) => loc !== "en")) || []
        ];
      }
      if ((_b = customConfig.auth) == null ? void 0 : _b.logo) {
        this.configurations.authLogo = customConfig.auth.logo;
      }
      if ((_c = customConfig.menu) == null ? void 0 : _c.logo) {
        this.configurations.menuLogo = customConfig.menu.logo;
      }
      if ((_d = customConfig.head) == null ? void 0 : _d.favicon) {
        this.configurations.head.favicon = customConfig.head.favicon;
      }
      if (customConfig.theme) {
        const darkTheme2 = customConfig.theme.dark;
        const lightTheme2 = customConfig.theme.light;
        if (!darkTheme2 && !lightTheme2) {
          console.warn(`[deprecated] In future versions, Strapi will stop supporting this theme customization syntax. The theme configuration accepts a light and a dark key to customize each theme separately. See https://docs.strapi.io/developer-docs/latest/development/admin-customization.html#theme-extension.`.trim());
          (0, import_merge.default)(this.configurations.themes.light, customConfig.theme);
        }
        if (lightTheme2) (0, import_merge.default)(this.configurations.themes.light, lightTheme2);
        if (darkTheme2) (0, import_merge.default)(this.configurations.themes.dark, darkTheme2);
      }
      if (((_e = customConfig.notifications) == null ? void 0 : _e.releases) !== void 0) {
        this.configurations.notifications.releases = customConfig.notifications.releases;
      }
      if (customConfig.tutorials !== void 0) {
        this.configurations.tutorials = customConfig.tutorials;
      }
    };
    this.createHook = (name) => {
      this.hooksDict[name] = createHook();
    };
    this.getAdminInjectedComponents = (moduleName, containerName, blockName) => {
      try {
        return this.admin.injectionZones[moduleName][containerName][blockName] || [];
      } catch (err) {
        console.error("Cannot get injected component", err);
        return [];
      }
    };
    this.getPlugin = (pluginId) => this.plugins[pluginId];
    this.registerHook = (name, fn2) => {
      (0, import_invariant4.default)(this.hooksDict[name], `The hook ${name} is not defined. You are trying to register a hook that does not exist in the application.`);
      this.hooksDict[name].register(fn2);
    };
    this.registerPlugin = (pluginConf) => {
      const plugin = new Plugin(pluginConf);
      this.plugins[plugin.pluginId] = plugin;
    };
    this.runHookSeries = (name, asynchronous = false) => asynchronous ? this.hooksDict[name].runSeriesAsync() : this.hooksDict[name].runSeries();
    this.runHookWaterfall = (name, initialValue, store) => {
      return this.hooksDict[name].runWaterfall(initialValue, store);
    };
    this.runHookParallel = (name) => this.hooksDict[name].runParallel();
    this.appPlugins = appPlugins || {};
    this.createCustomConfigurations(config ?? {});
    this.createHook(INJECT_COLUMN_IN_TABLE);
    this.createHook(MUTATE_COLLECTION_TYPES_LINKS);
    this.createHook(MUTATE_SINGLE_TYPES_LINKS);
    this.createHook(MUTATE_EDIT_VIEW_LAYOUT);
    this.router = new Router(getInitialRoutes());
  }
};

// node_modules/@strapi/admin/dist/admin/admin/src/render.mjs
var renderAdmin = async (mountNode, { plugins, customisations, features }) => {
  var _a, _b;
  if (!mountNode) {
    throw new Error("[@strapi/admin]: Could not find the root element to mount the admin app");
  }
  window.strapi = {
    /**
    * This ENV variable is passed from the strapi instance, by default no url is set
    * in the config and therefore the instance returns you an empty string so URLs are relative.
    *
    * To ensure that the backendURL is always set, we use the window.location.origin as a fallback.
    */
    backendURL: createAbsoluteUrl(process.env.STRAPI_ADMIN_BACKEND_URL),
    isEE: false,
    isTrial: false,
    telemetryDisabled: process.env.STRAPI_TELEMETRY_DISABLED === "true",
    future: {
      isEnabled: (name) => {
        var _a2;
        return ((_a2 = features == null ? void 0 : features.future) == null ? void 0 : _a2[name]) === true;
      }
    },
    // @ts-expect-error – there's pollution from the global scope of Node.
    features: {
      SSO: "sso",
      AUDIT_LOGS: "audit-logs",
      REVIEW_WORKFLOWS: "review-workflows",
      /**
      * If we don't get the license then we know it's not EE
      * so no feature is enabled.
      */
      isEnabled: () => false
    },
    projectType: "Community",
    flags: {
      nps: false,
      promoteEE: true
    }
  };
  const { get } = getFetchClient();
  try {
    const { data: { data: { isEE, isTrial, features: features2, flags } } } = await get("/admin/project-type");
    window.strapi.isEE = isEE;
    window.strapi.isTrialLicense = isTrial;
    window.strapi.flags = flags;
    window.strapi.features = {
      ...window.strapi.features,
      isEnabled: (featureName) => features2.some((feature) => feature.name === featureName)
    };
    window.strapi.projectType = isEE ? "Enterprise" : "Community";
  } catch (err) {
    console.error(err);
  }
  const app = new StrapiApp({
    config: customisations == null ? void 0 : customisations.config,
    appPlugins: plugins
  });
  await app.register(customisations == null ? void 0 : customisations.register);
  await app.bootstrap(customisations == null ? void 0 : customisations.bootstrap);
  await app.loadTrads((_a = customisations == null ? void 0 : customisations.config) == null ? void 0 : _a.translations);
  (0, import_client.createRoot)(mountNode).render(app.render());
  if (typeof module !== "undefined" && module && "hot" in module && typeof module.hot === "object" && module.hot !== null && "accept" in module.hot && typeof module.hot.accept === "function") {
    module.hot.accept();
  }
  if (typeof ((_b = import.meta.hot) == null ? void 0 : _b.accept) === "function") {
    import.meta.hot.accept();
  }
};

// node_modules/@strapi/admin/dist/admin/admin/src/components/DescriptionComponentRenderer.mjs
var import_jsx_runtime19 = __toESM(require_jsx_runtime(), 1);
var React10 = __toESM(require_react(), 1);
var import_isEqual = __toESM(require_isEqual(), 1);

// node_modules/@strapi/admin/dist/admin/admin/src/hooks/useForceUpdate.mjs
var React9 = __toESM(require_react(), 1);

// node_modules/@strapi/admin/dist/admin/admin/src/hooks/useIsMounted.mjs
var React8 = __toESM(require_react(), 1);
var useIsMounted = () => {
  const isMounted = React8.useRef(false);
  React8.useLayoutEffect(() => {
    isMounted.current = true;
    return () => {
      isMounted.current = false;
    };
  }, []);
  return isMounted;
};

// node_modules/@strapi/admin/dist/admin/admin/src/hooks/useForceUpdate.mjs
var useForceUpdate = () => {
  const [tick, update] = React9.useState();
  const isMounted = useIsMounted();
  const forceUpdate = React9.useCallback(() => {
    if (isMounted.current) {
      update(Math.random());
    }
  }, [
    isMounted,
    update
  ]);
  return [
    tick,
    forceUpdate
  ];
};

// node_modules/@strapi/admin/dist/admin/admin/src/hooks/useThrottledCallback.mjs
var import_react7 = __toESM(require_react(), 1);
var import_throttle = __toESM(require_throttle(), 1);
var useThrottledCallback = (callback, wait, options) => {
  const throttledCallback = (0, import_react7.useMemo)(() => (0, import_throttle.default)(callback, wait, options), [
    callback,
    options,
    wait
  ]);
  return throttledCallback;
};

// node_modules/@strapi/admin/dist/admin/admin/src/utils/shims.mjs
var requestIdleCallbackShim = (callback) => {
  const start = Date.now();
  return setTimeout(() => {
    callback({
      didTimeout: false,
      timeRemaining() {
        return Math.max(0, Date.now() - start);
      }
    });
  }, 1);
};
var _requestIdleCallback = typeof requestIdleCallback === "undefined" ? requestIdleCallbackShim : requestIdleCallback;
var cancelIdleCallbackShim = (handle) => {
  return clearTimeout(handle);
};
var _cancelIdleCallback = typeof cancelIdleCallback === "undefined" ? cancelIdleCallbackShim : cancelIdleCallback;

// node_modules/@strapi/admin/dist/admin/admin/src/components/DescriptionComponentRenderer.mjs
var DescriptionComponentRenderer = ({ children, props, descriptions }) => {
  const statesRef = React10.useRef({});
  const [tick, forceUpdate] = useForceUpdate();
  const requestHandle = React10.useRef(null);
  const requestUpdate = React10.useCallback(() => {
    if (requestHandle.current) {
      _cancelIdleCallback(requestHandle.current);
    }
    requestHandle.current = _requestIdleCallback(() => {
      requestHandle.current = null;
      forceUpdate();
    });
  }, [
    forceUpdate
  ]);
  const throttledRequestUpdate = useThrottledCallback(requestUpdate, 60, {
    trailing: true
  });
  const update = React10.useCallback((id, description) => {
    if (description === null) {
      delete statesRef.current[id];
    } else {
      const current = statesRef.current[id];
      statesRef.current[id] = {
        ...current,
        value: {
          ...description,
          id
        }
      };
    }
    throttledRequestUpdate();
  }, [
    throttledRequestUpdate
  ]);
  const ids2 = React10.useMemo(() => descriptions.map((description) => getCompId(description)), [
    descriptions
  ]);
  const states = React10.useMemo(
    () => ids2.map((id) => {
      var _a;
      return (_a = statesRef.current[id]) == null ? void 0 : _a.value;
    }).filter((state) => state !== null && state !== void 0),
    /**
    * we leave tick in the deps to ensure the memo is recalculated when the `update` function  is called.
    * the `ids` will most likely be stable unless we get new actions, but we can't respond to the Description
    * Component changing the ref data in any other way.
    */
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      ids2,
      tick
    ]
  );
  return (0, import_jsx_runtime19.jsxs)(import_jsx_runtime19.Fragment, {
    children: [
      descriptions.map((description) => {
        const key = getCompId(description);
        return (0, import_jsx_runtime19.jsx)(Description, {
          id: key,
          description,
          props,
          update
        }, key);
      }),
      children(states)
    ]
  });
};
var Description = React10.memo(({ description, id, props, update }) => {
  const comp = description(props);
  useShallowCompareEffect(() => {
    update(id, comp);
    return () => {
      update(id, null);
    };
  }, comp);
  return null;
}, (prev, next) => (0, import_isEqual.default)(prev.props, next.props));
var ids = /* @__PURE__ */ new WeakMap();
var counter = 0;
function getCompId(comp) {
  const cachedId = ids.get(comp);
  if (cachedId) return cachedId;
  const id = `${comp.name || comp.displayName || "<anonymous>"}-${counter++}`;
  ids.set(comp, id);
  return id;
}
var useShallowCompareMemoize = (value) => {
  const ref = React10.useRef(void 0);
  if (!(0, import_isEqual.default)(value, ref.current)) {
    ref.current = value;
  }
  return [
    ref.current
  ];
};
var useShallowCompareEffect = (callback, dependencies) => {
  React10.useEffect(callback, useShallowCompareMemoize(dependencies));
};

// node_modules/@strapi/admin/dist/admin/admin/src/hooks/useInjectReducer.mjs
var import_react8 = __toESM(require_react(), 1);
function useInjectReducer(namespace, reducer3) {
  const store = useTypedStore();
  (0, import_react8.useEffect)(() => {
    store.injectReducer(namespace, reducer3);
  }, [
    store,
    namespace,
    reducer3
  ]);
}

// node_modules/@strapi/admin/dist/admin/admin/src/utils/rulesEngine.mjs
var import_json_logic_js = __toESM(require_logic(), 1);

// node_modules/zod/lib/index.mjs
var util;
(function(util2) {
  util2.assertEqual = (val) => val;
  function assertIs(_arg) {
  }
  util2.assertIs = assertIs;
  function assertNever(_x) {
    throw new Error();
  }
  util2.assertNever = assertNever;
  util2.arrayToEnum = (items) => {
    const obj = {};
    for (const item of items) {
      obj[item] = item;
    }
    return obj;
  };
  util2.getValidEnumValues = (obj) => {
    const validKeys = util2.objectKeys(obj).filter((k) => typeof obj[obj[k]] !== "number");
    const filtered = {};
    for (const k of validKeys) {
      filtered[k] = obj[k];
    }
    return util2.objectValues(filtered);
  };
  util2.objectValues = (obj) => {
    return util2.objectKeys(obj).map(function(e) {
      return obj[e];
    });
  };
  util2.objectKeys = typeof Object.keys === "function" ? (obj) => Object.keys(obj) : (object) => {
    const keys = [];
    for (const key in object) {
      if (Object.prototype.hasOwnProperty.call(object, key)) {
        keys.push(key);
      }
    }
    return keys;
  };
  util2.find = (arr, checker) => {
    for (const item of arr) {
      if (checker(item))
        return item;
    }
    return void 0;
  };
  util2.isInteger = typeof Number.isInteger === "function" ? (val) => Number.isInteger(val) : (val) => typeof val === "number" && isFinite(val) && Math.floor(val) === val;
  function joinValues(array, separator = " | ") {
    return array.map((val) => typeof val === "string" ? `'${val}'` : val).join(separator);
  }
  util2.joinValues = joinValues;
  util2.jsonStringifyReplacer = (_, value) => {
    if (typeof value === "bigint") {
      return value.toString();
    }
    return value;
  };
})(util || (util = {}));
var objectUtil;
(function(objectUtil2) {
  objectUtil2.mergeShapes = (first, second) => {
    return {
      ...first,
      ...second
      // second overwrites first
    };
  };
})(objectUtil || (objectUtil = {}));
var ZodParsedType = util.arrayToEnum([
  "string",
  "nan",
  "number",
  "integer",
  "float",
  "boolean",
  "date",
  "bigint",
  "symbol",
  "function",
  "undefined",
  "null",
  "array",
  "object",
  "unknown",
  "promise",
  "void",
  "never",
  "map",
  "set"
]);
var getParsedType = (data) => {
  const t = typeof data;
  switch (t) {
    case "undefined":
      return ZodParsedType.undefined;
    case "string":
      return ZodParsedType.string;
    case "number":
      return isNaN(data) ? ZodParsedType.nan : ZodParsedType.number;
    case "boolean":
      return ZodParsedType.boolean;
    case "function":
      return ZodParsedType.function;
    case "bigint":
      return ZodParsedType.bigint;
    case "symbol":
      return ZodParsedType.symbol;
    case "object":
      if (Array.isArray(data)) {
        return ZodParsedType.array;
      }
      if (data === null) {
        return ZodParsedType.null;
      }
      if (data.then && typeof data.then === "function" && data.catch && typeof data.catch === "function") {
        return ZodParsedType.promise;
      }
      if (typeof Map !== "undefined" && data instanceof Map) {
        return ZodParsedType.map;
      }
      if (typeof Set !== "undefined" && data instanceof Set) {
        return ZodParsedType.set;
      }
      if (typeof Date !== "undefined" && data instanceof Date) {
        return ZodParsedType.date;
      }
      return ZodParsedType.object;
    default:
      return ZodParsedType.unknown;
  }
};
var ZodIssueCode = util.arrayToEnum([
  "invalid_type",
  "invalid_literal",
  "custom",
  "invalid_union",
  "invalid_union_discriminator",
  "invalid_enum_value",
  "unrecognized_keys",
  "invalid_arguments",
  "invalid_return_type",
  "invalid_date",
  "invalid_string",
  "too_small",
  "too_big",
  "invalid_intersection_types",
  "not_multiple_of",
  "not_finite"
]);
var quotelessJson = (obj) => {
  const json = JSON.stringify(obj, null, 2);
  return json.replace(/"([^"]+)":/g, "$1:");
};
var ZodError = class _ZodError extends Error {
  get errors() {
    return this.issues;
  }
  constructor(issues) {
    super();
    this.issues = [];
    this.addIssue = (sub) => {
      this.issues = [...this.issues, sub];
    };
    this.addIssues = (subs = []) => {
      this.issues = [...this.issues, ...subs];
    };
    const actualProto = new.target.prototype;
    if (Object.setPrototypeOf) {
      Object.setPrototypeOf(this, actualProto);
    } else {
      this.__proto__ = actualProto;
    }
    this.name = "ZodError";
    this.issues = issues;
  }
  format(_mapper) {
    const mapper = _mapper || function(issue) {
      return issue.message;
    };
    const fieldErrors = { _errors: [] };
    const processError = (error) => {
      for (const issue of error.issues) {
        if (issue.code === "invalid_union") {
          issue.unionErrors.map(processError);
        } else if (issue.code === "invalid_return_type") {
          processError(issue.returnTypeError);
        } else if (issue.code === "invalid_arguments") {
          processError(issue.argumentsError);
        } else if (issue.path.length === 0) {
          fieldErrors._errors.push(mapper(issue));
        } else {
          let curr = fieldErrors;
          let i = 0;
          while (i < issue.path.length) {
            const el = issue.path[i];
            const terminal = i === issue.path.length - 1;
            if (!terminal) {
              curr[el] = curr[el] || { _errors: [] };
            } else {
              curr[el] = curr[el] || { _errors: [] };
              curr[el]._errors.push(mapper(issue));
            }
            curr = curr[el];
            i++;
          }
        }
      }
    };
    processError(this);
    return fieldErrors;
  }
  static assert(value) {
    if (!(value instanceof _ZodError)) {
      throw new Error(`Not a ZodError: ${value}`);
    }
  }
  toString() {
    return this.message;
  }
  get message() {
    return JSON.stringify(this.issues, util.jsonStringifyReplacer, 2);
  }
  get isEmpty() {
    return this.issues.length === 0;
  }
  flatten(mapper = (issue) => issue.message) {
    const fieldErrors = {};
    const formErrors = [];
    for (const sub of this.issues) {
      if (sub.path.length > 0) {
        fieldErrors[sub.path[0]] = fieldErrors[sub.path[0]] || [];
        fieldErrors[sub.path[0]].push(mapper(sub));
      } else {
        formErrors.push(mapper(sub));
      }
    }
    return { formErrors, fieldErrors };
  }
  get formErrors() {
    return this.flatten();
  }
};
ZodError.create = (issues) => {
  const error = new ZodError(issues);
  return error;
};
var errorMap = (issue, _ctx) => {
  let message;
  switch (issue.code) {
    case ZodIssueCode.invalid_type:
      if (issue.received === ZodParsedType.undefined) {
        message = "Required";
      } else {
        message = `Expected ${issue.expected}, received ${issue.received}`;
      }
      break;
    case ZodIssueCode.invalid_literal:
      message = `Invalid literal value, expected ${JSON.stringify(issue.expected, util.jsonStringifyReplacer)}`;
      break;
    case ZodIssueCode.unrecognized_keys:
      message = `Unrecognized key(s) in object: ${util.joinValues(issue.keys, ", ")}`;
      break;
    case ZodIssueCode.invalid_union:
      message = `Invalid input`;
      break;
    case ZodIssueCode.invalid_union_discriminator:
      message = `Invalid discriminator value. Expected ${util.joinValues(issue.options)}`;
      break;
    case ZodIssueCode.invalid_enum_value:
      message = `Invalid enum value. Expected ${util.joinValues(issue.options)}, received '${issue.received}'`;
      break;
    case ZodIssueCode.invalid_arguments:
      message = `Invalid function arguments`;
      break;
    case ZodIssueCode.invalid_return_type:
      message = `Invalid function return type`;
      break;
    case ZodIssueCode.invalid_date:
      message = `Invalid date`;
      break;
    case ZodIssueCode.invalid_string:
      if (typeof issue.validation === "object") {
        if ("includes" in issue.validation) {
          message = `Invalid input: must include "${issue.validation.includes}"`;
          if (typeof issue.validation.position === "number") {
            message = `${message} at one or more positions greater than or equal to ${issue.validation.position}`;
          }
        } else if ("startsWith" in issue.validation) {
          message = `Invalid input: must start with "${issue.validation.startsWith}"`;
        } else if ("endsWith" in issue.validation) {
          message = `Invalid input: must end with "${issue.validation.endsWith}"`;
        } else {
          util.assertNever(issue.validation);
        }
      } else if (issue.validation !== "regex") {
        message = `Invalid ${issue.validation}`;
      } else {
        message = "Invalid";
      }
      break;
    case ZodIssueCode.too_small:
      if (issue.type === "array")
        message = `Array must contain ${issue.exact ? "exactly" : issue.inclusive ? `at least` : `more than`} ${issue.minimum} element(s)`;
      else if (issue.type === "string")
        message = `String must contain ${issue.exact ? "exactly" : issue.inclusive ? `at least` : `over`} ${issue.minimum} character(s)`;
      else if (issue.type === "number")
        message = `Number must be ${issue.exact ? `exactly equal to ` : issue.inclusive ? `greater than or equal to ` : `greater than `}${issue.minimum}`;
      else if (issue.type === "date")
        message = `Date must be ${issue.exact ? `exactly equal to ` : issue.inclusive ? `greater than or equal to ` : `greater than `}${new Date(Number(issue.minimum))}`;
      else
        message = "Invalid input";
      break;
    case ZodIssueCode.too_big:
      if (issue.type === "array")
        message = `Array must contain ${issue.exact ? `exactly` : issue.inclusive ? `at most` : `less than`} ${issue.maximum} element(s)`;
      else if (issue.type === "string")
        message = `String must contain ${issue.exact ? `exactly` : issue.inclusive ? `at most` : `under`} ${issue.maximum} character(s)`;
      else if (issue.type === "number")
        message = `Number must be ${issue.exact ? `exactly` : issue.inclusive ? `less than or equal to` : `less than`} ${issue.maximum}`;
      else if (issue.type === "bigint")
        message = `BigInt must be ${issue.exact ? `exactly` : issue.inclusive ? `less than or equal to` : `less than`} ${issue.maximum}`;
      else if (issue.type === "date")
        message = `Date must be ${issue.exact ? `exactly` : issue.inclusive ? `smaller than or equal to` : `smaller than`} ${new Date(Number(issue.maximum))}`;
      else
        message = "Invalid input";
      break;
    case ZodIssueCode.custom:
      message = `Invalid input`;
      break;
    case ZodIssueCode.invalid_intersection_types:
      message = `Intersection results could not be merged`;
      break;
    case ZodIssueCode.not_multiple_of:
      message = `Number must be a multiple of ${issue.multipleOf}`;
      break;
    case ZodIssueCode.not_finite:
      message = "Number must be finite";
      break;
    default:
      message = _ctx.defaultError;
      util.assertNever(issue);
  }
  return { message };
};
var overrideErrorMap = errorMap;
function setErrorMap(map) {
  overrideErrorMap = map;
}
function getErrorMap() {
  return overrideErrorMap;
}
var makeIssue = (params) => {
  const { data, path, errorMaps, issueData } = params;
  const fullPath = [...path, ...issueData.path || []];
  const fullIssue = {
    ...issueData,
    path: fullPath
  };
  if (issueData.message !== void 0) {
    return {
      ...issueData,
      path: fullPath,
      message: issueData.message
    };
  }
  let errorMessage = "";
  const maps = errorMaps.filter((m) => !!m).slice().reverse();
  for (const map of maps) {
    errorMessage = map(fullIssue, { data, defaultError: errorMessage }).message;
  }
  return {
    ...issueData,
    path: fullPath,
    message: errorMessage
  };
};
var EMPTY_PATH = [];
function addIssueToContext(ctx, issueData) {
  const overrideMap = getErrorMap();
  const issue = makeIssue({
    issueData,
    data: ctx.data,
    path: ctx.path,
    errorMaps: [
      ctx.common.contextualErrorMap,
      // contextual error map is first priority
      ctx.schemaErrorMap,
      // then schema-bound map if available
      overrideMap,
      // then global override map
      overrideMap === errorMap ? void 0 : errorMap
      // then global default map
    ].filter((x) => !!x)
  });
  ctx.common.issues.push(issue);
}
var ParseStatus = class _ParseStatus {
  constructor() {
    this.value = "valid";
  }
  dirty() {
    if (this.value === "valid")
      this.value = "dirty";
  }
  abort() {
    if (this.value !== "aborted")
      this.value = "aborted";
  }
  static mergeArray(status, results) {
    const arrayValue = [];
    for (const s of results) {
      if (s.status === "aborted")
        return INVALID;
      if (s.status === "dirty")
        status.dirty();
      arrayValue.push(s.value);
    }
    return { status: status.value, value: arrayValue };
  }
  static async mergeObjectAsync(status, pairs) {
    const syncPairs = [];
    for (const pair of pairs) {
      const key = await pair.key;
      const value = await pair.value;
      syncPairs.push({
        key,
        value
      });
    }
    return _ParseStatus.mergeObjectSync(status, syncPairs);
  }
  static mergeObjectSync(status, pairs) {
    const finalObject = {};
    for (const pair of pairs) {
      const { key, value } = pair;
      if (key.status === "aborted")
        return INVALID;
      if (value.status === "aborted")
        return INVALID;
      if (key.status === "dirty")
        status.dirty();
      if (value.status === "dirty")
        status.dirty();
      if (key.value !== "__proto__" && (typeof value.value !== "undefined" || pair.alwaysSet)) {
        finalObject[key.value] = value.value;
      }
    }
    return { status: status.value, value: finalObject };
  }
};
var INVALID = Object.freeze({
  status: "aborted"
});
var DIRTY = (value) => ({ status: "dirty", value });
var OK = (value) => ({ status: "valid", value });
var isAborted = (x) => x.status === "aborted";
var isDirty = (x) => x.status === "dirty";
var isValid = (x) => x.status === "valid";
var isAsync = (x) => typeof Promise !== "undefined" && x instanceof Promise;
function __classPrivateFieldGet(receiver, state, kind, f) {
  if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a getter");
  if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot read private member from an object whose class did not declare it");
  return kind === "m" ? f : kind === "a" ? f.call(receiver) : f ? f.value : state.get(receiver);
}
function __classPrivateFieldSet(receiver, state, value, kind, f) {
  if (kind === "m") throw new TypeError("Private method is not writable");
  if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a setter");
  if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot write private member to an object whose class did not declare it");
  return kind === "a" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value), value;
}
var errorUtil;
(function(errorUtil2) {
  errorUtil2.errToObj = (message) => typeof message === "string" ? { message } : message || {};
  errorUtil2.toString = (message) => typeof message === "string" ? message : message === null || message === void 0 ? void 0 : message.message;
})(errorUtil || (errorUtil = {}));
var _ZodEnum_cache;
var _ZodNativeEnum_cache;
var ParseInputLazyPath = class {
  constructor(parent, value, path, key) {
    this._cachedPath = [];
    this.parent = parent;
    this.data = value;
    this._path = path;
    this._key = key;
  }
  get path() {
    if (!this._cachedPath.length) {
      if (this._key instanceof Array) {
        this._cachedPath.push(...this._path, ...this._key);
      } else {
        this._cachedPath.push(...this._path, this._key);
      }
    }
    return this._cachedPath;
  }
};
var handleResult = (ctx, result) => {
  if (isValid(result)) {
    return { success: true, data: result.value };
  } else {
    if (!ctx.common.issues.length) {
      throw new Error("Validation failed but no issues detected.");
    }
    return {
      success: false,
      get error() {
        if (this._error)
          return this._error;
        const error = new ZodError(ctx.common.issues);
        this._error = error;
        return this._error;
      }
    };
  }
};
function processCreateParams(params) {
  if (!params)
    return {};
  const { errorMap: errorMap2, invalid_type_error, required_error, description } = params;
  if (errorMap2 && (invalid_type_error || required_error)) {
    throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);
  }
  if (errorMap2)
    return { errorMap: errorMap2, description };
  const customMap = (iss, ctx) => {
    var _a, _b;
    const { message } = params;
    if (iss.code === "invalid_enum_value") {
      return { message: message !== null && message !== void 0 ? message : ctx.defaultError };
    }
    if (typeof ctx.data === "undefined") {
      return { message: (_a = message !== null && message !== void 0 ? message : required_error) !== null && _a !== void 0 ? _a : ctx.defaultError };
    }
    if (iss.code !== "invalid_type")
      return { message: ctx.defaultError };
    return { message: (_b = message !== null && message !== void 0 ? message : invalid_type_error) !== null && _b !== void 0 ? _b : ctx.defaultError };
  };
  return { errorMap: customMap, description };
}
var ZodType = class {
  get description() {
    return this._def.description;
  }
  _getType(input) {
    return getParsedType(input.data);
  }
  _getOrReturnCtx(input, ctx) {
    return ctx || {
      common: input.parent.common,
      data: input.data,
      parsedType: getParsedType(input.data),
      schemaErrorMap: this._def.errorMap,
      path: input.path,
      parent: input.parent
    };
  }
  _processInputParams(input) {
    return {
      status: new ParseStatus(),
      ctx: {
        common: input.parent.common,
        data: input.data,
        parsedType: getParsedType(input.data),
        schemaErrorMap: this._def.errorMap,
        path: input.path,
        parent: input.parent
      }
    };
  }
  _parseSync(input) {
    const result = this._parse(input);
    if (isAsync(result)) {
      throw new Error("Synchronous parse encountered promise.");
    }
    return result;
  }
  _parseAsync(input) {
    const result = this._parse(input);
    return Promise.resolve(result);
  }
  parse(data, params) {
    const result = this.safeParse(data, params);
    if (result.success)
      return result.data;
    throw result.error;
  }
  safeParse(data, params) {
    var _a;
    const ctx = {
      common: {
        issues: [],
        async: (_a = params === null || params === void 0 ? void 0 : params.async) !== null && _a !== void 0 ? _a : false,
        contextualErrorMap: params === null || params === void 0 ? void 0 : params.errorMap
      },
      path: (params === null || params === void 0 ? void 0 : params.path) || [],
      schemaErrorMap: this._def.errorMap,
      parent: null,
      data,
      parsedType: getParsedType(data)
    };
    const result = this._parseSync({ data, path: ctx.path, parent: ctx });
    return handleResult(ctx, result);
  }
  "~validate"(data) {
    var _a, _b;
    const ctx = {
      common: {
        issues: [],
        async: !!this["~standard"].async
      },
      path: [],
      schemaErrorMap: this._def.errorMap,
      parent: null,
      data,
      parsedType: getParsedType(data)
    };
    if (!this["~standard"].async) {
      try {
        const result = this._parseSync({ data, path: [], parent: ctx });
        return isValid(result) ? {
          value: result.value
        } : {
          issues: ctx.common.issues
        };
      } catch (err) {
        if ((_b = (_a = err === null || err === void 0 ? void 0 : err.message) === null || _a === void 0 ? void 0 : _a.toLowerCase()) === null || _b === void 0 ? void 0 : _b.includes("encountered")) {
          this["~standard"].async = true;
        }
        ctx.common = {
          issues: [],
          async: true
        };
      }
    }
    return this._parseAsync({ data, path: [], parent: ctx }).then((result) => isValid(result) ? {
      value: result.value
    } : {
      issues: ctx.common.issues
    });
  }
  async parseAsync(data, params) {
    const result = await this.safeParseAsync(data, params);
    if (result.success)
      return result.data;
    throw result.error;
  }
  async safeParseAsync(data, params) {
    const ctx = {
      common: {
        issues: [],
        contextualErrorMap: params === null || params === void 0 ? void 0 : params.errorMap,
        async: true
      },
      path: (params === null || params === void 0 ? void 0 : params.path) || [],
      schemaErrorMap: this._def.errorMap,
      parent: null,
      data,
      parsedType: getParsedType(data)
    };
    const maybeAsyncResult = this._parse({ data, path: ctx.path, parent: ctx });
    const result = await (isAsync(maybeAsyncResult) ? maybeAsyncResult : Promise.resolve(maybeAsyncResult));
    return handleResult(ctx, result);
  }
  refine(check, message) {
    const getIssueProperties = (val) => {
      if (typeof message === "string" || typeof message === "undefined") {
        return { message };
      } else if (typeof message === "function") {
        return message(val);
      } else {
        return message;
      }
    };
    return this._refinement((val, ctx) => {
      const result = check(val);
      const setError = () => ctx.addIssue({
        code: ZodIssueCode.custom,
        ...getIssueProperties(val)
      });
      if (typeof Promise !== "undefined" && result instanceof Promise) {
        return result.then((data) => {
          if (!data) {
            setError();
            return false;
          } else {
            return true;
          }
        });
      }
      if (!result) {
        setError();
        return false;
      } else {
        return true;
      }
    });
  }
  refinement(check, refinementData) {
    return this._refinement((val, ctx) => {
      if (!check(val)) {
        ctx.addIssue(typeof refinementData === "function" ? refinementData(val, ctx) : refinementData);
        return false;
      } else {
        return true;
      }
    });
  }
  _refinement(refinement) {
    return new ZodEffects({
      schema: this,
      typeName: ZodFirstPartyTypeKind.ZodEffects,
      effect: { type: "refinement", refinement }
    });
  }
  superRefine(refinement) {
    return this._refinement(refinement);
  }
  constructor(def) {
    this.spa = this.safeParseAsync;
    this._def = def;
    this.parse = this.parse.bind(this);
    this.safeParse = this.safeParse.bind(this);
    this.parseAsync = this.parseAsync.bind(this);
    this.safeParseAsync = this.safeParseAsync.bind(this);
    this.spa = this.spa.bind(this);
    this.refine = this.refine.bind(this);
    this.refinement = this.refinement.bind(this);
    this.superRefine = this.superRefine.bind(this);
    this.optional = this.optional.bind(this);
    this.nullable = this.nullable.bind(this);
    this.nullish = this.nullish.bind(this);
    this.array = this.array.bind(this);
    this.promise = this.promise.bind(this);
    this.or = this.or.bind(this);
    this.and = this.and.bind(this);
    this.transform = this.transform.bind(this);
    this.brand = this.brand.bind(this);
    this.default = this.default.bind(this);
    this.catch = this.catch.bind(this);
    this.describe = this.describe.bind(this);
    this.pipe = this.pipe.bind(this);
    this.readonly = this.readonly.bind(this);
    this.isNullable = this.isNullable.bind(this);
    this.isOptional = this.isOptional.bind(this);
    this["~standard"] = {
      version: 1,
      vendor: "zod",
      validate: (data) => this["~validate"](data)
    };
  }
  optional() {
    return ZodOptional.create(this, this._def);
  }
  nullable() {
    return ZodNullable.create(this, this._def);
  }
  nullish() {
    return this.nullable().optional();
  }
  array() {
    return ZodArray.create(this);
  }
  promise() {
    return ZodPromise.create(this, this._def);
  }
  or(option) {
    return ZodUnion.create([this, option], this._def);
  }
  and(incoming) {
    return ZodIntersection.create(this, incoming, this._def);
  }
  transform(transform) {
    return new ZodEffects({
      ...processCreateParams(this._def),
      schema: this,
      typeName: ZodFirstPartyTypeKind.ZodEffects,
      effect: { type: "transform", transform }
    });
  }
  default(def) {
    const defaultValueFunc = typeof def === "function" ? def : () => def;
    return new ZodDefault({
      ...processCreateParams(this._def),
      innerType: this,
      defaultValue: defaultValueFunc,
      typeName: ZodFirstPartyTypeKind.ZodDefault
    });
  }
  brand() {
    return new ZodBranded({
      typeName: ZodFirstPartyTypeKind.ZodBranded,
      type: this,
      ...processCreateParams(this._def)
    });
  }
  catch(def) {
    const catchValueFunc = typeof def === "function" ? def : () => def;
    return new ZodCatch({
      ...processCreateParams(this._def),
      innerType: this,
      catchValue: catchValueFunc,
      typeName: ZodFirstPartyTypeKind.ZodCatch
    });
  }
  describe(description) {
    const This = this.constructor;
    return new This({
      ...this._def,
      description
    });
  }
  pipe(target) {
    return ZodPipeline.create(this, target);
  }
  readonly() {
    return ZodReadonly.create(this);
  }
  isOptional() {
    return this.safeParse(void 0).success;
  }
  isNullable() {
    return this.safeParse(null).success;
  }
};
var cuidRegex = /^c[^\s-]{8,}$/i;
var cuid2Regex = /^[0-9a-z]+$/;
var ulidRegex = /^[0-9A-HJKMNP-TV-Z]{26}$/i;
var uuidRegex = /^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i;
var nanoidRegex = /^[a-z0-9_-]{21}$/i;
var jwtRegex = /^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/;
var durationRegex = /^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/;
var emailRegex = /^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i;
var _emojiRegex = `^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$`;
var emojiRegex;
var ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/;
var ipv4CidrRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/;
var ipv6Regex = /^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/;
var ipv6CidrRegex = /^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/;
var base64Regex = /^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/;
var base64urlRegex = /^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/;
var dateRegexSource = `((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))`;
var dateRegex = new RegExp(`^${dateRegexSource}$`);
function timeRegexSource(args) {
  let regex = `([01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d`;
  if (args.precision) {
    regex = `${regex}\\.\\d{${args.precision}}`;
  } else if (args.precision == null) {
    regex = `${regex}(\\.\\d+)?`;
  }
  return regex;
}
function timeRegex(args) {
  return new RegExp(`^${timeRegexSource(args)}$`);
}
function datetimeRegex(args) {
  let regex = `${dateRegexSource}T${timeRegexSource(args)}`;
  const opts = [];
  opts.push(args.local ? `Z?` : `Z`);
  if (args.offset)
    opts.push(`([+-]\\d{2}:?\\d{2})`);
  regex = `${regex}(${opts.join("|")})`;
  return new RegExp(`^${regex}$`);
}
function isValidIP(ip, version) {
  if ((version === "v4" || !version) && ipv4Regex.test(ip)) {
    return true;
  }
  if ((version === "v6" || !version) && ipv6Regex.test(ip)) {
    return true;
  }
  return false;
}
function isValidJWT(jwt, alg) {
  if (!jwtRegex.test(jwt))
    return false;
  try {
    const [header] = jwt.split(".");
    const base64 = header.replace(/-/g, "+").replace(/_/g, "/").padEnd(header.length + (4 - header.length % 4) % 4, "=");
    const decoded = JSON.parse(atob(base64));
    if (typeof decoded !== "object" || decoded === null)
      return false;
    if (!decoded.typ || !decoded.alg)
      return false;
    if (alg && decoded.alg !== alg)
      return false;
    return true;
  } catch (_a) {
    return false;
  }
}
function isValidCidr(ip, version) {
  if ((version === "v4" || !version) && ipv4CidrRegex.test(ip)) {
    return true;
  }
  if ((version === "v6" || !version) && ipv6CidrRegex.test(ip)) {
    return true;
  }
  return false;
}
var ZodString = class _ZodString extends ZodType {
  _parse(input) {
    if (this._def.coerce) {
      input.data = String(input.data);
    }
    const parsedType = this._getType(input);
    if (parsedType !== ZodParsedType.string) {
      const ctx2 = this._getOrReturnCtx(input);
      addIssueToContext(ctx2, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.string,
        received: ctx2.parsedType
      });
      return INVALID;
    }
    const status = new ParseStatus();
    let ctx = void 0;
    for (const check of this._def.checks) {
      if (check.kind === "min") {
        if (input.data.length < check.value) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.too_small,
            minimum: check.value,
            type: "string",
            inclusive: true,
            exact: false,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "max") {
        if (input.data.length > check.value) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.too_big,
            maximum: check.value,
            type: "string",
            inclusive: true,
            exact: false,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "length") {
        const tooBig = input.data.length > check.value;
        const tooSmall = input.data.length < check.value;
        if (tooBig || tooSmall) {
          ctx = this._getOrReturnCtx(input, ctx);
          if (tooBig) {
            addIssueToContext(ctx, {
              code: ZodIssueCode.too_big,
              maximum: check.value,
              type: "string",
              inclusive: true,
              exact: true,
              message: check.message
            });
          } else if (tooSmall) {
            addIssueToContext(ctx, {
              code: ZodIssueCode.too_small,
              minimum: check.value,
              type: "string",
              inclusive: true,
              exact: true,
              message: check.message
            });
          }
          status.dirty();
        }
      } else if (check.kind === "email") {
        if (!emailRegex.test(input.data)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            validation: "email",
            code: ZodIssueCode.invalid_string,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "emoji") {
        if (!emojiRegex) {
          emojiRegex = new RegExp(_emojiRegex, "u");
        }
        if (!emojiRegex.test(input.data)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            validation: "emoji",
            code: ZodIssueCode.invalid_string,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "uuid") {
        if (!uuidRegex.test(input.data)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            validation: "uuid",
            code: ZodIssueCode.invalid_string,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "nanoid") {
        if (!nanoidRegex.test(input.data)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            validation: "nanoid",
            code: ZodIssueCode.invalid_string,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "cuid") {
        if (!cuidRegex.test(input.data)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            validation: "cuid",
            code: ZodIssueCode.invalid_string,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "cuid2") {
        if (!cuid2Regex.test(input.data)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            validation: "cuid2",
            code: ZodIssueCode.invalid_string,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "ulid") {
        if (!ulidRegex.test(input.data)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            validation: "ulid",
            code: ZodIssueCode.invalid_string,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "url") {
        try {
          new URL(input.data);
        } catch (_a) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            validation: "url",
            code: ZodIssueCode.invalid_string,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "regex") {
        check.regex.lastIndex = 0;
        const testResult = check.regex.test(input.data);
        if (!testResult) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            validation: "regex",
            code: ZodIssueCode.invalid_string,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "trim") {
        input.data = input.data.trim();
      } else if (check.kind === "includes") {
        if (!input.data.includes(check.value, check.position)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.invalid_string,
            validation: { includes: check.value, position: check.position },
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "toLowerCase") {
        input.data = input.data.toLowerCase();
      } else if (check.kind === "toUpperCase") {
        input.data = input.data.toUpperCase();
      } else if (check.kind === "startsWith") {
        if (!input.data.startsWith(check.value)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.invalid_string,
            validation: { startsWith: check.value },
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "endsWith") {
        if (!input.data.endsWith(check.value)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.invalid_string,
            validation: { endsWith: check.value },
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "datetime") {
        const regex = datetimeRegex(check);
        if (!regex.test(input.data)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.invalid_string,
            validation: "datetime",
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "date") {
        const regex = dateRegex;
        if (!regex.test(input.data)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.invalid_string,
            validation: "date",
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "time") {
        const regex = timeRegex(check);
        if (!regex.test(input.data)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.invalid_string,
            validation: "time",
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "duration") {
        if (!durationRegex.test(input.data)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            validation: "duration",
            code: ZodIssueCode.invalid_string,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "ip") {
        if (!isValidIP(input.data, check.version)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            validation: "ip",
            code: ZodIssueCode.invalid_string,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "jwt") {
        if (!isValidJWT(input.data, check.alg)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            validation: "jwt",
            code: ZodIssueCode.invalid_string,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "cidr") {
        if (!isValidCidr(input.data, check.version)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            validation: "cidr",
            code: ZodIssueCode.invalid_string,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "base64") {
        if (!base64Regex.test(input.data)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            validation: "base64",
            code: ZodIssueCode.invalid_string,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "base64url") {
        if (!base64urlRegex.test(input.data)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            validation: "base64url",
            code: ZodIssueCode.invalid_string,
            message: check.message
          });
          status.dirty();
        }
      } else {
        util.assertNever(check);
      }
    }
    return { status: status.value, value: input.data };
  }
  _regex(regex, validation, message) {
    return this.refinement((data) => regex.test(data), {
      validation,
      code: ZodIssueCode.invalid_string,
      ...errorUtil.errToObj(message)
    });
  }
  _addCheck(check) {
    return new _ZodString({
      ...this._def,
      checks: [...this._def.checks, check]
    });
  }
  email(message) {
    return this._addCheck({ kind: "email", ...errorUtil.errToObj(message) });
  }
  url(message) {
    return this._addCheck({ kind: "url", ...errorUtil.errToObj(message) });
  }
  emoji(message) {
    return this._addCheck({ kind: "emoji", ...errorUtil.errToObj(message) });
  }
  uuid(message) {
    return this._addCheck({ kind: "uuid", ...errorUtil.errToObj(message) });
  }
  nanoid(message) {
    return this._addCheck({ kind: "nanoid", ...errorUtil.errToObj(message) });
  }
  cuid(message) {
    return this._addCheck({ kind: "cuid", ...errorUtil.errToObj(message) });
  }
  cuid2(message) {
    return this._addCheck({ kind: "cuid2", ...errorUtil.errToObj(message) });
  }
  ulid(message) {
    return this._addCheck({ kind: "ulid", ...errorUtil.errToObj(message) });
  }
  base64(message) {
    return this._addCheck({ kind: "base64", ...errorUtil.errToObj(message) });
  }
  base64url(message) {
    return this._addCheck({
      kind: "base64url",
      ...errorUtil.errToObj(message)
    });
  }
  jwt(options) {
    return this._addCheck({ kind: "jwt", ...errorUtil.errToObj(options) });
  }
  ip(options) {
    return this._addCheck({ kind: "ip", ...errorUtil.errToObj(options) });
  }
  cidr(options) {
    return this._addCheck({ kind: "cidr", ...errorUtil.errToObj(options) });
  }
  datetime(options) {
    var _a, _b;
    if (typeof options === "string") {
      return this._addCheck({
        kind: "datetime",
        precision: null,
        offset: false,
        local: false,
        message: options
      });
    }
    return this._addCheck({
      kind: "datetime",
      precision: typeof (options === null || options === void 0 ? void 0 : options.precision) === "undefined" ? null : options === null || options === void 0 ? void 0 : options.precision,
      offset: (_a = options === null || options === void 0 ? void 0 : options.offset) !== null && _a !== void 0 ? _a : false,
      local: (_b = options === null || options === void 0 ? void 0 : options.local) !== null && _b !== void 0 ? _b : false,
      ...errorUtil.errToObj(options === null || options === void 0 ? void 0 : options.message)
    });
  }
  date(message) {
    return this._addCheck({ kind: "date", message });
  }
  time(options) {
    if (typeof options === "string") {
      return this._addCheck({
        kind: "time",
        precision: null,
        message: options
      });
    }
    return this._addCheck({
      kind: "time",
      precision: typeof (options === null || options === void 0 ? void 0 : options.precision) === "undefined" ? null : options === null || options === void 0 ? void 0 : options.precision,
      ...errorUtil.errToObj(options === null || options === void 0 ? void 0 : options.message)
    });
  }
  duration(message) {
    return this._addCheck({ kind: "duration", ...errorUtil.errToObj(message) });
  }
  regex(regex, message) {
    return this._addCheck({
      kind: "regex",
      regex,
      ...errorUtil.errToObj(message)
    });
  }
  includes(value, options) {
    return this._addCheck({
      kind: "includes",
      value,
      position: options === null || options === void 0 ? void 0 : options.position,
      ...errorUtil.errToObj(options === null || options === void 0 ? void 0 : options.message)
    });
  }
  startsWith(value, message) {
    return this._addCheck({
      kind: "startsWith",
      value,
      ...errorUtil.errToObj(message)
    });
  }
  endsWith(value, message) {
    return this._addCheck({
      kind: "endsWith",
      value,
      ...errorUtil.errToObj(message)
    });
  }
  min(minLength, message) {
    return this._addCheck({
      kind: "min",
      value: minLength,
      ...errorUtil.errToObj(message)
    });
  }
  max(maxLength, message) {
    return this._addCheck({
      kind: "max",
      value: maxLength,
      ...errorUtil.errToObj(message)
    });
  }
  length(len, message) {
    return this._addCheck({
      kind: "length",
      value: len,
      ...errorUtil.errToObj(message)
    });
  }
  /**
   * Equivalent to `.min(1)`
   */
  nonempty(message) {
    return this.min(1, errorUtil.errToObj(message));
  }
  trim() {
    return new _ZodString({
      ...this._def,
      checks: [...this._def.checks, { kind: "trim" }]
    });
  }
  toLowerCase() {
    return new _ZodString({
      ...this._def,
      checks: [...this._def.checks, { kind: "toLowerCase" }]
    });
  }
  toUpperCase() {
    return new _ZodString({
      ...this._def,
      checks: [...this._def.checks, { kind: "toUpperCase" }]
    });
  }
  get isDatetime() {
    return !!this._def.checks.find((ch) => ch.kind === "datetime");
  }
  get isDate() {
    return !!this._def.checks.find((ch) => ch.kind === "date");
  }
  get isTime() {
    return !!this._def.checks.find((ch) => ch.kind === "time");
  }
  get isDuration() {
    return !!this._def.checks.find((ch) => ch.kind === "duration");
  }
  get isEmail() {
    return !!this._def.checks.find((ch) => ch.kind === "email");
  }
  get isURL() {
    return !!this._def.checks.find((ch) => ch.kind === "url");
  }
  get isEmoji() {
    return !!this._def.checks.find((ch) => ch.kind === "emoji");
  }
  get isUUID() {
    return !!this._def.checks.find((ch) => ch.kind === "uuid");
  }
  get isNANOID() {
    return !!this._def.checks.find((ch) => ch.kind === "nanoid");
  }
  get isCUID() {
    return !!this._def.checks.find((ch) => ch.kind === "cuid");
  }
  get isCUID2() {
    return !!this._def.checks.find((ch) => ch.kind === "cuid2");
  }
  get isULID() {
    return !!this._def.checks.find((ch) => ch.kind === "ulid");
  }
  get isIP() {
    return !!this._def.checks.find((ch) => ch.kind === "ip");
  }
  get isCIDR() {
    return !!this._def.checks.find((ch) => ch.kind === "cidr");
  }
  get isBase64() {
    return !!this._def.checks.find((ch) => ch.kind === "base64");
  }
  get isBase64url() {
    return !!this._def.checks.find((ch) => ch.kind === "base64url");
  }
  get minLength() {
    let min = null;
    for (const ch of this._def.checks) {
      if (ch.kind === "min") {
        if (min === null || ch.value > min)
          min = ch.value;
      }
    }
    return min;
  }
  get maxLength() {
    let max = null;
    for (const ch of this._def.checks) {
      if (ch.kind === "max") {
        if (max === null || ch.value < max)
          max = ch.value;
      }
    }
    return max;
  }
};
ZodString.create = (params) => {
  var _a;
  return new ZodString({
    checks: [],
    typeName: ZodFirstPartyTypeKind.ZodString,
    coerce: (_a = params === null || params === void 0 ? void 0 : params.coerce) !== null && _a !== void 0 ? _a : false,
    ...processCreateParams(params)
  });
};
function floatSafeRemainder(val, step) {
  const valDecCount = (val.toString().split(".")[1] || "").length;
  const stepDecCount = (step.toString().split(".")[1] || "").length;
  const decCount = valDecCount > stepDecCount ? valDecCount : stepDecCount;
  const valInt = parseInt(val.toFixed(decCount).replace(".", ""));
  const stepInt = parseInt(step.toFixed(decCount).replace(".", ""));
  return valInt % stepInt / Math.pow(10, decCount);
}
var ZodNumber = class _ZodNumber extends ZodType {
  constructor() {
    super(...arguments);
    this.min = this.gte;
    this.max = this.lte;
    this.step = this.multipleOf;
  }
  _parse(input) {
    if (this._def.coerce) {
      input.data = Number(input.data);
    }
    const parsedType = this._getType(input);
    if (parsedType !== ZodParsedType.number) {
      const ctx2 = this._getOrReturnCtx(input);
      addIssueToContext(ctx2, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.number,
        received: ctx2.parsedType
      });
      return INVALID;
    }
    let ctx = void 0;
    const status = new ParseStatus();
    for (const check of this._def.checks) {
      if (check.kind === "int") {
        if (!util.isInteger(input.data)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.invalid_type,
            expected: "integer",
            received: "float",
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "min") {
        const tooSmall = check.inclusive ? input.data < check.value : input.data <= check.value;
        if (tooSmall) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.too_small,
            minimum: check.value,
            type: "number",
            inclusive: check.inclusive,
            exact: false,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "max") {
        const tooBig = check.inclusive ? input.data > check.value : input.data >= check.value;
        if (tooBig) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.too_big,
            maximum: check.value,
            type: "number",
            inclusive: check.inclusive,
            exact: false,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "multipleOf") {
        if (floatSafeRemainder(input.data, check.value) !== 0) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.not_multiple_of,
            multipleOf: check.value,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "finite") {
        if (!Number.isFinite(input.data)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.not_finite,
            message: check.message
          });
          status.dirty();
        }
      } else {
        util.assertNever(check);
      }
    }
    return { status: status.value, value: input.data };
  }
  gte(value, message) {
    return this.setLimit("min", value, true, errorUtil.toString(message));
  }
  gt(value, message) {
    return this.setLimit("min", value, false, errorUtil.toString(message));
  }
  lte(value, message) {
    return this.setLimit("max", value, true, errorUtil.toString(message));
  }
  lt(value, message) {
    return this.setLimit("max", value, false, errorUtil.toString(message));
  }
  setLimit(kind, value, inclusive, message) {
    return new _ZodNumber({
      ...this._def,
      checks: [
        ...this._def.checks,
        {
          kind,
          value,
          inclusive,
          message: errorUtil.toString(message)
        }
      ]
    });
  }
  _addCheck(check) {
    return new _ZodNumber({
      ...this._def,
      checks: [...this._def.checks, check]
    });
  }
  int(message) {
    return this._addCheck({
      kind: "int",
      message: errorUtil.toString(message)
    });
  }
  positive(message) {
    return this._addCheck({
      kind: "min",
      value: 0,
      inclusive: false,
      message: errorUtil.toString(message)
    });
  }
  negative(message) {
    return this._addCheck({
      kind: "max",
      value: 0,
      inclusive: false,
      message: errorUtil.toString(message)
    });
  }
  nonpositive(message) {
    return this._addCheck({
      kind: "max",
      value: 0,
      inclusive: true,
      message: errorUtil.toString(message)
    });
  }
  nonnegative(message) {
    return this._addCheck({
      kind: "min",
      value: 0,
      inclusive: true,
      message: errorUtil.toString(message)
    });
  }
  multipleOf(value, message) {
    return this._addCheck({
      kind: "multipleOf",
      value,
      message: errorUtil.toString(message)
    });
  }
  finite(message) {
    return this._addCheck({
      kind: "finite",
      message: errorUtil.toString(message)
    });
  }
  safe(message) {
    return this._addCheck({
      kind: "min",
      inclusive: true,
      value: Number.MIN_SAFE_INTEGER,
      message: errorUtil.toString(message)
    })._addCheck({
      kind: "max",
      inclusive: true,
      value: Number.MAX_SAFE_INTEGER,
      message: errorUtil.toString(message)
    });
  }
  get minValue() {
    let min = null;
    for (const ch of this._def.checks) {
      if (ch.kind === "min") {
        if (min === null || ch.value > min)
          min = ch.value;
      }
    }
    return min;
  }
  get maxValue() {
    let max = null;
    for (const ch of this._def.checks) {
      if (ch.kind === "max") {
        if (max === null || ch.value < max)
          max = ch.value;
      }
    }
    return max;
  }
  get isInt() {
    return !!this._def.checks.find((ch) => ch.kind === "int" || ch.kind === "multipleOf" && util.isInteger(ch.value));
  }
  get isFinite() {
    let max = null, min = null;
    for (const ch of this._def.checks) {
      if (ch.kind === "finite" || ch.kind === "int" || ch.kind === "multipleOf") {
        return true;
      } else if (ch.kind === "min") {
        if (min === null || ch.value > min)
          min = ch.value;
      } else if (ch.kind === "max") {
        if (max === null || ch.value < max)
          max = ch.value;
      }
    }
    return Number.isFinite(min) && Number.isFinite(max);
  }
};
ZodNumber.create = (params) => {
  return new ZodNumber({
    checks: [],
    typeName: ZodFirstPartyTypeKind.ZodNumber,
    coerce: (params === null || params === void 0 ? void 0 : params.coerce) || false,
    ...processCreateParams(params)
  });
};
var ZodBigInt = class _ZodBigInt extends ZodType {
  constructor() {
    super(...arguments);
    this.min = this.gte;
    this.max = this.lte;
  }
  _parse(input) {
    if (this._def.coerce) {
      try {
        input.data = BigInt(input.data);
      } catch (_a) {
        return this._getInvalidInput(input);
      }
    }
    const parsedType = this._getType(input);
    if (parsedType !== ZodParsedType.bigint) {
      return this._getInvalidInput(input);
    }
    let ctx = void 0;
    const status = new ParseStatus();
    for (const check of this._def.checks) {
      if (check.kind === "min") {
        const tooSmall = check.inclusive ? input.data < check.value : input.data <= check.value;
        if (tooSmall) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.too_small,
            type: "bigint",
            minimum: check.value,
            inclusive: check.inclusive,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "max") {
        const tooBig = check.inclusive ? input.data > check.value : input.data >= check.value;
        if (tooBig) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.too_big,
            type: "bigint",
            maximum: check.value,
            inclusive: check.inclusive,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "multipleOf") {
        if (input.data % check.value !== BigInt(0)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.not_multiple_of,
            multipleOf: check.value,
            message: check.message
          });
          status.dirty();
        }
      } else {
        util.assertNever(check);
      }
    }
    return { status: status.value, value: input.data };
  }
  _getInvalidInput(input) {
    const ctx = this._getOrReturnCtx(input);
    addIssueToContext(ctx, {
      code: ZodIssueCode.invalid_type,
      expected: ZodParsedType.bigint,
      received: ctx.parsedType
    });
    return INVALID;
  }
  gte(value, message) {
    return this.setLimit("min", value, true, errorUtil.toString(message));
  }
  gt(value, message) {
    return this.setLimit("min", value, false, errorUtil.toString(message));
  }
  lte(value, message) {
    return this.setLimit("max", value, true, errorUtil.toString(message));
  }
  lt(value, message) {
    return this.setLimit("max", value, false, errorUtil.toString(message));
  }
  setLimit(kind, value, inclusive, message) {
    return new _ZodBigInt({
      ...this._def,
      checks: [
        ...this._def.checks,
        {
          kind,
          value,
          inclusive,
          message: errorUtil.toString(message)
        }
      ]
    });
  }
  _addCheck(check) {
    return new _ZodBigInt({
      ...this._def,
      checks: [...this._def.checks, check]
    });
  }
  positive(message) {
    return this._addCheck({
      kind: "min",
      value: BigInt(0),
      inclusive: false,
      message: errorUtil.toString(message)
    });
  }
  negative(message) {
    return this._addCheck({
      kind: "max",
      value: BigInt(0),
      inclusive: false,
      message: errorUtil.toString(message)
    });
  }
  nonpositive(message) {
    return this._addCheck({
      kind: "max",
      value: BigInt(0),
      inclusive: true,
      message: errorUtil.toString(message)
    });
  }
  nonnegative(message) {
    return this._addCheck({
      kind: "min",
      value: BigInt(0),
      inclusive: true,
      message: errorUtil.toString(message)
    });
  }
  multipleOf(value, message) {
    return this._addCheck({
      kind: "multipleOf",
      value,
      message: errorUtil.toString(message)
    });
  }
  get minValue() {
    let min = null;
    for (const ch of this._def.checks) {
      if (ch.kind === "min") {
        if (min === null || ch.value > min)
          min = ch.value;
      }
    }
    return min;
  }
  get maxValue() {
    let max = null;
    for (const ch of this._def.checks) {
      if (ch.kind === "max") {
        if (max === null || ch.value < max)
          max = ch.value;
      }
    }
    return max;
  }
};
ZodBigInt.create = (params) => {
  var _a;
  return new ZodBigInt({
    checks: [],
    typeName: ZodFirstPartyTypeKind.ZodBigInt,
    coerce: (_a = params === null || params === void 0 ? void 0 : params.coerce) !== null && _a !== void 0 ? _a : false,
    ...processCreateParams(params)
  });
};
var ZodBoolean = class extends ZodType {
  _parse(input) {
    if (this._def.coerce) {
      input.data = Boolean(input.data);
    }
    const parsedType = this._getType(input);
    if (parsedType !== ZodParsedType.boolean) {
      const ctx = this._getOrReturnCtx(input);
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.boolean,
        received: ctx.parsedType
      });
      return INVALID;
    }
    return OK(input.data);
  }
};
ZodBoolean.create = (params) => {
  return new ZodBoolean({
    typeName: ZodFirstPartyTypeKind.ZodBoolean,
    coerce: (params === null || params === void 0 ? void 0 : params.coerce) || false,
    ...processCreateParams(params)
  });
};
var ZodDate = class _ZodDate extends ZodType {
  _parse(input) {
    if (this._def.coerce) {
      input.data = new Date(input.data);
    }
    const parsedType = this._getType(input);
    if (parsedType !== ZodParsedType.date) {
      const ctx2 = this._getOrReturnCtx(input);
      addIssueToContext(ctx2, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.date,
        received: ctx2.parsedType
      });
      return INVALID;
    }
    if (isNaN(input.data.getTime())) {
      const ctx2 = this._getOrReturnCtx(input);
      addIssueToContext(ctx2, {
        code: ZodIssueCode.invalid_date
      });
      return INVALID;
    }
    const status = new ParseStatus();
    let ctx = void 0;
    for (const check of this._def.checks) {
      if (check.kind === "min") {
        if (input.data.getTime() < check.value) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.too_small,
            message: check.message,
            inclusive: true,
            exact: false,
            minimum: check.value,
            type: "date"
          });
          status.dirty();
        }
      } else if (check.kind === "max") {
        if (input.data.getTime() > check.value) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.too_big,
            message: check.message,
            inclusive: true,
            exact: false,
            maximum: check.value,
            type: "date"
          });
          status.dirty();
        }
      } else {
        util.assertNever(check);
      }
    }
    return {
      status: status.value,
      value: new Date(input.data.getTime())
    };
  }
  _addCheck(check) {
    return new _ZodDate({
      ...this._def,
      checks: [...this._def.checks, check]
    });
  }
  min(minDate, message) {
    return this._addCheck({
      kind: "min",
      value: minDate.getTime(),
      message: errorUtil.toString(message)
    });
  }
  max(maxDate, message) {
    return this._addCheck({
      kind: "max",
      value: maxDate.getTime(),
      message: errorUtil.toString(message)
    });
  }
  get minDate() {
    let min = null;
    for (const ch of this._def.checks) {
      if (ch.kind === "min") {
        if (min === null || ch.value > min)
          min = ch.value;
      }
    }
    return min != null ? new Date(min) : null;
  }
  get maxDate() {
    let max = null;
    for (const ch of this._def.checks) {
      if (ch.kind === "max") {
        if (max === null || ch.value < max)
          max = ch.value;
      }
    }
    return max != null ? new Date(max) : null;
  }
};
ZodDate.create = (params) => {
  return new ZodDate({
    checks: [],
    coerce: (params === null || params === void 0 ? void 0 : params.coerce) || false,
    typeName: ZodFirstPartyTypeKind.ZodDate,
    ...processCreateParams(params)
  });
};
var ZodSymbol = class extends ZodType {
  _parse(input) {
    const parsedType = this._getType(input);
    if (parsedType !== ZodParsedType.symbol) {
      const ctx = this._getOrReturnCtx(input);
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.symbol,
        received: ctx.parsedType
      });
      return INVALID;
    }
    return OK(input.data);
  }
};
ZodSymbol.create = (params) => {
  return new ZodSymbol({
    typeName: ZodFirstPartyTypeKind.ZodSymbol,
    ...processCreateParams(params)
  });
};
var ZodUndefined = class extends ZodType {
  _parse(input) {
    const parsedType = this._getType(input);
    if (parsedType !== ZodParsedType.undefined) {
      const ctx = this._getOrReturnCtx(input);
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.undefined,
        received: ctx.parsedType
      });
      return INVALID;
    }
    return OK(input.data);
  }
};
ZodUndefined.create = (params) => {
  return new ZodUndefined({
    typeName: ZodFirstPartyTypeKind.ZodUndefined,
    ...processCreateParams(params)
  });
};
var ZodNull = class extends ZodType {
  _parse(input) {
    const parsedType = this._getType(input);
    if (parsedType !== ZodParsedType.null) {
      const ctx = this._getOrReturnCtx(input);
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.null,
        received: ctx.parsedType
      });
      return INVALID;
    }
    return OK(input.data);
  }
};
ZodNull.create = (params) => {
  return new ZodNull({
    typeName: ZodFirstPartyTypeKind.ZodNull,
    ...processCreateParams(params)
  });
};
var ZodAny = class extends ZodType {
  constructor() {
    super(...arguments);
    this._any = true;
  }
  _parse(input) {
    return OK(input.data);
  }
};
ZodAny.create = (params) => {
  return new ZodAny({
    typeName: ZodFirstPartyTypeKind.ZodAny,
    ...processCreateParams(params)
  });
};
var ZodUnknown = class extends ZodType {
  constructor() {
    super(...arguments);
    this._unknown = true;
  }
  _parse(input) {
    return OK(input.data);
  }
};
ZodUnknown.create = (params) => {
  return new ZodUnknown({
    typeName: ZodFirstPartyTypeKind.ZodUnknown,
    ...processCreateParams(params)
  });
};
var ZodNever = class extends ZodType {
  _parse(input) {
    const ctx = this._getOrReturnCtx(input);
    addIssueToContext(ctx, {
      code: ZodIssueCode.invalid_type,
      expected: ZodParsedType.never,
      received: ctx.parsedType
    });
    return INVALID;
  }
};
ZodNever.create = (params) => {
  return new ZodNever({
    typeName: ZodFirstPartyTypeKind.ZodNever,
    ...processCreateParams(params)
  });
};
var ZodVoid = class extends ZodType {
  _parse(input) {
    const parsedType = this._getType(input);
    if (parsedType !== ZodParsedType.undefined) {
      const ctx = this._getOrReturnCtx(input);
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.void,
        received: ctx.parsedType
      });
      return INVALID;
    }
    return OK(input.data);
  }
};
ZodVoid.create = (params) => {
  return new ZodVoid({
    typeName: ZodFirstPartyTypeKind.ZodVoid,
    ...processCreateParams(params)
  });
};
var ZodArray = class _ZodArray extends ZodType {
  _parse(input) {
    const { ctx, status } = this._processInputParams(input);
    const def = this._def;
    if (ctx.parsedType !== ZodParsedType.array) {
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.array,
        received: ctx.parsedType
      });
      return INVALID;
    }
    if (def.exactLength !== null) {
      const tooBig = ctx.data.length > def.exactLength.value;
      const tooSmall = ctx.data.length < def.exactLength.value;
      if (tooBig || tooSmall) {
        addIssueToContext(ctx, {
          code: tooBig ? ZodIssueCode.too_big : ZodIssueCode.too_small,
          minimum: tooSmall ? def.exactLength.value : void 0,
          maximum: tooBig ? def.exactLength.value : void 0,
          type: "array",
          inclusive: true,
          exact: true,
          message: def.exactLength.message
        });
        status.dirty();
      }
    }
    if (def.minLength !== null) {
      if (ctx.data.length < def.minLength.value) {
        addIssueToContext(ctx, {
          code: ZodIssueCode.too_small,
          minimum: def.minLength.value,
          type: "array",
          inclusive: true,
          exact: false,
          message: def.minLength.message
        });
        status.dirty();
      }
    }
    if (def.maxLength !== null) {
      if (ctx.data.length > def.maxLength.value) {
        addIssueToContext(ctx, {
          code: ZodIssueCode.too_big,
          maximum: def.maxLength.value,
          type: "array",
          inclusive: true,
          exact: false,
          message: def.maxLength.message
        });
        status.dirty();
      }
    }
    if (ctx.common.async) {
      return Promise.all([...ctx.data].map((item, i) => {
        return def.type._parseAsync(new ParseInputLazyPath(ctx, item, ctx.path, i));
      })).then((result2) => {
        return ParseStatus.mergeArray(status, result2);
      });
    }
    const result = [...ctx.data].map((item, i) => {
      return def.type._parseSync(new ParseInputLazyPath(ctx, item, ctx.path, i));
    });
    return ParseStatus.mergeArray(status, result);
  }
  get element() {
    return this._def.type;
  }
  min(minLength, message) {
    return new _ZodArray({
      ...this._def,
      minLength: { value: minLength, message: errorUtil.toString(message) }
    });
  }
  max(maxLength, message) {
    return new _ZodArray({
      ...this._def,
      maxLength: { value: maxLength, message: errorUtil.toString(message) }
    });
  }
  length(len, message) {
    return new _ZodArray({
      ...this._def,
      exactLength: { value: len, message: errorUtil.toString(message) }
    });
  }
  nonempty(message) {
    return this.min(1, message);
  }
};
ZodArray.create = (schema, params) => {
  return new ZodArray({
    type: schema,
    minLength: null,
    maxLength: null,
    exactLength: null,
    typeName: ZodFirstPartyTypeKind.ZodArray,
    ...processCreateParams(params)
  });
};
function deepPartialify(schema) {
  if (schema instanceof ZodObject) {
    const newShape = {};
    for (const key in schema.shape) {
      const fieldSchema = schema.shape[key];
      newShape[key] = ZodOptional.create(deepPartialify(fieldSchema));
    }
    return new ZodObject({
      ...schema._def,
      shape: () => newShape
    });
  } else if (schema instanceof ZodArray) {
    return new ZodArray({
      ...schema._def,
      type: deepPartialify(schema.element)
    });
  } else if (schema instanceof ZodOptional) {
    return ZodOptional.create(deepPartialify(schema.unwrap()));
  } else if (schema instanceof ZodNullable) {
    return ZodNullable.create(deepPartialify(schema.unwrap()));
  } else if (schema instanceof ZodTuple) {
    return ZodTuple.create(schema.items.map((item) => deepPartialify(item)));
  } else {
    return schema;
  }
}
var ZodObject = class _ZodObject extends ZodType {
  constructor() {
    super(...arguments);
    this._cached = null;
    this.nonstrict = this.passthrough;
    this.augment = this.extend;
  }
  _getCached() {
    if (this._cached !== null)
      return this._cached;
    const shape = this._def.shape();
    const keys = util.objectKeys(shape);
    return this._cached = { shape, keys };
  }
  _parse(input) {
    const parsedType = this._getType(input);
    if (parsedType !== ZodParsedType.object) {
      const ctx2 = this._getOrReturnCtx(input);
      addIssueToContext(ctx2, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.object,
        received: ctx2.parsedType
      });
      return INVALID;
    }
    const { status, ctx } = this._processInputParams(input);
    const { shape, keys: shapeKeys } = this._getCached();
    const extraKeys = [];
    if (!(this._def.catchall instanceof ZodNever && this._def.unknownKeys === "strip")) {
      for (const key in ctx.data) {
        if (!shapeKeys.includes(key)) {
          extraKeys.push(key);
        }
      }
    }
    const pairs = [];
    for (const key of shapeKeys) {
      const keyValidator = shape[key];
      const value = ctx.data[key];
      pairs.push({
        key: { status: "valid", value: key },
        value: keyValidator._parse(new ParseInputLazyPath(ctx, value, ctx.path, key)),
        alwaysSet: key in ctx.data
      });
    }
    if (this._def.catchall instanceof ZodNever) {
      const unknownKeys = this._def.unknownKeys;
      if (unknownKeys === "passthrough") {
        for (const key of extraKeys) {
          pairs.push({
            key: { status: "valid", value: key },
            value: { status: "valid", value: ctx.data[key] }
          });
        }
      } else if (unknownKeys === "strict") {
        if (extraKeys.length > 0) {
          addIssueToContext(ctx, {
            code: ZodIssueCode.unrecognized_keys,
            keys: extraKeys
          });
          status.dirty();
        }
      } else if (unknownKeys === "strip") ;
      else {
        throw new Error(`Internal ZodObject error: invalid unknownKeys value.`);
      }
    } else {
      const catchall = this._def.catchall;
      for (const key of extraKeys) {
        const value = ctx.data[key];
        pairs.push({
          key: { status: "valid", value: key },
          value: catchall._parse(
            new ParseInputLazyPath(ctx, value, ctx.path, key)
            //, ctx.child(key), value, getParsedType(value)
          ),
          alwaysSet: key in ctx.data
        });
      }
    }
    if (ctx.common.async) {
      return Promise.resolve().then(async () => {
        const syncPairs = [];
        for (const pair of pairs) {
          const key = await pair.key;
          const value = await pair.value;
          syncPairs.push({
            key,
            value,
            alwaysSet: pair.alwaysSet
          });
        }
        return syncPairs;
      }).then((syncPairs) => {
        return ParseStatus.mergeObjectSync(status, syncPairs);
      });
    } else {
      return ParseStatus.mergeObjectSync(status, pairs);
    }
  }
  get shape() {
    return this._def.shape();
  }
  strict(message) {
    errorUtil.errToObj;
    return new _ZodObject({
      ...this._def,
      unknownKeys: "strict",
      ...message !== void 0 ? {
        errorMap: (issue, ctx) => {
          var _a, _b, _c, _d;
          const defaultError = (_c = (_b = (_a = this._def).errorMap) === null || _b === void 0 ? void 0 : _b.call(_a, issue, ctx).message) !== null && _c !== void 0 ? _c : ctx.defaultError;
          if (issue.code === "unrecognized_keys")
            return {
              message: (_d = errorUtil.errToObj(message).message) !== null && _d !== void 0 ? _d : defaultError
            };
          return {
            message: defaultError
          };
        }
      } : {}
    });
  }
  strip() {
    return new _ZodObject({
      ...this._def,
      unknownKeys: "strip"
    });
  }
  passthrough() {
    return new _ZodObject({
      ...this._def,
      unknownKeys: "passthrough"
    });
  }
  // const AugmentFactory =
  //   <Def extends ZodObjectDef>(def: Def) =>
  //   <Augmentation extends ZodRawShape>(
  //     augmentation: Augmentation
  //   ): ZodObject<
  //     extendShape<ReturnType<Def["shape"]>, Augmentation>,
  //     Def["unknownKeys"],
  //     Def["catchall"]
  //   > => {
  //     return new ZodObject({
  //       ...def,
  //       shape: () => ({
  //         ...def.shape(),
  //         ...augmentation,
  //       }),
  //     }) as any;
  //   };
  extend(augmentation) {
    return new _ZodObject({
      ...this._def,
      shape: () => ({
        ...this._def.shape(),
        ...augmentation
      })
    });
  }
  /**
   * Prior to zod@1.0.12 there was a bug in the
   * inferred type of merged objects. Please
   * upgrade if you are experiencing issues.
   */
  merge(merging) {
    const merged = new _ZodObject({
      unknownKeys: merging._def.unknownKeys,
      catchall: merging._def.catchall,
      shape: () => ({
        ...this._def.shape(),
        ...merging._def.shape()
      }),
      typeName: ZodFirstPartyTypeKind.ZodObject
    });
    return merged;
  }
  // merge<
  //   Incoming extends AnyZodObject,
  //   Augmentation extends Incoming["shape"],
  //   NewOutput extends {
  //     [k in keyof Augmentation | keyof Output]: k extends keyof Augmentation
  //       ? Augmentation[k]["_output"]
  //       : k extends keyof Output
  //       ? Output[k]
  //       : never;
  //   },
  //   NewInput extends {
  //     [k in keyof Augmentation | keyof Input]: k extends keyof Augmentation
  //       ? Augmentation[k]["_input"]
  //       : k extends keyof Input
  //       ? Input[k]
  //       : never;
  //   }
  // >(
  //   merging: Incoming
  // ): ZodObject<
  //   extendShape<T, ReturnType<Incoming["_def"]["shape"]>>,
  //   Incoming["_def"]["unknownKeys"],
  //   Incoming["_def"]["catchall"],
  //   NewOutput,
  //   NewInput
  // > {
  //   const merged: any = new ZodObject({
  //     unknownKeys: merging._def.unknownKeys,
  //     catchall: merging._def.catchall,
  //     shape: () =>
  //       objectUtil.mergeShapes(this._def.shape(), merging._def.shape()),
  //     typeName: ZodFirstPartyTypeKind.ZodObject,
  //   }) as any;
  //   return merged;
  // }
  setKey(key, schema) {
    return this.augment({ [key]: schema });
  }
  // merge<Incoming extends AnyZodObject>(
  //   merging: Incoming
  // ): //ZodObject<T & Incoming["_shape"], UnknownKeys, Catchall> = (merging) => {
  // ZodObject<
  //   extendShape<T, ReturnType<Incoming["_def"]["shape"]>>,
  //   Incoming["_def"]["unknownKeys"],
  //   Incoming["_def"]["catchall"]
  // > {
  //   // const mergedShape = objectUtil.mergeShapes(
  //   //   this._def.shape(),
  //   //   merging._def.shape()
  //   // );
  //   const merged: any = new ZodObject({
  //     unknownKeys: merging._def.unknownKeys,
  //     catchall: merging._def.catchall,
  //     shape: () =>
  //       objectUtil.mergeShapes(this._def.shape(), merging._def.shape()),
  //     typeName: ZodFirstPartyTypeKind.ZodObject,
  //   }) as any;
  //   return merged;
  // }
  catchall(index) {
    return new _ZodObject({
      ...this._def,
      catchall: index
    });
  }
  pick(mask) {
    const shape = {};
    util.objectKeys(mask).forEach((key) => {
      if (mask[key] && this.shape[key]) {
        shape[key] = this.shape[key];
      }
    });
    return new _ZodObject({
      ...this._def,
      shape: () => shape
    });
  }
  omit(mask) {
    const shape = {};
    util.objectKeys(this.shape).forEach((key) => {
      if (!mask[key]) {
        shape[key] = this.shape[key];
      }
    });
    return new _ZodObject({
      ...this._def,
      shape: () => shape
    });
  }
  /**
   * @deprecated
   */
  deepPartial() {
    return deepPartialify(this);
  }
  partial(mask) {
    const newShape = {};
    util.objectKeys(this.shape).forEach((key) => {
      const fieldSchema = this.shape[key];
      if (mask && !mask[key]) {
        newShape[key] = fieldSchema;
      } else {
        newShape[key] = fieldSchema.optional();
      }
    });
    return new _ZodObject({
      ...this._def,
      shape: () => newShape
    });
  }
  required(mask) {
    const newShape = {};
    util.objectKeys(this.shape).forEach((key) => {
      if (mask && !mask[key]) {
        newShape[key] = this.shape[key];
      } else {
        const fieldSchema = this.shape[key];
        let newField = fieldSchema;
        while (newField instanceof ZodOptional) {
          newField = newField._def.innerType;
        }
        newShape[key] = newField;
      }
    });
    return new _ZodObject({
      ...this._def,
      shape: () => newShape
    });
  }
  keyof() {
    return createZodEnum(util.objectKeys(this.shape));
  }
};
ZodObject.create = (shape, params) => {
  return new ZodObject({
    shape: () => shape,
    unknownKeys: "strip",
    catchall: ZodNever.create(),
    typeName: ZodFirstPartyTypeKind.ZodObject,
    ...processCreateParams(params)
  });
};
ZodObject.strictCreate = (shape, params) => {
  return new ZodObject({
    shape: () => shape,
    unknownKeys: "strict",
    catchall: ZodNever.create(),
    typeName: ZodFirstPartyTypeKind.ZodObject,
    ...processCreateParams(params)
  });
};
ZodObject.lazycreate = (shape, params) => {
  return new ZodObject({
    shape,
    unknownKeys: "strip",
    catchall: ZodNever.create(),
    typeName: ZodFirstPartyTypeKind.ZodObject,
    ...processCreateParams(params)
  });
};
var ZodUnion = class extends ZodType {
  _parse(input) {
    const { ctx } = this._processInputParams(input);
    const options = this._def.options;
    function handleResults(results) {
      for (const result of results) {
        if (result.result.status === "valid") {
          return result.result;
        }
      }
      for (const result of results) {
        if (result.result.status === "dirty") {
          ctx.common.issues.push(...result.ctx.common.issues);
          return result.result;
        }
      }
      const unionErrors = results.map((result) => new ZodError(result.ctx.common.issues));
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_union,
        unionErrors
      });
      return INVALID;
    }
    if (ctx.common.async) {
      return Promise.all(options.map(async (option) => {
        const childCtx = {
          ...ctx,
          common: {
            ...ctx.common,
            issues: []
          },
          parent: null
        };
        return {
          result: await option._parseAsync({
            data: ctx.data,
            path: ctx.path,
            parent: childCtx
          }),
          ctx: childCtx
        };
      })).then(handleResults);
    } else {
      let dirty = void 0;
      const issues = [];
      for (const option of options) {
        const childCtx = {
          ...ctx,
          common: {
            ...ctx.common,
            issues: []
          },
          parent: null
        };
        const result = option._parseSync({
          data: ctx.data,
          path: ctx.path,
          parent: childCtx
        });
        if (result.status === "valid") {
          return result;
        } else if (result.status === "dirty" && !dirty) {
          dirty = { result, ctx: childCtx };
        }
        if (childCtx.common.issues.length) {
          issues.push(childCtx.common.issues);
        }
      }
      if (dirty) {
        ctx.common.issues.push(...dirty.ctx.common.issues);
        return dirty.result;
      }
      const unionErrors = issues.map((issues2) => new ZodError(issues2));
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_union,
        unionErrors
      });
      return INVALID;
    }
  }
  get options() {
    return this._def.options;
  }
};
ZodUnion.create = (types, params) => {
  return new ZodUnion({
    options: types,
    typeName: ZodFirstPartyTypeKind.ZodUnion,
    ...processCreateParams(params)
  });
};
var getDiscriminator = (type) => {
  if (type instanceof ZodLazy) {
    return getDiscriminator(type.schema);
  } else if (type instanceof ZodEffects) {
    return getDiscriminator(type.innerType());
  } else if (type instanceof ZodLiteral) {
    return [type.value];
  } else if (type instanceof ZodEnum) {
    return type.options;
  } else if (type instanceof ZodNativeEnum) {
    return util.objectValues(type.enum);
  } else if (type instanceof ZodDefault) {
    return getDiscriminator(type._def.innerType);
  } else if (type instanceof ZodUndefined) {
    return [void 0];
  } else if (type instanceof ZodNull) {
    return [null];
  } else if (type instanceof ZodOptional) {
    return [void 0, ...getDiscriminator(type.unwrap())];
  } else if (type instanceof ZodNullable) {
    return [null, ...getDiscriminator(type.unwrap())];
  } else if (type instanceof ZodBranded) {
    return getDiscriminator(type.unwrap());
  } else if (type instanceof ZodReadonly) {
    return getDiscriminator(type.unwrap());
  } else if (type instanceof ZodCatch) {
    return getDiscriminator(type._def.innerType);
  } else {
    return [];
  }
};
var ZodDiscriminatedUnion = class _ZodDiscriminatedUnion extends ZodType {
  _parse(input) {
    const { ctx } = this._processInputParams(input);
    if (ctx.parsedType !== ZodParsedType.object) {
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.object,
        received: ctx.parsedType
      });
      return INVALID;
    }
    const discriminator = this.discriminator;
    const discriminatorValue = ctx.data[discriminator];
    const option = this.optionsMap.get(discriminatorValue);
    if (!option) {
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_union_discriminator,
        options: Array.from(this.optionsMap.keys()),
        path: [discriminator]
      });
      return INVALID;
    }
    if (ctx.common.async) {
      return option._parseAsync({
        data: ctx.data,
        path: ctx.path,
        parent: ctx
      });
    } else {
      return option._parseSync({
        data: ctx.data,
        path: ctx.path,
        parent: ctx
      });
    }
  }
  get discriminator() {
    return this._def.discriminator;
  }
  get options() {
    return this._def.options;
  }
  get optionsMap() {
    return this._def.optionsMap;
  }
  /**
   * The constructor of the discriminated union schema. Its behaviour is very similar to that of the normal z.union() constructor.
   * However, it only allows a union of objects, all of which need to share a discriminator property. This property must
   * have a different value for each object in the union.
   * @param discriminator the name of the discriminator property
   * @param types an array of object schemas
   * @param params
   */
  static create(discriminator, options, params) {
    const optionsMap = /* @__PURE__ */ new Map();
    for (const type of options) {
      const discriminatorValues = getDiscriminator(type.shape[discriminator]);
      if (!discriminatorValues.length) {
        throw new Error(`A discriminator value for key \`${discriminator}\` could not be extracted from all schema options`);
      }
      for (const value of discriminatorValues) {
        if (optionsMap.has(value)) {
          throw new Error(`Discriminator property ${String(discriminator)} has duplicate value ${String(value)}`);
        }
        optionsMap.set(value, type);
      }
    }
    return new _ZodDiscriminatedUnion({
      typeName: ZodFirstPartyTypeKind.ZodDiscriminatedUnion,
      discriminator,
      options,
      optionsMap,
      ...processCreateParams(params)
    });
  }
};
function mergeValues(a, b) {
  const aType = getParsedType(a);
  const bType = getParsedType(b);
  if (a === b) {
    return { valid: true, data: a };
  } else if (aType === ZodParsedType.object && bType === ZodParsedType.object) {
    const bKeys = util.objectKeys(b);
    const sharedKeys = util.objectKeys(a).filter((key) => bKeys.indexOf(key) !== -1);
    const newObj = { ...a, ...b };
    for (const key of sharedKeys) {
      const sharedValue = mergeValues(a[key], b[key]);
      if (!sharedValue.valid) {
        return { valid: false };
      }
      newObj[key] = sharedValue.data;
    }
    return { valid: true, data: newObj };
  } else if (aType === ZodParsedType.array && bType === ZodParsedType.array) {
    if (a.length !== b.length) {
      return { valid: false };
    }
    const newArray = [];
    for (let index = 0; index < a.length; index++) {
      const itemA = a[index];
      const itemB = b[index];
      const sharedValue = mergeValues(itemA, itemB);
      if (!sharedValue.valid) {
        return { valid: false };
      }
      newArray.push(sharedValue.data);
    }
    return { valid: true, data: newArray };
  } else if (aType === ZodParsedType.date && bType === ZodParsedType.date && +a === +b) {
    return { valid: true, data: a };
  } else {
    return { valid: false };
  }
}
var ZodIntersection = class extends ZodType {
  _parse(input) {
    const { status, ctx } = this._processInputParams(input);
    const handleParsed = (parsedLeft, parsedRight) => {
      if (isAborted(parsedLeft) || isAborted(parsedRight)) {
        return INVALID;
      }
      const merged = mergeValues(parsedLeft.value, parsedRight.value);
      if (!merged.valid) {
        addIssueToContext(ctx, {
          code: ZodIssueCode.invalid_intersection_types
        });
        return INVALID;
      }
      if (isDirty(parsedLeft) || isDirty(parsedRight)) {
        status.dirty();
      }
      return { status: status.value, value: merged.data };
    };
    if (ctx.common.async) {
      return Promise.all([
        this._def.left._parseAsync({
          data: ctx.data,
          path: ctx.path,
          parent: ctx
        }),
        this._def.right._parseAsync({
          data: ctx.data,
          path: ctx.path,
          parent: ctx
        })
      ]).then(([left, right]) => handleParsed(left, right));
    } else {
      return handleParsed(this._def.left._parseSync({
        data: ctx.data,
        path: ctx.path,
        parent: ctx
      }), this._def.right._parseSync({
        data: ctx.data,
        path: ctx.path,
        parent: ctx
      }));
    }
  }
};
ZodIntersection.create = (left, right, params) => {
  return new ZodIntersection({
    left,
    right,
    typeName: ZodFirstPartyTypeKind.ZodIntersection,
    ...processCreateParams(params)
  });
};
var ZodTuple = class _ZodTuple extends ZodType {
  _parse(input) {
    const { status, ctx } = this._processInputParams(input);
    if (ctx.parsedType !== ZodParsedType.array) {
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.array,
        received: ctx.parsedType
      });
      return INVALID;
    }
    if (ctx.data.length < this._def.items.length) {
      addIssueToContext(ctx, {
        code: ZodIssueCode.too_small,
        minimum: this._def.items.length,
        inclusive: true,
        exact: false,
        type: "array"
      });
      return INVALID;
    }
    const rest = this._def.rest;
    if (!rest && ctx.data.length > this._def.items.length) {
      addIssueToContext(ctx, {
        code: ZodIssueCode.too_big,
        maximum: this._def.items.length,
        inclusive: true,
        exact: false,
        type: "array"
      });
      status.dirty();
    }
    const items = [...ctx.data].map((item, itemIndex) => {
      const schema = this._def.items[itemIndex] || this._def.rest;
      if (!schema)
        return null;
      return schema._parse(new ParseInputLazyPath(ctx, item, ctx.path, itemIndex));
    }).filter((x) => !!x);
    if (ctx.common.async) {
      return Promise.all(items).then((results) => {
        return ParseStatus.mergeArray(status, results);
      });
    } else {
      return ParseStatus.mergeArray(status, items);
    }
  }
  get items() {
    return this._def.items;
  }
  rest(rest) {
    return new _ZodTuple({
      ...this._def,
      rest
    });
  }
};
ZodTuple.create = (schemas, params) => {
  if (!Array.isArray(schemas)) {
    throw new Error("You must pass an array of schemas to z.tuple([ ... ])");
  }
  return new ZodTuple({
    items: schemas,
    typeName: ZodFirstPartyTypeKind.ZodTuple,
    rest: null,
    ...processCreateParams(params)
  });
};
var ZodRecord = class _ZodRecord extends ZodType {
  get keySchema() {
    return this._def.keyType;
  }
  get valueSchema() {
    return this._def.valueType;
  }
  _parse(input) {
    const { status, ctx } = this._processInputParams(input);
    if (ctx.parsedType !== ZodParsedType.object) {
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.object,
        received: ctx.parsedType
      });
      return INVALID;
    }
    const pairs = [];
    const keyType = this._def.keyType;
    const valueType = this._def.valueType;
    for (const key in ctx.data) {
      pairs.push({
        key: keyType._parse(new ParseInputLazyPath(ctx, key, ctx.path, key)),
        value: valueType._parse(new ParseInputLazyPath(ctx, ctx.data[key], ctx.path, key)),
        alwaysSet: key in ctx.data
      });
    }
    if (ctx.common.async) {
      return ParseStatus.mergeObjectAsync(status, pairs);
    } else {
      return ParseStatus.mergeObjectSync(status, pairs);
    }
  }
  get element() {
    return this._def.valueType;
  }
  static create(first, second, third) {
    if (second instanceof ZodType) {
      return new _ZodRecord({
        keyType: first,
        valueType: second,
        typeName: ZodFirstPartyTypeKind.ZodRecord,
        ...processCreateParams(third)
      });
    }
    return new _ZodRecord({
      keyType: ZodString.create(),
      valueType: first,
      typeName: ZodFirstPartyTypeKind.ZodRecord,
      ...processCreateParams(second)
    });
  }
};
var ZodMap = class extends ZodType {
  get keySchema() {
    return this._def.keyType;
  }
  get valueSchema() {
    return this._def.valueType;
  }
  _parse(input) {
    const { status, ctx } = this._processInputParams(input);
    if (ctx.parsedType !== ZodParsedType.map) {
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.map,
        received: ctx.parsedType
      });
      return INVALID;
    }
    const keyType = this._def.keyType;
    const valueType = this._def.valueType;
    const pairs = [...ctx.data.entries()].map(([key, value], index) => {
      return {
        key: keyType._parse(new ParseInputLazyPath(ctx, key, ctx.path, [index, "key"])),
        value: valueType._parse(new ParseInputLazyPath(ctx, value, ctx.path, [index, "value"]))
      };
    });
    if (ctx.common.async) {
      const finalMap = /* @__PURE__ */ new Map();
      return Promise.resolve().then(async () => {
        for (const pair of pairs) {
          const key = await pair.key;
          const value = await pair.value;
          if (key.status === "aborted" || value.status === "aborted") {
            return INVALID;
          }
          if (key.status === "dirty" || value.status === "dirty") {
            status.dirty();
          }
          finalMap.set(key.value, value.value);
        }
        return { status: status.value, value: finalMap };
      });
    } else {
      const finalMap = /* @__PURE__ */ new Map();
      for (const pair of pairs) {
        const key = pair.key;
        const value = pair.value;
        if (key.status === "aborted" || value.status === "aborted") {
          return INVALID;
        }
        if (key.status === "dirty" || value.status === "dirty") {
          status.dirty();
        }
        finalMap.set(key.value, value.value);
      }
      return { status: status.value, value: finalMap };
    }
  }
};
ZodMap.create = (keyType, valueType, params) => {
  return new ZodMap({
    valueType,
    keyType,
    typeName: ZodFirstPartyTypeKind.ZodMap,
    ...processCreateParams(params)
  });
};
var ZodSet = class _ZodSet extends ZodType {
  _parse(input) {
    const { status, ctx } = this._processInputParams(input);
    if (ctx.parsedType !== ZodParsedType.set) {
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.set,
        received: ctx.parsedType
      });
      return INVALID;
    }
    const def = this._def;
    if (def.minSize !== null) {
      if (ctx.data.size < def.minSize.value) {
        addIssueToContext(ctx, {
          code: ZodIssueCode.too_small,
          minimum: def.minSize.value,
          type: "set",
          inclusive: true,
          exact: false,
          message: def.minSize.message
        });
        status.dirty();
      }
    }
    if (def.maxSize !== null) {
      if (ctx.data.size > def.maxSize.value) {
        addIssueToContext(ctx, {
          code: ZodIssueCode.too_big,
          maximum: def.maxSize.value,
          type: "set",
          inclusive: true,
          exact: false,
          message: def.maxSize.message
        });
        status.dirty();
      }
    }
    const valueType = this._def.valueType;
    function finalizeSet(elements2) {
      const parsedSet = /* @__PURE__ */ new Set();
      for (const element of elements2) {
        if (element.status === "aborted")
          return INVALID;
        if (element.status === "dirty")
          status.dirty();
        parsedSet.add(element.value);
      }
      return { status: status.value, value: parsedSet };
    }
    const elements = [...ctx.data.values()].map((item, i) => valueType._parse(new ParseInputLazyPath(ctx, item, ctx.path, i)));
    if (ctx.common.async) {
      return Promise.all(elements).then((elements2) => finalizeSet(elements2));
    } else {
      return finalizeSet(elements);
    }
  }
  min(minSize, message) {
    return new _ZodSet({
      ...this._def,
      minSize: { value: minSize, message: errorUtil.toString(message) }
    });
  }
  max(maxSize, message) {
    return new _ZodSet({
      ...this._def,
      maxSize: { value: maxSize, message: errorUtil.toString(message) }
    });
  }
  size(size, message) {
    return this.min(size, message).max(size, message);
  }
  nonempty(message) {
    return this.min(1, message);
  }
};
ZodSet.create = (valueType, params) => {
  return new ZodSet({
    valueType,
    minSize: null,
    maxSize: null,
    typeName: ZodFirstPartyTypeKind.ZodSet,
    ...processCreateParams(params)
  });
};
var ZodFunction = class _ZodFunction extends ZodType {
  constructor() {
    super(...arguments);
    this.validate = this.implement;
  }
  _parse(input) {
    const { ctx } = this._processInputParams(input);
    if (ctx.parsedType !== ZodParsedType.function) {
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.function,
        received: ctx.parsedType
      });
      return INVALID;
    }
    function makeArgsIssue(args, error) {
      return makeIssue({
        data: args,
        path: ctx.path,
        errorMaps: [
          ctx.common.contextualErrorMap,
          ctx.schemaErrorMap,
          getErrorMap(),
          errorMap
        ].filter((x) => !!x),
        issueData: {
          code: ZodIssueCode.invalid_arguments,
          argumentsError: error
        }
      });
    }
    function makeReturnsIssue(returns, error) {
      return makeIssue({
        data: returns,
        path: ctx.path,
        errorMaps: [
          ctx.common.contextualErrorMap,
          ctx.schemaErrorMap,
          getErrorMap(),
          errorMap
        ].filter((x) => !!x),
        issueData: {
          code: ZodIssueCode.invalid_return_type,
          returnTypeError: error
        }
      });
    }
    const params = { errorMap: ctx.common.contextualErrorMap };
    const fn2 = ctx.data;
    if (this._def.returns instanceof ZodPromise) {
      const me = this;
      return OK(async function(...args) {
        const error = new ZodError([]);
        const parsedArgs = await me._def.args.parseAsync(args, params).catch((e) => {
          error.addIssue(makeArgsIssue(args, e));
          throw error;
        });
        const result = await Reflect.apply(fn2, this, parsedArgs);
        const parsedReturns = await me._def.returns._def.type.parseAsync(result, params).catch((e) => {
          error.addIssue(makeReturnsIssue(result, e));
          throw error;
        });
        return parsedReturns;
      });
    } else {
      const me = this;
      return OK(function(...args) {
        const parsedArgs = me._def.args.safeParse(args, params);
        if (!parsedArgs.success) {
          throw new ZodError([makeArgsIssue(args, parsedArgs.error)]);
        }
        const result = Reflect.apply(fn2, this, parsedArgs.data);
        const parsedReturns = me._def.returns.safeParse(result, params);
        if (!parsedReturns.success) {
          throw new ZodError([makeReturnsIssue(result, parsedReturns.error)]);
        }
        return parsedReturns.data;
      });
    }
  }
  parameters() {
    return this._def.args;
  }
  returnType() {
    return this._def.returns;
  }
  args(...items) {
    return new _ZodFunction({
      ...this._def,
      args: ZodTuple.create(items).rest(ZodUnknown.create())
    });
  }
  returns(returnType) {
    return new _ZodFunction({
      ...this._def,
      returns: returnType
    });
  }
  implement(func) {
    const validatedFunc = this.parse(func);
    return validatedFunc;
  }
  strictImplement(func) {
    const validatedFunc = this.parse(func);
    return validatedFunc;
  }
  static create(args, returns, params) {
    return new _ZodFunction({
      args: args ? args : ZodTuple.create([]).rest(ZodUnknown.create()),
      returns: returns || ZodUnknown.create(),
      typeName: ZodFirstPartyTypeKind.ZodFunction,
      ...processCreateParams(params)
    });
  }
};
var ZodLazy = class extends ZodType {
  get schema() {
    return this._def.getter();
  }
  _parse(input) {
    const { ctx } = this._processInputParams(input);
    const lazySchema = this._def.getter();
    return lazySchema._parse({ data: ctx.data, path: ctx.path, parent: ctx });
  }
};
ZodLazy.create = (getter, params) => {
  return new ZodLazy({
    getter,
    typeName: ZodFirstPartyTypeKind.ZodLazy,
    ...processCreateParams(params)
  });
};
var ZodLiteral = class extends ZodType {
  _parse(input) {
    if (input.data !== this._def.value) {
      const ctx = this._getOrReturnCtx(input);
      addIssueToContext(ctx, {
        received: ctx.data,
        code: ZodIssueCode.invalid_literal,
        expected: this._def.value
      });
      return INVALID;
    }
    return { status: "valid", value: input.data };
  }
  get value() {
    return this._def.value;
  }
};
ZodLiteral.create = (value, params) => {
  return new ZodLiteral({
    value,
    typeName: ZodFirstPartyTypeKind.ZodLiteral,
    ...processCreateParams(params)
  });
};
function createZodEnum(values, params) {
  return new ZodEnum({
    values,
    typeName: ZodFirstPartyTypeKind.ZodEnum,
    ...processCreateParams(params)
  });
}
var ZodEnum = class _ZodEnum extends ZodType {
  constructor() {
    super(...arguments);
    _ZodEnum_cache.set(this, void 0);
  }
  _parse(input) {
    if (typeof input.data !== "string") {
      const ctx = this._getOrReturnCtx(input);
      const expectedValues = this._def.values;
      addIssueToContext(ctx, {
        expected: util.joinValues(expectedValues),
        received: ctx.parsedType,
        code: ZodIssueCode.invalid_type
      });
      return INVALID;
    }
    if (!__classPrivateFieldGet(this, _ZodEnum_cache, "f")) {
      __classPrivateFieldSet(this, _ZodEnum_cache, new Set(this._def.values), "f");
    }
    if (!__classPrivateFieldGet(this, _ZodEnum_cache, "f").has(input.data)) {
      const ctx = this._getOrReturnCtx(input);
      const expectedValues = this._def.values;
      addIssueToContext(ctx, {
        received: ctx.data,
        code: ZodIssueCode.invalid_enum_value,
        options: expectedValues
      });
      return INVALID;
    }
    return OK(input.data);
  }
  get options() {
    return this._def.values;
  }
  get enum() {
    const enumValues = {};
    for (const val of this._def.values) {
      enumValues[val] = val;
    }
    return enumValues;
  }
  get Values() {
    const enumValues = {};
    for (const val of this._def.values) {
      enumValues[val] = val;
    }
    return enumValues;
  }
  get Enum() {
    const enumValues = {};
    for (const val of this._def.values) {
      enumValues[val] = val;
    }
    return enumValues;
  }
  extract(values, newDef = this._def) {
    return _ZodEnum.create(values, {
      ...this._def,
      ...newDef
    });
  }
  exclude(values, newDef = this._def) {
    return _ZodEnum.create(this.options.filter((opt) => !values.includes(opt)), {
      ...this._def,
      ...newDef
    });
  }
};
_ZodEnum_cache = /* @__PURE__ */ new WeakMap();
ZodEnum.create = createZodEnum;
var ZodNativeEnum = class extends ZodType {
  constructor() {
    super(...arguments);
    _ZodNativeEnum_cache.set(this, void 0);
  }
  _parse(input) {
    const nativeEnumValues = util.getValidEnumValues(this._def.values);
    const ctx = this._getOrReturnCtx(input);
    if (ctx.parsedType !== ZodParsedType.string && ctx.parsedType !== ZodParsedType.number) {
      const expectedValues = util.objectValues(nativeEnumValues);
      addIssueToContext(ctx, {
        expected: util.joinValues(expectedValues),
        received: ctx.parsedType,
        code: ZodIssueCode.invalid_type
      });
      return INVALID;
    }
    if (!__classPrivateFieldGet(this, _ZodNativeEnum_cache, "f")) {
      __classPrivateFieldSet(this, _ZodNativeEnum_cache, new Set(util.getValidEnumValues(this._def.values)), "f");
    }
    if (!__classPrivateFieldGet(this, _ZodNativeEnum_cache, "f").has(input.data)) {
      const expectedValues = util.objectValues(nativeEnumValues);
      addIssueToContext(ctx, {
        received: ctx.data,
        code: ZodIssueCode.invalid_enum_value,
        options: expectedValues
      });
      return INVALID;
    }
    return OK(input.data);
  }
  get enum() {
    return this._def.values;
  }
};
_ZodNativeEnum_cache = /* @__PURE__ */ new WeakMap();
ZodNativeEnum.create = (values, params) => {
  return new ZodNativeEnum({
    values,
    typeName: ZodFirstPartyTypeKind.ZodNativeEnum,
    ...processCreateParams(params)
  });
};
var ZodPromise = class extends ZodType {
  unwrap() {
    return this._def.type;
  }
  _parse(input) {
    const { ctx } = this._processInputParams(input);
    if (ctx.parsedType !== ZodParsedType.promise && ctx.common.async === false) {
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.promise,
        received: ctx.parsedType
      });
      return INVALID;
    }
    const promisified = ctx.parsedType === ZodParsedType.promise ? ctx.data : Promise.resolve(ctx.data);
    return OK(promisified.then((data) => {
      return this._def.type.parseAsync(data, {
        path: ctx.path,
        errorMap: ctx.common.contextualErrorMap
      });
    }));
  }
};
ZodPromise.create = (schema, params) => {
  return new ZodPromise({
    type: schema,
    typeName: ZodFirstPartyTypeKind.ZodPromise,
    ...processCreateParams(params)
  });
};
var ZodEffects = class extends ZodType {
  innerType() {
    return this._def.schema;
  }
  sourceType() {
    return this._def.schema._def.typeName === ZodFirstPartyTypeKind.ZodEffects ? this._def.schema.sourceType() : this._def.schema;
  }
  _parse(input) {
    const { status, ctx } = this._processInputParams(input);
    const effect = this._def.effect || null;
    const checkCtx = {
      addIssue: (arg) => {
        addIssueToContext(ctx, arg);
        if (arg.fatal) {
          status.abort();
        } else {
          status.dirty();
        }
      },
      get path() {
        return ctx.path;
      }
    };
    checkCtx.addIssue = checkCtx.addIssue.bind(checkCtx);
    if (effect.type === "preprocess") {
      const processed = effect.transform(ctx.data, checkCtx);
      if (ctx.common.async) {
        return Promise.resolve(processed).then(async (processed2) => {
          if (status.value === "aborted")
            return INVALID;
          const result = await this._def.schema._parseAsync({
            data: processed2,
            path: ctx.path,
            parent: ctx
          });
          if (result.status === "aborted")
            return INVALID;
          if (result.status === "dirty")
            return DIRTY(result.value);
          if (status.value === "dirty")
            return DIRTY(result.value);
          return result;
        });
      } else {
        if (status.value === "aborted")
          return INVALID;
        const result = this._def.schema._parseSync({
          data: processed,
          path: ctx.path,
          parent: ctx
        });
        if (result.status === "aborted")
          return INVALID;
        if (result.status === "dirty")
          return DIRTY(result.value);
        if (status.value === "dirty")
          return DIRTY(result.value);
        return result;
      }
    }
    if (effect.type === "refinement") {
      const executeRefinement = (acc) => {
        const result = effect.refinement(acc, checkCtx);
        if (ctx.common.async) {
          return Promise.resolve(result);
        }
        if (result instanceof Promise) {
          throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");
        }
        return acc;
      };
      if (ctx.common.async === false) {
        const inner = this._def.schema._parseSync({
          data: ctx.data,
          path: ctx.path,
          parent: ctx
        });
        if (inner.status === "aborted")
          return INVALID;
        if (inner.status === "dirty")
          status.dirty();
        executeRefinement(inner.value);
        return { status: status.value, value: inner.value };
      } else {
        return this._def.schema._parseAsync({ data: ctx.data, path: ctx.path, parent: ctx }).then((inner) => {
          if (inner.status === "aborted")
            return INVALID;
          if (inner.status === "dirty")
            status.dirty();
          return executeRefinement(inner.value).then(() => {
            return { status: status.value, value: inner.value };
          });
        });
      }
    }
    if (effect.type === "transform") {
      if (ctx.common.async === false) {
        const base = this._def.schema._parseSync({
          data: ctx.data,
          path: ctx.path,
          parent: ctx
        });
        if (!isValid(base))
          return base;
        const result = effect.transform(base.value, checkCtx);
        if (result instanceof Promise) {
          throw new Error(`Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.`);
        }
        return { status: status.value, value: result };
      } else {
        return this._def.schema._parseAsync({ data: ctx.data, path: ctx.path, parent: ctx }).then((base) => {
          if (!isValid(base))
            return base;
          return Promise.resolve(effect.transform(base.value, checkCtx)).then((result) => ({ status: status.value, value: result }));
        });
      }
    }
    util.assertNever(effect);
  }
};
ZodEffects.create = (schema, effect, params) => {
  return new ZodEffects({
    schema,
    typeName: ZodFirstPartyTypeKind.ZodEffects,
    effect,
    ...processCreateParams(params)
  });
};
ZodEffects.createWithPreprocess = (preprocess, schema, params) => {
  return new ZodEffects({
    schema,
    effect: { type: "preprocess", transform: preprocess },
    typeName: ZodFirstPartyTypeKind.ZodEffects,
    ...processCreateParams(params)
  });
};
var ZodOptional = class extends ZodType {
  _parse(input) {
    const parsedType = this._getType(input);
    if (parsedType === ZodParsedType.undefined) {
      return OK(void 0);
    }
    return this._def.innerType._parse(input);
  }
  unwrap() {
    return this._def.innerType;
  }
};
ZodOptional.create = (type, params) => {
  return new ZodOptional({
    innerType: type,
    typeName: ZodFirstPartyTypeKind.ZodOptional,
    ...processCreateParams(params)
  });
};
var ZodNullable = class extends ZodType {
  _parse(input) {
    const parsedType = this._getType(input);
    if (parsedType === ZodParsedType.null) {
      return OK(null);
    }
    return this._def.innerType._parse(input);
  }
  unwrap() {
    return this._def.innerType;
  }
};
ZodNullable.create = (type, params) => {
  return new ZodNullable({
    innerType: type,
    typeName: ZodFirstPartyTypeKind.ZodNullable,
    ...processCreateParams(params)
  });
};
var ZodDefault = class extends ZodType {
  _parse(input) {
    const { ctx } = this._processInputParams(input);
    let data = ctx.data;
    if (ctx.parsedType === ZodParsedType.undefined) {
      data = this._def.defaultValue();
    }
    return this._def.innerType._parse({
      data,
      path: ctx.path,
      parent: ctx
    });
  }
  removeDefault() {
    return this._def.innerType;
  }
};
ZodDefault.create = (type, params) => {
  return new ZodDefault({
    innerType: type,
    typeName: ZodFirstPartyTypeKind.ZodDefault,
    defaultValue: typeof params.default === "function" ? params.default : () => params.default,
    ...processCreateParams(params)
  });
};
var ZodCatch = class extends ZodType {
  _parse(input) {
    const { ctx } = this._processInputParams(input);
    const newCtx = {
      ...ctx,
      common: {
        ...ctx.common,
        issues: []
      }
    };
    const result = this._def.innerType._parse({
      data: newCtx.data,
      path: newCtx.path,
      parent: {
        ...newCtx
      }
    });
    if (isAsync(result)) {
      return result.then((result2) => {
        return {
          status: "valid",
          value: result2.status === "valid" ? result2.value : this._def.catchValue({
            get error() {
              return new ZodError(newCtx.common.issues);
            },
            input: newCtx.data
          })
        };
      });
    } else {
      return {
        status: "valid",
        value: result.status === "valid" ? result.value : this._def.catchValue({
          get error() {
            return new ZodError(newCtx.common.issues);
          },
          input: newCtx.data
        })
      };
    }
  }
  removeCatch() {
    return this._def.innerType;
  }
};
ZodCatch.create = (type, params) => {
  return new ZodCatch({
    innerType: type,
    typeName: ZodFirstPartyTypeKind.ZodCatch,
    catchValue: typeof params.catch === "function" ? params.catch : () => params.catch,
    ...processCreateParams(params)
  });
};
var ZodNaN = class extends ZodType {
  _parse(input) {
    const parsedType = this._getType(input);
    if (parsedType !== ZodParsedType.nan) {
      const ctx = this._getOrReturnCtx(input);
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.nan,
        received: ctx.parsedType
      });
      return INVALID;
    }
    return { status: "valid", value: input.data };
  }
};
ZodNaN.create = (params) => {
  return new ZodNaN({
    typeName: ZodFirstPartyTypeKind.ZodNaN,
    ...processCreateParams(params)
  });
};
var BRAND = Symbol("zod_brand");
var ZodBranded = class extends ZodType {
  _parse(input) {
    const { ctx } = this._processInputParams(input);
    const data = ctx.data;
    return this._def.type._parse({
      data,
      path: ctx.path,
      parent: ctx
    });
  }
  unwrap() {
    return this._def.type;
  }
};
var ZodPipeline = class _ZodPipeline extends ZodType {
  _parse(input) {
    const { status, ctx } = this._processInputParams(input);
    if (ctx.common.async) {
      const handleAsync = async () => {
        const inResult = await this._def.in._parseAsync({
          data: ctx.data,
          path: ctx.path,
          parent: ctx
        });
        if (inResult.status === "aborted")
          return INVALID;
        if (inResult.status === "dirty") {
          status.dirty();
          return DIRTY(inResult.value);
        } else {
          return this._def.out._parseAsync({
            data: inResult.value,
            path: ctx.path,
            parent: ctx
          });
        }
      };
      return handleAsync();
    } else {
      const inResult = this._def.in._parseSync({
        data: ctx.data,
        path: ctx.path,
        parent: ctx
      });
      if (inResult.status === "aborted")
        return INVALID;
      if (inResult.status === "dirty") {
        status.dirty();
        return {
          status: "dirty",
          value: inResult.value
        };
      } else {
        return this._def.out._parseSync({
          data: inResult.value,
          path: ctx.path,
          parent: ctx
        });
      }
    }
  }
  static create(a, b) {
    return new _ZodPipeline({
      in: a,
      out: b,
      typeName: ZodFirstPartyTypeKind.ZodPipeline
    });
  }
};
var ZodReadonly = class extends ZodType {
  _parse(input) {
    const result = this._def.innerType._parse(input);
    const freeze = (data) => {
      if (isValid(data)) {
        data.value = Object.freeze(data.value);
      }
      return data;
    };
    return isAsync(result) ? result.then((data) => freeze(data)) : freeze(result);
  }
  unwrap() {
    return this._def.innerType;
  }
};
ZodReadonly.create = (type, params) => {
  return new ZodReadonly({
    innerType: type,
    typeName: ZodFirstPartyTypeKind.ZodReadonly,
    ...processCreateParams(params)
  });
};
function cleanParams(params, data) {
  const p = typeof params === "function" ? params(data) : typeof params === "string" ? { message: params } : params;
  const p2 = typeof p === "string" ? { message: p } : p;
  return p2;
}
function custom(check, _params = {}, fatal) {
  if (check)
    return ZodAny.create().superRefine((data, ctx) => {
      var _a, _b;
      const r = check(data);
      if (r instanceof Promise) {
        return r.then((r2) => {
          var _a2, _b2;
          if (!r2) {
            const params = cleanParams(_params, data);
            const _fatal = (_b2 = (_a2 = params.fatal) !== null && _a2 !== void 0 ? _a2 : fatal) !== null && _b2 !== void 0 ? _b2 : true;
            ctx.addIssue({ code: "custom", ...params, fatal: _fatal });
          }
        });
      }
      if (!r) {
        const params = cleanParams(_params, data);
        const _fatal = (_b = (_a = params.fatal) !== null && _a !== void 0 ? _a : fatal) !== null && _b !== void 0 ? _b : true;
        ctx.addIssue({ code: "custom", ...params, fatal: _fatal });
      }
      return;
    });
  return ZodAny.create();
}
var late = {
  object: ZodObject.lazycreate
};
var ZodFirstPartyTypeKind;
(function(ZodFirstPartyTypeKind2) {
  ZodFirstPartyTypeKind2["ZodString"] = "ZodString";
  ZodFirstPartyTypeKind2["ZodNumber"] = "ZodNumber";
  ZodFirstPartyTypeKind2["ZodNaN"] = "ZodNaN";
  ZodFirstPartyTypeKind2["ZodBigInt"] = "ZodBigInt";
  ZodFirstPartyTypeKind2["ZodBoolean"] = "ZodBoolean";
  ZodFirstPartyTypeKind2["ZodDate"] = "ZodDate";
  ZodFirstPartyTypeKind2["ZodSymbol"] = "ZodSymbol";
  ZodFirstPartyTypeKind2["ZodUndefined"] = "ZodUndefined";
  ZodFirstPartyTypeKind2["ZodNull"] = "ZodNull";
  ZodFirstPartyTypeKind2["ZodAny"] = "ZodAny";
  ZodFirstPartyTypeKind2["ZodUnknown"] = "ZodUnknown";
  ZodFirstPartyTypeKind2["ZodNever"] = "ZodNever";
  ZodFirstPartyTypeKind2["ZodVoid"] = "ZodVoid";
  ZodFirstPartyTypeKind2["ZodArray"] = "ZodArray";
  ZodFirstPartyTypeKind2["ZodObject"] = "ZodObject";
  ZodFirstPartyTypeKind2["ZodUnion"] = "ZodUnion";
  ZodFirstPartyTypeKind2["ZodDiscriminatedUnion"] = "ZodDiscriminatedUnion";
  ZodFirstPartyTypeKind2["ZodIntersection"] = "ZodIntersection";
  ZodFirstPartyTypeKind2["ZodTuple"] = "ZodTuple";
  ZodFirstPartyTypeKind2["ZodRecord"] = "ZodRecord";
  ZodFirstPartyTypeKind2["ZodMap"] = "ZodMap";
  ZodFirstPartyTypeKind2["ZodSet"] = "ZodSet";
  ZodFirstPartyTypeKind2["ZodFunction"] = "ZodFunction";
  ZodFirstPartyTypeKind2["ZodLazy"] = "ZodLazy";
  ZodFirstPartyTypeKind2["ZodLiteral"] = "ZodLiteral";
  ZodFirstPartyTypeKind2["ZodEnum"] = "ZodEnum";
  ZodFirstPartyTypeKind2["ZodEffects"] = "ZodEffects";
  ZodFirstPartyTypeKind2["ZodNativeEnum"] = "ZodNativeEnum";
  ZodFirstPartyTypeKind2["ZodOptional"] = "ZodOptional";
  ZodFirstPartyTypeKind2["ZodNullable"] = "ZodNullable";
  ZodFirstPartyTypeKind2["ZodDefault"] = "ZodDefault";
  ZodFirstPartyTypeKind2["ZodCatch"] = "ZodCatch";
  ZodFirstPartyTypeKind2["ZodPromise"] = "ZodPromise";
  ZodFirstPartyTypeKind2["ZodBranded"] = "ZodBranded";
  ZodFirstPartyTypeKind2["ZodPipeline"] = "ZodPipeline";
  ZodFirstPartyTypeKind2["ZodReadonly"] = "ZodReadonly";
})(ZodFirstPartyTypeKind || (ZodFirstPartyTypeKind = {}));
var instanceOfType = (cls, params = {
  message: `Input not instance of ${cls.name}`
}) => custom((data) => data instanceof cls, params);
var stringType = ZodString.create;
var numberType = ZodNumber.create;
var nanType = ZodNaN.create;
var bigIntType = ZodBigInt.create;
var booleanType = ZodBoolean.create;
var dateType = ZodDate.create;
var symbolType = ZodSymbol.create;
var undefinedType = ZodUndefined.create;
var nullType = ZodNull.create;
var anyType = ZodAny.create;
var unknownType = ZodUnknown.create;
var neverType = ZodNever.create;
var voidType = ZodVoid.create;
var arrayType = ZodArray.create;
var objectType = ZodObject.create;
var strictObjectType = ZodObject.strictCreate;
var unionType = ZodUnion.create;
var discriminatedUnionType = ZodDiscriminatedUnion.create;
var intersectionType = ZodIntersection.create;
var tupleType = ZodTuple.create;
var recordType = ZodRecord.create;
var mapType = ZodMap.create;
var setType = ZodSet.create;
var functionType = ZodFunction.create;
var lazyType = ZodLazy.create;
var literalType = ZodLiteral.create;
var enumType = ZodEnum.create;
var nativeEnumType = ZodNativeEnum.create;
var promiseType = ZodPromise.create;
var effectsType = ZodEffects.create;
var optionalType = ZodOptional.create;
var nullableType = ZodNullable.create;
var preprocessType = ZodEffects.createWithPreprocess;
var pipelineType = ZodPipeline.create;
var ostring = () => stringType().optional();
var onumber = () => numberType().optional();
var oboolean = () => booleanType().optional();
var coerce = {
  string: (arg) => ZodString.create({ ...arg, coerce: true }),
  number: (arg) => ZodNumber.create({ ...arg, coerce: true }),
  boolean: (arg) => ZodBoolean.create({
    ...arg,
    coerce: true
  }),
  bigint: (arg) => ZodBigInt.create({ ...arg, coerce: true }),
  date: (arg) => ZodDate.create({ ...arg, coerce: true })
};
var NEVER = INVALID;
var z = Object.freeze({
  __proto__: null,
  defaultErrorMap: errorMap,
  setErrorMap,
  getErrorMap,
  makeIssue,
  EMPTY_PATH,
  addIssueToContext,
  ParseStatus,
  INVALID,
  DIRTY,
  OK,
  isAborted,
  isDirty,
  isValid,
  isAsync,
  get util() {
    return util;
  },
  get objectUtil() {
    return objectUtil;
  },
  ZodParsedType,
  getParsedType,
  ZodType,
  datetimeRegex,
  ZodString,
  ZodNumber,
  ZodBigInt,
  ZodBoolean,
  ZodDate,
  ZodSymbol,
  ZodUndefined,
  ZodNull,
  ZodAny,
  ZodUnknown,
  ZodNever,
  ZodVoid,
  ZodArray,
  ZodObject,
  ZodUnion,
  ZodDiscriminatedUnion,
  ZodIntersection,
  ZodTuple,
  ZodRecord,
  ZodMap,
  ZodSet,
  ZodFunction,
  ZodLazy,
  ZodLiteral,
  ZodEnum,
  ZodNativeEnum,
  ZodPromise,
  ZodEffects,
  ZodTransformer: ZodEffects,
  ZodOptional,
  ZodNullable,
  ZodDefault,
  ZodCatch,
  ZodNaN,
  BRAND,
  ZodBranded,
  ZodPipeline,
  ZodReadonly,
  custom,
  Schema: ZodType,
  ZodSchema: ZodType,
  late,
  get ZodFirstPartyTypeKind() {
    return ZodFirstPartyTypeKind;
  },
  coerce,
  any: anyType,
  array: arrayType,
  bigint: bigIntType,
  boolean: booleanType,
  date: dateType,
  discriminatedUnion: discriminatedUnionType,
  effect: effectsType,
  "enum": enumType,
  "function": functionType,
  "instanceof": instanceOfType,
  intersection: intersectionType,
  lazy: lazyType,
  literal: literalType,
  map: mapType,
  nan: nanType,
  nativeEnum: nativeEnumType,
  never: neverType,
  "null": nullType,
  nullable: nullableType,
  number: numberType,
  object: objectType,
  oboolean,
  onumber,
  optional: optionalType,
  ostring,
  pipeline: pipelineType,
  preprocess: preprocessType,
  promise: promiseType,
  record: recordType,
  set: setType,
  strictObject: strictObjectType,
  string: stringType,
  symbol: symbolType,
  transformer: effectsType,
  tuple: tupleType,
  "undefined": undefinedType,
  union: unionType,
  unknown: unknownType,
  "void": voidType,
  NEVER,
  ZodIssueCode,
  quotelessJson,
  ZodError
});

// node_modules/@strapi/admin/dist/admin/admin/src/utils/rulesEngine.mjs
var ConditionSchema = z.object({
  dependsOn: z.string().min(1),
  operator: z.enum([
    "is",
    "isNot"
  ]),
  value: z.union([
    z.string(),
    z.number(),
    z.boolean()
  ])
});
function createRulesEngine() {
  const generate = (condition) => {
    const { dependsOn, operator, value } = condition;
    const operatorsMap = {
      is: "==",
      isNot: "!="
    };
    if (!operatorsMap[operator]) {
      throw new Error(`Invalid operator: ${operator}`);
    }
    return {
      [operatorsMap[operator]]: [
        {
          var: dependsOn
        },
        value
      ]
    };
  };
  const validate = (condition) => {
    ConditionSchema.parse(condition);
  };
  const evaluate = (condition, data) => {
    try {
      return import_json_logic_js.default.apply(condition, data);
    } catch (err) {
      throw new Error(`Invalid condition: ${err.message}`);
    }
  };
  return {
    generate,
    validate,
    evaluate
  };
}

export {
  tours,
  renderAdmin,
  require_debounce,
  require_throttle,
  DescriptionComponentRenderer,
  useInjectReducer,
  ConditionSchema,
  createRulesEngine
};
//# sourceMappingURL=chunk-DDRVMFUU.js.map
