// node_modules/@strapi/plugin-users-permissions/dist/admin/package.json.mjs
var name = "@strapi/plugin-users-permissions";
var strapi = {
  displayName: "Roles & Permissions",
  name: "users-permissions",
  description: "Protect your API with a full authentication process based on JWT. This plugin comes also with an ACL strategy that allows you to manage the permissions between the groups of users.",
  required: true,
  kind: "plugin"
};

// node_modules/@strapi/plugin-users-permissions/dist/admin/constants.mjs
var PERMISSIONS = {
  // Roles
  accessRoles: [
    {
      action: "plugin::users-permissions.roles.create",
      subject: null
    },
    {
      action: "plugin::users-permissions.roles.read",
      subject: null
    }
  ],
  createRole: [
    {
      action: "plugin::users-permissions.roles.create",
      subject: null
    }
  ],
  deleteRole: [
    {
      action: "plugin::users-permissions.roles.delete",
      subject: null
    }
  ],
  readRoles: [
    {
      action: "plugin::users-permissions.roles.read",
      subject: null
    }
  ],
  updateRole: [
    {
      action: "plugin::users-permissions.roles.update",
      subject: null
    }
  ],
  // AdvancedSettings
  readAdvancedSettings: [
    {
      action: "plugin::users-permissions.advanced-settings.read",
      subject: null
    }
  ],
  updateAdvancedSettings: [
    {
      action: "plugin::users-permissions.advanced-settings.update",
      subject: null
    }
  ],
  // Emails
  readEmailTemplates: [
    {
      action: "plugin::users-permissions.email-templates.read",
      subject: null
    }
  ],
  updateEmailTemplates: [
    {
      action: "plugin::users-permissions.email-templates.update",
      subject: null
    }
  ],
  // Providers
  readProviders: [
    {
      action: "plugin::users-permissions.providers.read",
      subject: null
    }
  ],
  updateProviders: [
    {
      action: "plugin::users-permissions.providers.update",
      subject: null
    }
  ]
};

// node_modules/@strapi/plugin-users-permissions/dist/admin/pluginId.mjs
var pluginId = name.replace(/^@strapi\/plugin-/i, "");

// node_modules/@strapi/plugin-users-permissions/dist/admin/utils/getTrad.mjs
var getTrad = (id) => `${pluginId}.${id}`;

export {
  strapi,
  PERMISSIONS,
  getTrad
};
//# sourceMappingURL=chunk-DJJSG3NG.js.map
