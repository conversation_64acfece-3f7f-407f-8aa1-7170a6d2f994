{"version": 3, "sources": ["../../../@strapi/plugin-users-permissions/dist/admin/package.json.mjs", "../../../@strapi/plugin-users-permissions/admin/src/constants.js", "../../../@strapi/plugin-users-permissions/admin/src/pluginId.js", "../../../@strapi/plugin-users-permissions/admin/src/utils/getTrad.js"], "sourcesContent": ["var name = \"@strapi/plugin-users-permissions\";\nvar strapi = {\n    displayName: \"Roles & Permissions\",\n    name: \"users-permissions\",\n    description: \"Protect your API with a full authentication process based on JWT. This plugin comes also with an ACL strategy that allows you to manage the permissions between the groups of users.\",\n    required: true,\n    kind: \"plugin\"\n};\n\nexport { name, strapi };\n//# sourceMappingURL=package.json.mjs.map\n", "export const PERMISSIONS = {\n  // Roles\n  accessRoles: [\n    { action: 'plugin::users-permissions.roles.create', subject: null },\n    { action: 'plugin::users-permissions.roles.read', subject: null },\n  ],\n  createRole: [{ action: 'plugin::users-permissions.roles.create', subject: null }],\n  deleteRole: [{ action: 'plugin::users-permissions.roles.delete', subject: null }],\n  readRoles: [{ action: 'plugin::users-permissions.roles.read', subject: null }],\n  updateRole: [{ action: 'plugin::users-permissions.roles.update', subject: null }],\n\n  // AdvancedSettings\n  readAdvancedSettings: [\n    { action: 'plugin::users-permissions.advanced-settings.read', subject: null },\n  ],\n  updateAdvancedSettings: [\n    { action: 'plugin::users-permissions.advanced-settings.update', subject: null },\n  ],\n\n  // Emails\n  readEmailTemplates: [{ action: 'plugin::users-permissions.email-templates.read', subject: null }],\n  updateEmailTemplates: [\n    { action: 'plugin::users-permissions.email-templates.update', subject: null },\n  ],\n\n  // Providers\n  readProviders: [{ action: 'plugin::users-permissions.providers.read', subject: null }],\n  updateProviders: [{ action: 'plugin::users-permissions.providers.update', subject: null }],\n};\n", "import { name } from '../../package.json';\n\nconst pluginId = name.replace(/^@strapi\\/plugin-/i, '');\n\nexport default pluginId;\n", "import pluginId from '../pluginId';\n\nconst getTrad = (id) => `${pluginId}.${id}`;\n\nexport default getTrad;\n"], "mappings": ";AAAA,IAAI,OAAO;AACX,IAAI,SAAS;AAAA,EACT,aAAa;AAAA,EACb,MAAM;AAAA,EACN,aAAa;AAAA,EACb,UAAU;AAAA,EACV,MAAM;AACV;;;ICPaA,cAAc;;EAEzBC,aAAa;IACX;MAAEC,QAAQ;MAA0CC,SAAS;IAAK;IAClE;MAAED,QAAQ;MAAwCC,SAAS;IAAK;EACjE;EACDC,YAAY;IAAC;MAAEF,QAAQ;MAA0CC,SAAS;IAAK;EAAE;EACjFE,YAAY;IAAC;MAAEH,QAAQ;MAA0CC,SAAS;IAAK;EAAE;EACjFG,WAAW;IAAC;MAAEJ,QAAQ;MAAwCC,SAAS;IAAK;EAAE;EAC9EI,YAAY;IAAC;MAAEL,QAAQ;MAA0CC,SAAS;IAAK;EAAE;;EAGjFK,sBAAsB;IACpB;MAA<PERSON>,QAAQ;MAAoDC,SAAS;IAAK;EAC7E;EACDM,wBAAwB;IACtB;MAAEP,QAAQ;MAAsDC,SAAS;IAAK;EAC/E;;EAGDO,oBAAoB;IAAC;MAAER,QAAQ;MAAkDC,SAAS;IAAK;EAAE;EACjGQ,sBAAsB;IACpB;MAAET,QAAQ;MAAoDC,SAAS;IAAK;EAC7E;;EAGDS,eAAe;IAAC;MAAEV,QAAQ;MAA4CC,SAAS;IAAK;EAAE;EACtFU,iBAAiB;IAAC;MAAEX,QAAQ;MAA8CC,SAAS;IAAK;EAAE;AAC5F;;;AC1BA,IAAMW,WAAWC,KAAKC,QAAQ,sBAAsB,EAAA;;;ACA9CC,IAAAA,UAAU,CAACC,OAAO,GAAGC,QAAS,IAAGD,EAAAA;", "names": ["PERMISSIONS", "accessRoles", "action", "subject", "createRole", "deleteRole", "readRoles", "updateRole", "readAdvancedSettings", "updateAdvancedSettings", "readEmailTemplates", "updateEmailTemplates", "readProviders", "updateProviders", "pluginId", "name", "replace", "getTrad", "id", "pluginId"]}