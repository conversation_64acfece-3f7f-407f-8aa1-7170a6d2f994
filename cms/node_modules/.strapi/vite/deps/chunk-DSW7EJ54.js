import {
  useDeleteReleaseActionMutation
} from "./chunk-7KXIIYJL.js";
import {
  reducer
} from "./chunk-OTW64HJY.js";
import {
  DEFAULT_BULK_ACTIONS,
  DEFAULT_TABLE_ROW_ACTIONS
} from "./chunk-EHR24S3E.js";
import {
  ActionsPanel,
  DEFAULT_ACTIONS,
  DEFAULT_HEADER_ACTIONS,
  INJECTION_ZONES,
  PLUGIN_ID,
  require_prism,
  routes,
  useGetPreviewUrlQuery
} from "./chunk-AGGTEVCI.js";
import {
  require_lib
} from "./chunk-LIDWYKFI.js";
import {
  prefixPluginTranslations
} from "./chunk-HIZVCZYI.js";
import {
  useTracking
} from "./chunk-USP7YHPW.js";
import {
  useForm
} from "./chunk-VWIQ6S3Y.js";
import {
  useRBAC
} from "./chunk-EYSFNMMY.js";
import {
  useAPIErrorHand<PERSON>
} from "./chunk-3UVBSCPH.js";
import {
  useAuth,
  useQueryParams
} from "./chunk-JXTOCMQW.js";
import {
  isFetchError
} from "./chunk-XU64YSEO.js";
import {
  combineReducers
} from "./chunk-IHIZNGTI.js";
import {
  useNotification
} from "./chunk-4EY5FDQG.js";
import {
  AccessibleIcon,
  Box,
  Button,
  Field,
  Flex,
  Menu,
  TooltipImpl,
  VisuallyHidden,
  useIntl
} from "./chunk-4YYUDJZ2.js";
import {
  Link,
  NavLink,
  useLocation,
  useNavigate
} from "./chunk-S65ZWNEO.js";
import {
  ForwardRef$1V,
  ForwardRef$1v,
  ForwardRef$3x,
  ForwardRef$45,
  ForwardRef$4D,
  ForwardRef$4p
} from "./chunk-QW5ZDDTU.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  dt
} from "./chunk-3CQBCJ3G.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/content-manager/dist/admin/content-manager.mjs
var ContentManagerPlugin = class {
  addEditViewSidePanel(panels) {
    if (Array.isArray(panels)) {
      this.editViewSidePanels = [
        ...this.editViewSidePanels,
        ...panels
      ];
    } else if (typeof panels === "function") {
      this.editViewSidePanels = panels(this.editViewSidePanels);
    } else {
      throw new Error(`Expected the \`panels\` passed to \`addEditViewSidePanel\` to be an array or a function, but received ${getPrintableType(panels)}`);
    }
  }
  addDocumentAction(actions) {
    if (Array.isArray(actions)) {
      this.documentActions = [
        ...this.documentActions,
        ...actions
      ];
    } else if (typeof actions === "function") {
      this.documentActions = actions(this.documentActions);
    } else {
      throw new Error(`Expected the \`actions\` passed to \`addDocumentAction\` to be an array or a function, but received ${getPrintableType(actions)}`);
    }
  }
  addDocumentHeaderAction(actions) {
    if (Array.isArray(actions)) {
      this.headerActions = [
        ...this.headerActions,
        ...actions
      ];
    } else if (typeof actions === "function") {
      this.headerActions = actions(this.headerActions);
    } else {
      throw new Error(`Expected the \`actions\` passed to \`addDocumentHeaderAction\` to be an array or a function, but received ${getPrintableType(actions)}`);
    }
  }
  addBulkAction(actions) {
    if (Array.isArray(actions)) {
      this.bulkActions = [
        ...this.bulkActions,
        ...actions
      ];
    } else if (typeof actions === "function") {
      this.bulkActions = actions(this.bulkActions);
    } else {
      throw new Error(`Expected the \`actions\` passed to \`addBulkAction\` to be an array or a function, but received ${getPrintableType(actions)}`);
    }
  }
  get config() {
    return {
      id: PLUGIN_ID,
      name: "Content Manager",
      injectionZones: INJECTION_ZONES,
      apis: {
        addBulkAction: this.addBulkAction.bind(this),
        addDocumentAction: this.addDocumentAction.bind(this),
        addDocumentHeaderAction: this.addDocumentHeaderAction.bind(this),
        addEditViewSidePanel: this.addEditViewSidePanel.bind(this),
        getBulkActions: () => this.bulkActions,
        getDocumentActions: (position) => {
          if (position) {
            return this.documentActions.filter((action) => {
              return action.position == void 0 || [
                action.position
              ].flat().includes(position);
            });
          }
          return this.documentActions;
        },
        getEditViewSidePanels: () => this.editViewSidePanels,
        getHeaderActions: () => this.headerActions
      }
    };
  }
  constructor() {
    this.bulkActions = [
      ...DEFAULT_BULK_ACTIONS
    ];
    this.documentActions = [
      ...DEFAULT_ACTIONS,
      ...DEFAULT_TABLE_ROW_ACTIONS,
      ...DEFAULT_HEADER_ACTIONS
    ];
    this.editViewSidePanels = [
      ActionsPanel
    ];
    this.headerActions = [];
  }
};
var getPrintableType = (value) => {
  const nativeType = typeof value;
  if (nativeType === "object") {
    if (value === null) return "null";
    if (Array.isArray(value)) return "array";
    if (value instanceof Object && value.constructor.name !== "Object") {
      return value.constructor.name;
    }
  }
  return nativeType;
};

// node_modules/@strapi/content-manager/dist/admin/history/components/HistoryAction.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_qs = __toESM(require_lib(), 1);
var HistoryAction = ({ model, document }) => {
  const { formatMessage } = useIntl();
  const [{ query }] = useQueryParams();
  const navigate = useNavigate();
  const { trackUsage } = useTracking();
  const { pathname } = useLocation();
  const pluginsQueryParams = (0, import_qs.stringify)({
    plugins: query.plugins
  }, {
    encode: false
  });
  if (!window.strapi.features.isEnabled("cms-content-history")) {
    return null;
  }
  const handleOnClick = () => {
    const destination = {
      pathname: "history",
      search: pluginsQueryParams
    };
    trackUsage("willNavigate", {
      from: pathname,
      to: `${pathname}/${destination.pathname}`
    });
    navigate(destination);
  };
  return {
    icon: (0, import_jsx_runtime.jsx)(ForwardRef$4p, {}),
    label: formatMessage({
      id: "content-manager.history.document-action",
      defaultMessage: "Content History"
    }),
    onClick: handleOnClick,
    disabled: (
      /**
      * The user is creating a new document.
      * It hasn't been saved yet, so there's no history to go to
      */
      !document || /**
      * The document has been created but the current dimension has never been saved.
      * For example, the user is creating a new locale in an existing document,
      * so there's no history for the document in that locale
      */
      !document.id || /**
      * History is only available for content types created by the user.
      * These have the `api::` prefix, as opposed to the ones created by Strapi or plugins,
      * which start with `admin::` or `plugin::`
      */
      !model.startsWith("api::")
    ),
    position: "header"
  };
};
HistoryAction.type = "history";
HistoryAction.position = "header";

// node_modules/@strapi/content-manager/dist/admin/history/index.mjs
var historyAdmin = {
  bootstrap(app) {
    const { addDocumentAction } = app.getPlugin("content-manager").apis;
    addDocumentAction((actions) => {
      const indexOfDeleteAction = actions.findIndex((action) => action.type === "delete");
      actions.splice(indexOfDeleteAction, 0, HistoryAction);
      return actions;
    });
  }
};

// node_modules/@strapi/content-manager/dist/admin/modules/reducers.mjs
var reducer2 = combineReducers({
  app: reducer
});

// node_modules/@strapi/content-manager/dist/admin/preview/components/PreviewSidePanel.mjs
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);
var import_qs2 = __toESM(require_lib(), 1);
var ConditionalTooltip = ({ isShown, label, children }) => {
  if (isShown) {
    return (0, import_jsx_runtime2.jsx)(TooltipImpl, {
      label,
      children
    });
  }
  return children;
};
var PreviewSidePanel = ({ model, documentId, document }) => {
  var _a;
  const { formatMessage } = useIntl();
  const { trackUsage } = useTracking();
  const { pathname } = useLocation();
  const [{ query }] = useQueryParams();
  const isModified = useForm("PreviewSidePanel", (state) => state.modified);
  const { data, error } = useGetPreviewUrlQuery({
    params: {
      contentType: model
    },
    query: {
      documentId,
      locale: document == null ? void 0 : document.locale,
      status: document == null ? void 0 : document.status
    }
  });
  if (!((_a = data == null ? void 0 : data.data) == null ? void 0 : _a.url) || error) {
    return null;
  }
  const trackNavigation = () => {
    const destinationPathname = pathname.replace(/\/$/, "") + "/preview";
    trackUsage("willNavigate", {
      from: pathname,
      to: destinationPathname
    });
  };
  return {
    title: formatMessage({
      id: "content-manager.preview.panel.title",
      defaultMessage: "Preview"
    }),
    content: (0, import_jsx_runtime2.jsx)(ConditionalTooltip, {
      label: formatMessage({
        id: "content-manager.preview.panel.button-disabled-tooltip",
        defaultMessage: "Please save to open the preview"
      }),
      isShown: isModified,
      children: (0, import_jsx_runtime2.jsx)(Box, {
        cursor: "not-allowed",
        width: "100%",
        children: (0, import_jsx_runtime2.jsx)(Button, {
          variant: "tertiary",
          tag: Link,
          to: {
            pathname: "preview",
            search: (0, import_qs2.stringify)(query, {
              encode: false
            })
          },
          onClick: trackNavigation,
          width: "100%",
          disabled: isModified,
          pointerEvents: isModified ? "none" : void 0,
          tabIndex: isModified ? -1 : void 0,
          children: formatMessage({
            id: "content-manager.preview.panel.button",
            defaultMessage: "Open preview"
          })
        })
      })
    })
  };
};

// node_modules/@strapi/content-manager/dist/admin/preview/index.mjs
var previewAdmin = {
  bootstrap(app) {
    const contentManagerPluginApis = app.getPlugin("content-manager").apis;
    contentManagerPluginApis.addEditViewSidePanel([
      PreviewSidePanel
    ]);
  }
};

// node_modules/@strapi/content-manager/dist/admin/index.mjs
var import_prismjs = __toESM(require_prism(), 1);
function __variableDynamicImportRuntime3__(path) {
  switch (path) {
    case "./translations/ar.json":
      return import("./ar.json-DHRGSQ4D.js");
    case "./translations/ca.json":
      return import("./ca.json-RG7VH7B2.js");
    case "./translations/cs.json":
      return import("./cs.json-65YXLX2Y.js");
    case "./translations/de.json":
      return import("./de.json-WJF7NUDK.js");
    case "./translations/en.json":
      return import("./en.json-SJX4EWDG.js");
    case "./translations/es.json":
      return import("./es.json-MGPVJKPS.js");
    case "./translations/eu.json":
      return import("./eu.json-4UBKVO4U.js");
    case "./translations/fr.json":
      return import("./fr.json-NP6DDXJO.js");
    case "./translations/gu.json":
      return import("./gu.json-NKEBDNO3.js");
    case "./translations/hi.json":
      return import("./hi.json-NTCR56CN.js");
    case "./translations/hu.json":
      return import("./hu.json-YWLXOZVP.js");
    case "./translations/id.json":
      return import("./id.json-ENNNDR33.js");
    case "./translations/it.json":
      return import("./it.json-ZO5NPYVA.js");
    case "./translations/ja.json":
      return import("./ja.json-SZSQAQJE.js");
    case "./translations/ko.json":
      return import("./ko.json-P4IOXW24.js");
    case "./translations/ml.json":
      return import("./ml.json-A4QAJBBK.js");
    case "./translations/ms.json":
      return import("./ms.json-6QRBGJWX.js");
    case "./translations/nl.json":
      return import("./nl.json-XKUDPGEG.js");
    case "./translations/pl.json":
      return import("./pl.json-Q57J2CY5.js");
    case "./translations/pt-BR.json":
      return import("./pt-BR.json-J7IOOBC4.js");
    case "./translations/pt.json":
      return import("./pt.json-PH5OQ5QW.js");
    case "./translations/ru.json":
      return import("./ru.json-HHZAIWVJ.js");
    case "./translations/sa.json":
      return import("./sa.json-WMMCW77G.js");
    case "./translations/sk.json":
      return import("./sk.json-A3DYL6VF.js");
    case "./translations/sv.json":
      return import("./sv.json-AZZOP2IX.js");
    case "./translations/th.json":
      return import("./th.json-TJSRYKTC.js");
    case "./translations/tr.json":
      return import("./tr.json-S2TFSAXE.js");
    case "./translations/uk.json":
      return import("./uk.json-BTXCC2JR.js");
    case "./translations/vi.json":
      return import("./vi.json-QBCPBN6B.js");
    case "./translations/zh-Hans.json":
      return import("./zh-Hans.json-3FPROLFG.js");
    case "./translations/zh.json":
      return import("./zh.json-Z5KZSIXC.js");
    default:
      return new Promise(function(resolve, reject) {
        (typeof queueMicrotask === "function" ? queueMicrotask : setTimeout)(
          reject.bind(null, new Error("Unknown variable dynamic import: " + path))
        );
      });
  }
}
var index = {
  register(app) {
    const cm = new ContentManagerPlugin();
    app.addReducers({
      [PLUGIN_ID]: reducer2
    });
    app.addMenuLink({
      to: PLUGIN_ID,
      icon: ForwardRef$3x,
      intlLabel: {
        id: `content-manager.plugin.name`,
        defaultMessage: "Content Manager"
      },
      permissions: [],
      position: 1
    });
    app.router.addRoute({
      path: "content-manager/*",
      lazy: async () => {
        const { Layout } = await import("./layout-4PWMCUUA.js");
        return {
          Component: Layout
        };
      },
      children: routes
    });
    app.registerPlugin(cm.config);
    app.widgets.register([
      {
        icon: ForwardRef$1v,
        title: {
          id: `${PLUGIN_ID}.widget.last-edited.title`,
          defaultMessage: "Last edited entries"
        },
        component: async () => {
          const { LastEditedWidget } = await import("./Widgets-VVDKPOXA.js");
          return LastEditedWidget;
        },
        pluginId: PLUGIN_ID,
        id: "last-edited-entries",
        permissions: [
          {
            action: "plugin::content-manager.explorer.read"
          }
        ]
      },
      {
        icon: ForwardRef$4D,
        title: {
          id: `${PLUGIN_ID}.widget.last-published.title`,
          defaultMessage: "Last published entries"
        },
        component: async () => {
          const { LastPublishedWidget } = await import("./Widgets-VVDKPOXA.js");
          return LastPublishedWidget;
        },
        pluginId: PLUGIN_ID,
        id: "last-published-entries",
        permissions: [
          {
            action: "plugin::content-manager.explorer.read"
          }
        ]
      }
    ]);
  },
  bootstrap(app) {
    if (typeof historyAdmin.bootstrap === "function") {
      historyAdmin.bootstrap(app);
    }
    if (typeof previewAdmin.bootstrap === "function") {
      previewAdmin.bootstrap(app);
    }
  },
  async registerTrads({ locales }) {
    const importedTrads = await Promise.all(locales.map((locale) => {
      return __variableDynamicImportRuntime3__(`./translations/${locale}.json`).then(({ default: data }) => {
        return {
          data: prefixPluginTranslations(data, PLUGIN_ID),
          locale
        };
      }).catch(() => {
        return {
          data: {},
          locale
        };
      });
    }));
    return Promise.resolve(importedTrads);
  }
};

// node_modules/@strapi/content-releases/dist/admin/constants.mjs
var PERMISSIONS = {
  main: [
    {
      action: "plugin::content-releases.read",
      subject: null,
      id: "",
      actionParameters: {},
      properties: {},
      conditions: []
    }
  ],
  create: [
    {
      action: "plugin::content-releases.create",
      subject: null,
      id: "",
      actionParameters: {},
      properties: {},
      conditions: []
    }
  ],
  update: [
    {
      action: "plugin::content-releases.update",
      subject: null,
      id: "",
      actionParameters: {},
      properties: {},
      conditions: []
    }
  ],
  delete: [
    {
      action: "plugin::content-releases.delete",
      subject: null,
      id: "",
      actionParameters: {},
      properties: {},
      conditions: []
    }
  ],
  createAction: [
    {
      action: "plugin::content-releases.create-action",
      subject: null,
      id: "",
      actionParameters: {},
      properties: {},
      conditions: []
    }
  ],
  deleteAction: [
    {
      action: "plugin::content-releases.delete-action",
      subject: null,
      id: "",
      actionParameters: {},
      properties: {},
      conditions: []
    }
  ],
  publish: [
    {
      action: "plugin::content-releases.publish",
      subject: null,
      id: "",
      actionParameters: {},
      properties: {},
      conditions: []
    }
  ]
};

// node_modules/@strapi/content-releases/dist/admin/components/ReleaseActionOptions.mjs
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_react2 = __toESM(require_react(), 1);
var getBorderLeftRadiusValue = (actionType) => {
  return actionType === "publish" ? 1 : 0;
};
var getBorderRightRadiusValue = (actionType) => {
  return actionType === "publish" ? 0 : 1;
};
var FieldWrapper = dt(Field.Root)`
  border-top-left-radius: ${({ $actionType, theme }) => theme.spaces[getBorderLeftRadiusValue($actionType)]};
  border-bottom-left-radius: ${({ $actionType, theme }) => theme.spaces[getBorderLeftRadiusValue($actionType)]};
  border-top-right-radius: ${({ $actionType, theme }) => theme.spaces[getBorderRightRadiusValue($actionType)]};
  border-bottom-right-radius: ${({ $actionType, theme }) => theme.spaces[getBorderRightRadiusValue($actionType)]};

  > label {
    color: inherit;
    padding: ${({ theme }) => `${theme.spaces[2]} ${theme.spaces[3]}`};
    text-align: center;
    vertical-align: middle;
    text-transform: capitalize;
  }

  &[data-checked='true'] {
    color: ${({ theme, $actionType }) => $actionType === "publish" ? theme.colors.primary700 : theme.colors.danger600};
    background-color: ${({ theme, $actionType }) => $actionType === "publish" ? theme.colors.primary100 : theme.colors.danger100};
    border-color: ${({ theme, $actionType }) => $actionType === "publish" ? theme.colors.primary700 : theme.colors.danger600};
  }

  &[data-checked='false'] {
    border-left: ${({ $actionType }) => $actionType === "unpublish" && "none"};
    border-right: ${({ $actionType }) => $actionType === "publish" && "none"};
  }

  &[data-checked='false'][data-disabled='false']:hover {
    color: ${({ theme }) => theme.colors.neutral700};
    background-color: ${({ theme }) => theme.colors.neutral100};
    border-color: ${({ theme }) => theme.colors.neutral200};

    & > label {
      cursor: pointer;
    }
  }

  &[data-disabled='true'] {
    color: ${({ theme }) => theme.colors.neutral600};
    background-color: ${({ theme }) => theme.colors.neutral150};
    border-color: ${({ theme }) => theme.colors.neutral300};
  }
`;
var ActionOption = ({ selected, actionType, handleChange, name, disabled = false }) => {
  return (0, import_jsx_runtime3.jsx)(FieldWrapper, {
    $actionType: actionType,
    background: "primary0",
    borderColor: "neutral200",
    color: selected === actionType ? "primary600" : "neutral600",
    position: "relative",
    cursor: "pointer",
    "data-checked": selected === actionType,
    "data-disabled": disabled && selected !== actionType,
    children: (0, import_jsx_runtime3.jsxs)(Field.Label, {
      children: [
        (0, import_jsx_runtime3.jsx)(VisuallyHidden, {
          children: (0, import_jsx_runtime3.jsx)(Field.Input, {
            type: "radio",
            name,
            checked: selected === actionType,
            onChange: handleChange,
            value: actionType,
            disabled
          })
        }),
        actionType
      ]
    })
  });
};
var ReleaseActionOptions = ({ selected, handleChange, name, disabled = false }) => {
  return (0, import_jsx_runtime3.jsxs)(Flex, {
    children: [
      (0, import_jsx_runtime3.jsx)(ActionOption, {
        actionType: "publish",
        selected,
        handleChange,
        name,
        disabled
      }),
      (0, import_jsx_runtime3.jsx)(ActionOption, {
        actionType: "unpublish",
        selected,
        handleChange,
        name,
        disabled
      })
    ]
  });
};

// node_modules/@strapi/content-releases/dist/admin/components/ReleaseActionMenu.mjs
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var StyledMenuLink = dt(Menu.Item)`
  span,
  &:hover span {
    color: ${({ theme }) => theme.colors["neutral800"]};
  }

  svg path,
  &:hover svg path {
    fill: ${({ theme }) => theme.colors["neutral500"]};
  }
`;
var DeleteReleaseActionItem = ({ releaseId, actionId }) => {
  const { formatMessage } = useIntl();
  const { toggleNotification } = useNotification();
  const { formatAPIError } = useAPIErrorHandler();
  const [deleteReleaseAction] = useDeleteReleaseActionMutation();
  const { allowedActions: { canDeleteAction } } = useRBAC(PERMISSIONS);
  const handleDeleteAction = async () => {
    const response = await deleteReleaseAction({
      params: {
        releaseId,
        actionId
      }
    });
    if ("data" in response) {
      toggleNotification({
        type: "success",
        message: formatMessage({
          id: "content-releases.content-manager-edit-view.remove-from-release.notification.success",
          defaultMessage: "Entry removed from release"
        })
      });
      return;
    }
    if ("error" in response) {
      if (isFetchError(response.error)) {
        toggleNotification({
          type: "danger",
          message: formatAPIError(response.error)
        });
      } else {
        toggleNotification({
          type: "danger",
          message: formatMessage({
            id: "notification.error",
            defaultMessage: "An error occurred"
          })
        });
      }
    }
  };
  if (!canDeleteAction) {
    return null;
  }
  return (0, import_jsx_runtime4.jsx)(Menu.Item, {
    variant: "danger",
    startIcon: (0, import_jsx_runtime4.jsx)(ForwardRef$45, {}),
    onSelect: handleDeleteAction,
    children: formatMessage({
      id: "content-releases.content-manager-edit-view.remove-from-release",
      defaultMessage: "Remove from release"
    })
  });
};
var ReleaseActionEntryLinkItem = ({ contentTypeUid, documentId, locale }) => {
  const { formatMessage } = useIntl();
  const userPermissions = useAuth("ReleaseActionEntryLinkItem", (state) => state.permissions);
  const canUpdateEntryForLocale = React.useMemo(() => {
    var _a, _b;
    const updatePermissions = userPermissions.find((permission) => permission.subject === contentTypeUid && permission.action === "plugin::content-manager.explorer.update");
    if (!updatePermissions) {
      return false;
    }
    return Boolean(!locale || ((_b = (_a = updatePermissions.properties) == null ? void 0 : _a.locales) == null ? void 0 : _b.includes(locale)));
  }, [
    contentTypeUid,
    locale,
    userPermissions
  ]);
  const { allowedActions: { canUpdate: canUpdateContentType } } = useRBAC({
    updateContentType: [
      {
        action: "plugin::content-manager.explorer.update",
        subject: contentTypeUid
      }
    ]
  });
  if (!canUpdateContentType || !canUpdateEntryForLocale) {
    return null;
  }
  return (0, import_jsx_runtime4.jsx)(StyledMenuLink, {
    /* @ts-expect-error inference isn't working in DS */
    tag: NavLink,
    isLink: true,
    to: {
      pathname: `/content-manager/collection-types/${contentTypeUid}/${documentId}`,
      search: locale && `?plugins[i18n][locale]=${locale}`
    },
    startIcon: (0, import_jsx_runtime4.jsx)(ForwardRef$1v, {}),
    children: formatMessage({
      id: "content-releases.content-manager-edit-view.edit-entry",
      defaultMessage: "Edit entry"
    })
  });
};
var EditReleaseItem = ({ releaseId }) => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime4.jsx)(StyledMenuLink, {
    /* @ts-expect-error inference isn't working in DS */
    tag: NavLink,
    isLink: true,
    to: `/plugins/content-releases/${releaseId}`,
    startIcon: (0, import_jsx_runtime4.jsx)(ForwardRef$1v, {}),
    children: formatMessage({
      id: "content-releases.content-manager-edit-view.edit-release",
      defaultMessage: "Edit release"
    })
  });
};
var Root = ({ children }) => {
  const { formatMessage } = useIntl();
  const { allowedActions } = useRBAC(PERMISSIONS);
  return (
    // A user can access the dropdown if they have permissions to delete a release-action OR update a release
    allowedActions.canDeleteAction || allowedActions.canUpdate ? (0, import_jsx_runtime4.jsxs)(Menu.Root, {
      children: [
        (0, import_jsx_runtime4.jsx)(StyledMoreButton, {
          variant: "tertiary",
          endIcon: null,
          paddingLeft: "7px",
          paddingRight: "7px",
          children: (0, import_jsx_runtime4.jsx)(AccessibleIcon, {
            label: formatMessage({
              id: "content-releases.content-manager-edit-view.release-action-menu",
              defaultMessage: "Release action options"
            }),
            children: (0, import_jsx_runtime4.jsx)(ForwardRef$1V, {})
          })
        }),
        (0, import_jsx_runtime4.jsx)(Menu.Content, {
          top: 1,
          popoverPlacement: "bottom-end",
          children
        })
      ]
    }) : null
  );
};
var StyledMoreButton = dt(Menu.Trigger)`
  & > span {
    display: flex;
  }
`;
var ReleaseActionMenu = {
  Root,
  EditReleaseItem,
  DeleteReleaseActionItem,
  ReleaseActionEntryLinkItem
};

// node_modules/@strapi/content-releases/dist/admin/pluginId.mjs
var pluginId = "content-releases";

export {
  index,
  PERMISSIONS,
  ReleaseActionOptions,
  ReleaseActionMenu,
  pluginId
};
//# sourceMappingURL=chunk-DSW7EJ54.js.map
