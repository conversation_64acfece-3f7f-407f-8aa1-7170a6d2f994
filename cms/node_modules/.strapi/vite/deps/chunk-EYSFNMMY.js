import {
  capitalise
} from "./chunk-PQINNV4N.js";
import {
  require_isEqual
} from "./chunk-VYSYYPOB.js";
import {
  useAuth
} from "./chunk-JXTOCMQW.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/admin/src/hooks/useRBAC.mjs
var React = __toESM(require_react(), 1);
var import_isEqual = __toESM(require_isEqual(), 1);

// node_modules/@strapi/admin/dist/admin/admin/src/utils/once.mjs
var once = (fn) => {
  const func = fn;
  let called = false;
  if (typeof func !== "function") {
    throw new TypeError(`once requires a function parameter`);
  }
  return (...args) => {
    if (!called && true) {
      func(...args);
      called = true;
    }
  };
};

// node_modules/@strapi/admin/dist/admin/admin/src/hooks/usePrev.mjs
var import_react = __toESM(require_react(), 1);
var usePrev = (value) => {
  const ref = (0, import_react.useRef)();
  (0, import_react.useEffect)(() => {
    ref.current = value;
  }, [
    value
  ]);
  return ref.current;
};

// node_modules/@strapi/admin/dist/admin/admin/src/hooks/useRBAC.mjs
var useRBAC = (permissionsToCheck = [], passedPermissions, rawQueryContext) => {
  const isLoadingAuth = useAuth("useRBAC", (state) => state.isLoading);
  const [isLoading, setIsLoading] = React.useState(true);
  const [error, setError] = React.useState();
  const [data, setData] = React.useState();
  const warnOnce = React.useMemo(() => once(console.warn), []);
  const actualPermissionsToCheck = React.useMemo(() => {
    if (Array.isArray(permissionsToCheck)) {
      return permissionsToCheck;
    } else {
      warnOnce("useRBAC: The first argument should be an array of permissions, not an object. This will be deprecated in the future.");
      return Object.values(permissionsToCheck).flat();
    }
  }, [
    permissionsToCheck,
    warnOnce
  ]);
  const defaultAllowedActions = React.useMemo(() => {
    return actualPermissionsToCheck.reduce((acc, permission) => {
      return {
        ...acc,
        [getActionName(permission)]: false
      };
    }, {});
  }, [
    actualPermissionsToCheck
  ]);
  const checkUserHasPermissions = useAuth("useRBAC", (state) => state.checkUserHasPermissions);
  const permssionsChecked = usePrev(actualPermissionsToCheck);
  const contextChecked = usePrev(rawQueryContext);
  React.useEffect(() => {
    if (!(0, import_isEqual.default)(permssionsChecked, actualPermissionsToCheck) || // TODO: also run this when the query context changes
    contextChecked !== rawQueryContext) {
      setIsLoading(true);
      setData(void 0);
      setError(void 0);
      checkUserHasPermissions(actualPermissionsToCheck, passedPermissions, rawQueryContext).then((res) => {
        if (res) {
          setData(res.reduce((acc, permission) => {
            return {
              ...acc,
              [getActionName(permission)]: true
            };
          }, {}));
        }
      }).catch((err) => {
        setError(err);
      }).finally(() => {
        setIsLoading(false);
      });
    }
  }, [
    actualPermissionsToCheck,
    checkUserHasPermissions,
    passedPermissions,
    permissionsToCheck,
    permssionsChecked,
    contextChecked,
    rawQueryContext
  ]);
  const allowedActions = Object.entries({
    ...defaultAllowedActions,
    ...data
  }).reduce((acc, [name, allowed]) => {
    acc[`can${capitalise(name)}`] = allowed;
    return acc;
  }, {});
  return {
    allowedActions,
    permissions: actualPermissionsToCheck,
    isLoading: isLoading || isLoadingAuth,
    error
  };
};
var getActionName = (permission) => {
  const [action = ""] = permission.action.split(".").slice(-1);
  return action.split("-").map(capitalise).join("");
};

export {
  useRBAC
};
//# sourceMappingURL=chunk-EYSFNMMY.js.map
