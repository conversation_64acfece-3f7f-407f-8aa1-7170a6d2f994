{"version": 3, "sources": ["../../../@strapi/admin/admin/src/components/Pagination.tsx"], "sourcesContent": ["/* eslint-disable import/export */\nimport * as React from 'react';\n\nimport {\n  Flex,\n  SingleSelectOption,\n  SingleSelect,\n  Typography,\n  Dots,\n  NextLink,\n  PageLink,\n  Pagination as PaginationImpl,\n  PreviousLink,\n} from '@strapi/design-system';\nimport { stringify } from 'qs';\nimport { useIntl } from 'react-intl';\nimport { Link } from 'react-router-dom';\n\nimport { useQueryParams } from '../hooks/useQueryParams';\n\nimport { createContext } from './Context';\n\nimport type { Pagination as PaginationApi } from '../../../shared/contracts/shared';\n\n/* -------------------------------------------------------------------------------------------------\n * Root\n * -----------------------------------------------------------------------------------------------*/\ninterface PaginationContextValue {\n  /**\n   * @description the complete query object, this could include query params\n   * injected by other plugins, if you're navigating to a different page you\n   * should ensure these are still passed.\n   */\n  currentQuery?: object;\n  pageCount: string;\n  pageSize: string;\n  page: string;\n  setPageSize: (pageSize: string) => void;\n  total: NonNullable<RootProps['total']>;\n}\n\nconst [PaginationProvider, usePagination] = createContext<PaginationContextValue>('Pagination');\n\ninterface RootProps {\n  children: React.ReactNode;\n  /**\n   * @default 0\n   * @description the total number of pages\n   * that exist in the dataset.\n   */\n  pageCount?: PaginationApi['pageCount'];\n  /**\n   * @default 1\n   * @description the initial page number.\n   */\n  defaultPage?: PaginationApi['page'];\n  /**\n   * @default 10\n   * @description the initial number of items to display\n   */\n  defaultPageSize?: PaginationApi['pageSize'];\n  /**\n   * @description a callback that is called when the page size changes.\n   */\n  onPageSizeChange?: (pageSize: string) => void;\n  /**\n   * @default 0\n   * @description the total number of items in the dataset.\n   */\n  total?: PaginationApi['total'];\n}\n\n/**\n * @description The root component for the composable pagination component.\n * It's advised to spread the entire pagination option object into this component.\n *\n * @example\n * ```tsx\n * const MyComponent = () => {\n *  return (\n *    <Pagination.Root {...response.pagination}>\n *      <Pagination.PageSize />\n *      <Pagination.Links />\n *    </Pagination.Root>\n *  );\n * };\n * ```\n */\nconst Root = React.forwardRef<HTMLDivElement, RootProps>(\n  (\n    { children, defaultPageSize = 10, pageCount = 0, defaultPage = 1, onPageSizeChange, total = 0 },\n    forwardedRef\n  ) => {\n    const [{ query }, setQuery] = useQueryParams<Pick<PaginationContextValue, 'page' | 'pageSize'>>(\n      {\n        pageSize: defaultPageSize.toString(),\n        page: defaultPage.toString(),\n      }\n    );\n\n    const setPageSize = (pageSize: string) => {\n      setQuery({ pageSize, page: '1' });\n\n      if (onPageSizeChange) {\n        onPageSizeChange(pageSize);\n      }\n    };\n\n    return (\n      <Flex\n        ref={forwardedRef}\n        paddingTop={4}\n        paddingBottom={4}\n        alignItems=\"flex-end\"\n        justifyContent=\"space-between\"\n      >\n        <PaginationProvider\n          currentQuery={query}\n          page={query.page}\n          pageSize={query.pageSize}\n          pageCount={pageCount.toString()}\n          setPageSize={setPageSize}\n          total={total}\n        >\n          {children}\n        </PaginationProvider>\n      </Flex>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * PageSize\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * @description The page size component is responsible for rendering the select input that allows\n * the user to change the number of items displayed per page.\n * If the total number of items is less than the minimum option, this component will not render.\n */\nconst PageSize = ({ options = ['10', '20', '50', '100'] }: Pagination.PageSizeProps) => {\n  const { formatMessage } = useIntl();\n\n  const pageSize = usePagination('PageSize', (state) => state.pageSize);\n  const totalCount = usePagination('PageSize', (state) => state.total);\n  const setPageSize = usePagination('PageSize', (state) => state.setPageSize);\n\n  const handleChange = (value: string) => {\n    setPageSize(value);\n  };\n\n  const minimumOption = parseInt(options[0], 10);\n\n  if (minimumOption >= totalCount) {\n    return null;\n  }\n\n  return (\n    <Flex gap={2}>\n      <SingleSelect\n        size=\"S\"\n        aria-label={formatMessage({\n          id: 'components.PageFooter.select',\n          defaultMessage: 'Entries per page',\n        })}\n        // @ts-expect-error from the DS V2 this won't be needed because we're only returning strings.\n        onChange={handleChange}\n        value={pageSize}\n      >\n        {options.map((option) => (\n          <SingleSelectOption key={option} value={option}>\n            {option}\n          </SingleSelectOption>\n        ))}\n      </SingleSelect>\n      <Typography textColor=\"neutral600\" tag=\"span\">\n        {formatMessage({\n          id: 'components.PageFooter.select',\n          defaultMessage: 'Entries per page',\n        })}\n      </Typography>\n    </Flex>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Links\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * The component works as follows\n * `1` , 2, 3, ... 10\n * 1, `2`, 3, ... 10\n * 1, 2, `3`, 4, ... 10\n * 1, 2, 3, `4`, 5, ... 10\n * 1, ..,4, `5`, 6, ... 10\n *\n * 1, ...., 8, 9, `10`\n * 1, ...., 8, `9`, 10\n * 1, ...., 7, `8`, 9, 10\n * 1, ... 6, `7`, 8, 9, 10\n */\n\n/**\n * @description The links component is responsible for rendering the pagination links.\n * If the total number of pages is less than or equal to 1, this component will not render.\n */\nconst Links = ({ boundaryCount = 1, siblingCount = 1 }: Pagination.LinksProps) => {\n  const { formatMessage } = useIntl();\n\n  const query = usePagination('Links', (state) => state.currentQuery);\n  const currentPage = usePagination('Links', (state) => state.page);\n  const totalPages = usePagination('Links', (state) => state.pageCount);\n\n  const pageCount = parseInt(totalPages, 10);\n  const activePage = parseInt(currentPage, 10);\n\n  const range = (start: number, end: number) => {\n    const length = end - start + 1;\n\n    return Array.from({ length }, (_, i) => start + i);\n  };\n\n  const startPages = range(1, Math.min(boundaryCount, pageCount));\n  const endPages = range(Math.max(pageCount - boundaryCount + 1, boundaryCount + 1), pageCount);\n\n  const siblingsStart = Math.max(\n    Math.min(\n      // Natural start\n      activePage - siblingCount,\n      // Lower boundary when page is high\n      pageCount - boundaryCount - siblingCount * 2 - 1\n    ),\n    // Greater than startPages\n    boundaryCount + 2\n  );\n\n  const siblingsEnd = Math.min(\n    Math.max(\n      // Natural end\n      activePage + siblingCount,\n      // Upper boundary when page is low\n      boundaryCount + siblingCount * 2 + 2\n    ),\n    // Less than endPages\n    endPages.length > 0 ? endPages[0] - 2 : pageCount - 1\n  );\n\n  const items = [\n    ...startPages,\n\n    // Start ellipsis\n    // eslint-disable-next-line no-nested-ternary\n    ...(siblingsStart > boundaryCount + 2\n      ? ['start-ellipsis']\n      : boundaryCount + 1 < pageCount - boundaryCount\n        ? [boundaryCount + 1]\n        : []),\n\n    // Sibling pages\n    ...range(siblingsStart, siblingsEnd),\n\n    // End ellipsis\n    // eslint-disable-next-line no-nested-ternary\n    ...(siblingsEnd < pageCount - boundaryCount - 1\n      ? ['end-ellipsis']\n      : pageCount - boundaryCount > boundaryCount\n        ? [pageCount - boundaryCount]\n        : []),\n\n    ...endPages,\n  ];\n\n  if (pageCount <= 1) {\n    return null;\n  }\n\n  return (\n    <PaginationImpl activePage={activePage} pageCount={pageCount}>\n      <PreviousLink tag={Link} to={{ search: stringify({ ...query, page: activePage - 1 }) }}>\n        {formatMessage({\n          id: 'components.pagination.go-to-previous',\n          defaultMessage: 'Go to previous page',\n        })}\n      </PreviousLink>\n      {items.map((item) => {\n        if (typeof item === 'number') {\n          return (\n            <PageLink\n              tag={Link}\n              key={item}\n              number={item}\n              to={{ search: stringify({ ...query, page: item }) }}\n            >\n              {formatMessage(\n                { id: 'components.pagination.go-to', defaultMessage: 'Go to page {page}' },\n                { page: item }\n              )}\n            </PageLink>\n          );\n        }\n\n        return <Dots key={item} />;\n      })}\n\n      <NextLink tag={Link} to={{ search: stringify({ ...query, page: activePage + 1 }) }}>\n        {formatMessage({\n          id: 'components.pagination.go-to-next',\n          defaultMessage: 'Go to next page',\n        })}\n      </NextLink>\n    </PaginationImpl>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * EXPORTS\n * -----------------------------------------------------------------------------------------------*/\n\nconst Pagination = {\n  Root,\n  Links,\n  PageSize,\n};\n\n// eslint-disable-next-line @typescript-eslint/no-namespace\nnamespace Pagination {\n  export interface Props extends RootProps {}\n\n  export interface PageSizeProps {\n    options?: string[];\n  }\n\n  export interface LinksProps {\n    /**\n     * @default 1\n     * @description Number of always visible pages at the beginning and end.\n     */\n    boundaryCount?: number;\n    /**\n     * @default 1\n     * @description Number of always visible pages before and after the current page.\n     */\n    siblingCount?: number;\n  }\n}\n\nexport { Pagination };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyCA,IAAM,CAACA,oBAAoBC,aAAc,IAAGC,cAAsC,YAAA;AA+ClF,IAAMC,OAAaC,iBACjB,CACE,EAAEC,UAAUC,kBAAkB,IAAIC,YAAY,GAAGC,cAAc,GAAGC,kBAAkBC,QAAQ,EAAC,GAC7FC,iBAAAA;AAEA,QAAM,CAAC,EAAEC,MAAK,GAAIC,QAAAA,IAAYC,eAC5B;IACEC,UAAUT,gBAAgBU,SAAQ;IAClCC,MAAMT,YAAYQ,SAAQ;EAC5B,CAAA;AAGF,QAAME,cAAc,CAACH,aAAAA;AACnBF,aAAS;MAAEE;MAAUE,MAAM;IAAI,CAAA;AAE/B,QAAIR,kBAAkB;AACpBA,uBAAiBM,QAAAA;IACnB;EACF;AAEA,aACEI,wBAACC,MAAAA;IACCC,KAAKV;IACLW,YAAY;IACZC,eAAe;IACfC,YAAW;IACXC,gBAAe;IAEf,cAAAN,wBAACnB,oBAAAA;MACC0B,cAAcd;MACdK,MAAML,MAAMK;MACZF,UAAUH,MAAMG;MAChBR,WAAWA,UAAUS,SAAQ;MAC7BE;MACAR;MAECL;;;AAIT,CAAA;AAYF,IAAMsB,WAAW,CAAC,EAAEC,UAAU;EAAC;EAAM;EAAM;EAAM;EAAM,MAA4B;AACjF,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,QAAMf,WAAWd,cAAc,YAAY,CAAC8B,UAAUA,MAAMhB,QAAQ;AACpE,QAAMiB,aAAa/B,cAAc,YAAY,CAAC8B,UAAUA,MAAMrB,KAAK;AACnE,QAAMQ,cAAcjB,cAAc,YAAY,CAAC8B,UAAUA,MAAMb,WAAW;AAE1E,QAAMe,eAAe,CAACC,UAAAA;AACpBhB,gBAAYgB,KAAAA;EACd;AAEA,QAAMC,gBAAgBC,SAASR,QAAQ,CAAA,GAAI,EAAA;AAE3C,MAAIO,iBAAiBH,YAAY;AAC/B,WAAO;EACT;AAEA,aACEK,yBAACjB,MAAAA;IAAKkB,KAAK;;UACTnB,wBAACoB,cAAAA;QACCC,MAAK;QACLC,cAAYZ,cAAc;UACxBa,IAAI;UACJC,gBAAgB;QAClB,CAAA;;QAEAC,UAAUX;QACVC,OAAOnB;QAENa,UAAAA,QAAQiB,IAAI,CAACC,eACZ3B,wBAAC4B,oBAAAA;UAAgCb,OAAOY;UACrCA,UAAAA;QADsBA,GAAAA,MAAAA,CAAAA;;UAK7B3B,wBAAC6B,YAAAA;QAAWC,WAAU;QAAaC,KAAI;kBACpCrB,cAAc;UACba,IAAI;UACJC,gBAAgB;QAClB,CAAA;;;;AAIR;AAwBA,IAAMQ,QAAQ,CAAC,EAAEC,gBAAgB,GAAGC,eAAe,EAAC,MAAyB;AAC3E,QAAM,EAAExB,cAAa,IAAKC,QAAAA;AAE1B,QAAMlB,QAAQX,cAAc,SAAS,CAAC8B,UAAUA,MAAML,YAAY;AAClE,QAAM4B,cAAcrD,cAAc,SAAS,CAAC8B,UAAUA,MAAMd,IAAI;AAChE,QAAMsC,aAAatD,cAAc,SAAS,CAAC8B,UAAUA,MAAMxB,SAAS;AAEpE,QAAMA,YAAY6B,SAASmB,YAAY,EAAA;AACvC,QAAMC,aAAapB,SAASkB,aAAa,EAAA;AAEzC,QAAMG,QAAQ,CAACC,OAAeC,QAAAA;AAC5B,UAAMC,SAASD,MAAMD,QAAQ;AAE7B,WAAOG,MAAMC,KAAK;MAAEF;OAAU,CAACG,GAAGC,MAAMN,QAAQM,CAAAA;EAClD;AAEA,QAAMC,aAAaR,MAAM,GAAGS,KAAKC,IAAIf,eAAe7C,SAAAA,CAAAA;AACpD,QAAM6D,WAAWX,MAAMS,KAAKG,IAAI9D,YAAY6C,gBAAgB,GAAGA,gBAAgB,CAAI7C,GAAAA,SAAAA;AAEnF,QAAM+D,gBAAgBJ,KAAKG;IACzBH,KAAKC;;MAEHX,aAAaH;;MAEb9C,YAAY6C,gBAAgBC,eAAe,IAAI;IAAA;;IAGjDD,gBAAgB;EAAA;AAGlB,QAAMmB,cAAcL,KAAKC;IACvBD,KAAKG;;MAEHb,aAAaH;;MAEbD,gBAAgBC,eAAe,IAAI;IAAA;;IAGrCe,SAASR,SAAS,IAAIQ,SAAS,CAAE,IAAG,IAAI7D,YAAY;EAAA;AAGtD,QAAMiE,QAAQ;IACTP,GAAAA;;;IAICK,GAAAA,gBAAgBlB,gBAAgB,IAChC;MAAC;QACDA,gBAAgB,IAAI7C,YAAY6C,gBAC9B;MAACA,gBAAgB;IAAE,IACnB,CAAA;;IAGHK,GAAAA,MAAMa,eAAeC,WAAAA;;;OAIpBA,cAAchE,YAAY6C,gBAAgB,IAC1C;MAAC;QACD7C,YAAY6C,gBAAgBA,gBAC1B;MAAC7C,YAAY6C;IAAc,IAC3B,CAAA;IAEHgB,GAAAA;EACJ;AAED,MAAI7D,aAAa,GAAG;AAClB,WAAO;EACT;AAEA,aACE8B,yBAACoC,YAAAA;IAAejB;IAAwBjD;;UACtCY,wBAACuD,cAAAA;QAAaxB,KAAKyB;QAAMC,IAAI;UAAEC,YAAQC,qBAAU;YAAE,GAAGlE;YAAOK,MAAMuC,aAAa;UAAE,CAAA;QAAG;kBAClF3B,cAAc;UACba,IAAI;UACJC,gBAAgB;QAClB,CAAA;;MAED6B,MAAM3B,IAAI,CAACkC,SAAAA;AACV,YAAI,OAAOA,SAAS,UAAU;AAC5B,qBACE5D,wBAAC6D,UAAAA;YACC9B,KAAKyB;YAELM,QAAQF;YACRH,IAAI;cAAEC,YAAQC,qBAAU;gBAAE,GAAGlE;gBAAOK,MAAM8D;cAAK,CAAA;YAAG;sBAEjDlD,cACC;cAAEa,IAAI;cAA+BC,gBAAgB;eACrD;cAAE1B,MAAM8D;YAAK,CAAA;UANVA,GAAAA,IAAAA;QAUX;AAEA,mBAAO5D,wBAAC+D,MAAUH,CAAAA,GAAAA,IAAAA;MACpB,CAAA;UAEA5D,wBAACgE,UAAAA;QAASjC,KAAKyB;QAAMC,IAAI;UAAEC,YAAQC,qBAAU;YAAE,GAAGlE;YAAOK,MAAMuC,aAAa;UAAE,CAAA;QAAG;kBAC9E3B,cAAc;UACba,IAAI;UACJC,gBAAgB;QAClB,CAAA;;;;AAIR;AAIkG,IAE5FyC,cAAa;EACjBjF;EACAgD;EACAxB;AACF;", "names": ["PaginationProvider", "usePagination", "createContext", "Root", "forwardRef", "children", "defaultPageSize", "pageCount", "defaultPage", "onPageSizeChange", "total", "forwardedRef", "query", "<PERSON><PERSON><PERSON><PERSON>", "useQueryParams", "pageSize", "toString", "page", "setPageSize", "_jsx", "Flex", "ref", "paddingTop", "paddingBottom", "alignItems", "justifyContent", "<PERSON><PERSON><PERSON><PERSON>", "PageSize", "options", "formatMessage", "useIntl", "state", "totalCount", "handleChange", "value", "minimumOption", "parseInt", "_jsxs", "gap", "SingleSelect", "size", "aria-label", "id", "defaultMessage", "onChange", "map", "option", "SingleSelectOption", "Typography", "textColor", "tag", "Links", "boundaryCount", "siblingCount", "currentPage", "totalPages", "activePage", "range", "start", "end", "length", "Array", "from", "_", "i", "startPages", "Math", "min", "endPages", "max", "siblingsStart", "siblingsEnd", "items", "PaginationImpl", "PreviousLink", "Link", "to", "search", "stringify", "item", "PageLink", "number", "Dots", "NextLink", "Pagination"]}