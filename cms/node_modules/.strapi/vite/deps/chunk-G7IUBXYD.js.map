{"version": 3, "sources": ["../../../@strapi/admin/admin/src/hooks/useFetchClient.ts"], "sourcesContent": ["import * as React from 'react';\n\nimport { getFetchClient } from '../utils/getFetchClient';\n\n/**\n * @public\n * @description This is an abstraction around the native fetch exposed by a hook. It provides a simple interface to handle API calls\n * to the Strapi backend.\n * It handles request cancellations inside the hook with an {@link https://developer.mozilla.org/en-US/docs/Web/API/AbortController} AbortController.\n * This is typically triggered when the component is unmounted so all the requests that it is currently making are aborted.\n * The expected URL style includes either a protocol (such as HTTP or HTTPS) or a relative URL. The URLs with domain and path but not protocol are not allowed (ex: `www.example.com`).\n * @example\n * ```tsx\n * import * as React from 'react';\n * import { useFetchClient } from '@strapi/admin/admin';\n *\n * const MyComponent = () => {\n *   const [items, setItems] = React.useState([]);\n *   const { get } = useFetchClient();\n *   const requestURL = \"/some-endpoint\";\n *\n *   const handleGetData = async () => {\n *     const { data } = await get(requestURL);\n *     setItems(data.items);\n *   };\n *\n *   return (\n *    <div>\n *      <div>\n *       {\n *         items && items.map(item => <h2 key={item.uuid}>{item.name}</h2>))\n *       }\n *     </div>\n *    </div>\n *   );\n * };\n * ```\n * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/AbortController} AbortController.\n */\nconst useFetchClient = () => {\n  const controller = React.useRef<AbortController | null>(null);\n\n  if (controller.current === null) {\n    controller.current = new AbortController();\n  }\n\n  React.useEffect(() => {\n    return () => {\n      controller.current!.abort();\n    };\n  }, []);\n\n  return React.useMemo(\n    () =>\n      getFetchClient({\n        signal: controller.current!.signal,\n      }),\n    []\n  );\n};\n\nexport { useFetchClient };\n"], "mappings": ";;;;;;;;;;;;AAsCC,IACKA,iBAAiB,MAAA;AACrB,QAAMC,aAAmBC,aAA+B,IAAA;AAExD,MAAID,WAAWE,YAAY,MAAM;AAC/BF,eAAWE,UAAU,IAAIC,gBAAAA;EAC3B;AAEAC,EAAMC,gBAAU,MAAA;AACd,WAAO,MAAA;AACLL,iBAAWE,QAASI,MAAK;IAC3B;EACF,GAAG,CAAA,CAAE;AAEL,SAAaC,cACX,MACEC,eAAe;IACbC,QAAQT,WAAWE,QAASO;EAC9B,CAAA,GACF,CAAA,CAAE;AAEN;", "names": ["useFetchClient", "controller", "useRef", "current", "AbortController", "React", "useEffect", "abort", "useMemo", "getFetchClient", "signal"]}