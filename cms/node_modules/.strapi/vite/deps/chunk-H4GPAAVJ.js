// node_modules/@strapi/content-type-builder/dist/admin/pluginId.mjs
var pluginId = "content-type-builder";

// node_modules/@strapi/content-type-builder/dist/admin/utils/getRelationType.mjs
var getRelationType = (relation, targetAttribute) => {
  const hasNotTargetAttribute = targetAttribute === void 0 || targetAttribute === null;
  if (relation === "oneToOne" && hasNotTargetAttribute) {
    return "oneWay";
  }
  if (relation === "oneToMany" && hasNotTargetAttribute) {
    return "manyWay";
  }
  return relation;
};

export {
  pluginId,
  getRelationType
};
//# sourceMappingURL=chunk-H4GPAAVJ.js.map
