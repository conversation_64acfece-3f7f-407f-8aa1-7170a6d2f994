{"version": 3, "sources": ["../../../@strapi/content-type-builder/admin/src/pluginId.ts", "../../../@strapi/content-type-builder/admin/src/utils/getRelationType.ts"], "sourcesContent": ["export const pluginId = 'content-type-builder';\n", "import type { Schema } from '@strapi/types';\n\n/**\n *\n * Retrieves the relation type\n */\nexport const getRelationType = (\n  relation: Schema.Attribute.RelationKind.Any,\n  targetAttribute?: string | null\n) => {\n  const hasNotTargetAttribute = targetAttribute === undefined || targetAttribute === null;\n\n  if (relation === 'oneToOne' && hasNotTargetAttribute) {\n    return 'oneWay';\n  }\n\n  if (relation === 'oneToMany' && hasNotTargetAttribute) {\n    return 'manyWay';\n  }\n\n  return relation;\n};\n"], "mappings": ";AAAO,IAAMA,WAAW;;;ACMjB,IAAMC,kBAAkB,CAC7BC,UACAC,oBAAAA;AAEA,QAAMC,wBAAwBD,oBAAoBE,UAAaF,oBAAoB;AAEnF,MAAID,aAAa,cAAcE,uBAAuB;AACpD,WAAO;EACT;AAEA,MAAIF,aAAa,eAAeE,uBAAuB;AACrD,WAAO;EACT;AAEA,SAAOF;AACT;", "names": ["pluginId", "getRelationType", "relation", "targetAttribute", "hasNotTargetAttribute", "undefined"]}