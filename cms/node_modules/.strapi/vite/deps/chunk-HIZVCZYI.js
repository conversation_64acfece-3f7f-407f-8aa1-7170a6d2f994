// node_modules/@strapi/content-manager/dist/admin/utils/translations.mjs
var prefixPluginTranslations = (trad, pluginId) => {
  return Object.keys(trad).reduce((acc, current) => {
    acc[`${pluginId}.${current}`] = trad[current];
    return acc;
  }, {});
};
var getTranslation = (id) => `content-manager.${id}`;

export {
  prefixPluginTranslations,
  getTranslation
};
//# sourceMappingURL=chunk-HIZVCZYI.js.map
