{"version": 3, "sources": ["../../../@strapi/content-manager/admin/src/utils/translations.ts"], "sourcesContent": ["type TradOptions = Record<string, string>;\n\nconst prefixPluginTranslations = (trad: TradOptions, pluginId: string): TradOptions => {\n  if (!pluginId) {\n    throw new TypeError(\"pluginId can't be empty\");\n  }\n  return Object.keys(trad).reduce((acc, current) => {\n    acc[`${pluginId}.${current}`] = trad[current];\n    return acc;\n  }, {} as TradOptions);\n};\n\nconst getTranslation = (id: string) => `content-manager.${id}`;\n\nexport { getTranslation, prefixPluginTranslations };\n"], "mappings": ";AAEMA,IAAAA,2BAA2B,CAACC,MAAmBC,aAAAA;AAInD,SAAOC,OAAOC,KAAKH,IAAAA,EAAMI,OAAO,CAACC,KAAKC,YAAAA;AACpCD,QAAI,GAAGJ,QAAAA,IAAYK,OAAQ,EAAC,IAAIN,KAAKM,OAAQ;AAC7C,WAAOD;EACT,GAAG,CAAA,CAAC;AACN;AAEA,IAAME,iBAAiB,CAACC,OAAe,mBAAmBA,EAAAA;", "names": ["prefixPluginTranslations", "trad", "pluginId", "Object", "keys", "reduce", "acc", "current", "getTranslation", "id"]}