{"version": 3, "sources": ["../../../@strapi/admin/admin/src/components/ConfirmDialog.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { <PERSON><PERSON>, <PERSON>tonProps, Dialog } from '@strapi/design-system';\nimport { WarningCircle } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\nimport { styled } from 'styled-components';\n\n/* -------------------------------------------------------------------------------------------------\n * ConfirmDialog\n * -----------------------------------------------------------------------------------------------*/\ninterface ConfirmDialogProps extends Pick<ButtonProps, 'variant'>, Pick<Dialog.BodyProps, 'icon'> {\n  onConfirm?: (e?: React.MouseEvent<HTMLButtonElement>) => Promise<void> | void;\n  onCancel?: (e?: React.MouseEvent<HTMLButtonElement>) => Promise<void> | void;\n  children?: React.ReactNode;\n  endAction?: React.ReactNode;\n  startAction?: React.ReactNode;\n  title?: React.ReactNode;\n}\n\n/**\n * @beta\n * @public\n * @description A simple confirm dialog that out of the box can be used to confirm an action.\n * The component can additionally be customised if required e.g. the footer actions can be\n * completely replaced, but cannot be removed. Passing a string as the children prop will render\n * the string as the body of the dialog. If you need more control over the body, you can pass a\n * custom component as the children prop.\n * @example\n * ```tsx\n * import { Dialog } from '@strapi/design-system';\n *\n * const DeleteAction = ({ id }) => {\n *  const [isOpen, setIsOpen] = React.useState(false);\n *\n *  const [delete] = useDeleteMutation()\n *  const handleConfirm = async () => {\n *    await delete(id)\n *    setIsOpen(false)\n *  }\n *\n *  return (\n *    <Dialog.Root open={isOpen} onOpenChange={setIsOpen}>\n *      <Dialog.Trigger>\n *        <Button>Delete</Button>\n *      </Dialog.Trigger>\n *      <ConfirmDialog onConfirm={handleConfirm} />\n *    </Dialog.Root>\n *  )\n * }\n * ```\n */\nconst ConfirmDialog = ({\n  children,\n  icon = <StyledWarning />,\n  onConfirm,\n  onCancel,\n  variant = 'danger-light',\n  startAction,\n  endAction,\n  title,\n}: ConfirmDialogProps) => {\n  const { formatMessage } = useIntl();\n  const [isConfirming, setIsConfirming] = React.useState(false);\n\n  const content =\n    children ||\n    formatMessage({\n      id: 'app.confirm.body',\n      defaultMessage: 'Are you sure?',\n    });\n\n  const handleConfirm = async (e: React.MouseEvent<HTMLButtonElement>) => {\n    if (!onConfirm) {\n      return;\n    }\n\n    try {\n      setIsConfirming(true);\n      await onConfirm(e);\n    } finally {\n      setIsConfirming(false);\n    }\n  };\n\n  return (\n    <Dialog.Content>\n      <Dialog.Header>\n        {title ||\n          formatMessage({\n            id: 'app.components.ConfirmDialog.title',\n            defaultMessage: 'Confirmation',\n          })}\n      </Dialog.Header>\n      <Dialog.Body icon={icon}>{content}</Dialog.Body>\n      <Dialog.Footer>\n        {startAction || (\n          <Dialog.Cancel>\n            <Button\n              fullWidth\n              variant=\"tertiary\"\n              onClick={(e) => {\n                e.stopPropagation();\n                if (onCancel) {\n                  onCancel(e);\n                }\n              }}\n            >\n              {formatMessage({\n                id: 'app.components.Button.cancel',\n                defaultMessage: 'Cancel',\n              })}\n            </Button>\n          </Dialog.Cancel>\n        )}\n        {endAction || (\n          <Dialog.Action>\n            <Button fullWidth onClick={handleConfirm} variant={variant} loading={isConfirming}>\n              {formatMessage({\n                id: 'app.components.Button.confirm',\n                defaultMessage: 'Confirm',\n              })}\n            </Button>\n          </Dialog.Action>\n        )}\n      </Dialog.Footer>\n    </Dialog.Content>\n  );\n};\n\nconst StyledWarning = styled(WarningCircle)`\n  width: 24px;\n  height: 24px;\n\n  path {\n    fill: ${({ theme }) => theme.colors.danger600};\n  }\n`;\n\nexport { ConfirmDialog };\nexport type { ConfirmDialogProps };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAmDA,IAAMA,gBAAgB,CAAC,EACrBC,UACAC,WAAOC,wBAACC,eAAAA,CAAAA,CAAAA,GACRC,WACAC,UACAC,UAAU,gBACVC,aACAC,WACAC,MAAK,MACc;AACnB,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,CAACC,cAAcC,eAAAA,IAAyBC,eAAS,KAAA;AAEvD,QAAMC,UACJf,YACAU,cAAc;IACZM,IAAI;IACJC,gBAAgB;EAClB,CAAA;AAEF,QAAMC,gBAAgB,OAAOC,MAAAA;AAC3B,QAAI,CAACf,WAAW;AACd;IACF;AAEA,QAAI;AACFS,sBAAgB,IAAA;AAChB,YAAMT,UAAUe,CAAAA;cACR;AACRN,sBAAgB,KAAA;IAClB;EACF;AAEA,aACEO,yBAACC,OAAOC,SAAO;;UACbpB,wBAACmB,OAAOE,QAAM;QACXd,UAAAA,SACCC,cAAc;UACZM,IAAI;UACJC,gBAAgB;QAClB,CAAA;;UAEJf,wBAACmB,OAAOG,MAAI;QAACvB;QAAac,UAAAA;;UAC1BK,yBAACC,OAAOI,QAAM;;UACXlB,mBACCL,wBAACmB,OAAOK,QAAM;YACZ,cAAAxB,wBAACyB,QAAAA;cACCC,WAAS;cACTtB,SAAQ;cACRuB,SAAS,CAACV,MAAAA;AACRA,kBAAEW,gBAAe;AACjB,oBAAIzB,UAAU;AACZA,2BAASc,CAAAA;gBACX;cACF;wBAECT,cAAc;gBACbM,IAAI;gBACJC,gBAAgB;cAClB,CAAA;;;UAILT,iBACCN,wBAACmB,OAAOU,QAAM;YACZ,cAAA7B,wBAACyB,QAAAA;cAAOC,WAAS;cAACC,SAASX;cAAeZ;cAAkB0B,SAASpB;wBAClEF,cAAc;gBACbM,IAAI;gBACJC,gBAAgB;cAClB,CAAA;;;;;;;AAOd;AAEA,IAAMd,gBAAgB8B,GAAOC,YAAAA;;;;;YAKjB,CAAC,EAAEC,MAAK,MAAOA,MAAMC,OAAOC,SAAS;;;", "names": ["ConfirmDialog", "children", "icon", "_jsx", "StyledWarning", "onConfirm", "onCancel", "variant", "startAction", "endAction", "title", "formatMessage", "useIntl", "isConfirming", "setIsConfirming", "useState", "content", "id", "defaultMessage", "handleConfirm", "e", "_jsxs", "Dialog", "Content", "Header", "Body", "Footer", "Cancel", "<PERSON><PERSON>", "fullWidth", "onClick", "stopPropagation", "Action", "loading", "styled", "WarningCircle", "theme", "colors", "danger600"]}