// node_modules/@strapi/admin/dist/admin/admin/src/utils/translatedErrors.mjs
var errorsTrads = {
  email: {
    id: "components.Input.error.validation.email",
    defaultMessage: "This is not a valid email."
  },
  json: {
    id: "components.Input.error.validation.json",
    defaultMessage: "This doesn't match the JSON format"
  },
  lowercase: {
    id: "components.Input.error.validation.lowercase",
    defaultMessage: "The value must be a lowercase string"
  },
  max: {
    id: "components.Input.error.validation.max",
    defaultMessage: "The value is too high (max: {max})."
  },
  maxLength: {
    id: "components.Input.error.validation.maxLength",
    defaultMessage: "The value is too long (max: {max})."
  },
  min: {
    id: "components.Input.error.validation.min",
    defaultMessage: "The value is too low (min: {min})."
  },
  minLength: {
    id: "components.Input.error.validation.minLength",
    defaultMessage: "The value is too short (min: {min})."
  },
  regex: {
    id: "components.Input.error.validation.regex",
    defaultMessage: "The value does not match the regex."
  },
  required: {
    id: "components.Input.error.validation.required",
    defaultMessage: "This value is required."
  },
  string: {
    id: "components.Input.error.validation.string",
    defaultMessage: "This is not a valid string."
  },
  unique: {
    id: "components.Input.error.validation.unique",
    defaultMessage: "This value is already used."
  },
  integer: {
    id: "component.Input.error.validation.integer",
    defaultMessage: "The value must be an integer"
  }
};

export {
  errorsTrads
};
//# sourceMappingURL=chunk-IFOFBKTA.js.map
