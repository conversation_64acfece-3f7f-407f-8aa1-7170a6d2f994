import {
  ForwardRef$H,
  ForwardRef$J
} from "./chunk-3UVBSCPH.js";
import {
  Flex,
  Loader,
  Typography,
  useIntl
} from "./chunk-4YYUDJZ2.js";
import {
  ForwardRef$3
} from "./chunk-QW5ZDDTU.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/admin/src/components/WidgetHelpers.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var Loading = ({ children }) => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime.jsx)(Flex, {
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
    children: (0, import_jsx_runtime.jsx)(Loader, {
      children: children ?? formatMessage({
        id: "HomePage.widget.loading",
        defaultMessage: "Loading widget content"
      })
    })
  });
};
var Error = ({ children }) => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime.jsxs)(Flex, {
    height: "100%",
    direction: "column",
    justifyContent: "center",
    alignItems: "center",
    gap: 2,
    children: [
      (0, import_jsx_runtime.jsx)(ForwardRef$3, {
        width: "3.2rem",
        height: "3.2rem",
        fill: "danger600"
      }),
      (0, import_jsx_runtime.jsx)(Typography, {
        variant: "delta",
        children: formatMessage({
          id: "global.error",
          defaultMessage: "Something went wrong"
        })
      }),
      (0, import_jsx_runtime.jsx)(Typography, {
        textColor: "neutral600",
        children: children ?? formatMessage({
          id: "HomePage.widget.error",
          defaultMessage: "Couldn't load widget content."
        })
      })
    ]
  });
};
var NoData = ({ children }) => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime.jsxs)(Flex, {
    height: "100%",
    direction: "column",
    justifyContent: "center",
    alignItems: "center",
    gap: 6,
    children: [
      (0, import_jsx_runtime.jsx)(ForwardRef$J, {
        width: "16rem",
        height: "8.8rem"
      }),
      (0, import_jsx_runtime.jsx)(Typography, {
        textColor: "neutral600",
        children: children ?? formatMessage({
          id: "HomePage.widget.no-data",
          defaultMessage: "No content found."
        })
      })
    ]
  });
};
var NoPermissions = ({ children }) => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime.jsxs)(Flex, {
    height: "100%",
    direction: "column",
    justifyContent: "center",
    alignItems: "center",
    gap: 6,
    children: [
      (0, import_jsx_runtime.jsx)(ForwardRef$H, {
        width: "16rem",
        height: "8.8rem"
      }),
      (0, import_jsx_runtime.jsx)(Typography, {
        textColor: "neutral600",
        children: children ?? formatMessage({
          id: "HomePage.widget.no-permissions",
          defaultMessage: "You don’t have the permission to see this widget"
        })
      })
    ]
  });
};
var Widget = {
  Loading,
  Error,
  NoData,
  NoPermissions
};

export {
  Widget
};
//# sourceMappingURL=chunk-IHK57WMI.js.map
