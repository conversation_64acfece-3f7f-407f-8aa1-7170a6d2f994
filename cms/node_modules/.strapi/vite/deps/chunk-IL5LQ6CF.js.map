{"version": 3, "sources": ["../../../@strapi/admin/admin/src/hooks/useElementOnScreen.ts", "../../../@strapi/admin/admin/src/components/Layouts/ActionLayout.tsx", "../../../@strapi/admin/admin/src/components/Layouts/ContentLayout.tsx", "../../../@strapi/admin/admin/src/components/Layouts/GridLayout.tsx", "../../../@strapi/admin/admin/src/components/Layouts/HeaderLayout.tsx", "../../../@strapi/admin/admin/src/components/Layouts/Layout.tsx"], "sourcesContent": ["import * as React from 'react';\n\n/**\n * Hook that returns a ref to an element and a boolean indicating if the element is in the viewport\n * or in the element specified in `options.root`.\n */\nconst useElementOnScreen = <TElement extends HTMLElement = HTMLElement>(\n  onVisiblityChange: (isVisible: boolean) => void,\n  options?: IntersectionObserverInit\n): React.RefObject<TElement> => {\n  const containerRef = React.useRef<TElement>(null);\n\n  React.useEffect(() => {\n    const containerEl = containerRef.current;\n    const observer = new IntersectionObserver(([entry]) => {\n      onVisiblityChange(entry.isIntersecting);\n    }, options);\n\n    if (containerEl) {\n      observer.observe(containerRef.current);\n    }\n\n    return () => {\n      if (containerEl) {\n        observer.disconnect();\n      }\n    };\n  }, [containerRef, options, onVisiblityChange]);\n\n  return containerRef;\n};\n\nexport { useElementOnScreen };\n", "import * as React from 'react';\n\nimport { Flex } from '@strapi/design-system';\n\ninterface ActionLayoutProps {\n  endActions?: React.ReactNode;\n  startActions?: React.ReactNode;\n}\n\nconst ActionLayout = ({ startActions, endActions }: ActionLayoutProps) => {\n  if (!startActions && !endActions) {\n    return null;\n  }\n\n  return (\n    <Flex\n      justifyContent=\"space-between\"\n      alignItems=\"flex-start\"\n      paddingBottom={4}\n      paddingLeft={10}\n      paddingRight={10}\n    >\n      <Flex gap={2} wrap=\"wrap\">\n        {startActions}\n      </Flex>\n\n      <Flex gap={2} shrink={0} wrap=\"wrap\">\n        {endActions}\n      </Flex>\n    </Flex>\n  );\n};\n\nexport { ActionLayout, type ActionLayoutProps };\n", "import * as React from 'react';\n\nimport { Box } from '@strapi/design-system';\n\ninterface ContentLayoutProps {\n  children: React.ReactNode;\n}\n\nconst ContentLayout = ({ children }: ContentLayoutProps) => {\n  return (\n    <Box paddingLeft={10} paddingRight={10}>\n      {children}\n    </Box>\n  );\n};\n\nexport { ContentLayout, type ContentLayoutProps };\n", "import * as React from 'react';\n\nimport { Box } from '@strapi/design-system';\nimport { styled } from 'styled-components';\n\ninterface GridColSize {\n  S: number;\n  M: number;\n}\n\nconst GridColSize = {\n  S: 180,\n  M: 250,\n};\n\ntype Size = keyof GridColSize;\n\nconst StyledGrid = styled(Box)<{ $size: Size }>`\n  display: grid;\n  grid-template-columns: repeat(\n    auto-fit,\n    minmax(${({ $size }: { $size: Size }) => `${GridColSize[$size]}px`}, 1fr)\n  );\n  grid-gap: ${({ theme }) => theme.spaces[4]};\n`;\n\ninterface GridLayoutProps {\n  size: Size;\n  children: React.ReactNode;\n}\n\nconst GridLayout = ({ size, children }: GridLayoutProps) => {\n  return <StyledGrid $size={size}>{children}</StyledGrid>;\n};\n\nexport { GridLayout };\nexport type { GridLayoutProps, GridColSize };\n", "import * as React from 'react';\n\nimport { Box, Flex, Typography, TypographyProps, useCallbackRef } from '@strapi/design-system';\n\nimport { useElementOnScreen } from '../../hooks/useElementOnScreen';\n\n/* -------------------------------------------------------------------------------------------------\n * BaseHeaderLayout\n * -----------------------------------------------------------------------------------------------*/\n\ninterface BaseHeaderLayoutProps extends Omit<TypographyProps<'div'>, 'tag'> {\n  navigationAction?: React.ReactNode;\n  primaryAction?: React.ReactNode;\n  secondaryAction?: React.ReactNode;\n  subtitle?: React.ReactNode;\n  sticky?: boolean;\n  width?: number;\n}\n\nconst BaseHeaderLayout = React.forwardRef<HTMLDivElement, BaseHeaderLayoutProps>(\n  (\n    { navigationAction, primaryAction, secondaryAction, subtitle, title, sticky, width, ...props },\n    ref\n  ) => {\n    const isSubtitleString = typeof subtitle === 'string';\n\n    if (sticky) {\n      return (\n        <Box\n          paddingLeft={6}\n          paddingRight={6}\n          paddingTop={3}\n          paddingBottom={3}\n          position=\"fixed\"\n          top={0}\n          right={0}\n          background=\"neutral0\"\n          shadow=\"tableShadow\"\n          width={`${width}px`}\n          zIndex={3}\n          data-strapi-header-sticky\n        >\n          <Flex justifyContent=\"space-between\">\n            <Flex>\n              {navigationAction && <Box paddingRight={3}>{navigationAction}</Box>}\n              <Box>\n                <Typography variant=\"beta\" tag=\"h1\" {...props}>\n                  {title}\n                </Typography>\n                {isSubtitleString ? (\n                  <Typography variant=\"pi\" textColor=\"neutral600\">\n                    {subtitle}\n                  </Typography>\n                ) : (\n                  subtitle\n                )}\n              </Box>\n              {secondaryAction ? <Box paddingLeft={4}>{secondaryAction}</Box> : null}\n            </Flex>\n            <Flex>{primaryAction ? <Box paddingLeft={2}>{primaryAction}</Box> : undefined}</Flex>\n          </Flex>\n        </Box>\n      );\n    }\n\n    return (\n      <Box\n        ref={ref}\n        paddingLeft={10}\n        paddingRight={10}\n        paddingBottom={8}\n        paddingTop={navigationAction ? 6 : 8}\n        background=\"neutral100\"\n        data-strapi-header\n      >\n        {navigationAction ? <Box paddingBottom={2}>{navigationAction}</Box> : null}\n        <Flex justifyContent=\"space-between\">\n          <Flex minWidth={0}>\n            <Typography tag=\"h1\" variant=\"alpha\" {...props}>\n              {title}\n            </Typography>\n            {secondaryAction ? <Box paddingLeft={4}>{secondaryAction}</Box> : null}\n          </Flex>\n          {primaryAction}\n        </Flex>\n        {isSubtitleString ? (\n          <Typography variant=\"epsilon\" textColor=\"neutral600\" tag=\"p\">\n            {subtitle}\n          </Typography>\n        ) : (\n          subtitle\n        )}\n      </Box>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * HeaderLayout\n * -----------------------------------------------------------------------------------------------*/\n\ninterface HeaderLayoutProps extends BaseHeaderLayoutProps {}\n\nconst HeaderLayout = (props: HeaderLayoutProps) => {\n  const baseHeaderLayoutRef = React.useRef<HTMLDivElement>(null);\n  const [headerSize, setHeaderSize] = React.useState<DOMRect | null>(null);\n  const [isVisible, setIsVisible] = React.useState(true);\n\n  const containerRef = useElementOnScreen<HTMLDivElement>(setIsVisible, {\n    root: null,\n    rootMargin: '0px',\n    threshold: 0,\n  });\n\n  useResizeObserver(containerRef, () => {\n    if (containerRef.current) {\n      setHeaderSize(containerRef.current.getBoundingClientRect());\n    }\n  });\n\n  React.useEffect(() => {\n    if (baseHeaderLayoutRef.current) {\n      setHeaderSize(baseHeaderLayoutRef.current.getBoundingClientRect());\n    }\n  }, [baseHeaderLayoutRef]);\n\n  return (\n    <>\n      <div style={{ height: headerSize?.height }} ref={containerRef}>\n        {isVisible && <BaseHeaderLayout ref={baseHeaderLayoutRef} {...props} />}\n      </div>\n\n      {!isVisible && <BaseHeaderLayout {...props} sticky width={headerSize?.width} />}\n    </>\n  );\n};\n\nHeaderLayout.displayName = 'HeaderLayout';\n\n/**\n * useResizeObserver: hook that observes the size of an element and calls a callback when it changes.\n */\nconst useResizeObserver = (\n  sources: React.RefObject<HTMLElement> | React.RefObject<HTMLElement>[],\n  onResize: ResizeObserverCallback\n) => {\n  const handleResize = useCallbackRef(onResize);\n\n  React.useLayoutEffect(() => {\n    const resizeObs = new ResizeObserver(handleResize);\n\n    if (Array.isArray(sources)) {\n      sources.forEach((source) => {\n        if (source.current) {\n          resizeObs.observe(source.current);\n        }\n      });\n    } else if (sources.current) {\n      resizeObs.observe(sources.current);\n    }\n\n    return () => {\n      resizeObs.disconnect();\n    };\n  }, [sources, handleResize]);\n};\n\nexport type { HeaderLayoutProps, BaseHeaderLayoutProps };\nexport { HeaderLayout, BaseHeaderLayout };\n", "import * as React from 'react';\n\nimport { Box } from '@strapi/design-system';\nimport { styled } from 'styled-components';\n\nimport { ActionLayout } from './ActionLayout';\nimport { ContentLayout } from './ContentLayout';\nimport { GridLayout, GridLayoutProps } from './GridLayout';\nimport { HeaderLayout, BaseHeaderLayout } from './HeaderLayout';\n\ninterface LayoutProps {\n  children: React.ReactNode;\n  sideNav?: React.ReactNode;\n}\n\nconst GridContainer = styled(Box)<{ $hasSideNav: boolean }>`\n  display: grid;\n  grid-template-columns: ${({ $hasSideNav }) => ($hasSideNav ? `auto 1fr` : '1fr')};\n`;\n\nconst OverflowingItem = styled(Box)`\n  overflow-x: hidden;\n`;\n\nconst RootLayout = ({ sideNav, children }: LayoutProps) => {\n  return (\n    <GridContainer $hasSideNav={Boolean(sideNav)}>\n      {sideNav}\n      <OverflowingItem paddingBottom={10}>{children}</OverflowingItem>\n    </GridContainer>\n  );\n};\n\nconst Layouts = {\n  Root: RootLayout,\n  Header: HeaderLayout,\n  BaseHeader: BaseHeaderLayout,\n  Grid: GridLayout,\n  Action: ActionLayout,\n  Content: ContentLayout,\n};\n\nexport { Layouts, type LayoutProps, type GridLayoutProps };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAMA,IAAMA,qBAAqB,CACzBC,mBACAC,YAAAA;AAEA,QAAMC,eAAqBC,aAAiB,IAAA;AAE5CC,EAAMC,gBAAU,MAAA;AACd,UAAMC,cAAcJ,aAAaK;AACjC,UAAMC,WAAW,IAAIC,qBAAqB,CAAC,CAACC,KAAM,MAAA;AAChDV,wBAAkBU,MAAMC,cAAc;OACrCV,OAAAA;AAEH,QAAIK,aAAa;AACfE,eAASI,QAAQV,aAAaK,OAAO;IACvC;AAEA,WAAO,MAAA;AACL,UAAID,aAAa;AACfE,iBAASK,WAAU;MACrB;IACF;KACC;IAACX;IAAcD;IAASD;EAAkB,CAAA;AAE7C,SAAOE;AACT;;;;;;;;;ACrBA,IAAMY,eAAe,CAAC,EAAEC,cAAcC,WAAU,MAAqB;AACnE,MAAI,CAACD,gBAAgB,CAACC,YAAY;AAChC,WAAO;EACT;AAEA,aACEC,yBAACC,MAAAA;IACCC,gBAAe;IACfC,YAAW;IACXC,eAAe;IACfC,aAAa;IACbC,cAAc;;UAEdC,wBAACN,MAAAA;QAAKO,KAAK;QAAGC,MAAK;QAChBX,UAAAA;;UAGHS,wBAACN,MAAAA;QAAKO,KAAK;QAAGE,QAAQ;QAAGD,MAAK;QAC3BV,UAAAA;;;;AAIT;;;;;ACvBA,IAAMY,gBAAgB,CAAC,EAAEC,SAAQ,MAAsB;AACrD,aACEC,yBAACC,KAAAA;IAAIC,aAAa;IAAIC,cAAc;IACjCJ;;AAGP;;;;;ACJA,IAAMK,cAAc;EAClBC,GAAG;EACHC,GAAG;AACL;AAIA,IAAMC,aAAaC,GAAOC,GAAAA;;;;aAIb,CAAC,EAAEC,MAAK,MAAwB,GAAGN,YAAYM,KAAAA,CAAM,IAAI;;cAExD,CAAC,EAAEC,MAAK,MAAOA,MAAMC,OAAO,CAAA,CAAE;;AAQ5C,IAAMC,aAAa,CAAC,EAAEC,MAAMC,SAAQ,MAAmB;AACrD,aAAOC,yBAACT,YAAAA;IAAWG,OAAOI;IAAOC;;AACnC;;;;;ACdME,IAAAA,mBAAyBC,kBAC7B,CACE,EAAEC,kBAAkBC,eAAeC,iBAAiBC,UAAUC,OAAOC,QAAQC,OAAO,GAAGC,MAAAA,GACvFC,QAAAA;AAEA,QAAMC,mBAAmB,OAAON,aAAa;AAE7C,MAAIE,QAAQ;AACV,eACEK,yBAACC,KAAAA;MACCC,aAAa;MACbC,cAAc;MACdC,YAAY;MACZC,eAAe;MACfC,UAAS;MACTC,KAAK;MACLC,OAAO;MACPC,YAAW;MACXC,QAAO;MACPd,OAAO,GAAGA,KAAM;MAChBe,QAAQ;MACRC,6BAAyB;MAEzB,cAAAC,0BAACC,MAAAA;QAAKC,gBAAe;;cACnBF,0BAACC,MAAAA;;cACExB,wBAAoBU,yBAACC,KAAAA;gBAAIE,cAAc;gBAAIb,UAAAA;;kBAC5CuB,0BAACZ,KAAAA;;sBACCD,yBAACgB,YAAAA;oBAAWC,SAAQ;oBAAOC,KAAI;oBAAM,GAAGrB;oBACrCH,UAAAA;;kBAEFK,uBACCC,yBAACgB,YAAAA;oBAAWC,SAAQ;oBAAKE,WAAU;oBAChC1B,UAAAA;kBAGHA,CAAAA,IAAAA;;;cAGHD,sBAAkBQ,yBAACC,KAAAA;gBAAIC,aAAa;gBAAIV,UAAAA;cAAyB,CAAA,IAAA;;;cAEpEQ,yBAACc,MAAAA;YAAMvB,UAAAA,oBAAgBS,yBAACC,KAAAA;cAAIC,aAAa;cAAIX,UAAAA;YAAuB6B,CAAAA,IAAAA;;;;;EAI5E;AAEA,aACEP,0BAACZ,KAAAA;IACCH;IACAI,aAAa;IACbC,cAAc;IACdE,eAAe;IACfD,YAAYd,mBAAmB,IAAI;IACnCmB,YAAW;IACXY,sBAAkB;;MAEjB/B,uBAAmBU,yBAACC,KAAAA;QAAII,eAAe;QAAIf,UAAAA;MAA0B,CAAA,IAAA;UACtEuB,0BAACC,MAAAA;QAAKC,gBAAe;;cACnBF,0BAACC,MAAAA;YAAKQ,UAAU;;kBACdtB,yBAACgB,YAAAA;gBAAWE,KAAI;gBAAKD,SAAQ;gBAAS,GAAGpB;gBACtCH,UAAAA;;cAEFF,sBAAkBQ,yBAACC,KAAAA;gBAAIC,aAAa;gBAAIV,UAAAA;cAAyB,CAAA,IAAA;;;UAEnED;;;MAEFQ,uBACCC,yBAACgB,YAAAA;QAAWC,SAAQ;QAAUE,WAAU;QAAaD,KAAI;QACtDzB,UAAAA;MAGHA,CAAAA,IAAAA;;;AAIR,CAAA;AASF,IAAM8B,eAAe,CAAC1B,UAAAA;AACpB,QAAM2B,sBAA4BC,cAAuB,IAAA;AACzD,QAAM,CAACC,YAAYC,aAAAA,IAAuBC,gBAAyB,IAAA;AACnE,QAAM,CAACC,WAAWC,YAAAA,IAAsBF,gBAAS,IAAA;AAEjD,QAAMG,eAAeC,mBAAmCF,cAAc;IACpEG,MAAM;IACNC,YAAY;IACZC,WAAW;EACb,CAAA;AAEAC,oBAAkBL,cAAc,MAAA;AAC9B,QAAIA,aAAaM,SAAS;AACxBV,oBAAcI,aAAaM,QAAQC,sBAAqB,CAAA;IAC1D;EACF,CAAA;AAEAC,EAAMC,iBAAU,MAAA;AACd,QAAIhB,oBAAoBa,SAAS;AAC/BV,oBAAcH,oBAAoBa,QAAQC,sBAAqB,CAAA;IACjE;KACC;IAACd;EAAoB,CAAA;AAExB,aACEX,0BAAA4B,8BAAA;;UACEzC,yBAAC0C,OAAAA;QAAIC,OAAO;UAAEC,QAAQlB,yCAAYkB;QAAO;QAAG9C,KAAKiC;QAC9CF,UAAAA,iBAAa7B,yBAACZ,kBAAAA;UAAiBU,KAAK0B;UAAsB,GAAG3B;;;MAG/D,CAACgC,iBAAa7B,yBAACZ,kBAAAA;QAAkB,GAAGS;QAAOF,QAAM;QAACC,OAAO8B,yCAAY9B;;;;AAG5E;AAEA2B,aAAasB,cAAc;AAK3B,IAAMT,oBAAoB,CACxBU,SACAC,aAAAA;AAEA,QAAMC,eAAeC,eAAeF,QAAAA;AAEpCR,EAAMW,uBAAgB,MAAA;AACpB,UAAMC,YAAY,IAAIC,eAAeJ,YAAAA;AAErC,QAAIK,MAAMC,QAAQR,OAAU,GAAA;AAC1BA,cAAQS,QAAQ,CAACC,WAAAA;AACf,YAAIA,OAAOnB,SAAS;AAClBc,oBAAUM,QAAQD,OAAOnB,OAAO;QAClC;MACF,CAAA;eACSS,QAAQT,SAAS;AAC1Bc,gBAAUM,QAAQX,QAAQT,OAAO;IACnC;AAEA,WAAO,MAAA;AACLc,gBAAUO,WAAU;IACtB;KACC;IAACZ;IAASE;EAAa,CAAA;AAC5B;;;ACtJA,IAAMW,gBAAgBC,GAAOC,GAAAA;;2BAEF,CAAC,EAAEC,YAAW,MAAQA,cAAc,aAAa,KAAO;;AAGnF,IAAMC,kBAAkBH,GAAOC,GAAAA;;;AAI/B,IAAMG,aAAa,CAAC,EAAEC,SAASC,SAAQ,MAAe;AACpD,aACEC,0BAACR,eAAAA;IAAcG,aAAaM,QAAQH,OAAAA;;MACjCA;UACDI,yBAACN,iBAAAA;QAAgBO,eAAe;QAAKJ;;;;AAG3C;AAEA,IAAMK,UAAU;EACdC,MAAMR;EACNS,QAAQC;EACRC,YAAYC;EACZC,MAAMC;EACNC,QAAQC;EACRC,SAASC;AACX;", "names": ["useElementOnScreen", "onVisiblityChange", "options", "containerRef", "useRef", "React", "useEffect", "containerEl", "current", "observer", "IntersectionObserver", "entry", "isIntersecting", "observe", "disconnect", "ActionLayout", "startActions", "endActions", "_jsxs", "Flex", "justifyContent", "alignItems", "paddingBottom", "paddingLeft", "paddingRight", "_jsx", "gap", "wrap", "shrink", "ContentLayout", "children", "_jsx", "Box", "paddingLeft", "paddingRight", "GridColSize", "S", "M", "StyledGrid", "styled", "Box", "$size", "theme", "spaces", "GridLayout", "size", "children", "_jsx", "BaseHeaderLayout", "forwardRef", "navigationAction", "primaryAction", "secondaryAction", "subtitle", "title", "sticky", "width", "props", "ref", "isSubtitleString", "_jsx", "Box", "paddingLeft", "paddingRight", "paddingTop", "paddingBottom", "position", "top", "right", "background", "shadow", "zIndex", "data-strapi-header-sticky", "_jsxs", "Flex", "justifyContent", "Typography", "variant", "tag", "textColor", "undefined", "data-strapi-header", "min<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "baseHeaderLayoutRef", "useRef", "headerSize", "setHeaderSize", "useState", "isVisible", "setIsVisible", "containerRef", "useElementOnScreen", "root", "rootMargin", "threshold", "useResizeObserver", "current", "getBoundingClientRect", "React", "useEffect", "_Fragment", "div", "style", "height", "displayName", "sources", "onResize", "handleResize", "useCallbackRef", "useLayoutEffect", "resizeObs", "ResizeObserver", "Array", "isArray", "for<PERSON>ach", "source", "observe", "disconnect", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "styled", "Box", "$hasSideNav", "OverflowingItem", "RootLayout", "sideNav", "children", "_jsxs", "Boolean", "_jsx", "paddingBottom", "Layouts", "Root", "Header", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "BaseHeader", "BaseHeaderLayout", "Grid", "GridLayout", "Action", "ActionLayout", "Content", "ContentLayout"]}