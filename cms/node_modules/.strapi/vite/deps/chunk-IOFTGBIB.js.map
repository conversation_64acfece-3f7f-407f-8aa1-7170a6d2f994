{"version": 3, "sources": ["../../../@strapi/admin/ee/admin/src/pages/AuthPage/components/SSOProviders.tsx"], "sourcesContent": ["import { Flex, Grid, Tooltip, Typography } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\nimport { Link } from 'react-router-dom';\nimport { styled } from 'styled-components';\n\nimport { GetProviders } from '../../../../../../shared/contracts/providers';\n\n/* -------------------------------------------------------------------------------------------------\n * SSOProviders\n * -----------------------------------------------------------------------------------------------*/\n\ninterface SSOProvidersProps {\n  providers: GetProviders.Response;\n  displayAllProviders?: boolean;\n}\n\nconst SSOProviders = ({ providers, displayAllProviders }: SSOProvidersProps) => {\n  const { formatMessage } = useIntl();\n\n  if (displayAllProviders) {\n    return (\n      <Grid.Root gap={4}>\n        {providers.map((provider) => (\n          <Grid.Item key={provider.uid} col={4} direction=\"column\" alignItems=\"stretch\">\n            <SSOProviderButton provider={provider} />\n          </Grid.Item>\n        ))}\n      </Grid.Root>\n    );\n  }\n\n  if (providers.length > 2 && !displayAllProviders) {\n    return (\n      <Grid.Root gap={4}>\n        {providers.slice(0, 2).map((provider) => (\n          <Grid.Item key={provider.uid} col={4} direction=\"column\" alignItems=\"stretch\">\n            <SSOProviderButton provider={provider} />\n          </Grid.Item>\n        ))}\n        <Grid.Item col={4} direction=\"column\" alignItems=\"stretch\">\n          <Tooltip\n            label={formatMessage({\n              id: 'global.see-more',\n            })}\n          >\n            <SSOButton as={Link} to=\"/auth/providers\">\n              <span aria-hidden>•••</span>\n            </SSOButton>\n          </Tooltip>\n        </Grid.Item>\n      </Grid.Root>\n    );\n  }\n\n  return (\n    <SSOProvidersWrapper justifyContent=\"center\">\n      {providers.map((provider) => (\n        <SSOProviderButton key={provider.uid} provider={provider} />\n      ))}\n    </SSOProvidersWrapper>\n  );\n};\n\nconst SSOProvidersWrapper = styled(Flex)`\n  & a:not(:first-child):not(:last-child) {\n    margin: 0 ${({ theme }) => theme.spaces[2]};\n  }\n  & a:first-child {\n    margin-right: ${({ theme }) => theme.spaces[2]};\n  }\n  & a:last-child {\n    margin-left: ${({ theme }) => theme.spaces[2]};\n  }\n`;\n\n/* -------------------------------------------------------------------------------------------------\n * SSOProviderButton\n * -----------------------------------------------------------------------------------------------*/\n\ninterface SSOProviderButtonProps {\n  provider: GetProviders.Response[number];\n}\n\nconst SSOProviderButton = ({ provider }: SSOProviderButtonProps) => {\n  return (\n    <Tooltip label={provider.displayName}>\n      <SSOButton href={`${window.strapi.backendURL}/admin/connect/${provider.uid}`}>\n        {provider.icon ? (\n          <img src={provider.icon} aria-hidden alt=\"\" height=\"32px\" />\n        ) : (\n          <Typography>{provider.displayName}</Typography>\n        )}\n      </SSOButton>\n    </Tooltip>\n  );\n};\n\nconst SSOButton = styled.a`\n  width: 13.6rem;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 4.8rem;\n  border: 1px solid ${({ theme }) => theme.colors.neutral150};\n  border-radius: ${({ theme }) => theme.borderRadius};\n  text-decoration: inherit;\n  &:link {\n    text-decoration: none;\n  }\n  color: ${({ theme }) => theme.colors.neutral600};\n`;\n\nexport { SSOProviders };\nexport type { SSOProvidersProps };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAgBA,IAAMA,eAAe,CAAC,EAAEC,WAAWC,oBAAmB,MAAqB;AACzE,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,MAAIF,qBAAqB;AACvB,eACEG,wBAACC,KAAKC,MAAI;MAACC,KAAK;MACbP,UAAAA,UAAUQ,IAAI,CAACC,iBACdL,wBAACC,KAAKK,MAAI;QAAoBC,KAAK;QAAGC,WAAU;QAASC,YAAW;QAClE,cAAAT,wBAACU,mBAAAA;UAAkBL;;MADLA,GAAAA,SAASM,GAAG,CAAA;;EAMpC;AAEA,MAAIf,UAAUgB,SAAS,KAAK,CAACf,qBAAqB;AAChD,eACEgB,yBAACZ,KAAKC,MAAI;MAACC,KAAK;;QACbP,UAAUkB,MAAM,GAAG,CAAGV,EAAAA,IAAI,CAACC,iBAC1BL,wBAACC,KAAKK,MAAI;UAAoBC,KAAK;UAAGC,WAAU;UAASC,YAAW;UAClE,cAAAT,wBAACU,mBAAAA;YAAkBL;;QADLA,GAAAA,SAASM,GAAG,CAAA;YAI9BX,wBAACC,KAAKK,MAAI;UAACC,KAAK;UAAGC,WAAU;UAASC,YAAW;UAC/C,cAAAT,wBAACe,aAAAA;YACCC,OAAOlB,cAAc;cACnBmB,IAAI;YACN,CAAA;YAEA,cAAAjB,wBAACkB,WAAAA;cAAUC,IAAIC;cAAMC,IAAG;cACtB,cAAArB,wBAACsB,QAAAA;gBAAKC,eAAW;gBAAC,UAAA;;;;;;;EAM9B;AAEA,aACEvB,wBAACwB,qBAAAA;IAAoBC,gBAAe;IACjC7B,UAAAA,UAAUQ,IAAI,CAACC,iBACdL,wBAACU,mBAAAA;MAAqCL;IAAdA,GAAAA,SAASM,GAAG,CAAA;;AAI5C;AAEA,IAAMa,sBAAsBE,GAAOC,IAAAA;;gBAEnB,CAAC,EAAEC,MAAK,MAAOA,MAAMC,OAAO,CAAA,CAAE;;;oBAG1B,CAAC,EAAED,MAAK,MAAOA,MAAMC,OAAO,CAAA,CAAE;;;mBAG/B,CAAC,EAAED,MAAK,MAAOA,MAAMC,OAAO,CAAA,CAAE;;;AAYjD,IAAMnB,oBAAoB,CAAC,EAAEL,SAAQ,MAA0B;AAC7D,aACEL,wBAACe,aAAAA;IAAQC,OAAOX,SAASyB;IACvB,cAAA9B,wBAACkB,WAAAA;MAAUa,MAAM,GAAGC,OAAOC,OAAOC,UAAU,kBAAkB7B,SAASM,GAAG;gBACvEN,SAAS8B,WACRnC,wBAACoC,OAAAA;QAAIC,KAAKhC,SAAS8B;QAAMZ,eAAW;QAACe,KAAI;QAAGC,QAAO;eAEnDvC,wBAACwC,YAAAA;QAAYnC,UAAAA,SAASyB;;;;AAKhC;AAEA,IAAMZ,YAAYQ,GAAOe;;;;;;sBAMH,CAAC,EAAEb,MAAK,MAAOA,MAAMc,OAAOC,UAAU;mBACzC,CAAC,EAAEf,MAAK,MAAOA,MAAMgB,YAAY;;;;;WAKzC,CAAC,EAAEhB,MAAK,MAAOA,MAAMc,OAAOG,UAAU;;", "names": ["SSOProviders", "providers", "displayAllProviders", "formatMessage", "useIntl", "_jsx", "Grid", "Root", "gap", "map", "provider", "<PERSON><PERSON>", "col", "direction", "alignItems", "SSOProviderButton", "uid", "length", "_jsxs", "slice", "<PERSON><PERSON><PERSON>", "label", "id", "SSOButton", "as", "Link", "to", "span", "aria-hidden", "SSOProvidersWrapper", "justifyContent", "styled", "Flex", "theme", "spaces", "displayName", "href", "window", "strapi", "backendURL", "icon", "img", "src", "alt", "height", "Typography", "a", "colors", "neutral150", "borderRadius", "neutral600"]}