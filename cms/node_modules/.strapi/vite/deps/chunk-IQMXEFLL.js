import {
  useControllableState
} from "./chunk-6EESFY62.js";
import {
  MemoizedInputRenderer
} from "./chunk-QQ4VIATE.js";
import {
  Form
} from "./chunk-VWIQ6S3Y.js";
import {
  useQueryParams
} from "./chunk-JXTOCMQW.js";
import {
  createContext
} from "./chunk-BLJUR3JO.js";
import {
  Box,
  Button,
  Flex,
  Popover,
  Tag,
  useIntl
} from "./chunk-4YYUDJZ2.js";
import {
  ForwardRef$1h,
  ForwardRef$3j,
  ForwardRef$45
} from "./chunk-QW5ZDDTU.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/admin/src/components/Filters.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);

// node_modules/@strapi/admin/dist/admin/admin/src/constants/filters.mjs
var BASE_FILTERS = [
  {
    label: {
      id: "components.FilterOptions.FILTER_TYPES.$eq",
      defaultMessage: "is"
    },
    value: "$eq"
  },
  {
    label: {
      id: "components.FilterOptions.FILTER_TYPES.$ne",
      defaultMessage: "is not"
    },
    value: "$ne"
  },
  {
    label: {
      id: "components.FilterOptions.FILTER_TYPES.$null",
      defaultMessage: "is null"
    },
    value: "$null"
  },
  {
    label: {
      id: "components.FilterOptions.FILTER_TYPES.$notNull",
      defaultMessage: "is not null"
    },
    value: "$notNull"
  }
];
var NUMERIC_FILTERS = [
  {
    label: {
      id: "components.FilterOptions.FILTER_TYPES.$gt",
      defaultMessage: "is greater than"
    },
    value: "$gt"
  },
  {
    label: {
      id: "components.FilterOptions.FILTER_TYPES.$gte",
      defaultMessage: "is greater than or equal to"
    },
    value: "$gte"
  },
  {
    label: {
      id: "components.FilterOptions.FILTER_TYPES.$lt",
      defaultMessage: "is less than"
    },
    value: "$lt"
  },
  {
    label: {
      id: "components.FilterOptions.FILTER_TYPES.$lte",
      defaultMessage: "is less than or equal to"
    },
    value: "$lte"
  }
];
var IS_SENSITIVE_FILTERS = [
  {
    label: {
      id: "components.FilterOptions.FILTER_TYPES.$eqi",
      defaultMessage: "is (case insensitive)"
    },
    value: "$eqi"
  },
  {
    label: {
      id: "components.FilterOptions.FILTER_TYPES.$nei",
      defaultMessage: "is not (case insensitive)"
    },
    value: "$nei"
  }
];
var CONTAINS_FILTERS = [
  {
    label: {
      id: "components.FilterOptions.FILTER_TYPES.$contains",
      defaultMessage: "contains"
    },
    value: "$contains"
  },
  {
    label: {
      id: "components.FilterOptions.FILTER_TYPES.$containsi",
      defaultMessage: "contains (case insensitive)"
    },
    value: "$containsi"
  },
  {
    label: {
      id: "components.FilterOptions.FILTER_TYPES.$notContains",
      defaultMessage: "not contains"
    },
    value: "$notContains"
  },
  {
    label: {
      id: "components.FilterOptions.FILTER_TYPES.$notContainsi",
      defaultMessage: "not contains (case insensitive)"
    },
    value: "$notContainsi"
  }
];
var STRING_PARSE_FILTERS = [
  {
    label: {
      id: "components.FilterOptions.FILTER_TYPES.$startsWith",
      defaultMessage: "starts with"
    },
    value: "$startsWith"
  },
  {
    label: {
      id: "components.FilterOptions.FILTER_TYPES.$startsWithi",
      defaultMessage: "starts with (case insensitive)"
    },
    value: "$startsWithi"
  },
  {
    label: {
      id: "components.FilterOptions.FILTER_TYPES.$endsWith",
      defaultMessage: "ends with"
    },
    value: "$endsWith"
  },
  {
    label: {
      id: "components.FilterOptions.FILTER_TYPES.$endsWithi",
      defaultMessage: "ends with (case insensitive)"
    },
    value: "$endsWithi"
  }
];
var FILTERS_WITH_NO_VALUE = [
  "$null",
  "$notNull"
];

// node_modules/@strapi/admin/dist/admin/admin/src/components/Filters.mjs
var [FiltersProvider, useFilters] = createContext("Filters");
var Root = ({ children, disabled = false, onChange, options = [], onOpenChange, open: openProp, defaultOpen, ...restProps }) => {
  const handleChange = (data) => {
    if (onChange) {
      onChange(data);
    }
  };
  const [open = false, setOpen] = useControllableState({
    prop: openProp,
    defaultProp: defaultOpen,
    onChange: onOpenChange
  });
  return (0, import_jsx_runtime.jsx)(Popover.Root, {
    open,
    onOpenChange: setOpen,
    ...restProps,
    children: (0, import_jsx_runtime.jsx)(FiltersProvider, {
      setOpen,
      disabled,
      onChange: handleChange,
      options,
      children
    })
  });
};
var Trigger = React.forwardRef(({ label }, forwardedRef) => {
  const { formatMessage } = useIntl();
  const disabled = useFilters("Trigger", ({ disabled: disabled2 }) => disabled2);
  return (0, import_jsx_runtime.jsx)(Popover.Trigger, {
    children: (0, import_jsx_runtime.jsx)(Button, {
      variant: "tertiary",
      ref: forwardedRef,
      startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$3j, {}),
      size: "S",
      disabled,
      children: label || formatMessage({
        id: "app.utils.filters",
        defaultMessage: "Filters"
      })
    })
  });
});
var PopoverImpl = () => {
  var _a;
  const [{ query }, setQuery] = useQueryParams();
  const { formatMessage } = useIntl();
  const options = useFilters("Popover", ({ options: options2 }) => options2);
  const onChange = useFilters("Popover", ({ onChange: onChange2 }) => onChange2);
  const setOpen = useFilters("Popover", ({ setOpen: setOpen2 }) => setOpen2);
  if (options.length === 0) {
    return null;
  }
  const handleSubmit = (data) => {
    var _a2, _b;
    const value = FILTERS_WITH_NO_VALUE.includes(data.filter) ? "true" : encodeURIComponent(data.value ?? "");
    if (!value) {
      return;
    }
    if (onChange) {
      onChange(data);
    }
    const fieldOptions = options.find((filter) => filter.name === data.name);
    const operatorValuePairing = {
      [data.filter]: value
    };
    const newFilterQuery = {
      ...query.filters,
      $and: [
        ...((_a2 = query.filters) == null ? void 0 : _a2.$and) ?? [],
        {
          [data.name]: fieldOptions.type === "relation" ? {
            [((_b = fieldOptions.mainField) == null ? void 0 : _b.name) ?? "id"]: operatorValuePairing
          } : operatorValuePairing
        }
      ]
    };
    setQuery({
      filters: newFilterQuery,
      page: 1
    });
    setOpen(false);
  };
  return (0, import_jsx_runtime.jsx)(Popover.Content, {
    children: (0, import_jsx_runtime.jsx)(Box, {
      padding: 3,
      children: (0, import_jsx_runtime.jsx)(Form, {
        method: "POST",
        initialValues: {
          name: (_a = options[0]) == null ? void 0 : _a.name,
          filter: BASE_FILTERS[0].value
        },
        onSubmit: handleSubmit,
        children: ({ values: formValues, modified, isSubmitting }) => {
          var _a2;
          const filter = options.find((filter2) => filter2.name === formValues.name);
          const Input = (filter == null ? void 0 : filter.input) || MemoizedInputRenderer;
          return (0, import_jsx_runtime.jsxs)(Flex, {
            direction: "column",
            alignItems: "stretch",
            gap: 2,
            style: {
              minWidth: 184
            },
            children: [
              [
                {
                  ["aria-label"]: formatMessage({
                    id: "app.utils.select-field",
                    defaultMessage: "Select field"
                  }),
                  name: "name",
                  options: options.map((filter2) => ({
                    label: filter2.label,
                    value: filter2.name
                  })),
                  placholder: formatMessage({
                    id: "app.utils.select-field",
                    defaultMessage: "Select field"
                  }),
                  type: "enumeration"
                },
                {
                  ["aria-label"]: formatMessage({
                    id: "app.utils.select-filter",
                    defaultMessage: "Select filter"
                  }),
                  name: "filter",
                  options: (filter == null ? void 0 : filter.operators) || getFilterList(filter).map((opt) => ({
                    label: formatMessage(opt.label),
                    value: opt.value
                  })),
                  placeholder: formatMessage({
                    id: "app.utils.select-filter",
                    defaultMessage: "Select filter"
                  }),
                  type: "enumeration"
                }
              ].map((field) => (0, import_jsx_runtime.jsx)(MemoizedInputRenderer, {
                ...field
              }, field.name)),
              filter && formValues.filter && formValues.filter !== "$null" && formValues.filter !== "$notNull" ? (0, import_jsx_runtime.jsx)(Input, {
                ...filter,
                label: null,
                "aria-label": filter.label,
                name: "value",
                // @ts-expect-error – if type is `custom` then `Input` will be a custom component.
                type: ((_a2 = filter.mainField) == null ? void 0 : _a2.type) ?? filter.type
              }) : null,
              (0, import_jsx_runtime.jsx)(Button, {
                disabled: !modified || isSubmitting,
                size: "L",
                variant: "secondary",
                startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$1h, {}),
                type: "submit",
                fullWidth: true,
                children: formatMessage({
                  id: "app.utils.add-filter",
                  defaultMessage: "Add filter"
                })
              })
            ]
          });
        }
      })
    })
  });
};
var getFilterList = (filter) => {
  var _a;
  if (!filter) {
    return [];
  }
  const type = ((_a = filter.mainField) == null ? void 0 : _a.type) ? filter.mainField.type : filter.type;
  switch (type) {
    case "email":
    case "text":
    case "string": {
      return [
        ...BASE_FILTERS,
        ...IS_SENSITIVE_FILTERS,
        ...CONTAINS_FILTERS,
        ...STRING_PARSE_FILTERS
      ];
    }
    case "float":
    case "integer":
    case "biginteger":
    case "decimal": {
      return [
        ...BASE_FILTERS,
        ...NUMERIC_FILTERS
      ];
    }
    case "time":
    case "date": {
      return [
        ...BASE_FILTERS,
        ...NUMERIC_FILTERS,
        ...CONTAINS_FILTERS
      ];
    }
    case "datetime": {
      return [
        ...BASE_FILTERS,
        ...NUMERIC_FILTERS
      ];
    }
    case "enumeration": {
      return BASE_FILTERS;
    }
    default:
      return [
        ...BASE_FILTERS,
        ...IS_SENSITIVE_FILTERS
      ];
  }
};
var List = () => {
  var _a, _b, _c, _d;
  const [{ query }, setQuery] = useQueryParams();
  const options = useFilters("List", ({ options: options2 }) => options2);
  const handleClick = (data) => {
    var _a2;
    const nextFilters = (((_a2 = query == null ? void 0 : query.filters) == null ? void 0 : _a2.$and) ?? []).filter((filter) => {
      const [attributeName] = Object.keys(filter);
      if (attributeName !== data.name) {
        return true;
      }
      const { type, mainField } = options.find(({ name }) => name === attributeName);
      if (type === "relation") {
        const filterObj = filter[attributeName][(mainField == null ? void 0 : mainField.name) ?? "id"];
        if (typeof filterObj === "object") {
          const [operator] = Object.keys(filterObj);
          const value = filterObj[operator];
          return !(operator === data.filter && value === data.value);
        }
        return true;
      } else {
        const filterObj = filter[attributeName];
        const [operator] = Object.keys(filterObj);
        const value = filterObj[operator];
        return !(operator === data.filter && value === data.value);
      }
    });
    setQuery({
      filters: {
        $and: nextFilters
      },
      page: 1
    });
  };
  if (!((_b = (_a = query == null ? void 0 : query.filters) == null ? void 0 : _a.$and) == null ? void 0 : _b.length)) {
    return null;
  }
  return (0, import_jsx_runtime.jsx)(import_jsx_runtime.Fragment, {
    children: (_d = (_c = query == null ? void 0 : query.filters) == null ? void 0 : _c.$and) == null ? void 0 : _d.map((queryFilter) => {
      var _a2;
      const [attributeName] = Object.keys(queryFilter);
      const filter = options.find(({ name }) => name === attributeName);
      const filterObj = queryFilter[attributeName];
      if (!filter || typeof filterObj !== "object" || filterObj === null) {
        return null;
      }
      if (filter.type === "relation") {
        const modelFilter = filterObj[((_a2 = filter.mainField) == null ? void 0 : _a2.name) ?? "id"];
        if (typeof modelFilter === "object") {
          const [operator] = Object.keys(modelFilter);
          const value = modelFilter[operator];
          return (0, import_jsx_runtime.jsx)(AttributeTag, {
            ...filter,
            onClick: handleClick,
            operator,
            value
          }, `${attributeName}-${operator}-${value}`);
        }
        return null;
      } else {
        const [operator] = Object.keys(filterObj);
        const value = filterObj[operator];
        if (typeof value === "object") {
          return null;
        }
        return (0, import_jsx_runtime.jsx)(AttributeTag, {
          ...filter,
          onClick: handleClick,
          operator,
          value
        }, `${attributeName}-${operator}-${value}`);
      }
    })
  });
};
var AttributeTag = ({ input, label, mainField, name, onClick, operator, options, value, ...filter }) => {
  const { formatMessage, formatDate, formatTime, formatNumber } = useIntl();
  const handleClick = () => {
    onClick({
      name,
      value,
      filter: operator
    });
  };
  const type = (mainField == null ? void 0 : mainField.type) ? mainField.type : filter.type;
  let formattedValue = value;
  switch (type) {
    case "date":
      formattedValue = formatDate(value, {
        dateStyle: "full"
      });
      break;
    case "datetime":
      formattedValue = formatDate(value, {
        dateStyle: "full",
        timeStyle: "short"
      });
      break;
    case "time":
      const [hour, minute] = value.split(":");
      const date = /* @__PURE__ */ new Date();
      date.setHours(Number(hour));
      date.setMinutes(Number(minute));
      formattedValue = formatTime(date, {
        hour: "numeric",
        minute: "numeric"
      });
      break;
    case "float":
    case "integer":
    case "biginteger":
    case "decimal":
      formattedValue = formatNumber(Number(value));
      break;
  }
  if (input && options) {
    const selectedOption = options.find((option) => {
      return (typeof option === "string" ? option : option.value) === value;
    });
    formattedValue = selectedOption ? typeof selectedOption === "string" ? selectedOption : selectedOption.label ?? selectedOption.value : value;
  }
  const content = `${label} ${formatMessage({
    id: `components.FilterOptions.FILTER_TYPES.${operator}`,
    defaultMessage: operator
  })} ${operator !== "$null" && operator !== "$notNull" ? formattedValue : ""}`;
  return (0, import_jsx_runtime.jsx)(Tag, {
    padding: 1,
    onClick: handleClick,
    icon: (0, import_jsx_runtime.jsx)(ForwardRef$45, {}),
    children: content
  });
};
var Filters = {
  List,
  Popover: PopoverImpl,
  Root,
  Trigger
};

export {
  Filters
};
//# sourceMappingURL=chunk-IQMXEFLL.js.map
