{"version": 3, "sources": ["../../../@strapi/admin/admin/src/constants/filters.ts", "../../../@strapi/admin/admin/src/components/Filters.tsx"], "sourcesContent": ["import type { Modules } from '@strapi/types';\nimport type { MessageDescriptor } from 'react-intl';\n\n/**\n * @description designed to be parsed by formatMessage from react-intl\n * then passed to a Select component.\n */\ninterface FilterOption {\n  value: Modules.EntityService.Params.Filters.Operator.Where;\n  label: MessageDescriptor;\n}\n\n/**\n * @description these are shared by everyone\n */\nconst BASE_FILTERS = [\n  {\n    label: { id: 'components.FilterOptions.FILTER_TYPES.$eq', defaultMessage: 'is' },\n    value: '$eq',\n  },\n  {\n    label: { id: 'components.FilterOptions.FILTER_TYPES.$ne', defaultMessage: 'is not' },\n    value: '$ne',\n  },\n  {\n    label: {\n      id: 'components.FilterOptions.FILTER_TYPES.$null',\n      defaultMessage: 'is null',\n    },\n    value: '$null',\n  },\n  {\n    label: {\n      id: 'components.FilterOptions.FILTER_TYPES.$notNull',\n      defaultMessage: 'is not null',\n    },\n    value: '$notNull',\n  },\n] satisfies FilterOption[];\n\n/**\n * @description typically performed on attributes that are numerical incl. dates.\n */\nconst NUMERIC_FILTERS = [\n  {\n    label: {\n      id: 'components.FilterOptions.FILTER_TYPES.$gt',\n      defaultMessage: 'is greater than',\n    },\n    value: '$gt',\n  },\n  {\n    label: {\n      id: 'components.FilterOptions.FILTER_TYPES.$gte',\n      defaultMessage: 'is greater than or equal to',\n    },\n    value: '$gte',\n  },\n  {\n    label: {\n      id: 'components.FilterOptions.FILTER_TYPES.$lt',\n      defaultMessage: 'is less than',\n    },\n    value: '$lt',\n  },\n  {\n    label: {\n      id: 'components.FilterOptions.FILTER_TYPES.$lte',\n      defaultMessage: 'is less than or equal to',\n    },\n    value: '$lte',\n  },\n] satisfies FilterOption[];\n\nconst IS_SENSITIVE_FILTERS = [\n  {\n    label: {\n      id: 'components.FilterOptions.FILTER_TYPES.$eqi',\n      defaultMessage: 'is (case insensitive)',\n    },\n    value: '$eqi',\n  },\n\n  {\n    label: {\n      id: 'components.FilterOptions.FILTER_TYPES.$nei',\n      defaultMessage: 'is not (case insensitive)',\n    },\n    value: '$nei',\n  },\n] satisfies FilterOption[];\n\n/**\n * @description typically performed on attributes that are strings for partial looking.\n */\nconst CONTAINS_FILTERS = [\n  {\n    label: {\n      id: 'components.FilterOptions.FILTER_TYPES.$contains',\n      defaultMessage: 'contains',\n    },\n    value: '$contains',\n  },\n  {\n    label: {\n      id: 'components.FilterOptions.FILTER_TYPES.$containsi',\n      defaultMessage: 'contains (case insensitive)',\n    },\n    value: '$containsi',\n  },\n  {\n    label: {\n      id: 'components.FilterOptions.FILTER_TYPES.$notContains',\n      defaultMessage: 'not contains',\n    },\n    value: '$notContains',\n  },\n  {\n    label: {\n      id: 'components.FilterOptions.FILTER_TYPES.$notContainsi',\n      defaultMessage: 'not contains (case insensitive)',\n    },\n    value: '$notContainsi',\n  },\n] satisfies FilterOption[];\n\n/**\n * @description only used on string attributes.\n */\nconst STRING_PARSE_FILTERS = [\n  {\n    label: {\n      id: 'components.FilterOptions.FILTER_TYPES.$startsWith',\n      defaultMessage: 'starts with',\n    },\n    value: '$startsWith',\n  },\n  {\n    label: {\n      id: 'components.FilterOptions.FILTER_TYPES.$startsWithi',\n      defaultMessage: 'starts with (case insensitive)',\n    },\n    value: '$startsWithi',\n  },\n  {\n    label: {\n      id: 'components.FilterOptions.FILTER_TYPES.$endsWith',\n      defaultMessage: 'ends with',\n    },\n    value: '$endsWith',\n  },\n  {\n    label: {\n      id: 'components.FilterOptions.FILTER_TYPES.$endsWithi',\n      defaultMessage: 'ends with (case insensitive)',\n    },\n    value: '$endsWithi',\n  },\n] satisfies FilterOption[];\n\nconst FILTERS_WITH_NO_VALUE = ['$null', '$notNull'];\n\nexport {\n  BASE_FILTERS,\n  NUMERIC_FILTERS,\n  IS_SENSITIVE_FILTERS,\n  CONTAINS_FILTERS,\n  STRING_PARSE_FILTERS,\n  FILTERS_WITH_NO_VALUE,\n};\nexport type { FilterOption };\n", "import * as React from 'react';\n\nimport { Box, Button, Flex, Popover, Tag } from '@strapi/design-system';\nimport { Plus, Filter as FilterIcon, Cross } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\n\nimport {\n  BASE_FILTERS,\n  CONTAINS_FILTERS,\n  FilterOption,\n  IS_SENSITIVE_FILTERS,\n  NUMERIC_FILTERS,\n  STRING_PARSE_FILTERS,\n  FILTERS_WITH_NO_VALUE,\n} from '../constants/filters';\nimport { useControllableState } from '../hooks/useControllableState';\nimport { useQueryParams } from '../hooks/useQueryParams';\n\nimport { createContext } from './Context';\nimport { Form, InputProps } from './Form';\nimport { InputRenderer } from './FormInputs/Renderer';\n\nimport type { Schema } from '@strapi/types';\n\n/* -------------------------------------------------------------------------------------------------\n * Root\n * -----------------------------------------------------------------------------------------------*/\n\ninterface FilterFormData {\n  name: string;\n  filter: string;\n  value?: string;\n}\n\ninterface FitlersContextValue {\n  disabled: boolean;\n  onChange: (data: FilterFormData) => void;\n  options: Filters.Filter[];\n  setOpen: (open: boolean) => void;\n}\n\nconst [FiltersProvider, useFilters] = createContext<FitlersContextValue>('Filters');\n\ninterface RootProps extends Partial<FitlersContextValue>, Popover.Props {\n  children: React.ReactNode;\n}\n\nconst Root = ({\n  children,\n  disabled = false,\n  onChange,\n  options = [],\n  onOpenChange,\n  open: openProp,\n  defaultOpen,\n  ...restProps\n}: RootProps) => {\n  const handleChange = (data: FilterFormData) => {\n    if (onChange) {\n      onChange(data);\n    }\n  };\n  const [open = false, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen,\n    onChange: onOpenChange,\n  });\n\n  return (\n    <Popover.Root open={open} onOpenChange={setOpen} {...restProps}>\n      <FiltersProvider\n        setOpen={setOpen}\n        disabled={disabled}\n        onChange={handleChange}\n        options={options}\n      >\n        {children}\n      </FiltersProvider>\n    </Popover.Root>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Trigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst Trigger = React.forwardRef<HTMLButtonElement, Filters.TriggerProps>(\n  ({ label }, forwardedRef) => {\n    const { formatMessage } = useIntl();\n    const disabled = useFilters('Trigger', ({ disabled }) => disabled);\n\n    return (\n      <Popover.Trigger>\n        <Button\n          variant=\"tertiary\"\n          ref={forwardedRef}\n          startIcon={<FilterIcon />}\n          size=\"S\"\n          disabled={disabled}\n        >\n          {label || formatMessage({ id: 'app.utils.filters', defaultMessage: 'Filters' })}\n        </Button>\n      </Popover.Trigger>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * Popover\n * -----------------------------------------------------------------------------------------------*/\n\nconst PopoverImpl = () => {\n  const [{ query }, setQuery] = useQueryParams<Filters.Query>();\n  const { formatMessage } = useIntl();\n  const options = useFilters('Popover', ({ options }) => options);\n  const onChange = useFilters('Popover', ({ onChange }) => onChange);\n  const setOpen = useFilters('Popover', ({ setOpen }) => setOpen);\n\n  if (options.length === 0) {\n    return null;\n  }\n\n  const handleSubmit = (data: FilterFormData) => {\n    const value = FILTERS_WITH_NO_VALUE.includes(data.filter)\n      ? 'true'\n      : encodeURIComponent(data.value ?? '');\n\n    if (!value) {\n      return;\n    }\n\n    if (onChange) {\n      onChange(data);\n    }\n\n    /**\n     * There will ALWAYS be an option because we use the options to create the form data.\n     */\n    const fieldOptions = options.find((filter) => filter.name === data.name)!;\n\n    /**\n     * If the filter is a relation, we need to nest the filter object,\n     * we filter based on the mainField of the relation, if there is no mainField, we use the id.\n     * At the end, we pass the operator & value. This value _could_ look like:\n     * ```json\n     * {\n     *  \"$eq\": \"1\",\n     * }\n     * ```\n     */\n    const operatorValuePairing = {\n      [data.filter]: value,\n    };\n\n    const newFilterQuery = {\n      ...query.filters,\n      $and: [\n        ...(query.filters?.$and ?? []),\n        {\n          [data.name]:\n            fieldOptions.type === 'relation'\n              ? {\n                  [fieldOptions.mainField?.name ?? 'id']: operatorValuePairing,\n                }\n              : operatorValuePairing,\n        },\n      ],\n    };\n\n    setQuery({ filters: newFilterQuery, page: 1 });\n    setOpen(false);\n  };\n\n  return (\n    <Popover.Content>\n      <Box padding={3}>\n        <Form\n          method=\"POST\"\n          initialValues={\n            {\n              name: options[0]?.name,\n              filter: BASE_FILTERS[0].value,\n            } satisfies FilterFormData\n          }\n          onSubmit={handleSubmit}\n        >\n          {({ values: formValues, modified, isSubmitting }) => {\n            const filter = options.find((filter) => filter.name === formValues.name);\n            const Input = filter?.input || InputRenderer;\n            return (\n              <Flex direction=\"column\" alignItems=\"stretch\" gap={2} style={{ minWidth: 184 }}>\n                {[\n                  {\n                    ['aria-label']: formatMessage({\n                      id: 'app.utils.select-field',\n                      defaultMessage: 'Select field',\n                    }),\n                    name: 'name',\n                    options: options.map((filter) => ({\n                      label: filter.label,\n                      value: filter.name,\n                    })),\n                    placholder: formatMessage({\n                      id: 'app.utils.select-field',\n                      defaultMessage: 'Select field',\n                    }),\n                    type: 'enumeration' as const,\n                  },\n                  {\n                    ['aria-label']: formatMessage({\n                      id: 'app.utils.select-filter',\n                      defaultMessage: 'Select filter',\n                    }),\n                    name: 'filter',\n                    options:\n                      filter?.operators ||\n                      getFilterList(filter).map((opt) => ({\n                        label: formatMessage(opt.label),\n                        value: opt.value,\n                      })),\n                    placeholder: formatMessage({\n                      id: 'app.utils.select-filter',\n                      defaultMessage: 'Select filter',\n                    }),\n                    type: 'enumeration' as const,\n                  },\n                ].map((field) => (\n                  <InputRenderer key={field.name} {...field} />\n                ))}\n                {filter &&\n                formValues.filter &&\n                formValues.filter !== '$null' &&\n                formValues.filter !== '$notNull' ? (\n                  <Input\n                    {...filter}\n                    label={null}\n                    aria-label={filter.label}\n                    name=\"value\"\n                    // @ts-expect-error – if type is `custom` then `Input` will be a custom component.\n                    type={filter.mainField?.type ?? filter.type}\n                  />\n                ) : null}\n                <Button\n                  disabled={!modified || isSubmitting}\n                  size=\"L\"\n                  variant=\"secondary\"\n                  startIcon={<Plus />}\n                  type=\"submit\"\n                  fullWidth\n                >\n                  {formatMessage({ id: 'app.utils.add-filter', defaultMessage: 'Add filter' })}\n                </Button>\n              </Flex>\n            );\n          }}\n        </Form>\n      </Box>\n    </Popover.Content>\n  );\n};\n\n/**\n * Depending on the selected field find the possible filters to apply\n */\nconst getFilterList = (filter?: Filters.Filter): FilterOption[] => {\n  if (!filter) {\n    return [];\n  }\n\n  const type = filter.mainField?.type ? filter.mainField.type : filter.type;\n\n  switch (type) {\n    case 'email':\n    case 'text':\n    case 'string': {\n      return [\n        ...BASE_FILTERS,\n        ...IS_SENSITIVE_FILTERS,\n        ...CONTAINS_FILTERS,\n        ...STRING_PARSE_FILTERS,\n      ];\n    }\n\n    case 'float':\n    case 'integer':\n    case 'biginteger':\n    case 'decimal': {\n      return [...BASE_FILTERS, ...NUMERIC_FILTERS];\n    }\n    case 'time':\n    case 'date': {\n      return [...BASE_FILTERS, ...NUMERIC_FILTERS, ...CONTAINS_FILTERS];\n    }\n\n    case 'datetime': {\n      return [...BASE_FILTERS, ...NUMERIC_FILTERS];\n    }\n\n    case 'enumeration': {\n      return BASE_FILTERS;\n    }\n\n    default:\n      return [...BASE_FILTERS, ...IS_SENSITIVE_FILTERS];\n  }\n};\n\n/* -------------------------------------------------------------------------------------------------\n * List\n * -----------------------------------------------------------------------------------------------*/\n\nconst List = () => {\n  const [{ query }, setQuery] = useQueryParams<Filters.Query>();\n\n  const options = useFilters('List', ({ options }) => options);\n\n  const handleClick = (data: FilterFormData) => {\n    /**\n     * Check the name, operator and value to see if it already exists in the query\n     * if it does, remove it.\n     */\n    const nextFilters = (query?.filters?.$and ?? []).filter((filter) => {\n      const [attributeName] = Object.keys(filter);\n      if (attributeName !== data.name) {\n        return true;\n      }\n\n      const { type, mainField } = options.find(({ name }) => name === attributeName)!;\n\n      if (type === 'relation') {\n        const filterObj = filter[attributeName][mainField?.name ?? 'id'];\n\n        if (typeof filterObj === 'object') {\n          const [operator] = Object.keys(filterObj);\n          const value = filterObj[operator];\n\n          return !(operator === data.filter && value === data.value);\n        }\n\n        return true;\n      } else {\n        const filterObj = filter[attributeName];\n        const [operator] = Object.keys(filterObj);\n        const value = filterObj[operator];\n\n        return !(operator === data.filter && value === data.value);\n      }\n    });\n\n    setQuery({ filters: { $and: nextFilters }, page: 1 });\n  };\n\n  if (!query?.filters?.$and?.length) {\n    return null;\n  }\n\n  return (\n    <>\n      {query?.filters?.$and?.map((queryFilter) => {\n        const [attributeName] = Object.keys(queryFilter);\n        const filter = options.find(({ name }) => name === attributeName);\n        const filterObj = queryFilter[attributeName];\n\n        if (!filter || typeof filterObj !== 'object' || filterObj === null) {\n          return null;\n        }\n\n        if (filter.type === 'relation') {\n          const modelFilter = filterObj[filter.mainField?.name ?? 'id'];\n\n          if (typeof modelFilter === 'object') {\n            const [operator] = Object.keys(modelFilter);\n            const value = modelFilter[operator];\n            return (\n              <AttributeTag\n                key={`${attributeName}-${operator}-${value}`}\n                {...filter}\n                onClick={handleClick}\n                operator={operator}\n                value={value}\n              />\n            );\n          }\n\n          return null;\n        } else {\n          const [operator] = Object.keys(filterObj);\n          const value = filterObj[operator];\n\n          /**\n           * Something has gone wrong here, because the attribute is not a relation\n           * but we have a nested filter object.\n           */\n          if (typeof value === 'object') {\n            return null;\n          }\n\n          return (\n            <AttributeTag\n              key={`${attributeName}-${operator}-${value}`}\n              {...filter}\n              onClick={handleClick}\n              operator={operator}\n              value={value}\n            />\n          );\n        }\n      })}\n    </>\n  );\n};\n\ninterface AttributeTagProps extends Filters.Filter {\n  onClick: (data: FilterFormData) => void;\n  operator: string;\n  value: string;\n}\n\nconst AttributeTag = ({\n  input,\n  label,\n  mainField,\n  name,\n  onClick,\n  operator,\n  options,\n  value,\n  ...filter\n}: AttributeTagProps) => {\n  const { formatMessage, formatDate, formatTime, formatNumber } = useIntl();\n\n  const handleClick = () => {\n    onClick({ name, value, filter: operator });\n  };\n\n  const type = mainField?.type ? mainField.type : filter.type;\n\n  let formattedValue: string = value;\n\n  switch (type) {\n    case 'date':\n      formattedValue = formatDate(value, { dateStyle: 'full' });\n      break;\n    case 'datetime':\n      formattedValue = formatDate(value, { dateStyle: 'full', timeStyle: 'short' });\n      break;\n    case 'time':\n      const [hour, minute] = value.split(':');\n      const date = new Date();\n      date.setHours(Number(hour));\n      date.setMinutes(Number(minute));\n\n      formattedValue = formatTime(date, {\n        hour: 'numeric',\n        minute: 'numeric',\n      });\n      break;\n    case 'float':\n    case 'integer':\n    case 'biginteger':\n    case 'decimal':\n      formattedValue = formatNumber(Number(value));\n      break;\n  }\n\n  // Handle custom input\n  if (input && options) {\n    // If the custom input has an options array, find the option with a customValue matching the query value\n    const selectedOption = options.find((option) => {\n      return (typeof option === 'string' ? option : option.value) === value;\n    });\n\n    formattedValue = selectedOption\n      ? typeof selectedOption === 'string'\n        ? selectedOption\n        : (selectedOption.label ?? selectedOption.value)\n      : value;\n  }\n\n  const content = `${label} ${formatMessage({\n    id: `components.FilterOptions.FILTER_TYPES.${operator}`,\n    defaultMessage: operator,\n  })} ${operator !== '$null' && operator !== '$notNull' ? formattedValue : ''}`;\n\n  return (\n    <Tag padding={1} onClick={handleClick} icon={<Cross />}>\n      {content}\n    </Tag>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * EXPORTS\n * -----------------------------------------------------------------------------------------------*/\n\nconst Filters = {\n  List,\n  Popover: PopoverImpl,\n  Root,\n  Trigger,\n};\n\ninterface MainField {\n  name: string;\n  type: Schema.Attribute.Kind | 'custom';\n}\n\n// eslint-disable-next-line @typescript-eslint/no-namespace\nnamespace Filters {\n  export interface Filter {\n    input?: React.ComponentType<ValueInputProps>;\n    label: string;\n    /**\n     * the name of the attribute we use to display the actual name e.g. relations\n     * are just ids, so we use the mainField to display something meaninginful by\n     * looking at the target's schema\n     */\n    mainField?: MainField;\n    name: string;\n    operators?: Array<{\n      label: string;\n      value: string;\n    }>;\n    options?: Array<{ label?: string; value: string }> | string[];\n    type: InputProps['type'] | 'relation' | 'custom';\n  }\n\n  export interface ValueInputProps extends Omit<Filter, 'label'> {\n    ['aria-label']: string;\n  }\n\n  export type Props = RootProps;\n\n  export interface TriggerProps {\n    label?: string;\n  }\n\n  export interface Query {\n    filters?: {\n      /**\n       * Typically, a filter will be:\n       * ```ts\n       * {\n       *  [attributeName]: {\n       *    [operator]: value\n       *  }\n       * }\n       * ```\n       * However, for relation items it becomes more nested.\n       * ```ts\n       * {\n       *  [attributeName]: {\n       *    [relationTargetAttribute]: {\n       *     [operator]: value\n       *    }\n       *  }\n       * }\n       * ```\n       */\n      $and?: Array<Record<string, Record<string, string | Record<string, string>>>>;\n    };\n    page?: number;\n  }\n}\n\nexport { Filters };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcC,IACKA,eAAe;EACnB;IACEC,OAAO;MAAEC,IAAI;MAA6CC,gBAAgB;IAAK;IAC/EC,OAAO;EACT;EACA;IACEH,OAAO;MAAEC,IAAI;MAA6CC,gBAAgB;IAAS;IACnFC,OAAO;EACT;EACA;IACEH,OAAO;MACLC,IAAI;MACJC,gBAAgB;IAClB;IACAC,OAAO;EACT;EACA;IACEH,OAAO;MACLC,IAAI;MACJC,gBAAgB;IAClB;IACAC,OAAO;EACT;AACD;AAIA,IACKC,kBAAkB;EACtB;IACEJ,OAAO;MACLC,IAAI;MACJC,gBAAgB;IAClB;IACAC,OAAO;EACT;EACA;IACEH,OAAO;MACLC,IAAI;MACJC,gBAAgB;IAClB;IACAC,OAAO;EACT;EACA;IACEH,OAAO;MACLC,IAAI;MACJC,gBAAgB;IAClB;IACAC,OAAO;EACT;EACA;IACEH,OAAO;MACLC,IAAI;MACJC,gBAAgB;IAClB;IACAC,OAAO;EACT;AACD;AAED,IAAME,uBAAuB;EAC3B;IACEL,OAAO;MACLC,IAAI;MACJC,gBAAgB;IAClB;IACAC,OAAO;EACT;EAEA;IACEH,OAAO;MACLC,IAAI;MACJC,gBAAgB;IAClB;IACAC,OAAO;EACT;AACD;AAIA,IACKG,mBAAmB;EACvB;IACEN,OAAO;MACLC,IAAI;MACJC,gBAAgB;IAClB;IACAC,OAAO;EACT;EACA;IACEH,OAAO;MACLC,IAAI;MACJC,gBAAgB;IAClB;IACAC,OAAO;EACT;EACA;IACEH,OAAO;MACLC,IAAI;MACJC,gBAAgB;IAClB;IACAC,OAAO;EACT;EACA;IACEH,OAAO;MACLC,IAAI;MACJC,gBAAgB;IAClB;IACAC,OAAO;EACT;AACD;AAIA,IACKI,uBAAuB;EAC3B;IACEP,OAAO;MACLC,IAAI;MACJC,gBAAgB;IAClB;IACAC,OAAO;EACT;EACA;IACEH,OAAO;MACLC,IAAI;MACJC,gBAAgB;IAClB;IACAC,OAAO;EACT;EACA;IACEH,OAAO;MACLC,IAAI;MACJC,gBAAgB;IAClB;IACAC,OAAO;EACT;EACA;IACEH,OAAO;MACLC,IAAI;MACJC,gBAAgB;IAClB;IACAC,OAAO;EACT;AACD;AAED,IAAMK,wBAAwB;EAAC;EAAS;AAAW;;;ACvHnD,IAAM,CAACC,iBAAiBC,UAAW,IAAGC,cAAmC,SAAA;AAMzE,IAAMC,OAAO,CAAC,EACZC,UACAC,WAAW,OACXC,UACAC,UAAU,CAAA,GACVC,cACAC,MAAMC,UACNC,aACA,GAAGC,UACO,MAAA;AACV,QAAMC,eAAe,CAACC,SAAAA;AACpB,QAAIR,UAAU;AACZA,eAASQ,IAAAA;IACX;EACF;AACA,QAAM,CAACL,OAAO,OAAOM,OAAAA,IAAWC,qBAAqB;IACnDC,MAAMP;IACNQ,aAAaP;IACbL,UAAUE;EACZ,CAAA;AAEA,aACEW,wBAACC,QAAQjB,MAAI;IAACM;IAAYD,cAAcO;IAAU,GAAGH;IACnD,cAAAO,wBAACnB,iBAAAA;MACCe;MACAV;MACAC,UAAUO;MACVN;MAECH;;;AAIT;AAMA,IAAMiB,UAAgBC,iBACpB,CAAC,EAAEC,MAAK,GAAIC,iBAAAA;AACV,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAMrB,WAAWJ,WAAW,WAAW,CAAC,EAAEI,UAAAA,UAAQ,MAAOA,SAAAA;AAEzD,aACEc,wBAACC,QAAQC,SAAO;IACd,cAAAF,wBAACQ,QAAAA;MACCC,SAAQ;MACRC,KAAKL;MACLM,eAAWX,wBAACY,eAAAA,CAAAA,CAAAA;MACZC,MAAK;MACL3B;MAECkB,UAAAA,SAASE,cAAc;QAAEQ,IAAI;QAAqBC,gBAAgB;MAAU,CAAA;;;AAIrF,CAAA;AAOF,IAAMC,cAAc,MAAA;;AAClB,QAAM,CAAC,EAAEC,MAAK,GAAIC,QAAAA,IAAYC,eAAAA;AAC9B,QAAM,EAAEb,cAAa,IAAKC,QAAAA;AAC1B,QAAMnB,UAAUN,WAAW,WAAW,CAAC,EAAEM,SAAAA,SAAO,MAAOA,QAAAA;AACvD,QAAMD,WAAWL,WAAW,WAAW,CAAC,EAAEK,UAAAA,UAAQ,MAAOA,SAAAA;AACzD,QAAMS,UAAUd,WAAW,WAAW,CAAC,EAAEc,SAAAA,SAAO,MAAOA,QAAAA;AAEvD,MAAIR,QAAQgC,WAAW,GAAG;AACxB,WAAO;EACT;AAEA,QAAMC,eAAe,CAAC1B,SAAAA;;AACpB,UAAM2B,QAAQC,sBAAsBC,SAAS7B,KAAK8B,MAAM,IACpD,SACAC,mBAAmB/B,KAAK2B,SAAS,EAAA;AAErC,QAAI,CAACA,OAAO;AACV;IACF;AAEA,QAAInC,UAAU;AACZA,eAASQ,IAAAA;IACX;AAKA,UAAMgC,eAAevC,QAAQwC,KAAK,CAACH,WAAWA,OAAOI,SAASlC,KAAKkC,IAAI;AAYvE,UAAMC,uBAAuB;MAC3B,CAACnC,KAAK8B,MAAM,GAAGH;IACjB;AAEA,UAAMS,iBAAiB;MACrB,GAAGd,MAAMe;MACTC,MAAM;aACAhB,MAAAA,MAAMe,YAANf,gBAAAA,IAAegB,SAAQ,CAAA;QAC3B;UACE,CAACtC,KAAKkC,IAAI,GACRF,aAAaO,SAAS,aAClB;YACE,GAACP,kBAAaQ,cAAbR,mBAAwBE,SAAQ,IAAA,GAAOC;cAE1CA;QACR;MACD;IACH;AAEAZ,aAAS;MAAEc,SAASD;MAAgBK,MAAM;IAAE,CAAA;AAC5CxC,YAAQ,KAAA;EACV;AAEA,aACEI,wBAACC,QAAQoC,SAAO;IACd,cAAArC,wBAACsC,KAAAA;MAAIC,SAAS;MACZ,cAAAvC,wBAACwC,MAAAA;QACCC,QAAO;QACPC,eACE;UACEb,OAAMzC,aAAQ,CAAA,MAARA,mBAAYyC;UAClBJ,QAAQkB,aAAa,CAAE,EAACrB;QAC1B;QAEFsB,UAAUvB;QAET,UAAA,CAAC,EAAEwB,QAAQC,YAAYC,UAAUC,aAAY,MAAE;;AAC9C,gBAAMvB,SAASrC,QAAQwC,KAAK,CAACH,YAAWA,QAAOI,SAASiB,WAAWjB,IAAI;AACvE,gBAAMoB,SAAQxB,iCAAQyB,UAASC;AAC/B,qBACEC,yBAACC,MAAAA;YAAKC,WAAU;YAASC,YAAW;YAAUC,KAAK;YAAGC,OAAO;cAAEC,UAAU;YAAI;;cAC1E;gBACC;kBACE,CAAC,YAAA,GAAepD,cAAc;oBAC5BQ,IAAI;oBACJC,gBAAgB;kBAClB,CAAA;kBACAc,MAAM;kBACNzC,SAASA,QAAQuE,IAAI,CAAClC,aAAY;oBAChCrB,OAAOqB,QAAOrB;oBACdkB,OAAOG,QAAOI;oBAChB;kBACA+B,YAAYtD,cAAc;oBACxBQ,IAAI;oBACJC,gBAAgB;kBAClB,CAAA;kBACAmB,MAAM;gBACR;gBACA;kBACE,CAAC,YAAA,GAAe5B,cAAc;oBAC5BQ,IAAI;oBACJC,gBAAgB;kBAClB,CAAA;kBACAc,MAAM;kBACNzC,UACEqC,iCAAQoC,cACRC,cAAcrC,MAAAA,EAAQkC,IAAI,CAACI,SAAS;oBAClC3D,OAAOE,cAAcyD,IAAI3D,KAAK;oBAC9BkB,OAAOyC,IAAIzC;oBACb;kBACF0C,aAAa1D,cAAc;oBACzBQ,IAAI;oBACJC,gBAAgB;kBAClB,CAAA;kBACAmB,MAAM;gBACR;cACD,EAACyB,IAAI,CAACM,cACLjE,wBAACmD,uBAAAA;gBAAgC,GAAGc;cAAhBA,GAAAA,MAAMpC,IAAI,CAAA;cAE/BJ,UACDqB,WAAWrB,UACXqB,WAAWrB,WAAW,WACtBqB,WAAWrB,WAAW,iBACpBzB,wBAACiD,OAAAA;gBACE,GAAGxB;gBACJrB,OAAO;gBACP8D,cAAYzC,OAAOrB;gBACnByB,MAAK;;gBAELK,QAAMT,MAAAA,OAAOU,cAAPV,gBAAAA,IAAkBS,SAAQT,OAAOS;cAEvC,CAAA,IAAA;kBACJlC,wBAACQ,QAAAA;gBACCtB,UAAU,CAAC6D,YAAYC;gBACvBnC,MAAK;gBACLJ,SAAQ;gBACRE,eAAWX,wBAACmE,eAAAA,CAAAA,CAAAA;gBACZjC,MAAK;gBACLkC,WAAS;0BAER9D,cAAc;kBAAEQ,IAAI;kBAAwBC,gBAAgB;gBAAa,CAAA;;;;QAIlF;;;;AAKV;AAKA,IAAM+C,gBAAgB,CAACrC,WAAAA;;AACrB,MAAI,CAACA,QAAQ;AACX,WAAO,CAAA;EACT;AAEA,QAAMS,SAAOT,YAAOU,cAAPV,mBAAkBS,QAAOT,OAAOU,UAAUD,OAAOT,OAAOS;AAErE,UAAQA,MAAAA;IACN,KAAK;IACL,KAAK;IACL,KAAK,UAAU;AACb,aAAO;QACFS,GAAAA;QACA0B,GAAAA;QACAC,GAAAA;QACAC,GAAAA;MACJ;IACH;IAEA,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK,WAAW;AACd,aAAO;QAAI5B,GAAAA;QAAiB6B,GAAAA;MAAgB;IAC9C;IACA,KAAK;IACL,KAAK,QAAQ;AACX,aAAO;QAAI7B,GAAAA;QAAiB6B,GAAAA;QAAoBF,GAAAA;MAAiB;IACnE;IAEA,KAAK,YAAY;AACf,aAAO;QAAI3B,GAAAA;QAAiB6B,GAAAA;MAAgB;IAC9C;IAEA,KAAK,eAAe;AAClB,aAAO7B;IACT;IAEA;AACE,aAAO;QAAIA,GAAAA;QAAiB0B,GAAAA;MAAqB;EACrD;AACF;AAMA,IAAMI,OAAO,MAAA;;AACX,QAAM,CAAC,EAAExD,MAAK,GAAIC,QAAAA,IAAYC,eAAAA;AAE9B,QAAM/B,UAAUN,WAAW,QAAQ,CAAC,EAAEM,SAAAA,SAAO,MAAOA,QAAAA;AAEpD,QAAMsF,cAAc,CAAC/E,SAAAA;;AAKnB,UAAMgF,iBAAe1D,MAAAA,+BAAOe,YAAPf,gBAAAA,IAAgBgB,SAAQ,CAAA,GAAIR,OAAO,CAACA,WAAAA;AACvD,YAAM,CAACmD,aAAAA,IAAiBC,OAAOC,KAAKrD,MAAAA;AACpC,UAAImD,kBAAkBjF,KAAKkC,MAAM;AAC/B,eAAO;MACT;AAEA,YAAM,EAAEK,MAAMC,UAAS,IAAK/C,QAAQwC,KAAK,CAAC,EAAEC,KAAI,MAAOA,SAAS+C,aAAAA;AAEhE,UAAI1C,SAAS,YAAY;AACvB,cAAM6C,YAAYtD,OAAOmD,aAAAA,GAAezC,uCAAWN,SAAQ,IAAK;AAEhE,YAAI,OAAOkD,cAAc,UAAU;AACjC,gBAAM,CAACC,QAAAA,IAAYH,OAAOC,KAAKC,SAAAA;AAC/B,gBAAMzD,QAAQyD,UAAUC,QAAS;AAEjC,iBAAO,EAAEA,aAAarF,KAAK8B,UAAUH,UAAU3B,KAAK2B;QACtD;AAEA,eAAO;aACF;AACL,cAAMyD,YAAYtD,OAAOmD,aAAc;AACvC,cAAM,CAACI,QAAAA,IAAYH,OAAOC,KAAKC,SAAAA;AAC/B,cAAMzD,QAAQyD,UAAUC,QAAS;AAEjC,eAAO,EAAEA,aAAarF,KAAK8B,UAAUH,UAAU3B,KAAK2B;MACtD;IACF,CAAA;AAEAJ,aAAS;MAAEc,SAAS;QAAEC,MAAM0C;MAAY;MAAGvC,MAAM;IAAE,CAAA;EACrD;AAEA,MAAI,GAACnB,0CAAOe,YAAPf,mBAAgBgB,SAAhBhB,mBAAsBG,SAAQ;AACjC,WAAO;EACT;AAEA,aACEpB,wBAAAiF,6BAAA;eACGhE,0CAAOe,YAAPf,mBAAgBgB,SAAhBhB,mBAAsB0C,IAAI,CAACuB,gBAAAA;;AAC1B,YAAM,CAACN,aAAAA,IAAiBC,OAAOC,KAAKI,WAAAA;AACpC,YAAMzD,SAASrC,QAAQwC,KAAK,CAAC,EAAEC,KAAI,MAAOA,SAAS+C,aAAAA;AACnD,YAAMG,YAAYG,YAAYN,aAAc;AAE5C,UAAI,CAACnD,UAAU,OAAOsD,cAAc,YAAYA,cAAc,MAAM;AAClE,eAAO;MACT;AAEA,UAAItD,OAAOS,SAAS,YAAY;AAC9B,cAAMiD,cAAcJ,YAAUtD,MAAAA,OAAOU,cAAPV,gBAAAA,IAAkBI,SAAQ,IAAK;AAE7D,YAAI,OAAOsD,gBAAgB,UAAU;AACnC,gBAAM,CAACH,QAAAA,IAAYH,OAAOC,KAAKK,WAAAA;AAC/B,gBAAM7D,QAAQ6D,YAAYH,QAAS;AACnC,qBACEhF,wBAACoF,cAAAA;YAEE,GAAG3D;YACJ4D,SAASX;YACTM;YACA1D;aAJK,GAAGsD,aAAAA,IAAiBI,QAAAA,IAAY1D,KAAAA,EAAO;QAOlD;AAEA,eAAO;aACF;AACL,cAAM,CAAC0D,QAAAA,IAAYH,OAAOC,KAAKC,SAAAA;AAC/B,cAAMzD,QAAQyD,UAAUC,QAAS;AAMjC,YAAI,OAAO1D,UAAU,UAAU;AAC7B,iBAAO;QACT;AAEA,mBACEtB,wBAACoF,cAAAA;UAEE,GAAG3D;UACJ4D,SAASX;UACTM;UACA1D;WAJK,GAAGsD,aAAAA,IAAiBI,QAAAA,IAAY1D,KAAAA,EAAO;MAOlD;IACF;;AAGN;AAQA,IAAM8D,eAAe,CAAC,EACpBlC,OACA9C,OACA+B,WACAN,MACAwD,SACAL,UACA5F,SACAkC,OACA,GAAGG,OACe,MAAA;AAClB,QAAM,EAAEnB,eAAegF,YAAYC,YAAYC,aAAY,IAAKjF,QAAAA;AAEhE,QAAMmE,cAAc,MAAA;AAClBW,YAAQ;MAAExD;MAAMP;MAAOG,QAAQuD;IAAS,CAAA;EAC1C;AAEA,QAAM9C,QAAOC,uCAAWD,QAAOC,UAAUD,OAAOT,OAAOS;AAEvD,MAAIuD,iBAAyBnE;AAE7B,UAAQY,MAAAA;IACN,KAAK;AACHuD,uBAAiBH,WAAWhE,OAAO;QAAEoE,WAAW;MAAO,CAAA;AACvD;IACF,KAAK;AACHD,uBAAiBH,WAAWhE,OAAO;QAAEoE,WAAW;QAAQC,WAAW;MAAQ,CAAA;AAC3E;IACF,KAAK;AACH,YAAM,CAACC,MAAMC,MAAAA,IAAUvE,MAAMwE,MAAM,GAAA;AACnC,YAAMC,OAAO,oBAAIC,KAAAA;AACjBD,WAAKE,SAASC,OAAON,IAAAA,CAAAA;AACrBG,WAAKI,WAAWD,OAAOL,MAAAA,CAAAA;AAEvBJ,uBAAiBF,WAAWQ,MAAM;QAChCH,MAAM;QACNC,QAAQ;MACV,CAAA;AACA;IACF,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;AACHJ,uBAAiBD,aAAaU,OAAO5E,KAAAA,CAAAA;AACrC;EACJ;AAGA,MAAI4B,SAAS9D,SAAS;AAEpB,UAAMgH,iBAAiBhH,QAAQwC,KAAK,CAACyE,WAAAA;AACnC,cAAQ,OAAOA,WAAW,WAAWA,SAASA,OAAO/E,WAAWA;IAClE,CAAA;AAEAmE,qBAAiBW,iBACb,OAAOA,mBAAmB,WACxBA,iBACCA,eAAehG,SAASgG,eAAe9E,QAC1CA;EACN;AAEA,QAAMgF,UAAU,GAAGlG,KAAM,IAAGE,cAAc;IACxCQ,IAAI,yCAAyCkE,QAAAA;IAC7CjE,gBAAgBiE;GACf,CAAA,IAAGA,aAAa,WAAWA,aAAa,aAAaS,iBAAiB,EAAA;AAEzE,aACEzF,wBAACuG,KAAAA;IAAIhE,SAAS;IAAG8C,SAASX;IAAa8B,UAAMxG,wBAACyG,eAAAA,CAAAA,CAAAA;IAC3CH,UAAAA;;AAGP;AAIkG,IAE5FI,UAAU;EACdjC;EACAxE,SAASe;EACThC;EACAkB;AACF;", "names": ["BASE_FILTERS", "label", "id", "defaultMessage", "value", "NUMERIC_FILTERS", "IS_SENSITIVE_FILTERS", "CONTAINS_FILTERS", "STRING_PARSE_FILTERS", "FILTERS_WITH_NO_VALUE", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useFilters", "createContext", "Root", "children", "disabled", "onChange", "options", "onOpenChange", "open", "openProp", "defaultOpen", "restProps", "handleChange", "data", "<PERSON><PERSON><PERSON>", "useControllableState", "prop", "defaultProp", "_jsx", "Popover", "<PERSON><PERSON>", "forwardRef", "label", "forwardedRef", "formatMessage", "useIntl", "<PERSON><PERSON>", "variant", "ref", "startIcon", "FilterIcon", "size", "id", "defaultMessage", "PopoverImpl", "query", "<PERSON><PERSON><PERSON><PERSON>", "useQueryParams", "length", "handleSubmit", "value", "FILTERS_WITH_NO_VALUE", "includes", "filter", "encodeURIComponent", "fieldOptions", "find", "name", "operatorValuePairing", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "filters", "$and", "type", "mainField", "page", "Content", "Box", "padding", "Form", "method", "initialValues", "BASE_FILTERS", "onSubmit", "values", "formValues", "modified", "isSubmitting", "Input", "input", "InputR<PERSON><PERSON>", "_jsxs", "Flex", "direction", "alignItems", "gap", "style", "min<PERSON><PERSON><PERSON>", "map", "pla<PERSON><PERSON>", "operators", "getFilterList", "opt", "placeholder", "field", "aria-label", "Plus", "fullWidth", "IS_SENSITIVE_FILTERS", "CONTAINS_FILTERS", "STRING_PARSE_FILTERS", "NUMERIC_FILTERS", "List", "handleClick", "nextFilters", "attributeName", "Object", "keys", "filterObj", "operator", "_Fragment", "queryFilter", "modelFilter", "AttributeTag", "onClick", "formatDate", "formatTime", "formatNumber", "formattedValue", "dateStyle", "timeStyle", "hour", "minute", "split", "date", "Date", "setHours", "Number", "setMinutes", "selectedOption", "option", "content", "Tag", "icon", "Cross", "Filters"]}