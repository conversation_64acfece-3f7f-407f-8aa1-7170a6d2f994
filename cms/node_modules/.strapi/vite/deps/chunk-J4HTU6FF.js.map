{"version": 3, "sources": ["../../../@strapi/admin/admin/src/pages/Settings/pages/Webhooks/components/EventsTable.tsx", "../../../@strapi/admin/admin/src/pages/Settings/pages/Webhooks/components/HeadersInput.tsx", "../../../@strapi/admin/admin/src/pages/Settings/pages/Webhooks/components/TriggerContainer.tsx", "../../../@strapi/admin/admin/src/pages/Settings/pages/Webhooks/components/WebhookForm.tsx", "../../../@strapi/admin/admin/src/pages/Settings/pages/Webhooks/EditPage.tsx"], "sourcesContent": ["import { Events } from './Events';\n\nconst EventTableCE = () => {\n  return (\n    <Events.Root>\n      <Events.Headers />\n      <Events.Body />\n    </Events.Root>\n  );\n};\n\nexport { EventTableCE };\n", "import * as React from 'react';\n\nimport {\n  Box,\n  Flex,\n  Grid,\n  TextButton,\n  ComboboxOption,\n  Combobox,\n  ComboboxProps,\n  IconButton,\n  Field as DSField,\n} from '@strapi/design-system';\nimport { Minus, Plus } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\nimport { styled } from 'styled-components';\n\nimport { useField, useForm } from '../../../../../components/Form';\nimport { StringInput } from '../../../../../components/FormInputs/String';\n\nconst AddHeaderButton = styled(TextButton)`\n  cursor: pointer;\n`;\n\n/* -------------------------------------------------------------------------------------------------\n * HeadersInput\n * -----------------------------------------------------------------------------------------------*/\n\ninterface Header {\n  key: HTTPHeaders;\n  value: string;\n}\n\nconst HeadersInput = () => {\n  const { formatMessage } = useIntl();\n\n  const addFieldRow = useForm('HeadersInput', (state) => state.addFieldRow);\n  const removeFieldRow = useForm('HeadersInput', (state) => state.removeFieldRow);\n  const setFieldValue = useForm('HeadersInput', (state) => state.onChange);\n  const { value = [] } = useField<Header[]>('headers');\n\n  const removeRow = (index: number) => {\n    // if we are removing the last row, simply clear it\n    if (value.length === 1) {\n      setFieldValue('headers', [{ key: '', value: '' }]);\n    } else {\n      removeFieldRow('headers', index);\n    }\n  };\n\n  return (\n    <Flex direction=\"column\" alignItems=\"stretch\" gap={1}>\n      <DSField.Label>\n        {formatMessage({\n          id: 'Settings.webhooks.form.headers',\n          defaultMessage: 'Headers',\n        })}\n      </DSField.Label>\n      <Box padding={8} background=\"neutral100\" hasRadius>\n        {value.map((val, index) => {\n          return (\n            <Grid.Root key={`${index}-${JSON.stringify(val.key)}`} gap={4} padding={2}>\n              <Grid.Item col={6} direction=\"column\" alignItems=\"stretch\">\n                <HeaderCombobox\n                  name={`headers.${index}.key`}\n                  aria-label={`row ${index + 1} key`}\n                  label={formatMessage({\n                    id: 'Settings.webhooks.key',\n                    defaultMessage: 'Key',\n                  })}\n                />\n              </Grid.Item>\n              <Grid.Item col={6} direction=\"column\" alignItems=\"stretch\">\n                <Flex alignItems=\"flex-end\" gap={2}>\n                  <Box style={{ flex: 1 }}>\n                    <StringInput\n                      name={`headers.${index}.value`}\n                      aria-label={`row ${index + 1} value`}\n                      label={formatMessage({\n                        id: 'Settings.webhooks.value',\n                        defaultMessage: 'Value',\n                      })}\n                      type=\"string\"\n                    />\n                  </Box>\n                  <IconButton\n                    width=\"4rem\"\n                    height=\"4rem\"\n                    onClick={() => removeRow(index)}\n                    color=\"primary600\"\n                    label={formatMessage(\n                      {\n                        id: 'Settings.webhooks.headers.remove',\n                        defaultMessage: 'Remove header row {number}',\n                      },\n                      { number: index + 1 }\n                    )}\n                    type=\"button\"\n                  >\n                    <Minus width=\"0.8rem\" />\n                  </IconButton>\n                </Flex>\n              </Grid.Item>\n            </Grid.Root>\n          );\n        })}\n        <Box paddingTop={4}>\n          <AddHeaderButton\n            type=\"button\"\n            onClick={() => {\n              addFieldRow('headers', { key: '', value: '' });\n            }}\n            startIcon={<Plus />}\n          >\n            {formatMessage({\n              id: 'Settings.webhooks.create.header',\n              defaultMessage: 'Create new header',\n            })}\n          </AddHeaderButton>\n        </Box>\n      </Box>\n    </Flex>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * HeaderCombobox\n * -----------------------------------------------------------------------------------------------*/\n\ninterface HeaderComboboxProps extends Omit<ComboboxProps, 'children' | 'name'> {\n  name: string;\n  label: string;\n}\n\nconst HeaderCombobox = ({ name, label, ...restProps }: HeaderComboboxProps) => {\n  const [options, setOptions] = React.useState<HTTPHeaders[]>([...HTTP_HEADERS]);\n  const { value: headers } = useField<Header[]>('headers');\n  const field = useField(name);\n\n  React.useEffect(() => {\n    const headerOptions = HTTP_HEADERS.filter(\n      (key) => !headers?.some((header) => header.key !== field.value && header.key === key)\n    );\n\n    setOptions(headerOptions);\n  }, [headers, field.value]);\n\n  const handleChange: ComboboxProps['onChange'] = (value) => {\n    field.onChange(name, value);\n  };\n\n  const handleCreateOption = (value?: string) => {\n    setOptions((prev) => [...prev, value as HTTPHeaders]);\n\n    if (value) {\n      handleChange(value);\n    }\n  };\n\n  return (\n    <DSField.Root name={name} error={field.error}>\n      <DSField.Label>{label}</DSField.Label>\n      <Combobox\n        {...restProps}\n        onClear={() => handleChange('')}\n        onChange={handleChange}\n        onCreateOption={handleCreateOption}\n        placeholder=\"\"\n        creatable\n        value={field.value}\n      >\n        {options.map((key) => (\n          <ComboboxOption value={key} key={key}>\n            {key}\n          </ComboboxOption>\n        ))}\n      </Combobox>\n      <DSField.Error />\n    </DSField.Root>\n  );\n};\n\nconst HTTP_HEADERS = [\n  'A-IM',\n  'Accept',\n  'Accept-Charset',\n  'Accept-Encoding',\n  'Accept-Language',\n  'Accept-Datetime',\n  'Access-Control-Request-Method',\n  'Access-Control-Request-Headers',\n  'Authorization',\n  'Cache-Control',\n  'Connection',\n  'Content-Length',\n  'Content-Type',\n  'Cookie',\n  'Date',\n  'Expect',\n  'Forwarded',\n  'From',\n  'Host',\n  'If-Match',\n  'If-Modified-Since',\n  'If-None-Match',\n  'If-Range',\n  'If-Unmodified-Since',\n  'Max-Forwards',\n  'Origin',\n  'Pragma',\n  'Proxy-Authorization',\n  'Range',\n  'Referer',\n  'TE',\n  'User-Agent',\n  'Upgrade',\n  'Via',\n  'Warning',\n] as const;\n\ntype HTTPHeaders = (typeof HTTP_HEADERS)[number];\n\nexport { HeadersInput };\n", "import { Box, Flex, Grid, Typography } from '@strapi/design-system';\nimport { Check, <PERSON>, Loader } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\n\n/* -------------------------------------------------------------------------------------------------\n * TriggerContainer\n * -----------------------------------------------------------------------------------------------*/\n\ninterface TriggerContainerProps extends Pick<StatusProps, 'isPending'> {\n  onCancel: () => void;\n  response?: {\n    statusCode: number;\n    message?: string;\n  };\n}\n\nconst TriggerContainer = ({ isPending, onCancel, response }: TriggerContainerProps) => {\n  const { statusCode, message } = response ?? {};\n  const { formatMessage } = useIntl();\n\n  return (\n    <Box background=\"neutral0\" padding={5} shadow=\"filterShadow\" hasRadius>\n      <Grid.Root gap={4} style={{ alignItems: 'center' }}>\n        <Grid.Item col={3} direction=\"column\" alignItems=\"stretch\">\n          <Typography>\n            {formatMessage({\n              id: 'Settings.webhooks.trigger.test',\n              defaultMessage: 'Test-trigger',\n            })}\n          </Typography>\n        </Grid.Item>\n        <Grid.Item col={3} direction=\"column\" alignItems=\"stretch\">\n          <Status isPending={isPending} statusCode={statusCode} />\n        </Grid.Item>\n        <Grid.Item col={6} direction=\"column\" alignItems=\"stretch\">\n          {!isPending ? (\n            <Message statusCode={statusCode} message={message} />\n          ) : (\n            <Flex justifyContent=\"flex-end\">\n              <button onClick={onCancel} type=\"button\">\n                <Flex gap={2} alignItems=\"center\">\n                  <Typography textColor=\"neutral400\">\n                    {formatMessage({\n                      id: 'Settings.webhooks.trigger.cancel',\n                      defaultMessage: 'cancel',\n                    })}\n                  </Typography>\n                  <Cross fill=\"neutral400\" height=\"1.2rem\" width=\"1.2rem\" />\n                </Flex>\n              </button>\n            </Flex>\n          )}\n        </Grid.Item>\n      </Grid.Root>\n    </Box>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Status\n * -----------------------------------------------------------------------------------------------*/\n\ninterface StatusProps {\n  isPending: boolean;\n  statusCode?: number;\n}\n\nconst Status = ({ isPending, statusCode }: StatusProps) => {\n  const { formatMessage } = useIntl();\n\n  if (isPending || !statusCode) {\n    return (\n      <Flex gap={2} alignItems=\"center\">\n        <Loader height=\"1.2rem\" width=\"1.2rem\" />\n        <Typography>\n          {formatMessage({ id: 'Settings.webhooks.trigger.pending', defaultMessage: 'pending' })}\n        </Typography>\n      </Flex>\n    );\n  }\n\n  if (statusCode >= 200 && statusCode < 300) {\n    return (\n      <Flex gap={2} alignItems=\"center\">\n        <Check fill=\"success700\" height=\"1.2rem\" width=\"1.2rem\" />\n        <Typography>\n          {formatMessage({ id: 'Settings.webhooks.trigger.success', defaultMessage: 'success' })}\n        </Typography>\n      </Flex>\n    );\n  }\n\n  if (statusCode >= 300) {\n    return (\n      <Flex gap={2} alignItems=\"center\">\n        <Cross fill=\"danger700\" height=\"1.2rem\" width=\"1.2rem\" />\n        <Typography>\n          {formatMessage({ id: 'Settings.error', defaultMessage: 'error' })} {statusCode}\n        </Typography>\n      </Flex>\n    );\n  }\n\n  return null;\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Message\n * -----------------------------------------------------------------------------------------------*/\n\ninterface MessageProps {\n  statusCode?: number;\n  message?: string;\n}\n\nconst Message = ({ statusCode, message }: MessageProps) => {\n  const { formatMessage } = useIntl();\n\n  if (!statusCode) {\n    return null;\n  }\n\n  if (statusCode >= 200 && statusCode < 300) {\n    return (\n      <Flex justifyContent=\"flex-end\">\n        <Typography textColor=\"neutral600\" ellipsis>\n          {formatMessage({\n            id: 'Settings.webhooks.trigger.success.label',\n            defaultMessage: 'Trigger succeeded',\n          })}\n        </Typography>\n      </Flex>\n    );\n  }\n\n  if (statusCode >= 300) {\n    return (\n      <Flex justifyContent=\"flex-end\">\n        <Flex maxWidth={`25rem`} justifyContent=\"flex-end\" title={message}>\n          <Typography ellipsis textColor=\"neutral600\">\n            {message}\n          </Typography>\n        </Flex>\n      </Flex>\n    );\n  }\n\n  return null;\n};\n\nexport { TriggerContainer };\n", "import * as React from 'react';\n\nimport { Box, Button, Flex, Grid, TextInput } from '@strapi/design-system';\nimport { Check, Play as Publish } from '@strapi/icons';\nimport { IntlShape, useIntl } from 'react-intl';\nimport * as yup from 'yup';\n\nimport { TriggerWebhook } from '../../../../../../../shared/contracts/webhooks';\nimport { Form, FormHelpers } from '../../../../../components/Form';\nimport { InputRenderer } from '../../../../../components/FormInputs/Renderer';\nimport { Layouts } from '../../../../../components/Layouts/Layout';\nimport { BackButton } from '../../../../../features/BackButton';\nimport { useEnterprise } from '../../../../../hooks/useEnterprise';\n\nimport { EventTableCE } from './EventsTable';\nimport { HeadersInput } from './HeadersInput';\nimport { TriggerContainer } from './TriggerContainer';\n\nimport type { Modules } from '@strapi/types';\n\ninterface WebhookFormValues {\n  name: Modules.WebhookStore.Webhook['name'];\n  url: Modules.WebhookStore.Webhook['url'];\n  headers: Array<{ key: string; value: string }>;\n  events: Modules.WebhookStore.Webhook['events'];\n}\n\ninterface WebhookFormProps {\n  data?: Modules.WebhookStore.Webhook;\n  handleSubmit: (\n    values: WebhookFormValues,\n    helpers: FormHelpers<WebhookFormValues>\n  ) => Promise<void>;\n  isCreating: boolean;\n  isTriggering: boolean;\n  triggerWebhook: () => void;\n  triggerResponse?: TriggerWebhook.Response['data'];\n}\n\nconst WebhookForm = ({\n  handleSubmit,\n  triggerWebhook,\n  isCreating,\n  isTriggering,\n  triggerResponse,\n  data,\n}: WebhookFormProps) => {\n  const { formatMessage } = useIntl();\n  const [showTriggerResponse, setShowTriggerResponse] = React.useState(false);\n  const EventTable = useEnterprise(\n    EventTableCE,\n    async () =>\n      (\n        await import(\n          '../../../../../../../ee/admin/src/pages/SettingsPage/pages/Webhooks/components/EventsTable'\n        )\n      ).EventsTableEE\n  );\n\n  /**\n   * Map the headers into a form that can be used within the formik form\n   */\n  const mapHeaders = (headers: Modules.WebhookStore.Webhook['headers']) => {\n    if (!Object.keys(headers).length) {\n      return [{ key: '', value: '' }];\n    }\n\n    return Object.entries(headers).map(([key, value]) => ({ key, value }));\n  };\n\n  // block rendering until the EE component is fully loaded\n  if (!EventTable) {\n    return null;\n  }\n\n  return (\n    <Form\n      initialValues={{\n        name: data?.name || '',\n        url: data?.url || '',\n        headers: mapHeaders(data?.headers || {}),\n        events: data?.events || [],\n      }}\n      method={isCreating ? 'POST' : 'PUT'}\n      onSubmit={handleSubmit}\n      validationSchema={makeWebhookValidationSchema({ formatMessage })}\n    >\n      {({ isSubmitting, modified }) => (\n        <>\n          <Layouts.Header\n            primaryAction={\n              <Flex gap={2}>\n                <Button\n                  onClick={() => {\n                    triggerWebhook();\n                    setShowTriggerResponse(true);\n                  }}\n                  variant=\"tertiary\"\n                  startIcon={<Publish />}\n                  disabled={isCreating || isTriggering}\n                >\n                  {formatMessage({\n                    id: 'Settings.webhooks.trigger',\n                    defaultMessage: 'Trigger',\n                  })}\n                </Button>\n                <Button\n                  startIcon={<Check />}\n                  type=\"submit\"\n                  disabled={!modified}\n                  loading={isSubmitting}\n                >\n                  {formatMessage({\n                    id: 'global.save',\n                    defaultMessage: 'Save',\n                  })}\n                </Button>\n              </Flex>\n            }\n            title={\n              isCreating\n                ? formatMessage({\n                    id: 'Settings.webhooks.create',\n                    defaultMessage: 'Create a webhook',\n                  })\n                : data?.name\n            }\n            navigationAction={<BackButton fallback=\"../webhooks\" />}\n          />\n          <Layouts.Content>\n            <Flex direction=\"column\" alignItems=\"stretch\" gap={4}>\n              {showTriggerResponse && (\n                <TriggerContainer\n                  isPending={isTriggering}\n                  response={triggerResponse}\n                  onCancel={() => setShowTriggerResponse(false)}\n                />\n              )}\n              <Box background=\"neutral0\" padding={8} shadow=\"filterShadow\" hasRadius>\n                <Flex direction=\"column\" alignItems=\"stretch\" gap={6}>\n                  <Grid.Root gap={6}>\n                    {[\n                      {\n                        label: formatMessage({\n                          id: 'global.name',\n                          defaultMessage: 'Name',\n                        }),\n                        name: 'name',\n                        required: true,\n                        size: 6,\n                        type: 'string' as const,\n                      },\n                      {\n                        label: formatMessage({\n                          id: 'Settings.roles.form.input.url',\n                          defaultMessage: 'Url',\n                        }),\n                        name: 'url',\n                        required: true,\n                        size: 12,\n                        type: 'string' as const,\n                      },\n                    ].map(({ size, ...field }) => (\n                      <Grid.Item\n                        key={field.name}\n                        col={size}\n                        direction=\"column\"\n                        alignItems=\"stretch\"\n                      >\n                        <InputRenderer {...field} />\n                      </Grid.Item>\n                    ))}\n                  </Grid.Root>\n                  <HeadersInput />\n                  <EventTable />\n                </Flex>\n              </Box>\n            </Flex>\n          </Layouts.Content>\n        </>\n      )}\n    </Form>\n  );\n};\n\nconst NAME_REGEX = /(^$)|(^[A-Za-z][_0-9A-Za-z ]*$)/;\nconst URL_REGEX = /(^$)|((https?:\\/\\/.*)(d*)\\/?(.*))/;\n\nconst makeWebhookValidationSchema = ({ formatMessage }: Pick<IntlShape, 'formatMessage'>) =>\n  yup.object().shape({\n    name: yup\n      .string()\n      .nullable()\n      .required(\n        formatMessage({\n          id: 'Settings.webhooks.validation.name.required',\n          defaultMessage: 'Name is required',\n        })\n      )\n      .matches(\n        NAME_REGEX,\n        formatMessage({\n          id: 'Settings.webhooks.validation.name.regex',\n          defaultMessage:\n            'The name must start with a letter and only contain letters, numbers, spaces and underscores',\n        })\n      ),\n    url: yup\n      .string()\n      .nullable()\n      .required(\n        formatMessage({\n          id: 'Settings.webhooks.validation.url.required',\n          defaultMessage: 'Url is required',\n        })\n      )\n      .matches(\n        URL_REGEX,\n        formatMessage({\n          id: 'Settings.webhooks.validation.url.regex',\n          defaultMessage: 'The value must be a valid Url',\n        })\n      ),\n    headers: yup.lazy((array) => {\n      const baseSchema = yup.array();\n\n      if (array.length === 1) {\n        const { key, value } = array[0];\n\n        if (!key && !value) {\n          return baseSchema;\n        }\n      }\n\n      return baseSchema.of(\n        yup.object().shape({\n          key: yup\n            .string()\n            .required(\n              formatMessage({\n                id: 'Settings.webhooks.validation.key',\n                defaultMessage: 'Key is required',\n              })\n            )\n            .nullable(),\n          value: yup\n            .string()\n            .required(\n              formatMessage({\n                id: 'Settings.webhooks.validation.value',\n                defaultMessage: 'Value is required',\n              })\n            )\n            .nullable(),\n        })\n      );\n    }),\n    events: yup.array(),\n  });\n\nexport { WebhookForm };\nexport type { WebhookFormValues, WebhookFormProps };\n", "import * as React from 'react';\n\nimport { Main } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\nimport { useNavigate, useMatch } from 'react-router-dom';\n\nimport { CreateWebhook, TriggerWebhook } from '../../../../../../shared/contracts/webhooks';\nimport { Page } from '../../../../components/PageHelpers';\nimport { useTypedSelector } from '../../../../core/store/hooks';\nimport { useNotification } from '../../../../features/Notifications';\nimport { useAPIErrorHandler } from '../../../../hooks/useAPIErrorHandler';\nimport { selectAdminPermissions } from '../../../../selectors';\nimport { isBaseQueryError } from '../../../../utils/baseQuery';\n\nimport { WebhookForm, WebhookFormProps, WebhookFormValues } from './components/WebhookForm';\nimport { useWebhooks } from './hooks/useWebhooks';\n\nimport type { Modules } from '@strapi/types';\n\n/* -------------------------------------------------------------------------------------------------\n * EditView\n * -----------------------------------------------------------------------------------------------*/\n\nconst cleanData = (\n  data: WebhookFormValues\n): Omit<CreateWebhook.Request['body'], 'id' | 'isEnabled'> => ({\n  ...data,\n  headers: data.headers.reduce<Modules.WebhookStore.Webhook['headers']>((acc, { key, value }) => {\n    if (key !== '') {\n      acc[key] = value;\n    }\n\n    return acc;\n  }, {}),\n});\n\nconst EditPage = () => {\n  const { formatMessage } = useIntl();\n  const match = useMatch('/settings/webhooks/:id');\n  const id = match?.params.id;\n  const isCreating = id === 'create';\n\n  const navigate = useNavigate();\n  const { toggleNotification } = useNotification();\n  const {\n    _unstableFormatAPIError: formatAPIError,\n    _unstableFormatValidationErrors: formatValidationErrors,\n  } = useAPIErrorHandler();\n\n  /**\n   * Prevents the notifications from showing up twice because the function identity\n   * coming from the helper plugin is not stable\n   */\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  const stableFormatAPIError = React.useCallback(formatAPIError, []);\n  const [isTriggering, setIsTriggering] = React.useState(false);\n  const [triggerResponse, setTriggerResponse] = React.useState<TriggerWebhook.Response['data']>();\n\n  const { isLoading, webhooks, error, createWebhook, updateWebhook, triggerWebhook } = useWebhooks(\n    { id: id! },\n    {\n      skip: isCreating,\n    }\n  );\n\n  React.useEffect(() => {\n    if (error) {\n      toggleNotification({\n        type: 'danger',\n        message: stableFormatAPIError(error),\n      });\n    }\n  }, [error, toggleNotification, stableFormatAPIError]);\n\n  const handleTriggerWebhook = async () => {\n    try {\n      setIsTriggering(true);\n\n      const res = await triggerWebhook(id!);\n\n      if ('error' in res) {\n        toggleNotification({\n          type: 'danger',\n          message: formatAPIError(res.error),\n        });\n\n        return;\n      }\n\n      setTriggerResponse(res.data);\n    } catch {\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({\n          id: 'notification.error',\n          defaultMessage: 'An error occurred',\n        }),\n      });\n    } finally {\n      setIsTriggering(false);\n    }\n  };\n\n  const handleSubmit: WebhookFormProps['handleSubmit'] = async (data, helpers) => {\n    try {\n      if (isCreating) {\n        const res = await createWebhook(cleanData(data));\n\n        if ('error' in res) {\n          if (isBaseQueryError(res.error) && res.error.name === 'ValidationError') {\n            helpers.setErrors(formatValidationErrors(res.error));\n          } else {\n            toggleNotification({\n              type: 'danger',\n              message: formatAPIError(res.error),\n            });\n          }\n\n          return;\n        }\n\n        toggleNotification({\n          type: 'success',\n          message: formatMessage({ id: 'Settings.webhooks.created' }),\n        });\n\n        navigate(`../webhooks/${res.data.id}`, { replace: true });\n      } else {\n        const res = await updateWebhook({ id: id!, ...cleanData(data) });\n\n        if ('error' in res) {\n          if (isBaseQueryError(res.error) && res.error.name === 'ValidationError') {\n            helpers.setErrors(formatValidationErrors(res.error));\n          } else {\n            toggleNotification({\n              type: 'danger',\n              message: formatAPIError(res.error),\n            });\n          }\n\n          return;\n        }\n\n        toggleNotification({\n          type: 'success',\n          message: formatMessage({ id: 'notification.form.success.fields' }),\n        });\n      }\n    } catch {\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({\n          id: 'notification.error',\n          defaultMessage: 'An error occurred',\n        }),\n      });\n    }\n  };\n\n  if (isLoading) {\n    return <Page.Loading />;\n  }\n\n  const [webhook] = webhooks ?? [];\n\n  return (\n    <Main>\n      <Page.Title>\n        {formatMessage(\n          { id: 'Settings.PageTitle', defaultMessage: 'Settings - {name}' },\n          {\n            name: 'Webhooks',\n          }\n        )}\n      </Page.Title>\n      <WebhookForm\n        data={webhook}\n        handleSubmit={handleSubmit}\n        triggerWebhook={handleTriggerWebhook}\n        isCreating={isCreating}\n        isTriggering={isTriggering}\n        triggerResponse={triggerResponse}\n      />\n    </Main>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * ProtectedEditView\n * -----------------------------------------------------------------------------------------------*/\n\nconst ProtectedEditPage = () => {\n  const permissions = useTypedSelector(selectAdminPermissions);\n\n  return (\n    <Page.Protect permissions={permissions.settings?.webhooks.update}>\n      <EditPage />\n    </Page.Protect>\n  );\n};\n\nexport { ProtectedEditPage, EditPage };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAMA,eAAe,MAAA;AACnB,aACEC,yBAACC,OAAOC,MAAI;;UACVC,wBAACF,OAAOG,SAAO,CAAA,CAAA;UACfD,wBAACF,OAAOI,MAAI,CAAA,CAAA;;;AAGlB;;;;;ACWA,IAAMC,kBAAkBC,GAAOC,UAAAA;;;AAa/B,IAAMC,eAAe,MAAA;AACnB,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,QAAMC,cAAcC,QAAQ,gBAAgB,CAACC,UAAUA,MAAMF,WAAW;AACxE,QAAMG,iBAAiBF,QAAQ,gBAAgB,CAACC,UAAUA,MAAMC,cAAc;AAC9E,QAAMC,gBAAgBH,QAAQ,gBAAgB,CAACC,UAAUA,MAAMG,QAAQ;AACvE,QAAM,EAAEC,QAAQ,CAAA,EAAE,IAAKC,SAAmB,SAAA;AAE1C,QAAMC,YAAY,CAACC,UAAAA;AAEjB,QAAIH,MAAMI,WAAW,GAAG;AACtBN,oBAAc,WAAW;QAAC;UAAEO,KAAK;UAAIL,OAAO;QAAG;MAAE,CAAA;WAC5C;AACLH,qBAAe,WAAWM,KAAAA;IAC5B;EACF;AAEA,aACEG,0BAACC,MAAAA;IAAKC,WAAU;IAASC,YAAW;IAAUC,KAAK;;UACjDC,yBAACC,MAAQC,OAAK;kBACXrB,cAAc;UACbsB,IAAI;UACJC,gBAAgB;QAClB,CAAA;;UAEFT,0BAACU,KAAAA;QAAIC,SAAS;QAAGC,YAAW;QAAaC,WAAS;;UAC/CnB,MAAMoB,IAAI,CAACC,KAAKlB,UAAAA;AACf,uBACEG,0BAACgB,KAAKC,MAAI;cAA6Cb,KAAK;cAAGO,SAAS;;oBACtEN,yBAACW,KAAKE,MAAI;kBAACC,KAAK;kBAAGjB,WAAU;kBAASC,YAAW;kBAC/C,cAAAE,yBAACe,gBAAAA;oBACCC,MAAM,WAAWxB,KAAAA;oBACjByB,cAAY,OAAOzB,QAAQ,CAAA;oBAC3B0B,OAAOrC,cAAc;sBACnBsB,IAAI;sBACJC,gBAAgB;oBAClB,CAAA;;;oBAGJJ,yBAACW,KAAKE,MAAI;kBAACC,KAAK;kBAAGjB,WAAU;kBAASC,YAAW;kBAC/C,cAAAH,0BAACC,MAAAA;oBAAKE,YAAW;oBAAWC,KAAK;;0BAC/BC,yBAACK,KAAAA;wBAAIc,OAAO;0BAAEC,MAAM;wBAAE;wBACpB,cAAApB,yBAACqB,qBAAAA;0BACCL,MAAM,WAAWxB,KAAAA;0BACjByB,cAAY,OAAOzB,QAAQ,CAAA;0BAC3B0B,OAAOrC,cAAc;4BACnBsB,IAAI;4BACJC,gBAAgB;0BAClB,CAAA;0BACAkB,MAAK;;;0BAGTtB,yBAACuB,YAAAA;wBACCC,OAAM;wBACNC,QAAO;wBACPC,SAAS,MAAMnC,UAAUC,KAAAA;wBACzBmC,OAAM;wBACNT,OAAOrC,cACL;0BACEsB,IAAI;0BACJC,gBAAgB;2BAElB;0BAAEwB,QAAQpC,QAAQ;wBAAE,CAAA;wBAEtB8B,MAAK;wBAEL,cAAAtB,yBAAC6B,eAAAA;0BAAML,OAAM;;;;;;;eAtCL,GAAGhC,KAAM,IAAGsC,KAAKC,UAAUrB,IAAIhB,GAAG,CAAA,EAAG;UA4CzD,CAAA;cACAM,yBAACK,KAAAA;YAAI2B,YAAY;YACf,cAAAhC,yBAACvB,iBAAAA;cACC6C,MAAK;cACLI,SAAS,MAAA;AACP3C,4BAAY,WAAW;kBAAEW,KAAK;kBAAIL,OAAO;gBAAG,CAAA;cAC9C;cACA4C,eAAWjC,yBAACkC,eAAAA,CAAAA,CAAAA;wBAEXrD,cAAc;gBACbsB,IAAI;gBACJC,gBAAgB;cAClB,CAAA;;;;;;;AAMZ;AAWA,IAAMW,iBAAiB,CAAC,EAAEC,MAAME,OAAO,GAAGiB,UAAgC,MAAA;AACxE,QAAM,CAACC,SAASC,UAAAA,IAAoBC,eAAwB;IAAIC,GAAAA;EAAa,CAAA;AAC7E,QAAM,EAAElD,OAAOmD,QAAO,IAAKlD,SAAmB,SAAA;AAC9C,QAAMmD,QAAQnD,SAAS0B,IAAAA;AAEvB0B,EAAMC,gBAAU,MAAA;AACd,UAAMC,gBAAgBL,aAAaM,OACjC,CAACnD,QAAQ,EAAC8C,mCAASM,KAAK,CAACC,WAAWA,OAAOrD,QAAQ+C,MAAMpD,SAAS0D,OAAOrD,QAAQA,KAAAA;AAGnF2C,eAAWO,aAAAA;KACV;IAACJ;IAASC,MAAMpD;EAAM,CAAA;AAEzB,QAAM2D,eAA0C,CAAC3D,UAAAA;AAC/CoD,UAAMrD,SAAS4B,MAAM3B,KAAAA;EACvB;AAEA,QAAM4D,qBAAqB,CAAC5D,UAAAA;AAC1BgD,eAAW,CAACa,SAAS;MAAIA,GAAAA;MAAM7D;IAAqB,CAAA;AAEpD,QAAIA,OAAO;AACT2D,mBAAa3D,KAAAA;IACf;EACF;AAEA,aACEM,0BAACM,MAAQW,MAAI;IAACI;IAAYmC,OAAOV,MAAMU;;UACrCnD,yBAACC,MAAQC,OAAK;QAAEgB,UAAAA;;UAChBlB,yBAACoD,UAAAA;QACE,GAAGjB;QACJkB,SAAS,MAAML,aAAa,EAAA;QAC5B5D,UAAU4D;QACVM,gBAAgBL;QAChBM,aAAY;QACZC,WAAS;QACTnE,OAAOoD,MAAMpD;QAEZ+C,UAAAA,QAAQ3B,IAAI,CAACf,YACZM,yBAACyD,QAAAA;UAAepE,OAAOK;UACpBA,UAAAA;QAD8BA,GAAAA,GAAAA,CAAAA;;UAKrCM,yBAACC,MAAQyD,OAAK,CAAA,CAAA;;;AAGpB;AAEA,IAAMnB,eAAe;EACnB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACD;;;;AC1MKoB,IAAAA,mBAAmB,CAAC,EAAEC,WAAWC,UAAUC,SAAQ,MAAyB;AAChF,QAAM,EAAEC,YAAYC,QAAO,IAAKF,YAAY,CAAA;AAC5C,QAAM,EAAEG,cAAa,IAAKC,QAAAA;AAE1B,aACEC,yBAACC,KAAAA;IAAIC,YAAW;IAAWC,SAAS;IAAGC,QAAO;IAAeC,WAAS;kBACpEC,0BAACC,KAAKC,MAAI;MAACC,KAAK;MAAGC,OAAO;QAAEC,YAAY;MAAS;;YAC/CX,yBAACO,KAAKK,MAAI;UAACC,KAAK;UAAGC,WAAU;UAASH,YAAW;UAC/C,cAAAX,yBAACe,YAAAA;sBACEjB,cAAc;cACbkB,IAAI;cACJC,gBAAgB;YAClB,CAAA;;;YAGJjB,yBAACO,KAAKK,MAAI;UAACC,KAAK;UAAGC,WAAU;UAASH,YAAW;UAC/C,cAAAX,yBAACkB,QAAAA;YAAOzB;YAAsBG;;;YAEhCI,yBAACO,KAAKK,MAAI;UAACC,KAAK;UAAGC,WAAU;UAASH,YAAW;UAC9C,UAAA,CAAClB,gBACAO,yBAACmB,SAAAA;YAAQvB;YAAwBC;mBAEjCG,yBAACoB,MAAAA;YAAKC,gBAAe;YACnB,cAAArB,yBAACsB,UAAAA;cAAOC,SAAS7B;cAAU8B,MAAK;cAC9B,cAAAlB,0BAACc,MAAAA;gBAAKX,KAAK;gBAAGE,YAAW;;sBACvBX,yBAACe,YAAAA;oBAAWU,WAAU;8BACnB3B,cAAc;sBACbkB,IAAI;sBACJC,gBAAgB;oBAClB,CAAA;;sBAEFjB,yBAAC0B,eAAAA;oBAAMC,MAAK;oBAAaC,QAAO;oBAASC,OAAM;;;;;;;;;;AASjE;AAWA,IAAMX,SAAS,CAAC,EAAEzB,WAAWG,WAAU,MAAe;AACpD,QAAM,EAAEE,cAAa,IAAKC,QAAAA;AAE1B,MAAIN,aAAa,CAACG,YAAY;AAC5B,eACEU,0BAACc,MAAAA;MAAKX,KAAK;MAAGE,YAAW;;YACvBX,yBAAC8B,eAAAA;UAAOF,QAAO;UAASC,OAAM;;YAC9B7B,yBAACe,YAAAA;oBACEjB,cAAc;YAAEkB,IAAI;YAAqCC,gBAAgB;UAAU,CAAA;;;;EAI5F;AAEA,MAAIrB,cAAc,OAAOA,aAAa,KAAK;AACzC,eACEU,0BAACc,MAAAA;MAAKX,KAAK;MAAGE,YAAW;;YACvBX,yBAAC+B,eAAAA;UAAMJ,MAAK;UAAaC,QAAO;UAASC,OAAM;;YAC/C7B,yBAACe,YAAAA;oBACEjB,cAAc;YAAEkB,IAAI;YAAqCC,gBAAgB;UAAU,CAAA;;;;EAI5F;AAEA,MAAIrB,cAAc,KAAK;AACrB,eACEU,0BAACc,MAAAA;MAAKX,KAAK;MAAGE,YAAW;;YACvBX,yBAAC0B,eAAAA;UAAMC,MAAK;UAAYC,QAAO;UAASC,OAAM;;YAC9CvB,0BAACS,YAAAA;;YACEjB,cAAc;cAAEkB,IAAI;cAAkBC,gBAAgB;YAAQ,CAAA;YAAG;YAAErB;;;;;EAI5E;AAEA,SAAO;AACT;AAWA,IAAMuB,UAAU,CAAC,EAAEvB,YAAYC,QAAO,MAAgB;AACpD,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,MAAI,CAACH,YAAY;AACf,WAAO;EACT;AAEA,MAAIA,cAAc,OAAOA,aAAa,KAAK;AACzC,eACEI,yBAACoB,MAAAA;MAAKC,gBAAe;MACnB,cAAArB,yBAACe,YAAAA;QAAWU,WAAU;QAAaO,UAAQ;kBACxClC,cAAc;UACbkB,IAAI;UACJC,gBAAgB;QAClB,CAAA;;;EAIR;AAEA,MAAIrB,cAAc,KAAK;AACrB,eACEI,yBAACoB,MAAAA;MAAKC,gBAAe;MACnB,cAAArB,yBAACoB,MAAAA;QAAKa,UAAU;QAASZ,gBAAe;QAAWa,OAAOrC;QACxD,cAAAG,yBAACe,YAAAA;UAAWiB,UAAQ;UAACP,WAAU;UAC5B5B,UAAAA;;;;EAKX;AAEA,SAAO;AACT;;;AC7GA,IAAMsC,cAAc,CAAC,EACnBC,cACAC,gBACAC,YACAC,cACAC,iBACAC,KAAI,MACa;AACjB,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,CAACC,qBAAqBC,sBAAAA,IAAgCC,gBAAS,KAAA;AACrE,QAAMC,aAAaC,cACjBC,cACA,aAEI,MAAM,OACJ,2BACF,GACAC,aAAa;AAMnB,QAAMC,aAAa,CAACC,YAAAA;AAClB,QAAI,CAACC,OAAOC,KAAKF,OAAAA,EAASG,QAAQ;AAChC,aAAO;QAAC;UAAEC,KAAK;UAAIC,OAAO;QAAG;MAAE;IACjC;AAEA,WAAOJ,OAAOK,QAAQN,OAASO,EAAAA,IAAI,CAAC,CAACH,KAAKC,KAAM,OAAM;MAAED;MAAKC;MAAM;EACrE;AAGA,MAAI,CAACV,YAAY;AACf,WAAO;EACT;AAEA,aACEa,yBAACC,MAAAA;IACCC,eAAe;MACbC,OAAMtB,6BAAMsB,SAAQ;MACpBC,MAAKvB,6BAAMuB,QAAO;MAClBZ,SAASD,YAAWV,6BAAMW,YAAW,CAAA,CAAC;MACtCa,SAAQxB,6BAAMwB,WAAU,CAAA;IAC1B;IACAC,QAAQ5B,aAAa,SAAS;IAC9B6B,UAAU/B;IACVgC,kBAAkBC,4BAA4B;MAAE3B;IAAc,CAAA;IAE7D,UAAA,CAAC,EAAE4B,cAAcC,SAAQ,UACxBC,0BAAAC,8BAAA;;YACEb,yBAACc,QAAQC,QAAM;UACbC,mBACEJ,0BAACK,MAAAA;YAAKC,KAAK;;kBACTlB,yBAACmB,QAAAA;gBACCC,SAAS,MAAA;AACP3C,iCAAAA;AACAQ,yCAAuB,IAAA;gBACzB;gBACAoC,SAAQ;gBACRC,eAAWtB,yBAACuB,eAAAA,CAAAA,CAAAA;gBACZC,UAAU9C,cAAcC;0BAEvBG,cAAc;kBACb2C,IAAI;kBACJC,gBAAgB;gBAClB,CAAA;;kBAEF1B,yBAACmB,QAAAA;gBACCG,eAAWtB,yBAAC2B,eAAAA,CAAAA,CAAAA;gBACZC,MAAK;gBACLJ,UAAU,CAACb;gBACXkB,SAASnB;0BAER5B,cAAc;kBACb2C,IAAI;kBACJC,gBAAgB;gBAClB,CAAA;;;;UAINI,OACEpD,aACII,cAAc;YACZ2C,IAAI;YACJC,gBAAgB;UAClB,CAAA,IACA7C,6BAAMsB;UAEZ4B,sBAAkB/B,yBAACgC,YAAAA;YAAWC,UAAS;;;YAEzCjC,yBAACc,QAAQoB,SAAO;UACd,cAAAtB,0BAACK,MAAAA;YAAKkB,WAAU;YAASC,YAAW;YAAUlB,KAAK;;cAChDlC,2BACCgB,yBAACqC,kBAAAA;gBACCC,WAAW3D;gBACX4D,UAAU3D;gBACV4D,UAAU,MAAMvD,uBAAuB,KAAA;;kBAG3Ce,yBAACyC,KAAAA;gBAAIC,YAAW;gBAAWC,SAAS;gBAAGC,QAAO;gBAAeC,WAAS;gBACpE,cAAAjC,0BAACK,MAAAA;kBAAKkB,WAAU;kBAASC,YAAW;kBAAUlB,KAAK;;wBACjDlB,yBAAC8C,KAAKC,MAAI;sBAAC7B,KAAK;sBACb,UAAA;wBACC;0BACE8B,OAAOlE,cAAc;4BACnB2C,IAAI;4BACJC,gBAAgB;0BAClB,CAAA;0BACAvB,MAAM;0BACN8C,UAAU;0BACVC,MAAM;0BACNtB,MAAM;wBACR;wBACA;0BACEoB,OAAOlE,cAAc;4BACnB2C,IAAI;4BACJC,gBAAgB;0BAClB,CAAA;0BACAvB,MAAM;0BACN8C,UAAU;0BACVC,MAAM;0BACNtB,MAAM;wBACR;wBACA7B,IAAI,CAAC,EAAEmD,MAAM,GAAGC,MAAO,UACvBnD,yBAAC8C,KAAKM,MAAI;wBAERC,KAAKH;wBACLf,WAAU;wBACVC,YAAW;wBAEX,cAAApC,yBAACsD,uBAAAA;0BAAe,GAAGH;;sBALdA,GAAAA,MAAMhD,IAAI,CAAA;;wBASrBH,yBAACuD,cAAAA,CAAAA,CAAAA;wBACDvD,yBAACb,YAAAA,CAAAA,CAAAA;;;;;;;;;;AASnB;AAEA,IAAMqE,aAAa;AACnB,IAAMC,YAAY;AAElB,IAAMhD,8BAA8B,CAAC,EAAE3B,cAAa,MAC9C4E,QAAM,EAAGC,MAAM;EACjBxD,MACGyD,OAAM,EACNC,SAAQ,EACRZ,SACCnE,cAAc;IACZ2C,IAAI;IACJC,gBAAgB;GAGnBoC,CAAAA,EAAAA,QACCN,YACA1E,cAAc;IACZ2C,IAAI;IACJC,gBACE;EACJ,CAAA,CAAA;EAEJtB,KACGwD,OAAM,EACNC,SAAQ,EACRZ,SACCnE,cAAc;IACZ2C,IAAI;IACJC,gBAAgB;GAGnBoC,CAAAA,EAAAA,QACCL,WACA3E,cAAc;IACZ2C,IAAI;IACJC,gBAAgB;EAClB,CAAA,CAAA;EAEJlC,SAAauE,QAAK,CAACC,UAAAA;AACjB,UAAMC,aAAiBD,QAAK;AAE5B,QAAIA,MAAMrE,WAAW,GAAG;AACtB,YAAM,EAAEC,KAAKC,MAAK,IAAKmE,MAAM,CAAE;AAE/B,UAAI,CAACpE,OAAO,CAACC,OAAO;AAClB,eAAOoE;MACT;IACF;AAEA,WAAOA,WAAWC,GACZR,QAAM,EAAGC,MAAM;MACjB/D,KACGgE,OAAM,EACNX,SACCnE,cAAc;QACZ2C,IAAI;QACJC,gBAAgB;MAClB,CAAA,CAAA,EAEDmC,SAAQ;MACXhE,OACG+D,OAAM,EACNX,SACCnE,cAAc;QACZ2C,IAAI;QACJC,gBAAgB;MAClB,CAAA,CAAA,EAEDmC,SAAQ;IACb,CAAA,CAAA;EAEJ,CAAA;EACAxD,QAAY2D,QAAK;AACnB,CAAA;;;AC3OF,IAAMG,YAAY,CAChBC,UAC6D;EAC7D,GAAGA;EACHC,SAASD,KAAKC,QAAQC,OAAgD,CAACC,KAAK,EAAEC,KAAKC,MAAK,MAAE;AACxF,QAAID,QAAQ,IAAI;AACdD,UAAIC,GAAAA,IAAOC;IACb;AAEA,WAAOF;EACT,GAAG,CAAA,CAAC;;AAGN,IAAMG,WAAW,MAAA;AACf,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAMC,QAAQC,SAAS,wBAAA;AACvB,QAAMC,KAAKF,+BAAOG,OAAOD;AACzB,QAAME,aAAaF,OAAO;AAE1B,QAAMG,WAAWC,YAAAA;AACjB,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,EACJC,yBAAyBC,gBACzBC,iCAAiCC,uBAAsB,IACrDC,mBAAAA;AAOJ,QAAMC,uBAA6BC,mBAAYL,gBAAgB,CAAA,CAAE;AACjE,QAAM,CAACM,cAAcC,eAAAA,IAAyBC,gBAAS,KAAA;AACvD,QAAM,CAACC,iBAAiBC,kBAAmB,IAASF,gBAAQ;AAE5D,QAAM,EAAEG,WAAWC,UAAUC,OAAOC,eAAeC,eAAeC,eAAc,IAAKC,YACnF;IAAEzB;KACF;IACE0B,MAAMxB;EACR,CAAA;AAGFyB,EAAMC,iBAAU,MAAA;AACd,QAAIP,OAAO;AACThB,yBAAmB;QACjBwB,MAAM;QACNC,SAASlB,qBAAqBS,KAAAA;MAChC,CAAA;IACF;KACC;IAACA;IAAOhB;IAAoBO;EAAqB,CAAA;AAEpD,QAAMmB,uBAAuB,YAAA;AAC3B,QAAI;AACFhB,sBAAgB,IAAA;AAEhB,YAAMiB,MAAM,MAAMR,eAAexB,EAAAA;AAEjC,UAAI,WAAWgC,KAAK;AAClB3B,2BAAmB;UACjBwB,MAAM;UACNC,SAAStB,eAAewB,IAAIX,KAAK;QACnC,CAAA;AAEA;MACF;AAEAH,yBAAmBc,IAAI3C,IAAI;IAC7B,QAAQ;AACNgB,yBAAmB;QACjBwB,MAAM;QACNC,SAASlC,cAAc;UACrBI,IAAI;UACJiC,gBAAgB;QAClB,CAAA;MACF,CAAA;cACQ;AACRlB,sBAAgB,KAAA;IAClB;EACF;AAEA,QAAMmB,eAAiD,OAAO7C,MAAM8C,YAAAA;AAClE,QAAI;AACF,UAAIjC,YAAY;AACd,cAAM8B,MAAM,MAAMV,cAAclC,UAAUC,IAAAA,CAAAA;AAE1C,YAAI,WAAW2C,KAAK;AAClB,cAAII,iBAAiBJ,IAAIX,KAAK,KAAKW,IAAIX,MAAMgB,SAAS,mBAAmB;AACvEF,oBAAQG,UAAU5B,uBAAuBsB,IAAIX,KAAK,CAAA;iBAC7C;AACLhB,+BAAmB;cACjBwB,MAAM;cACNC,SAAStB,eAAewB,IAAIX,KAAK;YACnC,CAAA;UACF;AAEA;QACF;AAEAhB,2BAAmB;UACjBwB,MAAM;UACNC,SAASlC,cAAc;YAAEI,IAAI;UAA4B,CAAA;QAC3D,CAAA;AAEAG,iBAAS,eAAe6B,IAAI3C,KAAKW,EAAE,IAAI;UAAEuC,SAAS;QAAK,CAAA;aAClD;AACL,cAAMP,MAAM,MAAMT,cAAc;UAAEvB;UAAS,GAAGZ,UAAUC,IAAK;QAAC,CAAA;AAE9D,YAAI,WAAW2C,KAAK;AAClB,cAAII,iBAAiBJ,IAAIX,KAAK,KAAKW,IAAIX,MAAMgB,SAAS,mBAAmB;AACvEF,oBAAQG,UAAU5B,uBAAuBsB,IAAIX,KAAK,CAAA;iBAC7C;AACLhB,+BAAmB;cACjBwB,MAAM;cACNC,SAAStB,eAAewB,IAAIX,KAAK;YACnC,CAAA;UACF;AAEA;QACF;AAEAhB,2BAAmB;UACjBwB,MAAM;UACNC,SAASlC,cAAc;YAAEI,IAAI;UAAmC,CAAA;QAClE,CAAA;MACF;IACF,QAAQ;AACNK,yBAAmB;QACjBwB,MAAM;QACNC,SAASlC,cAAc;UACrBI,IAAI;UACJiC,gBAAgB;QAClB,CAAA;MACF,CAAA;IACF;EACF;AAEA,MAAId,WAAW;AACb,eAAOqB,yBAACC,KAAKC,SAAO,CAAA,CAAA;EACtB;AAEA,QAAM,CAACC,OAAAA,IAAWvB,YAAY,CAAA;AAE9B,aACEwB,0BAACC,MAAAA;;UACCL,yBAACC,KAAKK,OAAK;kBACRlD,cACC;UAAEI,IAAI;UAAsBiC,gBAAgB;WAC5C;UACEI,MAAM;QACR,CAAA;;UAGJG,yBAACO,aAAAA;QACC1D,MAAMsD;QACNT;QACAV,gBAAgBO;QAChB7B;QACAY;QACAG;;;;AAIR;AAIkG,IAE5F+B,oBAAoB,MAAA;;AACxB,QAAMC,cAAcC,iBAAiBC,sBAAAA;AAErC,aACEX,yBAACC,KAAKW,SAAO;IAACH,cAAaA,iBAAYI,aAAZJ,mBAAsB7B,SAASkC;IACxD,cAAAd,yBAAC7C,UAAAA,CAAAA,CAAAA;;AAGP;", "names": ["EventTableCE", "_jsxs", "Events", "Root", "_jsx", "Headers", "Body", "AddHeaderButton", "styled", "TextButton", "HeadersInput", "formatMessage", "useIntl", "addFieldRow", "useForm", "state", "removeFieldRow", "setFieldValue", "onChange", "value", "useField", "removeRow", "index", "length", "key", "_jsxs", "Flex", "direction", "alignItems", "gap", "_jsx", "DSField", "Label", "id", "defaultMessage", "Box", "padding", "background", "hasRadius", "map", "val", "Grid", "Root", "<PERSON><PERSON>", "col", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name", "aria-label", "label", "style", "flex", "StringInput", "type", "IconButton", "width", "height", "onClick", "color", "number", "Minus", "JSON", "stringify", "paddingTop", "startIcon", "Plus", "restProps", "options", "setOptions", "useState", "HTTP_HEADERS", "headers", "field", "React", "useEffect", "headerOptions", "filter", "some", "header", "handleChange", "handleCreateOption", "prev", "error", "Combobox", "onClear", "onCreateOption", "placeholder", "creatable", "ComboboxOption", "Error", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isPending", "onCancel", "response", "statusCode", "message", "formatMessage", "useIntl", "_jsx", "Box", "background", "padding", "shadow", "hasRadius", "_jsxs", "Grid", "Root", "gap", "style", "alignItems", "<PERSON><PERSON>", "col", "direction", "Typography", "id", "defaultMessage", "Status", "Message", "Flex", "justifyContent", "button", "onClick", "type", "textColor", "Cross", "fill", "height", "width", "Loader", "Check", "ellipsis", "max<PERSON><PERSON><PERSON>", "title", "WebhookForm", "handleSubmit", "triggerWebhook", "isCreating", "isTriggering", "triggerResponse", "data", "formatMessage", "useIntl", "showTriggerResponse", "setShowTriggerResponse", "useState", "EventTable", "useEnterprise", "EventTableCE", "EventsTableEE", "mapHeaders", "headers", "Object", "keys", "length", "key", "value", "entries", "map", "_jsx", "Form", "initialValues", "name", "url", "events", "method", "onSubmit", "validationSchema", "makeWebhookValidationSchema", "isSubmitting", "modified", "_jsxs", "_Fragment", "Layouts", "Header", "primaryAction", "Flex", "gap", "<PERSON><PERSON>", "onClick", "variant", "startIcon", "Publish", "disabled", "id", "defaultMessage", "Check", "type", "loading", "title", "navigationAction", "BackButton", "fallback", "Content", "direction", "alignItems", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isPending", "response", "onCancel", "Box", "background", "padding", "shadow", "hasRadius", "Grid", "Root", "label", "required", "size", "field", "<PERSON><PERSON>", "col", "InputR<PERSON><PERSON>", "HeadersInput", "NAME_REGEX", "URL_REGEX", "object", "shape", "string", "nullable", "matches", "lazy", "array", "baseSchema", "of", "cleanData", "data", "headers", "reduce", "acc", "key", "value", "EditPage", "formatMessage", "useIntl", "match", "useMatch", "id", "params", "isCreating", "navigate", "useNavigate", "toggleNotification", "useNotification", "_unstableFormatAPIError", "formatAPIError", "_unstableFormatValidationErrors", "formatValidationErrors", "useAPIErrorHandler", "stableFormatAPIError", "useCallback", "isTriggering", "setIsTriggering", "useState", "triggerResponse", "setTriggerResponse", "isLoading", "webhooks", "error", "createWebhook", "updateWebhook", "triggerWebhook", "useWebhooks", "skip", "React", "useEffect", "type", "message", "handleTriggerWebhook", "res", "defaultMessage", "handleSubmit", "helpers", "isBaseQueryError", "name", "setErrors", "replace", "_jsx", "Page", "Loading", "webhook", "_jsxs", "Main", "Title", "WebhookForm", "ProtectedEditPage", "permissions", "useTypedSelector", "selectAdminPermissions", "Protect", "settings", "update"]}