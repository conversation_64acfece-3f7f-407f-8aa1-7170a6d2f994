{"version": 3, "sources": ["../../../@strapi/admin/admin/src/pages/Settings/pages/Users/<USER>/CreateActionCE.tsx", "../../../@strapi/admin/admin/src/pages/Settings/pages/Users/<USER>/NewUserForm.tsx", "../../../@strapi/admin/admin/src/pages/Settings/pages/Users/<USER>"], "sourcesContent": ["import * as React from 'react';\n\nimport { Button, ButtonProps } from '@strapi/design-system';\nimport { Mail } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\n\ninterface CreateActionCEProps extends Pick<ButtonProps, 'onClick'> {}\n\nconst CreateActionCE = React.forwardRef<HTMLButtonElement, CreateActionCEProps>((props, ref) => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Button ref={ref} startIcon={<Mail />} size=\"S\" {...props}>\n      {formatMessage({\n        id: 'Settings.permissions.users.create',\n        defaultMessage: 'Invite new user',\n      })}\n    </Button>\n  );\n});\n\nexport { CreateActionCE };\nexport type { CreateActionCEProps };\n", "import * as React from 'react';\n\nimport {\n  Box,\n  Button,\n  Flex,\n  Grid,\n  Modal,\n  Typography,\n  Breadcrumbs,\n  Crumb,\n} from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\nimport * as yup from 'yup';\n\nimport { Form, type FormHelpers } from '../../../../../components/Form';\nimport { InputRenderer } from '../../../../../components/FormInputs/Renderer';\nimport { useNotification } from '../../../../../features/Notifications';\nimport { useAPIErrorHandler } from '../../../../../hooks/useAPIErrorHandler';\nimport { useEnterprise } from '../../../../../hooks/useEnterprise';\nimport { useCreateUserMutation } from '../../../../../services/users';\nimport { FormLayoutInputProps } from '../../../../../types/forms';\nimport { isBaseQueryError } from '../../../../../utils/baseQuery';\nimport { translatedErrors } from '../../../../../utils/translatedErrors';\n\nimport { MagicLinkCE } from './MagicLinkCE';\nimport { SelectRoles } from './SelectRoles';\n\nimport type { Data } from '@strapi/types';\n\ninterface ModalFormProps {\n  onToggle: () => void;\n}\n\ntype FormLayout = FormLayoutInputProps[][];\n\nconst ModalForm = ({ onToggle }: ModalFormProps) => {\n  const [currentStep, setStep] = React.useState<keyof typeof STEPPER>('create');\n  const [registrationToken, setRegistrationToken] = React.useState('');\n  const { formatMessage } = useIntl();\n  const { toggleNotification } = useNotification();\n  const {\n    _unstableFormatAPIError: formatAPIError,\n    _unstableFormatValidationErrors: formatValidationErrors,\n  } = useAPIErrorHandler();\n  const roleLayout = useEnterprise<FormLayout, FormLayout, FormLayout>(\n    ROLE_LAYOUT,\n    async () =>\n      (\n        await import(\n          '../../../../../../../ee/admin/src/pages/SettingsPage/pages/Users/<USER>/ModalForm'\n        )\n      ).ROLE_LAYOUT,\n    {\n      combine(ceRoles, eeRoles) {\n        return [...ceRoles, ...eeRoles];\n      },\n\n      defaultValue: [],\n    }\n  );\n\n  const initialValues = useEnterprise<InitialData>(\n    FORM_INITIAL_VALUES,\n    async () =>\n      (\n        await import(\n          '../../../../../../../ee/admin/src/pages/SettingsPage/pages/Users/<USER>/ModalForm'\n        )\n      ).FORM_INITIAL_VALUES,\n    {\n      combine(ceValues, eeValues) {\n        return {\n          ...ceValues,\n          ...eeValues,\n        };\n      },\n\n      defaultValue: FORM_INITIAL_VALUES,\n    }\n  );\n  const MagicLink = useEnterprise(\n    MagicLinkCE,\n    async () =>\n      (\n        await import(\n          '../../../../../../../ee/admin/src/pages/SettingsPage/pages/Users/<USER>/MagicLinkEE'\n        )\n      ).MagicLinkEE\n  );\n\n  const [createUser] = useCreateUserMutation();\n\n  const headerTitle = formatMessage({\n    id: 'Settings.permissions.users.create',\n    defaultMessage: 'Invite new user',\n  });\n\n  const handleSubmit = async (body: InitialData, { setErrors }: FormHelpers<InitialData>) => {\n    const res = await createUser({\n      ...body,\n      roles: body.roles ?? [],\n    });\n\n    if ('data' in res) {\n      // NOTE: when enabling SSO, the user doesn't have to register and the token is undefined\n      if (res.data.registrationToken) {\n        setRegistrationToken(res.data.registrationToken);\n      }\n\n      goNext();\n    } else {\n      toggleNotification({\n        type: 'danger',\n        message: formatAPIError(res.error),\n      });\n\n      if (isBaseQueryError(res.error) && res.error.name === 'ValidationError') {\n        setErrors(formatValidationErrors(res.error));\n      }\n    }\n  };\n\n  const goNext = () => {\n    if (next) {\n      setStep(next);\n    } else {\n      onToggle();\n    }\n  };\n\n  const { buttonSubmitLabel, isDisabled, next } = STEPPER[currentStep];\n\n  // block rendering until the EE component is fully loaded\n  if (!MagicLink) {\n    return null;\n  }\n\n  return (\n    <Modal.Root defaultOpen onOpenChange={onToggle}>\n      <Modal.Content>\n        <Modal.Header>\n          {/**\n           * TODO: this is not semantically correct and should be amended.\n           */}\n          <Breadcrumbs label={headerTitle}>\n            <Crumb isCurrent>{headerTitle}</Crumb>\n          </Breadcrumbs>\n        </Modal.Header>\n        <Form\n          method={currentStep === 'create' ? 'POST' : 'PUT'}\n          initialValues={initialValues ?? {}}\n          onSubmit={handleSubmit}\n          validationSchema={FORM_SCHEMA}\n        >\n          {({ isSubmitting }) => {\n            return (\n              <>\n                <Modal.Body>\n                  <Flex direction=\"column\" alignItems=\"stretch\" gap={6}>\n                    {currentStep !== 'create' && (\n                      <MagicLink registrationToken={registrationToken} />\n                    )}\n                    <Box>\n                      <Typography variant=\"beta\" tag=\"h2\">\n                        {formatMessage({\n                          id: 'app.components.Users.ModalCreateBody.block-title.details',\n                          defaultMessage: 'User details',\n                        })}\n                      </Typography>\n                      <Box paddingTop={4}>\n                        <Flex direction=\"column\" alignItems=\"stretch\" gap={1}>\n                          <Grid.Root gap={5}>\n                            {FORM_LAYOUT.map((row) => {\n                              return row.map(({ size, ...field }) => {\n                                return (\n                                  <Grid.Item\n                                    key={field.name}\n                                    col={size}\n                                    direction=\"column\"\n                                    alignItems=\"stretch\"\n                                  >\n                                    <InputRenderer\n                                      {...field}\n                                      disabled={isDisabled}\n                                      label={formatMessage(field.label)}\n                                      placeholder={formatMessage(field.placeholder)}\n                                    />\n                                  </Grid.Item>\n                                );\n                              });\n                            })}\n                          </Grid.Root>\n                        </Flex>\n                      </Box>\n                    </Box>\n                    <Box>\n                      <Typography variant=\"beta\" tag=\"h2\">\n                        {formatMessage({\n                          id: 'global.roles',\n                          defaultMessage: \"User's role\",\n                        })}\n                      </Typography>\n                      <Box paddingTop={4}>\n                        <Grid.Root gap={5}>\n                          <Grid.Item col={6} xs={12} direction=\"column\" alignItems=\"stretch\">\n                            <SelectRoles disabled={isDisabled} />\n                          </Grid.Item>\n                          {roleLayout.map((row) => {\n                            return row.map(({ size, ...field }) => {\n                              return (\n                                <Grid.Item\n                                  key={field.name}\n                                  col={size}\n                                  direction=\"column\"\n                                  alignItems=\"stretch\"\n                                >\n                                  <InputRenderer\n                                    {...field}\n                                    disabled={isDisabled}\n                                    label={formatMessage(field.label)}\n                                    placeholder={\n                                      field.placeholder\n                                        ? formatMessage(field.placeholder)\n                                        : undefined\n                                    }\n                                    hint={field.hint ? formatMessage(field.hint) : undefined}\n                                  />\n                                </Grid.Item>\n                              );\n                            });\n                          })}\n                        </Grid.Root>\n                      </Box>\n                    </Box>\n                  </Flex>\n                </Modal.Body>\n                <Modal.Footer>\n                  <Button variant=\"tertiary\" onClick={onToggle} type=\"button\">\n                    {formatMessage({\n                      id: 'app.components.Button.cancel',\n                      defaultMessage: 'Cancel',\n                    })}\n                  </Button>\n                  {currentStep === 'create' ? (\n                    <Button type=\"submit\" loading={isSubmitting}>\n                      {formatMessage(buttonSubmitLabel)}\n                    </Button>\n                  ) : (\n                    <Button type=\"button\" loading={isSubmitting} onClick={onToggle}>\n                      {formatMessage(buttonSubmitLabel)}\n                    </Button>\n                  )}\n                </Modal.Footer>\n              </>\n            );\n          }}\n        </Form>\n      </Modal.Content>\n    </Modal.Root>\n  );\n};\n\ninterface InitialData {\n  firstname?: string;\n  lastname?: string;\n  email?: string;\n  roles?: Data.ID[];\n  useSSORegistration?: boolean;\n}\n\nconst FORM_INITIAL_VALUES = {\n  firstname: '',\n  lastname: '',\n  email: '',\n  roles: [],\n};\n\nconst ROLE_LAYOUT: FormLayout = [];\n\nconst FORM_LAYOUT = [\n  [\n    {\n      label: {\n        id: 'Auth.form.firstname.label',\n        defaultMessage: 'First name',\n      },\n      name: 'firstname',\n      placeholder: {\n        id: 'Auth.form.firstname.placeholder',\n        defaultMessage: 'e.g. Kai',\n      },\n      type: 'string' as const,\n      size: 6,\n      required: true,\n    },\n    {\n      label: {\n        id: 'Auth.form.lastname.label',\n        defaultMessage: 'Last name',\n      },\n      name: 'lastname',\n      placeholder: {\n        id: 'Auth.form.lastname.placeholder',\n        defaultMessage: 'e.g. Doe',\n      },\n      type: 'string' as const,\n      size: 6,\n    },\n  ],\n  [\n    {\n      label: {\n        id: 'Auth.form.email.label',\n        defaultMessage: 'Email',\n      },\n      name: 'email',\n      placeholder: {\n        id: 'Auth.form.email.placeholder',\n        defaultMessage: 'e.g. <EMAIL>',\n      },\n      type: 'email' as const,\n      size: 6,\n      required: true,\n    },\n  ],\n] satisfies FormLayout;\n\nconst FORM_SCHEMA = yup.object().shape({\n  firstname: yup\n    .string()\n    .trim()\n    .required({\n      id: translatedErrors.required.id,\n      defaultMessage: 'This field is required',\n    })\n    .nullable(),\n  lastname: yup.string(),\n  email: yup\n    .string()\n    .email(translatedErrors.email)\n    .required({\n      id: translatedErrors.required.id,\n      defaultMessage: 'This field is required',\n    })\n    .nullable(),\n  roles: yup\n    .array()\n    .min(1, {\n      id: translatedErrors.required.id,\n      defaultMessage: 'This field is required',\n    })\n    .required({\n      id: translatedErrors.required.id,\n      defaultMessage: 'This field is required',\n    }),\n});\n\nconst STEPPER = {\n  create: {\n    buttonSubmitLabel: {\n      id: 'app.containers.Users.ModalForm.footer.button-success',\n      defaultMessage: 'Invite user',\n    },\n    isDisabled: false,\n    next: 'magic-link',\n  },\n  'magic-link': {\n    buttonSubmitLabel: { id: 'global.finish', defaultMessage: 'Finish' },\n    isDisabled: true,\n    next: null,\n  },\n} as const;\n\nexport { ModalForm };\nexport type { InitialData };\n", "import * as React from 'react';\n\nimport { <PERSON><PERSON>, <PERSON>po<PERSON>, Status, IconButton, Dialog } from '@strapi/design-system';\nimport { Pencil, Trash } from '@strapi/icons';\nimport * as qs from 'qs';\nimport { MessageDescriptor, useIntl } from 'react-intl';\nimport { NavLink, useLocation, useNavigate } from 'react-router-dom';\n\nimport { SanitizedAdminUser } from '../../../../../../shared/contracts/shared';\nimport { ConfirmDialog } from '../../../../components/ConfirmDialog';\nimport { Filters } from '../../../../components/Filters';\nimport { Layouts } from '../../../../components/Layouts/Layout';\nimport { Page } from '../../../../components/PageHelpers';\nimport { Pagination } from '../../../../components/Pagination';\nimport { SearchInput } from '../../../../components/SearchInput';\nimport { Table } from '../../../../components/Table';\nimport { useTypedSelector } from '../../../../core/store/hooks';\nimport { useNotification } from '../../../../features/Notifications';\nimport { useAPIErrorHandler } from '../../../../hooks/useAPIErrorHandler';\nimport { useEnterprise } from '../../../../hooks/useEnterprise';\nimport { useRBAC } from '../../../../hooks/useRBAC';\nimport { useAdminUsers, useDeleteManyUsersMutation } from '../../../../services/users';\nimport { getDisplayName } from '../../../../utils/users';\n\nimport { CreateActionCE } from './components/CreateActionCE';\nimport { ModalForm } from './components/NewUserForm';\n\n/* -------------------------------------------------------------------------------------------------\n * ListPageCE\n * -----------------------------------------------------------------------------------------------*/\n\nconst ListPageCE = () => {\n  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();\n  const [isModalOpened, setIsModalOpen] = React.useState(false);\n  const permissions = useTypedSelector((state) => state.admin_app.permissions);\n  const {\n    allowedActions: { canCreate, canDelete, canRead },\n  } = useRBAC(permissions.settings?.users);\n  const navigate = useNavigate();\n  const { toggleNotification } = useNotification();\n  const { formatMessage } = useIntl();\n  const { search } = useLocation();\n  const [showDeleteConfirmation, setShowDeleteConfirmation] = React.useState(false);\n  const [idsToDelete, setIdsToDelete] = React.useState<Array<SanitizedAdminUser['id']>>([]);\n  const { data, isError, isLoading } = useAdminUsers(qs.parse(search, { ignoreQueryPrefix: true }));\n\n  const { pagination, users = [] } = data ?? {};\n\n  const CreateAction = useEnterprise(\n    CreateActionCE,\n    async () =>\n      (\n        await import(\n          '../../../../../../ee/admin/src/pages/SettingsPage/pages/Users/<USER>/CreateActionEE'\n        )\n      ).CreateActionEE\n  );\n\n  const headers = TABLE_HEADERS.map((header) => ({\n    ...header,\n    label: formatMessage(header.label),\n  }));\n\n  const title = formatMessage({\n    id: 'global.users',\n    defaultMessage: 'Users',\n  });\n\n  const handleToggle = () => {\n    setIsModalOpen((prev) => !prev);\n  };\n\n  const [deleteAll] = useDeleteManyUsersMutation();\n  const handleDeleteAll = async (ids: Array<SanitizedAdminUser['id']>) => {\n    try {\n      const res = await deleteAll({ ids });\n\n      if ('error' in res) {\n        toggleNotification({\n          type: 'danger',\n          message: formatAPIError(res.error),\n        });\n      }\n    } catch (err) {\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({\n          id: 'global.error',\n          defaultMessage: 'An error occurred',\n        }),\n      });\n    }\n  };\n\n  const handleRowClick = (id: SanitizedAdminUser['id']) => () => {\n    if (canRead) {\n      navigate(id.toString());\n    }\n  };\n\n  const handleDeleteClick = (id: SanitizedAdminUser['id']) => async () => {\n    setIdsToDelete([id]);\n    setShowDeleteConfirmation(true);\n  };\n\n  const confirmDelete = async () => {\n    await handleDeleteAll(idsToDelete);\n    setShowDeleteConfirmation(false);\n  };\n\n  // block rendering until the EE component is fully loaded\n  if (!CreateAction) {\n    return null;\n  }\n\n  if (isError) {\n    return <Page.Error />;\n  }\n\n  return (\n    <Page.Main aria-busy={isLoading}>\n      <Page.Title>\n        {formatMessage(\n          { id: 'Settings.PageTitle', defaultMessage: 'Settings - {name}' },\n          {\n            name: 'Users',\n          }\n        )}\n      </Page.Title>\n      <Layouts.Header\n        primaryAction={canCreate && <CreateAction onClick={handleToggle} />}\n        title={title}\n        subtitle={formatMessage({\n          id: 'Settings.permissions.users.listview.header.subtitle',\n          defaultMessage: 'All the users who have access to the Strapi admin panel',\n        })}\n      />\n      <Layouts.Action\n        startActions={\n          <>\n            <SearchInput\n              label={formatMessage(\n                { id: 'app.component.search.label', defaultMessage: 'Search for {target}' },\n                { target: title }\n              )}\n            />\n            <Filters.Root options={FILTERS}>\n              <Filters.Trigger />\n              <Filters.Popover />\n              <Filters.List />\n            </Filters.Root>\n          </>\n        }\n      />\n      <Layouts.Content>\n        <Table.Root rows={users} headers={headers}>\n          <Table.ActionBar />\n          <Table.Content>\n            <Table.Head>\n              {canDelete ? <Table.HeaderCheckboxCell /> : null}\n              {headers.map((header) => (\n                <Table.HeaderCell key={header.name} {...header} />\n              ))}\n            </Table.Head>\n            <Table.Empty />\n            <Table.Loading />\n            <Table.Body>\n              {users.map((user) => (\n                <Table.Row\n                  key={user.id}\n                  onClick={handleRowClick(user.id)}\n                  cursor={canRead ? 'pointer' : 'default'}\n                >\n                  {canDelete ? <Table.CheckboxCell id={user.id} /> : null}\n                  {headers.map(({ cellFormatter, name, ...rest }) => {\n                    return (\n                      <Table.Cell key={name}>\n                        {typeof cellFormatter === 'function' ? (\n                          cellFormatter(user, { name, ...rest })\n                        ) : (\n                          // @ts-expect-error – name === \"roles\" has the data value of `AdminRole[]` but the header has a cellFormatter value so this shouldn't be called.\n                          <Typography textColor=\"neutral800\">{user[name] || '-'}</Typography>\n                        )}\n                      </Table.Cell>\n                    );\n                  })}\n                  {canRead || canDelete ? (\n                    <Table.Cell onClick={(e) => e.stopPropagation()}>\n                      <Flex justifyContent=\"end\">\n                        {canRead ? (\n                          <IconButton\n                            tag={NavLink}\n                            to={user.id.toString()}\n                            label={formatMessage(\n                              { id: 'app.component.table.edit', defaultMessage: 'Edit {target}' },\n                              { target: getDisplayName(user) }\n                            )}\n                            variant=\"ghost\"\n                          >\n                            <Pencil />\n                          </IconButton>\n                        ) : null}\n                        {canDelete ? (\n                          <IconButton\n                            onClick={handleDeleteClick(user.id)}\n                            label={formatMessage(\n                              { id: 'global.delete-target', defaultMessage: 'Delete {target}' },\n                              { target: getDisplayName(user) }\n                            )}\n                            variant=\"ghost\"\n                          >\n                            <Trash />\n                          </IconButton>\n                        ) : null}\n                      </Flex>\n                    </Table.Cell>\n                  ) : null}\n                </Table.Row>\n              ))}\n            </Table.Body>\n          </Table.Content>\n        </Table.Root>\n        <Pagination.Root {...pagination}>\n          <Pagination.PageSize />\n          <Pagination.Links />\n        </Pagination.Root>\n      </Layouts.Content>\n      {isModalOpened && <ModalForm onToggle={handleToggle} />}\n      <Dialog.Root open={showDeleteConfirmation} onOpenChange={setShowDeleteConfirmation}>\n        <ConfirmDialog onConfirm={confirmDelete} />\n      </Dialog.Root>\n    </Page.Main>\n  );\n};\n\nconst TABLE_HEADERS: Array<\n  Omit<Table.Header<SanitizedAdminUser, any>, 'label'> & { label: MessageDescriptor }\n> = [\n  {\n    name: 'firstname',\n    label: {\n      id: 'Settings.permissions.users.firstname',\n      defaultMessage: 'Firstname',\n    },\n    sortable: true,\n  },\n  {\n    name: 'lastname',\n    label: {\n      id: 'Settings.permissions.users.lastname',\n      defaultMessage: 'Lastname',\n    },\n    sortable: true,\n  },\n  {\n    name: 'email',\n    label: { id: 'Settings.permissions.users.email', defaultMessage: 'Email' },\n    sortable: true,\n  },\n  {\n    name: 'roles',\n    label: {\n      id: 'Settings.permissions.users.roles',\n      defaultMessage: 'Roles',\n    },\n    sortable: false,\n    cellFormatter({ roles }) {\n      return (\n        <Typography textColor=\"neutral800\">{roles.map((role) => role.name).join(',\\n')}</Typography>\n      );\n    },\n  },\n  {\n    name: 'username',\n    label: {\n      id: 'Settings.permissions.users.username',\n      defaultMessage: 'Username',\n    },\n    sortable: true,\n  },\n  {\n    name: 'isActive',\n    label: {\n      id: 'Settings.permissions.users.user-status',\n      defaultMessage: 'User status',\n    },\n    sortable: false,\n    cellFormatter({ isActive }) {\n      return (\n        <Flex>\n          <Status size=\"S\" variant={isActive ? 'success' : 'danger'}>\n            <Typography tag=\"span\" variant=\"omega\" fontWeight=\"bold\">\n              {isActive ? 'Active' : 'Inactive'}\n            </Typography>\n          </Status>\n        </Flex>\n      );\n    },\n  },\n];\n\nconst FILTERS = [\n  {\n    name: 'firstname',\n    label: 'Firstname',\n    type: 'string',\n  },\n  {\n    name: 'lastname',\n    label: 'Lastname',\n    type: 'string',\n  },\n  {\n    name: 'email',\n    label: 'Email',\n    type: 'email',\n  },\n  {\n    name: 'username',\n    label: 'Username',\n    type: 'string',\n  },\n  {\n    name: 'isActive',\n    label: 'Active user',\n    type: 'boolean',\n  },\n] satisfies Filters.Filter[];\n\n/* -------------------------------------------------------------------------------------------------\n * ListPage\n * -----------------------------------------------------------------------------------------------*/\n\n// component which determines whether this page should render the CE or EE page\nconst ListPage = () => {\n  const UsersListPage = useEnterprise(\n    ListPageCE,\n    async () =>\n      // eslint-disable-next-line import/no-cycle\n      (await import('../../../../../../ee/admin/src/pages/SettingsPage/pages/Users/<USER>'))\n        .UserListPageEE\n  );\n\n  // block rendering until the EE component is fully loaded\n  if (!UsersListPage) {\n    return null;\n  }\n\n  return <UsersListPage />;\n};\n\n/* -------------------------------------------------------------------------------------------------\n * ProtectedListPage\n * -----------------------------------------------------------------------------------------------*/\n\nconst ProtectedListPage = () => {\n  const permissions = useTypedSelector((state) => state.admin_app.permissions.settings?.users.read);\n\n  return (\n    <Page.Protect permissions={permissions}>\n      <ListPage />\n    </Page.Protect>\n  );\n};\n\nexport { ProtectedListPage, ListPage, ListPageCE };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQA,IAAMA,iBAAuBC,iBAAmD,CAACC,OAAOC,QAAAA;AACtF,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,aACEC,wBAACC,QAAAA;IAAOJ;IAAUK,eAAWF,wBAACG,eAAAA,CAAAA,CAAAA;IAASC,MAAK;IAAK,GAAGR;cACjDE,cAAc;MACbO,IAAI;MACJC,gBAAgB;IAClB,CAAA;;AAGN,CAAA;;;;;ACiBA,IAAMC,YAAY,CAAC,EAAEC,SAAQ,MAAkB;AAC7C,QAAM,CAACC,aAAaC,OAAAA,IAAiBC,gBAA+B,QAAA;AACpE,QAAM,CAACC,mBAAmBC,oBAAAA,IAA8BF,gBAAS,EAAA;AACjE,QAAM,EAAEG,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,EACJC,yBAAyBC,gBACzBC,iCAAiCC,uBAAsB,IACrDC,mBAAAA;AACJ,QAAMC,aAAaC,cACjBC,aACA,aAEI,MAAM,OACJ,yBAAA,GAEFA,aACJ;IACEC,QAAQC,SAASC,SAAO;AACtB,aAAO;QAAID,GAAAA;QAAYC,GAAAA;MAAQ;IACjC;IAEAC,cAAc,CAAA;EAChB,CAAA;AAGF,QAAMC,gBAAgBN,cACpBO,qBACA,aAEI,MAAM,OACJ,yBAAA,GAEFA,qBACJ;IACEL,QAAQM,UAAUC,UAAQ;AACxB,aAAO;QACL,GAAGD;QACH,GAAGC;MACL;IACF;IAEAJ,cAAcE;EAChB,CAAA;AAEF,QAAMG,YAAYV,cAChBW,aACA,aAEI,MAAM,OACJ,2BACF,GACAC,WAAW;AAGjB,QAAM,CAACC,UAAAA,IAAcC,sBAAAA;AAErB,QAAMC,cAAczB,cAAc;IAChC0B,IAAI;IACJC,gBAAgB;EAClB,CAAA;AAEA,QAAMC,eAAe,OAAOC,MAAmB,EAAEC,UAAS,MAA4B;AACpF,UAAMC,MAAM,MAAMR,WAAW;MAC3B,GAAGM;MACHG,OAAOH,KAAKG,SAAS,CAAA;IACvB,CAAA;AAEA,QAAI,UAAUD,KAAK;AAEjB,UAAIA,IAAIE,KAAKnC,mBAAmB;AAC9BC,6BAAqBgC,IAAIE,KAAKnC,iBAAiB;MACjD;AAEAoC,aAAAA;WACK;AACLhC,yBAAmB;QACjBiC,MAAM;QACNC,SAAS/B,eAAe0B,IAAIM,KAAK;MACnC,CAAA;AAEA,UAAIC,iBAAiBP,IAAIM,KAAK,KAAKN,IAAIM,MAAME,SAAS,mBAAmB;AACvET,kBAAUvB,uBAAuBwB,IAAIM,KAAK,CAAA;MAC5C;IACF;EACF;AAEA,QAAMH,SAAS,MAAA;AACb,QAAIM,MAAM;AACR5C,cAAQ4C,IAAAA;WACH;AACL9C,eAAAA;IACF;EACF;AAEA,QAAM,EAAE+C,mBAAmBC,YAAYF,KAAI,IAAKG,QAAQhD,WAAY;AAGpE,MAAI,CAACyB,WAAW;AACd,WAAO;EACT;AAEA,aACEwB,yBAACC,MAAMC,MAAI;IAACC,aAAW;IAACC,cAActD;kBACpCuD,0BAACJ,MAAMK,SAAO;;YACZN,yBAACC,MAAMM,QAAM;UAIX,cAAAP,yBAACQ,aAAAA;YAAYC,OAAO5B;YAClB,cAAAmB,yBAACU,OAAAA;cAAMC,WAAS;cAAE9B,UAAAA;;;;YAGtBmB,yBAACY,MAAAA;UACCC,QAAQ9D,gBAAgB,WAAW,SAAS;UAC5CqB,eAAeA,iBAAiB,CAAA;UAChC0C,UAAU9B;UACV+B,kBAAkBC;oBAEjB,CAAC,EAAEC,aAAY,MAAE;AAChB,uBACEZ,0BAAAa,8BAAA;;oBACElB,yBAACC,MAAMkB,MAAI;kBACT,cAAAd,0BAACe,MAAAA;oBAAKC,WAAU;oBAASC,YAAW;oBAAUC,KAAK;;sBAChDxE,gBAAgB,gBACfiD,yBAACxB,WAAAA;wBAAUtB;;0BAEbmD,0BAACmB,KAAAA;;8BACCxB,yBAACyB,YAAAA;4BAAWC,SAAQ;4BAAOC,KAAI;sCAC5BvE,cAAc;8BACb0B,IAAI;8BACJC,gBAAgB;4BAClB,CAAA;;8BAEFiB,yBAACwB,KAAAA;4BAAII,YAAY;4BACf,cAAA5B,yBAACoB,MAAAA;8BAAKC,WAAU;8BAASC,YAAW;8BAAUC,KAAK;4CACjDvB,yBAAC6B,KAAK3B,MAAI;gCAACqB,KAAK;0CACbO,YAAYC,IAAI,CAACC,QAAAA;AAChB,yCAAOA,IAAID,IAAI,CAAC,EAAEE,MAAM,GAAGC,MAAO,MAAA;AAChC,+CACElC,yBAAC6B,KAAKM,MAAI;sCAERC,KAAKH;sCACLZ,WAAU;sCACVC,YAAW;sCAEX,cAAAtB,yBAACqC,uBAAAA;wCACE,GAAGH;wCACJI,UAAUxC;wCACVW,OAAOrD,cAAc8E,MAAMzB,KAAK;wCAChC8B,aAAanF,cAAc8E,MAAMK,WAAW;;oCATzCL,GAAAA,MAAMvC,IAAI;kCAarB,CAAA;gCACF,CAAA;;;;;;0BAKRU,0BAACmB,KAAAA;;8BACCxB,yBAACyB,YAAAA;4BAAWC,SAAQ;4BAAOC,KAAI;sCAC5BvE,cAAc;8BACb0B,IAAI;8BACJC,gBAAgB;4BAClB,CAAA;;8BAEFiB,yBAACwB,KAAAA;4BAAII,YAAY;0CACfvB,0BAACwB,KAAK3B,MAAI;8BAACqB,KAAK;;oCACdvB,yBAAC6B,KAAKM,MAAI;kCAACC,KAAK;kCAAGI,IAAI;kCAAInB,WAAU;kCAASC,YAAW;kCACvD,cAAAtB,yBAACyC,aAAAA;oCAAYH,UAAUxC;;;gCAExBjC,WAAWkE,IAAI,CAACC,QAAAA;AACf,yCAAOA,IAAID,IAAI,CAAC,EAAEE,MAAM,GAAGC,MAAO,MAAA;AAChC,+CACElC,yBAAC6B,KAAKM,MAAI;sCAERC,KAAKH;sCACLZ,WAAU;sCACVC,YAAW;sCAEX,cAAAtB,yBAACqC,uBAAAA;wCACE,GAAGH;wCACJI,UAAUxC;wCACVW,OAAOrD,cAAc8E,MAAMzB,KAAK;wCAChC8B,aACEL,MAAMK,cACFnF,cAAc8E,MAAMK,WAAW,IAC/BG;wCAENC,MAAMT,MAAMS,OAAOvF,cAAc8E,MAAMS,IAAI,IAAID;;oCAd5CR,GAAAA,MAAMvC,IAAI;kCAkBrB,CAAA;gCACF,CAAA;;;;;;;;;oBAMVU,0BAACJ,MAAM2C,QAAM;;wBACX5C,yBAAC6C,QAAAA;sBAAOnB,SAAQ;sBAAWoB,SAAShG;sBAAUyC,MAAK;gCAChDnC,cAAc;wBACb0B,IAAI;wBACJC,gBAAgB;sBAClB,CAAA;;oBAEDhC,gBAAgB,eACfiD,yBAAC6C,QAAAA;sBAAOtD,MAAK;sBAASwD,SAAS9B;gCAC5B7D,cAAcyC,iBAAAA;6BAGjBG,yBAAC6C,QAAAA;sBAAOtD,MAAK;sBAASwD,SAAS9B;sBAAc6B,SAAShG;gCACnDM,cAAcyC,iBAAAA;;;;;;UAM3B;;;;;AAKV;AAUA,IAAMxB,sBAAsB;EAC1B2E,WAAW;EACXC,UAAU;EACVC,OAAO;EACP9D,OAAO,CAAA;AACT;AAEA,IAAMrB,cAA0B,CAAA;AAEhC,IAAM+D,cAAc;EAClB;IACE;MACErB,OAAO;QACL3B,IAAI;QACJC,gBAAgB;MAClB;MACAY,MAAM;MACN4C,aAAa;QACXzD,IAAI;QACJC,gBAAgB;MAClB;MACAQ,MAAM;MACN0C,MAAM;MACNkB,UAAU;IACZ;IACA;MACE1C,OAAO;QACL3B,IAAI;QACJC,gBAAgB;MAClB;MACAY,MAAM;MACN4C,aAAa;QACXzD,IAAI;QACJC,gBAAgB;MAClB;MACAQ,MAAM;MACN0C,MAAM;IACR;EACD;EACD;IACE;MACExB,OAAO;QACL3B,IAAI;QACJC,gBAAgB;MAClB;MACAY,MAAM;MACN4C,aAAa;QACXzD,IAAI;QACJC,gBAAgB;MAClB;MACAQ,MAAM;MACN0C,MAAM;MACNkB,UAAU;IACZ;EACD;AACF;AAED,IAAMnC,cAAkBoC,QAAM,EAAGC,MAAM;EACrCL,WACGM,OAAM,EACNC,KAAI,EACJJ,SAAS;IACRrE,IAAI0E,YAAiBL,SAASrE;IAC9BC,gBAAgB;EAClB,CAAA,EACC0E,SAAQ;EACXR,UAAcK,OAAM;EACpBJ,OACGI,OAAM,EACNJ,MAAMM,YAAiBN,KAAK,EAC5BC,SAAS;IACRrE,IAAI0E,YAAiBL,SAASrE;IAC9BC,gBAAgB;EAClB,CAAA,EACC0E,SAAQ;EACXrE,OACGsE,QAAK,EACLC,IAAI,GAAG;IACN7E,IAAI0E,YAAiBL,SAASrE;IAC9BC,gBAAgB;EAClB,CAAA,EACCoE,SAAS;IACRrE,IAAI0E,YAAiBL,SAASrE;IAC9BC,gBAAgB;EAClB,CAAA;AACJ,CAAA;AAEA,IAAMgB,UAAU;EACd6D,QAAQ;IACN/D,mBAAmB;MACjBf,IAAI;MACJC,gBAAgB;IAClB;IACAe,YAAY;IACZF,MAAM;EACR;EACA,cAAc;IACZC,mBAAmB;MAAEf,IAAI;MAAiBC,gBAAgB;IAAS;IACnEe,YAAY;IACZF,MAAM;EACR;AACF;;;ACvVkG,IAE5FiE,aAAa,MAAA;;AACjB,QAAM,EAAEC,yBAAyBC,eAAc,IAAKC,mBAAAA;AACpD,QAAM,CAACC,eAAeC,cAAAA,IAAwBC,gBAAS,KAAA;AACvD,QAAMC,cAAcC,iBAAiB,CAACC,UAAUA,MAAMC,UAAUH,WAAW;AAC3E,QAAM,EACJI,gBAAgB,EAAEC,WAAWC,WAAWC,QAAO,EAAE,IAC/CC,SAAQR,iBAAYS,aAAZT,mBAAsBU,KAAAA;AAClC,QAAMC,WAAWC,YAAAA;AACjB,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,OAAM,IAAKC,YAAAA;AACnB,QAAM,CAACC,wBAAwBC,yBAAAA,IAAmCrB,gBAAS,KAAA;AAC3E,QAAM,CAACsB,aAAaC,cAAAA,IAAwBvB,gBAA0C,CAAA,CAAE;AACxF,QAAM,EAAEwB,MAAMC,SAASC,UAAS,IAAKC,cAAiBC,SAAMV,QAAQ;IAAEW,mBAAmB;EAAK,CAAA,CAAA;AAE9F,QAAM,EAAEC,YAAYnB,QAAQ,CAAA,EAAE,IAAKa,QAAQ,CAAA;AAE3C,QAAMO,eAAeC,cACnBC,gBACA,aAEI,MAAM,OACJ,8BACF,GACAC,cAAc;AAGpB,QAAMC,UAAUC,cAAcC,IAAI,CAACC,YAAY;IAC7C,GAAGA;IACHC,OAAOvB,cAAcsB,OAAOC,KAAK;IACnC;AAEA,QAAMC,QAAQxB,cAAc;IAC1ByB,IAAI;IACJC,gBAAgB;EAClB,CAAA;AAEA,QAAMC,eAAe,MAAA;AACnB5C,mBAAe,CAAC6C,SAAS,CAACA,IAAAA;EAC5B;AAEA,QAAM,CAACC,SAAAA,IAAaC,2BAAAA;AACpB,QAAMC,kBAAkB,OAAOC,QAAAA;AAC7B,QAAI;AACF,YAAMC,MAAM,MAAMJ,UAAU;QAAEG;MAAI,CAAA;AAElC,UAAI,WAAWC,KAAK;AAClBnC,2BAAmB;UACjBoC,MAAM;UACNC,SAASvD,eAAeqD,IAAIG,KAAK;QACnC,CAAA;MACF;IACF,SAASC,KAAK;AACZvC,yBAAmB;QACjBoC,MAAM;QACNC,SAASnC,cAAc;UACrByB,IAAI;UACJC,gBAAgB;QAClB,CAAA;MACF,CAAA;IACF;EACF;AAEA,QAAMY,iBAAiB,CAACb,OAAiC,MAAA;AACvD,QAAIjC,SAAS;AACXI,eAAS6B,GAAGc,SAAQ,CAAA;IACtB;EACF;AAEA,QAAMC,oBAAoB,CAACf,OAAiC,YAAA;AAC1DlB,mBAAe;MAACkB;IAAG,CAAA;AACnBpB,8BAA0B,IAAA;EAC5B;AAEA,QAAMoC,gBAAgB,YAAA;AACpB,UAAMV,gBAAgBzB,WAAAA;AACtBD,8BAA0B,KAAA;EAC5B;AAGA,MAAI,CAACU,cAAc;AACjB,WAAO;EACT;AAEA,MAAIN,SAAS;AACX,eAAOiC,yBAACC,KAAKC,OAAK,CAAA,CAAA;EACpB;AAEA,aACEC,0BAACF,KAAKG,MAAI;IAACC,aAAWrC;;UACpBgC,yBAACC,KAAKK,OAAK;kBACRhD,cACC;UAAEyB,IAAI;UAAsBC,gBAAgB;WAC5C;UACEuB,MAAM;QACR,CAAA;;UAGJP,yBAACQ,QAAQC,QAAM;QACbC,eAAe9D,iBAAaoD,yBAAC3B,cAAAA;UAAasC,SAAS1B;;QACnDH;QACA8B,UAAUtD,cAAc;UACtByB,IAAI;UACJC,gBAAgB;QAClB,CAAA;;UAEFgB,yBAACQ,QAAQK,QAAM;QACbC,kBACEX,0BAAAY,8BAAA;;gBACEf,yBAACgB,aAAAA;cACCnC,OAAOvB,cACL;gBAAEyB,IAAI;gBAA8BC,gBAAgB;iBACpD;gBAAEiC,QAAQnC;cAAM,CAAA;;gBAGpBqB,0BAACe,QAAQC,MAAI;cAACC,SAASC;;oBACrBrB,yBAACkB,QAAQI,SAAO,CAAA,CAAA;oBAChBtB,yBAACkB,QAAQK,SAAO,CAAA,CAAA;oBAChBvB,yBAACkB,QAAQM,MAAI,CAAA,CAAA;;;;;;UAKrBrB,0BAACK,QAAQiB,SAAO;;cACdtB,0BAACuB,MAAMP,MAAI;YAACQ,MAAM1E;YAAOwB;;kBACvBuB,yBAAC0B,MAAME,WAAS,CAAA,CAAA;kBAChBzB,0BAACuB,MAAMD,SAAO;;sBACZtB,0BAACuB,MAAMG,MAAI;;sBACRhF,gBAAYmD,yBAAC0B,MAAMI,oBAAwB,CAAA,CAAA,IAAA;sBAC3CrD,QAAQE,IAAI,CAACC,eACZoB,yBAAC0B,MAAMK,YAAU;wBAAoB,GAAGnD;sBAAjBA,GAAAA,OAAO2B,IAAI,CAAA;;;sBAGtCP,yBAAC0B,MAAMM,OAAK,CAAA,CAAA;sBACZhC,yBAAC0B,MAAMO,SAAO,CAAA,CAAA;sBACdjC,yBAAC0B,MAAMQ,MAAI;oBACRjF,UAAAA,MAAM0B,IAAI,CAACwD,aACVhC,0BAACuB,MAAMU,KAAG;sBAERzB,SAASf,eAAeuC,KAAKpD,EAAE;sBAC/BsD,QAAQvF,UAAU,YAAY;;wBAE7BD,gBAAYmD,yBAAC0B,MAAMY,cAAY;0BAACvD,IAAIoD,KAAKpD;wBAAS,CAAA,IAAA;wBAClDN,QAAQE,IAAI,CAAC,EAAE4D,eAAehC,MAAM,GAAGiC,KAAM,MAAA;AAC5C,qCACExC,yBAAC0B,MAAMe,MAAI;sCACR,OAAOF,kBAAkB,aACxBA,cAAcJ,MAAM;8BAAE5B;8BAAM,GAAGiC;4BAAK,CAAA;;kCAGpCxC,yBAAC0C,YAAAA;gCAAWC,WAAU;0CAAcR,KAAK5B,IAAAA,KAAS;;;0BALrCA,GAAAA,IAAAA;wBASrB,CAAA;wBACCzD,WAAWD,gBACVmD,yBAAC0B,MAAMe,MAAI;0BAAC9B,SAAS,CAACiC,MAAMA,EAAEC,gBAAe;0BAC3C,cAAA1C,0BAAC2C,MAAAA;4BAAKC,gBAAe;;8BAClBjG,cACCkD,yBAACgD,YAAAA;gCACCC,KAAKC;gCACLC,IAAIhB,KAAKpD,GAAGc,SAAQ;gCACpBhB,OAAOvB,cACL;kCAAEyB,IAAI;kCAA4BC,gBAAgB;mCAClD;kCAAEiC,QAAQmC,eAAejB,IAAAA;gCAAM,CAAA;gCAEjCkB,SAAQ;gCAER,cAAArD,yBAACsD,eAAAA,CAAAA,CAAAA;8BAED,CAAA,IAAA;8BACHzG,gBACCmD,yBAACgD,YAAAA;gCACCrC,SAASb,kBAAkBqC,KAAKpD,EAAE;gCAClCF,OAAOvB,cACL;kCAAEyB,IAAI;kCAAwBC,gBAAgB;mCAC9C;kCAAEiC,QAAQmC,eAAejB,IAAAA;gCAAM,CAAA;gCAEjCkB,SAAQ;gCAER,cAAArD,yBAACuD,cAAAA,CAAAA,CAAAA;8BAED,CAAA,IAAA;;;wBAGN,CAAA,IAAA;;oBA/CCpB,GAAAA,KAAKpD,EAAE,CAAA;;;;;;cAqDtBoB,0BAACqD,WAAWrC,MAAI;YAAE,GAAG/C;;kBACnB4B,yBAACwD,WAAWC,UAAQ,CAAA,CAAA;kBACpBzD,yBAACwD,WAAWE,OAAK,CAAA,CAAA;;;;;MAGpBtH,qBAAiB4D,yBAAC2D,WAAAA;QAAUC,UAAU3E;;UACvCe,yBAAC6D,OAAO1C,MAAI;QAAC2C,MAAMpG;QAAwBqG,cAAcpG;QACvD,cAAAqC,yBAACgE,eAAAA;UAAcC,WAAWlE;;;;;AAIlC;AAEA,IAAMrB,gBAEF;EACF;IACE6B,MAAM;IACN1B,OAAO;MACLE,IAAI;MACJC,gBAAgB;IAClB;IACAkF,UAAU;EACZ;EACA;IACE3D,MAAM;IACN1B,OAAO;MACLE,IAAI;MACJC,gBAAgB;IAClB;IACAkF,UAAU;EACZ;EACA;IACE3D,MAAM;IACN1B,OAAO;MAAEE,IAAI;MAAoCC,gBAAgB;IAAQ;IACzEkF,UAAU;EACZ;EACA;IACE3D,MAAM;IACN1B,OAAO;MACLE,IAAI;MACJC,gBAAgB;IAClB;IACAkF,UAAU;IACV3B,cAAc,EAAE4B,MAAK,GAAE;AACrB,iBACEnE,yBAAC0C,YAAAA;QAAWC,WAAU;kBAAcwB,MAAMxF,IAAI,CAACyF,SAASA,KAAK7D,IAAI,EAAE8D,KAAK,KAAA;;IAE5E;EACF;EACA;IACE9D,MAAM;IACN1B,OAAO;MACLE,IAAI;MACJC,gBAAgB;IAClB;IACAkF,UAAU;EACZ;EACA;IACE3D,MAAM;IACN1B,OAAO;MACLE,IAAI;MACJC,gBAAgB;IAClB;IACAkF,UAAU;IACV3B,cAAc,EAAE+B,SAAQ,GAAE;AACxB,iBACEtE,yBAAC8C,MAAAA;QACC,cAAA9C,yBAACuE,QAAAA;UAAOC,MAAK;UAAInB,SAASiB,WAAW,YAAY;UAC/C,cAAAtE,yBAAC0C,YAAAA;YAAWO,KAAI;YAAOI,SAAQ;YAAQoB,YAAW;YAC/CH,UAAAA,WAAW,WAAW;;;;IAKjC;EACF;AACD;AAED,IAAMjD,UAAU;EACd;IACEd,MAAM;IACN1B,OAAO;IACPW,MAAM;EACR;EACA;IACEe,MAAM;IACN1B,OAAO;IACPW,MAAM;EACR;EACA;IACEe,MAAM;IACN1B,OAAO;IACPW,MAAM;EACR;EACA;IACEe,MAAM;IACN1B,OAAO;IACPW,MAAM;EACR;EACA;IACEe,MAAM;IACN1B,OAAO;IACPW,MAAM;EACR;AACD;AAOD,IAAMkF,WAAW,MAAA;AACf,QAAMC,gBAAgBrG,cACpBtC,YACA;;KAEG,MAAM,OAAO,wBAAA,GACX4I;GAAc;AAIrB,MAAI,CAACD,eAAe;AAClB,WAAO;EACT;AAEA,aAAO3E,yBAAC2E,eAAAA,CAAAA,CAAAA;AACV;AAIkG,IAE5FE,oBAAoB,MAAA;AACxB,QAAMtI,cAAcC,iBAAiB,CAACC,UAAUA;;AAAAA,uBAAMC,UAAUH,YAAYS,aAA5BP,mBAAsCQ,MAAM6H;GAAAA;AAE5F,aACE9E,yBAACC,KAAK8E,SAAO;IAACxI;IACZ,cAAAyD,yBAAC0E,UAAAA,CAAAA,CAAAA;;AAGP;", "names": ["CreateActionCE", "forwardRef", "props", "ref", "formatMessage", "useIntl", "_jsx", "<PERSON><PERSON>", "startIcon", "Mail", "size", "id", "defaultMessage", "ModalForm", "onToggle", "currentStep", "setStep", "useState", "registrationToken", "setRegistrationToken", "formatMessage", "useIntl", "toggleNotification", "useNotification", "_unstableFormatAPIError", "formatAPIError", "_unstableFormatValidationErrors", "formatValidationErrors", "useAPIErrorHandler", "roleLayout", "useEnterprise", "ROLE_LAYOUT", "combine", "ceRoles", "eeRoles", "defaultValue", "initialValues", "FORM_INITIAL_VALUES", "ceValues", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MagicLink", "MagicLinkCE", "MagicLinkEE", "createUser", "useCreateUserMutation", "headerTitle", "id", "defaultMessage", "handleSubmit", "body", "setErrors", "res", "roles", "data", "goNext", "type", "message", "error", "isBaseQueryError", "name", "next", "buttonSubmitLabel", "isDisabled", "STEPPER", "_jsx", "Modal", "Root", "defaultOpen", "onOpenChange", "_jsxs", "Content", "Header", "Breadcrumbs", "label", "Crumb", "isCurrent", "Form", "method", "onSubmit", "validationSchema", "FORM_SCHEMA", "isSubmitting", "_Fragment", "Body", "Flex", "direction", "alignItems", "gap", "Box", "Typography", "variant", "tag", "paddingTop", "Grid", "FORM_LAYOUT", "map", "row", "size", "field", "<PERSON><PERSON>", "col", "InputR<PERSON><PERSON>", "disabled", "placeholder", "xs", "SelectRoles", "undefined", "hint", "Footer", "<PERSON><PERSON>", "onClick", "loading", "firstname", "lastname", "email", "required", "object", "shape", "string", "trim", "translatedErrors", "nullable", "array", "min", "create", "ListPageCE", "_unstableFormatAPIError", "formatAPIError", "useAPIErrorHandler", "isModalOpened", "setIsModalOpen", "useState", "permissions", "useTypedSelector", "state", "admin_app", "allowedActions", "canCreate", "canDelete", "canRead", "useRBAC", "settings", "users", "navigate", "useNavigate", "toggleNotification", "useNotification", "formatMessage", "useIntl", "search", "useLocation", "showDeleteConfirmation", "setShowDeleteConfirmation", "idsToDelete", "setIdsToDelete", "data", "isError", "isLoading", "useAdminUsers", "parse", "ignoreQueryPrefix", "pagination", "CreateAction", "useEnterprise", "CreateActionCE", "CreateActionEE", "headers", "TABLE_HEADERS", "map", "header", "label", "title", "id", "defaultMessage", "handleToggle", "prev", "deleteAll", "useDeleteManyUsersMutation", "handleDeleteAll", "ids", "res", "type", "message", "error", "err", "handleRowClick", "toString", "handleDeleteClick", "confirmDelete", "_jsx", "Page", "Error", "_jsxs", "Main", "aria-busy", "Title", "name", "Layouts", "Header", "primaryAction", "onClick", "subtitle", "Action", "startActions", "_Fragment", "SearchInput", "target", "Filters", "Root", "options", "FILTERS", "<PERSON><PERSON>", "Popover", "List", "Content", "Table", "rows", "ActionBar", "Head", "HeaderCheckboxCell", "<PERSON><PERSON><PERSON><PERSON>", "Empty", "Loading", "Body", "user", "Row", "cursor", "CheckboxCell", "cell<PERSON>ormatt<PERSON>", "rest", "Cell", "Typography", "textColor", "e", "stopPropagation", "Flex", "justifyContent", "IconButton", "tag", "NavLink", "to", "getDisplayName", "variant", "Pencil", "Trash", "Pagination", "PageSize", "Links", "ModalForm", "onToggle", "Dialog", "open", "onOpenChange", "ConfirmDialog", "onConfirm", "sortable", "roles", "role", "join", "isActive", "Status", "size", "fontWeight", "ListPage", "UsersListPage", "UserListPageEE", "ProtectedListPage", "read", "Protect"]}