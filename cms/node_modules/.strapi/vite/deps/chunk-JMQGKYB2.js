import {
  adminApi
} from "./chunk-XU64YSEO.js";

// node_modules/@strapi/admin/dist/admin/admin/src/services/apiTokens.mjs
var apiTokensService = adminApi.enhanceEndpoints({
  addTagTypes: [
    "ApiToken",
    "GuidedTourMeta"
  ]
}).injectEndpoints({
  endpoints: (builder) => ({
    getAPITokens: builder.query({
      query: () => "/admin/api-tokens",
      transformResponse: (response) => response.data,
      providesTags: (res, _err) => [
        ...(res == null ? void 0 : res.map(({ id }) => ({
          type: "ApiToken",
          id
        }))) ?? [],
        {
          type: "ApiToken",
          id: "LIST"
        }
      ]
    }),
    getAPIToken: builder.query({
      query: (id) => `/admin/api-tokens/${id}`,
      transformResponse: (response) => response.data,
      providesTags: (res, _err, id) => [
        {
          type: "ApiToken",
          id
        }
      ]
    }),
    createAPIToken: builder.mutation({
      query: (body) => ({
        url: "/admin/api-tokens",
        method: "POST",
        data: body
      }),
      transformResponse: (response) => response.data,
      invalidatesTags: [
        {
          type: "ApiToken",
          id: "LIST"
        },
        "GuidedTourMeta"
      ]
    }),
    deleteAPIToken: builder.mutation({
      query: (id) => ({
        url: `/admin/api-tokens/${id}`,
        method: "DELETE"
      }),
      transformResponse: (response) => response.data,
      invalidatesTags: (_res, _err, id) => [
        {
          type: "ApiToken",
          id
        }
      ]
    }),
    updateAPIToken: builder.mutation({
      query: ({ id, ...body }) => ({
        url: `/admin/api-tokens/${id}`,
        method: "PUT",
        data: body
      }),
      transformResponse: (response) => response.data,
      invalidatesTags: (_res, _err, { id }) => [
        {
          type: "ApiToken",
          id
        }
      ]
    })
  })
});
var { useGetAPITokensQuery, useGetAPITokenQuery, useCreateAPITokenMutation, useDeleteAPITokenMutation, useUpdateAPITokenMutation } = apiTokensService;

export {
  useGetAPITokensQuery,
  useGetAPITokenQuery,
  useCreateAPITokenMutation,
  useDeleteAPITokenMutation,
  useUpdateAPITokenMutation
};
//# sourceMappingURL=chunk-JMQGKYB2.js.map
