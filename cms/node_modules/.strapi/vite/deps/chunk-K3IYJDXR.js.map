{"version": 3, "sources": ["../../../@strapi/content-type-builder/admin/src/utils/getTrad.ts", "../../../@strapi/content-type-builder/admin/src/components/DataManager/DataManagerContext.ts", "../../../@strapi/content-type-builder/admin/src/components/DataManager/useDataManager.ts", "../../../@strapi/content-type-builder/admin/src/components/FormModalNavigation/FormModalNavigationContext.ts", "../../../@strapi/content-type-builder/admin/src/components/FormModalNavigation/useFormModalNavigation.ts", "../../../@strapi/content-type-builder/admin/src/components/Status.tsx", "../../../@strapi/content-type-builder/admin/src/components/AttributeIcon.tsx", "../../../@strapi/content-type-builder/admin/src/components/IconPicker/constants.ts", "../../../@strapi/content-type-builder/admin/src/utils/conditions.ts"], "sourcesContent": ["import { pluginId } from '../pluginId';\n\nexport const getTrad = (id: string) => `${pluginId}.${id}`;\n", "/* eslint-disable check-file/filename-naming-convention */\nimport { createContext } from 'react';\n\nimport type { Component, ContentType } from '../../types';\nimport type { Internal, Struct } from '@strapi/types';\n\nexport interface DataManagerContextValue {\n  isLoading: boolean;\n  addAttribute: (opts: {\n    attributeToSet: Record<string, any>;\n    forTarget: Struct.ModelType;\n    targetUid: Internal.UID.Schema;\n  }) => void;\n  editAttribute: (opts: {\n    attributeToSet: Record<string, any>;\n    forTarget: Struct.ModelType;\n    targetUid: Internal.UID.Schema;\n    name: string;\n  }) => void;\n  moveAttribute: (opts: {\n    forTarget: Struct.ModelType;\n    targetUid: Internal.UID.Schema;\n    from: number;\n    to: number;\n  }) => void;\n  addCustomFieldAttribute: (params: {\n    attributeToSet: Record<string, any>;\n    forTarget: Struct.ModelType;\n    targetUid: Internal.UID.Schema;\n  }) => void;\n  editCustomFieldAttribute: (params: {\n    attributeToSet: Record<string, any>;\n    forTarget: Struct.ModelType;\n    targetUid: Internal.UID.Schema;\n    name: string;\n  }) => void;\n  addCreatedComponentToDynamicZone: (opts: {\n    forTarget: Struct.ModelType;\n    targetUid: Internal.UID.Schema;\n    dynamicZoneTarget: string;\n    componentsToAdd: Internal.UID.Component[];\n  }) => void;\n  createComponentSchema: (opts: {\n    data: {\n      icon: string;\n      displayName: string;\n    };\n    componentCategory: string;\n    uid: Internal.UID.Component;\n  }) => void;\n  createSchema: (opts: {\n    data: {\n      displayName: string;\n      singularName: string;\n      pluralName: string;\n      kind: Struct.ContentTypeKind;\n      draftAndPublish: boolean;\n      pluginOptions: Record<string, any>;\n    };\n    uid: Internal.UID.Schema;\n  }) => void;\n  changeDynamicZoneComponents: (opts: {\n    forTarget: Struct.ModelType;\n    targetUid: Internal.UID.Schema;\n    dynamicZoneTarget: string;\n    newComponents: Internal.UID.Component[];\n  }) => void;\n  removeAttribute: (opts: {\n    forTarget: Struct.ModelType;\n    targetUid: Internal.UID.Schema;\n    attributeToRemoveName: string;\n  }) => void;\n  deleteComponent(uid: Internal.UID.Component): void;\n  deleteContentType(uid: Internal.UID.ContentType): void;\n  removeComponentFromDynamicZone: (opts: {\n    forTarget: Struct.ModelType;\n    targetUid: Internal.UID.Schema;\n    dzName: string;\n    componentToRemoveIndex: number;\n  }) => void;\n  sortedContentTypesList: {\n    visible: ContentType['visible'];\n    name: ContentType['uid'];\n    title: ContentType['info']['displayName'];\n    plugin: ContentType['plugin'];\n    uid: ContentType['uid'];\n    to: string;\n    kind: ContentType['kind'];\n    restrictRelationsTo: ContentType['restrictRelationsTo'];\n    status: ContentType['status'];\n  }[];\n  updateComponentSchema: (opts: {\n    data: {\n      icon: string;\n      displayName: string;\n    };\n    componentUID: Internal.UID.Component;\n  }) => void;\n  updateComponentUid: (opts: {\n    newComponentUID: Internal.UID.Component;\n    componentUID: Internal.UID.Component;\n  }) => void;\n  updateSchema: (opts: {\n    data: {\n      displayName: string;\n      kind: Struct.ContentTypeKind;\n      draftAndPublish: boolean;\n      pluginOptions: Record<string, any>;\n    };\n    uid: Internal.UID.ContentType;\n  }) => void;\n  initialComponents: Record<Internal.UID.Component, Component>;\n  components: Record<Internal.UID.Component, Component>;\n  componentsGroupedByCategory: Record<string, Component[]>;\n  componentsThatHaveOtherComponentInTheirAttributes: any[]; // Define the actual type\n  initialContentTypes: Record<Internal.UID.ContentType, ContentType>;\n  contentTypes: Record<Internal.UID.ContentType, ContentType>;\n  isInDevelopmentMode?: boolean;\n  nestedComponents: any[]; // Define the actual type\n  reservedNames: {\n    models: string[];\n    attributes: string[];\n  };\n  allComponentsCategories: any[];\n  saveSchema(): Promise<void>;\n  isModified: boolean;\n  isSaving: boolean;\n  applyChange: (opts: {\n    action: 'add' | 'update' | 'delete';\n    schema: Component | ContentType;\n  }) => void;\n  history: {\n    undo(): void;\n    redo(): void;\n    discardAllChanges(): void;\n    canUndo: boolean;\n    canRedo: boolean;\n    canDiscardAll: boolean;\n  };\n}\n\n// @ts-expect-error need to pass initial value to params\nexport const DataManagerContext = createContext<DataManagerContextValue>();\n", "import { useContext } from 'react';\n\nimport { DataManagerContext } from './DataManagerContext';\n\nexport const useDataManager = () => useContext(DataManagerContext);\n", "/* eslint-disable check-file/filename-naming-convention */\nimport * as React from 'react';\n\nimport type {\n  State,\n  SelectFieldPayload,\n  SelectCustomFieldPayload,\n  NavigateToChooseAttributeModalPayload,\n  NavigateToAddCompoToDZModalPayload,\n  OpenModalAddComponentsToDZPayload,\n  OpenModalEditFieldPayload,\n  OpenModalEditCustomFieldPayload,\n  OpenModalEditSchemaPayload,\n  OpenModalAddFieldPayload,\n} from './FormModalNavigationProvider';\n\nexport type FormModalNavigationContextValue = State & {\n  onCloseModal: () => void;\n  onOpenModalAddField: (options: OpenModalAddFieldPayload) => void;\n  onClickSelectField: (option: SelectFieldPayload) => void;\n  onClickSelectCustomField: (option: SelectCustomFieldPayload) => void;\n  onNavigateToChooseAttributeModal: (options: NavigateToChooseAttributeModalPayload) => void;\n  onNavigateToAddCompoToDZModal: (options: NavigateToAddCompoToDZModalPayload) => void;\n  onOpenModalAddComponentsToDZ: (options: OpenModalAddComponentsToDZPayload) => void;\n  onNavigateToCreateComponentStep2: () => void;\n  onOpenModalCreateSchema: (options: State) => void;\n  onOpenModalEditField: (options: OpenModalEditFieldPayload) => void;\n  onOpenModalEditCustomField: (options: OpenModalEditCustomFieldPayload) => void;\n  onOpenModalEditSchema: (options: OpenModalEditSchemaPayload) => void;\n  setFormModalNavigationState: (value: React.SetStateAction<State>) => void;\n  setActiveTab: (value: State['activeTab']) => void;\n};\n\n// @ts-expect-error need to pass initial value to params\nexport const FormModalNavigationContext = React.createContext<FormModalNavigationContextValue>();\n", "import { useContext } from 'react';\n\nimport { FormModalNavigationContext } from './FormModalNavigationContext';\n\nexport const useFormModalNavigation = () => useContext(FormModalNavigationContext);\n", "import { Typography, Badge } from '@strapi/design-system';\n\nexport const Status = ({ status }: { status: string }) => {\n  switch (status) {\n    case 'UNCHANGED':\n      return null;\n    case 'CHANGED':\n      return (\n        <Typography fontWeight=\"semiBold\" textColor=\"alternative500\">\n          M\n        </Typography>\n      );\n    case 'REMOVED':\n      return (\n        <Typography fontWeight=\"semiBold\" textColor=\"danger500\">\n          D\n        </Typography>\n      );\n    case 'NEW':\n      return (\n        <Typography fontWeight=\"semiBold\" textColor=\"success500\">\n          N\n        </Typography>\n      );\n  }\n};\n\nexport const StatusBadge = ({ status }: { status: string }) => {\n  switch (status) {\n    case 'CHANGED':\n      return (\n        <Badge\n          fontWeight=\"bold\"\n          textColor=\"alternative600\"\n          backgroundColor=\"alternative100\"\n          borderColor=\"alternative200\"\n        >\n          Modified\n        </Badge>\n      );\n    case 'REMOVED':\n      return (\n        <Badge\n          fontWeight=\"bold\"\n          textColor=\"danger600\"\n          backgroundColor=\"danger100\"\n          borderColor=\"danger200\"\n        >\n          Deleted\n        </Badge>\n      );\n    case 'NEW':\n      return (\n        <Badge\n          fontWeight=\"bold\"\n          textColor=\"success600\"\n          backgroundColor=\"success100\"\n          borderColor=\"success200\"\n        >\n          New\n        </Badge>\n      );\n    case 'UNCHANGED':\n    default:\n      return (\n        <Badge\n          style={{\n            visibility: 'hidden',\n          }}\n          fontWeight=\"bold\"\n          textColor=\"warning600\"\n          backgroundColor=\"warning100\"\n          borderColor=\"warning200\"\n        >\n          Unchanged\n        </Badge>\n      );\n  }\n};\n", "import { ComponentType, SVGProps } from 'react';\n\nimport { useStrapiApp } from '@strapi/admin/strapi-admin';\nimport { Box } from '@strapi/design-system';\nimport {\n  BooleanField,\n  CollectionType,\n  ComponentField,\n  DateField,\n  DynamicZoneField,\n  EmailField,\n  EnumerationField,\n  JsonField,\n  MediaField,\n  NumberField,\n  PasswordField,\n  RelationField,\n  MarkdownField,\n  SingleType,\n  TextField,\n  UidField,\n  BlocksField,\n} from '@strapi/icons/symbols';\nimport { styled } from 'styled-components';\n\nconst iconByTypes: Record<string, ComponentType<SVGProps<SVGSVGElement>>> = {\n  biginteger: <PERSON>Field,\n  blocks: BlocksField,\n  boolean: BooleanField,\n  collectionType: CollectionType,\n  component: ComponentField,\n  contentType: CollectionType,\n  date: DateField,\n  datetime: DateField,\n  decimal: NumberField,\n  dynamiczone: DynamicZoneField,\n  email: EmailField,\n  enum: EnumerationField,\n  enumeration: EnumerationField,\n  file: <PERSON>Field,\n  files: <PERSON><PERSON><PERSON>,\n  float: <PERSON><PERSON><PERSON>,\n  integer: <PERSON><PERSON><PERSON>,\n  json: <PERSON><PERSON><PERSON><PERSON>,\n  JSON: <PERSON><PERSON><PERSON><PERSON>,\n  media: <PERSON><PERSON><PERSON>,\n  number: <PERSON><PERSON><PERSON>,\n  password: <PERSON><PERSON><PERSON><PERSON>,\n  relation: <PERSON><PERSON><PERSON><PERSON>,\n  richtext: <PERSON><PERSON><PERSON><PERSON>,\n  singleType: SingleType,\n  string: TextField,\n  text: TextField,\n  time: DateField,\n  timestamp: DateField,\n  uid: UidField,\n};\n\nconst IconBox = styled(Box)`\n  svg {\n    height: 100%;\n    width: 100%;\n  }\n`;\n\nexport type IconByType = keyof typeof iconByTypes;\n\ntype AttributeIconProps = {\n  type: IconByType;\n  customField?: string | null;\n};\n\nexport const AttributeIcon = ({ type, customField = null, ...rest }: AttributeIconProps) => {\n  const getCustomField = useStrapiApp('AttributeIcon', (state) => state.customFields.get);\n\n  let Compo: any = iconByTypes[type];\n\n  if (customField) {\n    const customFieldObject = getCustomField(customField);\n    const icon = customFieldObject?.icon;\n    if (icon) {\n      Compo = icon;\n    }\n  }\n\n  if (!iconByTypes[type]) {\n    return null;\n  }\n\n  return (\n    <IconBox width=\"3.2rem\" height=\"3.2rem\" shrink={0} {...rest} aria-hidden>\n      <Box tag={Compo} />\n    </IconBox>\n  );\n};\n", "import * as Icons from '@strapi/icons';\nimport * as Symbols from '@strapi/icons/symbols';\n\nexport type Icon = (typeof Icons)[keyof typeof Icons] | (typeof Symbols)[keyof typeof Symbols];\n\nconst COMPONENT_ICONS: Record<string, Icon> = {\n  alien: Icons.Alien,\n  apps: Icons.GridNine,\n  archive: Icons.Archive,\n  arrowDown: Icons.ArrowDown,\n  arrowLeft: Icons.ArrowLeft,\n  arrowRight: Icons.ArrowRight,\n  arrowUp: Icons.ArrowUp,\n  attachment: Icons.Paperclip,\n  bell: Icons.Bell,\n  bold: Icons.Bold,\n  book: Icons.Book,\n  briefcase: Icons.Briefcase,\n  brush: Icons.PaintBrush,\n  bulletList: Icons.BulletList,\n  calendar: Icons.Calendar,\n  car: Icons.Car,\n  cast: Icons.Cast,\n  chartBubble: Icons.ChartBubble,\n  chartCircle: Icons.ChartCircle,\n  chartPie: Icons.Chart<PERSON>ie,\n  check: Icons.Check,\n  clock: Icons.Clock,\n  cloud: Icons.Cloud,\n  code: Icons.Code,\n  cog: Icons.Cog,\n  collapse: Icons.Collapse,\n  command: Icons.Command,\n  connector: Icons.Faders,\n  crop: Icons.Crop,\n  crown: Icons.Crown,\n  cup: Icons.Coffee,\n  cursor: Icons.Cursor,\n  dashboard: Icons.SquaresFour,\n  database: Icons.Database,\n  discuss: Icons.Discuss,\n  doctor: Icons.Stethoscope,\n  earth: Icons.Earth,\n  emotionHappy: Icons.EmotionHappy,\n  emotionUnhappy: Icons.EmotionUnhappy,\n  envelop: Icons.Mail,\n  exit: Icons.SignOut,\n  expand: Icons.Expand,\n  eye: Icons.Eye,\n  feather: Icons.Feather,\n  file: Icons.File,\n  fileError: Icons.FileError,\n  filePdf: Icons.FilePdf,\n  filter: Icons.Filter,\n  folder: Icons.Folder,\n  gate: Icons.CastleTurret,\n  gift: Icons.Gift,\n  globe: Icons.Globe,\n  grid: Icons.GridFour,\n  handHeart: Icons.HandHeart,\n  hashtag: Icons.Hashtag,\n  headphone: Icons.Headphones,\n  heart: Icons.Heart,\n  house: Icons.House,\n  information: Icons.Information,\n  italic: Icons.Italic,\n  key: Icons.Key,\n  landscape: Icons.Images,\n  layer: Icons.ListPlus,\n  layout: Icons.Layout,\n  lightbulb: Icons.Lightbulb,\n  link: Icons.Link,\n  lock: Icons.Lock,\n  magic: Icons.Magic,\n  manyToMany: Icons.ManyToMany,\n  manyToOne: Icons.ManyToOne,\n  manyWays: Icons.ManyWays,\n  medium: Symbols.Medium,\n  message: Icons.Message,\n  microphone: Icons.Microphone,\n  monitor: Icons.Monitor,\n  moon: Icons.Moon,\n  music: Icons.MusicNotes,\n  oneToMany: Icons.OneToMany,\n  oneToOne: Icons.OneToOne,\n  oneWay: Icons.OneWay,\n  paint: Icons.PaintBrush,\n  paintBrush: Icons.PaintBrush,\n  paperPlane: Icons.PaperPlane,\n  pencil: Icons.Pencil,\n  phone: Icons.Phone,\n  picture: Icons.Image,\n  pin: Icons.Pin,\n  pinMap: Icons.PinMap,\n  plane: Icons.Plane,\n  play: Icons.Play,\n  plus: Icons.Plus,\n  priceTag: Icons.PriceTag,\n  puzzle: Icons.PuzzlePiece,\n  question: Icons.Question,\n  quote: Icons.Quotes,\n  refresh: Icons.ArrowClockwise,\n  restaurant: Icons.Restaurant,\n  rocket: Icons.Rocket,\n  rotate: Icons.ArrowsCounterClockwise,\n  scissors: Icons.Scissors,\n  search: Icons.Search,\n  seed: Icons.Plant,\n  server: Icons.Server,\n  shield: Icons.Shield,\n  shirt: Icons.Shirt,\n  shoppingCart: Icons.ShoppingCart,\n  slideshow: Icons.PresentationChart,\n  stack: Icons.Stack,\n  star: Icons.Star,\n  store: Icons.Store,\n  strikeThrough: Icons.StrikeThrough,\n  sun: Icons.Sun,\n  television: Icons.Television,\n  thumbDown: Icons.ThumbDown,\n  thumbUp: Icons.ThumbUp,\n  train: Icons.Train,\n  twitter: Symbols.X,\n  typhoon: Icons.Typhoon,\n  underline: Icons.Underline,\n  user: Icons.User,\n  volumeMute: Icons.VolumeMute,\n  volumeUp: Icons.VolumeUp,\n  walk: Icons.Walk,\n  wheelchair: Icons.Wheelchair,\n  write: Icons.Feather,\n};\n\nexport { COMPONENT_ICONS };\n", "import type { AnyAttribute } from '../types';\n\ninterface DependentRow {\n  contentTypeUid: string;\n  contentType: string;\n  attribute: string;\n}\n\nexport const checkDependentRows = (\n  contentTypes: Record<string, any>,\n  fieldName: string\n): DependentRow[] => {\n  const dependentRows: DependentRow[] = [];\n\n  Object.entries(contentTypes).forEach(([contentTypeUid, contentType]: [string, any]) => {\n    if (contentType.attributes) {\n      // Handle both array and object formats of attributes\n      const attributes = Array.isArray(contentType.attributes)\n        ? contentType.attributes.reduce((acc: Record<string, any>, attr: any, index: number) => {\n            acc[index.toString()] = attr;\n            return acc;\n          }, {})\n        : contentType.attributes;\n\n      Object.entries(attributes).forEach(([attrName, attr]: [string, any]) => {\n        if (attr.conditions?.visible) {\n          Object.entries(attr.conditions.visible).forEach(([, conditions]) => {\n            const [fieldVar] = conditions as [{ var: string }, any];\n            // Check if this condition references our field\n            if (fieldVar && fieldVar.var === fieldName) {\n              dependentRows.push({\n                contentTypeUid,\n                contentType: contentType.info.displayName,\n                attribute: attr.name || attrName,\n              });\n            }\n          });\n        }\n      });\n    }\n  });\n  return dependentRows;\n};\n\nexport const formatCondition = (\n  condition: any,\n  availableFields: Array<{ name: string; type: string }>,\n  attributeName: string\n): string => {\n  if (!condition?.visible) {\n    return '';\n  }\n\n  const [[operator, conditions]] = Object.entries(condition.visible);\n  const [fieldVar, value] = conditions as [{ var: string }, any];\n\n  const dependsOnField = availableFields.find((field) => field.name === fieldVar.var);\n  const dependsOnFieldName = dependsOnField ? dependsOnField.name : fieldVar.var;\n\n  const operatorText = operator === '==' ? 'is' : 'is not';\n  const valueText = String(value);\n  const actionText = operator === '==' ? 'Show' : 'Hide';\n\n  return `If ${dependsOnFieldName} ${operatorText} ${valueText}, then ${actionText} ${attributeName}`;\n};\n\nexport const getAvailableConditionFields = (\n  attributes: AnyAttribute[],\n  currentFieldName: string\n) => {\n  return attributes\n    .filter((attr) => {\n      // Only include boolean and enum fields\n      const isCorrectType = attr.type === 'boolean' || attr.type === 'enumeration';\n      // Exclude the current field to prevent self-referential conditions\n      const isNotCurrentField = attr.name !== currentFieldName;\n      return isCorrectType && isNotCurrentField;\n    })\n    .map((attr) => ({\n      name: attr.name,\n      type: attr.type,\n      enum: attr.type === 'enumeration' ? attr.enum : undefined,\n    }));\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEO,IAAMA,UAAU,CAACC,OAAe,GAAGC,QAAAA,IAAYD,EAAG;;;;;;;AC4IlD,IAAME,yBAAqBC,4BAAyC;;;AC1I9DC,IAAAA,iBAAiB,UAAMC,0BAAWC,kBAAoB;;;;;;;AC8BtDC,IAAAA,6BAAmCC,oBAAa;;;AC9BhDC,IAAAA,yBAAyB,UAAMC,0BAAWC,0BAA4B;;;;ACFtEC,IAAAA,SAAS,CAAC,EAAEC,OAAM,MAAsB;AACnD,UAAQA,QAAAA;IACN,KAAK;AACH,aAAO;IACT,KAAK;AACH,iBACEC,wBAACC,YAAAA;QAAWC,YAAW;QAAWC,WAAU;QAAiB,UAAA;;IAIjE,KAAK;AACH,iBACEH,wBAACC,YAAAA;QAAWC,YAAW;QAAWC,WAAU;QAAY,UAAA;;IAI5D,KAAK;AACH,iBACEH,wBAACC,YAAAA;QAAWC,YAAW;QAAWC,WAAU;QAAa,UAAA;;EAI/D;AACF;AAEaC,IAAAA,cAAc,CAAC,EAAEL,OAAM,MAAsB;AACxD,UAAQA,QAAAA;IACN,KAAK;AACH,iBACEC,wBAACK,OAAAA;QACCH,YAAW;QACXC,WAAU;QACVG,iBAAgB;QAChBC,aAAY;QACb,UAAA;;IAIL,KAAK;AACH,iBACEP,wBAACK,OAAAA;QACCH,YAAW;QACXC,WAAU;QACVG,iBAAgB;QAChBC,aAAY;QACb,UAAA;;IAIL,KAAK;AACH,iBACEP,wBAACK,OAAAA;QACCH,YAAW;QACXC,WAAU;QACVG,iBAAgB;QAChBC,aAAY;QACb,UAAA;;IAIL,KAAK;IACL;AACE,iBACEP,wBAACK,OAAAA;QACCG,OAAO;UACLC,YAAY;QACd;QACAP,YAAW;QACXC,WAAU;QACVG,iBAAgB;QAChBC,aAAY;QACb,UAAA;;EAIP;AACF;;;;ACrDA,IAAMG,cAAsE;EAC1EC,YAAYC;EACZC,QAAQC;EACRC,SAASC;EACTC,gBAAgBC;EAChBC,WAAWC;EACXC,aAAaH;EACbI,MAAMC;EACNC,UAAUD;EACVE,SAASb;EACTc,aAAaC;EACbC,OAAOC;EACPC,MAAMC;EACNC,aAAaD;EACbE,MAAMC;EACNC,OAAOD;EACPE,OAAOxB;EACPyB,SAASzB;EACT0B,MAAMC;EACNC,MAAMD;EACNE,OAAOP;EACPQ,QAAQ9B;EACR+B,UAAUC;EACVC,UAAUC;EACVC,UAAUC;EACVC,YAAYC;EACZC,QAAQC;EACRC,MAAMD;EACNE,MAAM/B;EACNgC,WAAWhC;EACXiC,KAAKC;AACP;AAEA,IAAMC,UAAUC,GAAOC,GAAAA;;;;;;AAchB,IAAMC,gBAAgB,CAAC,EAAEC,MAAMC,cAAc,MAAM,GAAGC,KAA0B,MAAA;AACrF,QAAMC,iBAAiBC,aAAa,iBAAiB,CAACC,UAAUA,MAAMC,aAAaC,GAAG;AAEtF,MAAIC,QAAa5D,YAAYoD,IAAK;AAElC,MAAIC,aAAa;AACf,UAAMQ,oBAAoBN,eAAeF,WAAAA;AACzC,UAAMS,OAAOD,uDAAmBC;AAChC,QAAIA,MAAM;AACRF,cAAQE;IACV;EACF;AAEA,MAAI,CAAC9D,YAAYoD,IAAAA,GAAO;AACtB,WAAO;EACT;AAEA,aACEW,yBAACf,SAAAA;IAAQgB,OAAM;IAASC,QAAO;IAASC,QAAQ;IAAI,GAAGZ;IAAMa,eAAW;IACtE,cAAAJ,yBAACb,KAAAA;MAAIkB,KAAKR;;;AAGhB;;;ACzFA,IAAMS,kBAAwC;EAC5CC,OAAaC;EACbC,MAAYC;EACZC,SAAeC;EACfC,WAAiBC;EACjBC,WAAiBC;EACjBC,YAAkBC;EAClBC,SAAeC;EACfC,YAAkBC;EAClBC,MAAYC;EACZC,MAAYC;EACZC,MAAYC;EACZC,WAAiBC;EACjBC,OAAaC;EACbC,YAAkBC;EAClBC,UAAgBC;EAChBC,KAAWC;EACXC,MAAYC;EACZC,aAAmBC;EACnBC,aAAmBC;EACnBC,UAAgBC;EAChBC,OAAaC;EACbC,OAAaC;EACbC,OAAaC;EACbC,MAAYC;EACZC,KAAWC;EACXC,UAAgBC;EAChBC,SAAeC;EACfC,WAAiBC;EACjBC,MAAYC;EACZC,OAAaC;EACbC,KAAWC;EACXC,QAAcC;EACdC,WAAiBC;EACjBC,UAAgBC;EAChBC,SAAeC;EACfC,QAAcC;EACdC,OAAaC;EACbC,cAAoBC;EACpBC,gBAAsBC;EACtBC,SAAeC;EACfC,MAAYC;EACZC,QAAcC;EACdC,KAAWC;EACXC,SAAeC;EACfC,MAAYC;EACZC,WAAiBC;EACjBC,SAAeC;EACfC,QAAcC;EACdC,QAAcC;EACdC,MAAYC;EACZC,MAAYC;EACZC,OAAaC;EACbC,MAAYC;EACZC,WAAiBC;EACjBC,SAAeC;EACfC,WAAiBC;EACjBC,OAAaC;EACbC,OAAaC;EACbC,aAAmBC;EACnBC,QAAcC;EACdC,KAAWC;EACXC,WAAiBC;EACjBC,OAAaC;EACbC,QAAcC;EACdC,WAAiBC;EACjBC,MAAYC;EACZC,MAAYC;EACZC,OAAaC;EACbC,YAAkBC;EAClBC,WAAiBC;EACjBC,UAAgBC;EAChBC,QAAgBC;EAChBC,SAAeC;EACfC,YAAkBC;EAClBC,SAAeC;EACfC,MAAYC;EACZC,OAAaC;EACbC,WAAiBC;EACjBC,UAAgBC;EAChBC,QAAcC;EACdC,OAAavI;EACbwI,YAAkBxI;EAClByI,YAAkBC;EAClBC,QAAcC;EACdC,OAAaC;EACbC,SAAeC;EACfC,KAAWC;EACXC,QAAcC;EACdC,OAAaC;EACbC,MAAYC;EACZC,MAAYC;EACZC,UAAgBC;EAChBC,QAAcC;EACdC,UAAgBC;EAChBC,OAAaC;EACbC,SAAeC;EACfC,YAAkBC;EAClBC,QAAcC;EACdC,QAAcC;EACdC,UAAgBC;EAChBC,QAAcC;EACdC,MAAYC;EACZC,QAAcC;EACdC,QAAcC;EACdC,OAAaC;EACbC,cAAoBC;EACpBC,WAAiBC;EACjBC,OAAaC;EACbC,MAAYC;EACZC,OAAaC;EACbC,eAAqBC;EACrBC,KAAWC;EACXC,YAAkBC;EAClBC,WAAiBC;EACjBC,SAAeC;EACfC,OAAaC;EACbC,SAAiBC;EACjBC,SAAeC;EACfC,WAAiBC;EACjBC,MAAYC;EACZC,YAAkBC;EAClBC,UAAgBC;EAChBC,MAAYC;EACZC,YAAkBC;EAClBC,OAAa/J;AACf;;;AC3HO,IAAMgK,qBAAqB,CAChCC,cACAC,cAAAA;AAEA,QAAMC,gBAAgC,CAAA;AAEtCC,SAAOC,QAAQJ,YAAcK,EAAAA,QAAQ,CAAC,CAACC,gBAAgBC,WAA2B,MAAA;AAChF,QAAIA,YAAYC,YAAY;AAE1B,YAAMA,aAAaC,MAAMC,QAAQH,YAAYC,UAAU,IACnDD,YAAYC,WAAWG,OAAO,CAACC,KAA0BC,MAAWC,UAAAA;AAClEF,YAAIE,MAAMC,SAAQ,CAAA,IAAMF;AACxB,eAAOD;SACN,CAAA,CACHL,IAAAA,YAAYC;AAEhBL,aAAOC,QAAQI,UAAYH,EAAAA,QAAQ,CAAC,CAACW,UAAUH,IAAoB,MAAA;AAhBlE;AAiBC,aAAIA,UAAKI,eAALJ,mBAAiBK,SAAS;AAC5Bf,iBAAOC,QAAQS,KAAKI,WAAWC,OAAO,EAAEb,QAAQ,CAAC,CAAA,EAAGY,UAAW,MAAA;AAC7D,kBAAM,CAACE,QAAAA,IAAYF;AAEnB,gBAAIE,YAAYA,SAASC,QAAQnB,WAAW;AAC1CC,4BAAcmB,KAAK;gBACjBf;gBACAC,aAAaA,YAAYe,KAAKC;gBAC9BC,WAAWX,KAAKY,QAAQT;cAC1B,CAAA;YACF;UACF,CAAA;QACF;MACF,CAAA;IACF;EACF,CAAA;AACA,SAAOd;AACT;AAEawB,IAAAA,kBAAkB,CAC7BC,WACAC,iBACAC,kBAAAA;AAEA,MAAI,EAACF,uCAAWT,UAAS;AACvB,WAAO;EACT;AAEA,QAAM,CAAC,CAACY,UAAUb,UAAW,CAAA,IAAId,OAAOC,QAAQuB,UAAUT,OAAO;AACjE,QAAM,CAACC,UAAUY,KAAAA,IAASd;AAE1B,QAAMe,iBAAiBJ,gBAAgBK,KAAK,CAACC,UAAUA,MAAMT,SAASN,SAASC,GAAG;AAClF,QAAMe,qBAAqBH,iBAAiBA,eAAeP,OAAON,SAASC;AAE3E,QAAMgB,eAAeN,aAAa,OAAO,OAAO;AAChD,QAAMO,YAAYC,OAAOP,KAAAA;AACzB,QAAMQ,aAAaT,aAAa,OAAO,SAAS;AAEhD,SAAO,MAAMK,kBAAmB,IAAGC,YAAa,IAAGC,SAAAA,UAAmBE,UAAAA,IAAcV,aAAAA;AACtF;AAEO,IAAMW,8BAA8B,CACzChC,YACAiC,qBAAAA;AAEA,SAAOjC,WACJkC,OAAO,CAAC7B,SAAAA;AAEP,UAAM8B,gBAAgB9B,KAAK+B,SAAS,aAAa/B,KAAK+B,SAAS;AAE/D,UAAMC,oBAAoBhC,KAAKY,SAASgB;AACxC,WAAOE,iBAAiBE;EAC1B,CAAA,EACCC,IAAI,CAACjC,UAAU;IACdY,MAAMZ,KAAKY;IACXmB,MAAM/B,KAAK+B;IACXG,MAAMlC,KAAK+B,SAAS,gBAAgB/B,KAAKkC,OAAOC;IAClD;AACJ;", "names": ["getTrad", "id", "pluginId", "DataManagerContext", "createContext", "useDataManager", "useContext", "DataManagerContext", "FormModalNavigationContext", "createContext", "useFormModalNavigation", "useContext", "FormModalNavigationContext", "Status", "status", "_jsx", "Typography", "fontWeight", "textColor", "StatusBadge", "Badge", "backgroundColor", "borderColor", "style", "visibility", "iconByTypes", "biginteger", "NumberField", "blocks", "BlocksField", "boolean", "BooleanField", "collectionType", "CollectionType", "component", "ComponentField", "contentType", "date", "DateField", "datetime", "decimal", "dynamiczone", "DynamicZoneField", "email", "EmailField", "enum", "EnumerationField", "enumeration", "file", "MediaField", "files", "float", "integer", "json", "JsonField", "JSON", "media", "number", "password", "PasswordField", "relation", "RelationField", "richtext", "MarkdownField", "singleType", "SingleType", "string", "TextField", "text", "time", "timestamp", "uid", "UidField", "IconBox", "styled", "Box", "AttributeIcon", "type", "customField", "rest", "getCustomField", "useStrapiApp", "state", "customFields", "get", "Compo", "customFieldObject", "icon", "_jsx", "width", "height", "shrink", "aria-hidden", "tag", "COMPONENT_ICONS", "alien", "Alien", "apps", "GridNine", "archive", "Archive", "arrowDown", "ArrowDown", "arrowLeft", "ArrowLeft", "arrowRight", "ArrowRight", "arrowUp", "ArrowUp", "attachment", "Paperclip", "bell", "Bell", "bold", "Bold", "book", "Book", "briefcase", "Briefcase", "brush", "PaintBrush", "bulletList", "BulletList", "calendar", "Calendar", "car", "Car", "cast", "Cast", "chartBubble", "ChartBubble", "chartCircle", "ChartCircle", "chartPie", "ChartPie", "check", "Check", "clock", "Clock", "cloud", "Cloud", "code", "Code", "cog", "Cog", "collapse", "Collapse", "command", "Command", "connector", "Faders", "crop", "Crop", "crown", "Crown", "cup", "Coffee", "cursor", "<PERSON><PERSON><PERSON>", "dashboard", "SquaresFour", "database", "Database", "discuss", "Discuss", "doctor", "Stethoscope", "earth", "Earth", "emotionHappy", "EmotionHappy", "emotion<PERSON><PERSON><PERSON><PERSON>", "EmotionUnhappy", "envelop", "Mail", "exit", "SignOut", "expand", "Expand", "eye", "Eye", "feather", "<PERSON><PERSON>", "file", "File", "fileError", "FileError", "filePdf", "FilePdf", "filter", "Filter", "folder", "Folder", "gate", "<PERSON><PERSON><PERSON><PERSON>", "gift", "Gift", "globe", "Globe", "grid", "GridFour", "<PERSON><PERSON><PERSON><PERSON>", "HandHeart", "hashtag", "Hashtag", "headphone", "Headphones", "heart", "Heart", "house", "House", "information", "Information", "italic", "Italic", "key", "Key", "landscape", "Images", "layer", "ListPlus", "layout", "Layout", "lightbulb", "Lightbulb", "link", "Link", "lock", "Lock", "magic", "Magic", "manyToMany", "ManyToMany", "manyToOne", "ManyToOne", "manyWays", "ManyWays", "medium", "Medium", "message", "Message", "microphone", "Microphone", "monitor", "Monitor", "moon", "Moon", "music", "MusicNotes", "oneToMany", "OneToMany", "oneToOne", "OneToOne", "oneWay", "OneWay", "paint", "paintBrush", "paperPlane", "PaperPlane", "pencil", "Pencil", "phone", "Phone", "picture", "Image", "pin", "<PERSON>n", "pinMap", "PinMap", "plane", "Plane", "play", "Play", "plus", "Plus", "priceTag", "PriceTag", "puzzle", "PuzzleP<PERSON>ce", "question", "Question", "quote", "Quotes", "refresh", "ArrowClockwise", "restaurant", "Restaurant", "rocket", "Rocket", "rotate", "ArrowsCounterClockwise", "scissors", "Scissors", "search", "Search", "seed", "Plant", "server", "Server", "shield", "Shield", "shirt", "Shirt", "shoppingCart", "ShoppingCart", "slideshow", "Presentation<PERSON>hart", "stack", "<PERSON><PERSON>", "star", "Star", "store", "Store", "strikeThrough", "StrikeThrough", "sun", "Sun", "television", "Television", "thumbDown", "ThumbDown", "thumbUp", "ThumbUp", "train", "Train", "twitter", "X", "typhoon", "Typhoon", "underline", "Underline", "user", "User", "volumeMute", "VolumeMute", "volumeUp", "VolumeUp", "walk", "Walk", "wheelchair", "Wheelchair", "write", "checkDependentRows", "contentTypes", "fieldName", "dependentRows", "Object", "entries", "for<PERSON>ach", "contentTypeUid", "contentType", "attributes", "Array", "isArray", "reduce", "acc", "attr", "index", "toString", "attrName", "conditions", "visible", "fieldVar", "var", "push", "info", "displayName", "attribute", "name", "formatCondition", "condition", "availableFields", "attributeName", "operator", "value", "dependsOnField", "find", "field", "dependsOnFieldName", "operatorText", "valueText", "String", "actionText", "getAvailableConditionFields", "currentFieldName", "filter", "isCorrectType", "type", "isNotCurrentField", "map", "enum", "undefined"]}