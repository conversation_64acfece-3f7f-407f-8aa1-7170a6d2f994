{"version": 3, "sources": ["../../../@strapi/content-manager/admin/src/components/FieldTypeIcon.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { useStrapiApp } from '@strapi/admin/strapi-admin';\nimport { Box } from '@strapi/design-system';\nimport {\n  BlocksField,\n  BooleanField,\n  ComponentField,\n  DateField,\n  DynamicZoneField,\n  EmailField,\n  EnumerationField,\n  JsonField,\n  MediaField,\n  NumberField,\n  PasswordField,\n  RelationField,\n  TextField,\n  UidField,\n} from '@strapi/icons/symbols';\n\nimport type { Schema } from '@strapi/types';\n\nconst iconByTypes: Record<Schema.Attribute.Kind, React.ReactElement> = {\n  biginteger: <NumberField />,\n  boolean: <BooleanField />,\n  date: <DateField />,\n  datetime: <DateField />,\n  decimal: <NumberField />,\n  email: <EmailField />,\n  enumeration: <EnumerationField />,\n  float: <NumberField />,\n  integer: <NumberField />,\n  media: <MediaField />,\n  password: <PasswordField />,\n  relation: <RelationField />,\n  string: <TextField />,\n  text: <TextField />,\n  richtext: <TextField />,\n  time: <DateField />,\n  timestamp: <DateField />,\n  json: <JsonField />,\n  uid: <UidField />,\n  component: <ComponentField />,\n  dynamiczone: <DynamicZoneField />,\n  blocks: <BlocksField />,\n};\n\ninterface FieldTypeIconProps {\n  type?: keyof typeof iconByTypes;\n  customFieldUid?: string;\n}\n\nconst FieldTypeIcon = ({ type, customFieldUid }: FieldTypeIconProps) => {\n  const getCustomField = useStrapiApp('FieldTypeIcon', (state) => state.customFields.get);\n\n  if (!type) {\n    return null;\n  }\n\n  let Compo = iconByTypes[type];\n\n  if (customFieldUid) {\n    const customField = getCustomField(customFieldUid);\n    const CustomFieldIcon = customField?.icon;\n\n    if (CustomFieldIcon) {\n      Compo = (\n        <Box marginRight={3} width={7} height={6}>\n          <CustomFieldIcon />\n        </Box>\n      );\n    }\n  }\n\n  if (!iconByTypes[type]) {\n    return null;\n  }\n\n  return Compo;\n};\n\nexport { FieldTypeIcon };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBA,IAAMA,cAAiE;EACrEC,gBAAYC,wBAACC,cAAAA,CAAAA,CAAAA;EACbC,aAASF,wBAACG,eAAAA,CAAAA,CAAAA;EACVC,UAAMJ,wBAACK,cAAAA,CAAAA,CAAAA;EACPC,cAAUN,wBAACK,cAAAA,CAAAA,CAAAA;EACXE,aAASP,wBAACC,cAAAA,CAAAA,CAAAA;EACVO,WAAOR,wBAACS,cAAAA,CAAAA,CAAAA;EACRC,iBAAaV,wBAACW,cAAAA,CAAAA,CAAAA;EACdC,WAAOZ,wBAACC,cAAAA,CAAAA,CAAAA;EACRY,aAASb,wBAACC,cAAAA,CAAAA,CAAAA;EACVa,WAAOd,wBAACe,cAAAA,CAAAA,CAAAA;EACRC,cAAUhB,wBAACiB,cAAAA,CAAAA,CAAAA;EACXC,cAAUlB,wBAACmB,cAAAA,CAAAA,CAAAA;EACXC,YAAQpB,wBAACqB,cAAAA,CAAAA,CAAAA;EACTC,UAAMtB,wBAACqB,cAAAA,CAAAA,CAAAA;EACPE,cAAUvB,wBAACqB,cAAAA,CAAAA,CAAAA;EACXG,UAAMxB,wBAACK,cAAAA,CAAAA,CAAAA;EACPoB,eAAWzB,wBAACK,cAAAA,CAAAA,CAAAA;EACZqB,UAAM1B,wBAAC2B,cAAAA,CAAAA,CAAAA;EACPC,SAAK5B,wBAAC6B,cAAAA,CAAAA,CAAAA;EACNC,eAAW9B,wBAAC+B,cAAAA,CAAAA,CAAAA;EACZC,iBAAahC,wBAACiC,cAAAA,CAAAA,CAAAA;EACdC,YAAQlC,wBAACmC,eAAAA,CAAAA,CAAAA;AACX;AAOA,IAAMC,gBAAgB,CAAC,EAAEC,MAAMC,eAAc,MAAsB;AACjE,QAAMC,iBAAiBC,aAAa,iBAAiB,CAACC,UAAUA,MAAMC,aAAaC,GAAG;AAEtF,MAAI,CAACN,MAAM;AACT,WAAO;EACT;AAEA,MAAIO,QAAQ9C,YAAYuC,IAAK;AAE7B,MAAIC,gBAAgB;AAClB,UAAMO,cAAcN,eAAeD,cAAAA;AACnC,UAAMQ,kBAAkBD,2CAAaE;AAErC,QAAID,iBAAiB;AACnBF,kBACE5C,wBAACgD,KAAAA;QAAIC,aAAa;QAAGC,OAAO;QAAGC,QAAQ;QACrC,cAAAnD,wBAAC8C,iBAAAA,CAAAA,CAAAA;;IAGP;EACF;AAEA,MAAI,CAAChD,YAAYuC,IAAAA,GAAO;AACtB,WAAO;EACT;AAEA,SAAOO;AACT;", "names": ["iconByTypes", "biginteger", "_jsx", "NumberField", "boolean", "BooleanField", "date", "DateField", "datetime", "decimal", "email", "EmailField", "enumeration", "EnumerationField", "float", "integer", "media", "MediaField", "password", "PasswordField", "relation", "RelationField", "string", "TextField", "text", "richtext", "time", "timestamp", "json", "JsonField", "uid", "UidField", "component", "ComponentField", "dynamiczone", "DynamicZoneField", "blocks", "BlocksField", "FieldTypeIcon", "type", "customFieldUid", "getCustomField", "useStrapiApp", "state", "customFields", "get", "Compo", "customField", "CustomFieldIcon", "icon", "Box", "marginRight", "width", "height"]}