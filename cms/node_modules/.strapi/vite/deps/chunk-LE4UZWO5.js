import {
  useGetRolesQuery
} from "./chunk-TLFBIKP6.js";
import {
  useCollator,
  useIntl
} from "./chunk-4YYUDJZ2.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/admin/src/hooks/useAdminRoles.mjs
var React = __toESM(require_react(), 1);
var useAdminRoles = (params = {}, queryOptions) => {
  const { locale } = useIntl();
  const formatter = useCollator(locale, {
    sensitivity: "base"
  });
  const { data, error, isError, isLoading, refetch } = useGetRolesQuery(params, queryOptions);
  const roles = React.useMemo(() => [
    ...data ?? []
  ].sort((a, b) => formatter.compare(a.name, b.name)), [
    data,
    formatter
  ]);
  return {
    roles,
    error,
    isError,
    isLoading,
    refetch
  };
};

export {
  useAdminRoles
};
//# sourceMappingURL=chunk-LE4UZWO5.js.map
