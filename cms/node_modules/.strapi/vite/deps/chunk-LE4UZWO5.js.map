{"version": 3, "sources": ["../../../@strapi/admin/admin/src/hooks/useAdminRoles.ts"], "sourcesContent": ["import * as React from 'react';\n\nimport { useCollator } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\n\nimport { GetRolesParams, useGetRolesQuery } from '../services/users';\n\nimport type { FindRoles } from '../../../shared/contracts/roles';\n\nexport type AdminRole = FindRoles.Response['data'][number];\n\nexport const useAdminRoles = (\n  params: GetRolesParams = {},\n  queryOptions?: Parameters<typeof useGetRolesQuery>[1]\n) => {\n  const { locale } = useIntl();\n  const formatter = useCollator(locale, {\n    sensitivity: 'base',\n  });\n\n  const { data, error, isError, isLoading, refetch } = useGetRolesQuery(params, queryOptions);\n\n  // the return value needs to be memoized, because intantiating\n  // an empty array as default value would lead to an unstable return\n  // value, which later on triggers infinite loops if used in the\n  // dependency arrays of other hooks\n  const roles = React.useMemo(\n    () =>\n      [...(data ?? [])].sort((a, b) =>\n        formatter.compare(a.name, b.name)\n      ) as FindRoles.Response['data'],\n    [data, formatter]\n  );\n\n  return {\n    roles,\n    error,\n    isError,\n    isLoading,\n    refetch,\n  };\n};\n"], "mappings": ";;;;;;;;;;;;;;;;IAWaA,gBAAgB,CAC3BC,SAAyB,CAAA,GACzBC,iBAAAA;AAEA,QAAM,EAAEC,OAAM,IAAKC,QAAAA;AACnB,QAAMC,YAAYC,YAAYH,QAAQ;IACpCI,aAAa;EACf,CAAA;AAEA,QAAM,EAAEC,MAAMC,OAAOC,SAASC,WAAWC,QAAO,IAAKC,iBAAiBZ,QAAQC,YAAAA;AAM9E,QAAMY,QAAcC,cAClB,MACE;IAAKP,GAAAA,QAAQ,CAAA;EAAI,EAACQ,KAAK,CAACC,GAAGC,MACzBb,UAAUc,QAAQF,EAAEG,MAAMF,EAAEE,IAAI,CAEpC,GAAA;IAACZ;IAAMH;EAAU,CAAA;AAGnB,SAAO;IACLS;IACAL;IACAC;IACAC;IACAC;EACF;AACF;", "names": ["useAdminRoles", "params", "queryOptions", "locale", "useIntl", "formatter", "useCollator", "sensitivity", "data", "error", "isError", "isLoading", "refetch", "useGetRolesQuery", "roles", "useMemo", "sort", "a", "b", "compare", "name"]}