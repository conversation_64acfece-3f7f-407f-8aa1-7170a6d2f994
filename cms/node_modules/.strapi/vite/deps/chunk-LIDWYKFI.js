import {
  content<PERSON>anager<PERSON><PERSON>
} from "./chunk-CNQJ6H2W.js";
import {
  createRulesEngine
} from "./chunk-DDRVMFUU.js";
import {
  errorsTrads
} from "./chunk-IFOFBKTA.js";
import {
  ValidationError,
  create2 as create,
  create3 as create2,
  create4 as create3,
  create5 as create4,
  create6 as create5,
  create7 as create6,
  create8 as create7
} from "./chunk-4WX3TY7V.js";
import {
  generateNKeysBetween,
  getYupValidationErrors,
  useForm
} from "./chunk-VWIQ6S3Y.js";
import {
  useAPIErrorHandler
} from "./chunk-3UVBSCPH.js";
import {
  useQueryParams,
  useStrapiApp
} from "./chunk-JXTOCMQW.js";
import {
  require_pipe,
  require_side_channel
} from "./chunk-XU64YSEO.js";
import {
  require_clone,
  require_toPath
} from "./chunk-CE4VABH2.js";
import {
  useNotification
} from "./chunk-4EY5FDQG.js";
import {
  useIntl
} from "./chunk-4YYUDJZ2.js";
import {
  useParams
} from "./chunk-S65ZWNEO.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __commonJS,
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/content-manager/node_modules/qs/lib/formats.js
var require_formats = __commonJS({
  "node_modules/@strapi/content-manager/node_modules/qs/lib/formats.js"(exports, module) {
    "use strict";
    var replace = String.prototype.replace;
    var percentTwenties = /%20/g;
    var Format = {
      RFC1738: "RFC1738",
      RFC3986: "RFC3986"
    };
    module.exports = {
      "default": Format.RFC3986,
      formatters: {
        RFC1738: function(value) {
          return replace.call(value, percentTwenties, "+");
        },
        RFC3986: function(value) {
          return String(value);
        }
      },
      RFC1738: Format.RFC1738,
      RFC3986: Format.RFC3986
    };
  }
});

// node_modules/@strapi/content-manager/node_modules/qs/lib/utils.js
var require_utils = __commonJS({
  "node_modules/@strapi/content-manager/node_modules/qs/lib/utils.js"(exports, module) {
    "use strict";
    var formats = require_formats();
    var has = Object.prototype.hasOwnProperty;
    var isArray = Array.isArray;
    var hexTable = function() {
      var array = [];
      for (var i = 0; i < 256; ++i) {
        array.push("%" + ((i < 16 ? "0" : "") + i.toString(16)).toUpperCase());
      }
      return array;
    }();
    var compactQueue = function compactQueue2(queue) {
      while (queue.length > 1) {
        var item = queue.pop();
        var obj = item.obj[item.prop];
        if (isArray(obj)) {
          var compacted = [];
          for (var j = 0; j < obj.length; ++j) {
            if (typeof obj[j] !== "undefined") {
              compacted.push(obj[j]);
            }
          }
          item.obj[item.prop] = compacted;
        }
      }
    };
    var arrayToObject = function arrayToObject2(source, options) {
      var obj = options && options.plainObjects ? /* @__PURE__ */ Object.create(null) : {};
      for (var i = 0; i < source.length; ++i) {
        if (typeof source[i] !== "undefined") {
          obj[i] = source[i];
        }
      }
      return obj;
    };
    var merge = function merge2(target, source, options) {
      if (!source) {
        return target;
      }
      if (typeof source !== "object") {
        if (isArray(target)) {
          target.push(source);
        } else if (target && typeof target === "object") {
          if (options && (options.plainObjects || options.allowPrototypes) || !has.call(Object.prototype, source)) {
            target[source] = true;
          }
        } else {
          return [target, source];
        }
        return target;
      }
      if (!target || typeof target !== "object") {
        return [target].concat(source);
      }
      var mergeTarget = target;
      if (isArray(target) && !isArray(source)) {
        mergeTarget = arrayToObject(target, options);
      }
      if (isArray(target) && isArray(source)) {
        source.forEach(function(item, i) {
          if (has.call(target, i)) {
            var targetItem = target[i];
            if (targetItem && typeof targetItem === "object" && item && typeof item === "object") {
              target[i] = merge2(targetItem, item, options);
            } else {
              target.push(item);
            }
          } else {
            target[i] = item;
          }
        });
        return target;
      }
      return Object.keys(source).reduce(function(acc, key) {
        var value = source[key];
        if (has.call(acc, key)) {
          acc[key] = merge2(acc[key], value, options);
        } else {
          acc[key] = value;
        }
        return acc;
      }, mergeTarget);
    };
    var assign = function assignSingleSource(target, source) {
      return Object.keys(source).reduce(function(acc, key) {
        acc[key] = source[key];
        return acc;
      }, target);
    };
    var decode = function(str, decoder, charset) {
      var strWithoutPlus = str.replace(/\+/g, " ");
      if (charset === "iso-8859-1") {
        return strWithoutPlus.replace(/%[0-9a-f]{2}/gi, unescape);
      }
      try {
        return decodeURIComponent(strWithoutPlus);
      } catch (e) {
        return strWithoutPlus;
      }
    };
    var encode = function encode2(str, defaultEncoder, charset, kind, format) {
      if (str.length === 0) {
        return str;
      }
      var string = str;
      if (typeof str === "symbol") {
        string = Symbol.prototype.toString.call(str);
      } else if (typeof str !== "string") {
        string = String(str);
      }
      if (charset === "iso-8859-1") {
        return escape(string).replace(/%u[0-9a-f]{4}/gi, function($0) {
          return "%26%23" + parseInt($0.slice(2), 16) + "%3B";
        });
      }
      var out = "";
      for (var i = 0; i < string.length; ++i) {
        var c = string.charCodeAt(i);
        if (c === 45 || c === 46 || c === 95 || c === 126 || c >= 48 && c <= 57 || c >= 65 && c <= 90 || c >= 97 && c <= 122 || format === formats.RFC1738 && (c === 40 || c === 41)) {
          out += string.charAt(i);
          continue;
        }
        if (c < 128) {
          out = out + hexTable[c];
          continue;
        }
        if (c < 2048) {
          out = out + (hexTable[192 | c >> 6] + hexTable[128 | c & 63]);
          continue;
        }
        if (c < 55296 || c >= 57344) {
          out = out + (hexTable[224 | c >> 12] + hexTable[128 | c >> 6 & 63] + hexTable[128 | c & 63]);
          continue;
        }
        i += 1;
        c = 65536 + ((c & 1023) << 10 | string.charCodeAt(i) & 1023);
        out += hexTable[240 | c >> 18] + hexTable[128 | c >> 12 & 63] + hexTable[128 | c >> 6 & 63] + hexTable[128 | c & 63];
      }
      return out;
    };
    var compact = function compact2(value) {
      var queue = [{ obj: { o: value }, prop: "o" }];
      var refs = [];
      for (var i = 0; i < queue.length; ++i) {
        var item = queue[i];
        var obj = item.obj[item.prop];
        var keys = Object.keys(obj);
        for (var j = 0; j < keys.length; ++j) {
          var key = keys[j];
          var val = obj[key];
          if (typeof val === "object" && val !== null && refs.indexOf(val) === -1) {
            queue.push({ obj, prop: key });
            refs.push(val);
          }
        }
      }
      compactQueue(queue);
      return value;
    };
    var isRegExp = function isRegExp2(obj) {
      return Object.prototype.toString.call(obj) === "[object RegExp]";
    };
    var isBuffer = function isBuffer2(obj) {
      if (!obj || typeof obj !== "object") {
        return false;
      }
      return !!(obj.constructor && obj.constructor.isBuffer && obj.constructor.isBuffer(obj));
    };
    var combine = function combine2(a, b) {
      return [].concat(a, b);
    };
    var maybeMap = function maybeMap2(val, fn) {
      if (isArray(val)) {
        var mapped = [];
        for (var i = 0; i < val.length; i += 1) {
          mapped.push(fn(val[i]));
        }
        return mapped;
      }
      return fn(val);
    };
    module.exports = {
      arrayToObject,
      assign,
      combine,
      compact,
      decode,
      encode,
      isBuffer,
      isRegExp,
      maybeMap,
      merge
    };
  }
});

// node_modules/@strapi/content-manager/node_modules/qs/lib/stringify.js
var require_stringify = __commonJS({
  "node_modules/@strapi/content-manager/node_modules/qs/lib/stringify.js"(exports, module) {
    "use strict";
    var getSideChannel = require_side_channel();
    var utils = require_utils();
    var formats = require_formats();
    var has = Object.prototype.hasOwnProperty;
    var arrayPrefixGenerators = {
      brackets: function brackets(prefix) {
        return prefix + "[]";
      },
      comma: "comma",
      indices: function indices(prefix, key) {
        return prefix + "[" + key + "]";
      },
      repeat: function repeat(prefix) {
        return prefix;
      }
    };
    var isArray = Array.isArray;
    var push = Array.prototype.push;
    var pushToArray = function(arr, valueOrArray) {
      push.apply(arr, isArray(valueOrArray) ? valueOrArray : [valueOrArray]);
    };
    var toISO = Date.prototype.toISOString;
    var defaultFormat = formats["default"];
    var defaults = {
      addQueryPrefix: false,
      allowDots: false,
      charset: "utf-8",
      charsetSentinel: false,
      delimiter: "&",
      encode: true,
      encoder: utils.encode,
      encodeValuesOnly: false,
      format: defaultFormat,
      formatter: formats.formatters[defaultFormat],
      // deprecated
      indices: false,
      serializeDate: function serializeDate(date) {
        return toISO.call(date);
      },
      skipNulls: false,
      strictNullHandling: false
    };
    var isNonNullishPrimitive = function isNonNullishPrimitive2(v) {
      return typeof v === "string" || typeof v === "number" || typeof v === "boolean" || typeof v === "symbol" || typeof v === "bigint";
    };
    var sentinel = {};
    var stringify2 = function stringify3(object, prefix, generateArrayPrefix, commaRoundTrip, strictNullHandling, skipNulls, encoder, filter, sort, allowDots, serializeDate, format, formatter, encodeValuesOnly, charset, sideChannel) {
      var obj = object;
      var tmpSc = sideChannel;
      var step = 0;
      var findFlag = false;
      while ((tmpSc = tmpSc.get(sentinel)) !== void 0 && !findFlag) {
        var pos = tmpSc.get(object);
        step += 1;
        if (typeof pos !== "undefined") {
          if (pos === step) {
            throw new RangeError("Cyclic object value");
          } else {
            findFlag = true;
          }
        }
        if (typeof tmpSc.get(sentinel) === "undefined") {
          step = 0;
        }
      }
      if (typeof filter === "function") {
        obj = filter(prefix, obj);
      } else if (obj instanceof Date) {
        obj = serializeDate(obj);
      } else if (generateArrayPrefix === "comma" && isArray(obj)) {
        obj = utils.maybeMap(obj, function(value2) {
          if (value2 instanceof Date) {
            return serializeDate(value2);
          }
          return value2;
        });
      }
      if (obj === null) {
        if (strictNullHandling) {
          return encoder && !encodeValuesOnly ? encoder(prefix, defaults.encoder, charset, "key", format) : prefix;
        }
        obj = "";
      }
      if (isNonNullishPrimitive(obj) || utils.isBuffer(obj)) {
        if (encoder) {
          var keyValue = encodeValuesOnly ? prefix : encoder(prefix, defaults.encoder, charset, "key", format);
          return [formatter(keyValue) + "=" + formatter(encoder(obj, defaults.encoder, charset, "value", format))];
        }
        return [formatter(prefix) + "=" + formatter(String(obj))];
      }
      var values = [];
      if (typeof obj === "undefined") {
        return values;
      }
      var objKeys;
      if (generateArrayPrefix === "comma" && isArray(obj)) {
        if (encodeValuesOnly && encoder) {
          obj = utils.maybeMap(obj, encoder);
        }
        objKeys = [{ value: obj.length > 0 ? obj.join(",") || null : void 0 }];
      } else if (isArray(filter)) {
        objKeys = filter;
      } else {
        var keys = Object.keys(obj);
        objKeys = sort ? keys.sort(sort) : keys;
      }
      var adjustedPrefix = commaRoundTrip && isArray(obj) && obj.length === 1 ? prefix + "[]" : prefix;
      for (var j = 0; j < objKeys.length; ++j) {
        var key = objKeys[j];
        var value = typeof key === "object" && typeof key.value !== "undefined" ? key.value : obj[key];
        if (skipNulls && value === null) {
          continue;
        }
        var keyPrefix = isArray(obj) ? typeof generateArrayPrefix === "function" ? generateArrayPrefix(adjustedPrefix, key) : adjustedPrefix : adjustedPrefix + (allowDots ? "." + key : "[" + key + "]");
        sideChannel.set(object, step);
        var valueSideChannel = getSideChannel();
        valueSideChannel.set(sentinel, sideChannel);
        pushToArray(values, stringify3(
          value,
          keyPrefix,
          generateArrayPrefix,
          commaRoundTrip,
          strictNullHandling,
          skipNulls,
          generateArrayPrefix === "comma" && encodeValuesOnly && isArray(obj) ? null : encoder,
          filter,
          sort,
          allowDots,
          serializeDate,
          format,
          formatter,
          encodeValuesOnly,
          charset,
          valueSideChannel
        ));
      }
      return values;
    };
    var normalizeStringifyOptions = function normalizeStringifyOptions2(opts) {
      if (!opts) {
        return defaults;
      }
      if (opts.encoder !== null && typeof opts.encoder !== "undefined" && typeof opts.encoder !== "function") {
        throw new TypeError("Encoder has to be a function.");
      }
      var charset = opts.charset || defaults.charset;
      if (typeof opts.charset !== "undefined" && opts.charset !== "utf-8" && opts.charset !== "iso-8859-1") {
        throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");
      }
      var format = formats["default"];
      if (typeof opts.format !== "undefined") {
        if (!has.call(formats.formatters, opts.format)) {
          throw new TypeError("Unknown format option provided.");
        }
        format = opts.format;
      }
      var formatter = formats.formatters[format];
      var filter = defaults.filter;
      if (typeof opts.filter === "function" || isArray(opts.filter)) {
        filter = opts.filter;
      }
      return {
        addQueryPrefix: typeof opts.addQueryPrefix === "boolean" ? opts.addQueryPrefix : defaults.addQueryPrefix,
        allowDots: typeof opts.allowDots === "undefined" ? defaults.allowDots : !!opts.allowDots,
        charset,
        charsetSentinel: typeof opts.charsetSentinel === "boolean" ? opts.charsetSentinel : defaults.charsetSentinel,
        delimiter: typeof opts.delimiter === "undefined" ? defaults.delimiter : opts.delimiter,
        encode: typeof opts.encode === "boolean" ? opts.encode : defaults.encode,
        encoder: typeof opts.encoder === "function" ? opts.encoder : defaults.encoder,
        encodeValuesOnly: typeof opts.encodeValuesOnly === "boolean" ? opts.encodeValuesOnly : defaults.encodeValuesOnly,
        filter,
        format,
        formatter,
        serializeDate: typeof opts.serializeDate === "function" ? opts.serializeDate : defaults.serializeDate,
        skipNulls: typeof opts.skipNulls === "boolean" ? opts.skipNulls : defaults.skipNulls,
        sort: typeof opts.sort === "function" ? opts.sort : null,
        strictNullHandling: typeof opts.strictNullHandling === "boolean" ? opts.strictNullHandling : defaults.strictNullHandling
      };
    };
    module.exports = function(object, opts) {
      var obj = object;
      var options = normalizeStringifyOptions(opts);
      var objKeys;
      var filter;
      if (typeof options.filter === "function") {
        filter = options.filter;
        obj = filter("", obj);
      } else if (isArray(options.filter)) {
        filter = options.filter;
        objKeys = filter;
      }
      var keys = [];
      if (typeof obj !== "object" || obj === null) {
        return "";
      }
      var arrayFormat;
      if (opts && opts.arrayFormat in arrayPrefixGenerators) {
        arrayFormat = opts.arrayFormat;
      } else if (opts && "indices" in opts) {
        arrayFormat = opts.indices ? "indices" : "repeat";
      } else {
        arrayFormat = "indices";
      }
      var generateArrayPrefix = arrayPrefixGenerators[arrayFormat];
      if (opts && "commaRoundTrip" in opts && typeof opts.commaRoundTrip !== "boolean") {
        throw new TypeError("`commaRoundTrip` must be a boolean, or absent");
      }
      var commaRoundTrip = generateArrayPrefix === "comma" && opts && opts.commaRoundTrip;
      if (!objKeys) {
        objKeys = Object.keys(obj);
      }
      if (options.sort) {
        objKeys.sort(options.sort);
      }
      var sideChannel = getSideChannel();
      for (var i = 0; i < objKeys.length; ++i) {
        var key = objKeys[i];
        if (options.skipNulls && obj[key] === null) {
          continue;
        }
        pushToArray(keys, stringify2(
          obj[key],
          key,
          generateArrayPrefix,
          commaRoundTrip,
          options.strictNullHandling,
          options.skipNulls,
          options.encode ? options.encoder : null,
          options.filter,
          options.sort,
          options.allowDots,
          options.serializeDate,
          options.format,
          options.formatter,
          options.encodeValuesOnly,
          options.charset,
          sideChannel
        ));
      }
      var joined = keys.join(options.delimiter);
      var prefix = options.addQueryPrefix === true ? "?" : "";
      if (options.charsetSentinel) {
        if (options.charset === "iso-8859-1") {
          prefix += "utf8=%26%2310003%3B&";
        } else {
          prefix += "utf8=%E2%9C%93&";
        }
      }
      return joined.length > 0 ? prefix + joined : "";
    };
  }
});

// node_modules/@strapi/content-manager/node_modules/qs/lib/parse.js
var require_parse = __commonJS({
  "node_modules/@strapi/content-manager/node_modules/qs/lib/parse.js"(exports, module) {
    "use strict";
    var utils = require_utils();
    var has = Object.prototype.hasOwnProperty;
    var isArray = Array.isArray;
    var defaults = {
      allowDots: false,
      allowPrototypes: false,
      allowSparse: false,
      arrayLimit: 20,
      charset: "utf-8",
      charsetSentinel: false,
      comma: false,
      decoder: utils.decode,
      delimiter: "&",
      depth: 5,
      ignoreQueryPrefix: false,
      interpretNumericEntities: false,
      parameterLimit: 1e3,
      parseArrays: true,
      plainObjects: false,
      strictNullHandling: false
    };
    var interpretNumericEntities = function(str) {
      return str.replace(/&#(\d+);/g, function($0, numberStr) {
        return String.fromCharCode(parseInt(numberStr, 10));
      });
    };
    var parseArrayValue = function(val, options) {
      if (val && typeof val === "string" && options.comma && val.indexOf(",") > -1) {
        return val.split(",");
      }
      return val;
    };
    var isoSentinel = "utf8=%26%2310003%3B";
    var charsetSentinel = "utf8=%E2%9C%93";
    var parseValues = function parseQueryStringValues(str, options) {
      var obj = {};
      var cleanStr = options.ignoreQueryPrefix ? str.replace(/^\?/, "") : str;
      var limit = options.parameterLimit === Infinity ? void 0 : options.parameterLimit;
      var parts = cleanStr.split(options.delimiter, limit);
      var skipIndex = -1;
      var i;
      var charset = options.charset;
      if (options.charsetSentinel) {
        for (i = 0; i < parts.length; ++i) {
          if (parts[i].indexOf("utf8=") === 0) {
            if (parts[i] === charsetSentinel) {
              charset = "utf-8";
            } else if (parts[i] === isoSentinel) {
              charset = "iso-8859-1";
            }
            skipIndex = i;
            i = parts.length;
          }
        }
      }
      for (i = 0; i < parts.length; ++i) {
        if (i === skipIndex) {
          continue;
        }
        var part = parts[i];
        var bracketEqualsPos = part.indexOf("]=");
        var pos = bracketEqualsPos === -1 ? part.indexOf("=") : bracketEqualsPos + 1;
        var key, val;
        if (pos === -1) {
          key = options.decoder(part, defaults.decoder, charset, "key");
          val = options.strictNullHandling ? null : "";
        } else {
          key = options.decoder(part.slice(0, pos), defaults.decoder, charset, "key");
          val = utils.maybeMap(
            parseArrayValue(part.slice(pos + 1), options),
            function(encodedVal) {
              return options.decoder(encodedVal, defaults.decoder, charset, "value");
            }
          );
        }
        if (val && options.interpretNumericEntities && charset === "iso-8859-1") {
          val = interpretNumericEntities(val);
        }
        if (part.indexOf("[]=") > -1) {
          val = isArray(val) ? [val] : val;
        }
        if (has.call(obj, key)) {
          obj[key] = utils.combine(obj[key], val);
        } else {
          obj[key] = val;
        }
      }
      return obj;
    };
    var parseObject = function(chain, val, options, valuesParsed) {
      var leaf = valuesParsed ? val : parseArrayValue(val, options);
      for (var i = chain.length - 1; i >= 0; --i) {
        var obj;
        var root = chain[i];
        if (root === "[]" && options.parseArrays) {
          obj = [].concat(leaf);
        } else {
          obj = options.plainObjects ? /* @__PURE__ */ Object.create(null) : {};
          var cleanRoot = root.charAt(0) === "[" && root.charAt(root.length - 1) === "]" ? root.slice(1, -1) : root;
          var index = parseInt(cleanRoot, 10);
          if (!options.parseArrays && cleanRoot === "") {
            obj = { 0: leaf };
          } else if (!isNaN(index) && root !== cleanRoot && String(index) === cleanRoot && index >= 0 && (options.parseArrays && index <= options.arrayLimit)) {
            obj = [];
            obj[index] = leaf;
          } else if (cleanRoot !== "__proto__") {
            obj[cleanRoot] = leaf;
          }
        }
        leaf = obj;
      }
      return leaf;
    };
    var parseKeys = function parseQueryStringKeys(givenKey, val, options, valuesParsed) {
      if (!givenKey) {
        return;
      }
      var key = options.allowDots ? givenKey.replace(/\.([^.[]+)/g, "[$1]") : givenKey;
      var brackets = /(\[[^[\]]*])/;
      var child = /(\[[^[\]]*])/g;
      var segment = options.depth > 0 && brackets.exec(key);
      var parent = segment ? key.slice(0, segment.index) : key;
      var keys = [];
      if (parent) {
        if (!options.plainObjects && has.call(Object.prototype, parent)) {
          if (!options.allowPrototypes) {
            return;
          }
        }
        keys.push(parent);
      }
      var i = 0;
      while (options.depth > 0 && (segment = child.exec(key)) !== null && i < options.depth) {
        i += 1;
        if (!options.plainObjects && has.call(Object.prototype, segment[1].slice(1, -1))) {
          if (!options.allowPrototypes) {
            return;
          }
        }
        keys.push(segment[1]);
      }
      if (segment) {
        keys.push("[" + key.slice(segment.index) + "]");
      }
      return parseObject(keys, val, options, valuesParsed);
    };
    var normalizeParseOptions = function normalizeParseOptions2(opts) {
      if (!opts) {
        return defaults;
      }
      if (opts.decoder !== null && opts.decoder !== void 0 && typeof opts.decoder !== "function") {
        throw new TypeError("Decoder has to be a function.");
      }
      if (typeof opts.charset !== "undefined" && opts.charset !== "utf-8" && opts.charset !== "iso-8859-1") {
        throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");
      }
      var charset = typeof opts.charset === "undefined" ? defaults.charset : opts.charset;
      return {
        allowDots: typeof opts.allowDots === "undefined" ? defaults.allowDots : !!opts.allowDots,
        allowPrototypes: typeof opts.allowPrototypes === "boolean" ? opts.allowPrototypes : defaults.allowPrototypes,
        allowSparse: typeof opts.allowSparse === "boolean" ? opts.allowSparse : defaults.allowSparse,
        arrayLimit: typeof opts.arrayLimit === "number" ? opts.arrayLimit : defaults.arrayLimit,
        charset,
        charsetSentinel: typeof opts.charsetSentinel === "boolean" ? opts.charsetSentinel : defaults.charsetSentinel,
        comma: typeof opts.comma === "boolean" ? opts.comma : defaults.comma,
        decoder: typeof opts.decoder === "function" ? opts.decoder : defaults.decoder,
        delimiter: typeof opts.delimiter === "string" || utils.isRegExp(opts.delimiter) ? opts.delimiter : defaults.delimiter,
        // eslint-disable-next-line no-implicit-coercion, no-extra-parens
        depth: typeof opts.depth === "number" || opts.depth === false ? +opts.depth : defaults.depth,
        ignoreQueryPrefix: opts.ignoreQueryPrefix === true,
        interpretNumericEntities: typeof opts.interpretNumericEntities === "boolean" ? opts.interpretNumericEntities : defaults.interpretNumericEntities,
        parameterLimit: typeof opts.parameterLimit === "number" ? opts.parameterLimit : defaults.parameterLimit,
        parseArrays: opts.parseArrays !== false,
        plainObjects: typeof opts.plainObjects === "boolean" ? opts.plainObjects : defaults.plainObjects,
        strictNullHandling: typeof opts.strictNullHandling === "boolean" ? opts.strictNullHandling : defaults.strictNullHandling
      };
    };
    module.exports = function(str, opts) {
      var options = normalizeParseOptions(opts);
      if (str === "" || str === null || typeof str === "undefined") {
        return options.plainObjects ? /* @__PURE__ */ Object.create(null) : {};
      }
      var tempObj = typeof str === "string" ? parseValues(str, options) : str;
      var obj = options.plainObjects ? /* @__PURE__ */ Object.create(null) : {};
      var keys = Object.keys(tempObj);
      for (var i = 0; i < keys.length; ++i) {
        var key = keys[i];
        var newObj = parseKeys(key, tempObj[key], options, typeof str === "string");
        obj = utils.merge(obj, newObj, options);
      }
      if (options.allowSparse === true) {
        return obj;
      }
      return utils.compact(obj);
    };
  }
});

// node_modules/@strapi/content-manager/node_modules/qs/lib/index.js
var require_lib = __commonJS({
  "node_modules/@strapi/content-manager/node_modules/qs/lib/index.js"(exports, module) {
    "use strict";
    var stringify2 = require_stringify();
    var parse = require_parse();
    var formats = require_formats();
    module.exports = {
      formats,
      parse,
      stringify: stringify2
    };
  }
});

// node_modules/@strapi/content-manager/dist/admin/constants/collections.mjs
var SINGLE_TYPES = "single-types";
var COLLECTION_TYPES = "collection-types";

// node_modules/@strapi/content-manager/dist/admin/pages/EditView/utils/data.mjs
var import_pipe = __toESM(require_pipe(), 1);

// node_modules/@strapi/content-manager/dist/admin/constants/attributes.mjs
var ID = "id";
var CREATED_BY_ATTRIBUTE_NAME = "createdBy";
var UPDATED_BY_ATTRIBUTE_NAME = "updatedBy";
var CREATOR_FIELDS = [
  CREATED_BY_ATTRIBUTE_NAME,
  UPDATED_BY_ATTRIBUTE_NAME
];
var PUBLISHED_BY_ATTRIBUTE_NAME = "publishedBy";
var CREATED_AT_ATTRIBUTE_NAME = "createdAt";
var UPDATED_AT_ATTRIBUTE_NAME = "updatedAt";
var PUBLISHED_AT_ATTRIBUTE_NAME = "publishedAt";
var DOCUMENT_META_FIELDS = [
  ID,
  ...CREATOR_FIELDS,
  PUBLISHED_BY_ATTRIBUTE_NAME,
  CREATED_AT_ATTRIBUTE_NAME,
  UPDATED_AT_ATTRIBUTE_NAME,
  PUBLISHED_AT_ATTRIBUTE_NAME
];
var ATTRIBUTE_TYPES_THAT_CANNOT_BE_MAIN_FIELD = [
  "dynamiczone",
  "json",
  "text",
  "relation",
  "component",
  "boolean",
  "media",
  "password",
  "richtext",
  "timestamp",
  "blocks"
];

// node_modules/@strapi/content-manager/dist/admin/pages/EditView/utils/data.mjs
var BLOCK_LIST_ATTRIBUTE_KEYS = [
  "__component",
  "__temp_key__"
];
var traverseData = (predicate, transform) => (schema, components = {}) => (data = {}) => {
  const traverse = (datum, attributes) => {
    return Object.entries(datum).reduce((acc, [key, value]) => {
      var _a;
      const attribute = attributes[key];
      if (BLOCK_LIST_ATTRIBUTE_KEYS.includes(key) || value === null || value === void 0) {
        acc[key] = value;
        return acc;
      }
      if (attribute.type === "component") {
        if (attribute.repeatable) {
          const componentValue = predicate(attribute, value) ? transform(value, attribute) : value;
          acc[key] = componentValue.map((componentData) => {
            var _a2;
            return traverse(componentData, ((_a2 = components[attribute.component]) == null ? void 0 : _a2.attributes) ?? {});
          });
        } else {
          const componentValue = predicate(attribute, value) ? transform(value, attribute) : value;
          acc[key] = traverse(componentValue, ((_a = components[attribute.component]) == null ? void 0 : _a.attributes) ?? {});
        }
      } else if (attribute.type === "dynamiczone") {
        const dynamicZoneValue = predicate(attribute, value) ? transform(value, attribute) : value;
        acc[key] = dynamicZoneValue.map((componentData) => {
          var _a2;
          return traverse(componentData, ((_a2 = components[componentData.__component]) == null ? void 0 : _a2.attributes) ?? {});
        });
      } else if (predicate(attribute, value)) {
        acc[key] = transform(value, attribute);
      } else {
        acc[key] = value;
      }
      return acc;
    }, {});
  };
  return traverse(data, schema.attributes);
};
var removeProhibitedFields = (prohibitedFields) => traverseData((attribute) => prohibitedFields.includes(attribute.type), () => "");
var prepareRelations = traverseData((attribute) => attribute.type === "relation", () => ({
  connect: [],
  disconnect: []
}));
var prepareTempKeys = traverseData((attribute) => attribute.type === "component" && attribute.repeatable || attribute.type === "dynamiczone", (data) => {
  if (Array.isArray(data) && data.length > 0) {
    const keys = generateNKeysBetween(void 0, void 0, data.length);
    return data.map((datum, index) => ({
      ...datum,
      __temp_key__: keys[index]
    }));
  }
  return data;
});
var removeFieldsThatDontExistOnSchema = (schema) => (data) => {
  const schemaKeys = Object.keys(schema.attributes);
  const dataKeys = Object.keys(data);
  const keysToRemove = dataKeys.filter((key) => !schemaKeys.includes(key));
  const revisedData = [
    ...keysToRemove,
    ...DOCUMENT_META_FIELDS
  ].reduce((acc, key) => {
    delete acc[key];
    return acc;
  }, structuredClone(data));
  return revisedData;
};
var removeNullValues = (data) => {
  return Object.entries(data).reduce((acc, [key, value]) => {
    if (value === null) {
      return acc;
    }
    acc[key] = value;
    return acc;
  }, {});
};
var transformDocument = (schema, components = {}) => (document) => {
  const transformations = (0, import_pipe.default)(removeFieldsThatDontExistOnSchema(schema), removeProhibitedFields([
    "password"
  ])(schema, components), removeNullValues, prepareRelations(schema, components), prepareTempKeys(schema, components));
  return transformations(document);
};
var handleInvisibleAttributes = (data, { schema, initialValues = {}, components = {} }, path = [], removedAttributes = []) => {
  var _a;
  if (!(schema == null ? void 0 : schema.attributes)) return {
    data,
    removedAttributes
  };
  const rulesEngine = createRulesEngine();
  const result = {};
  for (const [attrName, attrDef] of Object.entries(schema.attributes)) {
    const fullPath = [
      ...path,
      attrName
    ].join(".");
    const condition = (_a = attrDef == null ? void 0 : attrDef.conditions) == null ? void 0 : _a.visible;
    const isVisible = condition ? rulesEngine.evaluate(condition, {
      ...data,
      ...result
    }) : true;
    if (!isVisible) {
      removedAttributes.push(fullPath);
      continue;
    }
    const userProvided = Object.prototype.hasOwnProperty.call(data, attrName);
    const currentValue = userProvided ? data[attrName] : void 0;
    const initialValue = initialValues == null ? void 0 : initialValues[attrName];
    if (attrDef.type === "component") {
      const compSchema = components[attrDef.component];
      const value = currentValue ?? initialValue;
      if (!value) {
        result[attrName] = attrDef.repeatable ? [] : null;
        continue;
      }
      if (attrDef.repeatable && Array.isArray(value)) {
        result[attrName] = value.map((item, index) => handleInvisibleAttributes(item, {
          schema: compSchema,
          initialValues: (initialValue == null ? void 0 : initialValue[index]) ?? {},
          components
        }, [
          ...path,
          `${attrName}[${index}]`
        ], removedAttributes).data);
      } else {
        result[attrName] = handleInvisibleAttributes(value, {
          schema: compSchema,
          initialValues: initialValue ?? {},
          components
        }, [
          ...path,
          attrName
        ], removedAttributes).data;
      }
      continue;
    }
    if (attrDef.type === "dynamiczone") {
      if (!Array.isArray(currentValue)) {
        result[attrName] = [];
        continue;
      }
      result[attrName] = currentValue.map((dzItem, index) => {
        const compUID = dzItem == null ? void 0 : dzItem.__component;
        const compSchema = components[compUID];
        const cleaned = handleInvisibleAttributes(dzItem, {
          schema: compSchema,
          initialValues: (initialValue == null ? void 0 : initialValue[index]) ?? {},
          components
        }, [
          ...path,
          `${attrName}[${index}]`
        ], removedAttributes).data;
        return {
          __component: compUID,
          ...cleaned
        };
      });
      continue;
    }
    if (currentValue !== void 0) {
      result[attrName] = currentValue;
    } else if (initialValue !== void 0) {
      result[attrName] = initialValue;
    } else {
      if (attrName === "id" || attrName === "documentId") {
        continue;
      }
      result[attrName] = null;
    }
  }
  return {
    data: result,
    removedAttributes
  };
};

// node_modules/@strapi/content-manager/dist/admin/services/documents.mjs
var import_qs = __toESM(require_lib(), 1);
var documentApi = contentManagerApi.injectEndpoints({
  overrideExisting: true,
  endpoints: (builder) => ({
    autoCloneDocument: builder.mutation({
      query: ({ model, sourceId, params }) => ({
        url: `/content-manager/collection-types/${model}/auto-clone/${sourceId}`,
        method: "POST",
        config: {
          params
        }
      }),
      invalidatesTags: (_result, error, { model }) => {
        if (error) {
          return [];
        }
        return [
          {
            type: "Document",
            id: `${model}_LIST`
          },
          "RecentDocumentList"
        ];
      }
    }),
    cloneDocument: builder.mutation({
      query: ({ model, sourceId, data, params }) => ({
        url: `/content-manager/collection-types/${model}/clone/${sourceId}`,
        method: "POST",
        data,
        config: {
          params
        }
      }),
      invalidatesTags: (_result, _error, { model }) => [
        {
          type: "Document",
          id: `${model}_LIST`
        },
        {
          type: "UidAvailability",
          id: model
        },
        "RecentDocumentList"
      ]
    }),
    /**
    * Creates a new collection-type document. This should ONLY be used for collection-types.
    * single-types should always be using `updateDocument` since they always exist.
    */
    createDocument: builder.mutation({
      query: ({ model, data, params }) => ({
        url: `/content-manager/collection-types/${model}`,
        method: "POST",
        data,
        config: {
          params
        }
      }),
      invalidatesTags: (result, _error, { model }) => [
        {
          type: "Document",
          id: `${model}_LIST`
        },
        "Relations",
        {
          type: "UidAvailability",
          id: model
        },
        "RecentDocumentList"
      ],
      transformResponse: (response, meta, arg) => {
        if (!("data" in response) && arg.model === "plugin::users-permissions.user") {
          return {
            data: response,
            meta: {
              availableStatus: [],
              availableLocales: []
            }
          };
        }
        return response;
      }
    }),
    deleteDocument: builder.mutation({
      query: ({ collectionType, model, documentId, params }) => ({
        url: `/content-manager/${collectionType}/${model}${collectionType !== SINGLE_TYPES && documentId ? `/${documentId}` : ""}`,
        method: "DELETE",
        config: {
          params
        }
      }),
      invalidatesTags: (_result, _error, { collectionType, model }) => [
        {
          type: "Document",
          id: collectionType !== SINGLE_TYPES ? `${model}_LIST` : model
        },
        "RecentDocumentList"
      ]
    }),
    deleteManyDocuments: builder.mutation({
      query: ({ model, params, ...body }) => ({
        url: `/content-manager/collection-types/${model}/actions/bulkDelete`,
        method: "POST",
        data: body,
        config: {
          params
        }
      }),
      invalidatesTags: (_res, _error, { model }) => [
        {
          type: "Document",
          id: `${model}_LIST`
        },
        "RecentDocumentList"
      ]
    }),
    discardDocument: builder.mutation({
      query: ({ collectionType, model, documentId, params }) => ({
        url: documentId ? `/content-manager/${collectionType}/${model}/${documentId}/actions/discard` : `/content-manager/${collectionType}/${model}/actions/discard`,
        method: "POST",
        config: {
          params
        }
      }),
      invalidatesTags: (_result, _error, { collectionType, model, documentId }) => {
        return [
          {
            type: "Document",
            id: collectionType !== SINGLE_TYPES ? `${model}_${documentId}` : model
          },
          {
            type: "Document",
            id: `${model}_LIST`
          },
          "Relations",
          {
            type: "UidAvailability",
            id: model
          },
          "RecentDocumentList"
        ];
      }
    }),
    /**
    * Gets all documents of a collection type or single type.
    * By passing different params you can get different results e.g. only published documents or 'es' documents.
    */
    getAllDocuments: builder.query({
      query: ({ model, params }) => ({
        url: `/content-manager/collection-types/${model}`,
        method: "GET",
        config: {
          params: (0, import_qs.stringify)(params, {
            encode: true
          })
        }
      }),
      providesTags: (result, _error, arg) => {
        return [
          {
            type: "Document",
            id: `ALL_LIST`
          },
          {
            type: "Document",
            id: `${arg.model}_LIST`
          },
          ...(result == null ? void 0 : result.results.map(({ documentId }) => ({
            type: "Document",
            id: `${arg.model}_${documentId}`
          }))) ?? []
        ];
      }
    }),
    getDraftRelationCount: builder.query({
      query: ({ collectionType, model, documentId, params }) => ({
        url: documentId ? `/content-manager/${collectionType}/${model}/${documentId}/actions/countDraftRelations` : `/content-manager/${collectionType}/${model}/actions/countDraftRelations`,
        method: "GET",
        config: {
          params
        }
      })
    }),
    getDocument: builder.query({
      // @ts-expect-error – TODO: fix ts error where data unknown doesn't work with response via an assertion?
      queryFn: async ({ collectionType, model, documentId, params }, _api, _extraOpts, baseQuery) => {
        const res = await baseQuery({
          url: `/content-manager/${collectionType}/${model}${documentId ? `/${documentId}` : ""}`,
          method: "GET",
          config: {
            params
          }
        });
        if (res.error && res.error.name === "NotFoundError" && collectionType === SINGLE_TYPES) {
          return {
            data: {
              document: void 0
            },
            error: void 0
          };
        }
        return res;
      },
      providesTags: (result, _error, { collectionType, model, documentId }) => {
        return [
          // we prefer the result's id because we don't fetch single-types with an ID.
          {
            type: "Document",
            id: collectionType !== SINGLE_TYPES ? `${model}_${result && "documentId" in result ? result.documentId : documentId}` : model
          },
          // Make it easy to invalidate all individual documents queries for a model
          {
            type: "Document",
            id: `${model}_ALL_ITEMS`
          }
        ];
      }
    }),
    getManyDraftRelationCount: builder.query({
      query: ({ model, ...params }) => ({
        url: `/content-manager/collection-types/${model}/actions/countManyEntriesDraftRelations`,
        method: "GET",
        config: {
          params
        }
      }),
      transformResponse: (response) => response.data
    }),
    /**
    * This endpoint will either create or update documents at the same time as publishing.
    */
    publishDocument: builder.mutation({
      query: ({ collectionType, model, documentId, params, data }) => ({
        url: documentId ? `/content-manager/${collectionType}/${model}/${documentId}/actions/publish` : `/content-manager/${collectionType}/${model}/actions/publish`,
        method: "POST",
        data,
        config: {
          params
        }
      }),
      invalidatesTags: (_result, _error, { collectionType, model, documentId }) => {
        return [
          {
            type: "Document",
            id: collectionType !== SINGLE_TYPES ? `${model}_${documentId}` : model
          },
          {
            type: "Document",
            id: `${model}_LIST`
          },
          "Relations",
          "RecentDocumentList"
        ];
      }
    }),
    publishManyDocuments: builder.mutation({
      query: ({ model, params, ...body }) => ({
        url: `/content-manager/collection-types/${model}/actions/bulkPublish`,
        method: "POST",
        data: body,
        config: {
          params
        }
      }),
      invalidatesTags: (_res, _error, { model, documentIds }) => documentIds.map((id) => ({
        type: "Document",
        id: `${model}_${id}`
      }))
    }),
    updateDocument: builder.mutation({
      query: ({ collectionType, model, documentId, data, params }) => ({
        url: `/content-manager/${collectionType}/${model}${documentId ? `/${documentId}` : ""}`,
        method: "PUT",
        data,
        config: {
          params
        }
      }),
      invalidatesTags: (_result, _error, { collectionType, model, documentId }) => {
        return [
          {
            type: "Document",
            id: collectionType !== SINGLE_TYPES ? `${model}_${documentId}` : model
          },
          "Relations",
          {
            type: "UidAvailability",
            id: model
          },
          "RecentDocumentList",
          "RecentDocumentList"
        ];
      },
      async onQueryStarted({ data, ...patch }, { dispatch, queryFulfilled }) {
        const patchResult = dispatch(documentApi.util.updateQueryData("getDocument", patch, (draft) => {
          Object.assign(draft.data, data);
        }));
        try {
          await queryFulfilled;
        } catch {
          patchResult.undo();
        }
      },
      transformResponse: (response, meta, arg) => {
        if (!("data" in response) && arg.model === "plugin::users-permissions.user") {
          return {
            data: response,
            meta: {
              availableStatus: [],
              availableLocales: []
            }
          };
        }
        return response;
      }
    }),
    unpublishDocument: builder.mutation({
      query: ({ collectionType, model, documentId, params, data }) => ({
        url: documentId ? `/content-manager/${collectionType}/${model}/${documentId}/actions/unpublish` : `/content-manager/${collectionType}/${model}/actions/unpublish`,
        method: "POST",
        data,
        config: {
          params
        }
      }),
      invalidatesTags: (_result, _error, { collectionType, model, documentId }) => {
        return [
          {
            type: "Document",
            id: collectionType !== SINGLE_TYPES ? `${model}_${documentId}` : model
          },
          "RecentDocumentList"
        ];
      }
    }),
    unpublishManyDocuments: builder.mutation({
      query: ({ model, params, ...body }) => ({
        url: `/content-manager/collection-types/${model}/actions/bulkUnpublish`,
        method: "POST",
        data: body,
        config: {
          params
        }
      }),
      invalidatesTags: (_res, _error, { model, documentIds }) => [
        ...documentIds.map((id) => ({
          type: "Document",
          id: `${model}_${id}`
        })),
        "RecentDocumentList"
      ]
    })
  })
});
var { useAutoCloneDocumentMutation, useCloneDocumentMutation, useCreateDocumentMutation, useDeleteDocumentMutation, useDeleteManyDocumentsMutation, useDiscardDocumentMutation, useGetAllDocumentsQuery, useLazyGetDocumentQuery, useGetDocumentQuery, useLazyGetDraftRelationCountQuery, useGetManyDraftRelationCountQuery, usePublishDocumentMutation, usePublishManyDocumentsMutation, useUpdateDocumentMutation, useUnpublishDocumentMutation, useUnpublishManyDocumentsMutation } = documentApi;

// node_modules/@strapi/content-manager/dist/admin/utils/api.mjs
var buildValidParams = (query) => {
  if (!query) return query;
  const { plugins: _, ...validQueryParams } = {
    ...query,
    ...Object.values((query == null ? void 0 : query.plugins) ?? {}).reduce((acc, current) => Object.assign(acc, current), {})
  };
  return validQueryParams;
};
var isBaseQueryError = (error) => {
  return error.name !== void 0;
};

// node_modules/@strapi/content-manager/dist/admin/utils/validation.mjs
var import_pipe2 = __toESM(require_pipe(), 1);
var arrayValidator = (attribute, options) => ({
  message: errorsTrads.required,
  test(value) {
    if (options.status === "draft") {
      return true;
    }
    if (!attribute.required) {
      return true;
    }
    if (!value) {
      return false;
    }
    if (Array.isArray(value) && value.length === 0) {
      return false;
    }
    return true;
  }
});
var escapeRegex = (str) => str.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
var createYupSchema = (attributes = {}, components = {}, options = {
  status: null
}) => {
  const createModelSchema = (attributes2, removedAttributes = []) => create5().shape(Object.entries(attributes2).reduce((acc, [name, attribute]) => {
    const getNestedPathsForAttribute = (removed, attrName) => {
      const prefix = `${attrName}.`;
      const bracketRegex = new RegExp(`^${escapeRegex(attrName)}\\[\\d+\\]\\.`);
      return removed.filter((p) => p.startsWith(prefix) || bracketRegex.test(p)).map((p) => p.startsWith(prefix) ? p.slice(prefix.length) : p.replace(bracketRegex, ""));
    };
    if (DOCUMENT_META_FIELDS.includes(name)) {
      return acc;
    }
    if (removedAttributes == null ? void 0 : removedAttributes.includes(name)) {
      return acc;
    }
    const nestedRemoved = getNestedPathsForAttribute(removedAttributes, name);
    const validations = [
      addNullableValidation,
      addRequiredValidation,
      addMinLengthValidation,
      addMaxLengthValidation,
      addMinValidation,
      addMaxValidation,
      addRegexValidation
    ].map((fn) => fn(attribute, options));
    const transformSchema = (0, import_pipe2.default)(...validations);
    switch (attribute.type) {
      case "component": {
        const { attributes: attributes3 } = components[attribute.component];
        if (attribute.repeatable) {
          return {
            ...acc,
            [name]: transformSchema(create6().of(createModelSchema(attributes3, nestedRemoved).nullable(false))).test(arrayValidator(attribute, options))
          };
        } else {
          return {
            ...acc,
            [name]: transformSchema(createModelSchema(attributes3, nestedRemoved).nullable())
          };
        }
      }
      case "dynamiczone":
        return {
          ...acc,
          [name]: transformSchema(create6().of(create7((data) => {
            var _a;
            const attributes3 = (_a = components == null ? void 0 : components[data == null ? void 0 : data.__component]) == null ? void 0 : _a.attributes;
            const validation = create5().shape({
              __component: create3().required().oneOf(Object.keys(components))
            }).nullable(false);
            if (!attributes3) {
              return validation;
            }
            return validation.concat(createModelSchema(attributes3, nestedRemoved));
          }))).test(arrayValidator(attribute, options))
        };
      case "relation":
        return {
          ...acc,
          [name]: transformSchema(create7((value) => {
            if (!value) {
              return create().nullable(true);
            } else if (Array.isArray(value)) {
              return create6().of(create5().shape({
                id: create4().required()
              }));
            } else if (typeof value === "object") {
              return create5();
            } else {
              return create().test("type-error", "Relation values must be either null, an array of objects with {id} or an object.", () => false);
            }
          }))
        };
      default:
        return {
          ...acc,
          [name]: transformSchema(createAttributeSchema(attribute))
        };
    }
  }, {})).default(null);
  return createModelSchema(attributes, options.removedAttributes);
};
var createAttributeSchema = (attribute) => {
  switch (attribute.type) {
    case "biginteger":
      return create3().matches(/^-?\d*$/);
    case "boolean":
      return create2();
    case "blocks":
      return create().test("isBlocks", errorsTrads.json, (value) => {
        if (!value || Array.isArray(value)) {
          return true;
        } else {
          return false;
        }
      });
    case "decimal":
    case "float":
    case "integer":
      return create4();
    case "email":
      return create3().email(errorsTrads.email);
    case "enumeration":
      return create3().oneOf([
        ...attribute.enum,
        null
      ]);
    case "json":
      return create().test("isJSON", errorsTrads.json, (value) => {
        if (!value || typeof value === "string" && value.length === 0) {
          return true;
        }
        if (typeof value === "object") {
          try {
            JSON.stringify(value);
            return true;
          } catch (err) {
            return false;
          }
        }
        try {
          JSON.parse(value);
          return true;
        } catch (err) {
          return false;
        }
      });
    case "password":
    case "richtext":
    case "string":
    case "text":
      return create3();
    case "uid":
      return create3().matches(attribute.regex ? new RegExp(attribute.regex) : /^[A-Za-z0-9-_.~]*$/);
    default:
      return create();
  }
};
var nullableSchema = (schema) => {
  return (schema == null ? void 0 : schema.nullable) ? schema.nullable() : (
    // e.g. when the schema has been built using yup.lazy (e.g. for relations).
    // In these cases we should just return the schema as it is.
    schema
  );
};
var addNullableValidation = () => (schema) => {
  return nullableSchema(schema);
};
var addRequiredValidation = (attribute, options) => (schema) => {
  if (options.status === "draft" || !attribute.required) {
    return schema;
  }
  if (attribute.required && "required" in schema) {
    return schema.required(errorsTrads.required);
  }
  return schema;
};
var addMinLengthValidation = (attribute, options) => (schema) => {
  if (options.status === "draft") {
    return schema;
  }
  if ("minLength" in attribute && attribute.minLength && Number.isInteger(attribute.minLength) && "min" in schema) {
    return schema.min(attribute.minLength, {
      ...errorsTrads.minLength,
      values: {
        min: attribute.minLength
      }
    });
  }
  return schema;
};
var addMaxLengthValidation = (attribute) => (schema) => {
  if ("maxLength" in attribute && attribute.maxLength && Number.isInteger(attribute.maxLength) && "max" in schema) {
    return schema.max(attribute.maxLength, {
      ...errorsTrads.maxLength,
      values: {
        max: attribute.maxLength
      }
    });
  }
  return schema;
};
var addMinValidation = (attribute, options) => (schema) => {
  if (options.status === "draft") {
    return schema;
  }
  if ("min" in attribute && "min" in schema) {
    const min = toInteger(attribute.min);
    if (min) {
      return schema.min(min, {
        ...errorsTrads.min,
        values: {
          min
        }
      });
    }
  }
  return schema;
};
var addMaxValidation = (attribute) => (schema) => {
  if ("max" in attribute) {
    const max = toInteger(attribute.max);
    if ("max" in schema && max) {
      return schema.max(max, {
        ...errorsTrads.max,
        values: {
          max
        }
      });
    }
  }
  return schema;
};
var toInteger = (val) => {
  if (typeof val === "number" || val === void 0) {
    return val;
  } else {
    const num = Number(val);
    return isNaN(num) ? void 0 : num;
  }
};
var addRegexValidation = (attribute) => (schema) => {
  if ("regex" in attribute && attribute.regex && "matches" in schema) {
    return schema.matches(new RegExp(attribute.regex), {
      message: {
        id: errorsTrads.regex.id,
        defaultMessage: "The value does not match the defined pattern."
      },
      excludeEmptyString: !attribute.required
    });
  }
  return schema;
};

// node_modules/@strapi/content-manager/dist/admin/services/init.mjs
var initApi = contentManagerApi.injectEndpoints({
  endpoints: (builder) => ({
    getInitialData: builder.query({
      query: () => "/content-manager/init",
      transformResponse: (response) => response.data,
      providesTags: [
        "InitialData"
      ]
    })
  })
});
var { useGetInitialDataQuery } = initApi;

// node_modules/@strapi/content-manager/dist/admin/hooks/useContentTypeSchema.mjs
var React = __toESM(require_react(), 1);
var useContentTypeSchema = (model) => {
  const { toggleNotification } = useNotification();
  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();
  const { data, error, isLoading, isFetching } = useGetInitialDataQuery(void 0);
  const { components, contentType, contentTypes } = React.useMemo(() => {
    const contentType2 = data == null ? void 0 : data.contentTypes.find((ct) => ct.uid === model);
    const componentsByKey = data == null ? void 0 : data.components.reduce((acc, component) => {
      acc[component.uid] = component;
      return acc;
    }, {});
    const components2 = extractContentTypeComponents(contentType2 == null ? void 0 : contentType2.attributes, componentsByKey);
    return {
      components: Object.keys(components2).length === 0 ? void 0 : components2,
      contentType: contentType2,
      contentTypes: (data == null ? void 0 : data.contentTypes) ?? []
    };
  }, [
    model,
    data
  ]);
  React.useEffect(() => {
    if (error) {
      toggleNotification({
        type: "danger",
        message: formatAPIError(error)
      });
    }
  }, [
    toggleNotification,
    error,
    formatAPIError
  ]);
  return {
    // This must be memoized to avoid inifiinite re-renders where the empty object is different everytime.
    components: React.useMemo(() => components ?? {}, [
      components
    ]),
    schema: contentType,
    schemas: contentTypes,
    isLoading: isLoading || isFetching
  };
};
var extractContentTypeComponents = (attributes = {}, allComponents = {}) => {
  const getComponents = (attributes2) => {
    return attributes2.reduce((acc, attribute) => {
      var _a;
      if (attribute.type === "component") {
        const componentAttributes = Object.values(((_a = allComponents[attribute.component]) == null ? void 0 : _a.attributes) ?? {});
        acc.push(attribute.component, ...getComponents(componentAttributes));
      } else if (attribute.type === "dynamiczone") {
        acc.push(
          ...attribute.components,
          ...attribute.components.flatMap((componentUid) => {
            var _a2;
            const componentAttributes = Object.values(((_a2 = allComponents[componentUid]) == null ? void 0 : _a2.attributes) ?? {});
            return getComponents(componentAttributes);
          })
        );
      }
      return acc;
    }, []);
  };
  const componentUids = getComponents(Object.values(attributes));
  const uniqueComponentUids = [
    ...new Set(componentUids)
  ];
  const componentsByKey = uniqueComponentUids.reduce((acc, uid) => {
    acc[uid] = allComponents[uid];
    return acc;
  }, {});
  return componentsByKey;
};

// node_modules/@strapi/content-manager/dist/admin/constants/hooks.mjs
var HOOKS = {
  /**
  * Hook that allows to mutate the displayed headers of the list view table
  * @constant
  * @type {string}
  */
  INJECT_COLUMN_IN_TABLE: "Admin/CM/pages/ListView/inject-column-in-table",
  /**
  * Hook that allows to mutate the CM's collection types links pre-set filters
  * @constant
  * @type {string}
  */
  MUTATE_COLLECTION_TYPES_LINKS: "Admin/CM/pages/App/mutate-collection-types-links",
  /**
  * Hook that allows to mutate the CM's edit view layout
  * @constant
  * @type {string}
  */
  MUTATE_EDIT_VIEW_LAYOUT: "Admin/CM/pages/EditView/mutate-edit-view-layout",
  /**
  * Hook that allows to mutate the CM's single types links pre-set filters
  * @constant
  * @type {string}
  */
  MUTATE_SINGLE_TYPES_LINKS: "Admin/CM/pages/App/mutate-single-types-links"
};

// node_modules/@strapi/content-manager/dist/admin/services/contentTypes.mjs
var contentTypesApi = contentManagerApi.injectEndpoints({
  endpoints: (builder) => ({
    getContentTypeConfiguration: builder.query({
      query: (uid) => ({
        url: `/content-manager/content-types/${uid}/configuration`,
        method: "GET"
      }),
      transformResponse: (response) => response.data,
      providesTags: (_result, _error, uid) => [
        {
          type: "ContentTypesConfiguration",
          id: uid
        },
        {
          type: "ContentTypeSettings",
          id: "LIST"
        }
      ]
    }),
    getAllContentTypeSettings: builder.query({
      query: () => "/content-manager/content-types-settings",
      transformResponse: (response) => response.data,
      providesTags: [
        {
          type: "ContentTypeSettings",
          id: "LIST"
        }
      ]
    }),
    updateContentTypeConfiguration: builder.mutation({
      query: ({ uid, ...body }) => ({
        url: `/content-manager/content-types/${uid}/configuration`,
        method: "PUT",
        data: body
      }),
      transformResponse: (response) => response.data,
      invalidatesTags: (_result, _error, { uid }) => [
        {
          type: "ContentTypesConfiguration",
          id: uid
        },
        {
          type: "ContentTypeSettings",
          id: "LIST"
        },
        // Is this necessary?
        {
          type: "InitialData"
        }
      ]
    })
  })
});
var { useGetContentTypeConfigurationQuery, useGetAllContentTypeSettingsQuery, useUpdateContentTypeConfigurationMutation } = contentTypesApi;

// node_modules/@strapi/content-manager/dist/admin/hooks/useDocumentLayout.mjs
var React3 = __toESM(require_react(), 1);

// node_modules/@strapi/content-manager/dist/admin/utils/attributes.mjs
var checkIfAttributeIsDisplayable = (attribute) => {
  const { type } = attribute;
  if (type === "relation") {
    return !attribute.relation.toLowerCase().includes("morph");
  }
  return ![
    "json",
    "dynamiczone",
    "richtext",
    "password",
    "blocks"
  ].includes(type) && !!type;
};
var getMainField = (attribute, mainFieldName, { schemas, components }) => {
  var _a;
  if (!mainFieldName) {
    return void 0;
  }
  const mainFieldType = attribute.type === "component" ? components[attribute.component].attributes[mainFieldName].type : (_a = schemas.find((schema) => schema.uid === attribute.targetModel)) == null ? void 0 : _a.attributes[mainFieldName].type;
  return {
    name: mainFieldName,
    type: mainFieldType ?? "string"
  };
};

// node_modules/@strapi/content-manager/dist/admin/hooks/useDocument.mjs
var React2 = __toESM(require_react(), 1);

// node_modules/@strapi/content-manager/dist/admin/pages/EditView/utils/forms.mjs
var createDefaultForm = (contentType, components = {}) => {
  const traverseSchema = (attributes) => {
    return Object.entries(attributes).reduce((acc, [key, attribute]) => {
      if ("default" in attribute) {
        acc[key] = attribute.default;
      } else if (attribute.type === "component" && attribute.required) {
        const defaultComponentForm = traverseSchema(components[attribute.component].attributes);
        if (attribute.repeatable) {
          acc[key] = attribute.min ? [
            ...Array(attribute.min).fill(defaultComponentForm)
          ] : [];
        } else {
          acc[key] = defaultComponentForm;
        }
      } else if (attribute.type === "dynamiczone" && attribute.required) {
        acc[key] = [];
      }
      return acc;
    }, {});
  };
  return traverseSchema(contentType.attributes);
};

// node_modules/@strapi/content-manager/dist/admin/hooks/useDocument.mjs
var useDocument = (args, opts) => {
  const { toggleNotification } = useNotification();
  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();
  const { formatMessage } = useIntl();
  const { currentData: data, isLoading: isLoadingDocument, isFetching: isFetchingDocument, error, refetch } = useGetDocumentQuery(args, {
    ...opts,
    skip: !args.documentId && args.collectionType !== SINGLE_TYPES || (opts == null ? void 0 : opts.skip)
  });
  const document = data == null ? void 0 : data.data;
  const meta = data == null ? void 0 : data.meta;
  const { components, schema, schemas, isLoading: isLoadingSchema } = useContentTypeSchema(args.model);
  const isSingleType = (schema == null ? void 0 : schema.kind) === "singleType";
  const getTitle = (mainField) => {
    if (mainField !== "id" && (document == null ? void 0 : document[mainField])) {
      return document[mainField];
    }
    if (isSingleType && (schema == null ? void 0 : schema.info.displayName)) {
      return schema.info.displayName;
    }
    return formatMessage({
      id: "content-manager.containers.untitled",
      defaultMessage: "Untitled"
    });
  };
  React2.useEffect(() => {
    if (error) {
      toggleNotification({
        type: "danger",
        message: formatAPIError(error)
      });
    }
  }, [
    toggleNotification,
    error,
    formatAPIError,
    args.collectionType
  ]);
  const validationSchema = React2.useMemo(() => {
    if (!schema) {
      return null;
    }
    return createYupSchema(schema.attributes, components);
  }, [
    schema,
    components
  ]);
  const validate = React2.useCallback((document2) => {
    if (!validationSchema) {
      throw new Error("There is no validation schema generated, this is likely due to the schema not being loaded yet.");
    }
    try {
      validationSchema.validateSync(document2, {
        abortEarly: false,
        strict: true
      });
      return null;
    } catch (error2) {
      if (error2 instanceof ValidationError) {
        return getYupValidationErrors(error2);
      }
      throw error2;
    }
  }, [
    validationSchema
  ]);
  const getInitialFormValues = React2.useCallback((isCreatingDocument = false) => {
    if (!document && !isCreatingDocument && !isSingleType || !schema) {
      return void 0;
    }
    const form = (document == null ? void 0 : document.id) ? document : createDefaultForm(schema, components);
    return transformDocument(schema, components)(form);
  }, [
    document,
    isSingleType,
    schema,
    components
  ]);
  const isLoading = isLoadingDocument || isFetchingDocument || isLoadingSchema;
  const hasError = !!error;
  return {
    components,
    document,
    meta,
    isLoading,
    hasError,
    schema,
    schemas,
    validate,
    getTitle,
    getInitialFormValues,
    refetch
  };
};
var useDoc = () => {
  const { id, slug, collectionType, origin } = useParams();
  const [{ query }] = useQueryParams();
  const params = React2.useMemo(() => buildValidParams(query), [
    query
  ]);
  if (!collectionType) {
    throw new Error("Could not find collectionType in url params");
  }
  if (!slug) {
    throw new Error("Could not find model in url params");
  }
  const document = useDocument({
    documentId: origin || id,
    model: slug,
    collectionType,
    params
  }, {
    skip: id === "create" || !origin && !id && collectionType !== SINGLE_TYPES
  });
  const returnId = origin || id === "create" ? void 0 : id;
  return {
    collectionType,
    model: slug,
    id: returnId,
    ...document
  };
};
var useContentManagerContext = () => {
  var _a;
  const { collectionType, model, id, components, isLoading: isLoadingDoc, schema, schemas } = useDoc();
  const layout = useDocumentLayout(model);
  const form = useForm("useContentManagerContext", (state) => state);
  const isSingleType = collectionType === SINGLE_TYPES;
  const slug = model;
  const isCreatingEntry = id === "create";
  useContentTypeSchema();
  const isLoading = isLoadingDoc || layout.isLoading;
  const error = layout.error;
  return {
    error,
    isLoading,
    // Base metadata
    model,
    collectionType,
    id,
    slug,
    isCreatingEntry,
    isSingleType,
    hasDraftAndPublish: ((_a = schema == null ? void 0 : schema.options) == null ? void 0 : _a.draftAndPublish) ?? false,
    // All schema infos
    components,
    contentType: schema,
    contentTypes: schemas,
    // Form state
    form,
    // layout infos
    layout
  };
};

// node_modules/@strapi/content-manager/dist/admin/hooks/useDocumentLayout.mjs
var DEFAULT_SETTINGS = {
  bulkable: false,
  filterable: false,
  searchable: false,
  pagination: false,
  defaultSortBy: "",
  defaultSortOrder: "asc",
  mainField: "id",
  pageSize: 10
};
var useDocumentLayout = (model) => {
  const { schema, components } = useDocument({
    model,
    collectionType: ""
  }, {
    skip: true
  });
  const [{ query }] = useQueryParams();
  const runHookWaterfall = useStrapiApp("useDocumentLayout", (state) => state.runHookWaterfall);
  const { toggleNotification } = useNotification();
  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();
  const { isLoading: isLoadingSchemas, schemas } = useContentTypeSchema();
  const { data, isLoading: isLoadingConfigs, error, isFetching: isFetchingConfigs } = useGetContentTypeConfigurationQuery(model);
  const isLoading = isLoadingSchemas || isFetchingConfigs || isLoadingConfigs;
  React3.useEffect(() => {
    if (error) {
      toggleNotification({
        type: "danger",
        message: formatAPIError(error)
      });
    }
  }, [
    error,
    formatAPIError,
    toggleNotification
  ]);
  const editLayout = React3.useMemo(() => data && !isLoading ? formatEditLayout(data, {
    schemas,
    schema,
    components
  }) : {
    layout: [],
    components: {},
    metadatas: {},
    options: {},
    settings: DEFAULT_SETTINGS
  }, [
    data,
    isLoading,
    schemas,
    schema,
    components
  ]);
  const listLayout = React3.useMemo(() => {
    return data && !isLoading ? formatListLayout(data, {
      schemas,
      schema,
      components
    }) : {
      layout: [],
      metadatas: {},
      options: {},
      settings: DEFAULT_SETTINGS
    };
  }, [
    data,
    isLoading,
    schemas,
    schema,
    components
  ]);
  const { layout: edit } = React3.useMemo(() => runHookWaterfall(HOOKS.MUTATE_EDIT_VIEW_LAYOUT, {
    layout: editLayout,
    query
  }), [
    editLayout,
    query,
    runHookWaterfall
  ]);
  return {
    error,
    isLoading,
    edit,
    list: listLayout
  };
};
var useDocLayout = () => {
  const { model } = useDoc();
  return useDocumentLayout(model);
};
var formatEditLayout = (data, { schemas, schema, components }) => {
  let currentPanelIndex = 0;
  const panelledEditAttributes = convertEditLayoutToFieldLayouts(data.contentType.layouts.edit, schema == null ? void 0 : schema.attributes, data.contentType.metadatas, {
    configurations: data.components,
    schemas: components
  }, schemas).reduce((panels, row) => {
    if (row.some((field) => field.type === "dynamiczone")) {
      panels.push([
        row
      ]);
      currentPanelIndex += 2;
    } else {
      if (!panels[currentPanelIndex]) {
        panels.push([
          row
        ]);
      } else {
        panels[currentPanelIndex].push(row);
      }
    }
    return panels;
  }, []);
  const componentEditAttributes = Object.entries(data.components).reduce((acc, [uid, configuration]) => {
    acc[uid] = {
      layout: convertEditLayoutToFieldLayouts(configuration.layouts.edit, components[uid].attributes, configuration.metadatas, {
        configurations: data.components,
        schemas: components
      }),
      settings: {
        ...configuration.settings,
        icon: components[uid].info.icon,
        displayName: components[uid].info.displayName
      }
    };
    return acc;
  }, {});
  const editMetadatas = Object.entries(data.contentType.metadatas).reduce((acc, [attribute, metadata]) => {
    return {
      ...acc,
      [attribute]: metadata.edit
    };
  }, {});
  return {
    layout: panelledEditAttributes,
    components: componentEditAttributes,
    metadatas: editMetadatas,
    settings: {
      ...data.contentType.settings,
      displayName: schema == null ? void 0 : schema.info.displayName
    },
    options: {
      ...schema == null ? void 0 : schema.options,
      ...schema == null ? void 0 : schema.pluginOptions,
      ...data.contentType.options
    }
  };
};
var convertEditLayoutToFieldLayouts = (rows, attributes = {}, metadatas, components, schemas = []) => {
  return rows.map((row) => row.map((field) => {
    const attribute = attributes[field.name];
    if (!attribute) {
      return null;
    }
    const { edit: metadata } = metadatas[field.name];
    const settings = attribute.type === "component" && components ? components.configurations[attribute.component].settings : {};
    return {
      attribute,
      disabled: !metadata.editable,
      hint: metadata.description,
      label: metadata.label ?? "",
      name: field.name,
      // @ts-expect-error – mainField does exist on the metadata for a relation.
      mainField: getMainField(attribute, metadata.mainField || settings.mainField, {
        schemas,
        components: (components == null ? void 0 : components.schemas) ?? {}
      }),
      placeholder: metadata.placeholder ?? "",
      required: attribute.required ?? false,
      size: field.size,
      unique: "unique" in attribute ? attribute.unique : false,
      visible: metadata.visible ?? true,
      type: attribute.type
    };
  }).filter((field) => field !== null));
};
var formatListLayout = (data, { schemas, schema, components }) => {
  const listMetadatas = Object.entries(data.contentType.metadatas).reduce((acc, [attribute, metadata]) => {
    return {
      ...acc,
      [attribute]: metadata.list
    };
  }, {});
  const listAttributes = convertListLayoutToFieldLayouts(data.contentType.layouts.list, schema == null ? void 0 : schema.attributes, listMetadatas, {
    configurations: data.components,
    schemas: components
  }, schemas);
  return {
    layout: listAttributes,
    settings: {
      ...data.contentType.settings,
      displayName: schema == null ? void 0 : schema.info.displayName
    },
    metadatas: listMetadatas,
    options: {
      ...schema == null ? void 0 : schema.options,
      ...schema == null ? void 0 : schema.pluginOptions,
      ...data.contentType.options
    }
  };
};
var convertListLayoutToFieldLayouts = (columns, attributes = {}, metadatas, components, schemas = []) => {
  return columns.map((name) => {
    const attribute = attributes[name];
    if (!attribute) {
      return null;
    }
    const metadata = metadatas[name];
    const settings = attribute.type === "component" && components ? components.configurations[attribute.component].settings : {};
    return {
      attribute,
      label: metadata.label ?? "",
      mainField: getMainField(attribute, metadata.mainField || settings.mainField, {
        schemas,
        components: (components == null ? void 0 : components.schemas) ?? {}
      }),
      name,
      searchable: metadata.searchable ?? true,
      sortable: metadata.sortable ?? true
    };
  }).filter((field) => field !== null);
};

// node_modules/@strapi/content-manager/dist/admin/utils/objects.mjs
var import_clone = __toESM(require_clone(), 1);
var import_toPath = __toESM(require_toPath(), 1);
function getIn(obj, key, def, pathStartIndex = 0) {
  const path = (0, import_toPath.default)(key);
  while (obj && pathStartIndex < path.length) {
    obj = obj[path[pathStartIndex++]];
  }
  if (pathStartIndex !== path.length && !obj) {
    return def;
  }
  return obj === void 0 ? def : obj;
}
var isObject = (obj) => obj !== null && typeof obj === "object" && !Array.isArray(obj);
var isInteger = (obj) => String(Math.floor(Number(obj))) === obj;
function setIn(obj, path, value) {
  const res = (0, import_clone.default)(obj);
  let resVal = res;
  let i = 0;
  const pathArray = (0, import_toPath.default)(path);
  for (; i < pathArray.length - 1; i++) {
    const currentPath = pathArray[i];
    const currentObj = getIn(obj, pathArray.slice(0, i + 1));
    if (currentObj && (isObject(currentObj) || Array.isArray(currentObj))) {
      resVal = resVal[currentPath] = (0, import_clone.default)(currentObj);
    } else {
      const nextPath = pathArray[i + 1];
      resVal = resVal[currentPath] = isInteger(nextPath) && Number(nextPath) >= 0 ? [] : {};
    }
  }
  if ((i === 0 ? obj : resVal)[pathArray[i]] === value) {
    return obj;
  }
  {
    delete resVal[pathArray[i]];
  }
  if (i === 0 && value === void 0) {
    delete res[pathArray[i]];
  }
  return res;
}

export {
  CREATED_BY_ATTRIBUTE_NAME,
  UPDATED_BY_ATTRIBUTE_NAME,
  CREATOR_FIELDS,
  PUBLISHED_BY_ATTRIBUTE_NAME,
  CREATED_AT_ATTRIBUTE_NAME,
  UPDATED_AT_ATTRIBUTE_NAME,
  PUBLISHED_AT_ATTRIBUTE_NAME,
  ATTRIBUTE_TYPES_THAT_CANNOT_BE_MAIN_FIELD,
  SINGLE_TYPES,
  COLLECTION_TYPES,
  prepareTempKeys,
  removeFieldsThatDontExistOnSchema,
  transformDocument,
  handleInvisibleAttributes,
  createDefaultForm,
  require_lib,
  useAutoCloneDocumentMutation,
  useCloneDocumentMutation,
  useCreateDocumentMutation,
  useDeleteDocumentMutation,
  useDeleteManyDocumentsMutation,
  useDiscardDocumentMutation,
  useGetAllDocumentsQuery,
  useLazyGetDocumentQuery,
  useLazyGetDraftRelationCountQuery,
  useGetManyDraftRelationCountQuery,
  usePublishDocumentMutation,
  usePublishManyDocumentsMutation,
  useUpdateDocumentMutation,
  useUnpublishDocumentMutation,
  useUnpublishManyDocumentsMutation,
  buildValidParams,
  isBaseQueryError,
  createYupSchema,
  useGetInitialDataQuery,
  useContentTypeSchema,
  extractContentTypeComponents,
  HOOKS,
  useGetContentTypeConfigurationQuery,
  useGetAllContentTypeSettingsQuery,
  useUpdateContentTypeConfigurationMutation,
  checkIfAttributeIsDisplayable,
  getMainField,
  DEFAULT_SETTINGS,
  useDocumentLayout,
  useDocLayout,
  convertEditLayoutToFieldLayouts,
  convertListLayoutToFieldLayouts,
  useDocument,
  useDoc,
  useContentManagerContext,
  getIn,
  setIn
};
//# sourceMappingURL=chunk-LIDWYKFI.js.map
