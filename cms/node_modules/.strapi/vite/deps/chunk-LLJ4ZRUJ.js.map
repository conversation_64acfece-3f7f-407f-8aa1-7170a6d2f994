{"version": 3, "sources": ["../../../lodash/_charsEndIndex.js", "../../../lodash/trimEnd.js", "../../../@strapi/admin/admin/src/utils/urls.ts", "../../../@strapi/admin/admin/src/services/admin.ts"], "sourcesContent": ["var baseIndexOf = require('./_baseIndexOf');\n\n/**\n * Used by `_.trim` and `_.trimEnd` to get the index of the last string symbol\n * that is not found in the character symbols.\n *\n * @private\n * @param {Array} strSymbols The string symbols to inspect.\n * @param {Array} chrSymbols The character symbols to find.\n * @returns {number} Returns the index of the last unmatched string symbol.\n */\nfunction charsEndIndex(strSymbols, chrSymbols) {\n  var index = strSymbols.length;\n\n  while (index-- && baseIndexOf(chrSymbols, strSymbols[index], 0) > -1) {}\n  return index;\n}\n\nmodule.exports = charsEndIndex;\n", "var baseToString = require('./_baseToString'),\n    castSlice = require('./_castSlice'),\n    charsEndIndex = require('./_charsEndIndex'),\n    stringToArray = require('./_stringToArray'),\n    toString = require('./toString'),\n    trimmedEndIndex = require('./_trimmedEndIndex');\n\n/**\n * Removes trailing whitespace or specified characters from `string`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category String\n * @param {string} [string=''] The string to trim.\n * @param {string} [chars=whitespace] The characters to trim.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.\n * @returns {string} Returns the trimmed string.\n * @example\n *\n * _.trimEnd('  abc  ');\n * // => '  abc'\n *\n * _.trimEnd('-_-abc-_-', '_-');\n * // => '-_-abc'\n */\nfunction trimEnd(string, chars, guard) {\n  string = toString(string);\n  if (string && (guard || chars === undefined)) {\n    return string.slice(0, trimmedEndIndex(string) + 1);\n  }\n  if (!string || !(chars = baseToString(chars))) {\n    return string;\n  }\n  var strSymbols = stringToArray(string),\n      end = charsEndIndex(strSymbols, stringToArray(chars)) + 1;\n\n  return castSlice(strSymbols, 0, end).join('');\n}\n\nmodule.exports = trimEnd;\n", "import trimEnd from 'lodash/trimEnd';\n\nconst prefixFileUrlWithBackendUrl = (fileURL?: string): string | undefined => {\n  return !!fileURL && fileURL.startsWith('/') ? `${window.strapi.backendURL}${fileURL}` : fileURL;\n};\n\n/**\n * @description Creates an absolute URL, if there is no URL or it\n * is relative, we use the `window.location.origin` as a fallback.\n * IF it's an absolute URL, we return it as is.\n */\nconst createAbsoluteUrl = (url?: string): string => {\n  if (!url) {\n    return window.location.origin;\n  }\n  if (url.startsWith('/')) {\n    /**\n     * This will also manage protocol relative URLs which is fine because\n     * as we can see from the test, we still get the expected result.\n     */\n    return trimEnd(new URL(url, window.location.origin).toString(), '/');\n  } else {\n    return url;\n  }\n};\n\nexport { createAbsoluteUrl, prefixFileUrlWithBackendUrl };\n", "import {\n  type TelemetryProperties,\n  type Init,\n  type Information,\n  type GetProjectSettings,\n  type UpdateProjectSettings,\n  type Plugins,\n  type GetLicenseLimitInformation,\n  GetGuidedTourMeta,\n} from '../../../shared/contracts/admin';\nimport { prefixFileUrlWithBackendUrl } from '../utils/urls';\n\nimport { adminApi } from './api';\n\ninterface ConfigurationLogo {\n  custom?: {\n    name?: string;\n    url?: string;\n  };\n  default: string;\n}\n\nconst admin = adminApi\n  .enhanceEndpoints({\n    addTagTypes: ['ProjectSettings', 'LicenseLimits', 'LicenseTrialTimeLeft', 'GuidedTourMeta'],\n  })\n  .injectEndpoints({\n    endpoints: (builder) => ({\n      init: builder.query<Init.Response['data'], void>({\n        query: () => ({\n          url: '/admin/init',\n          method: 'GET',\n        }),\n        transformResponse(res: Init.Response) {\n          return res.data;\n        },\n        providesTags: ['ProjectSettings'],\n      }),\n      information: builder.query<Information.Response['data'], void>({\n        query: () => ({\n          url: '/admin/information',\n          method: 'GET',\n        }),\n        transformResponse(res: Information.Response) {\n          return res.data;\n        },\n      }),\n      telemetryProperties: builder.query<TelemetryProperties.Response['data'], void>({\n        query: () => ({\n          url: '/admin/telemetry-properties',\n          method: 'GET',\n          config: {\n            validateStatus: (status: number) => status < 500,\n          },\n        }),\n        transformResponse(res: TelemetryProperties.Response) {\n          return res.data;\n        },\n      }),\n      projectSettings: builder.query<\n        { authLogo?: ConfigurationLogo['custom']; menuLogo?: ConfigurationLogo['custom'] },\n        void\n      >({\n        query: () => ({\n          url: '/admin/project-settings',\n          method: 'GET',\n        }),\n        providesTags: ['ProjectSettings'],\n        transformResponse(data: GetProjectSettings.Response) {\n          return {\n            authLogo: data.authLogo\n              ? {\n                  name: data.authLogo.name,\n                  url: prefixFileUrlWithBackendUrl(data.authLogo.url),\n                }\n              : undefined,\n            menuLogo: data.menuLogo\n              ? {\n                  name: data.menuLogo.name,\n                  url: prefixFileUrlWithBackendUrl(data.menuLogo.url),\n                }\n              : undefined,\n          };\n        },\n      }),\n      updateProjectSettings: builder.mutation<UpdateProjectSettings.Response, FormData>({\n        query: (data) => ({\n          url: '/admin/project-settings',\n          method: 'POST',\n          data,\n          config: {\n            headers: {\n              'Content-Type': 'multipart/form-data',\n            },\n          },\n        }),\n        invalidatesTags: ['ProjectSettings'],\n      }),\n      getPlugins: builder.query<Plugins.Response, void>({\n        query: () => ({\n          url: '/admin/plugins',\n          method: 'GET',\n        }),\n      }),\n      getLicenseLimits: builder.query<GetLicenseLimitInformation.Response, void>({\n        query: () => ({\n          url: '/admin/license-limit-information',\n          method: 'GET',\n        }),\n        providesTags: ['LicenseLimits'],\n      }),\n      getLicenseTrialTimeLeft: builder.query<{ trialEndsAt: string }, void>({\n        query: () => ({\n          url: '/admin/license-trial-time-left',\n          method: 'GET',\n        }),\n        providesTags: ['LicenseTrialTimeLeft'],\n      }),\n      getGuidedTourMeta: builder.query<GetGuidedTourMeta.Response, void>({\n        query: () => ({\n          url: '/admin/guided-tour-meta',\n          method: 'GET',\n        }),\n        providesTags: ['GuidedTourMeta'],\n      }),\n    }),\n    overrideExisting: false,\n  });\n\nconst {\n  useInitQuery,\n  useTelemetryPropertiesQuery,\n  useInformationQuery,\n  useProjectSettingsQuery,\n  useUpdateProjectSettingsMutation,\n  useGetPluginsQuery,\n  useGetLicenseLimitsQuery,\n  useGetLicenseTrialTimeLeftQuery,\n  useGetGuidedTourMetaQuery,\n} = admin;\n\nexport {\n  useInitQuery,\n  useTelemetryPropertiesQuery,\n  useInformationQuery,\n  useProjectSettingsQuery,\n  useUpdateProjectSettingsMutation,\n  useGetPluginsQuery,\n  useGetLicenseLimitsQuery,\n  useGetLicenseTrialTimeLeftQuery,\n  useGetGuidedTourMetaQuery,\n};\n\nexport type { ConfigurationLogo };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,cAAc;AAWlB,aAAS,cAAc,YAAY,YAAY;AAC7C,UAAI,QAAQ,WAAW;AAEvB,aAAO,WAAW,YAAY,YAAY,WAAW,KAAK,GAAG,CAAC,IAAI,IAAI;AAAA,MAAC;AACvE,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;AClBjB;AAAA;AAAA,QAAI,eAAe;AAAnB,QACI,YAAY;AADhB,QAEI,gBAAgB;AAFpB,QAGI,gBAAgB;AAHpB,QAII,WAAW;AAJf,QAKI,kBAAkB;AAqBtB,aAASA,SAAQ,QAAQ,OAAO,OAAO;AACrC,eAAS,SAAS,MAAM;AACxB,UAAI,WAAW,SAAS,UAAU,SAAY;AAC5C,eAAO,OAAO,MAAM,GAAG,gBAAgB,MAAM,IAAI,CAAC;AAAA,MACpD;AACA,UAAI,CAAC,UAAU,EAAE,QAAQ,aAAa,KAAK,IAAI;AAC7C,eAAO;AAAA,MACT;AACA,UAAI,aAAa,cAAc,MAAM,GACjC,MAAM,cAAc,YAAY,cAAc,KAAK,CAAC,IAAI;AAE5D,aAAO,UAAU,YAAY,GAAG,GAAG,EAAE,KAAK,EAAE;AAAA,IAC9C;AAEA,WAAO,UAAUA;AAAA;AAAA;;;;ACtCjB,IAAMC,8BAA8B,CAACC,YAAAA;AACnC,SAAO,CAAC,CAACA,WAAWA,QAAQC,WAAW,GAAO,IAAA,GAAGC,OAAOC,OAAOC,UAAU,GAAGJ,OAAAA,KAAYA;AAC1F;AAOA,IAAMK,oBAAoB,CAACC,QAAAA;AACzB,MAAI,CAACA,KAAK;AACR,WAAOJ,OAAOK,SAASC;EACzB;AACA,MAAIF,IAAIL,WAAW,GAAM,GAAA;AAKvB,eAAOQ,eAAAA,SAAQ,IAAIC,IAAIJ,KAAKJ,OAAOK,SAASC,MAAM,EAAEG,SAAQ,GAAI,GAAA;SAC3D;AACL,WAAOL;EACT;AACF;;;ACFA,IAAMM,QAAQC,SACXC,iBAAiB;EAChBC,aAAa;IAAC;IAAmB;IAAiB;IAAwB;EAAiB;AAC7F,CAAA,EACCC,gBAAgB;EACfC,WAAW,CAACC,aAAa;IACvBC,MAAMD,QAAQE,MAAmC;MAC/CA,OAAO,OAAO;QACZC,KAAK;QACLC,QAAQ;;MAEVC,kBAAkBC,KAAkB;AAClC,eAAOA,IAAIC;MACb;MACAC,cAAc;QAAC;MAAkB;IACnC,CAAA;IACAC,aAAaT,QAAQE,MAA0C;MAC7DA,OAAO,OAAO;QACZC,KAAK;QACLC,QAAQ;;MAEVC,kBAAkBC,KAAyB;AACzC,eAAOA,IAAIC;MACb;IACF,CAAA;IACAG,qBAAqBV,QAAQE,MAAkD;MAC7EA,OAAO,OAAO;QACZC,KAAK;QACLC,QAAQ;QACRO,QAAQ;UACNC,gBAAgB,CAACC,WAAmBA,SAAS;QAC/C;;MAEFR,kBAAkBC,KAAiC;AACjD,eAAOA,IAAIC;MACb;IACF,CAAA;IACAO,iBAAiBd,QAAQE,MAGvB;MACAA,OAAO,OAAO;QACZC,KAAK;QACLC,QAAQ;;MAEVI,cAAc;QAAC;MAAkB;MACjCH,kBAAkBE,MAAiC;AACjD,eAAO;UACLQ,UAAUR,KAAKQ,WACX;YACEC,MAAMT,KAAKQ,SAASC;YACpBb,KAAKc,4BAA4BV,KAAKQ,SAASZ,GAAG;cAEpDe;UACJC,UAAUZ,KAAKY,WACX;YACEH,MAAMT,KAAKY,SAASH;YACpBb,KAAKc,4BAA4BV,KAAKY,SAAShB,GAAG;cAEpDe;QACN;MACF;IACF,CAAA;IACAE,uBAAuBpB,QAAQqB,SAAmD;MAChFnB,OAAO,CAACK,UAAU;QAChBJ,KAAK;QACLC,QAAQ;QACRG;QACAI,QAAQ;UACNW,SAAS;YACP,gBAAgB;UAClB;QACF;;MAEFC,iBAAiB;QAAC;MAAkB;IACtC,CAAA;IACAC,YAAYxB,QAAQE,MAA8B;MAChDA,OAAO,OAAO;QACZC,KAAK;QACLC,QAAQ;;IAEZ,CAAA;IACAqB,kBAAkBzB,QAAQE,MAAiD;MACzEA,OAAO,OAAO;QACZC,KAAK;QACLC,QAAQ;;MAEVI,cAAc;QAAC;MAAgB;IACjC,CAAA;IACAkB,yBAAyB1B,QAAQE,MAAqC;MACpEA,OAAO,OAAO;QACZC,KAAK;QACLC,QAAQ;;MAEVI,cAAc;QAAC;MAAuB;IACxC,CAAA;IACAmB,mBAAmB3B,QAAQE,MAAwC;MACjEA,OAAO,OAAO;QACZC,KAAK;QACLC,QAAQ;;MAEVI,cAAc;QAAC;MAAiB;IAClC,CAAA;;EAEFoB,kBAAkB;AACpB,CAAA;AAEF,IAAM,EACJC,cACAC,6BACAC,qBACAC,yBACAC,kCACAC,oBACAC,0BACAC,iCACAC,0BAAyB,IACvB3C;", "names": ["trimEnd", "prefixFileUrlWithBackendUrl", "fileURL", "startsWith", "window", "strapi", "backendURL", "createAbsoluteUrl", "url", "location", "origin", "trimEnd", "URL", "toString", "admin", "adminApi", "enhanceEndpoints", "addTagTypes", "injectEndpoints", "endpoints", "builder", "init", "query", "url", "method", "transformResponse", "res", "data", "providesTags", "information", "telemetryProperties", "config", "validateStatus", "status", "projectSettings", "auth<PERSON><PERSON>", "name", "prefixFileUrlWithBackendUrl", "undefined", "menuLogo", "updateProjectSettings", "mutation", "headers", "invalidatesTags", "getPlugins", "getLicenseLimits", "getLicenseTrialTimeLeft", "getGuidedTourMeta", "overrideExisting", "useInitQuery", "useTelemetryPropertiesQuery", "useInformationQuery", "useProjectSettingsQuery", "useUpdateProjectSettingsMutation", "useGetPluginsQuery", "useGetLicenseLimitsQuery", "useGetLicenseTrialTimeLeftQuery", "useGetGuidedTourMetaQuery"]}