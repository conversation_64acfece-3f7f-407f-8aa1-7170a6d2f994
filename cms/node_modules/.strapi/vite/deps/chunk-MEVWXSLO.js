import {
  useDrag,
  useDrop
} from "./chunk-HN5HEQBI.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/content-manager/dist/admin/constants/dragAndDrop.mjs
var ItemTypes = {
  COMPONENT: "component",
  EDIT_FIELD: "editField",
  FIELD: "field",
  DYNAMIC_ZONE: "dynamicZone",
  RELATION: "relation",
  BLOCKS: "blocks"
};

// node_modules/@strapi/content-manager/dist/admin/hooks/useDragAndDrop.mjs
var React2 = __toESM(require_react(), 1);

// node_modules/@strapi/content-manager/dist/admin/hooks/useKeyboardDragAndDrop.mjs
var React = __toESM(require_react(), 1);
var useKeyboardDragAndDrop = (active, index, { onCancel, onDropItem, onGrabItem, onMoveItem }) => {
  const [isSelected, setIsSelected] = React.useState(false);
  const handleMove = (movement) => {
    if (!isSelected) {
      return;
    }
    if (typeof index === "number" && onMoveItem) {
      if (movement === "UP") {
        onMoveItem(index - 1, index);
      } else if (movement === "DOWN") {
        onMoveItem(index + 1, index);
      }
    }
  };
  const handleDragClick = () => {
    if (isSelected) {
      if (onDropItem) {
        onDropItem(index);
      }
      setIsSelected(false);
    } else {
      if (onGrabItem) {
        onGrabItem(index);
      }
      setIsSelected(true);
    }
  };
  const handleCancel = () => {
    if (isSelected) {
      setIsSelected(false);
      if (onCancel) {
        onCancel(index);
      }
    }
  };
  const handleKeyDown = (e) => {
    if (!active) {
      return;
    }
    if (e.key === "Tab" && !isSelected) {
      return;
    }
    e.preventDefault();
    switch (e.key) {
      case " ":
      case "Enter":
        handleDragClick();
        break;
      case "Escape":
        handleCancel();
        break;
      case "ArrowDown":
      case "ArrowRight":
        handleMove("DOWN");
        break;
      case "ArrowUp":
      case "ArrowLeft":
        handleMove("UP");
        break;
    }
  };
  return handleKeyDown;
};

// node_modules/@strapi/content-manager/dist/admin/hooks/useDragAndDrop.mjs
var DIRECTIONS = {
  UPWARD: "upward",
  DOWNWARD: "downward"
};
var DROP_SENSITIVITY = {
  REGULAR: "regular",
  IMMEDIATE: "immediate"
};
var useDragAndDrop = (active, { type = "STRAPI_DND", index, item, onStart, onEnd, onGrabItem, onDropItem, onCancel, onMoveItem, dropSensitivity = DROP_SENSITIVITY.REGULAR }) => {
  const objectRef = React2.useRef(null);
  const [{ handlerId, isOver }, dropRef] = useDrop({
    accept: type,
    collect(monitor) {
      return {
        handlerId: monitor.getHandlerId(),
        isOver: monitor.isOver({
          shallow: true
        })
      };
    },
    drop(item2) {
      const draggedIndex = item2.index;
      const newIndex = index;
      if (isOver && onDropItem) {
        onDropItem(draggedIndex, newIndex);
      }
    },
    hover(item2, monitor) {
      var _a;
      if (!objectRef.current || !onMoveItem) {
        return;
      }
      const dragIndex = item2.index;
      const newIndex = index;
      const hoverBoundingRect = (_a = objectRef.current) == null ? void 0 : _a.getBoundingClientRect();
      const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
      const clientOffset = monitor.getClientOffset();
      if (!clientOffset) return;
      const hoverClientY = clientOffset && clientOffset.y - hoverBoundingRect.top;
      if (typeof dragIndex === "number" && typeof newIndex === "number") {
        if (dragIndex === newIndex) {
          return;
        }
        if (dropSensitivity === DROP_SENSITIVITY.REGULAR) {
          if (dragIndex < newIndex && hoverClientY < hoverMiddleY) {
            return;
          }
          if (dragIndex > newIndex && hoverClientY > hoverMiddleY) {
            return;
          }
        }
        onMoveItem(newIndex, dragIndex);
        item2.index = newIndex;
      } else {
        if (Array.isArray(dragIndex) && Array.isArray(newIndex)) {
          const minLength = Math.min(dragIndex.length, newIndex.length);
          let areEqual = true;
          let isLessThan = false;
          let isGreaterThan = false;
          for (let i = 0; i < minLength; i++) {
            if (dragIndex[i] < newIndex[i]) {
              isLessThan = true;
              areEqual = false;
              break;
            } else if (dragIndex[i] > newIndex[i]) {
              isGreaterThan = true;
              areEqual = false;
              break;
            }
          }
          if (areEqual && dragIndex.length === newIndex.length) {
            return;
          }
          if (dropSensitivity === DROP_SENSITIVITY.REGULAR) {
            if (isLessThan && !isGreaterThan && hoverClientY < hoverMiddleY) {
              return;
            }
            if (isGreaterThan && !isLessThan && hoverClientY > hoverMiddleY) {
              return;
            }
          }
        }
        onMoveItem(newIndex, dragIndex);
        item2.index = newIndex;
      }
    }
  });
  const getDragDirection = (monitor) => {
    if (monitor && monitor.isDragging() && !monitor.didDrop() && monitor.getInitialClientOffset() && monitor.getClientOffset()) {
      const deltaY = monitor.getInitialClientOffset().y - monitor.getClientOffset().y;
      if (deltaY > 0) return DIRECTIONS.UPWARD;
      if (deltaY < 0) return DIRECTIONS.DOWNWARD;
      return null;
    }
    return null;
  };
  const [{ isDragging, direction }, dragRef, dragPreviewRef] = useDrag({
    type,
    item() {
      var _a;
      if (onStart) {
        onStart();
      }
      const { width } = ((_a = objectRef.current) == null ? void 0 : _a.getBoundingClientRect()) ?? {};
      return {
        index,
        width,
        ...item
      };
    },
    end() {
      if (onEnd) {
        onEnd();
      }
    },
    canDrag: active,
    /**
    * This is useful when the item is in a virtualized list.
    * However, if we don't have an ID then we want the libraries
    * defaults to take care of this.
    */
    isDragging: (item == null ? void 0 : item.id) ? (monitor) => {
      return item.id === monitor.getItem().id;
    } : void 0,
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
      initialOffset: monitor.getInitialClientOffset(),
      currentOffset: monitor.getClientOffset(),
      direction: getDragDirection(monitor)
    })
  });
  const handleKeyDown = useKeyboardDragAndDrop(active, index, {
    onGrabItem,
    onDropItem,
    onCancel,
    onMoveItem
  });
  return [
    {
      handlerId,
      isDragging,
      handleKeyDown,
      isOverDropTarget: isOver,
      direction
    },
    objectRef,
    dropRef,
    dragRef,
    dragPreviewRef
  ];
};

export {
  ItemTypes,
  DIRECTIONS,
  DROP_SENSITIVITY,
  useDragAndDrop
};
//# sourceMappingURL=chunk-MEVWXSLO.js.map
