{"version": 3, "sources": ["../../../@strapi/content-manager/admin/src/constants/dragAndDrop.ts", "../../../@strapi/content-manager/admin/src/hooks/useKeyboardDragAndDrop.ts", "../../../@strapi/content-manager/admin/src/hooks/useDragAndDrop.ts"], "sourcesContent": ["export const ItemTypes = {\n  COMPONENT: 'component',\n  EDIT_FIELD: 'editField',\n  FIELD: 'field',\n  DYNAMIC_ZONE: 'dynamicZone',\n  RELATION: 'relation',\n  BLOCKS: 'blocks',\n} as const;\n", "import * as React from 'react';\n\nexport type UseKeyboardDragAndDropCallbacks<TIndex extends number | Array<number> = number> = {\n  onCancel?: (index: TIndex) => void;\n  onDropItem?: (currentIndex: TIndex, newIndex?: TIndex) => void;\n  onGrabItem?: (index: TIndex) => void;\n  onMoveItem?: (newIndex: TIndex, currentIndex: TIndex) => void;\n};\n\n/**\n * Utility hook designed to implement keyboard accessibile drag and drop by\n * returning an onKeyDown handler to be passed to the drag icon button.\n *\n * @internal - You should use `useDragAndDrop` instead.\n */\nexport const useKeyboardDragAndDrop = <TIndex extends number | Array<number> = number>(\n  active: boolean,\n  index: TIndex,\n  { onCancel, onDropItem, onGrabItem, onMoveItem }: UseKeyboardDragAndDropCallbacks<TIndex>\n) => {\n  const [isSelected, setIsSelected] = React.useState(false);\n\n  const handleMove = (movement: 'UP' | 'DOWN') => {\n    if (!isSelected) {\n      return;\n    }\n    if (typeof index === 'number' && onMoveItem) {\n      if (movement === 'UP') {\n        onMoveItem((index - 1) as TIndex, index);\n      } else if (movement === 'DOWN') {\n        onMoveItem((index + 1) as TIndex, index);\n      }\n    }\n  };\n\n  const handleDragClick = () => {\n    if (isSelected) {\n      if (onDropItem) {\n        onDropItem(index);\n      }\n      setIsSelected(false);\n    } else {\n      if (onGrabItem) {\n        onGrabItem(index);\n      }\n      setIsSelected(true);\n    }\n  };\n\n  const handleCancel = () => {\n    if (isSelected) {\n      setIsSelected(false);\n\n      if (onCancel) {\n        onCancel(index);\n      }\n    }\n  };\n\n  const handleKeyDown = <E extends Element>(e: React.KeyboardEvent<E>) => {\n    if (!active) {\n      return;\n    }\n\n    if (e.key === 'Tab' && !isSelected) {\n      return;\n    }\n\n    e.preventDefault();\n\n    switch (e.key) {\n      case ' ':\n      case 'Enter':\n        handleDragClick();\n        break;\n\n      case 'Escape':\n        handleCancel();\n        break;\n\n      case 'ArrowDown':\n      case 'ArrowRight':\n        handleMove('DOWN');\n        break;\n\n      case 'ArrowUp':\n      case 'ArrowLeft':\n        handleMove('UP');\n        break;\n\n      default:\n    }\n  };\n\n  return handleKeyDown;\n};\n", "import * as React from 'react';\n\nimport {\n  useDrag,\n  useDrop,\n  type HandlerManager,\n  type ConnectDragSource,\n  type ConnectDropTarget,\n  type ConnectDragPreview,\n  type DragSourceMonitor,\n} from 'react-dnd';\n\nimport {\n  useKeyboardDragAndDrop,\n  type UseKeyboardDragAndDropCallbacks,\n} from './useKeyboardDragAndDrop';\n\nimport type { Data } from '@strapi/types';\n\nconst DIRECTIONS = {\n  UPWARD: 'upward',\n  DOWNWARD: 'downward',\n} as const;\n\nconst DROP_SENSITIVITY = {\n  REGULAR: 'regular',\n  IMMEDIATE: 'immediate',\n} as const;\n\ninterface UseDragAndDropOptions<\n  TIndex extends number | Array<number> = number,\n  TItem extends { index: TIndex } = { index: TIndex },\n> extends UseKeyboardDragAndDropCallbacks<TIndex> {\n  type?: string;\n  index: TIndex;\n  item?: TItem;\n  onStart?: () => void;\n  onEnd?: () => void;\n  dropSensitivity?: (typeof DROP_SENSITIVITY)[keyof typeof DROP_SENSITIVITY];\n}\n\ntype Identifier = ReturnType<HandlerManager['getHandlerId']>;\n\ntype UseDragAndDropReturn<E extends Element = HTMLElement> = [\n  props: {\n    handlerId: Identifier;\n    isDragging: boolean;\n    handleKeyDown: <E extends Element>(event: React.KeyboardEvent<E>) => void;\n    isOverDropTarget: boolean;\n    direction: (typeof DIRECTIONS)[keyof typeof DIRECTIONS] | null;\n  },\n  objectRef: React.RefObject<E>,\n  dropRef: ConnectDropTarget,\n  dragRef: ConnectDragSource,\n  dragPreviewRef: ConnectDragPreview,\n];\n\ntype DropCollectedProps = {\n  handlerId: Identifier;\n  isOver: boolean;\n};\n\n/**\n * A utility hook abstracting the general drag and drop hooks from react-dnd.\n * Centralising the same behaviours and by default offering keyboard support.\n */\nconst useDragAndDrop = <\n  TIndex extends number | Array<number>,\n  TItem extends { index: TIndex; id?: Data.ID; [key: string]: unknown } = {\n    index: TIndex;\n    [key: string]: unknown;\n  },\n  E extends Element = HTMLElement,\n>(\n  active: boolean,\n  {\n    type = 'STRAPI_DND',\n    index,\n    item,\n    onStart,\n    onEnd,\n    onGrabItem,\n    onDropItem,\n    onCancel,\n    onMoveItem,\n    dropSensitivity = DROP_SENSITIVITY.REGULAR,\n  }: UseDragAndDropOptions<TIndex, TItem>\n): UseDragAndDropReturn<E> => {\n  const objectRef = React.useRef<E>(null);\n\n  const [{ handlerId, isOver }, dropRef] = useDrop<TItem, void, DropCollectedProps>({\n    accept: type,\n    collect(monitor) {\n      return {\n        handlerId: monitor.getHandlerId(),\n        isOver: monitor.isOver({ shallow: true }),\n      };\n    },\n    drop(item) {\n      const draggedIndex = item.index;\n      const newIndex = index;\n\n      if (isOver && onDropItem) {\n        onDropItem(draggedIndex, newIndex);\n      }\n    },\n    hover(item, monitor) {\n      if (!objectRef.current || !onMoveItem) {\n        return;\n      }\n\n      const dragIndex = item.index;\n      const newIndex = index;\n\n      const hoverBoundingRect = objectRef.current?.getBoundingClientRect();\n      const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;\n      const clientOffset = monitor.getClientOffset();\n      if (!clientOffset) return;\n\n      const hoverClientY = clientOffset && clientOffset.y - hoverBoundingRect.top;\n      if (typeof dragIndex === 'number' && typeof newIndex === 'number') {\n        if (dragIndex === newIndex) {\n          // Don't replace items with themselves\n          return;\n        }\n\n        if (dropSensitivity === DROP_SENSITIVITY.REGULAR) {\n          // Dragging downwards\n          if (dragIndex < newIndex && hoverClientY < hoverMiddleY) {\n            return;\n          }\n\n          // Dragging upwards\n          if (dragIndex > newIndex && hoverClientY > hoverMiddleY) {\n            return;\n          }\n        }\n\n        // Time to actually perform the action\n        onMoveItem(newIndex, dragIndex);\n        item.index = newIndex;\n      } else {\n        // Using numbers as indices doesn't work for nested list items with path like [1, 1, 0]\n        if (Array.isArray(dragIndex) && Array.isArray(newIndex)) {\n          // Indices comparison to find item position in nested list\n          const minLength = Math.min(dragIndex.length, newIndex.length);\n          let areEqual = true;\n          let isLessThan = false;\n          let isGreaterThan = false;\n\n          for (let i = 0; i < minLength; i++) {\n            if (dragIndex[i] < newIndex[i]) {\n              isLessThan = true;\n              areEqual = false;\n              break;\n            } else if (dragIndex[i] > newIndex[i]) {\n              isGreaterThan = true;\n              areEqual = false;\n              break;\n            }\n          }\n\n          // Don't replace items with themselves\n          if (areEqual && dragIndex.length === newIndex.length) {\n            return;\n          }\n\n          if (dropSensitivity === DROP_SENSITIVITY.REGULAR) {\n            // Dragging downwards\n            if (isLessThan && !isGreaterThan && hoverClientY < hoverMiddleY) {\n              return;\n            }\n\n            // Dragging upwards\n            if (isGreaterThan && !isLessThan && hoverClientY > hoverMiddleY) {\n              return;\n            }\n          }\n        }\n\n        onMoveItem(newIndex, dragIndex);\n        item.index = newIndex;\n      }\n    },\n  });\n\n  const getDragDirection = (monitor: DragSourceMonitor<TItem, void>) => {\n    if (\n      monitor &&\n      monitor.isDragging() &&\n      !monitor.didDrop() &&\n      monitor.getInitialClientOffset() &&\n      monitor.getClientOffset()\n    ) {\n      const deltaY = monitor.getInitialClientOffset()!.y - monitor.getClientOffset()!.y;\n\n      if (deltaY > 0) return DIRECTIONS.UPWARD;\n\n      if (deltaY < 0) return DIRECTIONS.DOWNWARD;\n\n      return null;\n    }\n\n    return null;\n  };\n\n  const [{ isDragging, direction }, dragRef, dragPreviewRef] = useDrag({\n    type,\n    item() {\n      if (onStart) {\n        onStart();\n      }\n\n      /**\n       * This will be attached and it helps define the preview sizes\n       * when a component is flexy e.g. Relations\n       */\n      const { width } = objectRef.current?.getBoundingClientRect() ?? {};\n\n      return { index, width, ...item };\n    },\n    end() {\n      if (onEnd) {\n        onEnd();\n      }\n    },\n    canDrag: active,\n    /**\n     * This is useful when the item is in a virtualized list.\n     * However, if we don't have an ID then we want the libraries\n     * defaults to take care of this.\n     */\n    isDragging: item?.id\n      ? (monitor) => {\n          return item.id === monitor.getItem().id;\n        }\n      : undefined,\n    collect: (monitor) => ({\n      isDragging: monitor.isDragging(),\n      initialOffset: monitor.getInitialClientOffset(),\n      currentOffset: monitor.getClientOffset(),\n      direction: getDragDirection(monitor),\n    }),\n  });\n\n  const handleKeyDown = useKeyboardDragAndDrop(active, index, {\n    onGrabItem,\n    onDropItem,\n    onCancel,\n    onMoveItem,\n  });\n\n  return [\n    { handlerId, isDragging, handleKeyDown, isOverDropTarget: isOver, direction },\n    objectRef,\n    dropRef,\n    dragRef,\n    dragPreviewRef,\n  ];\n};\n\nexport {\n  useDragAndDrop,\n  UseDragAndDropReturn,\n  UseDragAndDropOptions,\n  DIRECTIONS,\n  DROP_SENSITIVITY,\n};\n"], "mappings": ";;;;;;;;;;;;IAAaA,YAAY;EACvBC,WAAW;EACXC,YAAY;EACZC,OAAO;EACPC,cAAc;EACdC,UAAU;EACVC,QAAQ;AACV;;;;;;;ACQO,IAAMC,yBAAyB,CACpCC,QACAC,OACA,EAAEC,UAAUC,YAAYC,YAAYC,WAAU,MAA2C;AAEzF,QAAM,CAACC,YAAYC,aAAAA,IAAuBC,eAAS,KAAA;AAEnD,QAAMC,aAAa,CAACC,aAAAA;AAClB,QAAI,CAACJ,YAAY;AACf;IACF;AACA,QAAI,OAAOL,UAAU,YAAYI,YAAY;AAC3C,UAAIK,aAAa,MAAM;AACrBL,mBAAYJ,QAAQ,GAAcA,KAAAA;iBACzBS,aAAa,QAAQ;AAC9BL,mBAAYJ,QAAQ,GAAcA,KAAAA;MACpC;IACF;EACF;AAEA,QAAMU,kBAAkB,MAAA;AACtB,QAAIL,YAAY;AACd,UAAIH,YAAY;AACdA,mBAAWF,KAAAA;MACb;AACAM,oBAAc,KAAA;WACT;AACL,UAAIH,YAAY;AACdA,mBAAWH,KAAAA;MACb;AACAM,oBAAc,IAAA;IAChB;EACF;AAEA,QAAMK,eAAe,MAAA;AACnB,QAAIN,YAAY;AACdC,oBAAc,KAAA;AAEd,UAAIL,UAAU;AACZA,iBAASD,KAAAA;MACX;IACF;EACF;AAEA,QAAMY,gBAAgB,CAAoBC,MAAAA;AACxC,QAAI,CAACd,QAAQ;AACX;IACF;AAEA,QAAIc,EAAEC,QAAQ,SAAS,CAACT,YAAY;AAClC;IACF;AAEAQ,MAAEE,eAAc;AAEhB,YAAQF,EAAEC,KAAG;MACX,KAAK;MACL,KAAK;AACHJ,wBAAAA;AACA;MAEF,KAAK;AACHC,qBAAAA;AACA;MAEF,KAAK;MACL,KAAK;AACHH,mBAAW,MAAA;AACX;MAEF,KAAK;MACL,KAAK;AACHA,mBAAW,IAAA;AACX;IAGJ;EACF;AAEA,SAAOI;AACT;;;AC5EA,IAAMI,aAAa;EACjBC,QAAQ;EACRC,UAAU;AACZ;AAEA,IAAMC,mBAAmB;EACvBC,SAAS;EACTC,WAAW;AACb;AAuCA,IAAMC,iBAAiB,CAQrBC,QACA,EACEC,OAAO,cACPC,OACAC,MACAC,SACAC,OACAC,YACAC,YACAC,UACAC,YACAC,kBAAkBd,iBAAiBC,QAAO,MACL;AAEvC,QAAMc,YAAkBC,cAAU,IAAA;AAElC,QAAM,CAAC,EAAEC,WAAWC,OAAM,GAAIC,OAAQ,IAAGC,QAAyC;IAChFC,QAAQhB;IACRiB,QAAQC,SAAO;AACb,aAAO;QACLN,WAAWM,QAAQC,aAAY;QAC/BN,QAAQK,QAAQL,OAAO;UAAEO,SAAS;QAAK,CAAA;MACzC;IACF;IACAC,KAAKnB,OAAI;AACP,YAAMoB,eAAepB,MAAKD;AAC1B,YAAMsB,WAAWtB;AAEjB,UAAIY,UAAUP,YAAY;AACxBA,mBAAWgB,cAAcC,QAAAA;MAC3B;IACF;IACAC,MAAMtB,OAAMgB,SAAO;;AACjB,UAAI,CAACR,UAAUe,WAAW,CAACjB,YAAY;AACrC;MACF;AAEA,YAAMkB,YAAYxB,MAAKD;AACvB,YAAMsB,WAAWtB;AAEjB,YAAM0B,qBAAoBjB,eAAUe,YAAVf,mBAAmBkB;AAC7C,YAAMC,gBAAgBF,kBAAkBG,SAASH,kBAAkBI,OAAO;AAC1E,YAAMC,eAAed,QAAQe,gBAAe;AAC5C,UAAI,CAACD,aAAc;AAEnB,YAAME,eAAeF,gBAAgBA,aAAaG,IAAIR,kBAAkBI;AACxE,UAAI,OAAOL,cAAc,YAAY,OAAOH,aAAa,UAAU;AACjE,YAAIG,cAAcH,UAAU;AAE1B;QACF;AAEA,YAAId,oBAAoBd,iBAAiBC,SAAS;AAEhD,cAAI8B,YAAYH,YAAYW,eAAeL,cAAc;AACvD;UACF;AAGA,cAAIH,YAAYH,YAAYW,eAAeL,cAAc;AACvD;UACF;QACF;AAGArB,mBAAWe,UAAUG,SAAAA;AACrBxB,QAAAA,MAAKD,QAAQsB;aACR;AAEL,YAAIa,MAAMC,QAAQX,SAAAA,KAAcU,MAAMC,QAAQd,QAAW,GAAA;AAEvD,gBAAMe,YAAYC,KAAKC,IAAId,UAAUe,QAAQlB,SAASkB,MAAM;AAC5D,cAAIC,WAAW;AACf,cAAIC,aAAa;AACjB,cAAIC,gBAAgB;AAEpB,mBAASC,IAAI,GAAGA,IAAIP,WAAWO,KAAK;AAClC,gBAAInB,UAAUmB,CAAAA,IAAKtB,SAASsB,CAAAA,GAAI;AAC9BF,2BAAa;AACbD,yBAAW;AACX;uBACShB,UAAUmB,CAAAA,IAAKtB,SAASsB,CAAAA,GAAI;AACrCD,8BAAgB;AAChBF,yBAAW;AACX;YACF;UACF;AAGA,cAAIA,YAAYhB,UAAUe,WAAWlB,SAASkB,QAAQ;AACpD;UACF;AAEA,cAAIhC,oBAAoBd,iBAAiBC,SAAS;AAEhD,gBAAI+C,cAAc,CAACC,iBAAiBV,eAAeL,cAAc;AAC/D;YACF;AAGA,gBAAIe,iBAAiB,CAACD,cAAcT,eAAeL,cAAc;AAC/D;YACF;UACF;QACF;AAEArB,mBAAWe,UAAUG,SAAAA;AACrBxB,QAAAA,MAAKD,QAAQsB;MACf;IACF;EACF,CAAA;AAEA,QAAMuB,mBAAmB,CAAC5B,YAAAA;AACxB,QACEA,WACAA,QAAQ6B,WAAU,KAClB,CAAC7B,QAAQ8B,QAAO,KAChB9B,QAAQ+B,uBAAsB,KAC9B/B,QAAQe,gBAAe,GACvB;AACA,YAAMiB,SAAShC,QAAQ+B,uBAAsB,EAAId,IAAIjB,QAAQe,gBAAe,EAAIE;AAEhF,UAAIe,SAAS,EAAG,QAAO1D,WAAWC;AAElC,UAAIyD,SAAS,EAAG,QAAO1D,WAAWE;AAElC,aAAO;IACT;AAEA,WAAO;EACT;AAEA,QAAM,CAAC,EAAEqD,YAAYI,UAAS,GAAIC,SAASC,cAAe,IAAGC,QAAQ;IACnEtD;IACAE,OAAAA;;AACE,UAAIC,SAAS;AACXA,gBAAAA;MACF;AAMA,YAAM,EAAEoD,MAAK,MAAK7C,eAAUe,YAAVf,mBAAmBkB,4BAA2B,CAAA;AAEhE,aAAO;QAAE3B;QAAOsD;QAAO,GAAGrD;MAAK;IACjC;IACAsD,MAAAA;AACE,UAAIpD,OAAO;AACTA,cAAAA;MACF;IACF;IACAqD,SAAS1D;;;;;;IAMTgD,aAAY7C,6BAAMwD,MACd,CAACxC,YAAAA;AACC,aAAOhB,KAAKwD,OAAOxC,QAAQyC,QAAO,EAAGD;QAEvCE;IACJ3C,SAAS,CAACC,aAAa;MACrB6B,YAAY7B,QAAQ6B,WAAU;MAC9Bc,eAAe3C,QAAQ+B,uBAAsB;MAC7Ca,eAAe5C,QAAQe,gBAAe;MACtCkB,WAAWL,iBAAiB5B,OAAAA;;EAEhC,CAAA;AAEA,QAAM6C,gBAAgBC,uBAAuBjE,QAAQE,OAAO;IAC1DI;IACAC;IACAC;IACAC;EACF,CAAA;AAEA,SAAO;IACL;MAAEI;MAAWmC;MAAYgB;MAAeE,kBAAkBpD;MAAQsC;IAAU;IAC5EzC;IACAI;IACAsC;IACAC;EACD;AACH;", "names": ["ItemTypes", "COMPONENT", "EDIT_FIELD", "FIELD", "DYNAMIC_ZONE", "RELATION", "BLOCKS", "useKeyboardDragAndDrop", "active", "index", "onCancel", "onDropItem", "onGrabItem", "onMoveItem", "isSelected", "setIsSelected", "useState", "handleMove", "movement", "handleDragClick", "handleCancel", "handleKeyDown", "e", "key", "preventDefault", "DIRECTIONS", "UPWARD", "DOWNWARD", "DROP_SENSITIVITY", "REGULAR", "IMMEDIATE", "useDragAndDrop", "active", "type", "index", "item", "onStart", "onEnd", "onGrabItem", "onDropItem", "onCancel", "onMoveItem", "dropSensitivity", "objectRef", "useRef", "handlerId", "isOver", "dropRef", "useDrop", "accept", "collect", "monitor", "getHandlerId", "shallow", "drop", "draggedIndex", "newIndex", "hover", "current", "dragIndex", "hoverBoundingRect", "getBoundingClientRect", "hoverMiddleY", "bottom", "top", "clientOffset", "getClientOffset", "hoverClientY", "y", "Array", "isArray", "<PERSON><PERSON><PERSON><PERSON>", "Math", "min", "length", "areEqual", "is<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "i", "getDragDirection", "isDragging", "didDrop", "getInitialClientOffset", "deltaY", "direction", "dragRef", "dragPreviewRef", "useDrag", "width", "end", "canDrag", "id", "getItem", "undefined", "initialOffset", "currentOffset", "handleKeyDown", "useKeyboardDragAndDrop", "isOverDropTarget"]}