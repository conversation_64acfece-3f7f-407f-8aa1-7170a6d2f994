import {
  useField
} from "./chunk-VWIQ6S3Y.js";
import {
  CheckboxImpl,
  Field,
  Flex,
  RawTable,
  RawTbody,
  RawTd,
  RawTh,
  RawThead,
  RawTr,
  Typography,
  VisuallyHidden,
  useIntl
} from "./chunk-4YYUDJZ2.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  dt
} from "./chunk-3CQBCJ3G.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/Webhooks/components/Events.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);
var EventsRoot = ({ children }) => {
  const { formatMessage } = useIntl();
  const label = formatMessage({
    id: "Settings.webhooks.form.events",
    defaultMessage: "Events"
  });
  return (0, import_jsx_runtime.jsxs)(Flex, {
    direction: "column",
    alignItems: "stretch",
    gap: 1,
    children: [
      (0, import_jsx_runtime.jsx)(Field.Label, {
        "aria-hidden": true,
        children: label
      }),
      (0, import_jsx_runtime.jsx)(StyledTable, {
        "aria-label": label,
        children
      })
    ]
  });
};
var StyledTable = dt(RawTable)`
  tbody tr:nth-child(odd) {
    background: ${({ theme }) => theme.colors.neutral100};
  }

  thead th span {
    color: ${({ theme }) => theme.colors.neutral500};
  }

  td,
  th {
    padding-block-start: ${({ theme }) => theme.spaces[3]};
    padding-block-end: ${({ theme }) => theme.spaces[3]};
    width: 6%;
    vertical-align: middle;
  }

  tbody tr td:first-child {
    /**
     * Add padding to the start of the first column to avoid the checkbox appearing
     * too close to the edge of the table
     */
    padding-inline-start: ${({ theme }) => theme.spaces[2]};
  }
`;
var getCEHeaders = () => {
  const headers = [
    {
      id: "Settings.webhooks.events.create",
      defaultMessage: "Create"
    },
    {
      id: "Settings.webhooks.events.update",
      defaultMessage: "Update"
    },
    {
      id: "app.utils.delete",
      defaultMessage: "Delete"
    },
    {
      id: "app.utils.publish",
      defaultMessage: "Publish"
    },
    {
      id: "app.utils.unpublish",
      defaultMessage: "Unpublish"
    }
  ];
  return headers;
};
var EventsHeaders = ({ getHeaders = getCEHeaders }) => {
  const { formatMessage } = useIntl();
  const headers = getHeaders();
  return (0, import_jsx_runtime.jsx)(RawThead, {
    children: (0, import_jsx_runtime.jsxs)(RawTr, {
      children: [
        (0, import_jsx_runtime.jsx)(RawTh, {
          children: (0, import_jsx_runtime.jsx)(VisuallyHidden, {
            children: formatMessage({
              id: "Settings.webhooks.event.select",
              defaultMessage: "Select event"
            })
          })
        }),
        headers.map((header) => {
          if ([
            "app.utils.publish",
            "app.utils.unpublish"
          ].includes((header == null ? void 0 : header.id) ?? "")) {
            return (0, import_jsx_runtime.jsx)(RawTh, {
              title: formatMessage({
                id: "Settings.webhooks.event.publish-tooltip",
                defaultMessage: "This event only exists for content with draft & publish enabled"
              }),
              children: (0, import_jsx_runtime.jsx)(Typography, {
                variant: "sigma",
                textColor: "neutral600",
                children: formatMessage(header)
              })
            }, header.id);
          }
          return (0, import_jsx_runtime.jsx)(RawTh, {
            children: (0, import_jsx_runtime.jsx)(Typography, {
              variant: "sigma",
              textColor: "neutral600",
              children: formatMessage(header)
            })
          }, header.id);
        })
      ]
    })
  });
};
var EventsBody = ({ providedEvents }) => {
  const events = providedEvents || getCEEvents();
  const { value = [], onChange } = useField("events");
  const inputName = "events";
  const inputValue = value;
  const disabledEvents = [];
  const formattedValue = inputValue.reduce((acc, curr) => {
    const key = curr.split(".")[0];
    if (!acc[key]) {
      acc[key] = [];
    }
    acc[key].push(curr);
    return acc;
  }, {});
  const handleSelect = (name, value2) => {
    const set = new Set(inputValue);
    if (value2) {
      set.add(name);
    } else {
      set.delete(name);
    }
    onChange(inputName, Array.from(set));
  };
  const handleSelectAll = (name, value2) => {
    const set = new Set(inputValue);
    if (value2) {
      events[name].forEach((event) => {
        if (!disabledEvents.includes(event)) {
          set.add(event);
        }
      });
    } else {
      events[name].forEach((event) => set.delete(event));
    }
    onChange(inputName, Array.from(set));
  };
  return (0, import_jsx_runtime.jsx)(RawTbody, {
    children: Object.entries(events).map(([event, value2]) => {
      return (0, import_jsx_runtime.jsx)(EventsRow, {
        disabledEvents,
        name: event,
        events: value2,
        inputValue: formattedValue[event],
        handleSelect,
        handleSelectAll
      }, event);
    })
  });
};
var getCEEvents = () => {
  const entryEvents = [
    "entry.create",
    "entry.update",
    "entry.delete",
    "entry.publish",
    "entry.unpublish"
  ];
  return {
    entry: entryEvents,
    media: [
      "media.create",
      "media.update",
      "media.delete"
    ]
  };
};
var EventsRow = ({ disabledEvents = [], name, events = [], inputValue = [], handleSelect, handleSelectAll }) => {
  const { formatMessage } = useIntl();
  const enabledCheckboxes = events.filter((event) => !disabledEvents.includes(event));
  const hasSomeCheckboxSelected = inputValue.length > 0;
  const areAllCheckboxesSelected = inputValue.length === enabledCheckboxes.length;
  const onChangeAll = () => {
    const valueToSet = !areAllCheckboxesSelected;
    handleSelectAll(name, valueToSet);
  };
  const targetColumns = 5;
  return (0, import_jsx_runtime.jsxs)(RawTr, {
    children: [
      (0, import_jsx_runtime.jsx)(RawTd, {
        children: (0, import_jsx_runtime.jsx)(CheckboxImpl, {
          "aria-label": formatMessage({
            id: "global.select-all-entries",
            defaultMessage: "Select all entries"
          }),
          name,
          checked: hasSomeCheckboxSelected && !areAllCheckboxesSelected ? "indeterminate" : areAllCheckboxesSelected,
          onCheckedChange: onChangeAll,
          children: removeHyphensAndTitleCase(name)
        })
      }),
      events.map((event) => {
        return (0, import_jsx_runtime.jsx)(RawTd, {
          textAlign: "center",
          children: (0, import_jsx_runtime.jsx)(Flex, {
            width: "100%",
            justifyContent: "center",
            children: (0, import_jsx_runtime.jsx)(CheckboxImpl, {
              disabled: disabledEvents.includes(event),
              "aria-label": event,
              name: event,
              checked: inputValue.includes(event),
              onCheckedChange: (value) => handleSelect(event, !!value)
            })
          })
        }, event);
      }),
      events.length < targetColumns && (0, import_jsx_runtime.jsx)(RawTd, {
        colSpan: targetColumns - events.length
      })
    ]
  });
};
var removeHyphensAndTitleCase = (str) => str.replace(/-/g, " ").split(" ").map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()).join(" ");
var Events = {
  Root: EventsRoot,
  Headers: EventsHeaders,
  Body: EventsBody,
  Row: EventsRow
};

export {
  Events
};
//# sourceMappingURL=chunk-MGX3H22N.js.map
