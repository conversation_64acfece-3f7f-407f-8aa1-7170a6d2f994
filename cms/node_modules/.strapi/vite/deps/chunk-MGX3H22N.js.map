{"version": 3, "sources": ["../../../@strapi/admin/admin/src/pages/Settings/pages/Webhooks/components/Events.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport {\n  Checkbox,\n  Flex,\n  RawTable as Table,\n  RawTbody as Tbody,\n  RawTd as Td,\n  RawTh as Th,\n  <PERSON><PERSON><PERSON> as The<PERSON>,\n  RawTr as Tr,\n  Typography,\n  VisuallyHid<PERSON>,\n  Field,\n  CheckboxProps,\n} from '@strapi/design-system';\nimport { MessageDescriptor, useIntl } from 'react-intl';\nimport { styled } from 'styled-components';\n\nimport { useField } from '../../../../../components/Form';\n\n/* -------------------------------------------------------------------------------------------------\n * EventsRoot\n * -----------------------------------------------------------------------------------------------*/\n\ninterface EventsRootProps {\n  children: React.ReactNode;\n}\n\nconst EventsRoot = ({ children }: EventsRootProps) => {\n  const { formatMessage } = useIntl();\n\n  const label = formatMessage({\n    id: 'Settings.webhooks.form.events',\n    defaultMessage: 'Events',\n  });\n\n  return (\n    <Flex direction=\"column\" alignItems=\"stretch\" gap={1}>\n      <Field.Label aria-hidden>{label}</Field.Label>\n      {/* @ts-expect-error – TODO: add colCount & rowCount */}\n      <StyledTable aria-label={label}>{children}</StyledTable>\n    </Flex>\n  );\n};\n\n// TODO check whether we want to move alternating background colour tables to the design system\nconst StyledTable = styled(Table)`\n  tbody tr:nth-child(odd) {\n    background: ${({ theme }) => theme.colors.neutral100};\n  }\n\n  thead th span {\n    color: ${({ theme }) => theme.colors.neutral500};\n  }\n\n  td,\n  th {\n    padding-block-start: ${({ theme }) => theme.spaces[3]};\n    padding-block-end: ${({ theme }) => theme.spaces[3]};\n    width: 6%;\n    vertical-align: middle;\n  }\n\n  tbody tr td:first-child {\n    /**\n     * Add padding to the start of the first column to avoid the checkbox appearing\n     * too close to the edge of the table\n     */\n    padding-inline-start: ${({ theme }) => theme.spaces[2]};\n  }\n`;\n\n/* -------------------------------------------------------------------------------------------------\n * EventsHeaders\n * -----------------------------------------------------------------------------------------------*/\n\ninterface EventsHeadersProps {\n  getHeaders?: typeof getCEHeaders;\n}\n\nconst getCEHeaders = (): MessageDescriptor[] => {\n  const headers = [\n    { id: 'Settings.webhooks.events.create', defaultMessage: 'Create' },\n    { id: 'Settings.webhooks.events.update', defaultMessage: 'Update' },\n    { id: 'app.utils.delete', defaultMessage: 'Delete' },\n    { id: 'app.utils.publish', defaultMessage: 'Publish' },\n    { id: 'app.utils.unpublish', defaultMessage: 'Unpublish' },\n  ];\n\n  return headers;\n};\n\nconst EventsHeaders = ({ getHeaders = getCEHeaders }: EventsHeadersProps) => {\n  const { formatMessage } = useIntl();\n  const headers = getHeaders();\n\n  return (\n    <Thead>\n      <Tr>\n        <Th>\n          <VisuallyHidden>\n            {formatMessage({\n              id: 'Settings.webhooks.event.select',\n              defaultMessage: 'Select event',\n            })}\n          </VisuallyHidden>\n        </Th>\n        {headers.map((header) => {\n          if (['app.utils.publish', 'app.utils.unpublish'].includes(header?.id ?? '')) {\n            return (\n              <Th\n                key={header.id}\n                title={formatMessage({\n                  id: 'Settings.webhooks.event.publish-tooltip',\n                  defaultMessage: 'This event only exists for content with draft & publish enabled',\n                })}\n              >\n                <Typography variant=\"sigma\" textColor=\"neutral600\">\n                  {formatMessage(header)}\n                </Typography>\n              </Th>\n            );\n          }\n\n          return (\n            <Th key={header.id}>\n              <Typography variant=\"sigma\" textColor=\"neutral600\">\n                {formatMessage(header)}\n              </Typography>\n            </Th>\n          );\n        })}\n      </Tr>\n    </Thead>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * EventsBody\n * -----------------------------------------------------------------------------------------------*/\ninterface FormikContextValue {\n  events: string[];\n}\n\ninterface EventsBodyProps {\n  providedEvents?: Record<string, FormikContextValue['events']>;\n}\n\nconst EventsBody = ({ providedEvents }: EventsBodyProps) => {\n  const events = providedEvents || getCEEvents();\n  const { value = [], onChange } = useField<string[]>('events');\n\n  const inputName = 'events';\n  const inputValue = value;\n  const disabledEvents: string[] = [];\n\n  const formattedValue = inputValue.reduce<Record<string, string[]>>((acc, curr) => {\n    const key = curr.split('.')[0];\n\n    if (!acc[key]) {\n      acc[key] = [];\n    }\n    acc[key].push(curr);\n\n    return acc;\n  }, {});\n\n  const handleSelect: EventsRowProps['handleSelect'] = (name, value) => {\n    const set = new Set(inputValue);\n\n    if (value) {\n      set.add(name);\n    } else {\n      set.delete(name);\n    }\n\n    onChange(inputName, Array.from(set));\n  };\n\n  const handleSelectAll: EventsRowProps['handleSelectAll'] = (name, value) => {\n    const set = new Set(inputValue);\n\n    if (value) {\n      events[name].forEach((event) => {\n        if (!disabledEvents.includes(event)) {\n          set.add(event);\n        }\n      });\n    } else {\n      events[name].forEach((event) => set.delete(event));\n    }\n\n    onChange(inputName, Array.from(set));\n  };\n\n  return (\n    <Tbody>\n      {Object.entries(events).map(([event, value]) => {\n        return (\n          <EventsRow\n            disabledEvents={disabledEvents}\n            key={event}\n            name={event}\n            events={value}\n            inputValue={formattedValue[event]}\n            handleSelect={handleSelect}\n            handleSelectAll={handleSelectAll}\n          />\n        );\n      })}\n    </Tbody>\n  );\n};\n\nconst getCEEvents = (): Required<Pick<EventsBodyProps, 'providedEvents'>>['providedEvents'] => {\n  const entryEvents: FormikContextValue['events'] = [\n    'entry.create',\n    'entry.update',\n    'entry.delete',\n    'entry.publish',\n    'entry.unpublish',\n  ];\n\n  return {\n    entry: entryEvents,\n    media: ['media.create', 'media.update', 'media.delete'],\n  };\n};\n\n/* -------------------------------------------------------------------------------------------------\n * EventsRow\n * -----------------------------------------------------------------------------------------------*/\n\ninterface EventsRowProps {\n  disabledEvents?: string[];\n  events?: string[];\n  inputValue?: string[];\n  handleSelect: (name: string, value: boolean) => void;\n  handleSelectAll: (name: string, value: boolean) => void;\n  name: string;\n}\n\nconst EventsRow = ({\n  disabledEvents = [],\n  name,\n  events = [],\n  inputValue = [],\n  handleSelect,\n  handleSelectAll,\n}: EventsRowProps) => {\n  const { formatMessage } = useIntl();\n  const enabledCheckboxes = events.filter((event) => !disabledEvents.includes(event));\n\n  const hasSomeCheckboxSelected = inputValue.length > 0;\n  const areAllCheckboxesSelected = inputValue.length === enabledCheckboxes.length;\n\n  const onChangeAll: CheckboxProps['onCheckedChange'] = () => {\n    const valueToSet = !areAllCheckboxesSelected;\n\n    handleSelectAll(name, valueToSet);\n  };\n\n  const targetColumns = 5;\n\n  return (\n    <Tr>\n      <Td>\n        <Checkbox\n          aria-label={formatMessage({\n            id: 'global.select-all-entries',\n            defaultMessage: 'Select all entries',\n          })}\n          name={name}\n          checked={\n            hasSomeCheckboxSelected && !areAllCheckboxesSelected\n              ? 'indeterminate'\n              : areAllCheckboxesSelected\n          }\n          onCheckedChange={onChangeAll}\n        >\n          {removeHyphensAndTitleCase(name)}\n        </Checkbox>\n      </Td>\n\n      {events.map((event) => {\n        return (\n          <Td key={event} textAlign=\"center\">\n            <Flex width=\"100%\" justifyContent=\"center\">\n              <Checkbox\n                disabled={disabledEvents.includes(event)}\n                aria-label={event}\n                name={event}\n                checked={inputValue.includes(event)}\n                onCheckedChange={(value) => handleSelect(event, !!value)}\n              />\n            </Flex>\n          </Td>\n        );\n      })}\n      {events.length < targetColumns && <Td colSpan={targetColumns - events.length} />}\n    </Tr>\n  );\n};\n\n/**\n * Converts a string to title case and removes hyphens.\n */\nconst removeHyphensAndTitleCase = (str: string): string =>\n  str\n    .replace(/-/g, ' ')\n    .split(' ')\n    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())\n    .join(' ');\n\nconst Events = { Root: EventsRoot, Headers: EventsHeaders, Body: EventsBody, Row: EventsRow };\n\nexport { Events };\nexport type { EventsRowProps, EventsHeadersProps, EventsRootProps, EventsBodyProps };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BA,IAAMA,aAAa,CAAC,EAAEC,SAAQ,MAAmB;AAC/C,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,QAAMC,QAAQF,cAAc;IAC1BG,IAAI;IACJC,gBAAgB;EAClB,CAAA;AAEA,aACEC,yBAACC,MAAAA;IAAKC,WAAU;IAASC,YAAW;IAAUC,KAAK;;UACjDC,wBAACC,MAAMC,OAAK;QAACC,eAAW;QAAEX,UAAAA;;UAE1BQ,wBAACI,aAAAA;QAAYC,cAAYb;QAAQH;;;;AAGvC;AAGA,IAAMe,cAAcE,GAAOC,QAAAA;;kBAET,CAAC,EAAEC,MAAK,MAAOA,MAAMC,OAAOC,UAAU;;;;aAI3C,CAAC,EAAEF,MAAK,MAAOA,MAAMC,OAAOE,UAAU;;;;;2BAKxB,CAAC,EAAEH,MAAK,MAAOA,MAAMI,OAAO,CAAA,CAAE;yBAChC,CAAC,EAAEJ,MAAK,MAAOA,MAAMI,OAAO,CAAA,CAAE;;;;;;;;;;4BAU3B,CAAC,EAAEJ,MAAK,MAAOA,MAAMI,OAAO,CAAA,CAAE;;;AAY1D,IAAMC,eAAe,MAAA;AACnB,QAAMC,UAAU;IACd;MAAErB,IAAI;MAAmCC,gBAAgB;IAAS;IAClE;MAAED,IAAI;MAAmCC,gBAAgB;IAAS;IAClE;MAAED,IAAI;MAAoBC,gBAAgB;IAAS;IACnD;MAAED,IAAI;MAAqBC,gBAAgB;IAAU;IACrD;MAAED,IAAI;MAAuBC,gBAAgB;IAAY;EAC1D;AAED,SAAOoB;AACT;AAEA,IAAMC,gBAAgB,CAAC,EAAEC,aAAaH,aAAY,MAAsB;AACtE,QAAM,EAAEvB,cAAa,IAAKC,QAAAA;AAC1B,QAAMuB,UAAUE,WAAAA;AAEhB,aACEhB,wBAACiB,UAAAA;IACC,cAAAtB,yBAACuB,OAAAA;;YACClB,wBAACmB,OAAAA;UACC,cAAAnB,wBAACoB,gBAAAA;sBACE9B,cAAc;cACbG,IAAI;cACJC,gBAAgB;YAClB,CAAA;;;QAGHoB,QAAQO,IAAI,CAACC,WAAAA;AACZ,cAAI;YAAC;YAAqB;UAAsB,EAACC,UAASD,iCAAQ7B,OAAM,EAAK,GAAA;AAC3E,uBACEO,wBAACmB,OAAAA;cAECK,OAAOlC,cAAc;gBACnBG,IAAI;gBACJC,gBAAgB;cAClB,CAAA;cAEA,cAAAM,wBAACyB,YAAAA;gBAAWC,SAAQ;gBAAQC,WAAU;0BACnCrC,cAAcgC,MAAAA;;YAPZA,GAAAA,OAAO7B,EAAE;UAWpB;AAEA,qBACEO,wBAACmB,OAAAA;YACC,cAAAnB,wBAACyB,YAAAA;cAAWC,SAAQ;cAAQC,WAAU;wBACnCrC,cAAcgC,MAAAA;;UAFVA,GAAAA,OAAO7B,EAAE;QAMtB,CAAA;;;;AAIR;AAaA,IAAMmC,aAAa,CAAC,EAAEC,eAAc,MAAmB;AACrD,QAAMC,SAASD,kBAAkBE,YAAAA;AACjC,QAAM,EAAEC,QAAQ,CAAA,GAAIC,SAAQ,IAAKC,SAAmB,QAAA;AAEpD,QAAMC,YAAY;AAClB,QAAMC,aAAaJ;AACnB,QAAMK,iBAA2B,CAAA;AAEjC,QAAMC,iBAAiBF,WAAWG,OAAiC,CAACC,KAAKC,SAAAA;AACvE,UAAMC,MAAMD,KAAKE,MAAM,GAAA,EAAK,CAAE;AAE9B,QAAI,CAACH,IAAIE,GAAAA,GAAM;AACbF,UAAIE,GAAI,IAAG,CAAA;IACb;AACAF,QAAIE,GAAAA,EAAKE,KAAKH,IAAAA;AAEd,WAAOD;EACT,GAAG,CAAA,CAAC;AAEJ,QAAMK,eAA+C,CAACC,MAAMd,WAAAA;AAC1D,UAAMe,MAAM,IAAIC,IAAIZ,UAAAA;AAEpB,QAAIJ,QAAO;AACTe,UAAIE,IAAIH,IAAAA;WACH;AACLC,UAAIG,OAAOJ,IAAAA;IACb;AAEAb,aAASE,WAAWgB,MAAMC,KAAKL,GAAAA,CAAAA;EACjC;AAEA,QAAMM,kBAAqD,CAACP,MAAMd,WAAAA;AAChE,UAAMe,MAAM,IAAIC,IAAIZ,UAAAA;AAEpB,QAAIJ,QAAO;AACTF,aAAOgB,IAAAA,EAAMQ,QAAQ,CAACC,UAAAA;AACpB,YAAI,CAAClB,eAAed,SAASgC,KAAQ,GAAA;AACnCR,cAAIE,IAAIM,KAAAA;QACV;MACF,CAAA;WACK;AACLzB,aAAOgB,IAAAA,EAAMQ,QAAQ,CAACC,UAAUR,IAAIG,OAAOK,KAAAA,CAAAA;IAC7C;AAEAtB,aAASE,WAAWgB,MAAMC,KAAKL,GAAAA,CAAAA;EACjC;AAEA,aACE/C,wBAACwD,UAAAA;cACEC,OAAOC,QAAQ5B,MAAQT,EAAAA,IAAI,CAAC,CAACkC,OAAOvB,MAAM,MAAA;AACzC,iBACEhC,wBAAC2D,WAAAA;QACCtB;QAEAS,MAAMS;QACNzB,QAAQE;QACRI,YAAYE,eAAeiB,KAAM;QACjCV;QACAQ;MALKE,GAAAA,KAAAA;IAQX,CAAA;;AAGN;AAEA,IAAMxB,cAAc,MAAA;AAClB,QAAM6B,cAA4C;IAChD;IACA;IACA;IACA;IACA;EACD;AAED,SAAO;IACLC,OAAOD;IACPE,OAAO;MAAC;MAAgB;MAAgB;IAAe;EACzD;AACF;AAeA,IAAMH,YAAY,CAAC,EACjBtB,iBAAiB,CAAA,GACjBS,MACAhB,SAAS,CAAA,GACTM,aAAa,CAAA,GACbS,cACAQ,gBAAe,MACA;AACf,QAAM,EAAE/D,cAAa,IAAKC,QAAAA;AAC1B,QAAMwE,oBAAoBjC,OAAOkC,OAAO,CAACT,UAAU,CAAClB,eAAed,SAASgC,KAAAA,CAAAA;AAE5E,QAAMU,0BAA0B7B,WAAW8B,SAAS;AACpD,QAAMC,2BAA2B/B,WAAW8B,WAAWH,kBAAkBG;AAEzE,QAAME,cAAgD,MAAA;AACpD,UAAMC,aAAa,CAACF;AAEpBd,oBAAgBP,MAAMuB,UAAAA;EACxB;AAEA,QAAMC,gBAAgB;AAEtB,aACE3E,yBAACuB,OAAAA;;UACClB,wBAACuE,OAAAA;QACC,cAAAvE,wBAACwE,cAAAA;UACCnE,cAAYf,cAAc;YACxBG,IAAI;YACJC,gBAAgB;UAClB,CAAA;UACAoD;UACA2B,SACER,2BAA2B,CAACE,2BACxB,kBACAA;UAENO,iBAAiBN;oBAEhBO,0BAA0B7B,IAAAA;;;MAI9BhB,OAAOT,IAAI,CAACkC,UAAAA;AACX,mBACEvD,wBAACuE,OAAAA;UAAeK,WAAU;UACxB,cAAA5E,wBAACJ,MAAAA;YAAKiF,OAAM;YAAOC,gBAAe;YAChC,cAAA9E,wBAACwE,cAAAA;cACCO,UAAU1C,eAAed,SAASgC,KAAAA;cAClClD,cAAYkD;cACZT,MAAMS;cACNkB,SAASrC,WAAWb,SAASgC,KAAAA;cAC7BmB,iBAAiB,CAAC1C,UAAUa,aAAaU,OAAO,CAAC,CAACvB,KAAAA;;;QAP/CuB,GAAAA,KAAAA;MAYb,CAAA;MACCzB,OAAOoC,SAASI,qBAAiBtE,wBAACuE,OAAAA;QAAGS,SAASV,gBAAgBxC,OAAOoC;;;;AAG5E;AAKA,IAAMS,4BAA4B,CAACM,QACjCA,IACGC,QAAQ,MAAM,GACdvC,EAAAA,MAAM,GAAA,EACNtB,IAAI,CAAC8D,SAASA,KAAKC,OAAO,CAAA,EAAGC,YAAW,IAAKF,KAAKG,MAAM,CAAA,EAAGC,YAAW,CAAA,EACtEC,KAAK,GAAA;AAEV,IAAMC,SAAS;EAAEC,MAAMtG;EAAYuG,SAAS5E;EAAe6E,MAAMhE;EAAYiE,KAAKlC;AAAU;", "names": ["EventsRoot", "children", "formatMessage", "useIntl", "label", "id", "defaultMessage", "_jsxs", "Flex", "direction", "alignItems", "gap", "_jsx", "Field", "Label", "aria-hidden", "StyledTable", "aria-label", "styled", "Table", "theme", "colors", "neutral100", "neutral500", "spaces", "getCEHeaders", "headers", "EventsHeaders", "getHeaders", "<PERSON><PERSON>", "Tr", "Th", "VisuallyHidden", "map", "header", "includes", "title", "Typography", "variant", "textColor", "EventsBody", "providedEvents", "events", "getCEEvents", "value", "onChange", "useField", "inputName", "inputValue", "disabledEvents", "formattedValue", "reduce", "acc", "curr", "key", "split", "push", "handleSelect", "name", "set", "Set", "add", "delete", "Array", "from", "handleSelectAll", "for<PERSON>ach", "event", "Tbody", "Object", "entries", "EventsRow", "entryEvents", "entry", "media", "enabledCheckboxes", "filter", "hasSomeCheckboxSelected", "length", "areAllCheckboxesSelected", "onChangeAll", "valueToSet", "targetColumns", "Td", "Checkbox", "checked", "onCheckedChange", "removeHyphensAndTitleCase", "textAlign", "width", "justifyContent", "disabled", "colSpan", "str", "replace", "word", "char<PERSON>t", "toUpperCase", "slice", "toLowerCase", "join", "Events", "Root", "Headers", "Body", "Row"]}