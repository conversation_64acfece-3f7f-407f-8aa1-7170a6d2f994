import {
  Box,
  Flex,
  IconButton,
  SubNav,
  Typography
} from "./chunk-4YYUDJZ2.js";
import {
  NavLink
} from "./chunk-S65ZWNEO.js";
import {
  ForwardRef$1h,
  ForwardRef$4z
} from "./chunk-QW5ZDDTU.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  dt
} from "./chunk-3CQBCJ3G.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/admin/src/components/SubNav.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);
var Main = dt(SubNav)`
  background-color: ${({ theme }) => theme.colors.neutral0};
  border-right: 1px solid ${({ theme }) => theme.colors.neutral150};

  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    display: none;
  }
`;
var StyledLink = dt(NavLink)`
  display: flex;
  align-items: center;
  justify-content: space-between;
  text-decoration: none;
  height: 32px;

  color: ${({ theme }) => theme.colors.neutral800};

  &.active > div {
    ${({ theme }) => {
  return `
        background-color: ${theme.colors.primary100};
        color: ${theme.colors.primary700};
        font-weight: 500;
      `;
}}
  }

  &:hover.active > div {
    ${({ theme }) => {
  return `
        background-color: ${theme.colors.primary100};
      `;
}}
  }

  &:hover > div {
    ${({ theme }) => {
  return `
        background-color: ${theme.colors.neutral100};
      `;
}}
  }

  &:focus-visible {
    outline-offset: -2px;
  }
`;
var Link = (props) => {
  const { label, endAction, ...rest } = props;
  return (0, import_jsx_runtime.jsx)(StyledLink, {
    ...rest,
    children: (0, import_jsx_runtime.jsx)(Box, {
      width: "100%",
      paddingLeft: 3,
      paddingRight: 3,
      borderRadius: 1,
      children: (0, import_jsx_runtime.jsxs)(Flex, {
        justifyContent: "space-between",
        width: "100%",
        gap: 1,
        children: [
          (0, import_jsx_runtime.jsx)(Typography, {
            tag: "div",
            lineHeight: "32px",
            width: "100%",
            overflow: "hidden",
            style: {
              textOverflow: "ellipsis",
              whiteSpace: "nowrap"
            },
            children: label
          }),
          (0, import_jsx_runtime.jsx)(Flex, {
            gap: 2,
            children: endAction
          })
        ]
      })
    })
  });
};
var StyledHeader = dt(Box)`
  height: 56px;
  display: flex;
  align-items: center;
  padding-left: ${({ theme }) => theme.spaces[5]};
`;
var Header = ({ label }) => {
  return (0, import_jsx_runtime.jsx)(StyledHeader, {
    children: (0, import_jsx_runtime.jsx)(Typography, {
      variant: "beta",
      tag: "h2",
      children: label
    })
  });
};
var Sections = ({ children, ...props }) => {
  return (0, import_jsx_runtime.jsx)(Box, {
    paddingBottom: 4,
    children: (0, import_jsx_runtime.jsx)(Flex, {
      tag: "ol",
      gap: "5",
      direction: "column",
      alignItems: "stretch",
      ...props,
      children: children.map((child, index) => {
        return (0, import_jsx_runtime.jsx)("li", {
          children: child
        }, index);
      })
    })
  });
};
var Section = ({ label, children, link }) => {
  const listId = (0, import_react.useId)();
  return (0, import_jsx_runtime.jsxs)(Flex, {
    direction: "column",
    alignItems: "stretch",
    gap: 2,
    children: [
      (0, import_jsx_runtime.jsx)(Box, {
        paddingLeft: 5,
        paddingRight: 5,
        children: (0, import_jsx_runtime.jsxs)(Flex, {
          position: "relative",
          justifyContent: "space-between",
          children: [
            (0, import_jsx_runtime.jsx)(Flex, {
              children: (0, import_jsx_runtime.jsx)(Box, {
                children: (0, import_jsx_runtime.jsx)(Typography, {
                  variant: "sigma",
                  textColor: "neutral600",
                  children: label
                })
              })
            }),
            link && (0, import_jsx_runtime.jsx)(IconButton, {
              label: link.label,
              variant: "ghost",
              withTooltip: true,
              onClick: link.onClik,
              size: "XS",
              children: (0, import_jsx_runtime.jsx)(ForwardRef$1h, {})
            })
          ]
        })
      }),
      (0, import_jsx_runtime.jsx)(Flex, {
        tag: "ol",
        id: listId,
        direction: "column",
        gap: "2px",
        alignItems: "stretch",
        marginLeft: 2,
        marginRight: 2,
        children: children.map((child, index) => {
          return (0, import_jsx_runtime.jsx)("li", {
            children: child
          }, index);
        })
      })
    ]
  });
};
var SubSectionHeader = dt.button`
  cursor: pointer;
  width: 100%;
  border: none;
  padding: 0;
  background: transparent;
  display: flex;
  align-items: center;

  height: 32px;

  border-radius: ${({ theme }) => theme.borderRadius};

  padding-left: ${({ theme }) => theme.spaces[3]};
  padding-right: ${({ theme }) => theme.spaces[3]};
  padding-top: ${({ theme }) => theme.spaces[2]};
  padding-bottom: ${({ theme }) => theme.spaces[2]};

  &:hover {
    background-color: ${({ theme }) => theme.colors.neutral100};
  }
`;
var SubSectionLinkWrapper = dt.li`
  ${StyledLink} > div {
    padding-left: 36px;
  }
`;
var SubSection = ({ label, children }) => {
  const [isOpen, setOpenLinks] = (0, import_react.useState)(true);
  const listId = (0, import_react.useId)();
  const handleClick = () => {
    setOpenLinks((prev) => !prev);
  };
  return (0, import_jsx_runtime.jsxs)(Box, {
    children: [
      (0, import_jsx_runtime.jsx)(Flex, {
        justifyContent: "space-between",
        children: (0, import_jsx_runtime.jsxs)(SubSectionHeader, {
          onClick: handleClick,
          "aria-expanded": isOpen,
          "aria-controls": listId,
          children: [
            (0, import_jsx_runtime.jsx)(ForwardRef$4z, {
              "aria-hidden": true,
              fill: "neutral500",
              style: {
                transform: `rotate(${isOpen ? "0deg" : "-90deg"})`,
                transition: "transform 0.5s"
              }
            }),
            (0, import_jsx_runtime.jsx)(Box, {
              paddingLeft: 2,
              children: (0, import_jsx_runtime.jsx)(Typography, {
                tag: "span",
                fontWeight: "semiBold",
                textColor: "neutral800",
                children: label
              })
            })
          ]
        })
      }),
      (0, import_jsx_runtime.jsx)(Flex, {
        tag: "ul",
        id: listId,
        direction: "column",
        gap: "2px",
        alignItems: "stretch",
        style: {
          maxHeight: isOpen ? "1000px" : 0,
          overflow: "hidden",
          transition: isOpen ? "max-height 1s ease-in-out" : "max-height 0.5s cubic-bezier(0, 1, 0, 1)"
        },
        children: children.map((child, index) => {
          return (0, import_jsx_runtime.jsx)(SubSectionLinkWrapper, {
            children: child
          }, index);
        })
      })
    ]
  });
};
var SubNav2 = {
  Main,
  Header,
  Link,
  Sections,
  Section,
  SubSection
};

// node_modules/@strapi/admin/dist/admin/admin/src/constants.mjs
var ADMIN_PERMISSIONS_CE = {
  contentManager: {
    main: [],
    collectionTypesConfigurations: [
      {
        action: "plugin::content-manager.collection-types.configure-view",
        subject: null
      }
    ],
    componentsConfigurations: [
      {
        action: "plugin::content-manager.components.configure-layout",
        subject: null
      }
    ],
    singleTypesConfigurations: [
      {
        action: "plugin::content-manager.single-types.configure-view",
        subject: null
      }
    ]
  },
  marketplace: {
    main: [
      {
        action: "admin::marketplace.read",
        subject: null
      }
    ],
    read: [
      {
        action: "admin::marketplace.read",
        subject: null
      }
    ]
  },
  settings: {
    roles: {
      main: [
        {
          action: "admin::roles.create",
          subject: null
        },
        {
          action: "admin::roles.update",
          subject: null
        },
        {
          action: "admin::roles.read",
          subject: null
        },
        {
          action: "admin::roles.delete",
          subject: null
        }
      ],
      create: [
        {
          action: "admin::roles.create",
          subject: null
        }
      ],
      delete: [
        {
          action: "admin::roles.delete",
          subject: null
        }
      ],
      read: [
        {
          action: "admin::roles.read",
          subject: null
        }
      ],
      update: [
        {
          action: "admin::roles.update",
          subject: null
        }
      ]
    },
    users: {
      main: [
        {
          action: "admin::users.create",
          subject: null
        },
        {
          action: "admin::users.read",
          subject: null
        },
        {
          action: "admin::users.update",
          subject: null
        },
        {
          action: "admin::users.delete",
          subject: null
        }
      ],
      create: [
        {
          action: "admin::users.create",
          subject: null
        }
      ],
      delete: [
        {
          action: "admin::users.delete",
          subject: null
        }
      ],
      read: [
        {
          action: "admin::users.read",
          subject: null
        }
      ],
      update: [
        {
          action: "admin::users.update",
          subject: null
        }
      ]
    },
    webhooks: {
      main: [
        {
          action: "admin::webhooks.create",
          subject: null
        },
        {
          action: "admin::webhooks.read",
          subject: null
        },
        {
          action: "admin::webhooks.update",
          subject: null
        },
        {
          action: "admin::webhooks.delete",
          subject: null
        }
      ],
      create: [
        {
          action: "admin::webhooks.create",
          subject: null
        }
      ],
      delete: [
        {
          action: "admin::webhooks.delete",
          subject: null
        }
      ],
      read: [
        {
          action: "admin::webhooks.read",
          subject: null
        },
        // NOTE: We need to check with the API
        {
          action: "admin::webhooks.update",
          subject: null
        },
        {
          action: "admin::webhooks.delete",
          subject: null
        }
      ],
      update: [
        {
          action: "admin::webhooks.update",
          subject: null
        }
      ]
    },
    "api-tokens": {
      main: [
        {
          action: "admin::api-tokens.access",
          subject: null
        }
      ],
      create: [
        {
          action: "admin::api-tokens.create",
          subject: null
        }
      ],
      delete: [
        {
          action: "admin::api-tokens.delete",
          subject: null
        }
      ],
      read: [
        {
          action: "admin::api-tokens.read",
          subject: null
        }
      ],
      update: [
        {
          action: "admin::api-tokens.update",
          subject: null
        }
      ],
      regenerate: [
        {
          action: "admin::api-tokens.regenerate",
          subject: null
        }
      ]
    },
    "transfer-tokens": {
      main: [
        {
          action: "admin::transfer.tokens.access",
          subject: null
        }
      ],
      create: [
        {
          action: "admin::transfer.tokens.create",
          subject: null
        }
      ],
      delete: [
        {
          action: "admin::transfer.tokens.delete",
          subject: null
        }
      ],
      read: [
        {
          action: "admin::transfer.tokens.read",
          subject: null
        }
      ],
      update: [
        {
          action: "admin::transfer.tokens.update",
          subject: null
        }
      ],
      regenerate: [
        {
          action: "admin::transfer.tokens.regenerate",
          subject: null
        }
      ]
    },
    "project-settings": {
      read: [
        {
          action: "admin::project-settings.read",
          subject: null
        }
      ],
      update: [
        {
          action: "admin::project-settings.update",
          subject: null
        }
      ]
    },
    plugins: {
      main: [
        {
          action: "admin::marketplace.read",
          subject: null
        }
      ],
      read: [
        {
          action: "admin::marketplace.read",
          subject: null
        }
      ]
    }
  }
};
var HOOKS = {
  /**
  * Hook that allows to mutate the displayed headers of the list view table
  * @constant
  * @type {string}
  */
  INJECT_COLUMN_IN_TABLE: "Admin/CM/pages/ListView/inject-column-in-table",
  /**
  * Hook that allows to mutate the CM's collection types links pre-set filters
  * @constant
  * @type {string}
  */
  MUTATE_COLLECTION_TYPES_LINKS: "Admin/CM/pages/App/mutate-collection-types-links",
  /**
  * Hook that allows to mutate the CM's edit view layout
  * @constant
  * @type {string}
  */
  MUTATE_EDIT_VIEW_LAYOUT: "Admin/CM/pages/EditView/mutate-edit-view-layout",
  /**
  * Hook that allows to mutate the CM's single types links pre-set filters
  * @constant
  * @type {string}
  */
  MUTATE_SINGLE_TYPES_LINKS: "Admin/CM/pages/App/mutate-single-types-links"
};
var SETTINGS_LINKS_CE = () => {
  var _a, _b, _c, _d, _e, _f;
  return {
    global: [
      {
        intlLabel: {
          id: "Settings.application.title",
          defaultMessage: "Overview"
        },
        to: "/settings/application-infos",
        id: "000-application-infos"
      },
      {
        intlLabel: {
          id: "Settings.webhooks.title",
          defaultMessage: "Webhooks"
        },
        to: "/settings/webhooks",
        id: "webhooks"
      },
      {
        intlLabel: {
          id: "Settings.apiTokens.title",
          defaultMessage: "API Tokens"
        },
        to: "/settings/api-tokens?sort=name:ASC",
        id: "api-tokens"
      },
      {
        intlLabel: {
          id: "Settings.transferTokens.title",
          defaultMessage: "Transfer Tokens"
        },
        to: "/settings/transfer-tokens?sort=name:ASC",
        id: "transfer-tokens"
      },
      {
        intlLabel: {
          id: "global.plugins",
          defaultMessage: "Plugins"
        },
        to: "/settings/list-plugins",
        id: "plugins"
      },
      // If the Enterprise/Cloud feature is not enabled and if the config doesn't disable it, we promote the Enterprise/Cloud feature by displaying them in the settings menu.
      // Disable this by adding "promoteEE: false" to your `./config/admin.js` file
      ...!window.strapi.features.isEnabled(window.strapi.features.SSO) && ((_b = (_a = window.strapi) == null ? void 0 : _a.flags) == null ? void 0 : _b.promoteEE) ? [
        {
          intlLabel: {
            id: "Settings.sso.title",
            defaultMessage: "Single Sign-On"
          },
          to: "/settings/purchase-single-sign-on",
          id: "sso-purchase-page",
          licenseOnly: true
        }
      ] : [],
      ...!window.strapi.features.isEnabled("cms-content-history") && ((_d = (_c = window.strapi) == null ? void 0 : _c.flags) == null ? void 0 : _d.promoteEE) ? [
        {
          intlLabel: {
            id: "Settings.content-history.title",
            defaultMessage: "Content History"
          },
          to: "/settings/purchase-content-history",
          id: "content-history-purchase-page",
          licenseOnly: true
        }
      ] : []
    ],
    admin: [
      {
        intlLabel: {
          id: "global.roles",
          defaultMessage: "Roles"
        },
        to: "/settings/roles",
        id: "roles"
      },
      {
        intlLabel: {
          id: "global.users",
          defaultMessage: "Users"
        },
        // Init the search params directly
        to: "/settings/users?pageSize=10&page=1&sort=firstname",
        id: "users"
      },
      ...!window.strapi.features.isEnabled(window.strapi.features.AUDIT_LOGS) && ((_f = (_e = window.strapi) == null ? void 0 : _e.flags) == null ? void 0 : _f.promoteEE) ? [
        {
          intlLabel: {
            id: "global.auditLogs",
            defaultMessage: "Audit Logs"
          },
          to: "/settings/purchase-audit-logs",
          id: "auditLogs-purchase-page",
          licenseOnly: true
        }
      ] : []
    ]
  };
};

export {
  ADMIN_PERMISSIONS_CE,
  HOOKS,
  SETTINGS_LINKS_CE,
  SubNav2 as SubNav
};
//# sourceMappingURL=chunk-NG2C3VVD.js.map
