import {
  createSlice
} from "./chunk-IHIZNGTI.js";

// node_modules/@strapi/content-manager/dist/admin/modules/app.mjs
var initialState = {
  collectionTypeLinks: [],
  components: [],
  fieldSizes: {},
  models: [],
  singleTypeLinks: [],
  isLoading: true
};
var appSlice = createSlice({
  name: "app",
  initialState,
  reducers: {
    setInitialData(state, action) {
      const { authorizedCollectionTypeLinks, authorizedSingleTypeLinks, components, contentTypeSchemas, fieldSizes } = action.payload;
      state.collectionTypeLinks = authorizedCollectionTypeLinks.filter(({ isDisplayed }) => isDisplayed);
      state.singleTypeLinks = authorizedSingleTypeLinks.filter(({ isDisplayed }) => isDisplayed);
      state.components = components;
      state.models = contentTypeSchemas;
      state.fieldSizes = fieldSizes;
      state.isLoading = false;
    }
  }
});
var { actions, reducer } = appSlice;
var { setInitialData } = actions;

export {
  reducer,
  setInitialData
};
//# sourceMappingURL=chunk-OTW64HJY.js.map
