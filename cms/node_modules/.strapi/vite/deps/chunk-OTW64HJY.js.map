{"version": 3, "sources": ["../../../@strapi/content-manager/admin/src/modules/app.ts"], "sourcesContent": ["import { createSlice, type PayloadAction } from '@reduxjs/toolkit';\n\nimport { GetInitData } from '../../../shared/contracts/init';\n\nimport type { ContentManagerLink } from '../hooks/useContentManagerInitData';\n\ninterface AppState {\n  collectionTypeLinks: ContentManagerLink[];\n  components: GetInitData.Response['data']['components'];\n  fieldSizes: GetInitData.Response['data']['fieldSizes'];\n  models: GetInitData.Response['data']['contentTypes'];\n  singleTypeLinks: ContentManagerLink[];\n  isLoading: boolean;\n}\n\nconst initialState: AppState = {\n  collectionTypeLinks: [],\n  components: [],\n  fieldSizes: {},\n  models: [],\n  singleTypeLinks: [],\n  isLoading: true,\n};\n\nconst appSlice = createSlice({\n  name: 'app',\n  initialState,\n  reducers: {\n    setInitialData(\n      state,\n      action: PayloadAction<{\n        authorizedCollectionTypeLinks: AppState['collectionTypeLinks'];\n        authorizedSingleTypeLinks: AppState['singleTypeLinks'];\n        components: AppState['components'];\n        contentTypeSchemas: AppState['models'];\n        fieldSizes: AppState['fieldSizes'];\n      }>\n    ) {\n      const {\n        authorizedCollectionTypeLinks,\n        authorizedSingleTypeLinks,\n        components,\n        contentTypeSchemas,\n        fieldSizes,\n      } = action.payload;\n      state.collectionTypeLinks = authorizedCollectionTypeLinks.filter(\n        ({ isDisplayed }) => isDisplayed\n      );\n      state.singleTypeLinks = authorizedSingleTypeLinks.filter(({ isDisplayed }) => isDisplayed);\n      state.components = components;\n      state.models = contentTypeSchemas;\n      state.fieldSizes = fieldSizes;\n      state.isLoading = false;\n    },\n  },\n});\n\nconst { actions, reducer } = appSlice;\nconst { setInitialData } = actions;\n\nexport { reducer, setInitialData };\nexport type { AppState };\n"], "mappings": ";;;;;AAeA,IAAMA,eAAyB;EAC7BC,qBAAqB,CAAA;EACrBC,YAAY,CAAA;EACZC,YAAY,CAAA;EACZC,QAAQ,CAAA;EACRC,iBAAiB,CAAA;EACjBC,WAAW;AACb;AAEA,IAAMC,WAAWC,YAAY;EAC3BC,MAAM;EACNT;EACAU,UAAU;IACRC,eACEC,OACAC,QAME;AAEF,YAAM,EACJC,+BACAC,2BACAb,YACAc,oBACAb,WAAU,IACRU,OAAOI;AACXL,YAAMX,sBAAsBa,8BAA8BI,OACxD,CAAC,EAAEC,YAAW,MAAOA,WAAAA;AAEvBP,YAAMP,kBAAkBU,0BAA0BG,OAAO,CAAC,EAAEC,YAAW,MAAOA,WAAAA;AAC9EP,YAAMV,aAAaA;AACnBU,YAAMR,SAASY;AACfJ,YAAMT,aAAaA;AACnBS,YAAMN,YAAY;IACpB;EACF;AACF,CAAA;AAEA,IAAM,EAAEc,SAASC,QAAO,IAAKd;AACvB,IAAA,EAAEI,eAAc,IAAKS;", "names": ["initialState", "collectionTypeLinks", "components", "fieldSizes", "models", "singleTypeLinks", "isLoading", "appSlice", "createSlice", "name", "reducers", "setInitialData", "state", "action", "authorizedCollectionTypeLinks", "authorizedSingleTypeLinks", "contentTypeSchemas", "payload", "filter", "isDisplayed", "actions", "reducer"]}