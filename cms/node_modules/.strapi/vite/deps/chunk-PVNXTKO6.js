// node_modules/@strapi/admin/dist/admin/admin/src/utils/users.mjs
var getDisplayName = ({ firstname, lastname, username, email } = {}) => {
  if (username) {
    return username;
  }
  if (firstname) {
    return `${firstname} ${lastname ?? ""}`.trim();
  }
  return email ?? "";
};
var hashAdminUserEmail = async (payload) => {
  if (!payload || !payload.email) {
    return null;
  }
  try {
    return await digestMessage(payload.email);
  } catch (error) {
    return null;
  }
};
var bufferToHex = (buffer) => {
  return [
    ...new Uint8Array(buffer)
  ].map((b) => b.toString(16).padStart(2, "0")).join("");
};
var digestMessage = async (message) => {
  const msgUint8 = new TextEncoder().encode(message);
  const hashBuffer = await crypto.subtle.digest("SHA-256", msgUint8);
  return bufferToHex(hashBuffer);
};

export {
  getDisplayName,
  hashAdminUserEmail
};
//# sourceMappingURL=chunk-PVNXTKO6.js.map
