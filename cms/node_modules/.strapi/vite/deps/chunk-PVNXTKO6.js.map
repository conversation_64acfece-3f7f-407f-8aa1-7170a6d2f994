{"version": 3, "sources": ["../../../@strapi/admin/admin/src/utils/users.ts"], "sourcesContent": ["import type { User } from '../features/Auth';\n\n/* -------------------------------------------------------------------------------------------------\n * getDisplayName\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * Retrieves the display name of an admin panel user\n */\nconst getDisplayName = ({ firstname, lastname, username, email }: Partial<User> = {}): string => {\n  if (username) {\n    return username;\n  }\n\n  // firstname is not required if the user is created with a username\n  if (firstname) {\n    return `${firstname} ${lastname ?? ''}`.trim();\n  }\n\n  return email ?? '';\n};\n\n/* -------------------------------------------------------------------------------------------------\n * hashAdminUserEmail\n * -----------------------------------------------------------------------------------------------*/\n\nconst hashAdminUserEmail = async (payload?: User) => {\n  if (!payload || !payload.email) {\n    return null;\n  }\n\n  try {\n    return await digestMessage(payload.email);\n  } catch (error) {\n    return null;\n  }\n};\n\nconst bufferToHex = (buffer: ArrayBuffer) => {\n  return [...new Uint8Array(buffer)].map((b) => b.toString(16).padStart(2, '0')).join('');\n};\nconst digestMessage = async (message: string) => {\n  const msgUint8 = new TextEncoder().encode(message);\n  const hashBuffer = await crypto.subtle.digest('SHA-256', msgUint8);\n\n  return bufferToHex(hashBuffer);\n};\n\nexport { getDisplayName, hashAdminUserEmail };\n"], "mappings": ";AASMA,IAAAA,iBAAiB,CAAC,EAAEC,WAAWC,UAAUC,UAAUC,MAAK,IAAoB,CAAA,MAAE;AAClF,MAAID,UAAU;AACZ,WAAOA;EACT;AAGA,MAAIF,WAAW;AACb,WAAO,GAAGA,SAAU,IAAGC,YAAY,EAAA,GAAKG,KAAI;EAC9C;AAEA,SAAOD,SAAS;AAClB;AAMA,IAAME,qBAAqB,OAAOC,YAAAA;AAChC,MAAI,CAACA,WAAW,CAACA,QAAQH,OAAO;AAC9B,WAAO;EACT;AAEA,MAAI;AACF,WAAO,MAAMI,cAAcD,QAAQH,KAAK;EAC1C,SAASK,OAAO;AACd,WAAO;EACT;AACF;AAEA,IAAMC,cAAc,CAACC,WAAAA;AACnB,SAAO;IAAI,GAAA,IAAIC,WAAWD,MAAAA;EAAQ,EAACE,IAAI,CAACC,MAAMA,EAAEC,SAAS,EAAA,EAAIC,SAAS,GAAG,GAAA,CAAA,EAAMC,KAAK,EAAA;AACtF;AACA,IAAMT,gBAAgB,OAAOU,YAAAA;AAC3B,QAAMC,WAAW,IAAIC,YAAcC,EAAAA,OAAOH,OAAAA;AAC1C,QAAMI,aAAa,MAAMC,OAAOC,OAAOC,OAAO,WAAWN,QAAAA;AAEzD,SAAOT,YAAYY,UAAAA;AACrB;", "names": ["getDisplayName", "firstname", "lastname", "username", "email", "trim", "hashAdminUserEmail", "payload", "digestMessage", "error", "bufferToHex", "buffer", "Uint8Array", "map", "b", "toString", "padStart", "join", "message", "msgUint8", "TextEncoder", "encode", "hash<PERSON><PERSON><PERSON>", "crypto", "subtle", "digest"]}