import {
  buildValidParams as buildValidParams2,
  getStageColorByHex
} from "./chunk-TGSITB5G.js";
import {
  PERMISSIONS as PERMISSIONS5,
  ReleaseActionMenu,
  ReleaseActionOptions,
  index,
  pluginId as pluginId4
} from "./chunk-DSW7EJ54.js";
import {
  getTimezoneOffset,
  useCreateManyReleaseActionsMutation,
  useCreateReleaseActionMutation,
  useGetMappedEntriesInReleasesQuery,
  useGetReleasesForEntryQuery,
  useGetReleasesQuery
} from "./chunk-7KXIIYJL.js";
import {
  CHARGEBEE_STAGES_PER_WORKFLOW_ENTITLEMENT_NAME,
  CHARGEBEE_WORKFLOW_ENTITLEMENT_NAME,
  FEATURE_ID,
  LimitsModal,
  PLUGIN_ID,
  STAGE_COLOR_DEFAULT,
  useGetStagesQuery,
  useUpdateAssigneeMutation,
  useUpdateStageMutation
} from "./chunk-HXNPIIH5.js";
import {
  useTypedSelector
} from "./chunk-HS4GSASO.js";
import {
  PERMISSIONS as PERMISSIONS4,
  getTranslation,
  i18nApi,
  pluginId as pluginId3,
  useGetLocalesQuery
} from "./chunk-7AJEWENI.js";
import {
  AssetGridList,
  AudioPreview,
  Breadcrumbs,
  CopyLinkButton,
  EditAssetContent,
  EditAssetDialog,
  EditFolderContent,
  EditFolderDialog,
  EmptyAssets,
  FilterList,
  FilterPopover,
  FolderCard,
  FolderCardBody,
  FolderCardBodyAction,
  FolderGridList,
  SortPicker,
  TableList,
  UploadAssetDialog,
  VideoPreview,
  containsAssetFilter,
  createAssetUrl,
  displayedFilters,
  prefixFileUrlWithBackendUrl,
  rawFileToAsset,
  useAssets,
  useFolder,
  useFolders,
  useMediaLibraryPermissions,
  usePersistentState,
  useSelectionState
} from "./chunk-KODIUVGC.js";
import {
  useConfig
} from "./chunk-UCNCKMDM.js";
import {
  AssetSource,
  AssetType,
  PERMISSIONS as PERMISSIONS3,
  getTrad,
  localStorageKeys,
  pluginId as pluginId2,
  pluginPkg,
  require_lib,
  viewOptions
} from "./chunk-LR4RNYDQ.js";
import {
  PERMISSIONS as PERMISSIONS2
} from "./chunk-PDGPTUUZ.js";
import {
  useDocumentActions
} from "./chunk-AGGTEVCI.js";
import {
  buildValidParams,
  useContentManagerContext,
  useDocument,
  useDocumentLayout
} from "./chunk-LIDWYKFI.js";
import {
  PERMISSIONS,
  reducer,
  reducer2
} from "./chunk-V33MIUVA.js";
import {
  pluginId
} from "./chunk-H4GPAAVJ.js";
import {
  renderAdmin
} from "./chunk-DDRVMFUU.js";
import {
  useLicenseLimits
} from "./chunk-44X4AIYR.js";
import {
  require_omit
} from "./chunk-X26IT6UA.js";
import {
  require_cloneDeep
} from "./chunk-GGK2TLCV.js";
import {
  Form,
  Formik,
  useFormik
} from "./chunk-BJKYWYYT.js";
import {
  create3 as create,
  create4 as create2,
  create6 as create3,
  require_has
} from "./chunk-4WX3TY7V.js";
import {
  useTracking
} from "./chunk-USP7YHPW.js";
import {
  Table,
  useTable
} from "./chunk-6EESFY62.js";
import {
  useField,
  useForm
} from "./chunk-VWIQ6S3Y.js";
import {
  useRBAC
} from "./chunk-EYSFNMMY.js";
import {
  useAdminUsers
} from "./chunk-TLFBIKP6.js";
import {
  ForwardRef$J,
  Page,
  useAPIErrorHandler
} from "./chunk-3UVBSCPH.js";
import {
  useAuth,
  useQueryParams
} from "./chunk-JXTOCMQW.js";
import {
  isFetchError,
  require_get,
  require_side_channel,
  skipToken
} from "./chunk-XU64YSEO.js";
import {
  useNotification
} from "./chunk-4EY5FDQG.js";
import {
  Badge,
  Box,
  Button,
  CarouselActions,
  CarouselInput,
  CarouselSlide,
  CheckboxImpl,
  Combobox,
  Dialog,
  Divider,
  EmptyStateLayout,
  Field,
  Flex,
  Grid,
  IconButton,
  Link as Link2,
  LinkButton,
  Loader,
  Modal,
  Option,
  Popover,
  SearchForm,
  Searchbar,
  SingleSelect,
  SingleSelectOption,
  Status,
  Tabs,
  TooltipImpl,
  Typography,
  VisuallyHidden,
  useCollator,
  useIntl
} from "./chunk-4YYUDJZ2.js";
import {
  Link,
  matchPath,
  useNavigate,
  useParams
} from "./chunk-S65ZWNEO.js";
import {
  ForwardRef$1B,
  ForwardRef$1f,
  ForwardRef$1h,
  ForwardRef$1v,
  ForwardRef$2J,
  ForwardRef$2n,
  ForwardRef$2p,
  ForwardRef$2x,
  ForwardRef$3,
  ForwardRef$39,
  ForwardRef$3P,
  ForwardRef$3j,
  ForwardRef$3p,
  ForwardRef$3v,
  ForwardRef$43,
  ForwardRef$45,
  ForwardRef$4D,
  ForwardRef$4T,
  ForwardRef$4v,
  ForwardRef$4x,
  ForwardRef$59,
  ForwardRef$X,
  ForwardRef$j
} from "./chunk-QW5ZDDTU.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  dt,
  lt
} from "./chunk-3CQBCJ3G.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __commonJS,
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/i18n/node_modules/qs/lib/formats.js
var require_formats = __commonJS({
  "node_modules/@strapi/i18n/node_modules/qs/lib/formats.js"(exports, module) {
    "use strict";
    var replace = String.prototype.replace;
    var percentTwenties = /%20/g;
    var Format = {
      RFC1738: "RFC1738",
      RFC3986: "RFC3986"
    };
    module.exports = {
      "default": Format.RFC3986,
      formatters: {
        RFC1738: function(value) {
          return replace.call(value, percentTwenties, "+");
        },
        RFC3986: function(value) {
          return String(value);
        }
      },
      RFC1738: Format.RFC1738,
      RFC3986: Format.RFC3986
    };
  }
});

// node_modules/@strapi/i18n/node_modules/qs/lib/utils.js
var require_utils = __commonJS({
  "node_modules/@strapi/i18n/node_modules/qs/lib/utils.js"(exports, module) {
    "use strict";
    var formats = require_formats();
    var has2 = Object.prototype.hasOwnProperty;
    var isArray = Array.isArray;
    var hexTable = function() {
      var array = [];
      for (var i = 0; i < 256; ++i) {
        array.push("%" + ((i < 16 ? "0" : "") + i.toString(16)).toUpperCase());
      }
      return array;
    }();
    var compactQueue = function compactQueue2(queue) {
      while (queue.length > 1) {
        var item = queue.pop();
        var obj = item.obj[item.prop];
        if (isArray(obj)) {
          var compacted = [];
          for (var j = 0; j < obj.length; ++j) {
            if (typeof obj[j] !== "undefined") {
              compacted.push(obj[j]);
            }
          }
          item.obj[item.prop] = compacted;
        }
      }
    };
    var arrayToObject = function arrayToObject2(source, options) {
      var obj = options && options.plainObjects ? /* @__PURE__ */ Object.create(null) : {};
      for (var i = 0; i < source.length; ++i) {
        if (typeof source[i] !== "undefined") {
          obj[i] = source[i];
        }
      }
      return obj;
    };
    var merge = function merge2(target, source, options) {
      if (!source) {
        return target;
      }
      if (typeof source !== "object") {
        if (isArray(target)) {
          target.push(source);
        } else if (target && typeof target === "object") {
          if (options && (options.plainObjects || options.allowPrototypes) || !has2.call(Object.prototype, source)) {
            target[source] = true;
          }
        } else {
          return [target, source];
        }
        return target;
      }
      if (!target || typeof target !== "object") {
        return [target].concat(source);
      }
      var mergeTarget = target;
      if (isArray(target) && !isArray(source)) {
        mergeTarget = arrayToObject(target, options);
      }
      if (isArray(target) && isArray(source)) {
        source.forEach(function(item, i) {
          if (has2.call(target, i)) {
            var targetItem = target[i];
            if (targetItem && typeof targetItem === "object" && item && typeof item === "object") {
              target[i] = merge2(targetItem, item, options);
            } else {
              target.push(item);
            }
          } else {
            target[i] = item;
          }
        });
        return target;
      }
      return Object.keys(source).reduce(function(acc, key) {
        var value = source[key];
        if (has2.call(acc, key)) {
          acc[key] = merge2(acc[key], value, options);
        } else {
          acc[key] = value;
        }
        return acc;
      }, mergeTarget);
    };
    var assign = function assignSingleSource(target, source) {
      return Object.keys(source).reduce(function(acc, key) {
        acc[key] = source[key];
        return acc;
      }, target);
    };
    var decode = function(str, decoder, charset) {
      var strWithoutPlus = str.replace(/\+/g, " ");
      if (charset === "iso-8859-1") {
        return strWithoutPlus.replace(/%[0-9a-f]{2}/gi, unescape);
      }
      try {
        return decodeURIComponent(strWithoutPlus);
      } catch (e) {
        return strWithoutPlus;
      }
    };
    var encode = function encode2(str, defaultEncoder, charset, kind, format) {
      if (str.length === 0) {
        return str;
      }
      var string = str;
      if (typeof str === "symbol") {
        string = Symbol.prototype.toString.call(str);
      } else if (typeof str !== "string") {
        string = String(str);
      }
      if (charset === "iso-8859-1") {
        return escape(string).replace(/%u[0-9a-f]{4}/gi, function($0) {
          return "%26%23" + parseInt($0.slice(2), 16) + "%3B";
        });
      }
      var out = "";
      for (var i = 0; i < string.length; ++i) {
        var c = string.charCodeAt(i);
        if (c === 45 || c === 46 || c === 95 || c === 126 || c >= 48 && c <= 57 || c >= 65 && c <= 90 || c >= 97 && c <= 122 || format === formats.RFC1738 && (c === 40 || c === 41)) {
          out += string.charAt(i);
          continue;
        }
        if (c < 128) {
          out = out + hexTable[c];
          continue;
        }
        if (c < 2048) {
          out = out + (hexTable[192 | c >> 6] + hexTable[128 | c & 63]);
          continue;
        }
        if (c < 55296 || c >= 57344) {
          out = out + (hexTable[224 | c >> 12] + hexTable[128 | c >> 6 & 63] + hexTable[128 | c & 63]);
          continue;
        }
        i += 1;
        c = 65536 + ((c & 1023) << 10 | string.charCodeAt(i) & 1023);
        out += hexTable[240 | c >> 18] + hexTable[128 | c >> 12 & 63] + hexTable[128 | c >> 6 & 63] + hexTable[128 | c & 63];
      }
      return out;
    };
    var compact = function compact2(value) {
      var queue = [{ obj: { o: value }, prop: "o" }];
      var refs = [];
      for (var i = 0; i < queue.length; ++i) {
        var item = queue[i];
        var obj = item.obj[item.prop];
        var keys = Object.keys(obj);
        for (var j = 0; j < keys.length; ++j) {
          var key = keys[j];
          var val = obj[key];
          if (typeof val === "object" && val !== null && refs.indexOf(val) === -1) {
            queue.push({ obj, prop: key });
            refs.push(val);
          }
        }
      }
      compactQueue(queue);
      return value;
    };
    var isRegExp = function isRegExp2(obj) {
      return Object.prototype.toString.call(obj) === "[object RegExp]";
    };
    var isBuffer = function isBuffer2(obj) {
      if (!obj || typeof obj !== "object") {
        return false;
      }
      return !!(obj.constructor && obj.constructor.isBuffer && obj.constructor.isBuffer(obj));
    };
    var combine = function combine2(a, b) {
      return [].concat(a, b);
    };
    var maybeMap = function maybeMap2(val, fn) {
      if (isArray(val)) {
        var mapped = [];
        for (var i = 0; i < val.length; i += 1) {
          mapped.push(fn(val[i]));
        }
        return mapped;
      }
      return fn(val);
    };
    module.exports = {
      arrayToObject,
      assign,
      combine,
      compact,
      decode,
      encode,
      isBuffer,
      isRegExp,
      maybeMap,
      merge
    };
  }
});

// node_modules/@strapi/i18n/node_modules/qs/lib/stringify.js
var require_stringify = __commonJS({
  "node_modules/@strapi/i18n/node_modules/qs/lib/stringify.js"(exports, module) {
    "use strict";
    var getSideChannel = require_side_channel();
    var utils = require_utils();
    var formats = require_formats();
    var has2 = Object.prototype.hasOwnProperty;
    var arrayPrefixGenerators = {
      brackets: function brackets(prefix) {
        return prefix + "[]";
      },
      comma: "comma",
      indices: function indices(prefix, key) {
        return prefix + "[" + key + "]";
      },
      repeat: function repeat(prefix) {
        return prefix;
      }
    };
    var isArray = Array.isArray;
    var push = Array.prototype.push;
    var pushToArray = function(arr, valueOrArray) {
      push.apply(arr, isArray(valueOrArray) ? valueOrArray : [valueOrArray]);
    };
    var toISO = Date.prototype.toISOString;
    var defaultFormat = formats["default"];
    var defaults = {
      addQueryPrefix: false,
      allowDots: false,
      charset: "utf-8",
      charsetSentinel: false,
      delimiter: "&",
      encode: true,
      encoder: utils.encode,
      encodeValuesOnly: false,
      format: defaultFormat,
      formatter: formats.formatters[defaultFormat],
      // deprecated
      indices: false,
      serializeDate: function serializeDate(date) {
        return toISO.call(date);
      },
      skipNulls: false,
      strictNullHandling: false
    };
    var isNonNullishPrimitive = function isNonNullishPrimitive2(v) {
      return typeof v === "string" || typeof v === "number" || typeof v === "boolean" || typeof v === "symbol" || typeof v === "bigint";
    };
    var sentinel = {};
    var stringify3 = function stringify4(object, prefix, generateArrayPrefix, commaRoundTrip, strictNullHandling, skipNulls, encoder, filter, sort, allowDots, serializeDate, format, formatter, encodeValuesOnly, charset, sideChannel) {
      var obj = object;
      var tmpSc = sideChannel;
      var step = 0;
      var findFlag = false;
      while ((tmpSc = tmpSc.get(sentinel)) !== void 0 && !findFlag) {
        var pos = tmpSc.get(object);
        step += 1;
        if (typeof pos !== "undefined") {
          if (pos === step) {
            throw new RangeError("Cyclic object value");
          } else {
            findFlag = true;
          }
        }
        if (typeof tmpSc.get(sentinel) === "undefined") {
          step = 0;
        }
      }
      if (typeof filter === "function") {
        obj = filter(prefix, obj);
      } else if (obj instanceof Date) {
        obj = serializeDate(obj);
      } else if (generateArrayPrefix === "comma" && isArray(obj)) {
        obj = utils.maybeMap(obj, function(value2) {
          if (value2 instanceof Date) {
            return serializeDate(value2);
          }
          return value2;
        });
      }
      if (obj === null) {
        if (strictNullHandling) {
          return encoder && !encodeValuesOnly ? encoder(prefix, defaults.encoder, charset, "key", format) : prefix;
        }
        obj = "";
      }
      if (isNonNullishPrimitive(obj) || utils.isBuffer(obj)) {
        if (encoder) {
          var keyValue = encodeValuesOnly ? prefix : encoder(prefix, defaults.encoder, charset, "key", format);
          return [formatter(keyValue) + "=" + formatter(encoder(obj, defaults.encoder, charset, "value", format))];
        }
        return [formatter(prefix) + "=" + formatter(String(obj))];
      }
      var values = [];
      if (typeof obj === "undefined") {
        return values;
      }
      var objKeys;
      if (generateArrayPrefix === "comma" && isArray(obj)) {
        if (encodeValuesOnly && encoder) {
          obj = utils.maybeMap(obj, encoder);
        }
        objKeys = [{ value: obj.length > 0 ? obj.join(",") || null : void 0 }];
      } else if (isArray(filter)) {
        objKeys = filter;
      } else {
        var keys = Object.keys(obj);
        objKeys = sort ? keys.sort(sort) : keys;
      }
      var adjustedPrefix = commaRoundTrip && isArray(obj) && obj.length === 1 ? prefix + "[]" : prefix;
      for (var j = 0; j < objKeys.length; ++j) {
        var key = objKeys[j];
        var value = typeof key === "object" && typeof key.value !== "undefined" ? key.value : obj[key];
        if (skipNulls && value === null) {
          continue;
        }
        var keyPrefix = isArray(obj) ? typeof generateArrayPrefix === "function" ? generateArrayPrefix(adjustedPrefix, key) : adjustedPrefix : adjustedPrefix + (allowDots ? "." + key : "[" + key + "]");
        sideChannel.set(object, step);
        var valueSideChannel = getSideChannel();
        valueSideChannel.set(sentinel, sideChannel);
        pushToArray(values, stringify4(
          value,
          keyPrefix,
          generateArrayPrefix,
          commaRoundTrip,
          strictNullHandling,
          skipNulls,
          generateArrayPrefix === "comma" && encodeValuesOnly && isArray(obj) ? null : encoder,
          filter,
          sort,
          allowDots,
          serializeDate,
          format,
          formatter,
          encodeValuesOnly,
          charset,
          valueSideChannel
        ));
      }
      return values;
    };
    var normalizeStringifyOptions = function normalizeStringifyOptions2(opts) {
      if (!opts) {
        return defaults;
      }
      if (opts.encoder !== null && typeof opts.encoder !== "undefined" && typeof opts.encoder !== "function") {
        throw new TypeError("Encoder has to be a function.");
      }
      var charset = opts.charset || defaults.charset;
      if (typeof opts.charset !== "undefined" && opts.charset !== "utf-8" && opts.charset !== "iso-8859-1") {
        throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");
      }
      var format = formats["default"];
      if (typeof opts.format !== "undefined") {
        if (!has2.call(formats.formatters, opts.format)) {
          throw new TypeError("Unknown format option provided.");
        }
        format = opts.format;
      }
      var formatter = formats.formatters[format];
      var filter = defaults.filter;
      if (typeof opts.filter === "function" || isArray(opts.filter)) {
        filter = opts.filter;
      }
      return {
        addQueryPrefix: typeof opts.addQueryPrefix === "boolean" ? opts.addQueryPrefix : defaults.addQueryPrefix,
        allowDots: typeof opts.allowDots === "undefined" ? defaults.allowDots : !!opts.allowDots,
        charset,
        charsetSentinel: typeof opts.charsetSentinel === "boolean" ? opts.charsetSentinel : defaults.charsetSentinel,
        delimiter: typeof opts.delimiter === "undefined" ? defaults.delimiter : opts.delimiter,
        encode: typeof opts.encode === "boolean" ? opts.encode : defaults.encode,
        encoder: typeof opts.encoder === "function" ? opts.encoder : defaults.encoder,
        encodeValuesOnly: typeof opts.encodeValuesOnly === "boolean" ? opts.encodeValuesOnly : defaults.encodeValuesOnly,
        filter,
        format,
        formatter,
        serializeDate: typeof opts.serializeDate === "function" ? opts.serializeDate : defaults.serializeDate,
        skipNulls: typeof opts.skipNulls === "boolean" ? opts.skipNulls : defaults.skipNulls,
        sort: typeof opts.sort === "function" ? opts.sort : null,
        strictNullHandling: typeof opts.strictNullHandling === "boolean" ? opts.strictNullHandling : defaults.strictNullHandling
      };
    };
    module.exports = function(object, opts) {
      var obj = object;
      var options = normalizeStringifyOptions(opts);
      var objKeys;
      var filter;
      if (typeof options.filter === "function") {
        filter = options.filter;
        obj = filter("", obj);
      } else if (isArray(options.filter)) {
        filter = options.filter;
        objKeys = filter;
      }
      var keys = [];
      if (typeof obj !== "object" || obj === null) {
        return "";
      }
      var arrayFormat;
      if (opts && opts.arrayFormat in arrayPrefixGenerators) {
        arrayFormat = opts.arrayFormat;
      } else if (opts && "indices" in opts) {
        arrayFormat = opts.indices ? "indices" : "repeat";
      } else {
        arrayFormat = "indices";
      }
      var generateArrayPrefix = arrayPrefixGenerators[arrayFormat];
      if (opts && "commaRoundTrip" in opts && typeof opts.commaRoundTrip !== "boolean") {
        throw new TypeError("`commaRoundTrip` must be a boolean, or absent");
      }
      var commaRoundTrip = generateArrayPrefix === "comma" && opts && opts.commaRoundTrip;
      if (!objKeys) {
        objKeys = Object.keys(obj);
      }
      if (options.sort) {
        objKeys.sort(options.sort);
      }
      var sideChannel = getSideChannel();
      for (var i = 0; i < objKeys.length; ++i) {
        var key = objKeys[i];
        if (options.skipNulls && obj[key] === null) {
          continue;
        }
        pushToArray(keys, stringify3(
          obj[key],
          key,
          generateArrayPrefix,
          commaRoundTrip,
          options.strictNullHandling,
          options.skipNulls,
          options.encode ? options.encoder : null,
          options.filter,
          options.sort,
          options.allowDots,
          options.serializeDate,
          options.format,
          options.formatter,
          options.encodeValuesOnly,
          options.charset,
          sideChannel
        ));
      }
      var joined = keys.join(options.delimiter);
      var prefix = options.addQueryPrefix === true ? "?" : "";
      if (options.charsetSentinel) {
        if (options.charset === "iso-8859-1") {
          prefix += "utf8=%26%2310003%3B&";
        } else {
          prefix += "utf8=%E2%9C%93&";
        }
      }
      return joined.length > 0 ? prefix + joined : "";
    };
  }
});

// node_modules/@strapi/i18n/node_modules/qs/lib/parse.js
var require_parse = __commonJS({
  "node_modules/@strapi/i18n/node_modules/qs/lib/parse.js"(exports, module) {
    "use strict";
    var utils = require_utils();
    var has2 = Object.prototype.hasOwnProperty;
    var isArray = Array.isArray;
    var defaults = {
      allowDots: false,
      allowPrototypes: false,
      allowSparse: false,
      arrayLimit: 20,
      charset: "utf-8",
      charsetSentinel: false,
      comma: false,
      decoder: utils.decode,
      delimiter: "&",
      depth: 5,
      ignoreQueryPrefix: false,
      interpretNumericEntities: false,
      parameterLimit: 1e3,
      parseArrays: true,
      plainObjects: false,
      strictNullHandling: false
    };
    var interpretNumericEntities = function(str) {
      return str.replace(/&#(\d+);/g, function($0, numberStr) {
        return String.fromCharCode(parseInt(numberStr, 10));
      });
    };
    var parseArrayValue = function(val, options) {
      if (val && typeof val === "string" && options.comma && val.indexOf(",") > -1) {
        return val.split(",");
      }
      return val;
    };
    var isoSentinel = "utf8=%26%2310003%3B";
    var charsetSentinel = "utf8=%E2%9C%93";
    var parseValues = function parseQueryStringValues(str, options) {
      var obj = {};
      var cleanStr = options.ignoreQueryPrefix ? str.replace(/^\?/, "") : str;
      var limit = options.parameterLimit === Infinity ? void 0 : options.parameterLimit;
      var parts = cleanStr.split(options.delimiter, limit);
      var skipIndex = -1;
      var i;
      var charset = options.charset;
      if (options.charsetSentinel) {
        for (i = 0; i < parts.length; ++i) {
          if (parts[i].indexOf("utf8=") === 0) {
            if (parts[i] === charsetSentinel) {
              charset = "utf-8";
            } else if (parts[i] === isoSentinel) {
              charset = "iso-8859-1";
            }
            skipIndex = i;
            i = parts.length;
          }
        }
      }
      for (i = 0; i < parts.length; ++i) {
        if (i === skipIndex) {
          continue;
        }
        var part = parts[i];
        var bracketEqualsPos = part.indexOf("]=");
        var pos = bracketEqualsPos === -1 ? part.indexOf("=") : bracketEqualsPos + 1;
        var key, val;
        if (pos === -1) {
          key = options.decoder(part, defaults.decoder, charset, "key");
          val = options.strictNullHandling ? null : "";
        } else {
          key = options.decoder(part.slice(0, pos), defaults.decoder, charset, "key");
          val = utils.maybeMap(
            parseArrayValue(part.slice(pos + 1), options),
            function(encodedVal) {
              return options.decoder(encodedVal, defaults.decoder, charset, "value");
            }
          );
        }
        if (val && options.interpretNumericEntities && charset === "iso-8859-1") {
          val = interpretNumericEntities(val);
        }
        if (part.indexOf("[]=") > -1) {
          val = isArray(val) ? [val] : val;
        }
        if (has2.call(obj, key)) {
          obj[key] = utils.combine(obj[key], val);
        } else {
          obj[key] = val;
        }
      }
      return obj;
    };
    var parseObject = function(chain, val, options, valuesParsed) {
      var leaf = valuesParsed ? val : parseArrayValue(val, options);
      for (var i = chain.length - 1; i >= 0; --i) {
        var obj;
        var root = chain[i];
        if (root === "[]" && options.parseArrays) {
          obj = [].concat(leaf);
        } else {
          obj = options.plainObjects ? /* @__PURE__ */ Object.create(null) : {};
          var cleanRoot = root.charAt(0) === "[" && root.charAt(root.length - 1) === "]" ? root.slice(1, -1) : root;
          var index4 = parseInt(cleanRoot, 10);
          if (!options.parseArrays && cleanRoot === "") {
            obj = { 0: leaf };
          } else if (!isNaN(index4) && root !== cleanRoot && String(index4) === cleanRoot && index4 >= 0 && (options.parseArrays && index4 <= options.arrayLimit)) {
            obj = [];
            obj[index4] = leaf;
          } else if (cleanRoot !== "__proto__") {
            obj[cleanRoot] = leaf;
          }
        }
        leaf = obj;
      }
      return leaf;
    };
    var parseKeys = function parseQueryStringKeys(givenKey, val, options, valuesParsed) {
      if (!givenKey) {
        return;
      }
      var key = options.allowDots ? givenKey.replace(/\.([^.[]+)/g, "[$1]") : givenKey;
      var brackets = /(\[[^[\]]*])/;
      var child = /(\[[^[\]]*])/g;
      var segment = options.depth > 0 && brackets.exec(key);
      var parent = segment ? key.slice(0, segment.index) : key;
      var keys = [];
      if (parent) {
        if (!options.plainObjects && has2.call(Object.prototype, parent)) {
          if (!options.allowPrototypes) {
            return;
          }
        }
        keys.push(parent);
      }
      var i = 0;
      while (options.depth > 0 && (segment = child.exec(key)) !== null && i < options.depth) {
        i += 1;
        if (!options.plainObjects && has2.call(Object.prototype, segment[1].slice(1, -1))) {
          if (!options.allowPrototypes) {
            return;
          }
        }
        keys.push(segment[1]);
      }
      if (segment) {
        keys.push("[" + key.slice(segment.index) + "]");
      }
      return parseObject(keys, val, options, valuesParsed);
    };
    var normalizeParseOptions = function normalizeParseOptions2(opts) {
      if (!opts) {
        return defaults;
      }
      if (opts.decoder !== null && opts.decoder !== void 0 && typeof opts.decoder !== "function") {
        throw new TypeError("Decoder has to be a function.");
      }
      if (typeof opts.charset !== "undefined" && opts.charset !== "utf-8" && opts.charset !== "iso-8859-1") {
        throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");
      }
      var charset = typeof opts.charset === "undefined" ? defaults.charset : opts.charset;
      return {
        allowDots: typeof opts.allowDots === "undefined" ? defaults.allowDots : !!opts.allowDots,
        allowPrototypes: typeof opts.allowPrototypes === "boolean" ? opts.allowPrototypes : defaults.allowPrototypes,
        allowSparse: typeof opts.allowSparse === "boolean" ? opts.allowSparse : defaults.allowSparse,
        arrayLimit: typeof opts.arrayLimit === "number" ? opts.arrayLimit : defaults.arrayLimit,
        charset,
        charsetSentinel: typeof opts.charsetSentinel === "boolean" ? opts.charsetSentinel : defaults.charsetSentinel,
        comma: typeof opts.comma === "boolean" ? opts.comma : defaults.comma,
        decoder: typeof opts.decoder === "function" ? opts.decoder : defaults.decoder,
        delimiter: typeof opts.delimiter === "string" || utils.isRegExp(opts.delimiter) ? opts.delimiter : defaults.delimiter,
        // eslint-disable-next-line no-implicit-coercion, no-extra-parens
        depth: typeof opts.depth === "number" || opts.depth === false ? +opts.depth : defaults.depth,
        ignoreQueryPrefix: opts.ignoreQueryPrefix === true,
        interpretNumericEntities: typeof opts.interpretNumericEntities === "boolean" ? opts.interpretNumericEntities : defaults.interpretNumericEntities,
        parameterLimit: typeof opts.parameterLimit === "number" ? opts.parameterLimit : defaults.parameterLimit,
        parseArrays: opts.parseArrays !== false,
        plainObjects: typeof opts.plainObjects === "boolean" ? opts.plainObjects : defaults.plainObjects,
        strictNullHandling: typeof opts.strictNullHandling === "boolean" ? opts.strictNullHandling : defaults.strictNullHandling
      };
    };
    module.exports = function(str, opts) {
      var options = normalizeParseOptions(opts);
      if (str === "" || str === null || typeof str === "undefined") {
        return options.plainObjects ? /* @__PURE__ */ Object.create(null) : {};
      }
      var tempObj = typeof str === "string" ? parseValues(str, options) : str;
      var obj = options.plainObjects ? /* @__PURE__ */ Object.create(null) : {};
      var keys = Object.keys(tempObj);
      for (var i = 0; i < keys.length; ++i) {
        var key = keys[i];
        var newObj = parseKeys(key, tempObj[key], options, typeof str === "string");
        obj = utils.merge(obj, newObj, options);
      }
      if (options.allowSparse === true) {
        return obj;
      }
      return utils.compact(obj);
    };
  }
});

// node_modules/@strapi/i18n/node_modules/qs/lib/index.js
var require_lib2 = __commonJS({
  "node_modules/@strapi/i18n/node_modules/qs/lib/index.js"(exports, module) {
    "use strict";
    var stringify3 = require_stringify();
    var parse2 = require_parse();
    var formats = require_formats();
    module.exports = {
      formats,
      parse: parse2,
      stringify: stringify3
    };
  }
});

// node_modules/@strapi/content-type-builder/dist/admin/reducers.mjs
var reducers = {
  [`content-type-builder_formModal`]: reducer2,
  [`content-type-builder_dataManagerProvider`]: reducer
};

// node_modules/@strapi/content-type-builder/dist/admin/utils/formAPI.mjs
var import_cloneDeep = __toESM(require_cloneDeep(), 1);
var import_get = __toESM(require_get(), 1);
var formsAPI = {
  components: {
    inputs: {},
    add({ id, component }) {
      if (!this.inputs[id]) {
        this.inputs[id] = component;
      }
    }
  },
  types: {
    attribute: {},
    contentType: {
      validators: [],
      form: {
        advanced: [],
        base: []
      }
    },
    component: {
      validators: [],
      form: {
        advanced: [],
        base: []
      }
    }
  },
  contentTypeSchemaMutations: [],
  addContentTypeSchemaMutation(cb) {
    this.contentTypeSchemaMutations.push(cb);
  },
  extendContentType({ validator, form: { advanced, base } }) {
    const { contentType } = this.types;
    if (validator) {
      contentType.validators.push(validator);
    }
    contentType.form.advanced.push(advanced);
    contentType.form.base.push(base);
  },
  extendFields(fields, { validator, form: { advanced, base } }) {
    const formType = this.types.attribute;
    fields.forEach((field) => {
      if (!formType[field]) {
        formType[field] = {
          validators: [],
          form: {
            advanced: [],
            base: []
          }
        };
      }
      if (validator) {
        formType[field].validators.push(validator);
      }
      formType[field].form.advanced.push(advanced);
      formType[field].form.base.push(base);
    });
  },
  getAdvancedForm(target, props = null) {
    const sectionsToAdd = (0, import_get.default)(this.types, [
      ...target,
      "form",
      "advanced"
    ], []).reduce((acc, current) => {
      const sections = current(props);
      return [
        ...acc,
        ...sections
      ];
    }, []);
    return sectionsToAdd;
  },
  makeCustomFieldValidator(attributeShape, validator, ...validatorArgs) {
    if (!validator) return attributeShape;
    return attributeShape.shape({
      options: create3().shape(validator(validatorArgs))
    });
  },
  makeValidator(target, initShape, ...args) {
    const validators = (0, import_get.default)(this.types, [
      ...target,
      "validators"
    ], []);
    const pluginOptionsShape = validators.reduce((acc, current) => {
      const pluginOptionShape = current(args);
      return {
        ...acc,
        ...pluginOptionShape
      };
    }, {});
    return initShape.shape({
      pluginOptions: create3().shape(pluginOptionsShape)
    });
  },
  mutateContentTypeSchema(data, initialData) {
    let enhancedData = (0, import_cloneDeep.default)(data);
    const refData = (0, import_cloneDeep.default)(initialData);
    this.contentTypeSchemaMutations.forEach((cb) => {
      enhancedData = cb(enhancedData, refData);
    });
    return enhancedData;
  }
};

// node_modules/@strapi/content-type-builder/dist/admin/utils/prefixPluginTranslations.mjs
var prefixPluginTranslations = (trad, pluginId5) => {
  return Object.keys(trad).reduce((acc, current) => {
    acc[`${pluginId5}.${current}`] = trad[current];
    return acc;
  }, {});
};

// node_modules/@strapi/content-type-builder/dist/admin/index.mjs
function __variableDynamicImportRuntime1__(path) {
  switch (path) {
    case "./translations/ar.json":
      return import("./ar.json-3PFZZ3QC.js");
    case "./translations/cs.json":
      return import("./cs.json-BVIOKZG5.js");
    case "./translations/de.json":
      return import("./de.json-OY46QGMN.js");
    case "./translations/dk.json":
      return import("./dk.json-5NZGFB5W.js");
    case "./translations/en.json":
      return import("./en.json-KATKQEQH.js");
    case "./translations/es.json":
      return import("./es.json-VK2AHDEW.js");
    case "./translations/fr.json":
      return import("./fr.json-YMBKIU5L.js");
    case "./translations/id.json":
      return import("./id.json-3OG5LH4D.js");
    case "./translations/it.json":
      return import("./it.json-VSEWBEC2.js");
    case "./translations/ja.json":
      return import("./ja.json-DWADQSVY.js");
    case "./translations/ko.json":
      return import("./ko.json-XRARLRFF.js");
    case "./translations/ms.json":
      return import("./ms.json-OYRQIG7I.js");
    case "./translations/nl.json":
      return import("./nl.json-ZXDL5NWV.js");
    case "./translations/pl.json":
      return import("./pl.json-PJB22UAI.js");
    case "./translations/pt-BR.json":
      return import("./pt-BR.json-4L4N7XZA.js");
    case "./translations/pt.json":
      return import("./pt.json-WXQYIHXO.js");
    case "./translations/ru.json":
      return import("./ru.json-T6RCGPRT.js");
    case "./translations/sk.json":
      return import("./sk.json-A33FQAC4.js");
    case "./translations/sv.json":
      return import("./sv.json-AAZEJLMC.js");
    case "./translations/th.json":
      return import("./th.json-VEBY3S5D.js");
    case "./translations/tr.json":
      return import("./tr.json-VLKKI6CE.js");
    case "./translations/uk.json":
      return import("./uk.json-CP6ZJYY2.js");
    case "./translations/zh-Hans.json":
      return import("./zh-Hans.json-7GSLOS5T.js");
    case "./translations/zh.json":
      return import("./zh.json-47LFTLPP.js");
    default:
      return new Promise(function(resolve, reject) {
        (typeof queueMicrotask === "function" ? queueMicrotask : setTimeout)(
          reject.bind(null, new Error("Unknown variable dynamic import: " + path))
        );
      });
  }
}
var index2 = {
  register(app) {
    app.addReducers(reducers);
    app.addMenuLink({
      to: `plugins/${pluginId}`,
      icon: ForwardRef$2x,
      intlLabel: {
        id: `${pluginId}.plugin.name`,
        defaultMessage: "Content-Type Builder"
      },
      permissions: PERMISSIONS.main,
      Component: () => import("./App-Y2DPBUO2.js"),
      position: 5
    });
    app.registerPlugin({
      id: pluginId,
      name: pluginId,
      // Internal APIs exposed by the CTB for the other plugins to use
      apis: {
        forms: formsAPI
      }
    });
  },
  bootstrap() {
  },
  async registerTrads({ locales }) {
    const importedTrads = await Promise.all(locales.map((locale) => {
      return __variableDynamicImportRuntime1__(`./translations/${locale}.json`).then(({ default: data }) => {
        return {
          data: prefixPluginTranslations(data, pluginId),
          locale
        };
      }).catch(() => {
        return {
          data: {},
          locale
        };
      });
    }));
    return Promise.resolve(importedTrads);
  }
};

// node_modules/@strapi/email/dist/admin/utils/prefixPluginTranslations.mjs
var prefixPluginTranslations2 = (trad, pluginId5) => {
  return Object.keys(trad).reduce((acc, current) => {
    acc[`${pluginId5}.${current}`] = trad[current];
    return acc;
  }, {});
};

// node_modules/@strapi/email/dist/admin/index.mjs
function __variableDynamicImportRuntime1__2(path) {
  switch (path) {
    case "./translations/ar.json":
      return import("./ar.json-XT2AE5MD.js");
    case "./translations/cs.json":
      return import("./cs.json-5JA7XHIC.js");
    case "./translations/de.json":
      return import("./de.json-7VRMCTYR.js");
    case "./translations/dk.json":
      return import("./dk.json-XKI7CLW6.js");
    case "./translations/en.json":
      return import("./en.json-MD5H2ZNO.js");
    case "./translations/es.json":
      return import("./es.json-WU2KAU5P.js");
    case "./translations/fr.json":
      return import("./fr.json-HZUEJNY3.js");
    case "./translations/id.json":
      return import("./id.json-FHZVCNDO.js");
    case "./translations/it.json":
      return import("./it.json-B3SKNTFX.js");
    case "./translations/ja.json":
      return import("./ja.json-EZKJRE4T.js");
    case "./translations/ko.json":
      return import("./ko.json-IY4N3EIS.js");
    case "./translations/ms.json":
      return import("./ms.json-XXLISYOZ.js");
    case "./translations/nl.json":
      return import("./nl.json-OPZTYQ5F.js");
    case "./translations/pl.json":
      return import("./pl.json-KIA6EPL4.js");
    case "./translations/pt-BR.json":
      return import("./pt-BR.json-6GSTCGDQ.js");
    case "./translations/pt.json":
      return import("./pt.json-TC5QVEJX.js");
    case "./translations/ru.json":
      return import("./ru.json-626W5GSD.js");
    case "./translations/sk.json":
      return import("./sk.json-3JI2YCKF.js");
    case "./translations/th.json":
      return import("./th.json-Z6CZT4Y2.js");
    case "./translations/tr.json":
      return import("./tr.json-MLYUSLWC.js");
    case "./translations/uk.json":
      return import("./uk.json-L5EGH2OL.js");
    case "./translations/vi.json":
      return import("./vi.json-PPZALNIY.js");
    case "./translations/zh-Hans.json":
      return import("./zh-Hans.json-RVHRMTOJ.js");
    case "./translations/zh.json":
      return import("./zh.json-4S7OU5HD.js");
    default:
      return new Promise(function(resolve, reject) {
        (typeof queueMicrotask === "function" ? queueMicrotask : setTimeout)(
          reject.bind(null, new Error("Unknown variable dynamic import: " + path))
        );
      });
  }
}
var admin = {
  // TODO typing app in strapi/types as every plugin needs it
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  register(app) {
    app.createSettingSection({
      id: "email",
      intlLabel: {
        id: "email.SettingsNav.section-label",
        defaultMessage: "Email Plugin"
      }
    }, [
      {
        intlLabel: {
          id: "email.Settings.email.plugin.title",
          defaultMessage: "Settings"
        },
        id: "settings",
        to: "email",
        Component: () => import("./Settings-Q7DA3KMU.js").then((mod) => ({
          default: mod.ProtectedSettingsPage
        })),
        permissions: PERMISSIONS2.settings
      }
    ]);
    app.registerPlugin({
      id: "email",
      name: "email"
    });
  },
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  bootstrap() {
  },
  async registerTrads({ locales }) {
    const importedTrads = await Promise.all(locales.map((locale) => {
      return __variableDynamicImportRuntime1__2(`./translations/${locale}.json`).then(({ default: data }) => {
        return {
          data: prefixPluginTranslations2(data, "email"),
          locale
        };
      }).catch(() => {
        return {
          data: {},
          locale
        };
      });
    }));
    return Promise.resolve(importedTrads);
  }
};

// node_modules/@strapi/upload/dist/admin/components/MediaLibraryDialog/MediaLibraryDialog.mjs
var import_jsx_runtime10 = __toESM(require_jsx_runtime(), 1);
var React6 = __toESM(require_react(), 1);

// node_modules/@strapi/upload/dist/admin/components/AssetDialog/AssetDialog.mjs
var import_jsx_runtime9 = __toESM(require_jsx_runtime(), 1);
var React5 = __toESM(require_react(), 1);

// node_modules/@strapi/upload/dist/admin/hooks/useModalQueryParams.mjs
var React = __toESM(require_react(), 1);
var import_qs = __toESM(require_lib(), 1);
var useModalQueryParams = (initialState) => {
  const { trackUsage } = useTracking();
  const { config: { data: config } } = useConfig();
  const [queryObject, setQueryObject] = React.useState({
    page: 1,
    sort: "updatedAt:DESC",
    pageSize: 10,
    filters: {
      $and: []
    },
    ...initialState
  });
  React.useEffect(() => {
    if (config && "sort" in config && "pageSize" in config) {
      setQueryObject((prevQuery) => ({
        ...prevQuery,
        sort: config.sort,
        pageSize: config.pageSize
      }));
    }
  }, [
    config
  ]);
  const handleChangeFilters = (nextFilters) => {
    if (nextFilters) {
      trackUsage("didFilterMediaLibraryElements", {
        location: "content-manager",
        filter: Object.keys(nextFilters[nextFilters.length - 1])[0]
      });
      setQueryObject((prev) => ({
        ...prev,
        page: 1,
        filters: {
          $and: nextFilters
        }
      }));
    }
  };
  const handleChangePageSize = (pageSize) => {
    setQueryObject((prev) => ({
      ...prev,
      pageSize: typeof pageSize === "string" ? parseInt(pageSize, 10) : pageSize,
      page: 1
    }));
  };
  const handeChangePage = (page) => {
    setQueryObject((prev) => ({
      ...prev,
      page
    }));
  };
  const handleChangeSort = (sort) => {
    if (sort) {
      trackUsage("didSortMediaLibraryElements", {
        location: "content-manager",
        sort
      });
      setQueryObject((prev) => ({
        ...prev,
        sort
      }));
    }
  };
  const handleChangeSearch = (_q) => {
    if (_q) {
      setQueryObject((prev) => ({
        ...prev,
        _q,
        page: 1
      }));
    } else {
      const newState = {
        page: 1
      };
      Object.keys(queryObject).forEach((key) => {
        if (![
          "page",
          "_q"
        ].includes(key)) {
          newState[key] = queryObject[key];
        }
      });
      setQueryObject(newState);
    }
  };
  const handleChangeFolder = (folder, folderPath) => {
    setQueryObject((prev) => ({
      ...prev,
      folder: folder ?? null,
      folderPath
    }));
  };
  return [
    {
      queryObject,
      rawQuery: (0, import_qs.stringify)(queryObject, {
        encode: false
      })
    },
    {
      onChangeFilters: handleChangeFilters,
      onChangeFolder: handleChangeFolder,
      onChangePage: handeChangePage,
      onChangePageSize: handleChangePageSize,
      onChangeSort: handleChangeSort,
      onChangeSearch: handleChangeSearch
    }
  ];
};

// node_modules/@strapi/upload/dist/admin/utils/toSingularTypes.mjs
var toSingularTypes = (types) => {
  if (!types) {
    return [];
  }
  return types.map((type) => type.substring(0, type.length - 1));
};

// node_modules/@strapi/upload/dist/admin/utils/getAllowedFiles.mjs
var getAllowedFiles = (pluralTypes, files) => {
  if (!pluralTypes) {
    return files;
  }
  const singularTypes = toSingularTypes(pluralTypes);
  const allowedFiles = files.filter((file) => {
    var _a;
    const fileType = (_a = file == null ? void 0 : file.mime) == null ? void 0 : _a.split("/")[0];
    if (!fileType) {
      return false;
    }
    if (singularTypes.includes("file") && ![
      "video",
      "image",
      "audio"
    ].includes(fileType)) {
      return true;
    }
    return singularTypes.includes(fileType);
  });
  return allowedFiles;
};

// node_modules/@strapi/upload/dist/admin/components/AssetDialog/AssetDialog.mjs
var import_qs6 = __toESM(require_lib(), 1);

// node_modules/@strapi/upload/dist/admin/utils/moveElement.mjs
var move = (array, oldIndex, newIndex) => {
  if (newIndex >= array.length) {
    newIndex = array.length - 1;
  }
  array.splice(newIndex, 0, array.splice(oldIndex, 1)[0]);
  return array;
};
var moveElement = (array, index4, offset) => {
  const newIndex = index4 + offset;
  return move(array, index4, newIndex);
};

// node_modules/@strapi/upload/dist/admin/components/AssetDialog/BrowseStep/BrowseStep.mjs
var import_jsx_runtime6 = __toESM(require_jsx_runtime(), 1);

// node_modules/@strapi/upload/dist/admin/utils/getBreadcrumbDataCM.mjs
var getBreadcrumbDataCM = (folder) => {
  var _a;
  const data = [
    {
      id: null,
      label: {
        id: getTrad("plugin.name"),
        defaultMessage: "Media Library"
      }
    }
  ];
  if ((_a = folder == null ? void 0 : folder.parent) == null ? void 0 : _a.parent) {
    data.push([]);
  }
  if (folder == null ? void 0 : folder.parent) {
    data.push({
      id: folder.parent.id,
      label: folder.parent.name,
      path: folder.parent.path
    });
  }
  if (folder) {
    data.push({
      id: folder.id,
      label: folder.name,
      path: folder.path
    });
  }
  return data;
};

// node_modules/@strapi/upload/dist/admin/components/AssetDialog/BrowseStep/BrowseStep.mjs
var import_qs4 = __toESM(require_lib(), 1);

// node_modules/@strapi/upload/dist/admin/components/AssetDialog/BrowseStep/Filters.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React2 = __toESM(require_react(), 1);
var import_qs2 = __toESM(require_lib(), 1);
var Filters = ({ appliedFilters, onChangeFilters }) => {
  const [open, setOpen] = React2.useState(false);
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime.jsxs)(Popover.Root, {
    open,
    onOpenChange: setOpen,
    children: [
      (0, import_jsx_runtime.jsx)(Popover.Trigger, {
        children: (0, import_jsx_runtime.jsx)(Button, {
          variant: "tertiary",
          startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$3j, {}),
          size: "S",
          children: formatMessage({
            id: "app.utils.filters",
            defaultMessage: "Filters"
          })
        })
      }),
      (0, import_jsx_runtime.jsx)(FilterPopover, {
        onToggle: () => setOpen((prev) => !prev),
        displayedFilters,
        filters: appliedFilters,
        onSubmit: onChangeFilters
      }),
      appliedFilters && (0, import_jsx_runtime.jsx)(FilterList, {
        appliedFilters,
        filtersSchema: displayedFilters,
        onRemoveFilter: onChangeFilters
      })
    ]
  });
};

// node_modules/@strapi/upload/dist/admin/components/AssetDialog/BrowseStep/PageSize.mjs
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var PageSize = ({ onChangePageSize, pageSize }) => {
  const { formatMessage } = useIntl();
  const handleChange = (value) => {
    onChangePageSize(Number(value));
  };
  return (0, import_jsx_runtime2.jsxs)(Flex, {
    children: [
      (0, import_jsx_runtime2.jsxs)(SingleSelect, {
        size: "S",
        "aria-label": formatMessage({
          id: "components.PageFooter.select",
          defaultMessage: "Entries per page"
        }),
        onChange: handleChange,
        value: pageSize.toString(),
        children: [
          (0, import_jsx_runtime2.jsx)(SingleSelectOption, {
            value: "10",
            children: "10"
          }),
          (0, import_jsx_runtime2.jsx)(SingleSelectOption, {
            value: "20",
            children: "20"
          }),
          (0, import_jsx_runtime2.jsx)(SingleSelectOption, {
            value: "50",
            children: "50"
          }),
          (0, import_jsx_runtime2.jsx)(SingleSelectOption, {
            value: "100",
            children: "100"
          })
        ]
      }),
      (0, import_jsx_runtime2.jsx)(Box, {
        paddingLeft: 2,
        children: (0, import_jsx_runtime2.jsx)(Typography, {
          textColor: "neutral600",
          tag: "label",
          htmlFor: "page-size",
          children: formatMessage({
            id: "components.PageFooter.select",
            defaultMessage: "Entries per page"
          })
        })
      })
    ]
  });
};

// node_modules/@strapi/upload/dist/admin/components/AssetDialog/BrowseStep/PaginationFooter/PaginationFooter.mjs
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);

// node_modules/@strapi/upload/dist/admin/components/AssetDialog/BrowseStep/PaginationFooter/Pagination.mjs
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var React3 = __toESM(require_react(), 1);
var PaginationContext = React3.createContext({
  activePage: 1,
  pageCount: 1
});
var usePagination = () => React3.useContext(PaginationContext);
var Pagination = ({ children, activePage, pageCount, label = "pagination" }) => {
  const paginationValue = React3.useMemo(() => ({
    activePage,
    pageCount
  }), [
    activePage,
    pageCount
  ]);
  return (0, import_jsx_runtime3.jsx)(PaginationContext.Provider, {
    value: paginationValue,
    children: (0, import_jsx_runtime3.jsx)(Box, {
      tag: "nav",
      "aria-label": label,
      children: (0, import_jsx_runtime3.jsx)(Flex, {
        tag: "ul",
        gap: 1,
        children
      })
    })
  });
};

// node_modules/@strapi/upload/dist/admin/components/AssetDialog/BrowseStep/PaginationFooter/PaginationFooter.mjs
var PaginationText = dt(Typography)`
  line-height: revert;
`;
var linkWrapperStyles = lt`
  padding: ${({ theme }) => theme.spaces[3]};
  border-radius: ${({ theme }) => theme.borderRadius};
  box-shadow: ${({ $active, theme }) => $active ? theme.shadows.filterShadow : void 0};
  text-decoration: none;
  display: flex;
  position: relative;
  outline: none;

  &:after {
    transition-property: all;
    transition-duration: 0.2s;
    border-radius: 8px;
    content: '';
    position: absolute;
    top: -4px;
    bottom: -4px;
    left: -4px;
    right: -4px;
    border: 2px solid transparent;
  }

  &:focus-visible {
    outline: none;

    &:after {
      border-radius: 8px;
      content: '';
      position: absolute;
      top: -5px;
      bottom: -5px;
      left: -5px;
      right: -5px;
      border: 2px solid ${(props) => props.theme.colors.primary600};
    }
  }
`;
var LinkWrapperButton = dt.button`
  ${linkWrapperStyles}
`;
var LinkWrapperDiv = dt.div`
  ${linkWrapperStyles}
`;
LinkWrapperButton.defaultProps = {
  type: "button"
};
var PageLinkWrapper = dt(LinkWrapperButton)`
  color: ${({ theme, $active }) => $active ? theme.colors.primary700 : theme.colors.neutral800};
  background: ${({ theme, $active }) => $active ? theme.colors.neutral0 : void 0};

  &:hover {
    box-shadow: ${({ theme }) => theme.shadows.filterShadow};
  }
`;
var ActionLinkWrapper = dt(LinkWrapperButton)`
  font-size: 1.1rem;
  svg path {
    fill: ${(p) => p["aria-disabled"] ? p.theme.colors.neutral300 : p.theme.colors.neutral600};
  }

  &:focus,
  &:hover {
    svg path {
      fill: ${(p) => p["aria-disabled"] ? p.theme.colors.neutral300 : p.theme.colors.neutral700};
    }
  }

  ${(p) => p["aria-disabled"] ? `
  pointer-events: none;
    ` : void 0}
`;
var DotsWrapper = dt(LinkWrapperDiv)`
  color: ${({ theme }) => theme.colors.neutral800};
`;
var PreviousLink = ({ children, ...props }) => {
  const { activePage } = usePagination();
  const disabled = activePage === 1;
  return (0, import_jsx_runtime4.jsx)("li", {
    children: (0, import_jsx_runtime4.jsxs)(ActionLinkWrapper, {
      "aria-disabled": disabled,
      tabIndex: disabled ? -1 : void 0,
      ...props,
      children: [
        (0, import_jsx_runtime4.jsx)(VisuallyHidden, {
          children
        }),
        (0, import_jsx_runtime4.jsx)(ForwardRef$4x, {
          "aria-hidden": true
        })
      ]
    })
  });
};
var NextLink = ({ children, ...props }) => {
  const { activePage, pageCount } = usePagination();
  const disabled = activePage === pageCount;
  return (0, import_jsx_runtime4.jsx)("li", {
    children: (0, import_jsx_runtime4.jsxs)(ActionLinkWrapper, {
      "aria-disabled": disabled,
      tabIndex: disabled ? -1 : void 0,
      ...props,
      children: [
        (0, import_jsx_runtime4.jsx)(VisuallyHidden, {
          children
        }),
        (0, import_jsx_runtime4.jsx)(ForwardRef$4v, {
          "aria-hidden": true
        })
      ]
    })
  });
};
var PageLink = ({ number, children, ...props }) => {
  const { activePage } = usePagination();
  const isActive = activePage === number;
  return (0, import_jsx_runtime4.jsx)("li", {
    children: (0, import_jsx_runtime4.jsxs)(PageLinkWrapper, {
      ...props,
      $active: isActive,
      children: [
        (0, import_jsx_runtime4.jsx)(VisuallyHidden, {
          children
        }),
        (0, import_jsx_runtime4.jsx)(PaginationText, {
          "aria-hidden": true,
          variant: "pi",
          fontWeight: isActive ? "bold" : "",
          children: number
        })
      ]
    })
  });
};
var Dots = ({ children, ...props }) => (0, import_jsx_runtime4.jsx)("li", {
  children: (0, import_jsx_runtime4.jsxs)(DotsWrapper, {
    ...props,
    as: "div",
    children: [
      (0, import_jsx_runtime4.jsx)(VisuallyHidden, {
        children
      }),
      (0, import_jsx_runtime4.jsx)(PaginationText, {
        "aria-hidden": true,
        small: true,
        children: "…"
      })
    ]
  })
});
var PaginationFooter = ({ activePage, onChangePage, pagination: { pageCount } }) => {
  const { formatMessage } = useIntl();
  const previousActivePage = activePage - 1;
  const nextActivePage = activePage + 1;
  const firstLinks = [
    (0, import_jsx_runtime4.jsx)(PageLink, {
      number: 1,
      onClick: () => {
        onChangePage(1);
      },
      children: formatMessage({
        id: "components.pagination.go-to",
        defaultMessage: "Go to page {page}"
      }, {
        page: 1
      })
    }, 1)
  ];
  if (pageCount <= 4) {
    const links = Array.from({
      length: pageCount
    }).map((_, i) => i + 1).map((number) => {
      return (0, import_jsx_runtime4.jsx)(PageLink, {
        number,
        onClick: () => onChangePage(number),
        children: formatMessage({
          id: "components.pagination.go-to",
          defaultMessage: "Go to page {page}"
        }, {
          page: number
        })
      }, number);
    });
    return (0, import_jsx_runtime4.jsxs)(Pagination, {
      activePage,
      pageCount,
      children: [
        (0, import_jsx_runtime4.jsx)(PreviousLink, {
          onClick: () => onChangePage(previousActivePage),
          children: formatMessage({
            id: "components.pagination.go-to-previous",
            defaultMessage: "Go to previous page"
          })
        }),
        links,
        (0, import_jsx_runtime4.jsx)(NextLink, {
          onClick: () => onChangePage(nextActivePage),
          children: formatMessage({
            id: "components.pagination.go-to-next",
            defaultMessage: "Go to next page"
          })
        })
      ]
    });
  }
  let firstLinksToCreate = [];
  const lastLinks = [];
  let lastLinksToCreate = [];
  const middleLinks = [];
  if (pageCount > 1) {
    lastLinks.push((0, import_jsx_runtime4.jsx)(PageLink, {
      number: pageCount,
      onClick: () => onChangePage(pageCount),
      children: formatMessage({
        id: "components.pagination.go-to",
        defaultMessage: "Go to page {page}"
      }, {
        page: pageCount
      })
    }, pageCount));
  }
  if (activePage === 1 && pageCount >= 3) {
    firstLinksToCreate = [
      2
    ];
  }
  if (activePage === 2 && pageCount >= 3) {
    if (pageCount === 5) {
      firstLinksToCreate = [
        2,
        3,
        4
      ];
    } else if (pageCount === 3) {
      firstLinksToCreate = [
        2
      ];
    } else {
      firstLinksToCreate = [
        2,
        3
      ];
    }
  }
  if (activePage === 4 && pageCount >= 3) {
    firstLinksToCreate = [
      2
    ];
  }
  if (activePage === pageCount && pageCount >= 3) {
    lastLinksToCreate = [
      pageCount - 1
    ];
  }
  if (activePage === pageCount - 2 && pageCount > 3) {
    lastLinksToCreate = [
      activePage + 1,
      activePage,
      activePage - 1
    ];
  }
  if (activePage === pageCount - 3 && pageCount > 3 && activePage > 5) {
    lastLinksToCreate = [
      activePage + 2,
      activePage + 1,
      activePage,
      activePage - 1
    ];
  }
  if (activePage === pageCount - 1 && pageCount > 3) {
    lastLinksToCreate = [
      activePage,
      activePage - 1
    ];
  }
  lastLinksToCreate.forEach((number) => {
    lastLinks.unshift((0, import_jsx_runtime4.jsxs)(PageLink, {
      number,
      onClick: () => onChangePage(number),
      children: [
        "Go to page ",
        number
      ]
    }, number));
  });
  firstLinksToCreate.forEach((number) => {
    firstLinks.push((0, import_jsx_runtime4.jsx)(PageLink, {
      number,
      onClick: () => onChangePage(number),
      children: formatMessage({
        id: "components.pagination.go-to",
        defaultMessage: "Go to page {page}"
      }, {
        page: number
      })
    }, number));
  });
  if (![
    1,
    2
  ].includes(activePage) && activePage <= pageCount - 3 && firstLinks.length + lastLinks.length < 6) {
    const middleLinksToCreate = [
      activePage - 1,
      activePage,
      activePage + 1
    ];
    middleLinksToCreate.forEach((number) => {
      middleLinks.push((0, import_jsx_runtime4.jsx)(PageLink, {
        number,
        onClick: () => onChangePage(number),
        children: formatMessage({
          id: "components.pagination.go-to",
          defaultMessage: "Go to page {page}"
        }, {
          page: number
        })
      }, number));
    });
  }
  const shouldShowDotsAfterFirstLink = pageCount > 5 || pageCount === 5 && (activePage === 1 || activePage === 5);
  const shouldShowMiddleDots = middleLinks.length > 2 && activePage > 4 && pageCount > 5;
  const beforeDotsLinksLength = shouldShowMiddleDots ? pageCount - activePage - 1 : pageCount - firstLinks.length - lastLinks.length;
  const afterDotsLength = shouldShowMiddleDots ? pageCount - firstLinks.length - lastLinks.length : pageCount - activePage - 1;
  return (0, import_jsx_runtime4.jsxs)(Pagination, {
    activePage,
    pageCount,
    children: [
      (0, import_jsx_runtime4.jsx)(PreviousLink, {
        onClick: () => onChangePage(previousActivePage),
        children: formatMessage({
          id: "components.pagination.go-to-previous",
          defaultMessage: "Go to previous page"
        })
      }),
      firstLinks,
      shouldShowMiddleDots && (0, import_jsx_runtime4.jsx)(Dots, {
        children: formatMessage({
          id: "components.pagination.remaining-links",
          defaultMessage: "And {number} other links"
        }, {
          number: beforeDotsLinksLength
        })
      }),
      middleLinks,
      shouldShowDotsAfterFirstLink && (0, import_jsx_runtime4.jsx)(Dots, {
        children: formatMessage({
          id: "components.pagination.remaining-links",
          defaultMessage: "And {number} other links"
        }, {
          number: afterDotsLength
        })
      }),
      lastLinks,
      (0, import_jsx_runtime4.jsx)(NextLink, {
        onClick: () => onChangePage(nextActivePage),
        children: formatMessage({
          id: "components.pagination.go-to-next",
          defaultMessage: "Go to next page"
        })
      })
    ]
  });
};

// node_modules/@strapi/upload/dist/admin/components/AssetDialog/BrowseStep/SearchAsset/SearchAsset.mjs
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
var React4 = __toESM(require_react(), 1);
var import_qs3 = __toESM(require_lib(), 1);
var SearchAsset = ({ onChangeSearch, queryValue = null }) => {
  const { formatMessage } = useIntl();
  const { trackUsage } = useTracking();
  const [isOpen, setIsOpen] = React4.useState(!!queryValue);
  const [value, setValue] = React4.useState(queryValue || "");
  const wrapperRef = React4.useRef(null);
  React4.useLayoutEffect(() => {
    if (isOpen) {
      setTimeout(() => {
        var _a, _b;
        (_b = (_a = wrapperRef.current) == null ? void 0 : _a.querySelector("input")) == null ? void 0 : _b.focus();
      }, 0);
    }
  }, [
    isOpen
  ]);
  const handleToggle = () => {
    setIsOpen((prev) => !prev);
  };
  const handleClear = () => {
    handleToggle();
    onChangeSearch(null);
  };
  const handleSubmit = (e) => {
    e.preventDefault();
    e.stopPropagation();
    trackUsage("didSearchMediaLibraryElements", {
      location: "content-manager"
    });
    onChangeSearch(value);
  };
  if (isOpen) {
    return (0, import_jsx_runtime5.jsx)("div", {
      ref: wrapperRef,
      children: (0, import_jsx_runtime5.jsx)(SearchForm, {
        onSubmit: handleSubmit,
        children: (0, import_jsx_runtime5.jsx)(Searchbar, {
          name: "search",
          onClear: handleClear,
          onChange: (e) => setValue(e.target.value),
          clearLabel: formatMessage({
            id: getTrad("search.clear.label"),
            defaultMessage: "Clear the search"
          }),
          "aria-label": "search",
          size: "S",
          value,
          placeholder: formatMessage({
            id: getTrad("search.placeholder"),
            defaultMessage: "e.g: the first dog on the moon"
          }),
          children: formatMessage({
            id: getTrad("search.label"),
            defaultMessage: "Search for an asset"
          })
        })
      })
    });
  }
  return (0, import_jsx_runtime5.jsx)(IconButton, {
    label: "Search",
    onClick: handleToggle,
    children: (0, import_jsx_runtime5.jsx)(ForwardRef$X, {})
  });
};

// node_modules/@strapi/upload/dist/admin/components/AssetDialog/BrowseStep/utils/isSelectable.mjs
var isSelectable = (allowedTypes, mime = "") => {
  if (!mime) return false;
  const fileType = mime.split("/")[0];
  return allowedTypes.includes(fileType) || allowedTypes.includes("file") && ![
    "video",
    "image",
    "audio"
  ].includes(fileType);
};

// node_modules/@strapi/upload/dist/admin/components/AssetDialog/BrowseStep/BrowseStep.mjs
var TypographyMaxWidth = dt(Typography)`
  max-width: 100%;
`;
var ActionContainer = dt(Box)`
  svg {
    path {
      fill: ${({ theme }) => theme.colors.neutral500};
    }
  }
`;
var BrowseStep = ({ allowedTypes = [], assets: rawAssets, canCreate, canRead, folders = [], multiple = false, onAddAsset, onChangeFilters, onChangePage, onChangePageSize, onChangeSearch, onChangeSort, onChangeFolder, onEditAsset, onEditFolder, onSelectAllAsset, onSelectAsset, pagination, queryObject, selectedAssets }) => {
  var _a, _b, _c;
  const { formatMessage } = useIntl();
  const [view, setView] = usePersistentState(localStorageKeys.modalView, viewOptions.GRID);
  const isGridView = view === viewOptions.GRID;
  const { data: currentFolder, isLoading: isCurrentFolderLoading } = useFolder(queryObject == null ? void 0 : queryObject.folder, {
    enabled: canRead && !!(queryObject == null ? void 0 : queryObject.folder)
  });
  const singularTypes = toSingularTypes(allowedTypes);
  const assets = rawAssets.map((asset) => ({
    ...asset,
    isSelectable: isSelectable(singularTypes, asset == null ? void 0 : asset.mime),
    type: "asset"
  }));
  const breadcrumbs = !isCurrentFolderLoading ? getBreadcrumbDataCM(currentFolder) : void 0;
  const allAllowedAsset = getAllowedFiles(allowedTypes, assets);
  const areAllAssetSelected = allAllowedAsset.length > 0 && selectedAssets.length > 0 && allAllowedAsset.every((asset) => selectedAssets.findIndex((currAsset) => currAsset.id === asset.id) !== -1);
  const hasSomeAssetSelected = allAllowedAsset.some((asset) => selectedAssets.findIndex((currAsset) => currAsset.id === asset.id) !== -1);
  const isSearching = !!(queryObject == null ? void 0 : queryObject._q);
  const isFiltering = !!((_b = (_a = queryObject == null ? void 0 : queryObject.filters) == null ? void 0 : _a.$and) == null ? void 0 : _b.length) && queryObject.filters.$and.length > 0;
  const isSearchingOrFiltering = isSearching || isFiltering;
  const assetCount = assets.length;
  const folderCount = folders.length;
  const handleClickFolderCard = (...args) => {
    onChangeSearch("");
    onChangeFolder(...args);
  };
  return (0, import_jsx_runtime6.jsxs)(Box, {
    children: [
      onSelectAllAsset && (0, import_jsx_runtime6.jsx)(Box, {
        paddingBottom: 4,
        children: (0, import_jsx_runtime6.jsxs)(Flex, {
          justifyContent: "space-between",
          alignItems: "flex-start",
          children: [
            (assetCount > 0 || folderCount > 0 || isFiltering) && (0, import_jsx_runtime6.jsxs)(Flex, {
              gap: 2,
              wrap: "wrap",
              children: [
                multiple && isGridView && (0, import_jsx_runtime6.jsx)(Flex, {
                  paddingLeft: 2,
                  paddingRight: 2,
                  background: "neutral0",
                  hasRadius: true,
                  borderColor: "neutral200",
                  height: "3.2rem",
                  children: (0, import_jsx_runtime6.jsx)(CheckboxImpl, {
                    "aria-label": formatMessage({
                      id: getTrad("bulk.select.label"),
                      defaultMessage: "Select all assets"
                    }),
                    checked: !areAllAssetSelected && hasSomeAssetSelected ? "indeterminate" : areAllAssetSelected,
                    onCheckedChange: onSelectAllAsset
                  })
                }),
                isGridView && (0, import_jsx_runtime6.jsx)(SortPicker, {
                  onChangeSort,
                  value: queryObject == null ? void 0 : queryObject.sort
                }),
                (0, import_jsx_runtime6.jsx)(Filters, {
                  appliedFilters: (_c = queryObject == null ? void 0 : queryObject.filters) == null ? void 0 : _c.$and,
                  onChangeFilters
                })
              ]
            }),
            (assetCount > 0 || folderCount > 0 || isSearching) && (0, import_jsx_runtime6.jsxs)(Flex, {
              marginLeft: "auto",
              shrink: 0,
              gap: 2,
              children: [
                (0, import_jsx_runtime6.jsx)(ActionContainer, {
                  paddingTop: 1,
                  paddingBottom: 1,
                  children: (0, import_jsx_runtime6.jsx)(IconButton, {
                    label: isGridView ? formatMessage({
                      id: "view-switch.list",
                      defaultMessage: "List View"
                    }) : formatMessage({
                      id: "view-switch.grid",
                      defaultMessage: "Grid View"
                    }),
                    onClick: () => setView(isGridView ? viewOptions.LIST : viewOptions.GRID),
                    children: isGridView ? (0, import_jsx_runtime6.jsx)(ForwardRef$2p, {}) : (0, import_jsx_runtime6.jsx)(ForwardRef$39, {})
                  })
                }),
                (0, import_jsx_runtime6.jsx)(SearchAsset, {
                  onChangeSearch,
                  queryValue: queryObject._q || ""
                })
              ]
            })
          ]
        })
      }),
      canRead && (breadcrumbs == null ? void 0 : breadcrumbs.length) && breadcrumbs.length > 0 && currentFolder && (0, import_jsx_runtime6.jsx)(Box, {
        paddingTop: 3,
        children: (0, import_jsx_runtime6.jsx)(Breadcrumbs, {
          onChangeFolder,
          label: formatMessage({
            id: getTrad("header.breadcrumbs.nav.label"),
            defaultMessage: "Folders navigation"
          }),
          breadcrumbs,
          currentFolderId: queryObject == null ? void 0 : queryObject.folder
        })
      }),
      assetCount === 0 && folderCount === 0 && (0, import_jsx_runtime6.jsx)(Box, {
        paddingBottom: 6,
        children: (0, import_jsx_runtime6.jsx)(EmptyAssets, {
          size: "S",
          count: 6,
          action: canCreate && !isFiltering && !isSearching && (0, import_jsx_runtime6.jsx)(Button, {
            variant: "secondary",
            startIcon: (0, import_jsx_runtime6.jsx)(ForwardRef$1h, {}),
            onClick: onAddAsset,
            children: formatMessage({
              id: getTrad("header.actions.add-assets"),
              defaultMessage: "Add new assets"
            })
          }),
          content: (
            // eslint-disable-next-line no-nested-ternary
            isSearchingOrFiltering ? formatMessage({
              id: getTrad("list.assets-empty.title-withSearch"),
              defaultMessage: "There are no assets with the applied filters"
            }) : canCreate && !isSearching ? formatMessage({
              id: getTrad("list.assets.empty"),
              defaultMessage: "Upload your first assets..."
            }) : formatMessage({
              id: getTrad("list.assets.empty.no-permissions"),
              defaultMessage: "The asset list is empty"
            })
          )
        })
      }),
      !isGridView && (folderCount > 0 || assetCount > 0) && (0, import_jsx_runtime6.jsx)(TableList, {
        allowedTypes,
        assetCount,
        folderCount,
        indeterminate: !areAllAssetSelected && hasSomeAssetSelected,
        isFolderSelectionAllowed: false,
        onChangeSort,
        onChangeFolder: handleClickFolderCard,
        onEditAsset,
        onEditFolder,
        onSelectOne: onSelectAsset,
        onSelectAll: onSelectAllAsset,
        rows: [
          ...folders.map((folder) => ({
            ...folder,
            type: "folder"
          })),
          ...assets
        ],
        selected: selectedAssets,
        shouldDisableBulkSelect: !multiple,
        sortQuery: (queryObject == null ? void 0 : queryObject.sort) ?? ""
      }),
      isGridView && (0, import_jsx_runtime6.jsxs)(import_jsx_runtime6.Fragment, {
        children: [
          folderCount > 0 && (0, import_jsx_runtime6.jsx)(FolderGridList, {
            title: (isSearchingOrFiltering && assetCount > 0 || !isSearchingOrFiltering) && formatMessage({
              id: getTrad("list.folders.title"),
              defaultMessage: "Folders ({count})"
            }, {
              count: folderCount
            }) || "",
            children: folders.map((folder) => {
              var _a2, _b2;
              return (0, import_jsx_runtime6.jsx)(Grid.Item, {
                col: 3,
                direction: "column",
                alignItems: "stretch",
                children: (0, import_jsx_runtime6.jsx)(FolderCard, {
                  ariaLabel: folder.name,
                  id: `folder-${folder.id}`,
                  onClick: () => handleClickFolderCard(folder.id, folder.path),
                  cardActions: onEditFolder && (0, import_jsx_runtime6.jsx)(IconButton, {
                    withTooltip: false,
                    label: formatMessage({
                      id: getTrad("list.folder.edit"),
                      defaultMessage: "Edit folder"
                    }),
                    onClick: () => onEditFolder(folder),
                    children: (0, import_jsx_runtime6.jsx)(ForwardRef$1v, {})
                  }),
                  children: (0, import_jsx_runtime6.jsx)(FolderCardBody, {
                    children: (0, import_jsx_runtime6.jsx)(FolderCardBodyAction, {
                      onClick: () => handleClickFolderCard(folder.id, folder.path),
                      children: (0, import_jsx_runtime6.jsxs)(Flex, {
                        tag: "h2",
                        direction: "column",
                        alignItems: "start",
                        maxWidth: "100%",
                        children: [
                          (0, import_jsx_runtime6.jsxs)(TypographyMaxWidth, {
                            fontWeight: "semiBold",
                            ellipsis: true,
                            textColor: "neutral800",
                            children: [
                              folder.name,
                              (0, import_jsx_runtime6.jsx)(VisuallyHidden, {
                                children: "-"
                              })
                            ]
                          }),
                          (0, import_jsx_runtime6.jsx)(TypographyMaxWidth, {
                            tag: "span",
                            textColor: "neutral600",
                            variant: "pi",
                            ellipsis: true,
                            children: formatMessage({
                              id: getTrad("list.folder.subtitle"),
                              defaultMessage: "{folderCount, plural, =0 {# folder} one {# folder} other {# folders}}, {filesCount, plural, =0 {# asset} one {# asset} other {# assets}}"
                            }, {
                              folderCount: (_a2 = folder.children) == null ? void 0 : _a2.count,
                              filesCount: (_b2 = folder.files) == null ? void 0 : _b2.count
                            })
                          })
                        ]
                      })
                    })
                  })
                })
              }, `folder-${folder.id}`);
            })
          }),
          assetCount > 0 && folderCount > 0 && (0, import_jsx_runtime6.jsx)(Box, {
            paddingTop: 6,
            children: (0, import_jsx_runtime6.jsx)(Divider, {})
          }),
          assetCount > 0 && (0, import_jsx_runtime6.jsx)(Box, {
            paddingTop: 6,
            children: (0, import_jsx_runtime6.jsx)(AssetGridList, {
              allowedTypes,
              size: "S",
              assets,
              onSelectAsset,
              selectedAssets,
              onEditAsset,
              title: (!isSearchingOrFiltering || isSearchingOrFiltering && folderCount > 0) && queryObject.page === 1 && formatMessage({
                id: getTrad("list.assets.title"),
                defaultMessage: "Assets ({count})"
              }, {
                count: assetCount
              }) || ""
            })
          })
        ]
      }),
      pagination.pageCount > 0 && (0, import_jsx_runtime6.jsxs)(Flex, {
        justifyContent: "space-between",
        paddingTop: 4,
        position: "relative",
        zIndex: 1,
        children: [
          (0, import_jsx_runtime6.jsx)(PageSize, {
            pageSize: queryObject.pageSize,
            onChangePageSize
          }),
          (0, import_jsx_runtime6.jsx)(PaginationFooter, {
            activePage: queryObject.page,
            onChangePage,
            pagination
          })
        ]
      })
    ]
  });
};

// node_modules/@strapi/upload/dist/admin/components/AssetDialog/DialogFooter.mjs
var import_jsx_runtime7 = __toESM(require_jsx_runtime(), 1);
var DialogFooter = ({ onClose, onValidate }) => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime7.jsxs)(Modal.Footer, {
    children: [
      (0, import_jsx_runtime7.jsx)(Button, {
        onClick: onClose,
        variant: "tertiary",
        children: formatMessage({
          id: "app.components.Button.cancel",
          defaultMessage: "Cancel"
        })
      }),
      onValidate && (0, import_jsx_runtime7.jsx)(Button, {
        onClick: onValidate,
        children: formatMessage({
          id: "global.finish",
          defaultMessage: "Finish"
        })
      })
    ]
  });
};

// node_modules/@strapi/upload/dist/admin/components/AssetDialog/SelectedStep/SelectedStep.mjs
var import_jsx_runtime8 = __toESM(require_jsx_runtime(), 1);
var import_qs5 = __toESM(require_lib(), 1);
var SelectedStep = ({ selectedAssets, onSelectAsset, onReorderAsset }) => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime8.jsxs)(Flex, {
    direction: "column",
    alignItems: "stretch",
    gap: 4,
    children: [
      (0, import_jsx_runtime8.jsxs)(Flex, {
        gap: 0,
        direction: "column",
        alignItems: "start",
        children: [
          (0, import_jsx_runtime8.jsx)(Typography, {
            variant: "pi",
            fontWeight: "bold",
            textColor: "neutral800",
            children: formatMessage({
              id: getTrad("list.assets.to-upload"),
              defaultMessage: "{number, plural, =0 {No asset} one {1 asset} other {# assets}} ready to upload"
            }, {
              number: selectedAssets.length
            })
          }),
          (0, import_jsx_runtime8.jsx)(Typography, {
            variant: "pi",
            textColor: "neutral600",
            children: formatMessage({
              id: getTrad("modal.upload-list.sub-header-subtitle"),
              defaultMessage: "Manage the assets before adding them to the Media Library"
            })
          })
        ]
      }),
      (0, import_jsx_runtime8.jsx)(AssetGridList, {
        size: "S",
        assets: selectedAssets,
        onSelectAsset,
        selectedAssets,
        onReorderAsset
      })
    ]
  });
};

// node_modules/@strapi/upload/dist/admin/components/AssetDialog/AssetDialog.mjs
var LoadingBody = dt(Flex)`
  /* 80px are coming from the Tabs component that is not included in the ModalBody */
  min-height: ${() => `calc(60vh + 8rem)`};
`;
var AssetContent = ({ allowedTypes = [], folderId = null, onClose, onAddAsset, onAddFolder, onChangeFolder, onValidate, multiple = false, initiallySelectedAssets = [], trackedLocation }) => {
  const [assetToEdit, setAssetToEdit] = React5.useState(void 0);
  const [folderToEdit, setFolderToEdit] = React5.useState(void 0);
  const { formatMessage } = useIntl();
  const { canRead, canCreate, isLoading: isLoadingPermissions, canUpdate, canCopyLink, canDownload } = useMediaLibraryPermissions();
  const [{ queryObject }, { onChangeFilters, onChangePage, onChangePageSize, onChangeSort, onChangeSearch, onChangeFolder: onChangeFolderParam }] = useModalQueryParams({
    folder: folderId
  });
  const { data: { pagination, results: assets } = {}, isLoading: isLoadingAssets, error: errorAssets } = useAssets({
    skipWhen: !canRead,
    query: queryObject
  });
  const { data: folders, isLoading: isLoadingFolders, error: errorFolders } = useFolders({
    enabled: canRead && !containsAssetFilter(queryObject) && (pagination == null ? void 0 : pagination.page) === 1,
    query: queryObject
  });
  const [selectedAssets, { selectOne, selectOnly, setSelections, selectMultiple, deselectMultiple }] = useSelectionState([
    "id"
  ], initiallySelectedAssets);
  const handleSelectAllAssets = () => {
    const allowedAssets = getAllowedFiles(allowedTypes, assets);
    if (!multiple) {
      return void 0;
    }
    const alreadySelected = allowedAssets.filter((asset) => selectedAssets.findIndex((selectedAsset) => selectedAsset.id === asset.id) !== -1);
    if (alreadySelected.length > 0) {
      deselectMultiple(alreadySelected);
    } else {
      selectMultiple(allowedAssets);
    }
  };
  const handleSelectAsset = (asset) => {
    return multiple ? selectOne(asset) : selectOnly(asset);
  };
  const isLoading = isLoadingPermissions || isLoadingAssets || isLoadingFolders;
  const hasError = errorAssets || errorFolders;
  const [activeTab, setActiveTab] = React5.useState(selectedAssets.length > 0 ? "selected" : "browse");
  if (isLoading) {
    return (0, import_jsx_runtime9.jsxs)(import_jsx_runtime9.Fragment, {
      children: [
        (0, import_jsx_runtime9.jsx)(Modal.Header, {
          children: (0, import_jsx_runtime9.jsx)(Modal.Title, {
            children: formatMessage({
              id: getTrad("header.actions.add-assets"),
              defaultMessage: "Add new assets"
            })
          })
        }),
        (0, import_jsx_runtime9.jsx)(LoadingBody, {
          justifyContent: "center",
          paddingTop: 4,
          paddingBottom: 4,
          children: (0, import_jsx_runtime9.jsx)(Loader, {
            children: formatMessage({
              id: getTrad("content.isLoading"),
              defaultMessage: "Content is loading."
            })
          })
        }),
        (0, import_jsx_runtime9.jsx)(DialogFooter, {
          onClose
        })
      ]
    });
  }
  if (hasError) {
    return (0, import_jsx_runtime9.jsxs)(import_jsx_runtime9.Fragment, {
      children: [
        (0, import_jsx_runtime9.jsx)(Modal.Header, {
          children: (0, import_jsx_runtime9.jsx)(Modal.Title, {
            children: formatMessage({
              id: getTrad("header.actions.add-assets"),
              defaultMessage: "Add new assets"
            })
          })
        }),
        (0, import_jsx_runtime9.jsx)(Page.Error, {}),
        (0, import_jsx_runtime9.jsx)(DialogFooter, {
          onClose
        })
      ]
    });
  }
  if (!canRead) {
    return (0, import_jsx_runtime9.jsxs)(import_jsx_runtime9.Fragment, {
      children: [
        (0, import_jsx_runtime9.jsx)(Modal.Header, {
          children: (0, import_jsx_runtime9.jsx)(Modal.Title, {
            children: formatMessage({
              id: getTrad("header.actions.add-assets"),
              defaultMessage: "Add new assets"
            })
          })
        }),
        (0, import_jsx_runtime9.jsx)(Page.NoPermissions, {}),
        (0, import_jsx_runtime9.jsx)(DialogFooter, {
          onClose
        })
      ]
    });
  }
  if (assetToEdit) {
    return (0, import_jsx_runtime9.jsx)(EditAssetContent, {
      onClose: () => setAssetToEdit(void 0),
      asset: assetToEdit,
      canUpdate,
      canCopyLink,
      canDownload,
      trackedLocation
    });
  }
  if (folderToEdit) {
    return (0, import_jsx_runtime9.jsx)(EditFolderContent, {
      folder: folderToEdit,
      onClose: () => setFolderToEdit(void 0),
      location: "content-manager",
      parentFolderId: queryObject == null ? void 0 : queryObject.folder
    });
  }
  const handleMoveItem = (hoverIndex, destIndex) => {
    const offset = destIndex - hoverIndex;
    const orderedAssetsClone = selectedAssets.slice();
    const nextAssets = moveElement(orderedAssetsClone, hoverIndex, offset);
    setSelections(nextAssets);
  };
  const handleFolderChange = (folderId2, folderPath) => {
    onChangeFolder(folderId2);
    if (onChangeFolderParam) {
      onChangeFolderParam(folderId2, folderPath);
    }
  };
  return (0, import_jsx_runtime9.jsxs)(import_jsx_runtime9.Fragment, {
    children: [
      (0, import_jsx_runtime9.jsx)(Modal.Header, {
        children: (0, import_jsx_runtime9.jsx)(Modal.Title, {
          children: formatMessage({
            id: getTrad("header.actions.add-assets"),
            defaultMessage: "Add new assets"
          })
        })
      }),
      (0, import_jsx_runtime9.jsxs)(TabsRoot, {
        variant: "simple",
        value: activeTab,
        onValueChange: setActiveTab,
        children: [
          (0, import_jsx_runtime9.jsxs)(Flex, {
            paddingLeft: 8,
            paddingRight: 8,
            paddingTop: 6,
            justifyContent: "space-between",
            children: [
              (0, import_jsx_runtime9.jsxs)(Tabs.List, {
                children: [
                  (0, import_jsx_runtime9.jsx)(Tabs.Trigger, {
                    value: "browse",
                    children: formatMessage({
                      id: getTrad("modal.nav.browse"),
                      defaultMessage: "Browse"
                    })
                  }),
                  (0, import_jsx_runtime9.jsxs)(Tabs.Trigger, {
                    value: "selected",
                    children: [
                      formatMessage({
                        id: getTrad("modal.header.select-files"),
                        defaultMessage: "Selected files"
                      }),
                      (0, import_jsx_runtime9.jsx)(Badge, {
                        marginLeft: 2,
                        children: selectedAssets.length
                      })
                    ]
                  })
                ]
              }),
              (0, import_jsx_runtime9.jsxs)(Flex, {
                gap: 2,
                children: [
                  (0, import_jsx_runtime9.jsx)(Button, {
                    variant: "secondary",
                    onClick: () => onAddFolder({
                      folderId: queryObject == null ? void 0 : queryObject.folder
                    }),
                    children: formatMessage({
                      id: getTrad("modal.upload-list.sub-header.add-folder"),
                      defaultMessage: "Add folder"
                    })
                  }),
                  (0, import_jsx_runtime9.jsx)(Button, {
                    onClick: () => onAddAsset({
                      folderId: queryObject == null ? void 0 : queryObject.folder
                    }),
                    children: formatMessage({
                      id: getTrad("modal.upload-list.sub-header.button"),
                      defaultMessage: "Add more assets"
                    })
                  })
                ]
              })
            ]
          }),
          (0, import_jsx_runtime9.jsx)(Divider, {}),
          (0, import_jsx_runtime9.jsxs)(Modal.Body, {
            children: [
              (0, import_jsx_runtime9.jsx)(Tabs.Content, {
                value: "browse",
                children: (0, import_jsx_runtime9.jsx)(BrowseStep, {
                  allowedTypes,
                  assets,
                  canCreate,
                  canRead,
                  folders,
                  onSelectAsset: handleSelectAsset,
                  selectedAssets,
                  multiple,
                  onSelectAllAsset: handleSelectAllAssets,
                  onEditAsset: setAssetToEdit,
                  onEditFolder: setFolderToEdit,
                  pagination,
                  queryObject,
                  onAddAsset,
                  onChangeFilters: (filters) => onChangeFilters(filters),
                  onChangeFolder: handleFolderChange,
                  onChangePage,
                  onChangePageSize,
                  onChangeSort: (sort) => onChangeSort(sort),
                  onChangeSearch
                })
              }),
              (0, import_jsx_runtime9.jsx)(Tabs.Content, {
                value: "selected",
                children: (0, import_jsx_runtime9.jsx)(SelectedStep, {
                  selectedAssets,
                  onSelectAsset: handleSelectAsset,
                  onReorderAsset: handleMoveItem
                })
              })
            ]
          })
        ]
      }),
      (0, import_jsx_runtime9.jsx)(DialogFooter, {
        onClose,
        onValidate: () => onValidate(selectedAssets)
      })
    ]
  });
};
var AssetDialog = ({ open = false, onClose, ...restProps }) => {
  return (0, import_jsx_runtime9.jsx)(Modal.Root, {
    open,
    onOpenChange: onClose,
    children: (0, import_jsx_runtime9.jsx)(Modal.Content, {
      children: (0, import_jsx_runtime9.jsx)(AssetContent, {
        onClose,
        ...restProps
      })
    })
  });
};
var TabsRoot = dt(Tabs.Root)`
  display: flex;
  flex-direction: column;
  overflow: hidden;
`;

// node_modules/@strapi/upload/dist/admin/components/MediaLibraryDialog/MediaLibraryDialog.mjs
var STEPS = {
  AssetSelect: "SelectAsset",
  AssetUpload: "UploadAsset",
  FolderCreate: "FolderCreate"
};
var MediaLibraryDialog = ({ onClose, onSelectAssets, allowedTypes = [
  "files",
  "images",
  "videos",
  "audios"
] }) => {
  const [step, setStep] = React6.useState(STEPS.AssetSelect);
  const [folderId, setFolderId] = React6.useState(null);
  switch (step) {
    case STEPS.AssetSelect:
      return (0, import_jsx_runtime10.jsx)(AssetDialog, {
        allowedTypes,
        folderId,
        open: true,
        onClose,
        onValidate: onSelectAssets,
        onAddAsset: () => setStep(STEPS.AssetUpload),
        onAddFolder: () => setStep(STEPS.FolderCreate),
        onChangeFolder: (folderId2) => setFolderId(folderId2),
        multiple: true
      });
    case STEPS.FolderCreate:
      return (0, import_jsx_runtime10.jsx)(EditFolderDialog, {
        open: true,
        onClose: () => setStep(STEPS.AssetSelect),
        parentFolderId: folderId
      });
    default:
      return (0, import_jsx_runtime10.jsx)(UploadAssetDialog, {
        open: true,
        onClose: () => setStep(STEPS.AssetSelect),
        folderId
      });
  }
};

// node_modules/@strapi/upload/dist/admin/components/MediaLibraryInput/MediaLibraryInput.mjs
var import_jsx_runtime15 = __toESM(require_jsx_runtime(), 1);
var React9 = __toESM(require_react(), 1);
var import_qs10 = __toESM(require_lib(), 1);

// node_modules/@strapi/upload/dist/admin/components/MediaLibraryInput/Carousel/CarouselAssets.mjs
var import_jsx_runtime14 = __toESM(require_jsx_runtime(), 1);
var React8 = __toESM(require_react(), 1);

// node_modules/@strapi/upload/dist/admin/components/MediaLibraryInput/Carousel/CarouselAsset.mjs
var import_jsx_runtime11 = __toESM(require_jsx_runtime(), 1);
var import_qs7 = __toESM(require_lib(), 1);
var DocAsset = dt(Flex)`
  background: linear-gradient(180deg, #ffffff 0%, #f6f6f9 121.48%);
`;
var VideoPreviewWrapper = dt(Box)`
  canvas,
  video {
    max-width: 100%;
    height: 124px;
  }
`;
var AudioPreviewWrapper = dt(Box)`
  canvas,
  audio {
    max-width: 100%;
  }
`;
var CarouselAsset = ({ asset }) => {
  var _a, _b, _c, _d;
  if ((_a = asset.mime) == null ? void 0 : _a.includes(AssetType.Video)) {
    return (0, import_jsx_runtime11.jsx)(VideoPreviewWrapper, {
      height: "100%",
      children: (0, import_jsx_runtime11.jsx)(VideoPreview, {
        url: createAssetUrl(asset, true),
        mime: asset.mime,
        alt: asset.alternativeText || asset.name
      })
    });
  }
  if ((_b = asset.mime) == null ? void 0 : _b.includes(AssetType.Audio)) {
    return (0, import_jsx_runtime11.jsx)(AudioPreviewWrapper, {
      children: (0, import_jsx_runtime11.jsx)(AudioPreview, {
        url: createAssetUrl(asset, true),
        alt: asset.alternativeText || asset.name
      })
    });
  }
  if ((_c = asset.mime) == null ? void 0 : _c.includes(AssetType.Image)) {
    const assetUrl = createAssetUrl(asset, true);
    if (!assetUrl) return null;
    const cacheBustedUrl = `${assetUrl}${assetUrl.includes("?") ? "&" : "?"}updatedAt=${asset.updatedAt}`;
    return (0, import_jsx_runtime11.jsx)(Box, {
      tag: "img",
      maxHeight: "100%",
      maxWidth: "100%",
      src: cacheBustedUrl,
      alt: asset.alternativeText || asset.name
    });
  }
  return (0, import_jsx_runtime11.jsx)(DocAsset, {
    width: "100%",
    height: "100%",
    justifyContent: "center",
    hasRadius: true,
    children: ((_d = asset.ext) == null ? void 0 : _d.includes("pdf")) ? (0, import_jsx_runtime11.jsx)(ForwardRef$3p, {
      "aria-label": asset.alternativeText || asset.name,
      width: "24px",
      height: "32px"
    }) : (0, import_jsx_runtime11.jsx)(ForwardRef$3v, {
      "aria-label": asset.alternativeText || asset.name,
      width: "24px",
      height: "32px"
    })
  });
};

// node_modules/@strapi/upload/dist/admin/components/MediaLibraryInput/Carousel/CarouselAssetActions.mjs
var import_jsx_runtime12 = __toESM(require_jsx_runtime(), 1);
var import_qs8 = __toESM(require_lib(), 1);
var CarouselAssetActions = ({ asset, onDeleteAsset, onAddAsset, onEditAsset }) => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime12.jsxs)(CarouselActions, {
    children: [
      onAddAsset && (0, import_jsx_runtime12.jsx)(IconButton, {
        label: formatMessage({
          id: getTrad("control-card.add"),
          defaultMessage: "Add"
        }),
        onClick: () => onAddAsset(asset),
        children: (0, import_jsx_runtime12.jsx)(ForwardRef$1h, {})
      }),
      (0, import_jsx_runtime12.jsx)(CopyLinkButton, {
        url: prefixFileUrlWithBackendUrl(asset.url)
      }),
      onDeleteAsset && (0, import_jsx_runtime12.jsx)(IconButton, {
        label: formatMessage({
          id: "global.delete",
          defaultMessage: "Delete"
        }),
        onClick: () => onDeleteAsset(asset),
        children: (0, import_jsx_runtime12.jsx)(ForwardRef$j, {})
      }),
      onEditAsset && (0, import_jsx_runtime12.jsx)(IconButton, {
        label: formatMessage({
          id: getTrad("control-card.edit"),
          defaultMessage: "edit"
        }),
        onClick: onEditAsset,
        children: (0, import_jsx_runtime12.jsx)(ForwardRef$1v, {})
      })
    ]
  });
};

// node_modules/@strapi/upload/dist/admin/components/MediaLibraryInput/Carousel/EmptyStateAsset.mjs
var import_jsx_runtime13 = __toESM(require_jsx_runtime(), 1);
var React7 = __toESM(require_react(), 1);
var import_qs9 = __toESM(require_lib(), 1);
var TextAlignTypography = dt(Typography)`
  align-items: center;
`;
var EmptyStateAsset = ({ disabled = false, onClick, onDropAsset }) => {
  const { formatMessage } = useIntl();
  const [dragOver, setDragOver] = React7.useState(false);
  const handleDragEnter = (e) => {
    e.preventDefault();
    setDragOver(true);
  };
  const handleDragLeave = (e) => {
    if (!e.currentTarget.contains(e.relatedTarget)) {
      setDragOver(false);
    }
  };
  const handleDragOver = (e) => {
    e.preventDefault();
  };
  const handleDrop = (e) => {
    var _a;
    e.preventDefault();
    if ((_a = e == null ? void 0 : e.dataTransfer) == null ? void 0 : _a.files) {
      const files = e.dataTransfer.files;
      const assets = [];
      for (let i = 0; i < files.length; i++) {
        const file = files.item(i);
        if (file) {
          const asset = rawFileToAsset(file, AssetSource.Computer);
          assets.push(asset);
        }
      }
      onDropAsset(assets);
    }
    setDragOver(false);
  };
  return (0, import_jsx_runtime13.jsxs)(Flex, {
    borderStyle: dragOver ? "dashed" : void 0,
    borderWidth: dragOver ? "1px" : void 0,
    borderColor: dragOver ? "primary600" : void 0,
    direction: "column",
    justifyContent: "center",
    alignItems: "center",
    height: "100%",
    width: "100%",
    tag: "button",
    type: "button",
    disabled,
    onClick,
    onDragEnter: handleDragEnter,
    onDragLeave: handleDragLeave,
    onDragOver: handleDragOver,
    onDrop: handleDrop,
    gap: 3,
    style: {
      cursor: disabled ? "not-allowed" : "pointer"
    },
    children: [
      (0, import_jsx_runtime13.jsx)(ForwardRef$1f, {
        "aria-hidden": true,
        width: "3.2rem",
        height: "3.2rem",
        fill: disabled ? "neutral400" : "primary600"
      }),
      (0, import_jsx_runtime13.jsx)(TextAlignTypography, {
        variant: "pi",
        fontWeight: "bold",
        textColor: "neutral600",
        style: {
          textAlign: "center"
        },
        tag: "span",
        children: formatMessage({
          id: getTrad("mediaLibraryInput.placeholder"),
          defaultMessage: "Click to add an asset or drag and drop one in this area"
        })
      })
    ]
  });
};

// node_modules/@strapi/upload/dist/admin/components/MediaLibraryInput/Carousel/CarouselAssets.mjs
var CarouselAssets = React8.forwardRef(({ assets, disabled = false, error, hint, label, labelAction, onAddAsset, onDeleteAsset, onDeleteAssetFromMediaLibrary, onDropAsset, onEditAsset, onNext, onPrevious, required = false, selectedAssetIndex, trackedLocation }, forwardedRef) => {
  const { formatMessage } = useIntl();
  const [isEditingAsset, setIsEditingAsset] = React8.useState(false);
  const currentAsset = assets[selectedAssetIndex];
  return (0, import_jsx_runtime14.jsxs)(import_jsx_runtime14.Fragment, {
    children: [
      (0, import_jsx_runtime14.jsx)(CarouselInput, {
        ref: forwardedRef,
        label,
        labelAction,
        secondaryLabel: currentAsset == null ? void 0 : currentAsset.name,
        selectedSlide: selectedAssetIndex,
        previousLabel: formatMessage({
          id: getTrad("mediaLibraryInput.actions.previousSlide"),
          defaultMessage: "Previous slide"
        }),
        nextLabel: formatMessage({
          id: getTrad("mediaLibraryInput.actions.nextSlide"),
          defaultMessage: "Next slide"
        }),
        onNext,
        onPrevious,
        hint,
        error,
        required,
        actions: currentAsset ? (0, import_jsx_runtime14.jsx)(CarouselAssetActions, {
          asset: currentAsset,
          onDeleteAsset: disabled ? void 0 : onDeleteAsset,
          onAddAsset: disabled ? void 0 : onAddAsset,
          onEditAsset: onEditAsset ? () => setIsEditingAsset(true) : void 0
        }) : void 0,
        children: assets.length === 0 ? (0, import_jsx_runtime14.jsx)(CarouselSlide, {
          label: formatMessage({
            id: getTrad("mediaLibraryInput.slideCount"),
            defaultMessage: "{n} of {m} slides"
          }, {
            n: 1,
            m: 1
          }),
          children: (0, import_jsx_runtime14.jsx)(EmptyStateAsset, {
            disabled,
            onClick: onAddAsset,
            onDropAsset
          })
        }) : assets.map((asset, index4) => (0, import_jsx_runtime14.jsx)(CarouselSlide, {
          label: formatMessage({
            id: getTrad("mediaLibraryInput.slideCount"),
            defaultMessage: "{n} of {m} slides"
          }, {
            n: index4 + 1,
            m: assets.length
          }),
          children: (0, import_jsx_runtime14.jsx)(CarouselAsset, {
            asset
          })
        }, asset.id))
      }),
      (0, import_jsx_runtime14.jsx)(EditAssetDialog, {
        open: isEditingAsset,
        onClose: (editedAsset) => {
          setIsEditingAsset(false);
          if (editedAsset === null) {
            onDeleteAssetFromMediaLibrary();
          }
          if (editedAsset && typeof editedAsset !== "boolean") {
            onEditAsset == null ? void 0 : onEditAsset(editedAsset);
          }
        },
        asset: currentAsset,
        canUpdate: true,
        canCopyLink: true,
        canDownload: true,
        trackedLocation
      })
    ]
  });
});

// node_modules/@strapi/upload/dist/admin/components/MediaLibraryInput/MediaLibraryInput.mjs
var STEPS2 = {
  AssetSelect: "SelectAsset",
  AssetUpload: "UploadAsset",
  FolderCreate: "FolderCreate"
};
var MediaLibraryInput = React9.forwardRef(({ attribute: { allowedTypes = null, multiple = false } = {}, label, hint, disabled = false, labelAction = void 0, name: name2, required = false }, forwardedRef) => {
  const { formatMessage } = useIntl();
  const { onChange, value, error } = useField(name2);
  const [uploadedFiles, setUploadedFiles] = React9.useState([]);
  const [step, setStep] = React9.useState(void 0);
  const [selectedIndex, setSelectedIndex] = React9.useState(0);
  const [droppedAssets, setDroppedAssets] = React9.useState();
  const [folderId, setFolderId] = React9.useState(null);
  const { toggleNotification } = useNotification();
  React9.useEffect(() => {
    if (step === void 0) {
      setUploadedFiles([]);
    }
  }, [
    step
  ]);
  let selectedAssets = [];
  if (Array.isArray(value)) {
    selectedAssets = value;
  } else if (value) {
    selectedAssets = [
      value
    ];
  }
  const handleValidation = (nextSelectedAssets) => {
    const value2 = multiple ? nextSelectedAssets : nextSelectedAssets[0];
    onChange(name2, value2);
    setStep(void 0);
  };
  const handleDeleteAssetFromMediaLibrary = () => {
    let nextValue;
    if (multiple) {
      const nextSelectedAssets = selectedAssets.filter((_, assetIndex) => assetIndex !== selectedIndex);
      nextValue = nextSelectedAssets.length > 0 ? nextSelectedAssets : null;
    } else {
      nextValue = null;
    }
    const value2 = nextValue;
    onChange(name2, value2);
    setSelectedIndex(0);
  };
  const handleDeleteAsset = (asset) => {
    let nextValue;
    if (multiple) {
      const nextSelectedAssets = selectedAssets.filter((prevAsset) => prevAsset.id !== asset.id);
      nextValue = nextSelectedAssets.length > 0 ? nextSelectedAssets : null;
    } else {
      nextValue = null;
    }
    onChange(name2, nextValue);
    setSelectedIndex(0);
  };
  const handleAssetEdit = (asset) => {
    const nextSelectedAssets = selectedAssets.map((prevAsset) => prevAsset.id === asset.id ? asset : prevAsset);
    onChange(name2, multiple ? nextSelectedAssets : nextSelectedAssets[0]);
  };
  const validateAssetsTypes = (assets, callback) => {
    const allowedAssets = getAllowedFiles(allowedTypes, assets);
    if (allowedAssets.length > 0) {
      callback(allowedAssets);
    } else {
      toggleNotification({
        type: "danger",
        timeout: 4e3,
        message: formatMessage({
          id: getTrad("input.notification.not-supported"),
          defaultMessage: `You can't upload this type of file.`
        }, {
          fileTypes: (allowedTypes ?? []).join(",")
        })
      });
    }
  };
  const handleAssetDrop = (assets) => {
    validateAssetsTypes(assets, (allowedAssets) => {
      setDroppedAssets(allowedAssets);
      setStep(STEPS2.AssetUpload);
    });
  };
  if (multiple && selectedAssets.length > 0) {
    label = `${label} (${selectedIndex + 1} / ${selectedAssets.length})`;
  }
  const handleNext = () => {
    setSelectedIndex((current) => current < selectedAssets.length - 1 ? current + 1 : 0);
  };
  const handlePrevious = () => {
    setSelectedIndex((current) => current > 0 ? current - 1 : selectedAssets.length - 1);
  };
  const handleFilesUploadSucceeded = (uploadedFiles2) => {
    setUploadedFiles((prev) => [
      ...prev,
      ...uploadedFiles2
    ]);
  };
  let initiallySelectedAssets = selectedAssets;
  if (uploadedFiles.length > 0) {
    const allowedUploadedFiles = getAllowedFiles(allowedTypes, uploadedFiles);
    initiallySelectedAssets = multiple ? [
      ...allowedUploadedFiles,
      ...selectedAssets
    ] : [
      allowedUploadedFiles[0]
    ];
  }
  return (0, import_jsx_runtime15.jsxs)(import_jsx_runtime15.Fragment, {
    children: [
      (0, import_jsx_runtime15.jsx)(CarouselAssets, {
        ref: forwardedRef,
        assets: selectedAssets,
        disabled,
        label,
        labelAction,
        onDeleteAsset: handleDeleteAsset,
        onDeleteAssetFromMediaLibrary: handleDeleteAssetFromMediaLibrary,
        onAddAsset: () => setStep(STEPS2.AssetSelect),
        onDropAsset: handleAssetDrop,
        onEditAsset: handleAssetEdit,
        onNext: handleNext,
        onPrevious: handlePrevious,
        error,
        hint,
        required,
        selectedAssetIndex: selectedIndex,
        trackedLocation: "content-manager"
      }),
      step === STEPS2.AssetSelect && (0, import_jsx_runtime15.jsx)(AssetDialog, {
        allowedTypes,
        initiallySelectedAssets,
        folderId,
        onClose: () => {
          setStep(void 0);
          setFolderId(null);
        },
        open: step === STEPS2.AssetSelect,
        onValidate: handleValidation,
        multiple,
        onAddAsset: () => setStep(STEPS2.AssetUpload),
        onAddFolder: () => setStep(STEPS2.FolderCreate),
        onChangeFolder: (folder) => setFolderId(folder),
        trackedLocation: "content-manager"
      }),
      step === STEPS2.AssetUpload && (0, import_jsx_runtime15.jsx)(UploadAssetDialog, {
        open: step === STEPS2.AssetUpload,
        onClose: () => setStep(STEPS2.AssetSelect),
        initialAssetsToAdd: droppedAssets,
        addUploadedFiles: handleFilesUploadSucceeded,
        trackedLocation: "content-manager",
        folderId,
        validateAssetsTypes
      }),
      step === STEPS2.FolderCreate && (0, import_jsx_runtime15.jsx)(EditFolderDialog, {
        open: step === STEPS2.FolderCreate,
        onClose: () => setStep(STEPS2.AssetSelect),
        parentFolderId: folderId
      })
    ]
  });
});

// node_modules/@strapi/upload/dist/admin/index.mjs
var import_qs11 = __toESM(require_lib(), 1);

// node_modules/@strapi/upload/dist/admin/utils/prefixPluginTranslations.mjs
var prefixPluginTranslations3 = (trad, pluginId5) => {
  if (!pluginId5) {
    throw new TypeError("pluginId can't be empty");
  }
  return Object.keys(trad).reduce((acc, current) => {
    acc[`${pluginId5}.${current}`] = trad[current];
    return acc;
  }, {});
};

// node_modules/@strapi/upload/dist/admin/index.mjs
function __variableDynamicImportRuntime2__(path) {
  switch (path) {
    case "./translations/ca.json":
      return import("./ca.json-SGLYOKFN.js");
    case "./translations/de.json":
      return import("./de.json-ZBRIRFBP.js");
    case "./translations/dk.json":
      return import("./dk.json-V2MWWTY7.js");
    case "./translations/en.json":
      return import("./en.json-7E4TPUWN.js");
    case "./translations/es.json":
      return import("./es.json-W3AZZ4NL.js");
    case "./translations/fr.json":
      return import("./fr.json-5MRAYCZS.js");
    case "./translations/he.json":
      return import("./he.json-G7H73RQW.js");
    case "./translations/it.json":
      return import("./it.json-ZDRZ7F5X.js");
    case "./translations/ja.json":
      return import("./ja.json-A5FGOGTR.js");
    case "./translations/ko.json":
      return import("./ko.json-TUNMS3U3.js");
    case "./translations/ms.json":
      return import("./ms.json-4TMLK7KR.js");
    case "./translations/pl.json":
      return import("./pl.json-WI3YPLLQ.js");
    case "./translations/pt-BR.json":
      return import("./pt-BR.json-IHUN7DZM.js");
    case "./translations/pt.json":
      return import("./pt.json-3PC4SO3V.js");
    case "./translations/ru.json":
      return import("./ru.json-E3NA2AA2.js");
    case "./translations/sk.json":
      return import("./sk.json-CIND4NQ3.js");
    case "./translations/th.json":
      return import("./th.json-FIKGWXOL.js");
    case "./translations/tr.json":
      return import("./tr.json-OVRXCZHU.js");
    case "./translations/uk.json":
      return import("./uk.json-WPUUSCPA.js");
    case "./translations/zh-Hans.json":
      return import("./zh-Hans.json-WPBNXHYL.js");
    case "./translations/zh.json":
      return import("./zh.json-BZJDH453.js");
    default:
      return new Promise(function(resolve, reject) {
        (typeof queueMicrotask === "function" ? queueMicrotask : setTimeout)(
          reject.bind(null, new Error("Unknown variable dynamic import: " + path))
        );
      });
  }
}
var name = pluginPkg.strapi.name;
var admin2 = {
  register(app) {
    app.addMenuLink({
      to: `plugins/${pluginId2}`,
      icon: ForwardRef$2J,
      intlLabel: {
        id: `${pluginId2}.plugin.name`,
        defaultMessage: "Media Library"
      },
      permissions: PERMISSIONS3.main,
      Component: () => import("./App-HRHBZIP7.js").then((mod) => ({
        default: mod.Upload
      })),
      position: 4
    });
    app.addSettingsLink("global", {
      id: "media-library-settings",
      to: "media-library",
      intlLabel: {
        id: getTrad("plugin.name"),
        defaultMessage: "Media Library"
      },
      async Component() {
        const { ProtectedSettingsPage } = await import("./SettingsPage-BDRHNTLG.js");
        return {
          default: ProtectedSettingsPage
        };
      },
      permissions: PERMISSIONS3.settings
    });
    app.addFields({
      type: "media",
      Component: MediaLibraryInput
    });
    app.addComponents([
      {
        name: "media-library",
        Component: MediaLibraryDialog
      }
    ]);
    app.registerPlugin({
      id: pluginId2,
      name
    });
  },
  async registerTrads({ locales }) {
    const importedTrads = await Promise.all(locales.map((locale) => {
      return __variableDynamicImportRuntime2__(`./translations/${locale}.json`).then(({ default: data }) => {
        return {
          data: prefixPluginTranslations3(data, pluginId2),
          locale
        };
      }).catch(() => {
        return {
          data: {},
          locale
        };
      });
    }));
    return Promise.resolve(importedTrads);
  }
};

// node_modules/@strapi/i18n/dist/admin/index.mjs
var import_get3 = __toESM(require_get(), 1);

// node_modules/@strapi/i18n/dist/admin/components/CheckboxConfirmation.mjs
var import_jsx_runtime16 = __toESM(require_jsx_runtime(), 1);
var React10 = __toESM(require_react(), 1);
var TextAlignTypography2 = dt(Typography)`
  text-align: center;
`;
var CheckboxConfirmation = ({ description, isCreating = false, intlLabel, name: name2, onChange, value }) => {
  const { formatMessage } = useIntl();
  const [isOpen, setIsOpen] = React10.useState(false);
  const handleChange = (value2) => {
    if (isCreating || value2) {
      return onChange({
        target: {
          name: name2,
          value: value2,
          type: "checkbox"
        }
      });
    }
    if (!value2) {
      return setIsOpen(true);
    }
    return null;
  };
  const handleConfirm = () => {
    onChange({
      target: {
        name: name2,
        value: false,
        type: "checkbox"
      }
    });
  };
  const label = intlLabel.id ? formatMessage({
    id: intlLabel.id,
    defaultMessage: intlLabel.defaultMessage
  }, {
    ...intlLabel.values
  }) : name2;
  const hint = description ? formatMessage({
    id: description.id,
    defaultMessage: description.defaultMessage
  }, {
    ...description.values
  }) : "";
  return (0, import_jsx_runtime16.jsxs)(Dialog.Root, {
    open: isOpen,
    onOpenChange: setIsOpen,
    children: [
      (0, import_jsx_runtime16.jsxs)(Field.Root, {
        hint,
        name: name2,
        children: [
          (0, import_jsx_runtime16.jsx)(CheckboxImpl, {
            onCheckedChange: handleChange,
            checked: value,
            children: label
          }),
          (0, import_jsx_runtime16.jsx)(Field.Hint, {})
        ]
      }),
      (0, import_jsx_runtime16.jsxs)(Dialog.Content, {
        children: [
          (0, import_jsx_runtime16.jsx)(Dialog.Header, {
            children: formatMessage({
              id: getTranslation("CheckboxConfirmation.Modal.title"),
              defaultMessage: "Disable localization"
            })
          }),
          (0, import_jsx_runtime16.jsx)(Dialog.Body, {
            icon: (0, import_jsx_runtime16.jsx)(ForwardRef$3, {}),
            children: (0, import_jsx_runtime16.jsxs)(Flex, {
              direction: "column",
              alignItems: "stretch",
              gap: 2,
              children: [
                (0, import_jsx_runtime16.jsx)(Flex, {
                  justifyContent: "center",
                  children: (0, import_jsx_runtime16.jsx)(TextAlignTypography2, {
                    children: formatMessage({
                      id: getTranslation("CheckboxConfirmation.Modal.content"),
                      defaultMessage: "Disabling localization will engender the deletion of all your content but the one associated to your default locale (if existing)."
                    })
                  })
                }),
                (0, import_jsx_runtime16.jsx)(Flex, {
                  justifyContent: "center",
                  children: (0, import_jsx_runtime16.jsx)(Typography, {
                    fontWeight: "semiBold",
                    children: formatMessage({
                      id: getTranslation("CheckboxConfirmation.Modal.body"),
                      defaultMessage: "Do you want to disable it?"
                    })
                  })
                })
              ]
            })
          }),
          (0, import_jsx_runtime16.jsxs)(Dialog.Footer, {
            children: [
              (0, import_jsx_runtime16.jsx)(Dialog.Cancel, {
                children: (0, import_jsx_runtime16.jsx)(Button, {
                  variant: "tertiary",
                  children: formatMessage({
                    id: "components.popUpWarning.button.cancel",
                    defaultMessage: "No, cancel"
                  })
                })
              }),
              (0, import_jsx_runtime16.jsx)(Dialog.Action, {
                children: (0, import_jsx_runtime16.jsx)(Button, {
                  variant: "danger-light",
                  onClick: handleConfirm,
                  children: formatMessage({
                    id: getTranslation("CheckboxConfirmation.Modal.button-confirm"),
                    defaultMessage: "Yes, disable"
                  })
                })
              })
            ]
          })
        ]
      })
    ]
  });
};

// node_modules/@strapi/i18n/dist/admin/components/CMHeaderActions.mjs
var import_jsx_runtime18 = __toESM(require_jsx_runtime(), 1);
var React12 = __toESM(require_react(), 1);

// node_modules/@strapi/i18n/dist/admin/hooks/useI18n.mjs
var React11 = __toESM(require_react(), 1);

// node_modules/@strapi/i18n/dist/admin/utils/fields.mjs
var import_has = __toESM(require_has(), 1);
var LOCALIZED_FIELDS = [
  "biginteger",
  "boolean",
  "component",
  "date",
  "datetime",
  "decimal",
  "dynamiczone",
  "email",
  "enumeration",
  "float",
  "integer",
  "json",
  "media",
  "number",
  "password",
  "richtext",
  "blocks",
  "string",
  "text",
  "time"
];
var doesPluginOptionsHaveI18nLocalized = (opts) => (0, import_has.default)(opts, [
  "i18n",
  "localized"
]);

// node_modules/@strapi/i18n/dist/admin/utils/strings.mjs
var capitalize = (str) => str.charAt(0).toUpperCase() + str.slice(1);

// node_modules/@strapi/i18n/dist/admin/hooks/useI18n.mjs
var useI18n = () => {
  const params = useParams();
  const userPermissions = useAuth("useI18n", (state) => state.permissions);
  const actions = React11.useMemo(() => {
    const permissions = userPermissions.filter((permission) => permission.subject === params.slug);
    return permissions.reduce((acc, permission) => {
      var _a;
      const [actionShorthand] = permission.action.split(".").slice(-1);
      return {
        ...acc,
        [`can${capitalize(actionShorthand)}`]: ((_a = permission.properties) == null ? void 0 : _a.locales) ?? []
      };
    }, {
      canCreate: [],
      canRead: [],
      canUpdate: [],
      canDelete: [],
      canPublish: []
    });
  }, [
    params.slug,
    userPermissions
  ]);
  const { schema } = useDocument({
    // We can non-null assert these because below we skip the query if they are not present
    collectionType: params.collectionType,
    model: params.slug
  }, {
    skip: true
  });
  if (doesPluginOptionsHaveI18nLocalized(schema == null ? void 0 : schema.pluginOptions)) {
    return {
      hasI18n: schema.pluginOptions.i18n.localized,
      ...actions
    };
  }
  return {
    hasI18n: false,
    ...actions
  };
};

// node_modules/@strapi/i18n/dist/admin/services/relations.mjs
var relationsApi = i18nApi.injectEndpoints({
  overrideExisting: true,
  endpoints: (builder) => ({
    getManyDraftRelationCount: builder.query({
      query: ({ model, ...params }) => ({
        url: `/content-manager/collection-types/${model}/actions/countManyEntriesDraftRelations`,
        method: "GET",
        config: {
          params
        }
      }),
      transformResponse: (response) => response.data
    })
  })
});
var { useGetManyDraftRelationCountQuery } = relationsApi;

// node_modules/@strapi/i18n/dist/admin/utils/clean.mjs
var cleanData = (data, schema, components) => {
  const cleanedData = removeFields(data, [
    "createdAt",
    "createdBy",
    "updatedAt",
    "updatedBy",
    "id",
    "documentId",
    "publishedAt",
    "strapi_stage",
    "strapi_assignee",
    "locale",
    "status"
  ]);
  const cleanedDataWithoutPasswordAndRelation = recursiveRemoveFieldTypes(cleanedData, schema, components, [
    "relation",
    "password"
  ]);
  return cleanedDataWithoutPasswordAndRelation;
};
var removeFields = (data, fields) => {
  return Object.keys(data).reduce((acc, current) => {
    if (fields.includes(current)) {
      return acc;
    }
    acc[current] = data[current];
    return acc;
  }, {});
};
var recursiveRemoveFieldTypes = (data, schema, components, fields) => {
  return Object.keys(data).reduce((acc, current) => {
    const attribute = schema.attributes[current] ?? {
      type: void 0
    };
    if (fields.includes(attribute.type)) {
      return acc;
    }
    if (attribute.type === "dynamiczone") {
      acc[current] = data[current].map((componentValue, index4) => {
        const { id: _, ...rest } = recursiveRemoveFieldTypes(componentValue, components[componentValue.__component], components, fields);
        return {
          ...rest,
          __temp_key__: index4 + 1
        };
      });
    } else if (attribute.type === "component") {
      const { repeatable, component } = attribute;
      if (repeatable) {
        acc[current] = (data[current] ?? []).map((compoData, index4) => {
          const { id: _, ...rest } = recursiveRemoveFieldTypes(compoData, components[component], components, fields);
          return {
            ...rest,
            __temp_key__: index4 + 1
          };
        });
      } else {
        const { id: _, ...rest } = recursiveRemoveFieldTypes(data[current] ?? {}, components[component], components, fields);
        acc[current] = rest;
      }
    } else {
      acc[current] = data[current];
    }
    return acc;
  }, {});
};

// node_modules/@strapi/i18n/dist/admin/components/BulkLocaleActionModal.mjs
var import_jsx_runtime17 = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);
var import_qs12 = __toESM(require_lib2(), 1);
var isErrorMessageDescriptor = (object) => {
  return typeof object === "object" && object !== null && "id" in object && "defaultMessage" in object;
};
var EntryValidationText = ({ status = "draft", validationErrors, action }) => {
  const { formatMessage } = useIntl();
  const getErrorStr = (key, value) => {
    if (typeof value === "string") {
      return `${key}: ${value}`;
    } else if (isErrorMessageDescriptor(value)) {
      return `${key}: ${formatMessage(value)}`;
    } else if (Array.isArray(value)) {
      return value.map((v) => getErrorStr(key, v)).join(" ");
    } else if (typeof value === "object" && !Array.isArray(value)) {
      return Object.entries(value).map(([k, v]) => getErrorStr(k, v)).join(" ");
    } else {
      return "";
    }
  };
  if (validationErrors) {
    const validationErrorsMessages = Object.entries(validationErrors).map(([key, value]) => {
      return getErrorStr(key, value);
    }).join(" ");
    return (0, import_jsx_runtime17.jsxs)(Flex, {
      gap: 2,
      children: [
        (0, import_jsx_runtime17.jsx)(ForwardRef$43, {
          fill: "danger600"
        }),
        (0, import_jsx_runtime17.jsx)(TooltipImpl, {
          label: validationErrorsMessages,
          children: (0, import_jsx_runtime17.jsx)(Typography, {
            maxWidth: "30rem",
            textColor: "danger600",
            variant: "omega",
            fontWeight: "semiBold",
            ellipsis: true,
            children: validationErrorsMessages
          })
        })
      ]
    });
  }
  const getStatusMessage = () => {
    if (action === "bulk-publish") {
      if (status === "published") {
        return {
          icon: (0, import_jsx_runtime17.jsx)(ForwardRef$4D, {
            fill: "success600"
          }),
          text: formatMessage({
            id: "content-manager.bulk-publish.already-published",
            defaultMessage: "Already Published"
          }),
          textColor: "success600",
          fontWeight: "bold"
        };
      } else if (status === "modified") {
        return {
          icon: (0, import_jsx_runtime17.jsx)(ForwardRef$59, {
            fill: "alternative600"
          }),
          text: formatMessage({
            id: "app.utils.ready-to-publish-changes",
            defaultMessage: "Ready to publish changes"
          })
        };
      } else {
        return {
          icon: (0, import_jsx_runtime17.jsx)(ForwardRef$4D, {
            fill: "success600"
          }),
          text: formatMessage({
            id: "app.utils.ready-to-publish",
            defaultMessage: "Ready to publish"
          })
        };
      }
    } else {
      if (status === "draft") {
        return {
          icon: (0, import_jsx_runtime17.jsx)(ForwardRef$4D, {
            fill: "success600"
          }),
          text: formatMessage({
            id: "content-manager.bulk-unpublish.already-unpublished",
            defaultMessage: "Already Unpublished"
          }),
          textColor: "success600",
          fontWeight: "bold"
        };
      } else {
        return {
          icon: (0, import_jsx_runtime17.jsx)(ForwardRef$4D, {
            fill: "success600"
          }),
          text: formatMessage({
            id: "app.utils.ready-to-unpublish-changes",
            defaultMessage: "Ready to unpublish"
          }),
          textColor: "success600",
          fontWeight: "bold"
        };
      }
    }
  };
  const { icon, text, textColor = "success600", fontWeight = "normal" } = getStatusMessage();
  return (0, import_jsx_runtime17.jsxs)(Flex, {
    gap: 2,
    children: [
      icon,
      (0, import_jsx_runtime17.jsx)(Typography, {
        textColor,
        fontWeight,
        children: text
      })
    ]
  });
};
var BoldChunk = (chunks) => (0, import_jsx_runtime17.jsx)(Typography, {
  fontWeight: "bold",
  children: chunks
});
var BulkLocaleActionModal = ({ headers, rows, localesMetadata, validationErrors = {}, action }) => {
  const { formatMessage } = useIntl();
  const selectedRows = useTable("BulkLocaleActionModal", (state) => state.selectedRows);
  const getFormattedCountMessage = () => {
    const currentStatusByLocale = rows.reduce((acc, { locale, status }) => {
      acc[locale] = status;
      return acc;
    }, {});
    const localesWithErrors = Object.keys(validationErrors);
    const publishedCount = selectedRows.filter(({ locale }) => currentStatusByLocale[locale] === "published").length;
    const draftCount = selectedRows.filter(({ locale }) => (currentStatusByLocale[locale] === "draft" || currentStatusByLocale[locale] === "modified") && !localesWithErrors.includes(locale)).length;
    const withErrorsCount = localesWithErrors.length;
    const messageId = action === "bulk-publish" ? "content-manager.containers.list.selectedEntriesModal.selectedCount.publish" : "content-manager.containers.list.selectedEntriesModal.selectedCount.unpublish";
    const defaultMessage = action === "bulk-publish" ? "<b>{publishedCount}</b> {publishedCount, plural, =0 {entries} one {entry} other {entries}} already published. <b>{draftCount}</b> {draftCount, plural, =0 {entries} one {entry} other {entries}} ready to publish. <b>{withErrorsCount}</b> {withErrorsCount, plural, =0 {entries} one {entry} other {entries}} waiting for action." : "<b>{draftCount}</b> {draftCount, plural, =0 {entries} one {entry} other {entries}} already unpublished. <b>{publishedCount}</b> {publishedCount, plural, =0 {entries} one {entry} other {entries}} ready to unpublish.";
    return formatMessage({
      id: messageId,
      defaultMessage
    }, {
      withErrorsCount,
      draftCount,
      publishedCount,
      b: BoldChunk
    });
  };
  return (0, import_jsx_runtime17.jsxs)(Modal.Body, {
    children: [
      (0, import_jsx_runtime17.jsx)(Typography, {
        children: getFormattedCountMessage()
      }),
      (0, import_jsx_runtime17.jsx)(Box, {
        marginTop: 5,
        children: (0, import_jsx_runtime17.jsxs)(Table.Content, {
          children: [
            (0, import_jsx_runtime17.jsxs)(Table.Head, {
              children: [
                (0, import_jsx_runtime17.jsx)(Table.HeaderCheckboxCell, {}),
                headers.map((head) => (0, import_jsx_runtime17.jsx)(Table.HeaderCell, {
                  ...head
                }, head.name))
              ]
            }),
            (0, import_jsx_runtime17.jsx)(Table.Body, {
              children: rows.map(({ locale, status }, index4) => {
                var _a;
                const error = (validationErrors == null ? void 0 : validationErrors[locale]) ?? null;
                const statusVariant = status === "draft" ? "primary" : status === "published" ? "success" : "alternative";
                return (0, import_jsx_runtime17.jsxs)(Table.Row, {
                  children: [
                    (0, import_jsx_runtime17.jsx)(Table.CheckboxCell, {
                      id: locale,
                      "aria-label": `Select ${locale}`
                    }),
                    (0, import_jsx_runtime17.jsx)(Table.Cell, {
                      children: (0, import_jsx_runtime17.jsx)(Typography, {
                        variant: "sigma",
                        textColor: "neutral600",
                        children: Array.isArray(localesMetadata) ? (_a = localesMetadata.find((localeEntry) => localeEntry.code === locale)) == null ? void 0 : _a.name : locale
                      })
                    }),
                    (0, import_jsx_runtime17.jsx)(Table.Cell, {
                      children: (0, import_jsx_runtime17.jsx)(Box, {
                        display: "flex",
                        children: (0, import_jsx_runtime17.jsx)(Status, {
                          display: "flex",
                          paddingLeft: "6px",
                          paddingRight: "6px",
                          paddingTop: "2px",
                          paddingBottom: "2px",
                          size: "S",
                          variant: statusVariant,
                          children: (0, import_jsx_runtime17.jsx)(Typography, {
                            tag: "span",
                            variant: "pi",
                            fontWeight: "bold",
                            children: capitalize(status)
                          })
                        })
                      })
                    }),
                    (0, import_jsx_runtime17.jsx)(Table.Cell, {
                      children: (0, import_jsx_runtime17.jsx)(EntryValidationText, {
                        validationErrors: error,
                        status,
                        action
                      })
                    }),
                    (0, import_jsx_runtime17.jsx)(Table.Cell, {
                      children: (0, import_jsx_runtime17.jsx)(IconButton, {
                        tag: Link,
                        to: {
                          search: (0, import_qs12.stringify)({
                            plugins: {
                              i18n: {
                                locale
                              }
                            }
                          })
                        },
                        label: formatMessage({
                          id: getTranslation("Settings.list.actions.edit"),
                          defaultMessage: "Edit {name} locale"
                        }, {
                          name: locale
                        }),
                        variant: "ghost",
                        children: (0, import_jsx_runtime17.jsx)(ForwardRef$1v, {})
                      })
                    })
                  ]
                }, index4);
              })
            })
          ]
        })
      })
    ]
  });
};

// node_modules/@strapi/i18n/dist/admin/components/CMHeaderActions.mjs
var statusVariants = {
  draft: "secondary",
  published: "success",
  modified: "alternative"
};
var LocaleOption = ({ isDraftAndPublishEnabled, locale, status, entryExists }) => {
  const { formatMessage } = useIntl();
  if (!entryExists) {
    return formatMessage({
      id: getTranslation("CMEditViewLocalePicker.locale.create"),
      defaultMessage: "Create <bold>{locale}</bold> locale"
    }, {
      bold: (locale2) => (0, import_jsx_runtime18.jsx)("b", {
        children: locale2
      }),
      locale: locale.name
    });
  }
  return (0, import_jsx_runtime18.jsxs)(Flex, {
    width: "100%",
    gap: 1,
    justifyContent: "space-between",
    children: [
      (0, import_jsx_runtime18.jsx)(Typography, {
        children: locale.name
      }),
      isDraftAndPublishEnabled ? (0, import_jsx_runtime18.jsx)(Status, {
        display: "flex",
        paddingLeft: "6px",
        paddingRight: "6px",
        paddingTop: "2px",
        paddingBottom: "2px",
        size: "S",
        variant: statusVariants[status],
        children: (0, import_jsx_runtime18.jsx)(Typography, {
          tag: "span",
          variant: "pi",
          fontWeight: "bold",
          children: capitalize(status)
        })
      }) : null
    ]
  });
};
var LocalePickerAction = ({ document, meta, model, collectionType, documentId }) => {
  var _a, _b;
  const { formatMessage } = useIntl();
  const [{ query }, setQuery] = useQueryParams();
  const { hasI18n, canCreate, canRead } = useI18n();
  const { data: locales = [] } = useGetLocalesQuery();
  const currentDesiredLocale = (_b = (_a = query.plugins) == null ? void 0 : _a.i18n) == null ? void 0 : _b.locale;
  const { schema } = useDocument({
    model,
    collectionType,
    documentId,
    params: {
      locale: currentDesiredLocale
    }
  });
  const handleSelect = React12.useCallback((value) => {
    setQuery({
      plugins: {
        ...query.plugins,
        i18n: {
          locale: value
        }
      }
    });
  }, [
    query.plugins,
    setQuery
  ]);
  React12.useEffect(() => {
    if (!Array.isArray(locales) || !hasI18n) {
      return;
    }
    const doesLocaleExist = locales.find((loc) => loc.code === currentDesiredLocale);
    const defaultLocale = locales.find((locale) => locale.isDefault);
    if (!doesLocaleExist && (defaultLocale == null ? void 0 : defaultLocale.code)) {
      handleSelect(defaultLocale.code);
    }
  }, [
    handleSelect,
    hasI18n,
    locales,
    currentDesiredLocale
  ]);
  const currentLocale = Array.isArray(locales) ? locales.find((locale) => locale.code === currentDesiredLocale) : void 0;
  const allCurrentLocales = [
    {
      status: getDocumentStatus(document, meta),
      locale: currentLocale == null ? void 0 : currentLocale.code
    },
    ...(document == null ? void 0 : document.localizations) ?? []
  ];
  if (!hasI18n || !Array.isArray(locales) || locales.length === 0) {
    return null;
  }
  const displayedLocales = locales.filter((locale) => {
    return canRead.includes(locale.code);
  });
  return {
    label: formatMessage({
      id: getTranslation("Settings.locales.modal.locales.label"),
      defaultMessage: "Locales"
    }),
    options: displayedLocales.map((locale) => {
      var _a2;
      const entryWithLocaleExists = allCurrentLocales.some((doc) => doc.locale === locale.code);
      const currentLocaleDoc = allCurrentLocales.find((doc) => "locale" in doc ? doc.locale === locale.code : false);
      const permissionsToCheck = currentLocaleDoc ? canRead : canCreate;
      return {
        disabled: !permissionsToCheck.includes(locale.code),
        value: locale.code,
        label: (0, import_jsx_runtime18.jsx)(LocaleOption, {
          isDraftAndPublishEnabled: !!((_a2 = schema == null ? void 0 : schema.options) == null ? void 0 : _a2.draftAndPublish),
          locale,
          status: currentLocaleDoc == null ? void 0 : currentLocaleDoc.status,
          entryExists: entryWithLocaleExists
        }),
        startIcon: !entryWithLocaleExists ? (0, import_jsx_runtime18.jsx)(ForwardRef$1h, {}) : null
      };
    }),
    customizeContent: () => currentLocale == null ? void 0 : currentLocale.name,
    onSelect: handleSelect,
    value: currentLocale
  };
};
var getDocumentStatus = (document, meta) => {
  const docStatus = document == null ? void 0 : document.status;
  const statuses = (meta == null ? void 0 : meta.availableStatus) ?? [];
  if (!docStatus) {
    return "draft";
  }
  if (docStatus === "draft" && statuses.find((doc) => doc.publishedAt !== null)) {
    return "published";
  }
  return docStatus;
};
var FillFromAnotherLocaleAction = ({ documentId, meta, model, collectionType }) => {
  var _a, _b;
  const { formatMessage } = useIntl();
  const [{ query }] = useQueryParams();
  const { hasI18n } = useI18n();
  const currentDesiredLocale = (_b = (_a = query.plugins) == null ? void 0 : _a.i18n) == null ? void 0 : _b.locale;
  const [localeSelected, setLocaleSelected] = React12.useState(null);
  const setValues = useForm("FillFromAnotherLocale", (state) => state.setValues);
  const { getDocument } = useDocumentActions();
  const { schema, components } = useDocument({
    model,
    documentId,
    collectionType,
    params: {
      locale: currentDesiredLocale
    }
  });
  const { data: locales = [] } = useGetLocalesQuery();
  const availableLocales = Array.isArray(locales) ? locales.filter((locale) => meta == null ? void 0 : meta.availableLocales.some((l) => l.locale === locale.code)) : [];
  const fillFromLocale = (onClose) => async () => {
    const response = await getDocument({
      collectionType,
      model,
      documentId,
      params: {
        locale: localeSelected
      }
    });
    if (!response || !schema) {
      return;
    }
    const { data } = response;
    const cleanedData = cleanData(data, schema, components);
    setValues(cleanedData);
    onClose();
  };
  if (!hasI18n) {
    return null;
  }
  return {
    type: "icon",
    icon: (0, import_jsx_runtime18.jsx)(ForwardRef$3P, {}),
    disabled: availableLocales.length === 0,
    label: formatMessage({
      id: getTranslation("CMEditViewCopyLocale.copy-text"),
      defaultMessage: "Fill in from another locale"
    }),
    dialog: {
      type: "dialog",
      title: formatMessage({
        id: getTranslation("CMEditViewCopyLocale.dialog.title"),
        defaultMessage: "Confirmation"
      }),
      content: ({ onClose }) => (0, import_jsx_runtime18.jsxs)(import_jsx_runtime18.Fragment, {
        children: [
          (0, import_jsx_runtime18.jsx)(Dialog.Body, {
            children: (0, import_jsx_runtime18.jsxs)(Flex, {
              direction: "column",
              gap: 3,
              children: [
                (0, import_jsx_runtime18.jsx)(ForwardRef$3, {
                  width: "24px",
                  height: "24px",
                  fill: "danger600"
                }),
                (0, import_jsx_runtime18.jsx)(Typography, {
                  textAlign: "center",
                  children: formatMessage({
                    id: getTranslation("CMEditViewCopyLocale.dialog.body"),
                    defaultMessage: "Your current content will be erased and filled by the content of the selected locale:"
                  })
                }),
                (0, import_jsx_runtime18.jsxs)(Field.Root, {
                  width: "100%",
                  children: [
                    (0, import_jsx_runtime18.jsx)(Field.Label, {
                      children: formatMessage({
                        id: getTranslation("CMEditViewCopyLocale.dialog.field.label"),
                        defaultMessage: "Locale"
                      })
                    }),
                    (0, import_jsx_runtime18.jsx)(SingleSelect, {
                      value: localeSelected,
                      placeholder: formatMessage({
                        id: getTranslation("CMEditViewCopyLocale.dialog.field.placeholder"),
                        defaultMessage: "Select one locale..."
                      }),
                      // @ts-expect-error – the DS will handle numbers, but we're not allowing the API.
                      onChange: (value) => setLocaleSelected(value),
                      children: availableLocales.map((locale) => (0, import_jsx_runtime18.jsx)(SingleSelectOption, {
                        value: locale.code,
                        children: locale.name
                      }, locale.code))
                    })
                  ]
                })
              ]
            })
          }),
          (0, import_jsx_runtime18.jsx)(Dialog.Footer, {
            children: (0, import_jsx_runtime18.jsxs)(Flex, {
              gap: 2,
              width: "100%",
              children: [
                (0, import_jsx_runtime18.jsx)(Button, {
                  flex: "auto",
                  variant: "tertiary",
                  onClick: onClose,
                  children: formatMessage({
                    id: getTranslation("CMEditViewCopyLocale.cancel-text"),
                    defaultMessage: "No, cancel"
                  })
                }),
                (0, import_jsx_runtime18.jsx)(Button, {
                  flex: "auto",
                  variant: "success",
                  onClick: fillFromLocale(onClose),
                  children: formatMessage({
                    id: getTranslation("CMEditViewCopyLocale.submit-text"),
                    defaultMessage: "Yes, fill in"
                  })
                })
              ]
            })
          })
        ]
      })
    }
  };
};
var DeleteLocaleAction = ({ document, documentId, model, collectionType }) => {
  var _a, _b;
  const { formatMessage } = useIntl();
  const navigate = useNavigate();
  const { toggleNotification } = useNotification();
  const { delete: deleteAction, isLoading } = useDocumentActions();
  const { hasI18n, canDelete } = useI18n();
  const [{ query }] = useQueryParams();
  const { data: locales = [] } = useGetLocalesQuery();
  const currentDesiredLocale = (_b = (_a = query.plugins) == null ? void 0 : _a.i18n) == null ? void 0 : _b.locale;
  const locale = !("error" in locales) && locales.find((loc) => loc.code === currentDesiredLocale);
  if (!hasI18n) {
    return null;
  }
  return {
    disabled: (document == null ? void 0 : document.locale) && !canDelete.includes(document.locale) || !document || !document.id,
    position: [
      "header",
      "table-row"
    ],
    label: formatMessage({
      id: getTranslation("actions.delete.label"),
      defaultMessage: "Delete entry ({locale})"
    }, {
      locale: locale && locale.name
    }),
    icon: (0, import_jsx_runtime18.jsx)(StyledTrash, {}),
    variant: "danger",
    dialog: {
      type: "dialog",
      title: formatMessage({
        id: getTranslation("actions.delete.dialog.title"),
        defaultMessage: "Confirmation"
      }),
      content: (0, import_jsx_runtime18.jsxs)(Flex, {
        direction: "column",
        gap: 2,
        children: [
          (0, import_jsx_runtime18.jsx)(ForwardRef$3, {
            width: "24px",
            height: "24px",
            fill: "danger600"
          }),
          (0, import_jsx_runtime18.jsx)(Typography, {
            tag: "p",
            variant: "omega",
            textAlign: "center",
            children: formatMessage({
              id: getTranslation("actions.delete.dialog.body"),
              defaultMessage: "Are you sure?"
            })
          })
        ]
      }),
      loading: isLoading,
      onConfirm: async () => {
        const unableToDelete = (
          // We are unable to delete a collection type without a document ID
          // & unable to delete generally if there is no document locale
          collectionType !== "single-types" && !documentId || !(document == null ? void 0 : document.locale)
        );
        if (unableToDelete) {
          console.error("You're trying to delete a document without an id or locale, this is likely a bug with Strapi. Please open an issue.");
          toggleNotification({
            message: formatMessage({
              id: getTranslation("actions.delete.error"),
              defaultMessage: "An error occurred while trying to delete the document locale."
            }),
            type: "danger"
          });
          return;
        }
        const res = await deleteAction({
          documentId,
          model,
          collectionType,
          params: {
            locale: document.locale
          }
        });
        if (!("error" in res)) {
          navigate({
            pathname: `../${collectionType}/${model}`
          }, {
            replace: true
          });
        }
      }
    }
  };
};
var BulkLocaleAction = ({ document, documentId, model, collectionType, action }) => {
  var _a;
  const locale = (document == null ? void 0 : document.locale) ?? null;
  const [{ query }] = useQueryParams();
  const params = React12.useMemo(() => buildValidParams(query), [
    query
  ]);
  const isOnPublishedTab = query.status === "published";
  const { formatMessage } = useIntl();
  const { hasI18n, canPublish } = useI18n();
  const { toggleNotification } = useNotification();
  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();
  const [selectedRows, setSelectedRows] = React12.useState([]);
  const [isDraftRelationConfirmationOpen, setIsDraftRelationConfirmationOpen] = React12.useState(false);
  const { publishMany: publishManyAction, unpublishMany: unpublishManyAction } = useDocumentActions();
  const { schema, validate } = useDocument({
    model,
    collectionType,
    documentId,
    params: {
      locale
    }
  }, {
    // No need to fetch the document, the data is already available in the `document` prop
    skip: true
  });
  const { data: localesMetadata = [] } = useGetLocalesQuery(hasI18n ? void 0 : skipToken);
  const headers = [
    {
      label: formatMessage({
        id: "global.name",
        defaultMessage: "Name"
      }),
      name: "name"
    },
    {
      label: formatMessage({
        id: getTranslation("CMEditViewBulkLocale.status"),
        defaultMessage: "Status"
      }),
      name: "status"
    },
    {
      label: formatMessage({
        id: getTranslation("CMEditViewBulkLocale.publication-status"),
        defaultMessage: "Publication Status"
      }),
      name: "publication-status"
    }
  ];
  const [rows, validationErrors] = React12.useMemo(() => {
    if (!document) {
      return [
        [],
        {}
      ];
    }
    const localizations = document.localizations ?? [];
    const locales = localizations.map((doc) => {
      const { locale: locale2, status } = doc;
      return {
        locale: locale2,
        status
      };
    });
    locales.unshift({
      locale: document.locale,
      status: document.status
    });
    const allDocuments = [
      document,
      ...localizations
    ];
    const errors = allDocuments.reduce((errs, document2) => {
      if (!document2) {
        return errs;
      }
      const validation = validate(document2);
      if (validation !== null) {
        errs[document2.locale] = validation;
      }
      return errs;
    }, {});
    return [
      locales,
      errors
    ];
  }, [
    document,
    validate
  ]);
  const isBulkPublish = action === "bulk-publish";
  const localesForAction = selectedRows.reduce((acc, selectedRow) => {
    const isValidLocale = (
      // Validation errors are irrelevant if we are trying to unpublish
      !isBulkPublish || !Object.keys(validationErrors).includes(selectedRow.locale)
    );
    const shouldAddLocale = isBulkPublish ? selectedRow.status !== "published" && isValidLocale : selectedRow.status !== "draft" && isValidLocale;
    if (shouldAddLocale) {
      acc.push(selectedRow.locale);
    }
    return acc;
  }, []);
  const enableDraftRelationsCount = false;
  const { data: draftRelationsCount = 0, isLoading: isDraftRelationsLoading, error: isDraftRelationsError } = useGetManyDraftRelationCountQuery({
    model,
    documentIds: [
      documentId
    ],
    locale: localesForAction
  }, {
    skip: !enableDraftRelationsCount
  });
  React12.useEffect(() => {
    if (isDraftRelationsError) {
      toggleNotification({
        type: "danger",
        message: formatAPIError(isDraftRelationsError)
      });
    }
  }, [
    isDraftRelationsError,
    toggleNotification,
    formatAPIError
  ]);
  if (!((_a = schema == null ? void 0 : schema.options) == null ? void 0 : _a.draftAndPublish)) {
    return null;
  }
  if (!hasI18n) {
    return null;
  }
  if (!documentId) {
    return null;
  }
  const publish = async () => {
    await publishManyAction({
      model,
      documentIds: [
        documentId
      ],
      params: {
        ...params,
        locale: localesForAction
      }
    });
    setSelectedRows([]);
  };
  const unpublish = async () => {
    await unpublishManyAction({
      model,
      documentIds: [
        documentId
      ],
      params: {
        ...params,
        locale: localesForAction
      }
    });
    setSelectedRows([]);
  };
  const handleAction = async () => {
    if (draftRelationsCount > 0) {
      setIsDraftRelationConfirmationOpen(true);
    } else if (isBulkPublish) {
      await publish();
    } else {
      await unpublish();
    }
  };
  if (isDraftRelationConfirmationOpen) {
    return {
      label: formatMessage({
        id: "app.components.ConfirmDialog.title",
        defaultMessage: "Confirmation"
      }),
      variant: "danger",
      dialog: {
        onCancel: () => {
          setIsDraftRelationConfirmationOpen(false);
        },
        onConfirm: async () => {
          await publish();
          setIsDraftRelationConfirmationOpen(false);
        },
        type: "dialog",
        title: formatMessage({
          id: getTranslation("actions.publish.dialog.title"),
          defaultMessage: "Confirmation"
        }),
        content: (0, import_jsx_runtime18.jsxs)(Flex, {
          direction: "column",
          alignItems: "center",
          gap: 2,
          children: [
            (0, import_jsx_runtime18.jsx)(ForwardRef$3, {
              width: "2.4rem",
              height: "2.4rem",
              fill: "danger600"
            }),
            (0, import_jsx_runtime18.jsx)(Typography, {
              textAlign: "center",
              children: formatMessage({
                id: getTranslation("CMEditViewBulkLocale.draft-relation-warning"),
                defaultMessage: "Some locales are related to draft entries. Publishing them could leave broken links in your app."
              })
            }),
            (0, import_jsx_runtime18.jsx)(Typography, {
              textAlign: "center",
              children: formatMessage({
                id: getTranslation("CMEditViewBulkLocale.continue-confirmation"),
                defaultMessage: "Are you sure you want to continue?"
              })
            })
          ]
        })
      }
    };
  }
  const hasPermission = selectedRows.map(({ locale: locale2 }) => locale2).every((locale2) => canPublish.includes(locale2));
  return {
    label: formatMessage({
      id: getTranslation(`CMEditViewBulkLocale.${isBulkPublish ? "publish" : "unpublish"}-title`),
      defaultMessage: `${isBulkPublish ? "Publish" : "Unpublish"} Multiple Locales`
    }),
    variant: isBulkPublish ? "secondary" : "danger",
    icon: isBulkPublish ? (0, import_jsx_runtime18.jsx)(ForwardRef$2n, {}) : (0, import_jsx_runtime18.jsx)(ForwardRef$45, {}),
    disabled: isOnPublishedTab || canPublish.length === 0,
    position: [
      "panel"
    ],
    dialog: {
      type: "modal",
      title: formatMessage({
        id: getTranslation(`CMEditViewBulkLocale.${isBulkPublish ? "publish" : "unpublish"}-title`),
        defaultMessage: `${isBulkPublish ? "Publish" : "Unpublish"} Multiple Locales`
      }),
      content: () => {
        return (0, import_jsx_runtime18.jsx)(Table.Root, {
          headers,
          rows: rows.map((row) => ({
            ...row,
            id: row.locale
          })),
          selectedRows,
          onSelectedRowsChange: (tableSelectedRows) => setSelectedRows(tableSelectedRows),
          children: (0, import_jsx_runtime18.jsx)(BulkLocaleActionModal, {
            validationErrors,
            headers,
            rows,
            localesMetadata,
            action: action ?? "bulk-publish"
          })
        });
      },
      footer: () => (0, import_jsx_runtime18.jsx)(Modal.Footer, {
        justifyContent: "flex-end",
        children: (0, import_jsx_runtime18.jsx)(Button, {
          loading: isDraftRelationsLoading,
          disabled: !hasPermission || localesForAction.length === 0,
          variant: "default",
          onClick: handleAction,
          children: formatMessage({
            id: isBulkPublish ? "app.utils.publish" : "app.utils.unpublish",
            defaultMessage: isBulkPublish ? "Publish" : "Unpublish"
          })
        })
      })
    }
  };
};
var BulkLocalePublishAction = (props) => {
  return BulkLocaleAction({
    action: "bulk-publish",
    ...props
  });
};
var BulkLocaleUnpublishAction = (props) => {
  return BulkLocaleAction({
    action: "bulk-unpublish",
    ...props
  });
};
var StyledTrash = dt(ForwardRef$j)`
  path {
    fill: currentColor;
  }
`;

// node_modules/@strapi/i18n/dist/admin/components/CMListViewModalsAdditionalInformation.mjs
var import_jsx_runtime19 = __toESM(require_jsx_runtime(), 1);
var import_react2 = __toESM(require_react(), 1);
var Emphasis = (chunks) => {
  return (0, import_jsx_runtime19.jsx)(Typography, {
    fontWeight: "semiBold",
    textColor: "danger500",
    children: chunks
  });
};
var DeleteModalAdditionalInfo = () => {
  const { hasI18n } = useI18n();
  const { formatMessage } = useIntl();
  if (!hasI18n) {
    return null;
  }
  return (0, import_jsx_runtime19.jsx)(Typography, {
    textColor: "danger500",
    children: formatMessage({
      id: getTranslation("Settings.list.actions.deleteAdditionalInfos"),
      defaultMessage: "This will delete the active locale versions <em>(from Internationalization)</em>"
    }, {
      em: Emphasis
    })
  });
};
var PublishModalAdditionalInfo = () => {
  const { hasI18n } = useI18n();
  const { formatMessage } = useIntl();
  if (!hasI18n) {
    return null;
  }
  return (0, import_jsx_runtime19.jsx)(Typography, {
    textColor: "danger500",
    children: formatMessage({
      id: getTranslation("Settings.list.actions.publishAdditionalInfos"),
      defaultMessage: "This will publish the active locale versions <em>(from Internationalization)</em>"
    }, {
      em: Emphasis
    })
  });
};
var UnpublishModalAdditionalInfo = () => {
  const { hasI18n } = useI18n();
  const { formatMessage } = useIntl();
  if (!hasI18n) {
    return null;
  }
  return (0, import_jsx_runtime19.jsx)(Typography, {
    textColor: "danger500",
    children: formatMessage({
      id: getTranslation("Settings.list.actions.unpublishAdditionalInfos"),
      defaultMessage: "This will unpublish the active locale versions <em>(from Internationalization)</em>"
    }, {
      em: Emphasis
    })
  });
};

// node_modules/@strapi/i18n/dist/admin/components/LocalePicker.mjs
var import_jsx_runtime20 = __toESM(require_jsx_runtime(), 1);
var React13 = __toESM(require_react(), 1);
var LocalePicker = () => {
  var _a, _b, _c, _d, _e;
  const { formatMessage } = useIntl();
  const [{ query }, setQuery] = useQueryParams();
  const { hasI18n, canRead, canCreate } = useI18n();
  const { data: locales = [] } = useGetLocalesQuery(void 0, {
    skip: !hasI18n
  });
  const handleChange = React13.useCallback((code, replace = false) => {
    setQuery({
      page: 1,
      plugins: {
        ...query.plugins,
        i18n: {
          locale: code
        }
      }
    }, "push", replace);
  }, [
    query.plugins,
    setQuery
  ]);
  React13.useEffect(() => {
    var _a2, _b2;
    if (!Array.isArray(locales) || !hasI18n) {
      return;
    }
    const currentDesiredLocale = (_b2 = (_a2 = query.plugins) == null ? void 0 : _a2.i18n) == null ? void 0 : _b2.locale;
    const doesLocaleExist = locales.find((loc) => loc.code === currentDesiredLocale);
    const defaultLocale = locales.find((locale) => locale.isDefault);
    if (!doesLocaleExist && (defaultLocale == null ? void 0 : defaultLocale.code)) {
      handleChange(defaultLocale.code, true);
    }
  }, [
    hasI18n,
    handleChange,
    locales,
    (_b = (_a = query.plugins) == null ? void 0 : _a.i18n) == null ? void 0 : _b.locale
  ]);
  if (!hasI18n || !Array.isArray(locales) || locales.length === 0) {
    return null;
  }
  const displayedLocales = locales.filter((locale) => {
    return canCreate.includes(locale.code) || canRead.includes(locale.code);
  });
  return (0, import_jsx_runtime20.jsx)(SingleSelect, {
    size: "S",
    "aria-label": formatMessage({
      id: getTranslation("actions.select-locale"),
      defaultMessage: "Select locale"
    }),
    value: ((_d = (_c = query.plugins) == null ? void 0 : _c.i18n) == null ? void 0 : _d.locale) || ((_e = locales.find((locale) => locale.isDefault)) == null ? void 0 : _e.code),
    // @ts-expect-error – This can be removed in V2 of the DS.
    onChange: handleChange,
    children: displayedLocales.map((locale) => (0, import_jsx_runtime20.jsx)(SingleSelectOption, {
      value: locale.code,
      children: locale.name
    }, locale.id))
  });
};

// node_modules/@strapi/i18n/dist/admin/contentManagerHooks/editView.mjs
var import_jsx_runtime21 = __toESM(require_jsx_runtime(), 1);
var React14 = __toESM(require_react(), 1);
var mutateEditViewHook = ({ layout }) => {
  if (!("i18n" in layout.options) || typeof layout.options.i18n === "object" && layout.options.i18n !== null && "localized" in layout.options.i18n && !layout.options.i18n.localized) {
    return {
      layout
    };
  }
  const components = Object.entries(layout.components).reduce((acc, [key, componentLayout]) => {
    return {
      ...acc,
      [key]: {
        ...componentLayout,
        layout: componentLayout.layout.map((row) => row.map(addLabelActionToField))
      }
    };
  }, {});
  return {
    layout: {
      ...layout,
      components,
      layout: layout.layout.map((panel) => panel.map((row) => row.map(addLabelActionToField)))
    }
  };
};
var addLabelActionToField = (field) => {
  const isFieldLocalized = doesFieldHaveI18nPluginOpt(field.attribute.pluginOptions) ? field.attribute.pluginOptions.i18n.localized : true;
  const labelActionProps = {
    title: {
      id: isFieldLocalized ? getTranslation("Field.localized") : getTranslation("Field.not-localized"),
      defaultMessage: isFieldLocalized ? "This value is unique for the selected locale" : "This value is the same across all locales"
    },
    icon: isFieldLocalized ? (0, import_jsx_runtime21.jsx)(ForwardRef$3P, {}) : null
  };
  return {
    ...field,
    labelAction: isFieldLocalized ? (0, import_jsx_runtime21.jsx)(LabelAction, {
      ...labelActionProps
    }) : null
  };
};
var doesFieldHaveI18nPluginOpt = (pluginOpts) => {
  if (!pluginOpts) {
    return false;
  }
  return "i18n" in pluginOpts && typeof pluginOpts.i18n === "object" && pluginOpts.i18n !== null && "localized" in pluginOpts.i18n;
};
var LabelAction = ({ title, icon }) => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime21.jsxs)(Span, {
    tag: "span",
    children: [
      (0, import_jsx_runtime21.jsx)(VisuallyHidden, {
        tag: "span",
        children: formatMessage(title)
      }),
      React14.cloneElement(icon, {
        "aria-hidden": true,
        focusable: false
      })
    ]
  });
};
var Span = dt(Flex)`
  svg {
    width: 12px;
    height: 12px;

    fill: ${({ theme }) => theme.colors.neutral500};

    path {
      fill: ${({ theme }) => theme.colors.neutral500};
    }
  }
`;

// node_modules/@strapi/i18n/dist/admin/contentManagerHooks/listView.mjs
var import_jsx_runtime23 = __toESM(require_jsx_runtime(), 1);

// node_modules/@strapi/i18n/dist/admin/components/LocaleListCell.mjs
var import_jsx_runtime22 = __toESM(require_jsx_runtime(), 1);
var LocaleListCell = ({ locale: currentLocale, localizations }) => {
  const { locale: language } = useIntl();
  const { data: locales = [] } = useGetLocalesQuery();
  const formatter = useCollator(language, {
    sensitivity: "base"
  });
  if (!Array.isArray(locales) || !localizations) {
    return null;
  }
  const availableLocales = localizations.map((loc) => loc.locale);
  const localesForDocument = locales.reduce((acc, locale) => {
    const createdLocale = [
      currentLocale,
      ...availableLocales
    ].find((loc) => {
      return loc === locale.code;
    });
    if (createdLocale) {
      acc.push(locale);
    }
    return acc;
  }, []).map((locale) => {
    if (locale.isDefault) {
      return `${locale.name} (default)`;
    }
    return locale.name;
  }).toSorted((a, b) => formatter.compare(a, b));
  return (0, import_jsx_runtime22.jsxs)(Popover.Root, {
    children: [
      (0, import_jsx_runtime22.jsx)(Popover.Trigger, {
        children: (0, import_jsx_runtime22.jsx)(Button, {
          variant: "ghost",
          type: "button",
          onClick: (e) => e.stopPropagation(),
          children: (0, import_jsx_runtime22.jsxs)(Flex, {
            minWidth: "100%",
            alignItems: "center",
            justifyContent: "center",
            fontWeight: "regular",
            children: [
              (0, import_jsx_runtime22.jsx)(Typography, {
                textColor: "neutral800",
                ellipsis: true,
                marginRight: 2,
                children: localesForDocument.join(", ")
              }),
              (0, import_jsx_runtime22.jsx)(Flex, {
                children: (0, import_jsx_runtime22.jsx)(ForwardRef$4T, {
                  width: "1.2rem",
                  height: "1.2rem"
                })
              })
            ]
          })
        })
      }),
      (0, import_jsx_runtime22.jsx)(Popover.Content, {
        sideOffset: 16,
        children: (0, import_jsx_runtime22.jsx)("ul", {
          children: localesForDocument.map((name2) => (0, import_jsx_runtime22.jsx)(Box, {
            padding: 3,
            tag: "li",
            children: (0, import_jsx_runtime22.jsx)(Typography, {
              children: name2
            })
          }, name2))
        })
      })
    ]
  });
};

// node_modules/@strapi/i18n/dist/admin/contentManagerHooks/listView.mjs
var addColumnToTableHook = ({ displayedHeaders, layout }) => {
  const { options } = layout;
  const isFieldLocalized = doesPluginOptionsHaveI18nLocalized(options) ? options.i18n.localized : false;
  if (!isFieldLocalized) {
    return {
      displayedHeaders,
      layout
    };
  }
  return {
    displayedHeaders: [
      ...displayedHeaders,
      {
        attribute: {
          type: "string"
        },
        label: {
          id: getTranslation("list-view.table.header.label"),
          defaultMessage: "Available in"
        },
        searchable: false,
        sortable: false,
        name: "locales",
        // @ts-expect-error – ID is seen as number | string; this will change when we move the type over.
        cellFormatter: (props, _header, meta) => (0, import_jsx_runtime23.jsx)(LocaleListCell, {
          ...props,
          ...meta
        })
      }
    ],
    layout
  };
};

// node_modules/@strapi/i18n/dist/admin/contentReleasesHooks/releaseDetailsView.mjs
var addLocaleToReleasesHook = ({ displayedHeaders = [] }) => {
  return {
    displayedHeaders: [
      ...displayedHeaders,
      {
        label: {
          id: "content-releases.page.ReleaseDetails.table.header.label.locale",
          defaultMessage: "locale"
        },
        name: "locale"
      }
    ],
    hasI18nEnabled: true
  };
};

// node_modules/@strapi/i18n/dist/admin/middlewares/extendCTBAttributeInitialData.mjs
var import_get2 = __toESM(require_get(), 1);
var extendCTBAttributeInitialDataMiddleware = () => {
  return ({ getState }) => (next) => (action) => {
    const enhanceAction = ({ uid }) => {
      var _a, _b;
      try {
        const store = getState();
        const type2 = (0, import_get2.default)(store, [
          "content-type-builder_dataManagerProvider",
          "current",
          "contentTypes",
          uid
        ]);
        if (!type2 || type2.modelType !== "contentType") {
          return next(action);
        }
        const hasi18nEnabled = (0, import_get2.default)(type2, [
          "pluginOptions",
          "i18n",
          "localized"
        ], false);
        if (hasi18nEnabled) {
          return next({
            ...action,
            payload: {
              ...action.payload,
              options: {
                pluginOptions: {
                  ...((_b = (_a = action == null ? void 0 : action.payload) == null ? void 0 : _a.options) == null ? void 0 : _b.pluginOptions) ?? {},
                  i18n: {
                    localized: true
                  }
                }
              }
            }
          });
        }
        return next(action);
      } catch (err) {
        return next(action);
      }
    };
    const { payload = {}, type } = action ?? {};
    if (type === "formModal/setAttributeDataSchema" && ![
      "relation",
      "component"
    ].includes(payload.attributeType) && !payload.isEditing) {
      return enhanceAction({
        uid: payload.uid
      });
    }
    if (type === "formModal/setCustomFieldDataSchema" && !payload.isEditing) {
      return enhanceAction({
        uid: payload.uid
      });
    }
    if (type === "formModal/resetPropsAndSetFormForAddingAnExistingCompo" || type === "formModal/resetPropsAndSaveCurrentData") {
      return enhanceAction({
        uid: payload.uid
      });
    }
    return next(action);
  };
};

// node_modules/@strapi/i18n/dist/admin/middlewares/extendCTBInitialData.mjs
var extendCTBInitialDataMiddleware = () => {
  return () => (next) => (action) => {
    var _a, _b;
    if (action.type === "ContentTypeBuilder/FormModal/SET_DATA_TO_EDIT" && action.modalType === "contentType") {
      const i18n = {
        localized: false
      };
      const pluginOptions = action.data.pluginOptions ? {
        ...action.data.pluginOptions,
        i18n
      } : {
        i18n
      };
      const data = {
        ...action.data,
        pluginOptions
      };
      if (action.actionType === "create") {
        return next({
          ...action,
          data
        });
      }
      if (!((_b = (_a = action.data.pluginOptions) == null ? void 0 : _a.i18n) == null ? void 0 : _b.localized)) {
        return next({
          ...action,
          data
        });
      }
    }
    return next(action);
  };
};

// node_modules/@strapi/i18n/dist/admin/middlewares/rbac-middleware.mjs
var qs = __toESM(require_lib2(), 1);
var localeMiddleware = (ctx) => (next) => (permissions) => {
  const match = matchPath("/content-manager/:collectionType/:model?/:id", ctx.pathname);
  if (!match) {
    return next(permissions);
  }
  const search = qs.parse(ctx.search);
  if (typeof search !== "object") {
    return next(permissions);
  }
  if (!("plugins" in search && typeof search.plugins === "object")) {
    return next(permissions);
  }
  if (!("i18n" in search.plugins && typeof search.plugins.i18n === "object" && !Array.isArray(search.plugins.i18n))) {
    return next(permissions);
  }
  const { locale } = search.plugins.i18n;
  if (typeof locale !== "string") {
    return next(permissions);
  }
  const revisedPermissions = permissions.filter((permission) => {
    var _a;
    return !((_a = permission.properties) == null ? void 0 : _a.locales) || permission.properties.locales.includes(locale);
  });
  return next(revisedPermissions);
};

// node_modules/@strapi/i18n/dist/admin/utils/prefixPluginTranslations.mjs
var prefixPluginTranslations4 = (trad, pluginId5) => {
  return Object.keys(trad).reduce((acc, current) => {
    acc[`${pluginId5}.${current}`] = trad[current];
    return acc;
  }, {});
};

// node_modules/@strapi/i18n/dist/admin/utils/schemas.mjs
var import_omit = __toESM(require_omit(), 1);
var mutateCTBContentTypeSchema = (nextSchema, prevSchema) => {
  if (!prevSchema) {
    return nextSchema;
  }
  if (!doesPluginOptionsHaveI18nLocalized(nextSchema.pluginOptions)) {
    return nextSchema;
  }
  const isNextSchemaLocalized = nextSchema.pluginOptions.i18n.localized;
  const isPrevSchemaLocalized = doesPluginOptionsHaveI18nLocalized(prevSchema == null ? void 0 : prevSchema.pluginOptions) ? prevSchema == null ? void 0 : prevSchema.pluginOptions.i18n.localized : false;
  if (isNextSchemaLocalized && isPrevSchemaLocalized) {
    return nextSchema;
  }
  if (isNextSchemaLocalized) {
    const attributes = addLocalisationToFields(nextSchema.attributes);
    return {
      ...nextSchema,
      attributes
    };
  }
  if (!isNextSchemaLocalized) {
    const pluginOptions = (0, import_omit.default)(nextSchema.pluginOptions, "i18n");
    const attributes = disableAttributesLocalisation(nextSchema.attributes);
    return {
      ...nextSchema,
      pluginOptions,
      attributes
    };
  }
  return nextSchema;
};
var addLocalisationToFields = (attributes) => {
  return attributes.map((currentAttribute) => {
    if (LOCALIZED_FIELDS.includes(currentAttribute.type)) {
      const i18n = {
        localized: true
      };
      const pluginOptions = currentAttribute.pluginOptions ? {
        ...currentAttribute.pluginOptions,
        i18n
      } : {
        i18n
      };
      return {
        ...currentAttribute,
        pluginOptions
      };
    }
    return currentAttribute;
  });
};
var disableAttributesLocalisation = (attributes) => {
  return attributes.map((currentAttribute) => {
    return (0, import_omit.default)(currentAttribute, "pluginOptions.i18n");
  });
};

// node_modules/@strapi/i18n/dist/admin/index.mjs
function __variableDynamicImportRuntime1__3(path) {
  switch (path) {
    case "./translations/de.json":
      return import("./de.json-YTOGLXJI.js");
    case "./translations/dk.json":
      return import("./dk.json-72I6MEAZ.js");
    case "./translations/en.json":
      return import("./en.json-M6YDYHQC.js");
    case "./translations/es.json":
      return import("./es.json-NYTG6PJT.js");
    case "./translations/fr.json":
      return import("./fr.json-KOZ5KIZ5.js");
    case "./translations/ko.json":
      return import("./ko.json-7LNB7CNC.js");
    case "./translations/pl.json":
      return import("./pl.json-FYKDY6BQ.js");
    case "./translations/ru.json":
      return import("./ru.json-R3U5MBSO.js");
    case "./translations/tr.json":
      return import("./tr.json-HDJKSAL6.js");
    case "./translations/uk.json":
      return import("./uk.json-R5OQDKG4.js");
    case "./translations/zh-Hans.json":
      return import("./zh-Hans.json-JB5YZZP7.js");
    case "./translations/zh.json":
      return import("./zh.json-32A62ZT5.js");
    default:
      return new Promise(function(resolve, reject) {
        (typeof queueMicrotask === "function" ? queueMicrotask : setTimeout)(
          reject.bind(null, new Error("Unknown variable dynamic import: " + path))
        );
      });
  }
}
var index3 = {
  register(app) {
    app.addMiddlewares([
      extendCTBAttributeInitialDataMiddleware,
      extendCTBInitialDataMiddleware
    ]);
    app.addMiddlewares([
      () => i18nApi.middleware
    ]);
    app.addReducers({
      [i18nApi.reducerPath]: i18nApi.reducer
    });
    app.addRBACMiddleware([
      localeMiddleware
    ]);
    app.registerPlugin({
      id: pluginId3,
      name: pluginId3
    });
  },
  bootstrap(app) {
    app.registerHook("Admin/CM/pages/ListView/inject-column-in-table", addColumnToTableHook);
    app.registerHook("Admin/CM/pages/EditView/mutate-edit-view-layout", mutateEditViewHook);
    app.registerHook("ContentReleases/pages/ReleaseDetails/add-locale-in-releases", addLocaleToReleasesHook);
    app.addSettingsLink("global", {
      intlLabel: {
        id: getTranslation("plugin.name"),
        defaultMessage: "Internationalization"
      },
      id: "internationalization",
      to: "internationalization",
      Component: () => import("./SettingsPage-47RYAYLH.js").then((mod) => ({
        default: mod.ProtectedSettingsPage
      })),
      permissions: PERMISSIONS4.accessMain
    });
    const contentManager = app.getPlugin("content-manager");
    contentManager.apis.addDocumentHeaderAction([
      LocalePickerAction,
      FillFromAnotherLocaleAction
    ]);
    contentManager.apis.addDocumentAction((actions) => {
      const indexOfDeleteAction = actions.findIndex((action) => action.type === "delete");
      actions.splice(indexOfDeleteAction, 0, DeleteLocaleAction);
      return actions;
    });
    contentManager.apis.addDocumentAction((actions) => {
      actions.splice(2, 0, BulkLocalePublishAction);
      actions.splice(5, 0, BulkLocaleUnpublishAction);
      return actions;
    });
    contentManager.injectComponent("listView", "actions", {
      name: "i18n-locale-filter",
      Component: LocalePicker
    });
    contentManager.injectComponent("listView", "publishModalAdditionalInfos", {
      name: "i18n-publish-bullets-in-modal",
      Component: PublishModalAdditionalInfo
    });
    contentManager.injectComponent("listView", "unpublishModalAdditionalInfos", {
      name: "i18n-unpublish-bullets-in-modal",
      Component: UnpublishModalAdditionalInfo
    });
    contentManager.injectComponent("listView", "deleteModalAdditionalInfos", {
      name: "i18n-delete-bullets-in-modal",
      Component: DeleteModalAdditionalInfo
    });
    const ctbPlugin = app.getPlugin("content-type-builder");
    if (ctbPlugin) {
      const ctbFormsAPI = ctbPlugin.apis.forms;
      ctbFormsAPI.addContentTypeSchemaMutation(mutateCTBContentTypeSchema);
      ctbFormsAPI.components.add({
        id: "checkboxConfirmation",
        component: CheckboxConfirmation
      });
      ctbFormsAPI.extendContentType({
        validator: () => ({
          i18n: create3().shape({
            localized: create()
          })
        }),
        form: {
          advanced() {
            return [
              {
                name: "pluginOptions.i18n.localized",
                description: {
                  id: getTranslation("plugin.schema.i18n.localized.description-content-type"),
                  defaultMessage: "Allows translating an entry into different languages"
                },
                type: "checkboxConfirmation",
                intlLabel: {
                  id: getTranslation("plugin.schema.i18n.localized.label-content-type"),
                  defaultMessage: "Localization"
                }
              }
            ];
          }
        }
      });
      ctbFormsAPI.extendFields(LOCALIZED_FIELDS, {
        form: {
          advanced({ contentTypeSchema, forTarget, type, step }) {
            if (forTarget !== "contentType") {
              return [];
            }
            const hasI18nEnabled = (0, import_get3.default)(contentTypeSchema, [
              "pluginOptions",
              "i18n",
              "localized"
            ], false);
            if (!hasI18nEnabled) {
              return [];
            }
            if (type === "component" && step === "1") {
              return [];
            }
            return [
              {
                name: "pluginOptions.i18n.localized",
                description: {
                  id: getTranslation("plugin.schema.i18n.localized.description-field"),
                  defaultMessage: "The field can have different values in each locale"
                },
                type: "checkbox",
                intlLabel: {
                  id: getTranslation("plugin.schema.i18n.localized.label-field"),
                  defaultMessage: "Enable localization for this field"
                }
              }
            ];
          }
        }
      });
    }
  },
  async registerTrads({ locales }) {
    const importedTrads = await Promise.all(locales.map((locale) => {
      return __variableDynamicImportRuntime1__3(`./translations/${locale}.json`).then(({ default: data }) => {
        return {
          data: prefixPluginTranslations4(data, pluginId3),
          locale
        };
      }).catch(() => {
        return {
          data: {},
          locale
        };
      });
    }));
    return Promise.resolve(importedTrads);
  }
};

// node_modules/@strapi/content-releases/dist/admin/components/ReleaseAction.mjs
var import_jsx_runtime25 = __toESM(require_jsx_runtime(), 1);
var import_react4 = __toESM(require_react(), 1);

// node_modules/@strapi/content-releases/dist/admin/components/ReleaseActionModal.mjs
var import_jsx_runtime24 = __toESM(require_jsx_runtime(), 1);
var import_react3 = __toESM(require_react(), 1);
var RELEASE_ACTION_FORM_SCHEMA = create3().shape({
  type: create2().oneOf([
    "publish",
    "unpublish"
  ]).required(),
  releaseId: create2().required()
});
var INITIAL_VALUES = {
  type: "publish",
  releaseId: ""
};
var NoReleases = () => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime24.jsx)(EmptyStateLayout, {
    icon: (0, import_jsx_runtime24.jsx)(ForwardRef$J, {
      width: "16rem"
    }),
    content: formatMessage({
      id: "content-releases.content-manager-edit-view.add-to-release.no-releases-message",
      defaultMessage: "No available releases. Open the list of releases and create a new one from there."
    }),
    action: (0, import_jsx_runtime24.jsx)(LinkButton, {
      to: {
        pathname: "/plugins/content-releases"
      },
      tag: Link,
      variant: "secondary",
      children: formatMessage({
        id: "content-releases.content-manager-edit-view.add-to-release.redirect-button",
        defaultMessage: "Open the list of releases"
      })
    }),
    shadow: "none"
  });
};
var AddActionToReleaseModal = ({ contentType, documentId, onInputChange, values }) => {
  var _a, _b, _c;
  const { formatMessage } = useIntl();
  const [{ query }] = useQueryParams();
  const locale = (_b = (_a = query.plugins) == null ? void 0 : _a.i18n) == null ? void 0 : _b.locale;
  const response = useGetReleasesForEntryQuery({
    contentType,
    entryDocumentId: documentId,
    hasEntryAttached: false,
    locale
  });
  const releases = (_c = response.data) == null ? void 0 : _c.data;
  if ((releases == null ? void 0 : releases.length) === 0) {
    return (0, import_jsx_runtime24.jsx)(NoReleases, {});
  }
  return (0, import_jsx_runtime24.jsxs)(Flex, {
    direction: "column",
    alignItems: "stretch",
    gap: 2,
    children: [
      (0, import_jsx_runtime24.jsx)(Box, {
        paddingBottom: 6,
        children: (0, import_jsx_runtime24.jsxs)(Field.Root, {
          required: true,
          children: [
            (0, import_jsx_runtime24.jsx)(Field.Label, {
              children: formatMessage({
                id: "content-releases.content-manager-edit-view.add-to-release.select-label",
                defaultMessage: "Select a release"
              })
            }),
            (0, import_jsx_runtime24.jsx)(SingleSelect, {
              required: true,
              placeholder: formatMessage({
                id: "content-releases.content-manager-edit-view.add-to-release.select-placeholder",
                defaultMessage: "Select"
              }),
              name: "releaseId",
              onChange: (value) => onInputChange("releaseId", value),
              value: values.releaseId,
              children: releases == null ? void 0 : releases.map((release) => (0, import_jsx_runtime24.jsx)(SingleSelectOption, {
                value: release.id,
                children: release.name
              }, release.id))
            })
          ]
        })
      }),
      (0, import_jsx_runtime24.jsx)(Field.Label, {
        children: formatMessage({
          id: "content-releases.content-manager-edit-view.add-to-release.action-type-label",
          defaultMessage: "What do you want to do with this entry?"
        })
      }),
      (0, import_jsx_runtime24.jsx)(ReleaseActionOptions, {
        selected: values.type,
        handleChange: (e) => onInputChange("type", e.target.value),
        name: "type"
      })
    ]
  });
};
var ReleaseActionModalForm = ({ documentId, document, model, collectionType }) => {
  var _a, _b;
  const { formatMessage } = useIntl();
  const { allowedActions } = useRBAC(PERMISSIONS5);
  const { canCreateAction } = allowedActions;
  const [createReleaseAction, { isLoading }] = useCreateReleaseActionMutation();
  const { toggleNotification } = useNotification();
  const { formatAPIError } = useAPIErrorHandler();
  const [{ query }] = useQueryParams();
  const locale = (_b = (_a = query.plugins) == null ? void 0 : _a.i18n) == null ? void 0 : _b.locale;
  const handleSubmit = async (e, onClose) => {
    try {
      await formik.handleSubmit(e);
      onClose();
    } catch (error) {
      if (isFetchError(error)) {
        toggleNotification({
          type: "danger",
          message: formatAPIError(error)
        });
      } else {
        toggleNotification({
          type: "danger",
          message: formatMessage({
            id: "notification.error",
            defaultMessage: "An error occurred"
          })
        });
      }
    }
  };
  const formik = useFormik({
    initialValues: INITIAL_VALUES,
    validationSchema: RELEASE_ACTION_FORM_SCHEMA,
    onSubmit: async (values) => {
      if (collectionType === "collection-types" && !documentId) {
        throw new Error("Document id is required");
      }
      const response = await createReleaseAction({
        body: {
          type: values.type,
          contentType: model,
          entryDocumentId: documentId,
          locale
        },
        params: {
          releaseId: values.releaseId
        }
      });
      if ("data" in response) {
        toggleNotification({
          type: "success",
          message: formatMessage({
            id: "content-releases.content-manager-edit-view.add-to-release.notification.success",
            defaultMessage: "Entry added to release"
          })
        });
        return;
      }
      if ("error" in response) {
        throw response.error;
      }
    }
  });
  const { edit: { options } } = useDocumentLayout(model);
  if (!window.strapi.isEE || !(options == null ? void 0 : options.draftAndPublish) || !canCreateAction) {
    return null;
  }
  if (collectionType === "collection-types" && (!documentId || documentId === "create")) {
    return null;
  }
  return {
    label: formatMessage({
      id: "content-releases.content-manager-edit-view.add-to-release",
      defaultMessage: "Add to release"
    }),
    icon: (0, import_jsx_runtime24.jsx)(ForwardRef$1B, {}),
    // Entry is creating so we don't want to allow adding it to a release
    disabled: !document,
    position: [
      "panel",
      "table-row"
    ],
    dialog: {
      type: "modal",
      title: formatMessage({
        id: "content-releases.content-manager-edit-view.add-to-release",
        defaultMessage: "Add to release"
      }),
      content: (0, import_jsx_runtime24.jsx)(AddActionToReleaseModal, {
        contentType: model,
        documentId,
        onInputChange: formik.setFieldValue,
        values: formik.values
      }),
      footer: ({ onClose }) => (0, import_jsx_runtime24.jsxs)(Modal.Footer, {
        children: [
          (0, import_jsx_runtime24.jsx)(Button, {
            onClick: onClose,
            variant: "tertiary",
            name: "cancel",
            children: formatMessage({
              id: "content-releases.content-manager-edit-view.add-to-release.cancel-button",
              defaultMessage: "Cancel"
            })
          }),
          (0, import_jsx_runtime24.jsx)(Button, {
            type: "submit",
            // @ts-expect-error - formik ReactEvent types don't match button onClick types as they expect a MouseEvent
            onClick: (e) => handleSubmit(e, onClose),
            disabled: !formik.values.releaseId,
            loading: isLoading,
            children: formatMessage({
              id: "content-releases.content-manager-edit-view.add-to-release.continue-button",
              defaultMessage: "Continue"
            })
          })
        ]
      })
    }
  };
};

// node_modules/@strapi/content-releases/dist/admin/components/ReleaseAction.mjs
var getContentPermissions = (subject) => {
  const permissions = {
    publish: [
      {
        action: "plugin::content-manager.explorer.publish",
        subject,
        id: "",
        actionParameters: {},
        properties: {},
        conditions: []
      }
    ]
  };
  return permissions;
};
var ReleaseAction = ({ documents, model }) => {
  var _a;
  const { formatMessage } = useIntl();
  const { toggleNotification } = useNotification();
  const { formatAPIError } = useAPIErrorHandler();
  const [{ query }] = useQueryParams();
  const contentPermissions = getContentPermissions(model);
  const { allowedActions: { canPublish } } = useRBAC(contentPermissions);
  const { allowedActions: { canCreate } } = useRBAC(PERMISSIONS5);
  const { hasDraftAndPublish } = useContentManagerContext();
  const response = useGetReleasesQuery();
  const releases = (_a = response.data) == null ? void 0 : _a.data;
  const [createManyReleaseActions, { isLoading }] = useCreateManyReleaseActionsMutation();
  const documentIds = documents.map((doc) => doc.documentId);
  const handleSubmit = async (values) => {
    var _a2, _b;
    const locale = (_b = (_a2 = query.plugins) == null ? void 0 : _a2.i18n) == null ? void 0 : _b.locale;
    const releaseActionEntries = documentIds.map((entryDocumentId) => ({
      type: values.type,
      contentType: model,
      entryDocumentId,
      locale
    }));
    const response2 = await createManyReleaseActions({
      body: releaseActionEntries,
      params: {
        releaseId: values.releaseId
      }
    });
    if ("data" in response2) {
      const notificationMessage = formatMessage({
        id: "content-releases.content-manager-list-view.add-to-release.notification.success.message",
        defaultMessage: "{entriesAlreadyInRelease} out of {totalEntries} entries were already in the release."
      }, {
        entriesAlreadyInRelease: response2.data.meta.entriesAlreadyInRelease,
        totalEntries: response2.data.meta.totalEntries
      });
      const notification = {
        type: "success",
        title: formatMessage({
          id: "content-releases.content-manager-list-view.add-to-release.notification.success.title",
          defaultMessage: "Successfully added to release."
        }, {
          entriesAlreadyInRelease: response2.data.meta.entriesAlreadyInRelease,
          totalEntries: response2.data.meta.totalEntries
        }),
        message: response2.data.meta.entriesAlreadyInRelease ? notificationMessage : ""
      };
      toggleNotification(notification);
      return true;
    }
    if ("error" in response2) {
      if (isFetchError(response2.error)) {
        toggleNotification({
          type: "warning",
          message: formatAPIError(response2.error)
        });
      } else {
        toggleNotification({
          type: "warning",
          message: formatMessage({
            id: "notification.error",
            defaultMessage: "An error occurred"
          })
        });
      }
    }
  };
  if (!hasDraftAndPublish || !canCreate || !canPublish) return null;
  return {
    actionType: "release",
    variant: "tertiary",
    label: formatMessage({
      id: "content-manager-list-view.add-to-release",
      defaultMessage: "Add to Release"
    }),
    dialog: {
      type: "modal",
      title: formatMessage({
        id: "content-manager-list-view.add-to-release",
        defaultMessage: "Add to Release"
      }),
      content: ({ onClose }) => {
        return (0, import_jsx_runtime25.jsx)(Formik, {
          onSubmit: async (values) => {
            const data = await handleSubmit(values);
            if (data) {
              return onClose();
            }
          },
          validationSchema: RELEASE_ACTION_FORM_SCHEMA,
          initialValues: INITIAL_VALUES,
          children: ({ values, setFieldValue }) => (0, import_jsx_runtime25.jsxs)(Form, {
            children: [
              (releases == null ? void 0 : releases.length) === 0 ? (0, import_jsx_runtime25.jsx)(NoReleases, {}) : (0, import_jsx_runtime25.jsx)(Modal.Body, {
                children: (0, import_jsx_runtime25.jsxs)(Flex, {
                  direction: "column",
                  alignItems: "stretch",
                  gap: 2,
                  children: [
                    (0, import_jsx_runtime25.jsx)(Box, {
                      paddingBottom: 6,
                      children: (0, import_jsx_runtime25.jsxs)(Field.Root, {
                        required: true,
                        children: [
                          (0, import_jsx_runtime25.jsx)(Field.Label, {
                            children: formatMessage({
                              id: "content-releases.content-manager-list-view.add-to-release.select-label",
                              defaultMessage: "Select a release"
                            })
                          }),
                          (0, import_jsx_runtime25.jsx)(SingleSelect, {
                            placeholder: formatMessage({
                              id: "content-releases.content-manager-list-view.add-to-release.select-placeholder",
                              defaultMessage: "Select"
                            }),
                            onChange: (value) => setFieldValue("releaseId", value),
                            value: values.releaseId,
                            children: releases == null ? void 0 : releases.map((release) => (0, import_jsx_runtime25.jsx)(SingleSelectOption, {
                              value: release.id,
                              children: release.name
                            }, release.id))
                          })
                        ]
                      })
                    }),
                    (0, import_jsx_runtime25.jsx)(Field.Label, {
                      children: formatMessage({
                        id: "content-releases.content-manager-list-view.add-to-release.action-type-label",
                        defaultMessage: "What do you want to do with these entries?"
                      })
                    }),
                    (0, import_jsx_runtime25.jsx)(ReleaseActionOptions, {
                      selected: values.type,
                      handleChange: (e) => setFieldValue("type", e.target.value),
                      name: "type"
                    })
                  ]
                })
              }),
              (0, import_jsx_runtime25.jsxs)(Modal.Footer, {
                children: [
                  (0, import_jsx_runtime25.jsx)(Button, {
                    onClick: onClose,
                    variant: "tertiary",
                    name: "cancel",
                    children: formatMessage({
                      id: "content-releases.content-manager-list-view.add-to-release.cancel-button",
                      defaultMessage: "Cancel"
                    })
                  }),
                  (0, import_jsx_runtime25.jsx)(Button, {
                    type: "submit",
                    disabled: !values.releaseId,
                    loading: isLoading,
                    children: formatMessage({
                      id: "content-releases.content-manager-list-view.add-to-release.continue-button",
                      defaultMessage: "Continue"
                    })
                  })
                ]
              })
            ]
          })
        });
      }
    }
  };
};

// node_modules/@strapi/content-releases/dist/admin/components/ReleaseListCell.mjs
var import_jsx_runtime26 = __toESM(require_jsx_runtime(), 1);
var import_react5 = __toESM(require_react(), 1);
var useReleasesList = (contentTypeUid, documentId) => {
  var _a, _b;
  const listViewData = useTable("ListView", (state) => state.rows);
  const documentIds = listViewData.map((entry) => entry.documentId);
  const [{ query }] = useQueryParams();
  const locale = ((_b = (_a = query == null ? void 0 : query.plugins) == null ? void 0 : _a.i18n) == null ? void 0 : _b.locale) || void 0;
  const response = useGetMappedEntriesInReleasesQuery({
    contentTypeUid,
    documentIds,
    locale
  }, {
    skip: !documentIds || !contentTypeUid || documentIds.length === 0
  });
  const mappedEntriesInReleases = response.data || {};
  return (mappedEntriesInReleases == null ? void 0 : mappedEntriesInReleases[documentId]) || [];
};
var addColumnToTableHook2 = ({ displayedHeaders, layout }) => {
  const { options } = layout;
  if (!(options == null ? void 0 : options.draftAndPublish)) {
    return {
      displayedHeaders,
      layout
    };
  }
  return {
    displayedHeaders: [
      ...displayedHeaders,
      {
        searchable: false,
        sortable: false,
        name: "releases",
        label: {
          id: "content-releases.content-manager.list-view.releases.header",
          defaultMessage: "To be released in"
        },
        cellFormatter: (props, _, { model }) => (0, import_jsx_runtime26.jsx)(ReleaseListCell, {
          ...props,
          model
        })
      }
    ],
    layout
  };
};
var ReleaseListCell = ({ documentId, model }) => {
  const releases = useReleasesList(model, documentId);
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime26.jsxs)(Popover.Root, {
    children: [
      (0, import_jsx_runtime26.jsx)(Popover.Trigger, {
        children: (0, import_jsx_runtime26.jsx)(Button, {
          variant: "ghost",
          onClick: (e) => e.stopPropagation(),
          // TODO: find a way in the DS to define the widht and height of the icon
          endIcon: releases.length > 0 ? (0, import_jsx_runtime26.jsx)(ForwardRef$4T, {
            width: "1.2rem",
            height: "1.2rem"
          }) : null,
          children: (0, import_jsx_runtime26.jsx)(Typography, {
            style: {
              maxWidth: "252px",
              cursor: "pointer"
            },
            textColor: "neutral800",
            fontWeight: "regular",
            children: releases.length > 0 ? formatMessage({
              id: "content-releases.content-manager.list-view.releases-number",
              defaultMessage: "{number} {number, plural, one {release} other {releases}}"
            }, {
              number: releases.length
            }) : "-"
          })
        })
      }),
      (0, import_jsx_runtime26.jsx)(Popover.Content, {
        children: (0, import_jsx_runtime26.jsx)("ul", {
          children: releases.map(({ id, name: name2 }) => (0, import_jsx_runtime26.jsx)(Box, {
            padding: 3,
            tag: "li",
            children: (0, import_jsx_runtime26.jsx)(Link2, {
              href: `/admin/plugins/content-releases/${id}`,
              isExternal: false,
              children: name2
            })
          }, id))
        })
      })
    ]
  });
};

// node_modules/@strapi/content-releases/dist/admin/components/ReleasesPanel.mjs
var import_jsx_runtime27 = __toESM(require_jsx_runtime(), 1);
var Panel = ({ model, document, documentId, collectionType }) => {
  var _a, _b, _c;
  const [{ query }] = useQueryParams();
  const locale = (_b = (_a = query.plugins) == null ? void 0 : _a.i18n) == null ? void 0 : _b.locale;
  const { edit: { options } } = useDocumentLayout(model);
  const { formatMessage, formatDate, formatTime } = useIntl();
  const { allowedActions } = useRBAC(PERMISSIONS5);
  const { canRead, canDeleteAction } = allowedActions;
  const response = useGetReleasesForEntryQuery({
    contentType: model,
    entryDocumentId: documentId,
    locale,
    hasEntryAttached: true
  }, {
    skip: !document
  });
  const releases = (_c = response.data) == null ? void 0 : _c.data;
  const getReleaseColorVariant = (actionType, shade) => {
    if (actionType === "unpublish") {
      return `secondary${shade}`;
    }
    return `success${shade}`;
  };
  if (!window.strapi.isEE || !(options == null ? void 0 : options.draftAndPublish) || !canRead) {
    return null;
  }
  if (collectionType === "collection-types" && (!documentId || documentId === "create")) {
    return null;
  }
  if (!releases || releases.length === 0) {
    return null;
  }
  return {
    title: formatMessage({
      id: "content-releases.plugin.name",
      defaultMessage: "Releases"
    }),
    content: (0, import_jsx_runtime27.jsx)(Flex, {
      direction: "column",
      alignItems: "stretch",
      gap: 3,
      width: "100%",
      children: releases == null ? void 0 : releases.map((release) => (0, import_jsx_runtime27.jsxs)(Flex, {
        direction: "column",
        alignItems: "start",
        borderWidth: "1px",
        borderStyle: "solid",
        borderColor: getReleaseColorVariant(release.actions[0].type, "200"),
        overflow: "hidden",
        hasRadius: true,
        children: [
          (0, import_jsx_runtime27.jsx)(Box, {
            paddingTop: 3,
            paddingBottom: 3,
            paddingLeft: 4,
            paddingRight: 4,
            background: getReleaseColorVariant(release.actions[0].type, "100"),
            width: "100%",
            children: (0, import_jsx_runtime27.jsx)(Typography, {
              fontSize: 1,
              variant: "pi",
              textColor: getReleaseColorVariant(release.actions[0].type, "600"),
              children: formatMessage({
                id: "content-releases.content-manager-edit-view.list-releases.title",
                defaultMessage: "{isPublish, select, true {Will be published in} other {Will be unpublished in}}"
              }, {
                isPublish: release.actions[0].type === "publish"
              })
            })
          }),
          (0, import_jsx_runtime27.jsxs)(Flex, {
            padding: 4,
            direction: "column",
            gap: 2,
            width: "100%",
            alignItems: "flex-start",
            children: [
              (0, import_jsx_runtime27.jsx)(Typography, {
                fontSize: 2,
                fontWeight: "bold",
                variant: "omega",
                textColor: "neutral700",
                children: release.name
              }),
              release.scheduledAt && release.timezone && (0, import_jsx_runtime27.jsx)(Typography, {
                variant: "pi",
                textColor: "neutral600",
                children: formatMessage({
                  id: "content-releases.content-manager-edit-view.scheduled.date",
                  defaultMessage: "{date} at {time} ({offset})"
                }, {
                  date: formatDate(new Date(release.scheduledAt), {
                    day: "2-digit",
                    month: "2-digit",
                    year: "numeric",
                    timeZone: release.timezone
                  }),
                  time: formatTime(new Date(release.scheduledAt), {
                    hourCycle: "h23",
                    timeZone: release.timezone
                  }),
                  offset: getTimezoneOffset(release.timezone, new Date(release.scheduledAt))
                })
              }),
              canDeleteAction ? (0, import_jsx_runtime27.jsxs)(ReleaseActionMenu.Root, {
                hasTriggerBorder: true,
                children: [
                  (0, import_jsx_runtime27.jsx)(ReleaseActionMenu.EditReleaseItem, {
                    releaseId: release.id
                  }),
                  (0, import_jsx_runtime27.jsx)(ReleaseActionMenu.DeleteReleaseActionItem, {
                    releaseId: release.id,
                    actionId: release.actions[0].id
                  })
                ]
              }) : null
            ]
          })
        ]
      }, release.id))
    })
  };
};

// node_modules/@strapi/content-releases/dist/admin/utils/prefixPluginTranslations.mjs
var prefixPluginTranslations5 = (trad, pluginId5) => {
  return Object.keys(trad).reduce((acc, current) => {
    acc[`${pluginId5}.${current}`] = trad[current];
    return acc;
  }, {});
};

// node_modules/@strapi/content-releases/dist/admin/index.mjs
function __variableDynamicImportRuntime3__(path) {
  switch (path) {
    case "./translations/en.json":
      return import("./en.json-77PLR44Q.js");
    case "./translations/uk.json":
      return import("./uk.json-R7E67MQ6.js");
    default:
      return new Promise(function(resolve, reject) {
        (typeof queueMicrotask === "function" ? queueMicrotask : setTimeout)(
          reject.bind(null, new Error("Unknown variable dynamic import: " + path))
        );
      });
  }
}
var admin3 = {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  register(app) {
    var _a, _b;
    app.createHook("ContentReleases/pages/ReleaseDetails/add-locale-in-releases");
    if (window.strapi.features.isEnabled("cms-content-releases")) {
      app.addMenuLink({
        to: `plugins/${pluginId4}`,
        icon: ForwardRef$1B,
        intlLabel: {
          id: `${pluginId4}.plugin.name`,
          defaultMessage: "Releases"
        },
        Component: () => import("./App-NP6BJPUD.js").then((mod) => ({
          default: mod.App
        })),
        permissions: PERMISSIONS5.main,
        position: 2
      });
      const contentManagerPluginApis = app.getPlugin("content-manager").apis;
      if ("addEditViewSidePanel" in contentManagerPluginApis && typeof contentManagerPluginApis.addEditViewSidePanel === "function") {
        contentManagerPluginApis.addEditViewSidePanel([
          Panel
        ]);
      }
      if ("addDocumentAction" in contentManagerPluginApis && typeof contentManagerPluginApis.addDocumentAction === "function") {
        contentManagerPluginApis.addDocumentAction((actions) => {
          const indexOfDeleteAction = actions.findIndex((action) => action.type === "unpublish");
          actions.splice(indexOfDeleteAction, 0, ReleaseActionModalForm);
          return actions;
        });
      }
      app.addSettingsLink("global", {
        id: pluginId4,
        to: "releases",
        intlLabel: {
          id: `${pluginId4}.plugin.name`,
          defaultMessage: "Releases"
        },
        permissions: [],
        async Component() {
          const { ProtectedReleasesSettingsPage } = await import("./ReleasesSettingsPage-EM2ZP4SX.js");
          return {
            default: ProtectedReleasesSettingsPage
          };
        }
      });
      if ("addBulkAction" in contentManagerPluginApis && typeof contentManagerPluginApis.addBulkAction === "function") {
        contentManagerPluginApis.addBulkAction((actions) => {
          const deleteActionIndex = actions.findIndex((action) => action.type === "delete");
          actions.splice(deleteActionIndex, 0, ReleaseAction);
          return actions;
        });
      }
      app.registerHook("Admin/CM/pages/ListView/inject-column-in-table", addColumnToTableHook2);
    } else if (!window.strapi.features.isEnabled("cms-content-releases") && ((_b = (_a = window.strapi) == null ? void 0 : _a.flags) == null ? void 0 : _b.promoteEE)) {
      app.addSettingsLink("global", {
        id: pluginId4,
        to: "/plugins/purchase-content-releases",
        intlLabel: {
          id: `${pluginId4}.plugin.name`,
          defaultMessage: "Releases"
        },
        permissions: [],
        async Component() {
          const { PurchaseContentReleases } = await import("./PurchaseContentReleases-HD3XDN64.js");
          return {
            default: PurchaseContentReleases
          };
        },
        licenseOnly: true
      });
    }
  },
  async registerTrads({ locales }) {
    const importedTrads = await Promise.all(locales.map((locale) => {
      return __variableDynamicImportRuntime3__(`./translations/${locale}.json`).then(({ default: data }) => {
        return {
          data: prefixPluginTranslations5(data, "content-releases"),
          locale
        };
      }).catch(() => {
        return {
          data: {},
          locale
        };
      });
    }));
    return Promise.resolve(importedTrads);
  }
};

// node_modules/@strapi/review-workflows/dist/admin/routes/content-manager/model/id/components/Header.mjs
var import_jsx_runtime30 = __toESM(require_jsx_runtime(), 1);

// node_modules/@strapi/review-workflows/dist/admin/routes/content-manager/model/id/components/AssigneeSelect.mjs
var import_jsx_runtime28 = __toESM(require_jsx_runtime(), 1);
var React15 = __toESM(require_react(), 1);

// node_modules/@strapi/review-workflows/dist/admin/utils/users.mjs
var getDisplayName = ({ firstname, lastname, username, email } = {}) => {
  if (username) {
    return username;
  }
  if (firstname) {
    return `${firstname} ${lastname ?? ""}`.trim();
  }
  return email ?? "";
};

// node_modules/@strapi/review-workflows/dist/admin/routes/content-manager/model/id/components/constants.mjs
var STAGE_ATTRIBUTE_NAME = "strapi_stage";
var ASSIGNEE_ATTRIBUTE_NAME = "strapi_assignee";

// node_modules/@strapi/review-workflows/dist/admin/routes/content-manager/model/id/components/AssigneeSelect.mjs
var AssigneeSelect = ({ isCompact }) => {
  var _a;
  const { collectionType = "", id, slug: model = "" } = useParams();
  const permissions = useTypedSelector((state) => state.admin_app.permissions);
  const { formatMessage } = useIntl();
  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();
  const { toggleNotification } = useNotification();
  const { allowedActions: { canRead }, isLoading: isLoadingPermissions } = useRBAC((_a = permissions.settings) == null ? void 0 : _a.users);
  const [{ query }] = useQueryParams();
  const params = React15.useMemo(() => buildValidParams2(query), [
    query
  ]);
  const { data, isLoading: isLoadingUsers, isError } = useAdminUsers(void 0, {
    skip: isLoadingPermissions || !canRead
  });
  const { document } = useDocument({
    collectionType,
    model,
    documentId: id
  }, {
    skip: !id && collectionType !== "single-types"
  });
  const users = (data == null ? void 0 : data.users) || [];
  const currentAssignee = document ? document[ASSIGNEE_ATTRIBUTE_NAME] : null;
  const [updateAssignee, { error, isLoading: isMutating }] = useUpdateAssigneeMutation();
  if (!collectionType || !model || !(document == null ? void 0 : document.documentId)) {
    return null;
  }
  const handleChange = async (assigneeId) => {
    if ((currentAssignee == null ? void 0 : currentAssignee.id) === assigneeId) {
      return;
    }
    const res = await updateAssignee({
      slug: collectionType,
      model,
      id: document.documentId,
      params,
      data: {
        id: assigneeId ? parseInt(assigneeId, 10) : null
      }
    });
    if ("data" in res) {
      toggleNotification({
        type: "success",
        message: formatMessage({
          id: "content-manager.reviewWorkflows.assignee.notification.saved",
          defaultMessage: "Assignee updated"
        })
      });
    }
    if (isCompact && "error" in res) {
      toggleNotification({
        type: "danger",
        message: formatAPIError(res.error)
      });
    }
  };
  const isDisabled = !isLoadingPermissions && !isLoadingUsers && users.length === 0 || !document.documentId;
  const isLoading = isLoadingUsers || isLoadingPermissions || isMutating;
  const assigneeLabel = formatMessage({
    id: "content-manager.reviewWorkflows.assignee.label",
    defaultMessage: "Assignee"
  });
  const assigneeClearLabel = formatMessage({
    id: "content-manager.reviewWorkflows.assignee.clear",
    defaultMessage: "Clear assignee"
  });
  const assigneePlaceholder = formatMessage({
    id: "content-manager.reviewWorkflows.assignee.placeholder",
    defaultMessage: "Select…"
  });
  if (isCompact) {
    return (0, import_jsx_runtime28.jsxs)(Field.Root, {
      name: ASSIGNEE_ATTRIBUTE_NAME,
      id: ASSIGNEE_ATTRIBUTE_NAME,
      children: [
        (0, import_jsx_runtime28.jsx)(VisuallyHidden, {
          children: (0, import_jsx_runtime28.jsx)(Field.Label, {
            children: assigneeLabel
          })
        }),
        (0, import_jsx_runtime28.jsx)(Combobox, {
          clearLabel: assigneeClearLabel,
          disabled: isDisabled,
          value: currentAssignee ? currentAssignee.id.toString() : null,
          onChange: handleChange,
          onClear: () => handleChange(null),
          placeholder: assigneePlaceholder,
          loading: isLoading || isLoadingPermissions || isMutating,
          size: "S",
          children: users.map((user) => {
            return (0, import_jsx_runtime28.jsx)(Option, {
              value: user.id.toString(),
              textValue: getDisplayName(user),
              children: getDisplayName(user)
            }, user.id);
          })
        })
      ]
    });
  }
  return (0, import_jsx_runtime28.jsxs)(Field.Root, {
    name: ASSIGNEE_ATTRIBUTE_NAME,
    id: ASSIGNEE_ATTRIBUTE_NAME,
    error: (isError && canRead && formatMessage({
      id: "content-manager.reviewWorkflows.assignee.error",
      defaultMessage: "An error occurred while fetching users"
    }) || error && formatAPIError(error)) ?? void 0,
    children: [
      (0, import_jsx_runtime28.jsx)(Field.Label, {
        children: assigneeLabel
      }),
      (0, import_jsx_runtime28.jsx)(Combobox, {
        clearLabel: assigneeClearLabel,
        disabled: !isLoadingPermissions && !isLoading && users.length === 0 || !document.documentId,
        value: currentAssignee ? currentAssignee.id.toString() : null,
        onChange: handleChange,
        onClear: () => handleChange(null),
        placeholder: assigneePlaceholder,
        loading: isLoading || isLoadingPermissions || isMutating,
        children: users.map((user) => {
          return (0, import_jsx_runtime28.jsx)(Option, {
            value: user.id.toString(),
            textValue: getDisplayName(user),
            children: getDisplayName(user)
          }, user.id);
        })
      }),
      (0, import_jsx_runtime28.jsx)(Field.Error, {})
    ]
  });
};

// node_modules/@strapi/review-workflows/dist/admin/routes/content-manager/model/id/components/StageSelect.mjs
var import_jsx_runtime29 = __toESM(require_jsx_runtime(), 1);
var React16 = __toESM(require_react(), 1);
var WorkflowLimitModal = ({ open, onOpenChange }) => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime29.jsxs)(LimitsModal.Root, {
    open,
    onOpenChange,
    children: [
      (0, import_jsx_runtime29.jsx)(LimitsModal.Title, {
        children: formatMessage({
          id: "content-manager.reviewWorkflows.workflows.limit.title",
          defaultMessage: "You’ve reached the limit of workflows in your plan"
        })
      }),
      (0, import_jsx_runtime29.jsx)(LimitsModal.Body, {
        children: formatMessage({
          id: "content-manager.reviewWorkflows.workflows.limit.body",
          defaultMessage: "Delete a workflow or contact Sales to enable more workflows."
        })
      })
    ]
  });
};
var StageLimitModal = ({ open, onOpenChange }) => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime29.jsxs)(LimitsModal.Root, {
    open,
    onOpenChange,
    children: [
      (0, import_jsx_runtime29.jsx)(LimitsModal.Title, {
        children: formatMessage({
          id: "content-manager.reviewWorkflows.stages.limit.title",
          defaultMessage: "You have reached the limit of stages for this workflow in your plan"
        })
      }),
      (0, import_jsx_runtime29.jsx)(LimitsModal.Body, {
        children: formatMessage({
          id: "content-manager.reviewWorkflows.stages.limit.body",
          defaultMessage: "Try deleting some stages or contact Sales to enable more stages."
        })
      })
    ]
  });
};
var Select = ({ stages, activeWorkflowStage, isLoading, ...props }) => {
  const { formatMessage } = useIntl();
  const { themeColorName } = getStageColorByHex(activeWorkflowStage == null ? void 0 : activeWorkflowStage.color) ?? {};
  return (0, import_jsx_runtime29.jsx)(SingleSelect, {
    disabled: stages.length === 0,
    placeholder: formatMessage({
      id: "content-manager.reviewWorkflows.assignee.placeholder",
      defaultMessage: "Select…"
    }),
    startIcon: activeWorkflowStage && (0, import_jsx_runtime29.jsx)(Flex, {
      tag: "span",
      height: 2,
      background: activeWorkflowStage == null ? void 0 : activeWorkflowStage.color,
      borderColor: themeColorName === "neutral0" ? "neutral150" : void 0,
      hasRadius: true,
      shrink: 0,
      width: 2,
      marginRight: "-3px"
    }),
    // @ts-expect-error – `customizeContent` is not correctly typed in the DS.
    customizeContent: () => {
      return (0, import_jsx_runtime29.jsxs)(Flex, {
        tag: "span",
        justifyContent: "space-between",
        alignItems: "center",
        width: "100%",
        children: [
          (0, import_jsx_runtime29.jsx)(Typography, {
            textColor: "neutral800",
            ellipsis: true,
            lineHeight: "inherit",
            children: (activeWorkflowStage == null ? void 0 : activeWorkflowStage.name) ?? ""
          }),
          isLoading ? (0, import_jsx_runtime29.jsx)(Loader, {
            small: true,
            style: {
              display: "flex"
            },
            "data-testid": "loader"
          }) : null
        ]
      });
    },
    ...props,
    children: stages.map(({ id, color, name: name2 }) => {
      const { themeColorName: themeColorName2 } = getStageColorByHex(color) ?? {};
      return (0, import_jsx_runtime29.jsx)(SingleSelectOption, {
        startIcon: (0, import_jsx_runtime29.jsx)(Flex, {
          height: 2,
          background: color,
          borderColor: themeColorName2 === "neutral0" ? "neutral150" : void 0,
          hasRadius: true,
          shrink: 0,
          width: 2
        }),
        value: id,
        textValue: name2,
        children: name2
      }, id);
    })
  });
};
var StageSelect = ({ isCompact }) => {
  const { collectionType = "", slug: model = "", id = "" } = useParams();
  const { formatMessage } = useIntl();
  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();
  const { toggleNotification } = useNotification();
  const [{ query }] = useQueryParams();
  const params = React16.useMemo(() => buildValidParams2(query), [
    query
  ]);
  const { document, isLoading: isLoadingDocument } = useDocument({
    collectionType,
    model,
    documentId: id
  }, {
    skip: !id && collectionType !== "single-types"
  });
  const { data, isLoading: isLoadingStages } = useGetStagesQuery({
    slug: collectionType,
    model,
    // @ts-expect-error – `id` is not correctly typed in the DS.
    id: document == null ? void 0 : document.documentId,
    params
  }, {
    skip: !(document == null ? void 0 : document.documentId)
  });
  const { meta, stages = [] } = data ?? {};
  const { getFeature } = useLicenseLimits();
  const [showLimitModal, setShowLimitModal] = React16.useState(null);
  const limits = getFeature("review-workflows") ?? {};
  const activeWorkflowStage = document ? document[STAGE_ATTRIBUTE_NAME] : null;
  const [updateStage, { error }] = useUpdateStageMutation();
  const handleChange = async (stageId) => {
    try {
      if ((limits == null ? void 0 : limits[CHARGEBEE_WORKFLOW_ENTITLEMENT_NAME]) && parseInt(limits[CHARGEBEE_WORKFLOW_ENTITLEMENT_NAME], 10) < ((meta == null ? void 0 : meta.workflowCount) ?? 0)) {
        setShowLimitModal("workflow");
      } else if ((limits == null ? void 0 : limits[CHARGEBEE_STAGES_PER_WORKFLOW_ENTITLEMENT_NAME]) && parseInt(limits[CHARGEBEE_STAGES_PER_WORKFLOW_ENTITLEMENT_NAME], 10) < stages.length) {
        setShowLimitModal("stage");
      } else {
        if (document == null ? void 0 : document.documentId) {
          const res = await updateStage({
            model,
            id: document.documentId,
            slug: collectionType,
            params,
            data: {
              id: stageId
            }
          });
          if ("data" in res) {
            toggleNotification({
              type: "success",
              message: formatMessage({
                id: "content-manager.reviewWorkflows.stage.notification.saved",
                defaultMessage: "Review stage updated"
              })
            });
          }
          if (isCompact && "error" in res) {
            toggleNotification({
              type: "danger",
              message: formatAPIError(res.error)
            });
          }
        }
      }
    } catch (error2) {
      toggleNotification({
        type: "danger",
        message: formatMessage({
          id: "content-manager.reviewWorkflows.stage.notification.error",
          defaultMessage: "An error occurred while updating the review stage"
        })
      });
    }
  };
  const isLoading = isLoadingStages || isLoadingDocument;
  const reviewStageLabel = formatMessage({
    id: "content-manager.reviewWorkflows.stage.label",
    defaultMessage: "Review stage"
  });
  const reviewStageHint = !isLoading && stages.length === 0 && // TODO: Handle errors and hints
  formatMessage({
    id: "content-manager.reviewWorkflows.stages.no-transition",
    defaultMessage: "You don’t have the permission to update this stage."
  });
  if (isCompact) {
    return (0, import_jsx_runtime29.jsxs)(import_jsx_runtime29.Fragment, {
      children: [
        (0, import_jsx_runtime29.jsx)(TooltipImpl, {
          label: reviewStageHint,
          children: (0, import_jsx_runtime29.jsx)(Field.Root, {
            name: STAGE_ATTRIBUTE_NAME,
            id: STAGE_ATTRIBUTE_NAME,
            children: (0, import_jsx_runtime29.jsxs)(import_jsx_runtime29.Fragment, {
              children: [
                (0, import_jsx_runtime29.jsx)(VisuallyHidden, {
                  children: (0, import_jsx_runtime29.jsx)(Field.Label, {
                    children: reviewStageLabel
                  })
                }),
                (0, import_jsx_runtime29.jsx)(Select, {
                  stages,
                  activeWorkflowStage,
                  isLoading,
                  size: "S",
                  disabled: stages.length === 0,
                  value: activeWorkflowStage == null ? void 0 : activeWorkflowStage.id,
                  onChange: handleChange,
                  placeholder: formatMessage({
                    id: "content-manager.reviewWorkflows.assignee.placeholder",
                    defaultMessage: "Select…"
                  })
                })
              ]
            })
          })
        }),
        (0, import_jsx_runtime29.jsx)(WorkflowLimitModal, {
          open: showLimitModal === "workflow",
          onOpenChange: () => setShowLimitModal(null)
        }),
        (0, import_jsx_runtime29.jsx)(StageLimitModal, {
          open: showLimitModal === "stage",
          onOpenChange: () => setShowLimitModal(null)
        })
      ]
    });
  }
  return (0, import_jsx_runtime29.jsxs)(import_jsx_runtime29.Fragment, {
    children: [
      (0, import_jsx_runtime29.jsxs)(Field.Root, {
        hint: reviewStageHint,
        error: error && formatAPIError(error) || void 0,
        name: STAGE_ATTRIBUTE_NAME,
        id: STAGE_ATTRIBUTE_NAME,
        children: [
          (0, import_jsx_runtime29.jsx)(Field.Label, {
            children: reviewStageLabel
          }),
          (0, import_jsx_runtime29.jsx)(Select, {
            stages,
            activeWorkflowStage,
            isLoading,
            disabled: stages.length === 0,
            value: activeWorkflowStage == null ? void 0 : activeWorkflowStage.id,
            onChange: handleChange,
            placeholder: formatMessage({
              id: "content-manager.reviewWorkflows.assignee.placeholder",
              defaultMessage: "Select…"
            })
          }),
          (0, import_jsx_runtime29.jsx)(Field.Hint, {}),
          (0, import_jsx_runtime29.jsx)(Field.Error, {})
        ]
      }),
      (0, import_jsx_runtime29.jsx)(WorkflowLimitModal, {
        open: showLimitModal === "workflow",
        onOpenChange: () => setShowLimitModal(null)
      }),
      (0, import_jsx_runtime29.jsx)(StageLimitModal, {
        open: showLimitModal === "stage",
        onOpenChange: () => setShowLimitModal(null)
      })
    ]
  });
};

// node_modules/@strapi/review-workflows/dist/admin/routes/content-manager/model/id/components/Header.mjs
var Header = () => {
  const { slug = "", id, collectionType } = useParams();
  const { edit: { options } } = useDocumentLayout(slug);
  if (!window.strapi.isEE || !(options == null ? void 0 : options.reviewWorkflows) || collectionType !== "single-types" && !id || id === "create") {
    return null;
  }
  return (0, import_jsx_runtime30.jsxs)(Flex, {
    gap: 2,
    children: [
      (0, import_jsx_runtime30.jsx)(AssigneeSelect, {
        isCompact: true
      }),
      (0, import_jsx_runtime30.jsx)(StageSelect, {
        isCompact: true
      })
    ]
  });
};
Header.type = "preview";

// node_modules/@strapi/review-workflows/dist/admin/routes/content-manager/model/id/components/Panel.mjs
var import_jsx_runtime31 = __toESM(require_jsx_runtime(), 1);
var Panel2 = () => {
  const { slug = "", id, collectionType } = useParams();
  const { edit: { options } } = useDocumentLayout(slug);
  const { formatMessage } = useIntl();
  if (!window.strapi.isEE || !(options == null ? void 0 : options.reviewWorkflows) || collectionType !== "single-types" && !id || id === "create") {
    return null;
  }
  return {
    title: formatMessage({
      id: "content-manager.containers.edit.panels.review-workflows.title",
      defaultMessage: "Review Workflows"
    }),
    content: (0, import_jsx_runtime31.jsxs)(Flex, {
      direction: "column",
      gap: 2,
      alignItems: "stretch",
      width: "100%",
      children: [
        (0, import_jsx_runtime31.jsx)(AssigneeSelect, {}),
        (0, import_jsx_runtime31.jsx)(StageSelect, {})
      ]
    })
  };
};
Panel2.type = "review-workflows";

// node_modules/@strapi/review-workflows/dist/admin/routes/content-manager/model/constants.mjs
var import_jsx_runtime33 = __toESM(require_jsx_runtime(), 1);
var import_react6 = __toESM(require_react(), 1);

// node_modules/@strapi/review-workflows/dist/admin/routes/content-manager/model/components/TableColumns.mjs
var import_jsx_runtime32 = __toESM(require_jsx_runtime(), 1);
var StageColumn = (props) => {
  const { color = STAGE_COLOR_DEFAULT, name: name2 } = props.strapi_stage ?? {};
  const { themeColorName } = getStageColorByHex(color) ?? {};
  return (0, import_jsx_runtime32.jsxs)(Flex, {
    alignItems: "center",
    gap: 2,
    maxWidth: "30rem",
    children: [
      (0, import_jsx_runtime32.jsx)(Box, {
        height: 2,
        background: color,
        borderColor: themeColorName === "neutral0" ? "neutral150" : void 0,
        hasRadius: true,
        shrink: 0,
        width: 2
      }),
      (0, import_jsx_runtime32.jsx)(Typography, {
        fontWeight: "regular",
        textColor: "neutral700",
        ellipsis: true,
        children: name2
      })
    ]
  });
};
var AssigneeColumn = (props) => {
  const { strapi_assignee: user } = props;
  return (0, import_jsx_runtime32.jsx)(Typography, {
    textColor: "neutral800",
    children: user ? getDisplayName(user) : "-"
  });
};

// node_modules/@strapi/review-workflows/dist/admin/routes/content-manager/model/constants.mjs
var REVIEW_WORKFLOW_COLUMNS = [
  {
    name: STAGE_ATTRIBUTE_NAME,
    attribute: {
      type: "relation",
      relation: "oneToMany",
      target: "admin::review-workflow-stage"
    },
    label: {
      id: "review-workflows.containers.list.table-headers.reviewWorkflows.stage",
      defaultMessage: "Review stage"
    },
    searchable: false,
    sortable: true,
    mainField: {
      name: "name",
      type: "string"
    },
    cellFormatter: (props) => (0, import_jsx_runtime33.jsx)(StageColumn, {
      ...props
    })
  },
  {
    name: ASSIGNEE_ATTRIBUTE_NAME,
    attribute: {
      type: "relation",
      target: "admin::user",
      relation: "oneToMany"
    },
    label: {
      id: "review-workflows.containers.list.table-headers.reviewWorkflows.assignee",
      defaultMessage: "Assignee"
    },
    searchable: false,
    sortable: true,
    mainField: {
      name: "firstname",
      type: "string"
    },
    cellFormatter: (props) => (0, import_jsx_runtime33.jsx)(AssigneeColumn, {
      ...props
    })
  }
];

// node_modules/@strapi/review-workflows/dist/admin/utils/cm-hooks.mjs
var addColumnToTableHook3 = ({ displayedHeaders, layout }) => {
  const { options } = layout;
  if (!options.reviewWorkflows) {
    return {
      displayedHeaders,
      layout
    };
  }
  return {
    displayedHeaders: [
      ...displayedHeaders,
      ...REVIEW_WORKFLOW_COLUMNS
    ],
    layout
  };
};

// node_modules/@strapi/review-workflows/dist/admin/utils/translations.mjs
var prefixPluginTranslations6 = (trad, pluginId5) => {
  return Object.keys(trad).reduce((acc, current) => {
    acc[`${pluginId5}.${current}`] = trad[current];
    return acc;
  }, {});
};

// node_modules/@strapi/review-workflows/dist/admin/index.mjs
function __variableDynamicImportRuntime2__2(path) {
  switch (path) {
    case "./translations/en.json":
      return import("./en.json-ACWIBMGZ.js");
    case "./translations/uk.json":
      return import("./uk.json-AEVQXBJA.js");
    default:
      return new Promise(function(resolve, reject) {
        (typeof queueMicrotask === "function" ? queueMicrotask : setTimeout)(
          reject.bind(null, new Error("Unknown variable dynamic import: " + path))
        );
      });
  }
}
var admin4 = {
  register(app) {
    var _a, _b;
    if (window.strapi.features.isEnabled(FEATURE_ID)) {
      app.registerHook("Admin/CM/pages/ListView/inject-column-in-table", addColumnToTableHook3);
      const contentManagerPluginApis = app.getPlugin("content-manager").apis;
      if ("addEditViewSidePanel" in contentManagerPluginApis && typeof contentManagerPluginApis.addEditViewSidePanel === "function") {
        contentManagerPluginApis.addEditViewSidePanel([
          Panel2
        ]);
      }
      app.addSettingsLink("global", {
        id: PLUGIN_ID,
        to: `review-workflows`,
        intlLabel: {
          id: `${PLUGIN_ID}.plugin.name`,
          defaultMessage: "Review Workflows"
        },
        permissions: [],
        async Component() {
          const { Router } = await import("./router-7YS3ESGO.js");
          return {
            default: Router
          };
        }
      });
    } else if (!window.strapi.features.isEnabled(FEATURE_ID) && ((_b = (_a = window.strapi) == null ? void 0 : _a.flags) == null ? void 0 : _b.promoteEE)) {
      app.addSettingsLink("global", {
        id: PLUGIN_ID,
        to: `purchase-review-workflows`,
        intlLabel: {
          id: `${PLUGIN_ID}.plugin.name`,
          defaultMessage: "Review Workflows"
        },
        licenseOnly: true,
        permissions: [],
        async Component() {
          const { PurchaseReviewWorkflows } = await import("./purchase-review-workflows-3VQ4DIPV.js");
          return {
            default: PurchaseReviewWorkflows
          };
        }
      });
    }
  },
  bootstrap(app) {
    if (window.strapi.features.isEnabled(FEATURE_ID)) {
      app.getPlugin("content-manager").injectComponent("preview", "actions", {
        name: "review-workflows-assignee",
        Component: Header
      });
    }
  },
  async registerTrads({ locales }) {
    const importedTrads = await Promise.all(locales.map((locale) => {
      return __variableDynamicImportRuntime2__2(`./translations/${locale}.json`).then(({ default: data }) => {
        return {
          data: prefixPluginTranslations6(data, PLUGIN_ID),
          locale
        };
      }).catch(() => {
        return {
          data: {},
          locale
        };
      });
    }));
    return Promise.resolve(importedTrads);
  }
};

// node_modules/@strapi/strapi/dist/admin.mjs
var render = (mountNode, { plugins, ...restArgs }) => {
  return renderAdmin(mountNode, {
    ...restArgs,
    plugins: {
      "content-manager": index,
      "content-type-builder": index2,
      email: admin,
      upload: admin2,
      contentReleases: admin3,
      i18n: index3,
      reviewWorkflows: admin4,
      ...plugins
    }
  });
};

export {
  render
};
//# sourceMappingURL=chunk-Q4BWDPXE.js.map
