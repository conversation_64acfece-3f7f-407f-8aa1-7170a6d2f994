{"version": 3, "sources": ["../../../@strapi/i18n/node_modules/qs/lib/formats.js", "../../../@strapi/i18n/node_modules/qs/lib/utils.js", "../../../@strapi/i18n/node_modules/qs/lib/stringify.js", "../../../@strapi/i18n/node_modules/qs/lib/parse.js", "../../../@strapi/i18n/node_modules/qs/lib/index.js", "../../../@strapi/content-type-builder/admin/src/reducers.ts", "../../../@strapi/content-type-builder/admin/src/utils/formAPI.ts", "../../../@strapi/content-type-builder/admin/src/utils/prefixPluginTranslations.ts", "../../../@strapi/content-type-builder/admin/src/index.ts", "../../../@strapi/email/admin/src/utils/prefixPluginTranslations.ts", "../../../@strapi/email/admin/src/index.ts", "../../../@strapi/upload/admin/src/hooks/useModalQueryParams.ts", "../../../@strapi/upload/admin/src/utils/toSingularTypes.ts", "../../../@strapi/upload/admin/src/utils/getAllowedFiles.ts", "../../../@strapi/upload/admin/src/utils/moveElement.ts", "../../../@strapi/upload/admin/src/utils/getBreadcrumbDataCM.ts", "../../../@strapi/upload/admin/src/components/AssetDialog/BrowseStep/Filters.tsx", "../../../@strapi/upload/admin/src/components/AssetDialog/BrowseStep/PageSize.tsx", "../../../@strapi/upload/admin/src/components/AssetDialog/BrowseStep/PaginationFooter/Pagination.tsx", "../../../@strapi/upload/admin/src/components/AssetDialog/BrowseStep/PaginationFooter/PaginationFooter.tsx", "../../../@strapi/upload/admin/src/components/AssetDialog/BrowseStep/SearchAsset/SearchAsset.tsx", "../../../@strapi/upload/admin/src/components/AssetDialog/BrowseStep/utils/isSelectable.ts", "../../../@strapi/upload/admin/src/components/AssetDialog/BrowseStep/BrowseStep.tsx", "../../../@strapi/upload/admin/src/components/AssetDialog/DialogFooter.tsx", "../../../@strapi/upload/admin/src/components/AssetDialog/SelectedStep/SelectedStep.tsx", "../../../@strapi/upload/admin/src/components/AssetDialog/AssetDialog.tsx", "../../../@strapi/upload/admin/src/components/MediaLibraryDialog/MediaLibraryDialog.tsx", "../../../@strapi/upload/admin/src/components/MediaLibraryInput/Carousel/CarouselAsset.tsx", "../../../@strapi/upload/admin/src/components/MediaLibraryInput/Carousel/CarouselAssetActions.tsx", "../../../@strapi/upload/admin/src/components/MediaLibraryInput/Carousel/EmptyStateAsset.tsx", "../../../@strapi/upload/admin/src/components/MediaLibraryInput/Carousel/CarouselAssets.tsx", "../../../@strapi/upload/admin/src/components/MediaLibraryInput/MediaLibraryInput.tsx", "../../../@strapi/upload/admin/src/utils/prefixPluginTranslations.ts", "../../../@strapi/upload/admin/src/index.ts", "../../../@strapi/i18n/admin/src/components/CheckboxConfirmation.tsx", "../../../@strapi/i18n/admin/src/utils/fields.ts", "../../../@strapi/i18n/admin/src/utils/strings.ts", "../../../@strapi/i18n/admin/src/hooks/useI18n.ts", "../../../@strapi/i18n/admin/src/services/relations.ts", "../../../@strapi/i18n/admin/src/utils/clean.ts", "../../../@strapi/i18n/admin/src/components/BulkLocaleActionModal.tsx", "../../../@strapi/i18n/admin/src/components/CMHeaderActions.tsx", "../../../@strapi/i18n/admin/src/components/CMListViewModalsAdditionalInformation.tsx", "../../../@strapi/i18n/admin/src/components/LocalePicker.tsx", "../../../@strapi/i18n/admin/src/contentManagerHooks/editView.tsx", "../../../@strapi/i18n/admin/src/components/LocaleListCell.tsx", "../../../@strapi/i18n/admin/src/contentManagerHooks/listView.tsx", "../../../@strapi/i18n/admin/src/contentReleasesHooks/releaseDetailsView.ts", "../../../@strapi/i18n/admin/src/middlewares/extendCTBAttributeInitialData.ts", "../../../@strapi/i18n/admin/src/middlewares/extendCTBInitialData.ts", "../../../@strapi/i18n/admin/src/middlewares/rbac-middleware.ts", "../../../@strapi/i18n/admin/src/utils/prefixPluginTranslations.ts", "../../../@strapi/i18n/admin/src/utils/schemas.ts", "../../../@strapi/i18n/admin/src/index.ts", "../../../@strapi/content-releases/admin/src/components/ReleaseActionModal.tsx", "../../../@strapi/content-releases/admin/src/components/ReleaseAction.tsx", "../../../@strapi/content-releases/admin/src/components/ReleaseListCell.tsx", "../../../@strapi/content-releases/admin/src/components/ReleasesPanel.tsx", "../../../@strapi/content-releases/admin/src/utils/prefixPluginTranslations.ts", "../../../@strapi/content-releases/admin/src/index.ts", "../../../@strapi/review-workflows/admin/src/utils/users.ts", "../../../@strapi/review-workflows/admin/src/routes/content-manager/model/id/components/constants.ts", "../../../@strapi/review-workflows/admin/src/routes/content-manager/model/id/components/AssigneeSelect.tsx", "../../../@strapi/review-workflows/admin/src/routes/content-manager/model/id/components/StageSelect.tsx", "../../../@strapi/review-workflows/admin/src/routes/content-manager/model/id/components/Header.tsx", "../../../@strapi/review-workflows/admin/src/routes/content-manager/model/id/components/Panel.tsx", "../../../@strapi/review-workflows/admin/src/routes/content-manager/model/components/TableColumns.tsx", "../../../@strapi/review-workflows/admin/src/routes/content-manager/model/constants.tsx", "../../../@strapi/review-workflows/admin/src/utils/cm-hooks.ts", "../../../@strapi/review-workflows/admin/src/utils/translations.ts", "../../../@strapi/review-workflows/admin/src/index.ts", "../../../@strapi/strapi/src/admin.ts"], "sourcesContent": ["'use strict';\n\nvar replace = String.prototype.replace;\nvar percentTwenties = /%20/g;\n\nvar Format = {\n    RFC1738: 'RFC1738',\n    RFC3986: 'RFC3986'\n};\n\nmodule.exports = {\n    'default': Format.RFC3986,\n    formatters: {\n        RFC1738: function (value) {\n            return replace.call(value, percentTwenties, '+');\n        },\n        RFC3986: function (value) {\n            return String(value);\n        }\n    },\n    RFC1738: Format.RFC1738,\n    RFC3986: Format.RFC3986\n};\n", "'use strict';\n\nvar formats = require('./formats');\n\nvar has = Object.prototype.hasOwnProperty;\nvar isArray = Array.isArray;\n\nvar hexTable = (function () {\n    var array = [];\n    for (var i = 0; i < 256; ++i) {\n        array.push('%' + ((i < 16 ? '0' : '') + i.toString(16)).toUpperCase());\n    }\n\n    return array;\n}());\n\nvar compactQueue = function compactQueue(queue) {\n    while (queue.length > 1) {\n        var item = queue.pop();\n        var obj = item.obj[item.prop];\n\n        if (isArray(obj)) {\n            var compacted = [];\n\n            for (var j = 0; j < obj.length; ++j) {\n                if (typeof obj[j] !== 'undefined') {\n                    compacted.push(obj[j]);\n                }\n            }\n\n            item.obj[item.prop] = compacted;\n        }\n    }\n};\n\nvar arrayToObject = function arrayToObject(source, options) {\n    var obj = options && options.plainObjects ? Object.create(null) : {};\n    for (var i = 0; i < source.length; ++i) {\n        if (typeof source[i] !== 'undefined') {\n            obj[i] = source[i];\n        }\n    }\n\n    return obj;\n};\n\nvar merge = function merge(target, source, options) {\n    /* eslint no-param-reassign: 0 */\n    if (!source) {\n        return target;\n    }\n\n    if (typeof source !== 'object') {\n        if (isArray(target)) {\n            target.push(source);\n        } else if (target && typeof target === 'object') {\n            if ((options && (options.plainObjects || options.allowPrototypes)) || !has.call(Object.prototype, source)) {\n                target[source] = true;\n            }\n        } else {\n            return [target, source];\n        }\n\n        return target;\n    }\n\n    if (!target || typeof target !== 'object') {\n        return [target].concat(source);\n    }\n\n    var mergeTarget = target;\n    if (isArray(target) && !isArray(source)) {\n        mergeTarget = arrayToObject(target, options);\n    }\n\n    if (isArray(target) && isArray(source)) {\n        source.forEach(function (item, i) {\n            if (has.call(target, i)) {\n                var targetItem = target[i];\n                if (targetItem && typeof targetItem === 'object' && item && typeof item === 'object') {\n                    target[i] = merge(targetItem, item, options);\n                } else {\n                    target.push(item);\n                }\n            } else {\n                target[i] = item;\n            }\n        });\n        return target;\n    }\n\n    return Object.keys(source).reduce(function (acc, key) {\n        var value = source[key];\n\n        if (has.call(acc, key)) {\n            acc[key] = merge(acc[key], value, options);\n        } else {\n            acc[key] = value;\n        }\n        return acc;\n    }, mergeTarget);\n};\n\nvar assign = function assignSingleSource(target, source) {\n    return Object.keys(source).reduce(function (acc, key) {\n        acc[key] = source[key];\n        return acc;\n    }, target);\n};\n\nvar decode = function (str, decoder, charset) {\n    var strWithoutPlus = str.replace(/\\+/g, ' ');\n    if (charset === 'iso-8859-1') {\n        // unescape never throws, no try...catch needed:\n        return strWithoutPlus.replace(/%[0-9a-f]{2}/gi, unescape);\n    }\n    // utf-8\n    try {\n        return decodeURIComponent(strWithoutPlus);\n    } catch (e) {\n        return strWithoutPlus;\n    }\n};\n\nvar encode = function encode(str, defaultEncoder, charset, kind, format) {\n    // This code was originally written by Brian White (mscdex) for the io.js core querystring library.\n    // It has been adapted here for stricter adherence to RFC 3986\n    if (str.length === 0) {\n        return str;\n    }\n\n    var string = str;\n    if (typeof str === 'symbol') {\n        string = Symbol.prototype.toString.call(str);\n    } else if (typeof str !== 'string') {\n        string = String(str);\n    }\n\n    if (charset === 'iso-8859-1') {\n        return escape(string).replace(/%u[0-9a-f]{4}/gi, function ($0) {\n            return '%26%23' + parseInt($0.slice(2), 16) + '%3B';\n        });\n    }\n\n    var out = '';\n    for (var i = 0; i < string.length; ++i) {\n        var c = string.charCodeAt(i);\n\n        if (\n            c === 0x2D // -\n            || c === 0x2E // .\n            || c === 0x5F // _\n            || c === 0x7E // ~\n            || (c >= 0x30 && c <= 0x39) // 0-9\n            || (c >= 0x41 && c <= 0x5A) // a-z\n            || (c >= 0x61 && c <= 0x7A) // A-Z\n            || (format === formats.RFC1738 && (c === 0x28 || c === 0x29)) // ( )\n        ) {\n            out += string.charAt(i);\n            continue;\n        }\n\n        if (c < 0x80) {\n            out = out + hexTable[c];\n            continue;\n        }\n\n        if (c < 0x800) {\n            out = out + (hexTable[0xC0 | (c >> 6)] + hexTable[0x80 | (c & 0x3F)]);\n            continue;\n        }\n\n        if (c < 0xD800 || c >= 0xE000) {\n            out = out + (hexTable[0xE0 | (c >> 12)] + hexTable[0x80 | ((c >> 6) & 0x3F)] + hexTable[0x80 | (c & 0x3F)]);\n            continue;\n        }\n\n        i += 1;\n        c = 0x10000 + (((c & 0x3FF) << 10) | (string.charCodeAt(i) & 0x3FF));\n        /* eslint operator-linebreak: [2, \"before\"] */\n        out += hexTable[0xF0 | (c >> 18)]\n            + hexTable[0x80 | ((c >> 12) & 0x3F)]\n            + hexTable[0x80 | ((c >> 6) & 0x3F)]\n            + hexTable[0x80 | (c & 0x3F)];\n    }\n\n    return out;\n};\n\nvar compact = function compact(value) {\n    var queue = [{ obj: { o: value }, prop: 'o' }];\n    var refs = [];\n\n    for (var i = 0; i < queue.length; ++i) {\n        var item = queue[i];\n        var obj = item.obj[item.prop];\n\n        var keys = Object.keys(obj);\n        for (var j = 0; j < keys.length; ++j) {\n            var key = keys[j];\n            var val = obj[key];\n            if (typeof val === 'object' && val !== null && refs.indexOf(val) === -1) {\n                queue.push({ obj: obj, prop: key });\n                refs.push(val);\n            }\n        }\n    }\n\n    compactQueue(queue);\n\n    return value;\n};\n\nvar isRegExp = function isRegExp(obj) {\n    return Object.prototype.toString.call(obj) === '[object RegExp]';\n};\n\nvar isBuffer = function isBuffer(obj) {\n    if (!obj || typeof obj !== 'object') {\n        return false;\n    }\n\n    return !!(obj.constructor && obj.constructor.isBuffer && obj.constructor.isBuffer(obj));\n};\n\nvar combine = function combine(a, b) {\n    return [].concat(a, b);\n};\n\nvar maybeMap = function maybeMap(val, fn) {\n    if (isArray(val)) {\n        var mapped = [];\n        for (var i = 0; i < val.length; i += 1) {\n            mapped.push(fn(val[i]));\n        }\n        return mapped;\n    }\n    return fn(val);\n};\n\nmodule.exports = {\n    arrayToObject: arrayToObject,\n    assign: assign,\n    combine: combine,\n    compact: compact,\n    decode: decode,\n    encode: encode,\n    isBuffer: isBuffer,\n    isRegExp: isRegExp,\n    maybeMap: maybeMap,\n    merge: merge\n};\n", "'use strict';\n\nvar getSideChannel = require('side-channel');\nvar utils = require('./utils');\nvar formats = require('./formats');\nvar has = Object.prototype.hasOwnProperty;\n\nvar arrayPrefixGenerators = {\n    brackets: function brackets(prefix) {\n        return prefix + '[]';\n    },\n    comma: 'comma',\n    indices: function indices(prefix, key) {\n        return prefix + '[' + key + ']';\n    },\n    repeat: function repeat(prefix) {\n        return prefix;\n    }\n};\n\nvar isArray = Array.isArray;\nvar push = Array.prototype.push;\nvar pushToArray = function (arr, valueOrArray) {\n    push.apply(arr, isArray(valueOrArray) ? valueOrArray : [valueOrArray]);\n};\n\nvar toISO = Date.prototype.toISOString;\n\nvar defaultFormat = formats['default'];\nvar defaults = {\n    addQueryPrefix: false,\n    allowDots: false,\n    charset: 'utf-8',\n    charsetSentinel: false,\n    delimiter: '&',\n    encode: true,\n    encoder: utils.encode,\n    encodeValuesOnly: false,\n    format: defaultFormat,\n    formatter: formats.formatters[defaultFormat],\n    // deprecated\n    indices: false,\n    serializeDate: function serializeDate(date) {\n        return toISO.call(date);\n    },\n    skipNulls: false,\n    strictNullHandling: false\n};\n\nvar isNonNullishPrimitive = function isNonNullishPrimitive(v) {\n    return typeof v === 'string'\n        || typeof v === 'number'\n        || typeof v === 'boolean'\n        || typeof v === 'symbol'\n        || typeof v === 'bigint';\n};\n\nvar sentinel = {};\n\nvar stringify = function stringify(\n    object,\n    prefix,\n    generateArrayPrefix,\n    commaRoundTrip,\n    strictNullHandling,\n    skipNulls,\n    encoder,\n    filter,\n    sort,\n    allowDots,\n    serializeDate,\n    format,\n    formatter,\n    encodeValuesOnly,\n    charset,\n    sideChannel\n) {\n    var obj = object;\n\n    var tmpSc = sideChannel;\n    var step = 0;\n    var findFlag = false;\n    while ((tmpSc = tmpSc.get(sentinel)) !== void undefined && !findFlag) {\n        // Where object last appeared in the ref tree\n        var pos = tmpSc.get(object);\n        step += 1;\n        if (typeof pos !== 'undefined') {\n            if (pos === step) {\n                throw new RangeError('Cyclic object value');\n            } else {\n                findFlag = true; // Break while\n            }\n        }\n        if (typeof tmpSc.get(sentinel) === 'undefined') {\n            step = 0;\n        }\n    }\n\n    if (typeof filter === 'function') {\n        obj = filter(prefix, obj);\n    } else if (obj instanceof Date) {\n        obj = serializeDate(obj);\n    } else if (generateArrayPrefix === 'comma' && isArray(obj)) {\n        obj = utils.maybeMap(obj, function (value) {\n            if (value instanceof Date) {\n                return serializeDate(value);\n            }\n            return value;\n        });\n    }\n\n    if (obj === null) {\n        if (strictNullHandling) {\n            return encoder && !encodeValuesOnly ? encoder(prefix, defaults.encoder, charset, 'key', format) : prefix;\n        }\n\n        obj = '';\n    }\n\n    if (isNonNullishPrimitive(obj) || utils.isBuffer(obj)) {\n        if (encoder) {\n            var keyValue = encodeValuesOnly ? prefix : encoder(prefix, defaults.encoder, charset, 'key', format);\n            return [formatter(keyValue) + '=' + formatter(encoder(obj, defaults.encoder, charset, 'value', format))];\n        }\n        return [formatter(prefix) + '=' + formatter(String(obj))];\n    }\n\n    var values = [];\n\n    if (typeof obj === 'undefined') {\n        return values;\n    }\n\n    var objKeys;\n    if (generateArrayPrefix === 'comma' && isArray(obj)) {\n        // we need to join elements in\n        if (encodeValuesOnly && encoder) {\n            obj = utils.maybeMap(obj, encoder);\n        }\n        objKeys = [{ value: obj.length > 0 ? obj.join(',') || null : void undefined }];\n    } else if (isArray(filter)) {\n        objKeys = filter;\n    } else {\n        var keys = Object.keys(obj);\n        objKeys = sort ? keys.sort(sort) : keys;\n    }\n\n    var adjustedPrefix = commaRoundTrip && isArray(obj) && obj.length === 1 ? prefix + '[]' : prefix;\n\n    for (var j = 0; j < objKeys.length; ++j) {\n        var key = objKeys[j];\n        var value = typeof key === 'object' && typeof key.value !== 'undefined' ? key.value : obj[key];\n\n        if (skipNulls && value === null) {\n            continue;\n        }\n\n        var keyPrefix = isArray(obj)\n            ? typeof generateArrayPrefix === 'function' ? generateArrayPrefix(adjustedPrefix, key) : adjustedPrefix\n            : adjustedPrefix + (allowDots ? '.' + key : '[' + key + ']');\n\n        sideChannel.set(object, step);\n        var valueSideChannel = getSideChannel();\n        valueSideChannel.set(sentinel, sideChannel);\n        pushToArray(values, stringify(\n            value,\n            keyPrefix,\n            generateArrayPrefix,\n            commaRoundTrip,\n            strictNullHandling,\n            skipNulls,\n            generateArrayPrefix === 'comma' && encodeValuesOnly && isArray(obj) ? null : encoder,\n            filter,\n            sort,\n            allowDots,\n            serializeDate,\n            format,\n            formatter,\n            encodeValuesOnly,\n            charset,\n            valueSideChannel\n        ));\n    }\n\n    return values;\n};\n\nvar normalizeStringifyOptions = function normalizeStringifyOptions(opts) {\n    if (!opts) {\n        return defaults;\n    }\n\n    if (opts.encoder !== null && typeof opts.encoder !== 'undefined' && typeof opts.encoder !== 'function') {\n        throw new TypeError('Encoder has to be a function.');\n    }\n\n    var charset = opts.charset || defaults.charset;\n    if (typeof opts.charset !== 'undefined' && opts.charset !== 'utf-8' && opts.charset !== 'iso-8859-1') {\n        throw new TypeError('The charset option must be either utf-8, iso-8859-1, or undefined');\n    }\n\n    var format = formats['default'];\n    if (typeof opts.format !== 'undefined') {\n        if (!has.call(formats.formatters, opts.format)) {\n            throw new TypeError('Unknown format option provided.');\n        }\n        format = opts.format;\n    }\n    var formatter = formats.formatters[format];\n\n    var filter = defaults.filter;\n    if (typeof opts.filter === 'function' || isArray(opts.filter)) {\n        filter = opts.filter;\n    }\n\n    return {\n        addQueryPrefix: typeof opts.addQueryPrefix === 'boolean' ? opts.addQueryPrefix : defaults.addQueryPrefix,\n        allowDots: typeof opts.allowDots === 'undefined' ? defaults.allowDots : !!opts.allowDots,\n        charset: charset,\n        charsetSentinel: typeof opts.charsetSentinel === 'boolean' ? opts.charsetSentinel : defaults.charsetSentinel,\n        delimiter: typeof opts.delimiter === 'undefined' ? defaults.delimiter : opts.delimiter,\n        encode: typeof opts.encode === 'boolean' ? opts.encode : defaults.encode,\n        encoder: typeof opts.encoder === 'function' ? opts.encoder : defaults.encoder,\n        encodeValuesOnly: typeof opts.encodeValuesOnly === 'boolean' ? opts.encodeValuesOnly : defaults.encodeValuesOnly,\n        filter: filter,\n        format: format,\n        formatter: formatter,\n        serializeDate: typeof opts.serializeDate === 'function' ? opts.serializeDate : defaults.serializeDate,\n        skipNulls: typeof opts.skipNulls === 'boolean' ? opts.skipNulls : defaults.skipNulls,\n        sort: typeof opts.sort === 'function' ? opts.sort : null,\n        strictNullHandling: typeof opts.strictNullHandling === 'boolean' ? opts.strictNullHandling : defaults.strictNullHandling\n    };\n};\n\nmodule.exports = function (object, opts) {\n    var obj = object;\n    var options = normalizeStringifyOptions(opts);\n\n    var objKeys;\n    var filter;\n\n    if (typeof options.filter === 'function') {\n        filter = options.filter;\n        obj = filter('', obj);\n    } else if (isArray(options.filter)) {\n        filter = options.filter;\n        objKeys = filter;\n    }\n\n    var keys = [];\n\n    if (typeof obj !== 'object' || obj === null) {\n        return '';\n    }\n\n    var arrayFormat;\n    if (opts && opts.arrayFormat in arrayPrefixGenerators) {\n        arrayFormat = opts.arrayFormat;\n    } else if (opts && 'indices' in opts) {\n        arrayFormat = opts.indices ? 'indices' : 'repeat';\n    } else {\n        arrayFormat = 'indices';\n    }\n\n    var generateArrayPrefix = arrayPrefixGenerators[arrayFormat];\n    if (opts && 'commaRoundTrip' in opts && typeof opts.commaRoundTrip !== 'boolean') {\n        throw new TypeError('`commaRoundTrip` must be a boolean, or absent');\n    }\n    var commaRoundTrip = generateArrayPrefix === 'comma' && opts && opts.commaRoundTrip;\n\n    if (!objKeys) {\n        objKeys = Object.keys(obj);\n    }\n\n    if (options.sort) {\n        objKeys.sort(options.sort);\n    }\n\n    var sideChannel = getSideChannel();\n    for (var i = 0; i < objKeys.length; ++i) {\n        var key = objKeys[i];\n\n        if (options.skipNulls && obj[key] === null) {\n            continue;\n        }\n        pushToArray(keys, stringify(\n            obj[key],\n            key,\n            generateArrayPrefix,\n            commaRoundTrip,\n            options.strictNullHandling,\n            options.skipNulls,\n            options.encode ? options.encoder : null,\n            options.filter,\n            options.sort,\n            options.allowDots,\n            options.serializeDate,\n            options.format,\n            options.formatter,\n            options.encodeValuesOnly,\n            options.charset,\n            sideChannel\n        ));\n    }\n\n    var joined = keys.join(options.delimiter);\n    var prefix = options.addQueryPrefix === true ? '?' : '';\n\n    if (options.charsetSentinel) {\n        if (options.charset === 'iso-8859-1') {\n            // encodeURIComponent('&#10003;'), the \"numeric entity\" representation of a checkmark\n            prefix += 'utf8=%26%2310003%3B&';\n        } else {\n            // encodeURIComponent('✓')\n            prefix += 'utf8=%E2%9C%93&';\n        }\n    }\n\n    return joined.length > 0 ? prefix + joined : '';\n};\n", "'use strict';\n\nvar utils = require('./utils');\n\nvar has = Object.prototype.hasOwnProperty;\nvar isArray = Array.isArray;\n\nvar defaults = {\n    allowDots: false,\n    allowPrototypes: false,\n    allowSparse: false,\n    arrayLimit: 20,\n    charset: 'utf-8',\n    charsetSentinel: false,\n    comma: false,\n    decoder: utils.decode,\n    delimiter: '&',\n    depth: 5,\n    ignoreQueryPrefix: false,\n    interpretNumericEntities: false,\n    parameterLimit: 1000,\n    parseArrays: true,\n    plainObjects: false,\n    strictNullHandling: false\n};\n\nvar interpretNumericEntities = function (str) {\n    return str.replace(/&#(\\d+);/g, function ($0, numberStr) {\n        return String.fromCharCode(parseInt(numberStr, 10));\n    });\n};\n\nvar parseArrayValue = function (val, options) {\n    if (val && typeof val === 'string' && options.comma && val.indexOf(',') > -1) {\n        return val.split(',');\n    }\n\n    return val;\n};\n\n// This is what browsers will submit when the ✓ character occurs in an\n// application/x-www-form-urlencoded body and the encoding of the page containing\n// the form is iso-8859-1, or when the submitted form has an accept-charset\n// attribute of iso-8859-1. Presumably also with other charsets that do not contain\n// the ✓ character, such as us-ascii.\nvar isoSentinel = 'utf8=%26%2310003%3B'; // encodeURIComponent('&#10003;')\n\n// These are the percent-encoded utf-8 octets representing a checkmark, indicating that the request actually is utf-8 encoded.\nvar charsetSentinel = 'utf8=%E2%9C%93'; // encodeURIComponent('✓')\n\nvar parseValues = function parseQueryStringValues(str, options) {\n    var obj = {};\n    var cleanStr = options.ignoreQueryPrefix ? str.replace(/^\\?/, '') : str;\n    var limit = options.parameterLimit === Infinity ? undefined : options.parameterLimit;\n    var parts = cleanStr.split(options.delimiter, limit);\n    var skipIndex = -1; // Keep track of where the utf8 sentinel was found\n    var i;\n\n    var charset = options.charset;\n    if (options.charsetSentinel) {\n        for (i = 0; i < parts.length; ++i) {\n            if (parts[i].indexOf('utf8=') === 0) {\n                if (parts[i] === charsetSentinel) {\n                    charset = 'utf-8';\n                } else if (parts[i] === isoSentinel) {\n                    charset = 'iso-8859-1';\n                }\n                skipIndex = i;\n                i = parts.length; // The eslint settings do not allow break;\n            }\n        }\n    }\n\n    for (i = 0; i < parts.length; ++i) {\n        if (i === skipIndex) {\n            continue;\n        }\n        var part = parts[i];\n\n        var bracketEqualsPos = part.indexOf(']=');\n        var pos = bracketEqualsPos === -1 ? part.indexOf('=') : bracketEqualsPos + 1;\n\n        var key, val;\n        if (pos === -1) {\n            key = options.decoder(part, defaults.decoder, charset, 'key');\n            val = options.strictNullHandling ? null : '';\n        } else {\n            key = options.decoder(part.slice(0, pos), defaults.decoder, charset, 'key');\n            val = utils.maybeMap(\n                parseArrayValue(part.slice(pos + 1), options),\n                function (encodedVal) {\n                    return options.decoder(encodedVal, defaults.decoder, charset, 'value');\n                }\n            );\n        }\n\n        if (val && options.interpretNumericEntities && charset === 'iso-8859-1') {\n            val = interpretNumericEntities(val);\n        }\n\n        if (part.indexOf('[]=') > -1) {\n            val = isArray(val) ? [val] : val;\n        }\n\n        if (has.call(obj, key)) {\n            obj[key] = utils.combine(obj[key], val);\n        } else {\n            obj[key] = val;\n        }\n    }\n\n    return obj;\n};\n\nvar parseObject = function (chain, val, options, valuesParsed) {\n    var leaf = valuesParsed ? val : parseArrayValue(val, options);\n\n    for (var i = chain.length - 1; i >= 0; --i) {\n        var obj;\n        var root = chain[i];\n\n        if (root === '[]' && options.parseArrays) {\n            obj = [].concat(leaf);\n        } else {\n            obj = options.plainObjects ? Object.create(null) : {};\n            var cleanRoot = root.charAt(0) === '[' && root.charAt(root.length - 1) === ']' ? root.slice(1, -1) : root;\n            var index = parseInt(cleanRoot, 10);\n            if (!options.parseArrays && cleanRoot === '') {\n                obj = { 0: leaf };\n            } else if (\n                !isNaN(index)\n                && root !== cleanRoot\n                && String(index) === cleanRoot\n                && index >= 0\n                && (options.parseArrays && index <= options.arrayLimit)\n            ) {\n                obj = [];\n                obj[index] = leaf;\n            } else if (cleanRoot !== '__proto__') {\n                obj[cleanRoot] = leaf;\n            }\n        }\n\n        leaf = obj;\n    }\n\n    return leaf;\n};\n\nvar parseKeys = function parseQueryStringKeys(givenKey, val, options, valuesParsed) {\n    if (!givenKey) {\n        return;\n    }\n\n    // Transform dot notation to bracket notation\n    var key = options.allowDots ? givenKey.replace(/\\.([^.[]+)/g, '[$1]') : givenKey;\n\n    // The regex chunks\n\n    var brackets = /(\\[[^[\\]]*])/;\n    var child = /(\\[[^[\\]]*])/g;\n\n    // Get the parent\n\n    var segment = options.depth > 0 && brackets.exec(key);\n    var parent = segment ? key.slice(0, segment.index) : key;\n\n    // Stash the parent if it exists\n\n    var keys = [];\n    if (parent) {\n        // If we aren't using plain objects, optionally prefix keys that would overwrite object prototype properties\n        if (!options.plainObjects && has.call(Object.prototype, parent)) {\n            if (!options.allowPrototypes) {\n                return;\n            }\n        }\n\n        keys.push(parent);\n    }\n\n    // Loop through children appending to the array until we hit depth\n\n    var i = 0;\n    while (options.depth > 0 && (segment = child.exec(key)) !== null && i < options.depth) {\n        i += 1;\n        if (!options.plainObjects && has.call(Object.prototype, segment[1].slice(1, -1))) {\n            if (!options.allowPrototypes) {\n                return;\n            }\n        }\n        keys.push(segment[1]);\n    }\n\n    // If there's a remainder, just add whatever is left\n\n    if (segment) {\n        keys.push('[' + key.slice(segment.index) + ']');\n    }\n\n    return parseObject(keys, val, options, valuesParsed);\n};\n\nvar normalizeParseOptions = function normalizeParseOptions(opts) {\n    if (!opts) {\n        return defaults;\n    }\n\n    if (opts.decoder !== null && opts.decoder !== undefined && typeof opts.decoder !== 'function') {\n        throw new TypeError('Decoder has to be a function.');\n    }\n\n    if (typeof opts.charset !== 'undefined' && opts.charset !== 'utf-8' && opts.charset !== 'iso-8859-1') {\n        throw new TypeError('The charset option must be either utf-8, iso-8859-1, or undefined');\n    }\n    var charset = typeof opts.charset === 'undefined' ? defaults.charset : opts.charset;\n\n    return {\n        allowDots: typeof opts.allowDots === 'undefined' ? defaults.allowDots : !!opts.allowDots,\n        allowPrototypes: typeof opts.allowPrototypes === 'boolean' ? opts.allowPrototypes : defaults.allowPrototypes,\n        allowSparse: typeof opts.allowSparse === 'boolean' ? opts.allowSparse : defaults.allowSparse,\n        arrayLimit: typeof opts.arrayLimit === 'number' ? opts.arrayLimit : defaults.arrayLimit,\n        charset: charset,\n        charsetSentinel: typeof opts.charsetSentinel === 'boolean' ? opts.charsetSentinel : defaults.charsetSentinel,\n        comma: typeof opts.comma === 'boolean' ? opts.comma : defaults.comma,\n        decoder: typeof opts.decoder === 'function' ? opts.decoder : defaults.decoder,\n        delimiter: typeof opts.delimiter === 'string' || utils.isRegExp(opts.delimiter) ? opts.delimiter : defaults.delimiter,\n        // eslint-disable-next-line no-implicit-coercion, no-extra-parens\n        depth: (typeof opts.depth === 'number' || opts.depth === false) ? +opts.depth : defaults.depth,\n        ignoreQueryPrefix: opts.ignoreQueryPrefix === true,\n        interpretNumericEntities: typeof opts.interpretNumericEntities === 'boolean' ? opts.interpretNumericEntities : defaults.interpretNumericEntities,\n        parameterLimit: typeof opts.parameterLimit === 'number' ? opts.parameterLimit : defaults.parameterLimit,\n        parseArrays: opts.parseArrays !== false,\n        plainObjects: typeof opts.plainObjects === 'boolean' ? opts.plainObjects : defaults.plainObjects,\n        strictNullHandling: typeof opts.strictNullHandling === 'boolean' ? opts.strictNullHandling : defaults.strictNullHandling\n    };\n};\n\nmodule.exports = function (str, opts) {\n    var options = normalizeParseOptions(opts);\n\n    if (str === '' || str === null || typeof str === 'undefined') {\n        return options.plainObjects ? Object.create(null) : {};\n    }\n\n    var tempObj = typeof str === 'string' ? parseValues(str, options) : str;\n    var obj = options.plainObjects ? Object.create(null) : {};\n\n    // Iterate over the keys and setup the new object\n\n    var keys = Object.keys(tempObj);\n    for (var i = 0; i < keys.length; ++i) {\n        var key = keys[i];\n        var newObj = parseKeys(key, tempObj[key], options, typeof str === 'string');\n        obj = utils.merge(obj, newObj, options);\n    }\n\n    if (options.allowSparse === true) {\n        return obj;\n    }\n\n    return utils.compact(obj);\n};\n", "'use strict';\n\nvar stringify = require('./stringify');\nvar parse = require('./parse');\nvar formats = require('./formats');\n\nmodule.exports = {\n    formats: formats,\n    parse: parse,\n    stringify: stringify\n};\n", "import { reducer as dataManagerProviderReducer } from './components/DataManager/reducer';\nimport { reducer as formModalReducer } from './components/FormModal/reducer';\n\nexport const reducers = {\n  [`content-type-builder_formModal`]: formModalReducer,\n  [`content-type-builder_dataManagerProvider`]: dataManagerProviderReducer,\n};\n", "import cloneDeep from 'lodash/cloneDeep';\nimport get from 'lodash/get';\nimport * as yup from 'yup';\n\nexport interface FormAPI {\n  components: {\n    inputs: Record<string, any>;\n    add: ({ id, component }: { id: string; component: any }) => void;\n  };\n  types: {\n    attribute: {\n      [key: string]: {\n        validators: any[];\n        form: {\n          advanced: any[];\n          base: any[];\n        };\n      };\n    };\n    contentType: {\n      validators: any[];\n      form: {\n        advanced: any[];\n        base: any[];\n      };\n    };\n    component: {\n      validators: any[];\n      form: {\n        advanced: any[];\n        base: any[];\n      };\n    };\n  };\n  contentTypeSchemaMutations: any[];\n  addContentTypeSchemaMutation: (cb: any) => void;\n  extendContentType: (data: any) => void;\n  extendFields: (fields: any[], data: any) => void;\n  getAdvancedForm: (target: any, props?: any) => any[];\n  makeCustomFieldValidator: (attributeShape: any, validator: any, ...validatorArgs: any) => any;\n  makeValidator: (target: any, initShape: any, ...args: any) => any;\n  mutateContentTypeSchema: (\n    data: Record<string, unknown>,\n    initialData: Record<string, unknown>\n  ) => any;\n}\n\nexport const formsAPI: FormAPI = {\n  components: {\n    inputs: {},\n    add({ id, component }) {\n      if (!this.inputs[id]) {\n        this.inputs[id] = component;\n      }\n    },\n  },\n  types: {\n    attribute: {\n      // test: {\n      //   validators: [],\n      //   form: {\n      //     advanced: [\n      //       /* cb */\n      //     ],\n      //     base: [\n      //       /* cb */\n      //     ],\n      //   },\n      // },\n    },\n    contentType: {\n      validators: [],\n      form: {\n        advanced: [],\n        base: [],\n      },\n    },\n    component: {\n      validators: [],\n      form: {\n        advanced: [],\n        base: [],\n      },\n    },\n  },\n  contentTypeSchemaMutations: [],\n  addContentTypeSchemaMutation(cb) {\n    this.contentTypeSchemaMutations.push(cb);\n  },\n  extendContentType({ validator, form: { advanced, base } }) {\n    const { contentType } = this.types;\n\n    if (validator) {\n      contentType.validators.push(validator);\n    }\n    contentType.form.advanced.push(advanced);\n    contentType.form.base.push(base);\n  },\n  extendFields(fields, { validator, form: { advanced, base } }) {\n    const formType = this.types.attribute;\n\n    fields.forEach((field) => {\n      if (!formType[field]) {\n        formType[field] = {\n          validators: [],\n          form: {\n            advanced: [\n              /* cb */\n            ],\n            base: [\n              /* cb */\n            ],\n          },\n        };\n      }\n\n      if (validator) {\n        formType[field].validators.push(validator);\n      }\n      formType[field].form.advanced.push(advanced);\n      formType[field].form.base.push(base);\n    });\n  },\n\n  getAdvancedForm(target, props = null) {\n    const sectionsToAdd = get(this.types, [...target, 'form', 'advanced'], []).reduce(\n      (acc: any, current: any) => {\n        const sections = current(props);\n\n        return [...acc, ...sections];\n      },\n      []\n    );\n\n    return sectionsToAdd;\n  },\n\n  makeCustomFieldValidator(attributeShape, validator, ...validatorArgs) {\n    // When no validator, return the attribute shape\n    if (!validator) return attributeShape;\n\n    // Otherwise extend the shape with the provided validator\n    return attributeShape.shape({ options: yup.object().shape(validator(validatorArgs)) });\n  },\n\n  makeValidator(target, initShape, ...args) {\n    const validators = get(this.types, [...target, 'validators'], []);\n\n    const pluginOptionsShape = validators.reduce((acc: any, current: any) => {\n      const pluginOptionShape = current(args);\n\n      return { ...acc, ...pluginOptionShape };\n    }, {});\n\n    return initShape.shape({ pluginOptions: yup.object().shape(pluginOptionsShape) });\n  },\n  mutateContentTypeSchema(data: Record<string, unknown>, initialData: Record<string, unknown>) {\n    let enhancedData = cloneDeep(data);\n\n    const refData = cloneDeep(initialData);\n\n    this.contentTypeSchemaMutations.forEach((cb: any) => {\n      enhancedData = cb(enhancedData, refData);\n    });\n\n    return enhancedData;\n  },\n};\n", "type TradOptions = Record<string, string>;\n\nconst prefixPluginTranslations = (trad: TradOptions, pluginId: string): TradOptions => {\n  if (!pluginId) {\n    throw new TypeError(\"pluginId can't be empty\");\n  }\n  return Object.keys(trad).reduce((acc, current) => {\n    acc[`${pluginId}.${current}`] = trad[current];\n    return acc;\n  }, {} as TradOptions);\n};\n\nexport { prefixPluginTranslations };\n", "import { Layout } from '@strapi/icons';\n\nimport { PERMISSIONS } from './constants';\nimport { pluginId } from './pluginId';\nimport { reducers } from './reducers';\nimport { formsAPI } from './utils/formAPI';\nimport { prefixPluginTranslations } from './utils/prefixPluginTranslations';\n\nimport type { StrapiApp } from '@strapi/admin/strapi-admin';\n\n// eslint-disable-next-line import/no-default-export\nexport default {\n  register(app: StrapiApp) {\n    app.addReducers(reducers);\n    app.addMenuLink({\n      to: `plugins/${pluginId}`,\n      icon: Layout,\n      intlLabel: {\n        id: `${pluginId}.plugin.name`,\n        defaultMessage: 'Content-Type Builder',\n      },\n      permissions: PERMISSIONS.main,\n      Component: () => import('./pages/App'),\n      position: 5,\n    });\n\n    app.registerPlugin({\n      id: pluginId,\n      name: pluginId,\n      // Internal APIs exposed by the CTB for the other plugins to use\n      apis: {\n        forms: formsAPI,\n      },\n    });\n  },\n  bootstrap() {},\n  async registerTrads({ locales }: { locales: string[] }) {\n    const importedTrads = await Promise.all(\n      locales.map((locale) => {\n        return import(`./translations/${locale}.json`)\n          .then(({ default: data }) => {\n            return {\n              data: prefixPluginTranslations(data, pluginId),\n              locale,\n            };\n          })\n          .catch(() => {\n            return {\n              data: {},\n              locale,\n            };\n          });\n      })\n    );\n\n    return Promise.resolve(importedTrads);\n  },\n};\n\nexport * from './exports';\n", "type TradOptions = Record<string, string>;\n\nconst prefixPluginTranslations = (trad: TradOptions, pluginId: string): TradOptions => {\n  if (!pluginId) {\n    throw new TypeError(\"pluginId can't be empty\");\n  }\n  return Object.keys(trad).reduce((acc, current) => {\n    acc[`${pluginId}.${current}`] = trad[current];\n    return acc;\n  }, {} as TradOptions);\n};\n\nexport { prefixPluginTranslations };\n", "import { PERMISSIONS } from './constants';\nimport { prefixPluginTranslations } from './utils/prefixPluginTranslations';\n\nimport type { Plugin } from '@strapi/types';\n\nconst admin: Plugin.Config.AdminInput = {\n  // TODO typing app in strapi/types as every plugin needs it\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  register(app: any) {\n    // Create the email settings section\n    app.createSettingSection(\n      {\n        id: 'email',\n        intlLabel: { id: 'email.SettingsNav.section-label', defaultMessage: 'Email Plugin' },\n      },\n      [\n        {\n          intlLabel: {\n            id: 'email.Settings.email.plugin.title',\n            defaultMessage: 'Settings',\n          },\n          id: 'settings',\n          to: 'email',\n          Component: () =>\n            import('./pages/Settings').then((mod) => ({\n              default: mod.ProtectedSettingsPage,\n            })),\n          permissions: PERMISSIONS.settings,\n        },\n      ]\n    );\n    app.registerPlugin({\n      id: 'email',\n      name: 'email',\n    });\n  },\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  bootstrap() {},\n  async registerTrads({ locales }: { locales: string[] }) {\n    const importedTrads = await Promise.all(\n      locales.map((locale) => {\n        return import(`./translations/${locale}.json`)\n          .then(({ default: data }) => {\n            return {\n              data: prefixPluginTranslations(data, 'email'),\n              locale,\n            };\n          })\n          .catch(() => {\n            return {\n              data: {},\n              locale,\n            };\n          });\n      })\n    );\n\n    return Promise.resolve(importedTrads);\n  },\n};\n\n// eslint-disable-next-line import/no-default-export\nexport default admin;\n", "import * as React from 'react';\n\nimport { useTracking } from '@strapi/admin/strapi-admin';\nimport { stringify } from 'qs';\n\nimport { useConfig } from './useConfig';\n\nimport type { Query, FilterCondition } from '../../../shared/contracts/files';\n\nexport const useModalQueryParams = (initialState?: Partial<Query>) => {\n  const { trackUsage } = useTracking();\n  const {\n    config: { data: config },\n  } = useConfig();\n\n  const [queryObject, setQueryObject] = React.useState<Query>({\n    page: 1,\n    sort: 'updatedAt:DESC',\n    pageSize: 10,\n    filters: {\n      $and: [],\n    },\n    ...initialState,\n  });\n\n  React.useEffect(() => {\n    if (config && 'sort' in config && 'pageSize' in config) {\n      setQueryObject((prevQuery) => ({\n        ...prevQuery,\n        sort: config.sort,\n        pageSize: config.pageSize,\n      }));\n    }\n  }, [config]);\n\n  const handleChangeFilters = (nextFilters: FilterCondition<string>[]) => {\n    if (nextFilters) {\n      trackUsage('didFilterMediaLibraryElements', {\n        location: 'content-manager',\n        filter: Object.keys(nextFilters[nextFilters.length - 1])[0],\n      });\n      setQueryObject((prev) => ({ ...prev, page: 1, filters: { $and: nextFilters } }));\n    }\n  };\n\n  const handleChangePageSize = (pageSize: Query['pageSize']) => {\n    setQueryObject((prev) => ({\n      ...prev,\n      pageSize: typeof pageSize === 'string' ? parseInt(pageSize, 10) : pageSize,\n      page: 1,\n    }));\n  };\n\n  const handeChangePage = (page: Query['page']) => {\n    setQueryObject((prev) => ({ ...prev, page }));\n  };\n\n  const handleChangeSort = (sort: Query['sort']) => {\n    if (sort) {\n      trackUsage('didSortMediaLibraryElements', {\n        location: 'content-manager',\n        sort,\n      });\n      setQueryObject((prev) => ({ ...prev, sort }));\n    }\n  };\n\n  const handleChangeSearch = (_q: Query['_q'] | null) => {\n    if (_q) {\n      setQueryObject((prev) => ({ ...prev, _q, page: 1 }));\n    } else {\n      const newState: Query = { page: 1 };\n\n      Object.keys(queryObject).forEach((key) => {\n        if (!['page', '_q'].includes(key)) {\n          (newState as Record<string, string | number | undefined>)[key] = (\n            queryObject as Record<string, string | number | undefined>\n          )[key];\n        }\n      });\n\n      setQueryObject(newState);\n    }\n  };\n\n  const handleChangeFolder = (folder: Query['folder'], folderPath: Query['folderPath']) => {\n    setQueryObject((prev) => ({ ...prev, folder: folder ?? null, folderPath }));\n  };\n\n  return [\n    { queryObject, rawQuery: stringify(queryObject, { encode: false }) },\n    {\n      onChangeFilters: handleChangeFilters,\n      onChangeFolder: handleChangeFolder,\n      onChangePage: handeChangePage,\n      onChangePageSize: handleChangePageSize,\n      onChangeSort: handleChangeSort,\n      onChangeSearch: handleChangeSearch,\n    },\n  ];\n};\n", "export const toSingularTypes = (types?: string[]) => {\n  if (!types) {\n    return [];\n  }\n\n  return types.map((type) => type.substring(0, type.length - 1));\n};\n", "import { toSingularTypes } from './toSingularTypes';\n\nimport type { File } from '../../../shared/contracts/files';\n/**\n * Returns the files that can be added to the media field\n * @param {Object[]} pluralTypes Array of string (allowedTypes)\n * @param {Object[]} files Array of files\n * @returns Object[]\n */\n\nexport interface AllowedFiles extends File {\n  documentId: string;\n  isSelectable: boolean;\n  locale: string | null;\n  type: string;\n}\n\nexport const getAllowedFiles = (pluralTypes: string[] | null, files: AllowedFiles[]) => {\n  if (!pluralTypes) {\n    return files;\n  }\n\n  const singularTypes = toSingularTypes(pluralTypes);\n\n  const allowedFiles = files.filter((file) => {\n    const fileType = file?.mime?.split('/')[0];\n\n    if (!fileType) {\n      return false;\n    }\n\n    if (singularTypes.includes('file') && !['video', 'image', 'audio'].includes(fileType)) {\n      return true;\n    }\n\n    return singularTypes.includes(fileType);\n  });\n\n  return allowedFiles;\n};\n", "const move = <T = number>(array: T[], oldIndex: number, newIndex: number) => {\n  if (newIndex >= array.length) {\n    newIndex = array.length - 1;\n  }\n  array.splice(newIndex, 0, array.splice(oldIndex, 1)[0]);\n\n  return array;\n};\n\nexport const moveElement = <T = number>(array: T[], index: number, offset: number) => {\n  const newIndex = index + offset;\n\n  return move(array, index, newIndex);\n};\n", "import { getTrad } from './getTrad';\n\nimport type { Folder } from '../../../shared/contracts/folders';\nimport type { MessageDescriptor } from 'react-intl';\n\nexport interface BreadcrumbDataFolder extends Omit<Folder, 'children' | 'files' | 'parent'> {\n  parent?: BreadcrumbDataFolder;\n  children?: {\n    count: number;\n  };\n  files?: {\n    count: number;\n  };\n}\n\ninterface BreadcrumbItem {\n  id?: number | null;\n  label?: MessageDescriptor | string;\n  path?: string;\n}\n\ntype BreadcrumbData = BreadcrumbItem | [];\n\nexport const getBreadcrumbDataCM = (folder: BreadcrumbDataFolder | null) => {\n  const data: BreadcrumbData[] = [\n    {\n      id: null,\n      label: { id: getTrad('plugin.name'), defaultMessage: 'Media Library' },\n    },\n  ];\n\n  if (folder?.parent?.parent) {\n    data.push([]);\n  }\n\n  if (folder?.parent) {\n    data.push({\n      id: folder.parent.id,\n      label: folder.parent.name,\n      path: folder.parent.path,\n    });\n  }\n\n  if (folder) {\n    data.push({\n      id: folder.id,\n      label: folder.name,\n      path: folder.path,\n    });\n  }\n\n  return data;\n};\n", "import * as React from 'react';\n\nimport { Button, Popover } from '@strapi/design-system';\nimport { Filter } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\n\nimport { displayedFilters } from '../../../utils';\nimport { FilterList } from '../../FilterList/FilterList';\nimport { FilterPopover } from '../../FilterPopover/FilterPopover';\n\ntype NumberKeyedObject = Record<number, string>;\n\ntype StringFilter = {\n  [key: string]: string;\n};\n\ntype MimeFilter = {\n  [key: string]:\n    | string\n    | NumberKeyedObject\n    | Record<string, string | NumberKeyedObject>\n    | undefined;\n};\n\nexport type FilterStructure = {\n  [key: string]: MimeFilter | StringFilter | undefined;\n};\n\nexport type Filter = {\n  [key in 'mime' | 'createdAt' | 'updatedAt']?:\n    | {\n        [key in '$contains' | '$notContains' | '$eq' | '$not']?:\n          | string[]\n          | string\n          | { $contains: string[] };\n      }\n    | undefined;\n};\n\ninterface FiltersProps {\n  appliedFilters: FilterStructure[];\n  onChangeFilters: (filters: Filter[]) => void;\n}\n\nexport const Filters = ({ appliedFilters, onChangeFilters }: FiltersProps) => {\n  const [open, setOpen] = React.useState(false);\n  const { formatMessage } = useIntl();\n\n  return (\n    <Popover.Root open={open} onOpenChange={setOpen}>\n      <Popover.Trigger>\n        <Button variant=\"tertiary\" startIcon={<Filter />} size=\"S\">\n          {formatMessage({ id: 'app.utils.filters', defaultMessage: 'Filters' })}\n        </Button>\n      </Popover.Trigger>\n      <FilterPopover\n        onToggle={() => setOpen((prev) => !prev)}\n        displayedFilters={displayedFilters}\n        filters={appliedFilters}\n        onSubmit={onChangeFilters}\n      />\n\n      {appliedFilters && (\n        <FilterList\n          appliedFilters={appliedFilters}\n          filtersSchema={displayedFilters}\n          onRemoveFilter={onChangeFilters}\n        />\n      )}\n    </Popover.Root>\n  );\n};\n", "import { Box, Flex, SingleSelectOption, SingleSelect, Typography } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\n\ninterface PageSizeProps {\n  onChangePageSize: (value: number) => void;\n  pageSize: number;\n}\n\nexport const PageSize = ({ onChangePageSize, pageSize }: PageSizeProps) => {\n  const { formatMessage } = useIntl();\n\n  const handleChange = (value: string | number) => {\n    onChangePageSize(Number(value));\n  };\n\n  return (\n    <Flex>\n      <SingleSelect\n        size=\"S\"\n        aria-label={formatMessage({\n          id: 'components.PageFooter.select',\n          defaultMessage: 'Entries per page',\n        })}\n        onChange={handleChange}\n        value={pageSize.toString()}\n      >\n        <SingleSelectOption value=\"10\">10</SingleSelectOption>\n        <SingleSelectOption value=\"20\">20</SingleSelectOption>\n        <SingleSelectOption value=\"50\">50</SingleSelectOption>\n        <SingleSelectOption value=\"100\">100</SingleSelectOption>\n      </SingleSelect>\n      <Box paddingLeft={2}>\n        <Typography textColor=\"neutral600\" tag=\"label\" htmlFor=\"page-size\">\n          {formatMessage({\n            id: 'components.PageFooter.select',\n            defaultMessage: 'Entries per page',\n          })}\n        </Typography>\n      </Box>\n    </Flex>\n  );\n};\n", "import * as React from 'react';\n\nimport { Box, Flex } from '@strapi/design-system';\n\nconst PaginationContext = React.createContext({ activePage: 1, pageCount: 1 });\nexport const usePagination = () => React.useContext(PaginationContext);\n\ninterface PaginationProps {\n  activePage: number;\n  children: React.ReactNode;\n  label?: string;\n  pageCount: number;\n}\n\nexport const Pagination = ({\n  children,\n  activePage,\n  pageCount,\n  label = 'pagination',\n}: PaginationProps) => {\n  const paginationValue = React.useMemo(() => ({ activePage, pageCount }), [activePage, pageCount]);\n\n  return (\n    <PaginationContext.Provider value={paginationValue}>\n      <Box tag=\"nav\" aria-label={label}>\n        <Flex tag=\"ul\" gap={1}>\n          {children}\n        </Flex>\n      </Box>\n    </PaginationContext.Provider>\n  );\n};\n", "// TODO: find a better naming convention for the file that was an index file before\n/**\n * The component works as follows: this is a duplicate of the helper-plugin one but without the router\n * `1` , 2, 3, ... 10\n * 1, `2`, 3, ... 10\n * 1, 2, `3`, 4, ... 10\n * 1, 2, 3, `4`, 5, ... 10\n * 1, ..,4, `5`, 6, ... 10\n *\n * 1, ...., 8, 9, `10`\n * 1, ...., 8, `9`, 10\n * 1, ...., 7, `8`, 9, 10\n * 1, ... 6, `7`, 8, 9, 10\n */\nimport { Typography, VisuallyHidden } from '@strapi/design-system';\nimport { ChevronLeft, ChevronRight } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\nimport { styled, css } from 'styled-components';\n\nimport { Pagination, usePagination } from './Pagination';\n\nconst PaginationText = styled(Typography)`\n  line-height: revert;\n`;\n\nconst linkWrapperStyles = css<{ $active?: boolean }>`\n  padding: ${({ theme }) => theme.spaces[3]};\n  border-radius: ${({ theme }) => theme.borderRadius};\n  box-shadow: ${({ $active, theme }) => ($active ? theme.shadows.filterShadow : undefined)};\n  text-decoration: none;\n  display: flex;\n  position: relative;\n  outline: none;\n\n  &:after {\n    transition-property: all;\n    transition-duration: 0.2s;\n    border-radius: 8px;\n    content: '';\n    position: absolute;\n    top: -4px;\n    bottom: -4px;\n    left: -4px;\n    right: -4px;\n    border: 2px solid transparent;\n  }\n\n  &:focus-visible {\n    outline: none;\n\n    &:after {\n      border-radius: 8px;\n      content: '';\n      position: absolute;\n      top: -5px;\n      bottom: -5px;\n      left: -5px;\n      right: -5px;\n      border: 2px solid ${(props) => props.theme.colors.primary600};\n    }\n  }\n`;\n\nconst LinkWrapperButton = styled.button<{ $active?: boolean }>`\n  ${linkWrapperStyles}\n`;\n\nconst LinkWrapperDiv = styled.div<{ $active?: boolean }>`\n  ${linkWrapperStyles}\n`;\n\nLinkWrapperButton.defaultProps = { type: 'button' };\n\nconst PageLinkWrapper = styled(LinkWrapperButton)`\n  color: ${({ theme, $active }) => ($active ? theme.colors.primary700 : theme.colors.neutral800)};\n  background: ${({ theme, $active }) => ($active ? theme.colors.neutral0 : undefined)};\n\n  &:hover {\n    box-shadow: ${({ theme }) => theme.shadows.filterShadow};\n  }\n`;\n\nconst ActionLinkWrapper = styled(LinkWrapperButton)`\n  font-size: 1.1rem;\n  svg path {\n    fill: ${(p) => (p['aria-disabled'] ? p.theme.colors.neutral300 : p.theme.colors.neutral600)};\n  }\n\n  &:focus,\n  &:hover {\n    svg path {\n      fill: ${(p) => (p['aria-disabled'] ? p.theme.colors.neutral300 : p.theme.colors.neutral700)};\n    }\n  }\n\n  ${(p) =>\n    p['aria-disabled']\n      ? `\n  pointer-events: none;\n    `\n      : undefined}\n`;\n\nconst DotsWrapper = styled(LinkWrapperDiv)`\n  color: ${({ theme }) => theme.colors.neutral800};\n`;\n\ninterface PaginationLinkProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  children: React.ReactNode;\n}\n\ninterface PageLinkProps extends PaginationLinkProps {\n  number: number;\n}\n\nconst PreviousLink = ({ children, ...props }: PaginationLinkProps) => {\n  const { activePage } = usePagination();\n\n  const disabled = activePage === 1;\n\n  return (\n    <li>\n      <ActionLinkWrapper aria-disabled={disabled} tabIndex={disabled ? -1 : undefined} {...props}>\n        <VisuallyHidden>{children}</VisuallyHidden>\n        <ChevronLeft aria-hidden />\n      </ActionLinkWrapper>\n    </li>\n  );\n};\n\nconst NextLink = ({ children, ...props }: PaginationLinkProps) => {\n  const { activePage, pageCount } = usePagination();\n\n  const disabled = activePage === pageCount;\n\n  return (\n    <li>\n      <ActionLinkWrapper aria-disabled={disabled} tabIndex={disabled ? -1 : undefined} {...props}>\n        <VisuallyHidden>{children}</VisuallyHidden>\n        <ChevronRight aria-hidden />\n      </ActionLinkWrapper>\n    </li>\n  );\n};\n\nconst PageLink = ({ number, children, ...props }: PageLinkProps) => {\n  const { activePage } = usePagination();\n\n  const isActive = activePage === number;\n\n  return (\n    <li>\n      <PageLinkWrapper {...props} $active={isActive}>\n        <VisuallyHidden>{children}</VisuallyHidden>\n        <PaginationText aria-hidden variant=\"pi\" fontWeight={isActive ? 'bold' : ''}>\n          {number}\n        </PaginationText>\n      </PageLinkWrapper>\n    </li>\n  );\n};\n\ninterface DotsProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\nconst Dots = ({ children, ...props }: DotsProps) => (\n  <li>\n    <DotsWrapper {...props} as=\"div\">\n      <VisuallyHidden>{children}</VisuallyHidden>\n      <PaginationText aria-hidden small>\n        …\n      </PaginationText>\n    </DotsWrapper>\n  </li>\n);\n\ninterface PaginationFooterProps {\n  activePage: number;\n  onChangePage: (page: number) => void;\n  pagination: {\n    pageCount: number;\n  };\n}\n\nexport const PaginationFooter = ({\n  activePage,\n  onChangePage,\n  pagination: { pageCount },\n}: PaginationFooterProps) => {\n  const { formatMessage } = useIntl();\n\n  const previousActivePage = activePage - 1;\n  const nextActivePage = activePage + 1;\n\n  const firstLinks = [\n    <PageLink\n      key={1}\n      number={1}\n      onClick={() => {\n        onChangePage(1);\n      }}\n    >\n      {formatMessage(\n        { id: 'components.pagination.go-to', defaultMessage: 'Go to page {page}' },\n        { page: 1 }\n      )}\n    </PageLink>,\n  ];\n\n  if (pageCount <= 4) {\n    const links = Array.from({ length: pageCount })\n      .map((_, i) => i + 1)\n      .map((number) => {\n        return (\n          <PageLink key={number} number={number} onClick={() => onChangePage(number)}>\n            {formatMessage(\n              { id: 'components.pagination.go-to', defaultMessage: 'Go to page {page}' },\n              { page: number }\n            )}\n          </PageLink>\n        );\n      });\n\n    return (\n      <Pagination activePage={activePage} pageCount={pageCount}>\n        <PreviousLink onClick={() => onChangePage(previousActivePage)}>\n          {formatMessage({\n            id: 'components.pagination.go-to-previous',\n            defaultMessage: 'Go to previous page',\n          })}\n        </PreviousLink>\n        {links}\n        <NextLink onClick={() => onChangePage(nextActivePage)}>\n          {formatMessage({\n            id: 'components.pagination.go-to-next',\n            defaultMessage: 'Go to next page',\n          })}\n        </NextLink>\n      </Pagination>\n    );\n  }\n\n  let firstLinksToCreate: number[] = [];\n  const lastLinks: JSX.Element[] = [];\n  let lastLinksToCreate: number[] = [];\n  const middleLinks: JSX.Element[] = [];\n\n  if (pageCount > 1) {\n    lastLinks.push(\n      <PageLink key={pageCount} number={pageCount} onClick={() => onChangePage(pageCount)}>\n        {formatMessage(\n          { id: 'components.pagination.go-to', defaultMessage: 'Go to page {page}' },\n          { page: pageCount }\n        )}\n      </PageLink>\n    );\n  }\n\n  if (activePage === 1 && pageCount >= 3) {\n    firstLinksToCreate = [2];\n  }\n\n  if (activePage === 2 && pageCount >= 3) {\n    if (pageCount === 5) {\n      firstLinksToCreate = [2, 3, 4];\n    } else if (pageCount === 3) {\n      firstLinksToCreate = [2];\n    } else {\n      firstLinksToCreate = [2, 3];\n    }\n  }\n\n  if (activePage === 4 && pageCount >= 3) {\n    firstLinksToCreate = [2];\n  }\n\n  if (activePage === pageCount && pageCount >= 3) {\n    lastLinksToCreate = [pageCount - 1];\n  }\n\n  if (activePage === pageCount - 2 && pageCount > 3) {\n    lastLinksToCreate = [activePage + 1, activePage, activePage - 1];\n  }\n\n  if (activePage === pageCount - 3 && pageCount > 3 && activePage > 5) {\n    lastLinksToCreate = [activePage + 2, activePage + 1, activePage, activePage - 1];\n  }\n\n  if (activePage === pageCount - 1 && pageCount > 3) {\n    lastLinksToCreate = [activePage, activePage - 1];\n  }\n\n  lastLinksToCreate.forEach((number) => {\n    lastLinks.unshift(\n      <PageLink key={number} number={number} onClick={() => onChangePage(number)}>\n        Go to page {number}\n      </PageLink>\n    );\n  });\n\n  firstLinksToCreate.forEach((number) => {\n    firstLinks.push(\n      <PageLink key={number} number={number} onClick={() => onChangePage(number)}>\n        {formatMessage(\n          { id: 'components.pagination.go-to', defaultMessage: 'Go to page {page}' },\n          { page: number }\n        )}\n      </PageLink>\n    );\n  });\n\n  if (\n    ![1, 2].includes(activePage) &&\n    activePage <= pageCount - 3 &&\n    firstLinks.length + lastLinks.length < 6\n  ) {\n    const middleLinksToCreate = [activePage - 1, activePage, activePage + 1];\n\n    middleLinksToCreate.forEach((number) => {\n      middleLinks.push(\n        <PageLink key={number} number={number} onClick={() => onChangePage(number)}>\n          {formatMessage(\n            { id: 'components.pagination.go-to', defaultMessage: 'Go to page {page}' },\n            { page: number }\n          )}\n        </PageLink>\n      );\n    });\n  }\n\n  const shouldShowDotsAfterFirstLink =\n    pageCount > 5 || (pageCount === 5 && (activePage === 1 || activePage === 5));\n  const shouldShowMiddleDots = middleLinks.length > 2 && activePage > 4 && pageCount > 5;\n\n  const beforeDotsLinksLength = shouldShowMiddleDots\n    ? pageCount - activePage - 1\n    : pageCount - firstLinks.length - lastLinks.length;\n  const afterDotsLength = shouldShowMiddleDots\n    ? pageCount - firstLinks.length - lastLinks.length\n    : pageCount - activePage - 1;\n\n  return (\n    <Pagination activePage={activePage} pageCount={pageCount}>\n      <PreviousLink onClick={() => onChangePage(previousActivePage)}>\n        {formatMessage({\n          id: 'components.pagination.go-to-previous',\n          defaultMessage: 'Go to previous page',\n        })}\n      </PreviousLink>\n      {firstLinks}\n      {shouldShowMiddleDots && (\n        <Dots>\n          {formatMessage(\n            {\n              id: 'components.pagination.remaining-links',\n              defaultMessage: 'And {number} other links',\n            },\n            { number: beforeDotsLinksLength }\n          )}\n        </Dots>\n      )}\n      {middleLinks}\n      {shouldShowDotsAfterFirstLink && (\n        <Dots>\n          {formatMessage(\n            {\n              id: 'components.pagination.remaining-links',\n              defaultMessage: 'And {number} other links',\n            },\n            { number: afterDotsLength }\n          )}\n        </Dots>\n      )}\n      {lastLinks}\n      <NextLink onClick={() => onChangePage(nextActivePage)}>\n        {formatMessage({\n          id: 'components.pagination.go-to-next',\n          defaultMessage: 'Go to next page',\n        })}\n      </NextLink>\n    </Pagination>\n  );\n};\n", "// TODO: find a better naming convention for the file that was an index file before\nimport * as React from 'react';\n\nimport { useTracking } from '@strapi/admin/strapi-admin';\nimport { IconButton, Searchbar, SearchForm } from '@strapi/design-system';\nimport { Search } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\n\nimport { getTrad } from '../../../../utils';\n\nimport type { Query } from '../../../../../../shared/contracts/files';\n\ninterface SearchAssetProps {\n  onChangeSearch: (_q: Query['_q'] | null) => void;\n  queryValue?: Query['_q'] | null;\n}\n\nexport const SearchAsset = ({ onChangeSearch, queryValue = null }: SearchAssetProps) => {\n  const { formatMessage } = useIntl();\n  const { trackUsage } = useTracking();\n  const [isOpen, setIsOpen] = React.useState(!!queryValue);\n  const [value, setValue] = React.useState(queryValue || '');\n  const wrapperRef = React.useRef<HTMLDivElement>(null);\n\n  React.useLayoutEffect(() => {\n    if (isOpen) {\n      setTimeout(() => {\n        wrapperRef.current?.querySelector('input')?.focus();\n      }, 0);\n    }\n  }, [isOpen]);\n\n  const handleToggle = () => {\n    setIsOpen((prev) => !prev);\n  };\n\n  const handleClear = () => {\n    handleToggle();\n    onChangeSearch(null);\n  };\n\n  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {\n    e.preventDefault();\n    e.stopPropagation();\n\n    trackUsage('didSearchMediaLibraryElements', { location: 'content-manager' });\n    onChangeSearch(value);\n  };\n\n  if (isOpen) {\n    return (\n      <div ref={wrapperRef}>\n        <SearchForm onSubmit={handleSubmit}>\n          <Searchbar\n            name=\"search\"\n            onClear={handleClear}\n            onChange={(e) => setValue(e.target.value)}\n            clearLabel={formatMessage({\n              id: getTrad('search.clear.label'),\n              defaultMessage: 'Clear the search',\n            })}\n            aria-label=\"search\"\n            size=\"S\"\n            value={value}\n            placeholder={formatMessage({\n              id: getTrad('search.placeholder'),\n              defaultMessage: 'e.g: the first dog on the moon',\n            })}\n          >\n            {formatMessage({ id: getTrad('search.label'), defaultMessage: 'Search for an asset' })}\n          </Searchbar>\n        </SearchForm>\n      </div>\n    );\n  }\n\n  return (\n    <IconButton label=\"Search\" onClick={handleToggle}>\n      <Search />\n    </IconButton>\n  );\n};\n", "export const isSelectable = (allowedTypes: string[], mime = '') => {\n  if (!mime) return false;\n\n  const fileType = mime.split('/')[0];\n\n  return (\n    allowedTypes.includes(fileType) ||\n    (allowedTypes.includes('file') && !['video', 'image', 'audio'].includes(fileType))\n  );\n};\n", "// TODO: find a better naming convention for the file that was an index file before\nimport {\n  Checkbox,\n  Box,\n  Button,\n  Divider,\n  Flex,\n  IconButton,\n  Typography,\n  VisuallyHidden,\n  Grid,\n} from '@strapi/design-system';\nimport { GridFour as GridIcon, List, Pencil, Plus } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\nimport { styled } from 'styled-components';\n\nimport { localStorageKeys, viewOptions } from '../../../constants';\nimport { useFolder } from '../../../hooks/useFolder';\nimport { usePersistentState } from '../../../hooks/usePersistentState';\nimport {\n  getBreadcrumbDataCM,\n  toSingularTypes,\n  getTrad,\n  getAllowedFiles,\n  BreadcrumbDataFolder,\n  AllowedFiles,\n} from '../../../utils';\nimport { AssetGridList } from '../../AssetGridList/AssetGridList';\nimport { Breadcrumbs } from '../../Breadcrumbs/Breadcrumbs';\nimport { EmptyAssets } from '../../EmptyAssets/EmptyAssets';\nimport { FolderCard } from '../../FolderCard/FolderCard/FolderCard';\nimport { FolderCardBody } from '../../FolderCard/FolderCardBody/FolderCardBody';\nimport { FolderCardBodyAction } from '../../FolderCard/FolderCardBodyAction/FolderCardBodyAction';\nimport { FolderGridList } from '../../FolderGridList/FolderGridList';\nimport { SortPicker } from '../../SortPicker/SortPicker';\nimport { TableList, FolderRow, FileRow } from '../../TableList/TableList';\n\nimport { Filters, FilterStructure as ImportedFilterStructure } from './Filters';\nimport { PageSize } from './PageSize';\nimport { PaginationFooter } from './PaginationFooter/PaginationFooter';\nimport { SearchAsset } from './SearchAsset/SearchAsset';\nimport { isSelectable } from './utils/isSelectable';\n\nimport type { File, Query, FilterCondition } from '../../../../../shared/contracts/files';\nimport type { Folder } from '../../../../../shared/contracts/folders';\nimport type { AllowedTypes } from '../../AssetCard/AssetCard';\n\nconst TypographyMaxWidth = styled(Typography)`\n  max-width: 100%;\n`;\n\nconst ActionContainer = styled(Box)`\n  svg {\n    path {\n      fill: ${({ theme }) => theme.colors.neutral500};\n    }\n  }\n`;\n\ntype NumberKeyedObject = Record<number, string>;\n\ntype StringFilter = {\n  [key: string]: string;\n};\n\ntype MimeFilter = {\n  [key: string]:\n    | string\n    | NumberKeyedObject\n    | Record<string, string | NumberKeyedObject>\n    | undefined;\n};\n\nexport type FilterStructure = {\n  [key: string]: MimeFilter | StringFilter | undefined;\n};\n\nexport type Filter = {\n  [key in 'mime' | 'createdAt' | 'updatedAt']?:\n    | {\n        [key in '$contains' | '$notContains' | '$eq' | '$not']?:\n          | string[]\n          | string\n          | { $contains: string[] };\n      }\n    | undefined;\n};\n\nexport interface FolderWithType extends Omit<Folder, 'children' | 'files'> {\n  folderURL?: string;\n  isSelectable?: boolean;\n  type?: string;\n  children?: Folder['children'] & {\n    count: number;\n  };\n  files?: Folder['files'] & {\n    count: number;\n  };\n}\n\nexport interface FileWithType extends File {\n  folderURL?: string;\n  isSelectable?: boolean;\n  type?: string;\n}\n\nexport interface BrowseStepProps {\n  allowedTypes?: AllowedTypes[];\n  assets: File[];\n  canCreate: boolean;\n  canRead: boolean;\n  folders?: FolderWithType[];\n  multiple?: boolean;\n  onAddAsset: () => void;\n  onChangeFilters: (filters: FilterCondition<string>[] | Filter[]) => void;\n  onChangeFolder: (id: number, path?: string) => void;\n  onChangePage: (page: number) => void;\n  onChangePageSize: (value: number) => void;\n  onChangeSort: (value: Query['sort'] | string) => void;\n  onChangeSearch: (_q?: Query['_q'] | null) => void;\n  onEditAsset: ((asset: FileWithType) => void) | null;\n  onEditFolder: ((folder: FolderRow) => void) | null;\n  onSelectAsset: (element: FileRow | FolderRow) => void;\n  onSelectAllAsset?: (checked: boolean | string, rows?: FolderRow[] | FileRow[]) => void;\n  queryObject: Query;\n  pagination: { pageCount: number };\n  selectedAssets: FileWithType[] | FolderWithType[];\n}\n\nexport const BrowseStep = ({\n  allowedTypes = [],\n  assets: rawAssets,\n  canCreate,\n  canRead,\n  folders = [],\n  multiple = false,\n  onAddAsset,\n  onChangeFilters,\n  onChangePage,\n  onChangePageSize,\n  onChangeSearch,\n  onChangeSort,\n  onChangeFolder,\n  onEditAsset,\n  onEditFolder,\n  onSelectAllAsset,\n  onSelectAsset,\n  pagination,\n  queryObject,\n  selectedAssets,\n}: BrowseStepProps) => {\n  const { formatMessage } = useIntl();\n  const [view, setView] = usePersistentState(localStorageKeys.modalView, viewOptions.GRID);\n  const isGridView = view === viewOptions.GRID;\n\n  const { data: currentFolder, isLoading: isCurrentFolderLoading } = useFolder(\n    queryObject?.folder as number | null | undefined,\n    {\n      enabled: canRead && !!queryObject?.folder,\n    }\n  );\n\n  const singularTypes = toSingularTypes(allowedTypes);\n  const assets = rawAssets.map((asset) => ({\n    ...asset,\n    isSelectable: isSelectable(singularTypes, asset?.mime),\n    type: 'asset',\n  }));\n\n  const breadcrumbs = !isCurrentFolderLoading\n    ? getBreadcrumbDataCM(currentFolder as BreadcrumbDataFolder)\n    : undefined;\n\n  const allAllowedAsset = getAllowedFiles(allowedTypes, assets as AllowedFiles[]);\n  const areAllAssetSelected =\n    allAllowedAsset.length > 0 &&\n    selectedAssets.length > 0 &&\n    allAllowedAsset.every(\n      (asset) => selectedAssets.findIndex((currAsset) => currAsset.id === asset.id) !== -1\n    );\n  const hasSomeAssetSelected = allAllowedAsset.some(\n    (asset) => selectedAssets.findIndex((currAsset) => currAsset.id === asset.id) !== -1\n  );\n  const isSearching = !!queryObject?._q;\n  const isFiltering = !!queryObject?.filters?.$and?.length && queryObject.filters.$and.length > 0;\n  const isSearchingOrFiltering = isSearching || isFiltering;\n  const assetCount = assets.length;\n  const folderCount = folders.length;\n  const handleClickFolderCard = (...args: Parameters<typeof onChangeFolder>) => {\n    // Search query will always fetch the same results\n    // we remove it here to allow navigating in a folder and see the result of this navigation\n    onChangeSearch('');\n    onChangeFolder(...args);\n  };\n\n  return (\n    <Box>\n      {onSelectAllAsset && (\n        <Box paddingBottom={4}>\n          <Flex justifyContent=\"space-between\" alignItems=\"flex-start\">\n            {(assetCount > 0 || folderCount > 0 || isFiltering) && (\n              <Flex gap={2} wrap=\"wrap\">\n                {multiple && isGridView && (\n                  <Flex\n                    paddingLeft={2}\n                    paddingRight={2}\n                    background=\"neutral0\"\n                    hasRadius\n                    borderColor=\"neutral200\"\n                    height=\"3.2rem\"\n                  >\n                    <Checkbox\n                      aria-label={formatMessage({\n                        id: getTrad('bulk.select.label'),\n                        defaultMessage: 'Select all assets',\n                      })}\n                      checked={\n                        !areAllAssetSelected && hasSomeAssetSelected\n                          ? 'indeterminate'\n                          : areAllAssetSelected\n                      }\n                      onCheckedChange={onSelectAllAsset}\n                    />\n                  </Flex>\n                )}\n                {isGridView && <SortPicker onChangeSort={onChangeSort} value={queryObject?.sort} />}\n                <Filters\n                  appliedFilters={queryObject?.filters?.$and as ImportedFilterStructure[]}\n                  onChangeFilters={onChangeFilters}\n                />\n              </Flex>\n            )}\n\n            {(assetCount > 0 || folderCount > 0 || isSearching) && (\n              <Flex marginLeft=\"auto\" shrink={0} gap={2}>\n                <ActionContainer paddingTop={1} paddingBottom={1}>\n                  <IconButton\n                    label={\n                      isGridView\n                        ? formatMessage({\n                            id: 'view-switch.list',\n                            defaultMessage: 'List View',\n                          })\n                        : formatMessage({\n                            id: 'view-switch.grid',\n                            defaultMessage: 'Grid View',\n                          })\n                    }\n                    onClick={() => setView(isGridView ? viewOptions.LIST : viewOptions.GRID)}\n                  >\n                    {isGridView ? <List /> : <GridIcon />}\n                  </IconButton>\n                </ActionContainer>\n                <SearchAsset onChangeSearch={onChangeSearch} queryValue={queryObject._q || ''} />\n              </Flex>\n            )}\n          </Flex>\n        </Box>\n      )}\n\n      {canRead && breadcrumbs?.length && breadcrumbs.length > 0 && currentFolder && (\n        <Box paddingTop={3}>\n          <Breadcrumbs\n            onChangeFolder={onChangeFolder}\n            label={formatMessage({\n              id: getTrad('header.breadcrumbs.nav.label'),\n              defaultMessage: 'Folders navigation',\n            })}\n            breadcrumbs={breadcrumbs as BreadcrumbDataFolder[]}\n            currentFolderId={queryObject?.folder as number | undefined}\n          />\n        </Box>\n      )}\n\n      {assetCount === 0 && folderCount === 0 && (\n        <Box paddingBottom={6}>\n          <EmptyAssets\n            size=\"S\"\n            count={6}\n            action={\n              canCreate &&\n              !isFiltering &&\n              !isSearching && (\n                <Button variant=\"secondary\" startIcon={<Plus />} onClick={onAddAsset}>\n                  {formatMessage({\n                    id: getTrad('header.actions.add-assets'),\n                    defaultMessage: 'Add new assets',\n                  })}\n                </Button>\n              )\n            }\n            content={\n              // eslint-disable-next-line no-nested-ternary\n              isSearchingOrFiltering\n                ? formatMessage({\n                    id: getTrad('list.assets-empty.title-withSearch'),\n                    defaultMessage: 'There are no assets with the applied filters',\n                  })\n                : canCreate && !isSearching\n                  ? formatMessage({\n                      id: getTrad('list.assets.empty'),\n                      defaultMessage: 'Upload your first assets...',\n                    })\n                  : formatMessage({\n                      id: getTrad('list.assets.empty.no-permissions'),\n                      defaultMessage: 'The asset list is empty',\n                    })\n            }\n          />\n        </Box>\n      )}\n\n      {!isGridView && (folderCount > 0 || assetCount > 0) && (\n        <TableList\n          allowedTypes={allowedTypes}\n          assetCount={assetCount}\n          folderCount={folderCount}\n          indeterminate={!areAllAssetSelected && hasSomeAssetSelected}\n          isFolderSelectionAllowed={false}\n          onChangeSort={onChangeSort}\n          onChangeFolder={handleClickFolderCard}\n          onEditAsset={onEditAsset}\n          onEditFolder={onEditFolder}\n          onSelectOne={onSelectAsset}\n          onSelectAll={onSelectAllAsset!}\n          rows={\n            [...folders.map((folder) => ({ ...folder, type: 'folder' })), ...assets] as\n              | FolderRow[]\n              | FileRow[]\n          }\n          selected={selectedAssets}\n          shouldDisableBulkSelect={!multiple}\n          sortQuery={queryObject?.sort ?? ''}\n        />\n      )}\n\n      {isGridView && (\n        <>\n          {folderCount > 0 && (\n            <FolderGridList\n              title={\n                (((isSearchingOrFiltering && assetCount > 0) || !isSearchingOrFiltering) &&\n                  formatMessage(\n                    {\n                      id: getTrad('list.folders.title'),\n                      defaultMessage: 'Folders ({count})',\n                    },\n                    { count: folderCount }\n                  )) ||\n                ''\n              }\n            >\n              {folders.map((folder) => {\n                return (\n                  <Grid.Item\n                    col={3}\n                    key={`folder-${folder.id}`}\n                    direction=\"column\"\n                    alignItems=\"stretch\"\n                  >\n                    <FolderCard\n                      ariaLabel={folder.name}\n                      id={`folder-${folder.id}`}\n                      onClick={() => handleClickFolderCard(folder.id, folder.path)}\n                      cardActions={\n                        onEditFolder && (\n                          <IconButton\n                            withTooltip={false}\n                            label={formatMessage({\n                              id: getTrad('list.folder.edit'),\n                              defaultMessage: 'Edit folder',\n                            })}\n                            onClick={() => onEditFolder(folder)}\n                          >\n                            <Pencil />\n                          </IconButton>\n                        )\n                      }\n                    >\n                      <FolderCardBody>\n                        <FolderCardBodyAction\n                          onClick={() => handleClickFolderCard(folder.id, folder.path)}\n                        >\n                          <Flex tag=\"h2\" direction=\"column\" alignItems=\"start\" maxWidth=\"100%\">\n                            <TypographyMaxWidth\n                              fontWeight=\"semiBold\"\n                              ellipsis\n                              textColor=\"neutral800\"\n                            >\n                              {folder.name}\n                              {/* VisuallyHidden dash here allows to separate folder title and count informations\n                              for voice reading structure purpose */}\n                              <VisuallyHidden>-</VisuallyHidden>\n                            </TypographyMaxWidth>\n                            <TypographyMaxWidth\n                              tag=\"span\"\n                              textColor=\"neutral600\"\n                              variant=\"pi\"\n                              ellipsis\n                            >\n                              {formatMessage(\n                                {\n                                  id: getTrad('list.folder.subtitle'),\n                                  defaultMessage:\n                                    '{folderCount, plural, =0 {# folder} one {# folder} other {# folders}}, {filesCount, plural, =0 {# asset} one {# asset} other {# assets}}',\n                                },\n                                {\n                                  folderCount: folder.children?.count,\n                                  filesCount: folder.files?.count,\n                                }\n                              )}\n                            </TypographyMaxWidth>\n                          </Flex>\n                        </FolderCardBodyAction>\n                      </FolderCardBody>\n                    </FolderCard>\n                  </Grid.Item>\n                );\n              })}\n            </FolderGridList>\n          )}\n\n          {assetCount > 0 && folderCount > 0 && (\n            <Box paddingTop={6}>\n              <Divider />\n            </Box>\n          )}\n\n          {assetCount > 0 && (\n            <Box paddingTop={6}>\n              <AssetGridList\n                allowedTypes={allowedTypes}\n                size=\"S\"\n                assets={assets}\n                onSelectAsset={onSelectAsset}\n                selectedAssets={selectedAssets as FileWithType[]}\n                onEditAsset={onEditAsset!}\n                title={\n                  ((!isSearchingOrFiltering || (isSearchingOrFiltering && folderCount > 0)) &&\n                    queryObject.page === 1 &&\n                    formatMessage(\n                      {\n                        id: getTrad('list.assets.title'),\n                        defaultMessage: 'Assets ({count})',\n                      },\n                      { count: assetCount }\n                    )) ||\n                  ''\n                }\n              />\n            </Box>\n          )}\n        </>\n      )}\n\n      {pagination.pageCount > 0 && (\n        <Flex justifyContent=\"space-between\" paddingTop={4} position=\"relative\" zIndex={1}>\n          <PageSize\n            pageSize={queryObject.pageSize! as number}\n            onChangePageSize={onChangePageSize}\n          />\n          <PaginationFooter\n            activePage={queryObject.page as number}\n            onChangePage={onChangePage}\n            pagination={pagination}\n          />\n        </Flex>\n      )}\n    </Box>\n  );\n};\n", "import { Button, Modal } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\n\ninterface DialogFooterProps {\n  onClose: () => void;\n  onValidate?: () => void;\n}\n\nexport const DialogFooter = ({ onClose, onValidate }: DialogFooterProps) => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Modal.Footer>\n      <Button onClick={onClose} variant=\"tertiary\">\n        {formatMessage({ id: 'app.components.Button.cancel', defaultMessage: 'Cancel' })}\n      </Button>\n      {onValidate && (\n        <Button onClick={onValidate}>\n          {formatMessage({ id: 'global.finish', defaultMessage: 'Finish' })}\n        </Button>\n      )}\n    </Modal.Footer>\n  );\n};\n", "// TODO: find a better naming convention for the file that was an index file before\nimport { Flex, Typography } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\n\nimport { getTrad } from '../../../utils';\nimport { AssetGridList } from '../../AssetGridList/AssetGridList';\n\nimport type { File } from '../../../../../shared/contracts/files';\n\ninterface SelectedStepProps {\n  onSelectAsset: (asset: File) => void;\n  selectedAssets: File[];\n  onReorderAsset?: (fromIndex: number, toIndex: number) => void;\n}\n\nexport const SelectedStep = ({\n  selectedAssets,\n  onSelectAsset,\n  onReorderAsset,\n}: SelectedStepProps) => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Flex direction=\"column\" alignItems=\"stretch\" gap={4}>\n      <Flex gap={0} direction=\"column\" alignItems=\"start\">\n        <Typography variant=\"pi\" fontWeight=\"bold\" textColor=\"neutral800\">\n          {formatMessage(\n            {\n              id: getTrad('list.assets.to-upload'),\n              defaultMessage:\n                '{number, plural, =0 {No asset} one {1 asset} other {# assets}} ready to upload',\n            },\n            { number: selectedAssets.length }\n          )}\n        </Typography>\n        <Typography variant=\"pi\" textColor=\"neutral600\">\n          {formatMessage({\n            id: getTrad('modal.upload-list.sub-header-subtitle'),\n            defaultMessage: 'Manage the assets before adding them to the Media Library',\n          })}\n        </Typography>\n      </Flex>\n\n      <AssetGridList\n        size=\"S\"\n        assets={selectedAssets}\n        onSelectAsset={onSelectAsset}\n        selectedAssets={selectedAssets}\n        onReorderAsset={onReorderAsset}\n      />\n    </Flex>\n  );\n};\n", "// TODO: find a better naming convention for the file that was an index file before\nimport * as React from 'react';\n\nimport { Page } from '@strapi/admin/strapi-admin';\nimport { <PERSON><PERSON>, But<PERSON>, Divider, <PERSON>lex, Loader, Modal, Tabs } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\nimport { styled } from 'styled-components';\n\nimport { useAssets } from '../../hooks/useAssets';\nimport { useFolders } from '../../hooks/useFolders';\nimport { useMediaLibraryPermissions } from '../../hooks/useMediaLibraryPermissions';\nimport { useModalQueryParams } from '../../hooks/useModalQueryParams';\nimport { useSelectionState } from '../../hooks/useSelectionState';\nimport {\n  containsAssetFilter,\n  getTrad,\n  getAllowedFiles,\n  moveElement,\n  AllowedFiles,\n} from '../../utils';\nimport { EditAssetContent, Asset as EditAsset } from '../EditAssetDialog/EditAssetContent';\nimport { EditFolderContent } from '../EditFolderDialog/EditFolderDialog';\n\nimport {\n  BrowseStep,\n  FolderWithType,\n  FileWithType,\n  Filter as BrowseFilter,\n} from './BrowseStep/BrowseStep';\nimport { DialogFooter } from './DialogFooter';\nimport { SelectedStep } from './SelectedStep/SelectedStep';\n\nimport type { File as Asset, FilterCondition, Query } from '../../../../shared/contracts/files';\nimport type { Folder, FolderDefinition } from '../../../../shared/contracts/folders';\nimport type { AllowedTypes } from '../AssetCard/AssetCard';\n\nconst LoadingBody = styled(Flex)`\n  /* 80px are coming from the Tabs component that is not included in the ModalBody */\n  min-height: ${() => `calc(60vh + 8rem)`};\n`;\n\nexport interface FileRow extends Asset {\n  folderURL?: string;\n  isSelectable?: boolean;\n  type?: string;\n}\n\nexport interface FolderRow extends Folder {\n  folderURL?: string;\n  isSelectable?: boolean;\n  type?: string;\n}\n\ninterface AssetContentProps {\n  allowedTypes?: AllowedTypes[];\n  folderId?: number | null;\n  onClose: () => void;\n  onAddAsset: (arg?: { folderId: number | { id: number } | null | undefined }) => void;\n  onAddFolder: ({ folderId }: { folderId: number | { id: number } | null | undefined }) => void;\n  onChangeFolder: (folderId: number | null) => void;\n  onValidate: (selectedAssets: Asset[]) => void;\n  multiple?: boolean;\n  trackedLocation?: string;\n  initiallySelectedAssets?: Asset[];\n}\n\nexport const AssetContent = ({\n  allowedTypes = [],\n  folderId = null,\n  onClose,\n  onAddAsset,\n  onAddFolder,\n  onChangeFolder,\n  onValidate,\n  multiple = false,\n  initiallySelectedAssets = [],\n  trackedLocation,\n}: AssetContentProps) => {\n  const [assetToEdit, setAssetToEdit] = React.useState<FileWithType | undefined>(undefined);\n  const [folderToEdit, setFolderToEdit] = React.useState<FolderRow | undefined>(undefined);\n  const { formatMessage } = useIntl();\n  const {\n    canRead,\n    canCreate,\n    isLoading: isLoadingPermissions,\n    canUpdate,\n    canCopyLink,\n    canDownload,\n  } = useMediaLibraryPermissions();\n\n  const [\n    { queryObject },\n    {\n      onChangeFilters,\n      onChangePage,\n      onChangePageSize,\n      onChangeSort,\n      onChangeSearch,\n      onChangeFolder: onChangeFolderParam,\n    },\n  ] = useModalQueryParams({ folder: folderId });\n\n  const {\n    data: { pagination, results: assets } = {},\n    isLoading: isLoadingAssets,\n    error: errorAssets,\n  } = useAssets({ skipWhen: !canRead, query: queryObject });\n\n  const {\n    data: folders,\n    isLoading: isLoadingFolders,\n    error: errorFolders,\n  } = useFolders({\n    enabled: canRead && !containsAssetFilter(queryObject!) && pagination?.page === 1,\n    query: queryObject,\n  });\n\n  const [\n    selectedAssets,\n    { selectOne, selectOnly, setSelections, selectMultiple, deselectMultiple },\n  ] = useSelectionState(['id'], initiallySelectedAssets);\n\n  const handleSelectAllAssets = () => {\n    const allowedAssets = getAllowedFiles(allowedTypes, assets as AllowedFiles[]);\n\n    if (!multiple) {\n      return undefined;\n    }\n\n    // selected files in current folder\n    const alreadySelected = allowedAssets.filter(\n      (asset) => selectedAssets.findIndex((selectedAsset) => selectedAsset.id === asset.id) !== -1\n    );\n\n    if (alreadySelected.length > 0) {\n      deselectMultiple(alreadySelected);\n    } else {\n      selectMultiple(allowedAssets);\n    }\n  };\n\n  const handleSelectAsset = (asset: Asset | FileRow | FolderRow) => {\n    return multiple ? selectOne(asset as Asset) : selectOnly(asset as Asset);\n  };\n\n  const isLoading = isLoadingPermissions || isLoadingAssets || isLoadingFolders;\n  const hasError = errorAssets || errorFolders;\n\n  const [activeTab, setActiveTab] = React.useState(\n    selectedAssets.length > 0 ? 'selected' : 'browse'\n  );\n\n  if (isLoading) {\n    return (\n      <>\n        <Modal.Header>\n          <Modal.Title>\n            {formatMessage({\n              id: getTrad('header.actions.add-assets'),\n              defaultMessage: 'Add new assets',\n            })}\n          </Modal.Title>\n        </Modal.Header>\n        <LoadingBody justifyContent=\"center\" paddingTop={4} paddingBottom={4}>\n          <Loader>\n            {formatMessage({\n              id: getTrad('content.isLoading'),\n              defaultMessage: 'Content is loading.',\n            })}\n          </Loader>\n        </LoadingBody>\n        <DialogFooter onClose={onClose} />\n      </>\n    );\n  }\n\n  if (hasError) {\n    return (\n      <>\n        <Modal.Header>\n          <Modal.Title>\n            {formatMessage({\n              id: getTrad('header.actions.add-assets'),\n              defaultMessage: 'Add new assets',\n            })}\n          </Modal.Title>\n        </Modal.Header>\n        <Page.Error />\n        <DialogFooter onClose={onClose} />\n      </>\n    );\n  }\n\n  if (!canRead) {\n    return (\n      <>\n        <Modal.Header>\n          <Modal.Title>\n            {formatMessage({\n              id: getTrad('header.actions.add-assets'),\n              defaultMessage: 'Add new assets',\n            })}\n          </Modal.Title>\n        </Modal.Header>\n        <Page.NoPermissions />\n        <DialogFooter onClose={onClose} />\n      </>\n    );\n  }\n\n  if (assetToEdit) {\n    return (\n      <EditAssetContent\n        onClose={() => setAssetToEdit(undefined)}\n        asset={assetToEdit as EditAsset}\n        canUpdate={canUpdate}\n        canCopyLink={canCopyLink}\n        canDownload={canDownload}\n        trackedLocation={trackedLocation}\n      />\n    );\n  }\n\n  if (folderToEdit) {\n    return (\n      <EditFolderContent\n        folder={folderToEdit as FolderDefinition}\n        onClose={() => setFolderToEdit(undefined)}\n        location=\"content-manager\"\n        parentFolderId={queryObject?.folder as string | number | null | undefined}\n      />\n    );\n  }\n\n  const handleMoveItem = (hoverIndex: number, destIndex: number) => {\n    const offset = destIndex - hoverIndex;\n    const orderedAssetsClone = selectedAssets.slice();\n    const nextAssets = moveElement<Asset>(orderedAssetsClone, hoverIndex, offset);\n    setSelections(nextAssets);\n  };\n\n  const handleFolderChange = (folderId: number, folderPath?: string) => {\n    onChangeFolder(folderId);\n    if (onChangeFolderParam) {\n      onChangeFolderParam(folderId, folderPath);\n    }\n  };\n\n  return (\n    <>\n      <Modal.Header>\n        <Modal.Title>\n          {formatMessage({\n            id: getTrad('header.actions.add-assets'),\n            defaultMessage: 'Add new assets',\n          })}\n        </Modal.Title>\n      </Modal.Header>\n\n      <TabsRoot variant=\"simple\" value={activeTab} onValueChange={setActiveTab}>\n        <Flex paddingLeft={8} paddingRight={8} paddingTop={6} justifyContent=\"space-between\">\n          <Tabs.List>\n            <Tabs.Trigger value=\"browse\">\n              {formatMessage({\n                id: getTrad('modal.nav.browse'),\n                defaultMessage: 'Browse',\n              })}\n            </Tabs.Trigger>\n            <Tabs.Trigger value=\"selected\">\n              {formatMessage({\n                id: getTrad('modal.header.select-files'),\n                defaultMessage: 'Selected files',\n              })}\n              <Badge marginLeft={2}>{selectedAssets.length}</Badge>\n            </Tabs.Trigger>\n          </Tabs.List>\n          <Flex gap={2}>\n            <Button\n              variant=\"secondary\"\n              onClick={() => onAddFolder({ folderId: queryObject?.folder })}\n            >\n              {formatMessage({\n                id: getTrad('modal.upload-list.sub-header.add-folder'),\n                defaultMessage: 'Add folder',\n              })}\n            </Button>\n            <Button onClick={() => onAddAsset({ folderId: queryObject?.folder })}>\n              {formatMessage({\n                id: getTrad('modal.upload-list.sub-header.button'),\n                defaultMessage: 'Add more assets',\n              })}\n            </Button>\n          </Flex>\n        </Flex>\n        <Divider />\n        <Modal.Body>\n          <Tabs.Content value=\"browse\">\n            <BrowseStep\n              allowedTypes={allowedTypes}\n              assets={assets!}\n              canCreate={canCreate}\n              canRead={canRead}\n              folders={folders as FolderWithType[]}\n              onSelectAsset={handleSelectAsset}\n              selectedAssets={selectedAssets}\n              multiple={multiple}\n              onSelectAllAsset={handleSelectAllAssets}\n              onEditAsset={setAssetToEdit}\n              onEditFolder={setFolderToEdit}\n              pagination={pagination!}\n              queryObject={queryObject!}\n              onAddAsset={onAddAsset}\n              onChangeFilters={(filters: FilterCondition<string>[] | BrowseFilter[]) =>\n                onChangeFilters!(filters as FilterCondition<string>[])\n              }\n              onChangeFolder={handleFolderChange}\n              onChangePage={onChangePage!}\n              onChangePageSize={onChangePageSize!}\n              onChangeSort={(sort: string | undefined) => onChangeSort!(sort as Query['sort'])}\n              onChangeSearch={onChangeSearch!}\n            />\n          </Tabs.Content>\n          <Tabs.Content value=\"selected\">\n            <SelectedStep\n              selectedAssets={selectedAssets}\n              onSelectAsset={handleSelectAsset}\n              onReorderAsset={handleMoveItem}\n            />\n          </Tabs.Content>\n        </Modal.Body>\n      </TabsRoot>\n      <DialogFooter onClose={onClose} onValidate={() => onValidate(selectedAssets)} />\n    </>\n  );\n};\n\ninterface AssetDialogProps extends AssetContentProps {\n  open?: boolean;\n}\n\nexport const AssetDialog = ({ open = false, onClose, ...restProps }: AssetDialogProps) => {\n  return (\n    <Modal.Root open={open} onOpenChange={onClose}>\n      <Modal.Content>\n        <AssetContent onClose={onClose} {...restProps} />\n      </Modal.Content>\n    </Modal.Root>\n  );\n};\n\nconst TabsRoot = styled(Tabs.Root)`\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n`;\n", "// TODO: find a better naming convention for the file that was an index file before\nimport * as React from 'react';\n\nimport { AssetDialog } from '../AssetDialog/AssetDialog';\nimport { EditFolderDialog } from '../EditFolderDialog/EditFolderDialog';\nimport { UploadAssetDialog } from '../UploadAssetDialog/UploadAssetDialog';\n\nconst STEPS = {\n  AssetSelect: 'SelectAsset',\n  AssetUpload: 'UploadAsset',\n  FolderCreate: 'FolderCreate',\n};\n\nimport type { File } from '../../../../shared/contracts/files';\nimport type { AllowedTypes } from '../AssetCard/AssetCard';\nexport interface MediaLibraryDialogProps {\n  allowedTypes?: AllowedTypes[];\n  onClose: () => void;\n  onSelectAssets: (selectedAssets: File[]) => void;\n}\n\nexport const MediaLibraryDialog = ({\n  onClose,\n  onSelectAssets,\n  allowedTypes = ['files', 'images', 'videos', 'audios'],\n}: MediaLibraryDialogProps) => {\n  const [step, setStep] = React.useState(STEPS.AssetSelect);\n  const [folderId, setFolderId] = React.useState<number | null>(null);\n\n  switch (step) {\n    case STEPS.AssetSelect:\n      return (\n        <AssetDialog\n          allowedTypes={allowedTypes}\n          folderId={folderId}\n          open\n          onClose={onClose}\n          onValidate={onSelectAssets}\n          onAddAsset={() => setStep(STEPS.AssetUpload)}\n          onAddFolder={() => setStep(STEPS.FolderCreate)}\n          onChangeFolder={(folderId) => setFolderId(folderId)}\n          multiple\n        />\n      );\n\n    case STEPS.FolderCreate:\n      return (\n        <EditFolderDialog\n          open\n          onClose={() => setStep(STEPS.AssetSelect)}\n          parentFolderId={folderId}\n        />\n      );\n\n    default:\n      return (\n        <UploadAssetDialog open onClose={() => setStep(STEPS.AssetSelect)} folderId={folderId} />\n      );\n  }\n};\n", "import { Box, Flex } from '@strapi/design-system';\nimport { File, FilePdf } from '@strapi/icons';\nimport { styled } from 'styled-components';\n\nimport { AssetType } from '../../../constants';\nimport { createAssetUrl } from '../../../utils';\nimport { AudioPreview } from '../../AssetCard/AudioPreview';\nimport { VideoPreview } from '../../AssetCard/VideoPreview';\n\nimport type { File as FileAsset } from '../../../../../shared/contracts/files';\n\nconst DocAsset = styled(Flex)`\n  background: linear-gradient(180deg, #ffffff 0%, #f6f6f9 121.48%);\n`;\n\nconst VideoPreviewWrapper = styled(Box)`\n  canvas,\n  video {\n    max-width: 100%;\n    height: 124px;\n  }\n`;\n\nconst AudioPreviewWrapper = styled(Box)`\n  canvas,\n  audio {\n    max-width: 100%;\n  }\n`;\n\nexport const CarouselAsset = ({ asset }: { asset: FileAsset }) => {\n  if (asset.mime?.includes(AssetType.Video)) {\n    return (\n      <VideoPreviewWrapper height=\"100%\">\n        <VideoPreview\n          url={createAssetUrl(asset, true)!}\n          mime={asset.mime}\n          alt={asset.alternativeText || asset.name}\n        />\n      </VideoPreviewWrapper>\n    );\n  }\n\n  if (asset.mime?.includes(AssetType.Audio)) {\n    return (\n      <AudioPreviewWrapper>\n        <AudioPreview\n          url={createAssetUrl(asset, true)!}\n          alt={asset.alternativeText || asset.name}\n        />\n      </AudioPreviewWrapper>\n    );\n  }\n\n  if (asset.mime?.includes(AssetType.Image)) {\n    const assetUrl = createAssetUrl(asset, true);\n    if (!assetUrl) return null;\n\n    // Adding a param to the url to bust the cache and force the refresh of the image when replaced\n    const cacheBustedUrl = `${assetUrl}${assetUrl.includes('?') ? '&' : '?'}updatedAt=${asset.updatedAt}`;\n\n    return (\n      <Box\n        tag=\"img\"\n        maxHeight=\"100%\"\n        maxWidth=\"100%\"\n        src={cacheBustedUrl}\n        alt={asset.alternativeText || asset.name}\n      />\n    );\n  }\n\n  return (\n    <DocAsset width=\"100%\" height=\"100%\" justifyContent=\"center\" hasRadius>\n      {asset.ext?.includes('pdf') ? (\n        <FilePdf aria-label={asset.alternativeText || asset.name} width=\"24px\" height=\"32px\" />\n      ) : (\n        <File aria-label={asset.alternativeText || asset.name} width=\"24px\" height=\"32px\" />\n      )}\n    </DocAsset>\n  );\n};\n", "import { CarouselActions, IconButton } from '@strapi/design-system';\nimport { Pencil, Plus, Trash } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\n\nimport { getTrad, prefixFileUrlWithBackendUrl } from '../../../utils';\nimport { CopyLinkButton } from '../../CopyLinkButton/CopyLinkButton';\n\nimport type { File } from '../../../../../shared/contracts/files';\n\ninterface CarouselAssetActionsProps {\n  asset: File;\n  onDeleteAsset?: (asset: File) => void;\n  onAddAsset?: (asset: File) => void;\n  onEditAsset?: () => void;\n}\n\nexport const CarouselAssetActions = ({\n  asset,\n  onDeleteAsset,\n  onAddAsset,\n  onEditAsset,\n}: CarouselAssetActionsProps) => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <CarouselActions>\n      {onAddAsset && (\n        <IconButton\n          label={formatMessage({\n            id: getTrad('control-card.add'),\n            defaultMessage: 'Add',\n          })}\n          onClick={() => onAddAsset(asset)}\n        >\n          <Plus />\n        </IconButton>\n      )}\n\n      <CopyLinkButton url={prefixFileUrlWithBackendUrl(asset.url)!} />\n\n      {onDeleteAsset && (\n        <IconButton\n          label={formatMessage({\n            id: 'global.delete',\n            defaultMessage: 'Delete',\n          })}\n          onClick={() => onDeleteAsset(asset)}\n        >\n          <Trash />\n        </IconButton>\n      )}\n\n      {onEditAsset && (\n        <IconButton\n          label={formatMessage({\n            id: getTrad('control-card.edit'),\n            defaultMessage: 'edit',\n          })}\n          onClick={onEditAsset}\n        >\n          <Pencil />\n        </IconButton>\n      )}\n    </CarouselActions>\n  );\n};\n", "import * as React from 'react';\n\nimport { Flex, Typography } from '@strapi/design-system';\nimport { PlusCircle as PicturePlus } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\nimport { styled } from 'styled-components';\n\nimport { AssetSource } from '../../../constants';\nimport { getTrad, rawFileToAsset } from '../../../utils';\n\nimport type { File } from '../../../../../shared/contracts/files';\n\nconst TextAlignTypography = styled(Typography)`\n  align-items: center;\n`;\n\ntype FileWithoutIdHash = Omit<File, 'id' | 'hash'>;\n\ninterface EmptyStateAssetProps {\n  disabled?: boolean;\n  onClick: (asset?: File, event?: React.MouseEventHandler<HTMLButtonElement>) => void;\n  onDropAsset: (assets: FileWithoutIdHash[]) => void;\n}\n\nexport const EmptyStateAsset = ({\n  disabled = false,\n  onClick,\n  onDropAsset,\n}: EmptyStateAssetProps) => {\n  const { formatMessage } = useIntl();\n  const [dragOver, setDragOver] = React.useState(false);\n\n  const handleDragEnter = (e: React.DragEvent<HTMLButtonElement>) => {\n    e.preventDefault();\n    setDragOver(true);\n  };\n\n  const handleDragLeave = (e: React.DragEvent<HTMLButtonElement>) => {\n    if (!e.currentTarget.contains(e.relatedTarget as Node)) {\n      setDragOver(false);\n    }\n  };\n\n  const handleDragOver = (e: React.DragEvent<HTMLButtonElement>) => {\n    e.preventDefault();\n  };\n\n  const handleDrop = (e: React.DragEvent<HTMLButtonElement>) => {\n    e.preventDefault();\n\n    if (e?.dataTransfer?.files) {\n      const files = e.dataTransfer.files;\n      const assets: FileWithoutIdHash[] = [];\n\n      for (let i = 0; i < files.length; i++) {\n        const file = files.item(i);\n        if (file) {\n          const asset = rawFileToAsset(file, AssetSource.Computer);\n\n          assets.push(asset);\n        }\n      }\n\n      onDropAsset(assets);\n    }\n\n    setDragOver(false);\n  };\n\n  return (\n    <Flex\n      borderStyle={dragOver ? 'dashed' : undefined}\n      borderWidth={dragOver ? '1px' : undefined}\n      borderColor={dragOver ? 'primary600' : undefined}\n      direction=\"column\"\n      justifyContent=\"center\"\n      alignItems=\"center\"\n      height=\"100%\"\n      width=\"100%\"\n      tag=\"button\"\n      type=\"button\"\n      disabled={disabled}\n      onClick={onClick as unknown as React.MouseEventHandler<HTMLButtonElement>}\n      onDragEnter={handleDragEnter}\n      onDragLeave={handleDragLeave}\n      onDragOver={handleDragOver}\n      onDrop={handleDrop}\n      gap={3}\n      style={{ cursor: disabled ? 'not-allowed' : 'pointer' }}\n    >\n      <PicturePlus\n        aria-hidden\n        width=\"3.2rem\"\n        height=\"3.2rem\"\n        fill={disabled ? 'neutral400' : 'primary600'}\n      />\n      <TextAlignTypography\n        variant=\"pi\"\n        fontWeight=\"bold\"\n        textColor=\"neutral600\"\n        style={{ textAlign: 'center' }}\n        tag=\"span\"\n      >\n        {formatMessage({\n          id: getTrad('mediaLibraryInput.placeholder'),\n          defaultMessage: 'Click to add an asset or drag and drop one in this area',\n        })}\n      </TextAlignTypography>\n    </Flex>\n  );\n};\n", "import * as React from 'react';\n\nimport { CarouselInput, CarouselSlide } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\n\nimport { getTrad } from '../../../utils/getTrad';\nimport { EditAssetDialog } from '../../EditAssetDialog/EditAssetContent';\n\nimport { CarouselAsset } from './CarouselAsset';\nimport { CarouselAssetActions } from './CarouselAssetActions';\nimport { EmptyStateAsset } from './EmptyStateAsset';\n\nimport type { File as FileAsset, RawFile } from '../../../../../shared/contracts/files';\n\nexport type FileWithoutIdHash = Omit<FileAsset, 'id' | 'hash'>;\n\ninterface Asset extends Omit<FileAsset, 'folder'> {\n  isLocal?: boolean;\n  rawFile?: RawFile;\n  folder?: FileAsset['folder'] & { id: number };\n}\n\nexport interface CarouselAssetsProps {\n  assets: FileAsset[];\n  disabled?: boolean;\n  error?: string;\n  hint?: string;\n  label: string;\n  labelAction?: React.ReactNode;\n  onAddAsset: (asset?: FileAsset, event?: React.MouseEventHandler<HTMLButtonElement>) => void;\n  onDeleteAsset: (asset: FileAsset) => void;\n  onDeleteAssetFromMediaLibrary: () => void;\n  onDropAsset?: (assets: FileWithoutIdHash[]) => void;\n  onEditAsset?: (asset: FileAsset) => void;\n  onNext: () => void;\n  onPrevious: () => void;\n  required?: boolean;\n  selectedAssetIndex: number;\n  trackedLocation?: string;\n}\n\nexport const CarouselAssets = React.forwardRef(\n  (\n    {\n      assets,\n      disabled = false,\n      error,\n      hint,\n      label,\n      labelAction,\n      onAddAsset,\n      onDeleteAsset,\n      onDeleteAssetFromMediaLibrary,\n      onDropAsset,\n      onEditAsset,\n      onNext,\n      onPrevious,\n      required = false,\n      selectedAssetIndex,\n      trackedLocation,\n    }: CarouselAssetsProps,\n    forwardedRef\n  ) => {\n    const { formatMessage } = useIntl();\n    const [isEditingAsset, setIsEditingAsset] = React.useState(false);\n\n    const currentAsset = assets[selectedAssetIndex];\n\n    return (\n      <>\n        <CarouselInput\n          ref={forwardedRef as React.Ref<HTMLDivElement>}\n          label={label}\n          labelAction={labelAction}\n          secondaryLabel={currentAsset?.name}\n          selectedSlide={selectedAssetIndex}\n          previousLabel={formatMessage({\n            id: getTrad('mediaLibraryInput.actions.previousSlide'),\n            defaultMessage: 'Previous slide',\n          })}\n          nextLabel={formatMessage({\n            id: getTrad('mediaLibraryInput.actions.nextSlide'),\n            defaultMessage: 'Next slide',\n          })}\n          onNext={onNext}\n          onPrevious={onPrevious}\n          hint={hint}\n          error={error}\n          required={required}\n          actions={\n            currentAsset ? (\n              <CarouselAssetActions\n                asset={currentAsset}\n                onDeleteAsset={disabled ? undefined : onDeleteAsset}\n                onAddAsset={disabled ? undefined : onAddAsset}\n                onEditAsset={onEditAsset ? () => setIsEditingAsset(true) : undefined}\n              />\n            ) : undefined\n          }\n        >\n          {assets.length === 0 ? (\n            <CarouselSlide\n              label={formatMessage(\n                {\n                  id: getTrad('mediaLibraryInput.slideCount'),\n                  defaultMessage: '{n} of {m} slides',\n                },\n                { n: 1, m: 1 }\n              )}\n            >\n              <EmptyStateAsset\n                disabled={disabled}\n                onClick={onAddAsset}\n                onDropAsset={onDropAsset!}\n              />\n            </CarouselSlide>\n          ) : (\n            assets.map((asset, index) => (\n              <CarouselSlide\n                key={asset.id}\n                label={formatMessage(\n                  {\n                    id: getTrad('mediaLibraryInput.slideCount'),\n                    defaultMessage: '{n} of {m} slides',\n                  },\n                  { n: index + 1, m: assets.length }\n                )}\n              >\n                <CarouselAsset asset={asset} />\n              </CarouselSlide>\n            ))\n          )}\n        </CarouselInput>\n        <EditAssetDialog\n          open={isEditingAsset}\n          onClose={(editedAsset) => {\n            setIsEditingAsset(false);\n\n            // The asset has been deleted\n            if (editedAsset === null) {\n              onDeleteAssetFromMediaLibrary();\n            }\n            if (editedAsset && typeof editedAsset !== 'boolean') {\n              onEditAsset?.(editedAsset);\n            }\n          }}\n          asset={currentAsset as Asset}\n          canUpdate\n          canCopyLink\n          canDownload\n          trackedLocation={trackedLocation}\n        />\n      </>\n    );\n  }\n);\n", "// TODO: find a better naming convention for the file that was an index file before\nimport * as React from 'react';\n\nimport { useField, useNotification } from '@strapi/admin/strapi-admin';\nimport { useIntl } from 'react-intl';\n\nimport { getTrad, getAllowedFiles, AllowedFiles } from '../../utils';\nimport { AssetDialog } from '../AssetDialog/AssetDialog';\nimport { EditFolderDialog } from '../EditFolderDialog/EditFolderDialog';\nimport { UploadAssetDialog, Asset } from '../UploadAssetDialog/UploadAssetDialog';\n\nimport { CarouselAssets, CarouselAssetsProps, FileWithoutIdHash } from './Carousel/CarouselAssets';\n\nimport type { File } from '../../../../shared/contracts/files';\ntype AllowedTypes = 'files' | 'images' | 'videos' | 'audios';\n\nconst STEPS = {\n  AssetSelect: 'SelectAsset',\n  AssetUpload: 'UploadAsset',\n  FolderCreate: 'FolderCreate',\n};\n\nexport interface MediaLibraryInputProps {\n  required?: boolean;\n  name: string;\n  labelAction?: React.ReactNode;\n  label?: string;\n  hint?: string;\n  disabled?: boolean;\n  attribute?: {\n    allowedTypes?: AllowedTypes[];\n    multiple?: boolean;\n  };\n}\n\nexport const MediaLibraryInput = React.forwardRef<CarouselAssetsProps, MediaLibraryInputProps>(\n  (\n    {\n      attribute: { allowedTypes = null, multiple = false } = {},\n      label,\n      hint,\n      disabled = false,\n      labelAction = undefined,\n      name,\n      required = false,\n    },\n    forwardedRef\n  ) => {\n    const { formatMessage } = useIntl();\n    const { onChange, value, error } = useField(name);\n    const [uploadedFiles, setUploadedFiles] = React.useState<Asset[] | File[]>([]);\n    const [step, setStep] = React.useState<string | undefined>(undefined);\n    const [selectedIndex, setSelectedIndex] = React.useState(0);\n    const [droppedAssets, setDroppedAssets] = React.useState<AllowedFiles[]>();\n    const [folderId, setFolderId] = React.useState<number | null>(null);\n    const { toggleNotification } = useNotification();\n\n    React.useEffect(() => {\n      // Clear the uploaded files on close\n      if (step === undefined) {\n        setUploadedFiles([]);\n      }\n    }, [step]);\n\n    let selectedAssets: File[] = [];\n\n    if (Array.isArray(value)) {\n      selectedAssets = value;\n    } else if (value) {\n      selectedAssets = [value];\n    }\n\n    const handleValidation = (nextSelectedAssets: File[]) => {\n      const value = multiple ? nextSelectedAssets : nextSelectedAssets[0];\n      onChange(name, value);\n      setStep(undefined);\n    };\n\n    const handleDeleteAssetFromMediaLibrary = () => {\n      let nextValue;\n\n      if (multiple) {\n        const nextSelectedAssets = selectedAssets.filter(\n          (_, assetIndex) => assetIndex !== selectedIndex\n        );\n        nextValue = nextSelectedAssets.length > 0 ? nextSelectedAssets : null;\n      } else {\n        nextValue = null;\n      }\n\n      const value = nextValue;\n      onChange(name, value);\n\n      setSelectedIndex(0);\n    };\n\n    const handleDeleteAsset = (asset: File) => {\n      let nextValue;\n\n      if (multiple) {\n        const nextSelectedAssets = selectedAssets.filter((prevAsset) => prevAsset.id !== asset.id);\n\n        nextValue = nextSelectedAssets.length > 0 ? nextSelectedAssets : null;\n      } else {\n        nextValue = null;\n      }\n\n      onChange(name, nextValue);\n\n      setSelectedIndex(0);\n    };\n\n    const handleAssetEdit = (asset: File) => {\n      const nextSelectedAssets = selectedAssets.map((prevAsset) =>\n        prevAsset.id === asset.id ? asset : prevAsset\n      );\n\n      onChange(name, multiple ? nextSelectedAssets : nextSelectedAssets[0]);\n    };\n\n    const validateAssetsTypes = (\n      assets: FileWithoutIdHash[] | Asset[],\n      callback: (assets?: AllowedFiles[], error?: string) => void\n    ) => {\n      const allowedAssets = getAllowedFiles(allowedTypes, assets as AllowedFiles[]);\n\n      if (allowedAssets.length > 0) {\n        callback(allowedAssets);\n      } else {\n        toggleNotification({\n          type: 'danger',\n          timeout: 4000,\n          message: formatMessage(\n            {\n              id: getTrad('input.notification.not-supported'),\n              defaultMessage: `You can't upload this type of file.`,\n            },\n            {\n              fileTypes: (allowedTypes ?? []).join(','),\n            }\n          ),\n        });\n      }\n    };\n\n    const handleAssetDrop = (assets: FileWithoutIdHash[]) => {\n      validateAssetsTypes(assets, (allowedAssets?: AllowedFiles[]) => {\n        setDroppedAssets(allowedAssets);\n        setStep(STEPS.AssetUpload);\n      });\n    };\n\n    if (multiple && selectedAssets.length > 0) {\n      label = `${label} (${selectedIndex + 1} / ${selectedAssets.length})`;\n    }\n\n    const handleNext = () => {\n      setSelectedIndex((current) => (current < selectedAssets.length - 1 ? current + 1 : 0));\n    };\n\n    const handlePrevious = () => {\n      setSelectedIndex((current) => (current > 0 ? current - 1 : selectedAssets.length - 1));\n    };\n\n    const handleFilesUploadSucceeded = (uploadedFiles: Asset[] | File[]) => {\n      setUploadedFiles((prev) => [...prev, ...uploadedFiles]);\n    };\n\n    let initiallySelectedAssets = selectedAssets;\n\n    if (uploadedFiles.length > 0) {\n      const allowedUploadedFiles = getAllowedFiles(allowedTypes, uploadedFiles as AllowedFiles[]);\n\n      initiallySelectedAssets = multiple\n        ? [...allowedUploadedFiles, ...selectedAssets]\n        : [allowedUploadedFiles[0]];\n    }\n\n    return (\n      <>\n        <CarouselAssets\n          ref={forwardedRef}\n          assets={selectedAssets}\n          disabled={disabled}\n          label={label!}\n          labelAction={labelAction}\n          onDeleteAsset={handleDeleteAsset}\n          onDeleteAssetFromMediaLibrary={handleDeleteAssetFromMediaLibrary}\n          onAddAsset={() => setStep(STEPS.AssetSelect)}\n          onDropAsset={handleAssetDrop}\n          onEditAsset={handleAssetEdit}\n          onNext={handleNext}\n          onPrevious={handlePrevious}\n          error={error}\n          hint={hint}\n          required={required}\n          selectedAssetIndex={selectedIndex}\n          trackedLocation=\"content-manager\"\n        />\n\n        {step === STEPS.AssetSelect && (\n          <AssetDialog\n            allowedTypes={allowedTypes as AllowedTypes[]}\n            initiallySelectedAssets={initiallySelectedAssets}\n            folderId={folderId}\n            onClose={() => {\n              setStep(undefined);\n              setFolderId(null);\n            }}\n            open={step === STEPS.AssetSelect}\n            onValidate={handleValidation}\n            multiple={multiple}\n            onAddAsset={() => setStep(STEPS.AssetUpload)}\n            onAddFolder={() => setStep(STEPS.FolderCreate)}\n            onChangeFolder={(folder) => setFolderId(folder)}\n            trackedLocation=\"content-manager\"\n          />\n        )}\n\n        {step === STEPS.AssetUpload && (\n          <UploadAssetDialog\n            open={step === STEPS.AssetUpload}\n            onClose={() => setStep(STEPS.AssetSelect)}\n            initialAssetsToAdd={droppedAssets as Asset[]}\n            addUploadedFiles={handleFilesUploadSucceeded}\n            trackedLocation=\"content-manager\"\n            folderId={folderId}\n            validateAssetsTypes={validateAssetsTypes}\n          />\n        )}\n\n        {step === STEPS.FolderCreate && (\n          <EditFolderDialog\n            open={step === STEPS.FolderCreate}\n            onClose={() => setStep(STEPS.AssetSelect)}\n            parentFolderId={folderId}\n          />\n        )}\n      </>\n    );\n  }\n);\n", "type Translations = {\n  [key: string]: string;\n};\n\nexport const prefixPluginTranslations = (trad: Translations, pluginId?: string) => {\n  if (!pluginId) {\n    throw new TypeError(\"pluginId can't be empty\");\n  }\n\n  return Object.keys(trad).reduce((acc: Translations, current: string) => {\n    acc[`${pluginId}.${current}`] = trad[current];\n\n    return acc;\n  }, {});\n};\n", "import { Images } from '@strapi/icons';\n\nimport pluginPkg from '../../package.json';\n\nimport { MediaLibraryDialog } from './components/MediaLibraryDialog/MediaLibraryDialog';\nimport { MediaLibraryInput } from './components/MediaLibraryInput/MediaLibraryInput';\nimport { PERMISSIONS } from './constants';\nimport { pluginId } from './pluginId';\nimport { getTrad, prefixPluginTranslations } from './utils';\n\nimport type { MediaLibraryDialogProps } from './components/MediaLibraryDialog/MediaLibraryDialog';\nimport type { MediaLibraryInputProps } from './components/MediaLibraryInput/MediaLibraryInput';\nimport type { StrapiApp } from '@strapi/admin/strapi-admin';\nimport type { Plugin } from '@strapi/types';\n\nconst name = pluginPkg.strapi.name;\n\nconst admin: Plugin.Config.AdminInput = {\n  register(app: StrapiApp) {\n    app.addMenuLink({\n      to: `plugins/${pluginId}`,\n      icon: Images,\n      intlLabel: {\n        id: `${pluginId}.plugin.name`,\n        defaultMessage: 'Media Library',\n      },\n      permissions: PERMISSIONS.main,\n      Component: () => import('./pages/App/App').then((mod) => ({ default: mod.Upload })),\n      position: 4,\n    });\n\n    app.addSettingsLink('global', {\n      id: 'media-library-settings',\n      to: 'media-library',\n      intlLabel: {\n        id: getTrad('plugin.name'),\n        defaultMessage: 'Media Library',\n      },\n      async Component() {\n        const { ProtectedSettingsPage } = await import('./pages/SettingsPage/SettingsPage');\n        return { default: ProtectedSettingsPage };\n      },\n      permissions: PERMISSIONS.settings,\n    });\n\n    app.addFields({\n      type: 'media',\n      Component: MediaLibraryInput as React.FC<Partial<MediaLibraryInputProps>>,\n    });\n    app.addComponents([\n      {\n        name: 'media-library',\n        Component: MediaLibraryDialog as React.FC<Partial<MediaLibraryDialogProps>>,\n      },\n    ]);\n\n    app.registerPlugin({\n      id: pluginId,\n      name,\n    });\n  },\n  async registerTrads({ locales }: { locales: string[] }) {\n    const importedTrads = await Promise.all(\n      locales.map((locale) => {\n        return import(`./translations/${locale}.json`)\n          .then(({ default: data }) => {\n            return {\n              data: prefixPluginTranslations(data, pluginId),\n              locale,\n            };\n          })\n          .catch(() => {\n            return {\n              data: {},\n              locale,\n            };\n          });\n      })\n    );\n\n    return Promise.resolve(importedTrads);\n  },\n};\n\n// eslint-disable-next-line import/no-default-export\nexport default admin;\n", "import * as React from 'react';\n\nimport { Button, Checkbox, Dialog, Field, Flex, Typography } from '@strapi/design-system';\nimport { WarningCircle } from '@strapi/icons';\nimport { MessageDescriptor, useIntl } from 'react-intl';\nimport { styled } from 'styled-components';\n\nimport { getTranslation } from '../utils/getTranslation';\n\nconst TextAlignTypography = styled(Typography)`\n  text-align: center;\n`;\n\ninterface IntlMessage extends MessageDescriptor {\n  values: object;\n}\n\ninterface CheckboxConfirmationProps {\n  description: IntlMessage;\n  intlLabel: IntlMessage;\n  isCreating?: boolean;\n  name: string;\n  onChange: (event: { target: { name: string; value: boolean; type: string } }) => void;\n  value: boolean;\n}\n\nconst CheckboxConfirmation = ({\n  description,\n  isCreating = false,\n  intlLabel,\n  name,\n  onChange,\n  value,\n}: CheckboxConfirmationProps) => {\n  const { formatMessage } = useIntl();\n  const [isOpen, setIsOpen] = React.useState(false);\n\n  const handleChange = (value: boolean) => {\n    if (isCreating || value) {\n      return onChange({ target: { name, value, type: 'checkbox' } });\n    }\n\n    if (!value) {\n      return setIsOpen(true);\n    }\n\n    return null;\n  };\n\n  const handleConfirm = () => {\n    onChange({ target: { name, value: false, type: 'checkbox' } });\n  };\n\n  const label = intlLabel.id\n    ? formatMessage(\n        { id: intlLabel.id, defaultMessage: intlLabel.defaultMessage },\n        { ...intlLabel.values }\n      )\n    : name;\n\n  const hint = description\n    ? formatMessage(\n        { id: description.id, defaultMessage: description.defaultMessage },\n        { ...description.values }\n      )\n    : '';\n\n  return (\n    <Dialog.Root open={isOpen} onOpenChange={setIsOpen}>\n      <Field.Root hint={hint} name={name}>\n        <Checkbox onCheckedChange={handleChange} checked={value}>\n          {label}\n        </Checkbox>\n        <Field.Hint />\n      </Field.Root>\n      <Dialog.Content>\n        <Dialog.Header>\n          {formatMessage({\n            id: getTranslation('CheckboxConfirmation.Modal.title'),\n            defaultMessage: 'Disable localization',\n          })}\n        </Dialog.Header>\n        <Dialog.Body icon={<WarningCircle />}>\n          <Flex direction=\"column\" alignItems=\"stretch\" gap={2}>\n            <Flex justifyContent=\"center\">\n              <TextAlignTypography>\n                {formatMessage({\n                  id: getTranslation('CheckboxConfirmation.Modal.content'),\n                  defaultMessage:\n                    'Disabling localization will engender the deletion of all your content but the one associated to your default locale (if existing).',\n                })}\n              </TextAlignTypography>\n            </Flex>\n            <Flex justifyContent=\"center\">\n              <Typography fontWeight=\"semiBold\">\n                {formatMessage({\n                  id: getTranslation('CheckboxConfirmation.Modal.body'),\n                  defaultMessage: 'Do you want to disable it?',\n                })}\n              </Typography>\n            </Flex>\n          </Flex>\n        </Dialog.Body>\n        <Dialog.Footer>\n          <Dialog.Cancel>\n            <Button variant=\"tertiary\">\n              {formatMessage({\n                id: 'components.popUpWarning.button.cancel',\n                defaultMessage: 'No, cancel',\n              })}\n            </Button>\n          </Dialog.Cancel>\n          <Dialog.Action>\n            <Button variant=\"danger-light\" onClick={handleConfirm}>\n              {formatMessage({\n                id: getTranslation('CheckboxConfirmation.Modal.button-confirm'),\n                defaultMessage: 'Yes, disable',\n              })}\n            </Button>\n          </Dialog.Action>\n        </Dialog.Footer>\n      </Dialog.Content>\n    </Dialog.Root>\n  );\n};\n\nexport { CheckboxConfirmation };\n", "import has from 'lodash/has';\n\nconst LOCALIZED_FIELDS = [\n  'biginteger',\n  'boolean',\n  'component',\n  'date',\n  'datetime',\n  'decimal',\n  'dynamiczone',\n  'email',\n  'enumeration',\n  'float',\n  'integer',\n  'json',\n  'media',\n  'number',\n  'password',\n  'richtext',\n  'blocks',\n  'string',\n  'text',\n  'time',\n];\n\nconst doesPluginOptionsHaveI18nLocalized = (\n  opts?: object\n): opts is { i18n: { localized: boolean } } => has(opts, ['i18n', 'localized']);\n\nexport { LOCALIZED_FIELDS, doesPluginOptionsHaveI18nLocalized };\n", "const capitalize = (str: string) => str.charAt(0).toUpperCase() + str.slice(1);\n\nexport { capitalize };\n", "import * as React from 'react';\n\nimport { useAuth } from '@strapi/admin/strapi-admin';\nimport { unstable_useDocument as useDocument } from '@strapi/content-manager/strapi-admin';\nimport { useParams } from 'react-router-dom';\n\nimport { doesPluginOptionsHaveI18nLocalized } from '../utils/fields';\nimport { capitalize } from '../utils/strings';\n\ntype UseI18n = () => {\n  hasI18n: boolean;\n  canCreate: string[];\n  canRead: string[];\n  canUpdate: string[];\n  canDelete: string[];\n  canPublish: string[];\n};\n\n/**\n * @alpha\n * @description This hook is used to get the i18n status of a content type.\n * Also returns the CRUDP permission locale properties for the content type\n * so we know which locales the user can perform actions on.\n */\nconst useI18n: UseI18n = () => {\n  // Extract the params from the URL to pass to our useDocument hook\n  const params = useParams<{ collectionType: string; slug: string; model: string }>();\n\n  const userPermissions = useAuth('useI18n', (state) => state.permissions);\n  const actions = React.useMemo(() => {\n    const permissions = userPermissions.filter((permission) => permission.subject === params.slug);\n\n    return permissions.reduce<Omit<ReturnType<UseI18n>, 'hasI18n'>>(\n      (acc, permission) => {\n        const [actionShorthand] = permission.action.split('.').slice(-1);\n\n        return {\n          ...acc,\n          [`can${capitalize(actionShorthand)}`]: permission.properties?.locales ?? [],\n        };\n      },\n      { canCreate: [], canRead: [], canUpdate: [], canDelete: [], canPublish: [] }\n    );\n  }, [params.slug, userPermissions]);\n\n  // TODO: use specific hook to get schema only\n  const { schema } = useDocument(\n    {\n      // We can non-null assert these because below we skip the query if they are not present\n      collectionType: params.collectionType!,\n      model: params.slug!,\n    },\n    {\n      skip: true,\n    }\n  );\n\n  if (doesPluginOptionsHaveI18nLocalized(schema?.pluginOptions)) {\n    return {\n      hasI18n: schema.pluginOptions.i18n.localized,\n      ...actions,\n    };\n  }\n\n  return {\n    hasI18n: false,\n    ...actions,\n  };\n};\n\nexport { useI18n };\n", "import { i18nApi } from './api';\n\nimport type { CountManyEntriesDraftRelations } from '../../../shared/contracts/content-manager';\n\nconst relationsApi = i18nApi.injectEndpoints({\n  overrideExisting: true,\n  endpoints: (builder) => ({\n    getManyDraftRelationCount: builder.query<\n      CountManyEntriesDraftRelations.Response['data'],\n      CountManyEntriesDraftRelations.Request['query'] & {\n        model: string;\n      }\n    >({\n      query: ({ model, ...params }) => ({\n        url: `/content-manager/collection-types/${model}/actions/countManyEntriesDraftRelations`,\n        method: 'GET',\n        config: {\n          params,\n        },\n      }),\n      transformResponse: (response: CountManyEntriesDraftRelations.Response) => response.data,\n    }),\n  }),\n});\n\nconst { useGetManyDraftRelationCountQuery } = relationsApi;\n\nexport { useGetManyDraftRelationCountQuery };\n", "import type { Schema } from '@strapi/types';\n\ntype Data = Record<keyof Schema.ContentType['attributes'], any>;\n\nconst cleanData = (\n  data: Data,\n  schema: Schema.ContentType,\n  components: Record<string, Schema.Component>\n) => {\n  const cleanedData = removeFields(data, [\n    'createdAt',\n    'createdBy',\n    'updatedAt',\n    'updatedBy',\n    'id',\n    'documentId',\n    'publishedAt',\n    'strapi_stage',\n    'strapi_assignee',\n    'locale',\n    'status',\n  ]);\n\n  const cleanedDataWithoutPasswordAndRelation = recursiveRemoveFieldTypes(\n    cleanedData,\n    schema,\n    components,\n    ['relation', 'password']\n  );\n\n  return cleanedDataWithoutPasswordAndRelation;\n};\n\nconst removeFields = (data: Data, fields: Array<keyof Schema.ContentType['attributes']>) => {\n  return Object.keys(data).reduce((acc, current) => {\n    if (fields.includes(current)) {\n      return acc;\n    }\n    acc[current] = data[current];\n    return acc;\n  }, {} as Data);\n};\n\nconst recursiveRemoveFieldTypes = (\n  data: Data,\n  schema: Schema.Schema,\n  components: Record<string, Schema.Component>,\n  fields: Array<keyof Schema.ContentType['attributes']>\n) => {\n  return Object.keys(data).reduce((acc, current) => {\n    const attribute = schema.attributes[current] ?? { type: undefined };\n\n    if (fields.includes(attribute.type)) {\n      return acc;\n    }\n\n    if (attribute.type === 'dynamiczone') {\n      acc[current] = data[current].map((componentValue: any, index: number) => {\n        const { id: _, ...rest } = recursiveRemoveFieldTypes(\n          componentValue,\n          components[componentValue.__component],\n          components,\n          fields\n        );\n\n        return {\n          ...rest,\n          __temp_key__: index + 1,\n        };\n      });\n    } else if (attribute.type === 'component') {\n      const { repeatable, component } = attribute;\n\n      if (repeatable) {\n        acc[current] = (data[current] ?? []).map((compoData: any, index: number) => {\n          const { id: _, ...rest } = recursiveRemoveFieldTypes(\n            compoData,\n            components[component],\n            components,\n            fields\n          );\n\n          return {\n            ...rest,\n            __temp_key__: index + 1,\n          };\n        });\n      } else {\n        const { id: _, ...rest } = recursiveRemoveFieldTypes(\n          data[current] ?? {},\n          components[component],\n          components,\n          fields\n        );\n\n        acc[current] = rest;\n      }\n    } else {\n      acc[current] = data[current];\n    }\n\n    return acc;\n  }, {} as any);\n};\n\nexport { cleanData };\n", "import * as React from 'react';\n\nimport { FormErrors, Table, useTable } from '@strapi/admin/strapi-admin';\nimport { Box, Typography, IconButton, Flex, Tooltip, Status, Modal } from '@strapi/design-system';\nimport { Pencil, CheckCircle, CrossCircle, ArrowsCounterClockwise } from '@strapi/icons';\nimport { stringify } from 'qs';\nimport { type MessageDescriptor, useIntl, PrimitiveType } from 'react-intl';\nimport { Link } from 'react-router-dom';\n\nimport { Locale } from '../../../shared/contracts/locales';\nimport { getTranslation } from '../utils/getTranslation';\nimport { capitalize } from '../utils/strings';\n\nimport { LocaleStatus } from './CMHeaderActions';\n\nimport type { Modules } from '@strapi/types';\n\ntype Status = Modules.Documents.Params.PublicationStatus.Kind | 'modified';\n\n/* -------------------------------------------------------------------------------------------------\n * EntryValidationText\n * -----------------------------------------------------------------------------------------------*/\n\ninterface EntryValidationTextProps {\n  status: Status;\n  validationErrors: FormErrors[string] | null;\n  action: 'bulk-publish' | 'bulk-unpublish';\n}\n\ninterface TranslationMessage extends MessageDescriptor {\n  values?: Record<string, PrimitiveType>;\n}\n\nconst isErrorMessageDescriptor = (object?: string | object): object is TranslationMessage => {\n  return (\n    typeof object === 'object' && object !== null && 'id' in object && 'defaultMessage' in object\n  );\n};\n\nconst EntryValidationText = ({\n  status = 'draft',\n  validationErrors,\n  action,\n}: EntryValidationTextProps) => {\n  const { formatMessage } = useIntl();\n\n  /**\n   * TODO: Should this be extracted an made into a factory to recursively get\n   * error messages??\n   */\n  const getErrorStr = (key: string, value?: FormErrors[string]): string => {\n    if (typeof value === 'string') {\n      return `${key}: ${value}`;\n    } else if (isErrorMessageDescriptor(value)) {\n      return `${key}: ${formatMessage(value)}`;\n    } else if (Array.isArray(value)) {\n      return value.map((v) => getErrorStr(key, v)).join(' ');\n    } else if (typeof value === 'object' && !Array.isArray(value)) {\n      return Object.entries(value)\n        .map(([k, v]) => getErrorStr(k, v))\n        .join(' ');\n    } else {\n      /**\n       * unlikely to happen, but we need to return something\n       */\n      return '';\n    }\n  };\n\n  if (validationErrors) {\n    const validationErrorsMessages = Object.entries(validationErrors)\n      .map(([key, value]) => {\n        return getErrorStr(key, value);\n      })\n      .join(' ');\n\n    return (\n      <Flex gap={2}>\n        <CrossCircle fill=\"danger600\" />\n        <Tooltip label={validationErrorsMessages}>\n          <Typography\n            maxWidth={'30rem'}\n            textColor=\"danger600\"\n            variant=\"omega\"\n            fontWeight=\"semiBold\"\n            ellipsis\n          >\n            {validationErrorsMessages}\n          </Typography>\n        </Tooltip>\n      </Flex>\n    );\n  }\n\n  const getStatusMessage = () => {\n    if (action === 'bulk-publish') {\n      if (status === 'published') {\n        return {\n          icon: <CheckCircle fill=\"success600\" />,\n          text: formatMessage({\n            id: 'content-manager.bulk-publish.already-published',\n            defaultMessage: 'Already Published',\n          }),\n          textColor: 'success600',\n          fontWeight: 'bold',\n        };\n      } else if (status === 'modified') {\n        return {\n          icon: <ArrowsCounterClockwise fill=\"alternative600\" />,\n          text: formatMessage({\n            id: 'app.utils.ready-to-publish-changes',\n            defaultMessage: 'Ready to publish changes',\n          }),\n        };\n      } else {\n        return {\n          icon: <CheckCircle fill=\"success600\" />,\n          text: formatMessage({\n            id: 'app.utils.ready-to-publish',\n            defaultMessage: 'Ready to publish',\n          }),\n        };\n      }\n    } else {\n      if (status === 'draft') {\n        return {\n          icon: <CheckCircle fill=\"success600\" />,\n          text: formatMessage({\n            id: 'content-manager.bulk-unpublish.already-unpublished',\n            defaultMessage: 'Already Unpublished',\n          }),\n          textColor: 'success600',\n          fontWeight: 'bold',\n        };\n      } else {\n        return {\n          icon: <CheckCircle fill=\"success600\" />,\n          text: formatMessage({\n            id: 'app.utils.ready-to-unpublish-changes',\n            defaultMessage: 'Ready to unpublish',\n          }),\n          textColor: 'success600',\n          fontWeight: 'bold',\n        };\n      }\n    }\n  };\n\n  const { icon, text, textColor = 'success600', fontWeight = 'normal' } = getStatusMessage();\n\n  return (\n    <Flex gap={2}>\n      {icon}\n      <Typography textColor={textColor} fontWeight={fontWeight}>\n        {text}\n      </Typography>\n    </Flex>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * BoldChunk\n * -----------------------------------------------------------------------------------------------*/\n\nconst BoldChunk = (chunks: React.ReactNode) => <Typography fontWeight=\"bold\">{chunks}</Typography>;\n\n/* -------------------------------------------------------------------------------------------------\n * BulkLocaleActionModal\n * -----------------------------------------------------------------------------------------------*/\n\ninterface BulkLocaleActionModalProps {\n  rows: LocaleStatus[];\n  headers: {\n    label: string;\n    name: string;\n  }[];\n  localesMetadata: Locale[];\n  validationErrors?: FormErrors;\n  action: 'bulk-publish' | 'bulk-unpublish';\n}\n\nconst BulkLocaleActionModal = ({\n  headers,\n  rows,\n  localesMetadata,\n  validationErrors = {},\n  action,\n}: BulkLocaleActionModalProps) => {\n  const { formatMessage } = useIntl();\n\n  const selectedRows = useTable<LocaleStatus[]>(\n    'BulkLocaleActionModal',\n    (state) => state.selectedRows\n  );\n\n  const getFormattedCountMessage = () => {\n    const currentStatusByLocale = rows.reduce<Record<string, string>>((acc, { locale, status }) => {\n      acc[locale] = status;\n      return acc;\n    }, {});\n    const localesWithErrors = Object.keys(validationErrors);\n\n    const publishedCount = selectedRows.filter(\n      ({ locale }) => currentStatusByLocale[locale] === 'published'\n    ).length;\n\n    const draftCount = selectedRows.filter(\n      ({ locale }) =>\n        (currentStatusByLocale[locale] === 'draft' ||\n          currentStatusByLocale[locale] === 'modified') &&\n        !localesWithErrors.includes(locale)\n    ).length;\n\n    const withErrorsCount = localesWithErrors.length;\n    const messageId =\n      action === 'bulk-publish'\n        ? 'content-manager.containers.list.selectedEntriesModal.selectedCount.publish'\n        : 'content-manager.containers.list.selectedEntriesModal.selectedCount.unpublish';\n\n    const defaultMessage =\n      action === 'bulk-publish'\n        ? '<b>{publishedCount}</b> {publishedCount, plural, =0 {entries} one {entry} other {entries}} already published. <b>{draftCount}</b> {draftCount, plural, =0 {entries} one {entry} other {entries}} ready to publish. <b>{withErrorsCount}</b> {withErrorsCount, plural, =0 {entries} one {entry} other {entries}} waiting for action.'\n        : '<b>{draftCount}</b> {draftCount, plural, =0 {entries} one {entry} other {entries}} already unpublished. <b>{publishedCount}</b> {publishedCount, plural, =0 {entries} one {entry} other {entries}} ready to unpublish.';\n\n    return formatMessage(\n      {\n        id: messageId,\n        defaultMessage,\n      },\n      {\n        withErrorsCount,\n        draftCount,\n        publishedCount,\n        b: BoldChunk,\n      }\n    );\n  };\n\n  return (\n    <Modal.Body>\n      <Typography>{getFormattedCountMessage()}</Typography>\n      <Box marginTop={5}>\n        <Table.Content>\n          <Table.Head>\n            <Table.HeaderCheckboxCell />\n            {headers.map((head) => (\n              <Table.HeaderCell key={head.name} {...head} />\n            ))}\n          </Table.Head>\n          <Table.Body>\n            {rows.map(({ locale, status }, index) => {\n              const error = validationErrors?.[locale] ?? null;\n\n              const statusVariant =\n                status === 'draft' ? 'primary' : status === 'published' ? 'success' : 'alternative';\n\n              return (\n                <Table.Row key={index}>\n                  <Table.CheckboxCell id={locale} aria-label={`Select ${locale}`} />\n                  <Table.Cell>\n                    <Typography variant=\"sigma\" textColor=\"neutral600\">\n                      {Array.isArray(localesMetadata)\n                        ? localesMetadata.find((localeEntry) => localeEntry.code === locale)?.name\n                        : locale}\n                    </Typography>\n                  </Table.Cell>\n                  <Table.Cell>\n                    <Box display=\"flex\">\n                      <Status\n                        display=\"flex\"\n                        paddingLeft=\"6px\"\n                        paddingRight=\"6px\"\n                        paddingTop=\"2px\"\n                        paddingBottom=\"2px\"\n                        size={'S'}\n                        variant={statusVariant}\n                      >\n                        <Typography tag=\"span\" variant=\"pi\" fontWeight=\"bold\">\n                          {capitalize(status)}\n                        </Typography>\n                      </Status>\n                    </Box>\n                  </Table.Cell>\n                  <Table.Cell>\n                    <EntryValidationText validationErrors={error} status={status} action={action} />\n                  </Table.Cell>\n                  <Table.Cell>\n                    <IconButton\n                      tag={Link}\n                      to={{\n                        search: stringify({ plugins: { i18n: { locale } } }),\n                      }}\n                      label={formatMessage(\n                        {\n                          id: getTranslation('Settings.list.actions.edit'),\n                          defaultMessage: 'Edit {name} locale',\n                        },\n                        {\n                          name: locale,\n                        }\n                      )}\n                      variant=\"ghost\"\n                    >\n                      <Pencil />\n                    </IconButton>\n                  </Table.Cell>\n                </Table.Row>\n              );\n            })}\n          </Table.Body>\n        </Table.Content>\n      </Box>\n    </Modal.Body>\n  );\n};\n\nexport { BulkLocaleActionModal };\nexport type { BulkLocaleActionModalProps };\n", "import * as React from 'react';\n\nimport { skipToken } from '@reduxjs/toolkit/query';\nimport {\n  useNotification,\n  useQueryParams,\n  Table,\n  useAPIError<PERSON>andler,\n  FormErrors,\n  useForm,\n} from '@strapi/admin/strapi-admin';\nimport {\n  type DocumentActionComponent,\n  type DocumentActionProps,\n  unstable_useDocument as useDocument,\n  unstable_useDocumentActions as useDocumentActions,\n  buildValidParams,\n  HeaderActionProps,\n} from '@strapi/content-manager/strapi-admin';\nimport {\n  Flex,\n  Status,\n  Typography,\n  Button,\n  Modal,\n  Field,\n  SingleSelect,\n  SingleSelectOption,\n  Dialog,\n  type StatusVariant,\n} from '@strapi/design-system';\nimport { WarningCircle, ListPlus, Trash, Earth, Cross, Plus } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\nimport { useNavigate } from 'react-router-dom';\nimport { styled } from 'styled-components';\n\nimport { useI18n } from '../hooks/useI18n';\nimport { useGetLocalesQuery } from '../services/locales';\nimport { useGetManyDraftRelationCountQuery } from '../services/relations';\nimport { cleanData } from '../utils/clean';\nimport { getTranslation } from '../utils/getTranslation';\nimport { capitalize } from '../utils/strings';\n\nimport { BulkLocaleActionModal } from './BulkLocaleActionModal';\n\nimport type { Locale } from '../../../shared/contracts/locales';\nimport type { I18nBaseQuery } from '../types';\nimport type { Modules } from '@strapi/types';\n\n/* -------------------------------------------------------------------------------------------------\n * LocalePickerAction\n * -----------------------------------------------------------------------------------------------*/\n\ninterface LocaleOptionProps {\n  isDraftAndPublishEnabled: boolean;\n  locale: Locale;\n  status: 'draft' | 'published' | 'modified';\n  entryExists: boolean;\n}\n\nconst statusVariants: Record<LocaleOptionProps['status'], StatusVariant> = {\n  draft: 'secondary',\n  published: 'success',\n  modified: 'alternative',\n};\n\nconst LocaleOption = ({\n  isDraftAndPublishEnabled,\n  locale,\n  status,\n  entryExists,\n}: LocaleOptionProps) => {\n  const { formatMessage } = useIntl();\n\n  if (!entryExists) {\n    return formatMessage(\n      {\n        id: getTranslation('CMEditViewLocalePicker.locale.create'),\n        defaultMessage: 'Create <bold>{locale}</bold> locale',\n      },\n      {\n        bold: (locale: React.ReactNode) => <b>{locale}</b>,\n        locale: locale.name,\n      }\n    );\n  }\n\n  return (\n    <Flex width=\"100%\" gap={1} justifyContent=\"space-between\">\n      <Typography>{locale.name}</Typography>\n      {isDraftAndPublishEnabled ? (\n        <Status\n          display=\"flex\"\n          paddingLeft=\"6px\"\n          paddingRight=\"6px\"\n          paddingTop=\"2px\"\n          paddingBottom=\"2px\"\n          size=\"S\"\n          variant={statusVariants[status]}\n        >\n          <Typography tag=\"span\" variant=\"pi\" fontWeight=\"bold\">\n            {capitalize(status)}\n          </Typography>\n        </Status>\n      ) : null}\n    </Flex>\n  );\n};\n\nconst LocalePickerAction = ({\n  document,\n  meta,\n  model,\n  collectionType,\n  documentId,\n}: HeaderActionProps) => {\n  const { formatMessage } = useIntl();\n  const [{ query }, setQuery] = useQueryParams<I18nBaseQuery>();\n  const { hasI18n, canCreate, canRead } = useI18n();\n  const { data: locales = [] } = useGetLocalesQuery();\n  const currentDesiredLocale = query.plugins?.i18n?.locale;\n  const { schema } = useDocument({\n    model,\n    collectionType,\n    documentId,\n    params: { locale: currentDesiredLocale },\n  });\n\n  const handleSelect = React.useCallback(\n    (value: string) => {\n      setQuery({\n        plugins: {\n          ...query.plugins,\n          i18n: {\n            locale: value,\n          },\n        },\n      });\n    },\n    [query.plugins, setQuery]\n  );\n\n  React.useEffect(() => {\n    if (!Array.isArray(locales) || !hasI18n) {\n      return;\n    }\n    /**\n     * Handle the case where the current locale query param doesn't exist\n     * in the list of available locales, so we redirect to the default locale.\n     */\n    const doesLocaleExist = locales.find((loc) => loc.code === currentDesiredLocale);\n    const defaultLocale = locales.find((locale) => locale.isDefault);\n    if (!doesLocaleExist && defaultLocale?.code) {\n      handleSelect(defaultLocale.code);\n    }\n  }, [handleSelect, hasI18n, locales, currentDesiredLocale]);\n\n  const currentLocale = Array.isArray(locales)\n    ? locales.find((locale) => locale.code === currentDesiredLocale)\n    : undefined;\n\n  const allCurrentLocales = [\n    { status: getDocumentStatus(document, meta), locale: currentLocale?.code },\n    ...(document?.localizations ?? []),\n  ];\n\n  if (!hasI18n || !Array.isArray(locales) || locales.length === 0) {\n    return null;\n  }\n\n  const displayedLocales = locales.filter((locale) => {\n    /**\n     * If you can read we allow you to see the locale exists\n     * otherwise the locale is hidden.\n     */\n    return canRead.includes(locale.code);\n  });\n\n  return {\n    label: formatMessage({\n      id: getTranslation('Settings.locales.modal.locales.label'),\n      defaultMessage: 'Locales',\n    }),\n    options: displayedLocales.map((locale) => {\n      const entryWithLocaleExists = allCurrentLocales.some((doc) => doc.locale === locale.code);\n\n      const currentLocaleDoc = allCurrentLocales.find((doc) =>\n        'locale' in doc ? doc.locale === locale.code : false\n      );\n\n      const permissionsToCheck = currentLocaleDoc ? canRead : canCreate;\n\n      return {\n        disabled: !permissionsToCheck.includes(locale.code),\n        value: locale.code,\n        label: (\n          <LocaleOption\n            isDraftAndPublishEnabled={!!schema?.options?.draftAndPublish}\n            locale={locale}\n            status={currentLocaleDoc?.status}\n            entryExists={entryWithLocaleExists}\n          />\n        ),\n        startIcon: !entryWithLocaleExists ? <Plus /> : null,\n      };\n    }),\n    customizeContent: () => currentLocale?.name,\n    onSelect: handleSelect,\n    value: currentLocale,\n  };\n};\n\ntype UseDocument = typeof useDocument;\n\nconst getDocumentStatus = (\n  document: ReturnType<UseDocument>['document'],\n  meta: ReturnType<UseDocument>['meta']\n): 'draft' | 'published' | 'modified' => {\n  const docStatus = document?.status;\n  const statuses = meta?.availableStatus ?? [];\n\n  /**\n   * Creating an entry\n   */\n  if (!docStatus) {\n    return 'draft';\n  }\n\n  /**\n   * We're viewing a draft, but the document could have a published version\n   */\n  if (docStatus === 'draft' && statuses.find((doc) => doc.publishedAt !== null)) {\n    return 'published';\n  }\n\n  return docStatus;\n};\n\n/* -------------------------------------------------------------------------------------------------\n * FillFromAnotherLocaleAction\n * -----------------------------------------------------------------------------------------------*/\n\nconst FillFromAnotherLocaleAction = ({\n  documentId,\n  meta,\n  model,\n  collectionType,\n}: HeaderActionProps) => {\n  const { formatMessage } = useIntl();\n  const [{ query }] = useQueryParams<I18nBaseQuery>();\n  const { hasI18n } = useI18n();\n  const currentDesiredLocale = query.plugins?.i18n?.locale;\n  const [localeSelected, setLocaleSelected] = React.useState<string | null>(null);\n  const setValues = useForm('FillFromAnotherLocale', (state) => state.setValues);\n\n  const { getDocument } = useDocumentActions();\n  const { schema, components } = useDocument({\n    model,\n    documentId,\n    collectionType,\n    params: { locale: currentDesiredLocale },\n  });\n  const { data: locales = [] } = useGetLocalesQuery();\n\n  const availableLocales = Array.isArray(locales)\n    ? locales.filter((locale) => meta?.availableLocales.some((l) => l.locale === locale.code))\n    : [];\n\n  const fillFromLocale = (onClose: () => void) => async () => {\n    const response = await getDocument({\n      collectionType,\n      model,\n      documentId,\n      params: { locale: localeSelected },\n    });\n    if (!response || !schema) {\n      return;\n    }\n\n    const { data } = response;\n\n    const cleanedData = cleanData(data, schema, components);\n\n    setValues(cleanedData);\n\n    onClose();\n  };\n\n  if (!hasI18n) {\n    return null;\n  }\n\n  return {\n    type: 'icon',\n    icon: <Earth />,\n    disabled: availableLocales.length === 0,\n    label: formatMessage({\n      id: getTranslation('CMEditViewCopyLocale.copy-text'),\n      defaultMessage: 'Fill in from another locale',\n    }),\n    dialog: {\n      type: 'dialog',\n      title: formatMessage({\n        id: getTranslation('CMEditViewCopyLocale.dialog.title'),\n        defaultMessage: 'Confirmation',\n      }),\n      content: ({ onClose }: { onClose: () => void }) => (\n        <>\n          <Dialog.Body>\n            <Flex direction=\"column\" gap={3}>\n              <WarningCircle width=\"24px\" height=\"24px\" fill=\"danger600\" />\n              <Typography textAlign=\"center\">\n                {formatMessage({\n                  id: getTranslation('CMEditViewCopyLocale.dialog.body'),\n                  defaultMessage:\n                    'Your current content will be erased and filled by the content of the selected locale:',\n                })}\n              </Typography>\n              <Field.Root width=\"100%\">\n                <Field.Label>\n                  {formatMessage({\n                    id: getTranslation('CMEditViewCopyLocale.dialog.field.label'),\n                    defaultMessage: 'Locale',\n                  })}\n                </Field.Label>\n                <SingleSelect\n                  value={localeSelected}\n                  placeholder={formatMessage({\n                    id: getTranslation('CMEditViewCopyLocale.dialog.field.placeholder'),\n                    defaultMessage: 'Select one locale...',\n                  })}\n                  // @ts-expect-error – the DS will handle numbers, but we're not allowing the API.\n                  onChange={(value) => setLocaleSelected(value)}\n                >\n                  {availableLocales.map((locale) => (\n                    <SingleSelectOption key={locale.code} value={locale.code}>\n                      {locale.name}\n                    </SingleSelectOption>\n                  ))}\n                </SingleSelect>\n              </Field.Root>\n            </Flex>\n          </Dialog.Body>\n          <Dialog.Footer>\n            <Flex gap={2} width=\"100%\">\n              <Button flex=\"auto\" variant=\"tertiary\" onClick={onClose}>\n                {formatMessage({\n                  id: getTranslation('CMEditViewCopyLocale.cancel-text'),\n                  defaultMessage: 'No, cancel',\n                })}\n              </Button>\n              <Button flex=\"auto\" variant=\"success\" onClick={fillFromLocale(onClose)}>\n                {formatMessage({\n                  id: getTranslation('CMEditViewCopyLocale.submit-text'),\n                  defaultMessage: 'Yes, fill in',\n                })}\n              </Button>\n            </Flex>\n          </Dialog.Footer>\n        </>\n      ),\n    },\n  };\n};\n\n/* -------------------------------------------------------------------------------------------------\n * DeleteLocaleAction\n * -----------------------------------------------------------------------------------------------*/\n\nconst DeleteLocaleAction: DocumentActionComponent = ({\n  document,\n  documentId,\n  model,\n  collectionType,\n}) => {\n  const { formatMessage } = useIntl();\n  const navigate = useNavigate();\n  const { toggleNotification } = useNotification();\n  const { delete: deleteAction, isLoading } = useDocumentActions();\n  const { hasI18n, canDelete } = useI18n();\n\n  // Get the current locale object, using the URL instead of document so it works while creating\n  const [{ query }] = useQueryParams<I18nBaseQuery>();\n  const { data: locales = [] } = useGetLocalesQuery();\n  const currentDesiredLocale = query.plugins?.i18n?.locale;\n  const locale = !('error' in locales) && locales.find((loc) => loc.code === currentDesiredLocale);\n\n  if (!hasI18n) {\n    return null;\n  }\n\n  return {\n    disabled:\n      (document?.locale && !canDelete.includes(document.locale)) || !document || !document.id,\n    position: ['header', 'table-row'],\n    label: formatMessage(\n      {\n        id: getTranslation('actions.delete.label'),\n        defaultMessage: 'Delete entry ({locale})',\n      },\n      { locale: locale && locale.name }\n    ),\n    icon: <StyledTrash />,\n    variant: 'danger',\n    dialog: {\n      type: 'dialog',\n      title: formatMessage({\n        id: getTranslation('actions.delete.dialog.title'),\n        defaultMessage: 'Confirmation',\n      }),\n      content: (\n        <Flex direction=\"column\" gap={2}>\n          <WarningCircle width=\"24px\" height=\"24px\" fill=\"danger600\" />\n          <Typography tag=\"p\" variant=\"omega\" textAlign=\"center\">\n            {formatMessage({\n              id: getTranslation('actions.delete.dialog.body'),\n              defaultMessage: 'Are you sure?',\n            })}\n          </Typography>\n        </Flex>\n      ),\n      loading: isLoading,\n      onConfirm: async () => {\n        const unableToDelete =\n          // We are unable to delete a collection type without a document ID\n          // & unable to delete generally if there is no document locale\n          (collectionType !== 'single-types' && !documentId) || !document?.locale;\n\n        if (unableToDelete) {\n          console.error(\n            \"You're trying to delete a document without an id or locale, this is likely a bug with Strapi. Please open an issue.\"\n          );\n\n          toggleNotification({\n            message: formatMessage({\n              id: getTranslation('actions.delete.error'),\n              defaultMessage: 'An error occurred while trying to delete the document locale.',\n            }),\n            type: 'danger',\n          });\n\n          return;\n        }\n\n        const res = await deleteAction({\n          documentId,\n          model,\n          collectionType,\n          params: { locale: document.locale },\n        });\n\n        if (!('error' in res)) {\n          navigate({ pathname: `../${collectionType}/${model}` }, { replace: true });\n        }\n      },\n    },\n  };\n};\n\nexport type LocaleStatus = {\n  locale: string;\n  status: Modules.Documents.Params.PublicationStatus.Kind | 'modified';\n};\n\ninterface ExtendedDocumentActionProps extends DocumentActionProps {\n  action?: 'bulk-publish' | 'bulk-unpublish';\n}\n\n/* -------------------------------------------------------------------------------------------------\n * BulkLocaleAction\n *\n * This component is used to handle bulk publish and unpublish actions on locales.\n * -----------------------------------------------------------------------------------------------*/\n\nconst BulkLocaleAction: DocumentActionComponent = ({\n  document,\n  documentId,\n  model,\n  collectionType,\n  action,\n}: ExtendedDocumentActionProps) => {\n  const locale = document?.locale ?? null;\n  const [{ query }] = useQueryParams<{ status: 'draft' | 'published' }>();\n\n  const params = React.useMemo(() => buildValidParams(query), [query]);\n  const isOnPublishedTab = query.status === 'published';\n\n  const { formatMessage } = useIntl();\n  const { hasI18n, canPublish } = useI18n();\n  const { toggleNotification } = useNotification();\n  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();\n\n  const [selectedRows, setSelectedRows] = React.useState<any[]>([]);\n  const [isDraftRelationConfirmationOpen, setIsDraftRelationConfirmationOpen] =\n    React.useState<boolean>(false);\n\n  const { publishMany: publishManyAction, unpublishMany: unpublishManyAction } =\n    useDocumentActions();\n\n  const { schema, validate } = useDocument(\n    {\n      model,\n      collectionType,\n      documentId,\n      params: {\n        locale,\n      },\n    },\n    {\n      // No need to fetch the document, the data is already available in the `document` prop\n      skip: true,\n    }\n  );\n\n  const { data: localesMetadata = [] } = useGetLocalesQuery(hasI18n ? undefined : skipToken);\n\n  const headers = [\n    {\n      label: formatMessage({\n        id: 'global.name',\n        defaultMessage: 'Name',\n      }),\n      name: 'name',\n    },\n    {\n      label: formatMessage({\n        id: getTranslation('CMEditViewBulkLocale.status'),\n        defaultMessage: 'Status',\n      }),\n      name: 'status',\n    },\n    {\n      label: formatMessage({\n        id: getTranslation('CMEditViewBulkLocale.publication-status'),\n        defaultMessage: 'Publication Status',\n      }),\n      name: 'publication-status',\n    },\n  ];\n\n  // Extract the rows for the bulk locale publish modal and any validation\n  // errors per locale\n  const [rows, validationErrors] = React.useMemo(() => {\n    if (!document) {\n      return [[], {}];\n    }\n\n    const localizations = document.localizations ?? [];\n\n    // Build the rows for the bulk locale publish modal by combining the current\n    // document with all the available locales from the document meta\n    const locales: LocaleStatus[] = localizations.map((doc: any) => {\n      const { locale, status } = doc;\n      return { locale, status };\n    });\n\n    // Add the current document locale\n    locales.unshift({\n      locale: document.locale,\n      status: document.status,\n    });\n\n    // Build the validation errors for each locale.\n    const allDocuments = [document, ...localizations];\n    const errors = allDocuments.reduce<FormErrors>((errs, document) => {\n      if (!document) {\n        return errs;\n      }\n\n      // Validate each locale entry via the useDocument validate function and store any errors in a dictionary\n      const validation = validate(document as Modules.Documents.AnyDocument);\n      if (validation !== null) {\n        errs[document.locale] = validation;\n      }\n      return errs;\n    }, {});\n\n    return [locales, errors];\n  }, [document, validate]);\n\n  const isBulkPublish = action === 'bulk-publish';\n  const localesForAction = selectedRows.reduce((acc: string[], selectedRow: LocaleStatus) => {\n    const isValidLocale =\n      // Validation errors are irrelevant if we are trying to unpublish\n      !isBulkPublish || !Object.keys(validationErrors).includes(selectedRow.locale);\n\n    const shouldAddLocale = isBulkPublish\n      ? selectedRow.status !== 'published' && isValidLocale\n      : selectedRow.status !== 'draft' && isValidLocale;\n\n    if (shouldAddLocale) {\n      acc.push(selectedRow.locale);\n    }\n\n    return acc;\n  }, []);\n\n  // TODO skipping this for now as there is a bug with the draft relation count that will be worked on separately\n  // see https://www.notion.so/strapi/Count-draft-relations-56901b492efb45ab90d42fe975b32bd8?pvs=4\n  const enableDraftRelationsCount = false;\n  const {\n    data: draftRelationsCount = 0,\n    isLoading: isDraftRelationsLoading,\n    error: isDraftRelationsError,\n  } = useGetManyDraftRelationCountQuery(\n    {\n      model,\n      documentIds: [documentId!],\n      locale: localesForAction,\n    },\n    {\n      skip: !enableDraftRelationsCount || !documentId || localesForAction.length === 0,\n    }\n  );\n\n  React.useEffect(() => {\n    if (isDraftRelationsError) {\n      toggleNotification({\n        type: 'danger',\n        message: formatAPIError(isDraftRelationsError),\n      });\n    }\n  }, [isDraftRelationsError, toggleNotification, formatAPIError]);\n\n  if (!schema?.options?.draftAndPublish) {\n    return null;\n  }\n\n  if (!hasI18n) {\n    return null;\n  }\n\n  if (!documentId) {\n    return null;\n  }\n\n  // This document action can be enabled given that draft and publish and i18n are\n  // enabled and we can publish the current locale.\n\n  const publish = async () => {\n    await publishManyAction({\n      model,\n      documentIds: [documentId],\n      params: {\n        ...params,\n        locale: localesForAction,\n      },\n    });\n\n    setSelectedRows([]);\n  };\n\n  const unpublish = async () => {\n    await unpublishManyAction({\n      model,\n      documentIds: [documentId],\n      params: {\n        ...params,\n        locale: localesForAction,\n      },\n    });\n\n    setSelectedRows([]);\n  };\n\n  const handleAction = async () => {\n    if (draftRelationsCount > 0) {\n      setIsDraftRelationConfirmationOpen(true);\n    } else if (isBulkPublish) {\n      await publish();\n    } else {\n      await unpublish();\n    }\n  };\n\n  if (isDraftRelationConfirmationOpen) {\n    return {\n      label: formatMessage({\n        id: 'app.components.ConfirmDialog.title',\n        defaultMessage: 'Confirmation',\n      }),\n      variant: 'danger',\n      dialog: {\n        onCancel: () => {\n          setIsDraftRelationConfirmationOpen(false);\n        },\n        onConfirm: async () => {\n          await publish();\n\n          setIsDraftRelationConfirmationOpen(false);\n        },\n        type: 'dialog',\n        title: formatMessage({\n          id: getTranslation('actions.publish.dialog.title'),\n          defaultMessage: 'Confirmation',\n        }),\n        content: (\n          <Flex direction=\"column\" alignItems=\"center\" gap={2}>\n            <WarningCircle width=\"2.4rem\" height=\"2.4rem\" fill=\"danger600\" />\n            <Typography textAlign=\"center\">\n              {formatMessage({\n                id: getTranslation('CMEditViewBulkLocale.draft-relation-warning'),\n                defaultMessage:\n                  'Some locales are related to draft entries. Publishing them could leave broken links in your app.',\n              })}\n            </Typography>\n            <Typography textAlign=\"center\">\n              {formatMessage({\n                id: getTranslation('CMEditViewBulkLocale.continue-confirmation'),\n                defaultMessage: 'Are you sure you want to continue?',\n              })}\n            </Typography>\n          </Flex>\n        ),\n      },\n    };\n  }\n\n  const hasPermission = selectedRows\n    .map(({ locale }) => locale)\n    .every((locale) => canPublish.includes(locale));\n\n  return {\n    label: formatMessage({\n      id: getTranslation(`CMEditViewBulkLocale.${isBulkPublish ? 'publish' : 'unpublish'}-title`),\n      defaultMessage: `${isBulkPublish ? 'Publish' : 'Unpublish'} Multiple Locales`,\n    }),\n    variant: isBulkPublish ? 'secondary' : 'danger',\n    icon: isBulkPublish ? <ListPlus /> : <Cross />,\n    disabled: isOnPublishedTab || canPublish.length === 0,\n    position: ['panel'],\n    dialog: {\n      type: 'modal',\n      title: formatMessage({\n        id: getTranslation(`CMEditViewBulkLocale.${isBulkPublish ? 'publish' : 'unpublish'}-title`),\n        defaultMessage: `${isBulkPublish ? 'Publish' : 'Unpublish'} Multiple Locales`,\n      }),\n      content: () => {\n        return (\n          <Table.Root\n            headers={headers}\n            rows={rows.map((row) => ({\n              ...row,\n              id: row.locale,\n            }))}\n            selectedRows={selectedRows}\n            onSelectedRowsChange={(tableSelectedRows) => setSelectedRows(tableSelectedRows)}\n          >\n            <BulkLocaleActionModal\n              validationErrors={validationErrors}\n              headers={headers}\n              rows={rows}\n              localesMetadata={localesMetadata as Locale[]}\n              action={action ?? 'bulk-publish'}\n            />\n          </Table.Root>\n        );\n      },\n      footer: () => (\n        <Modal.Footer justifyContent=\"flex-end\">\n          <Button\n            loading={isDraftRelationsLoading}\n            disabled={!hasPermission || localesForAction.length === 0}\n            variant=\"default\"\n            onClick={handleAction}\n          >\n            {formatMessage({\n              id: isBulkPublish ? 'app.utils.publish' : 'app.utils.unpublish',\n              defaultMessage: isBulkPublish ? 'Publish' : 'Unpublish',\n            })}\n          </Button>\n        </Modal.Footer>\n      ),\n    },\n  };\n};\n\n/* -------------------------------------------------------------------------------------------------\n * BulkLocalePublishAction\n * -----------------------------------------------------------------------------------------------*/\nconst BulkLocalePublishAction: DocumentActionComponent = (props: ExtendedDocumentActionProps) => {\n  return BulkLocaleAction({ action: 'bulk-publish', ...props });\n};\n\n/* -------------------------------------------------------------------------------------------------\n * BulkLocaleUnpublishAction\n * -----------------------------------------------------------------------------------------------*/\nconst BulkLocaleUnpublishAction: DocumentActionComponent = (props: ExtendedDocumentActionProps) => {\n  return BulkLocaleAction({ action: 'bulk-unpublish', ...props });\n};\n\n/**\n * Because the icon system is completely broken, we have to do\n * this to remove the fill from the cog.\n */\nconst StyledTrash = styled(Trash)`\n  path {\n    fill: currentColor;\n  }\n`;\n\nexport {\n  BulkLocalePublishAction,\n  BulkLocaleUnpublishAction,\n  DeleteLocaleAction,\n  LocalePickerAction,\n  FillFromAnotherLocaleAction,\n};\n", "import * as React from 'react';\n\nimport { Typography } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\n\nimport { useI18n } from '../hooks/useI18n';\nimport { getTranslation } from '../utils/getTranslation';\n\nconst Emphasis = (chunks: React.ReactNode) => {\n  return (\n    <Typography fontWeight=\"semiBold\" textColor=\"danger500\">\n      {chunks}\n    </Typography>\n  );\n};\n\nconst DeleteModalAdditionalInfo = () => {\n  const { hasI18n } = useI18n();\n  const { formatMessage } = useIntl();\n\n  if (!hasI18n) {\n    return null;\n  }\n\n  return (\n    <Typography textColor=\"danger500\">\n      {formatMessage(\n        {\n          id: getTranslation('Settings.list.actions.deleteAdditionalInfos'),\n          defaultMessage:\n            'This will delete the active locale versions <em>(from Internationalization)</em>',\n        },\n        {\n          em: Emphasis,\n        }\n      )}\n    </Typography>\n  );\n};\n\nconst PublishModalAdditionalInfo = () => {\n  const { hasI18n } = useI18n();\n  const { formatMessage } = useIntl();\n\n  if (!hasI18n) {\n    return null;\n  }\n\n  return (\n    <Typography textColor=\"danger500\">\n      {formatMessage(\n        {\n          id: getTranslation('Settings.list.actions.publishAdditionalInfos'),\n          defaultMessage:\n            'This will publish the active locale versions <em>(from Internationalization)</em>',\n        },\n        {\n          em: Emphasis,\n        }\n      )}\n    </Typography>\n  );\n};\n\nconst UnpublishModalAdditionalInfo = () => {\n  const { hasI18n } = useI18n();\n  const { formatMessage } = useIntl();\n\n  if (!hasI18n) {\n    return null;\n  }\n\n  return (\n    <Typography textColor=\"danger500\">\n      {formatMessage(\n        {\n          id: getTranslation('Settings.list.actions.unpublishAdditionalInfos'),\n          defaultMessage:\n            'This will unpublish the active locale versions <em>(from Internationalization)</em>',\n        },\n        {\n          em: Emphasis,\n        }\n      )}\n    </Typography>\n  );\n};\n\nexport { DeleteModalAdditionalInfo, PublishModalAdditionalInfo, UnpublishModalAdditionalInfo };\n", "import * as React from 'react';\n\nimport { useQueryParams } from '@strapi/admin/strapi-admin';\nimport { SingleSelect, SingleSelectOption } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\n\nimport { useI18n } from '../hooks/useI18n';\nimport { useGetLocalesQuery } from '../services/locales';\nimport { getTranslation } from '../utils/getTranslation';\n\nimport type { I18nBaseQuery } from '../types';\n\ninterface Query extends I18nBaseQuery {\n  page?: number;\n}\n\nconst LocalePicker = () => {\n  const { formatMessage } = useIntl();\n  const [{ query }, setQuery] = useQueryParams<Query>();\n\n  const { hasI18n, canRead, canCreate } = useI18n();\n  const { data: locales = [] } = useGetLocalesQuery(undefined, {\n    skip: !hasI18n,\n  });\n\n  const handleChange = React.useCallback(\n    (code: string, replace = false) => {\n      setQuery(\n        {\n          page: 1,\n          plugins: { ...query.plugins, i18n: { locale: code } },\n        },\n        'push',\n        replace\n      );\n    },\n    [query.plugins, setQuery]\n  );\n\n  React.useEffect(() => {\n    if (!Array.isArray(locales) || !hasI18n) {\n      return;\n    }\n    /**\n     * Handle the case where the current locale query param doesn't exist\n     * in the list of available locales, so we redirect to the default locale.\n     */\n    const currentDesiredLocale = query.plugins?.i18n?.locale;\n    const doesLocaleExist = locales.find((loc) => loc.code === currentDesiredLocale);\n    const defaultLocale = locales.find((locale) => locale.isDefault);\n    if (!doesLocaleExist && defaultLocale?.code) {\n      handleChange(defaultLocale.code, true);\n    }\n  }, [hasI18n, handleChange, locales, query.plugins?.i18n?.locale]);\n\n  if (!hasI18n || !Array.isArray(locales) || locales.length === 0) {\n    return null;\n  }\n\n  const displayedLocales = locales.filter((locale) => {\n    /**\n     * If you can create or read we allow you to see the locale exists\n     * this is because in the ListView, you may be able to create a new entry\n     * in a locale you can't read.\n     */\n    return canCreate.includes(locale.code) || canRead.includes(locale.code);\n  });\n\n  return (\n    <SingleSelect\n      size=\"S\"\n      aria-label={formatMessage({\n        id: getTranslation('actions.select-locale'),\n        defaultMessage: 'Select locale',\n      })}\n      value={query.plugins?.i18n?.locale || locales.find((locale) => locale.isDefault)?.code}\n      // @ts-expect-error – This can be removed in V2 of the DS.\n      onChange={handleChange}\n    >\n      {displayedLocales.map((locale) => (\n        <SingleSelectOption key={locale.id} value={locale.code}>\n          {locale.name}\n        </SingleSelectOption>\n      ))}\n    </SingleSelect>\n  );\n};\n\nexport { LocalePicker };\n", "/* eslint-disable check-file/filename-naming-convention */\nimport * as React from 'react';\n\nimport { Flex, VisuallyHidden } from '@strapi/design-system';\nimport { Earth, EarthStriked } from '@strapi/icons';\nimport { MessageDescriptor, useIntl } from 'react-intl';\nimport { styled } from 'styled-components';\n\nimport { getTranslation } from '../utils/getTranslation';\n\nimport type { EditFieldLayout, EditLayout } from '@strapi/content-manager/strapi-admin';\n\ninterface MutateEditViewArgs {\n  layout: EditLayout;\n}\n\nconst mutateEditViewHook = ({ layout }: MutateEditViewArgs): MutateEditViewArgs => {\n  // If i18n isn't explicitly enabled on the content type, then no field can be localized\n  if (\n    !('i18n' in layout.options) ||\n    (typeof layout.options.i18n === 'object' &&\n      layout.options.i18n !== null &&\n      'localized' in layout.options.i18n &&\n      !layout.options.i18n.localized)\n  ) {\n    return { layout };\n  }\n\n  const components = Object.entries(layout.components).reduce<EditLayout['components']>(\n    (acc, [key, componentLayout]) => {\n      return {\n        ...acc,\n        [key]: {\n          ...componentLayout,\n          layout: componentLayout.layout.map((row) => row.map(addLabelActionToField)),\n        },\n      };\n    },\n    {}\n  );\n\n  return {\n    layout: {\n      ...layout,\n      components,\n      layout: layout.layout.map((panel) => panel.map((row) => row.map(addLabelActionToField))),\n    },\n  } satisfies Pick<MutateEditViewArgs, 'layout'>;\n};\n\nconst addLabelActionToField = (field: EditFieldLayout) => {\n  const isFieldLocalized = doesFieldHaveI18nPluginOpt(field.attribute.pluginOptions)\n    ? field.attribute.pluginOptions.i18n.localized\n    : true || ['uid', 'relation'].includes(field.attribute.type);\n\n  const labelActionProps = {\n    title: {\n      id: isFieldLocalized\n        ? getTranslation('Field.localized')\n        : getTranslation('Field.not-localized'),\n      defaultMessage: isFieldLocalized\n        ? 'This value is unique for the selected locale'\n        : 'This value is the same across all locales',\n    },\n    icon: isFieldLocalized ? <Earth /> : null,\n  };\n\n  return {\n    ...field,\n    labelAction: isFieldLocalized ? <LabelAction {...labelActionProps} /> : null,\n  };\n};\n\nconst doesFieldHaveI18nPluginOpt = (\n  pluginOpts?: object\n): pluginOpts is { i18n: { localized: boolean } } => {\n  if (!pluginOpts) {\n    return false;\n  }\n\n  return (\n    'i18n' in pluginOpts &&\n    typeof pluginOpts.i18n === 'object' &&\n    pluginOpts.i18n !== null &&\n    'localized' in pluginOpts.i18n\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * LabelAction\n * -----------------------------------------------------------------------------------------------*/\n\ninterface LabelActionProps {\n  title: MessageDescriptor;\n  icon: React.ReactNode;\n}\n\nconst LabelAction = ({ title, icon }: LabelActionProps) => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Span tag=\"span\">\n      <VisuallyHidden tag=\"span\">{formatMessage(title)}</VisuallyHidden>\n      {React.cloneElement(icon as React.ReactElement, {\n        'aria-hidden': true,\n        focusable: false, // See: https://allyjs.io/tutorials/focusing-in-svg.html#making-svg-elements-focusable\n      })}\n    </Span>\n  );\n};\n\nconst Span = styled(Flex)`\n  svg {\n    width: 12px;\n    height: 12px;\n\n    fill: ${({ theme }) => theme.colors.neutral500};\n\n    path {\n      fill: ${({ theme }) => theme.colors.neutral500};\n    }\n  }\n`;\n\nexport { mutateEditViewHook };\n", "import { <PERSON>, Flex, Popover, Typography, useCollator, Button } from '@strapi/design-system';\nimport { CaretDown } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\n\nimport { Locale } from '../../../shared/contracts/locales';\nimport { useGetLocalesQuery } from '../services/locales';\n\ninterface LocaleListCellProps {\n  localizations: { locale: string }[];\n  locale: string;\n}\n\nconst LocaleListCell = ({ locale: currentLocale, localizations }: LocaleListCellProps) => {\n  const { locale: language } = useIntl();\n  const { data: locales = [] } = useGetLocalesQuery();\n  const formatter = useCollator(language, {\n    sensitivity: 'base',\n  });\n\n  if (!Array.isArray(locales) || !localizations) {\n    return null;\n  }\n\n  const availableLocales = localizations.map((loc) => loc.locale);\n\n  const localesForDocument = locales\n    .reduce<Locale[]>((acc, locale) => {\n      const createdLocale = [currentLocale, ...availableLocales].find((loc) => {\n        return loc === locale.code;\n      });\n\n      if (createdLocale) {\n        acc.push(locale);\n      }\n\n      return acc;\n    }, [])\n    .map((locale) => {\n      if (locale.isDefault) {\n        return `${locale.name} (default)`;\n      }\n\n      return locale.name;\n    })\n    .toSorted((a, b) => formatter.compare(a, b));\n\n  return (\n    <Popover.Root>\n      <Popover.Trigger>\n        <Button variant=\"ghost\" type=\"button\" onClick={(e) => e.stopPropagation()}>\n          <Flex minWidth=\"100%\" alignItems=\"center\" justifyContent=\"center\" fontWeight=\"regular\">\n            <Typography textColor=\"neutral800\" ellipsis marginRight={2}>\n              {localesForDocument.join(', ')}\n            </Typography>\n            <Flex>\n              <CaretDown width=\"1.2rem\" height=\"1.2rem\" />\n            </Flex>\n          </Flex>\n        </Button>\n      </Popover.Trigger>\n      <Popover.Content sideOffset={16}>\n        <ul>\n          {localesForDocument.map((name) => (\n            <Box key={name} padding={3} tag=\"li\">\n              <Typography>{name}</Typography>\n            </Box>\n          ))}\n        </ul>\n      </Popover.Content>\n    </Popover.Root>\n  );\n};\n\nexport { LocaleListCell };\nexport type { LocaleListCellProps };\n", "/* eslint-disable check-file/filename-naming-convention */\nimport { LocaleListCell } from '../components/LocaleListCell';\nimport { doesPluginOptionsHaveI18nLocalized } from '../utils/fields';\nimport { getTranslation } from '../utils/getTranslation';\n\nimport type { ListFieldLayout, ListLayout } from '@strapi/content-manager/strapi-admin';\n\n/* -------------------------------------------------------------------------------------------------\n * addColumnToTableHook\n * -----------------------------------------------------------------------------------------------*/\ninterface AddColumnToTableHookArgs {\n  layout: ListLayout;\n  displayedHeaders: ListFieldLayout[];\n}\n\nconst addColumnToTableHook = ({ displayedHeaders, layout }: AddColumnToTableHookArgs) => {\n  const { options } = layout;\n\n  const isFieldLocalized = doesPluginOptionsHaveI18nLocalized(options)\n    ? options.i18n.localized\n    : false;\n\n  if (!isFieldLocalized) {\n    return { displayedHeaders, layout };\n  }\n\n  return {\n    displayedHeaders: [\n      ...displayedHeaders,\n      {\n        attribute: { type: 'string' },\n        label: {\n          id: getTranslation('list-view.table.header.label'),\n          defaultMessage: 'Available in',\n        },\n        searchable: false,\n        sortable: false,\n        name: 'locales',\n        // @ts-expect-error – ID is seen as number | string; this will change when we move the type over.\n        cellFormatter: (props, _header, meta) => <LocaleListCell {...props} {...meta} />,\n      },\n    ],\n    layout,\n  };\n};\n\nexport { addColumnToTableHook };\n", "import type { Schema } from '@strapi/types';\n/* -------------------------------------------------------------------------------------------------\n * addLocaleToReleasesHook\n * -----------------------------------------------------------------------------------------------*/\ninterface AddLocaleToReleasesHookArgs {\n  displayedHeaders: {\n    key: string;\n    fieldSchema: Schema.Attribute.Kind | 'custom';\n    metadatas: {\n      label: { id: string; defaultMessage: string };\n      searchable: boolean;\n      sortable: boolean;\n    };\n    name: string;\n  }[];\n  hasI18nEnabled: boolean;\n}\n\nconst addLocaleToReleasesHook = ({ displayedHeaders = [] }: AddLocaleToReleasesHookArgs) => {\n  return {\n    displayedHeaders: [\n      ...displayedHeaders,\n      {\n        label: {\n          id: 'content-releases.page.ReleaseDetails.table.header.label.locale',\n          defaultMessage: 'locale',\n        },\n        name: 'locale',\n      },\n    ],\n    hasI18nEnabled: true,\n  };\n};\n\nexport { addLocaleToReleasesHook };\n", "import get from 'lodash/get';\n\nimport type { Middleware } from '@reduxjs/toolkit';\nimport type { Store } from '@strapi/admin/strapi-admin';\n\nconst extendCTBAttributeInitialDataMiddleware: () => Middleware<\n  object,\n  ReturnType<Store['getState']>\n> = () => {\n  return ({ getState }) =>\n    (next) =>\n    (action) => {\n      const enhanceAction = ({ uid }: { uid: string }) => {\n        // the block here is to catch the error when trying to access the state\n        // of the ctb when the plugin is not mounted\n        try {\n          const store = getState();\n\n          const type = get(store, [\n            'content-type-builder_dataManagerProvider',\n            'current',\n            'contentTypes',\n            uid,\n          ]);\n\n          if (!type || type.modelType !== 'contentType') {\n            return next(action);\n          }\n\n          const hasi18nEnabled = get(type, ['pluginOptions', 'i18n', 'localized'], false);\n\n          if (hasi18nEnabled) {\n            return next({\n              ...action,\n              payload: {\n                ...action.payload,\n                options: {\n                  pluginOptions: {\n                    ...(action?.payload?.options?.pluginOptions ?? {}),\n                    i18n: {\n                      localized: true,\n                    },\n                  },\n                },\n              },\n            });\n          }\n\n          return next(action);\n        } catch (err) {\n          return next(action);\n        }\n      };\n\n      const { payload = {}, type } = action ?? {};\n\n      if (\n        type === 'formModal/setAttributeDataSchema' &&\n        !['relation', 'component'].includes(payload.attributeType) &&\n        !payload.isEditing\n      ) {\n        return enhanceAction({\n          uid: payload.uid,\n        });\n      }\n\n      if (type === 'formModal/setCustomFieldDataSchema' && !payload.isEditing) {\n        return enhanceAction({\n          uid: payload.uid,\n        });\n      }\n\n      if (\n        type === 'formModal/resetPropsAndSetFormForAddingAnExistingCompo' ||\n        type === 'formModal/resetPropsAndSaveCurrentData'\n      ) {\n        return enhanceAction({\n          uid: payload.uid,\n        });\n      }\n\n      return next(action);\n    };\n};\n\nexport { extendCTBAttributeInitialDataMiddleware };\n", "import type { Middleware } from '@reduxjs/toolkit';\nimport type { Store } from '@strapi/admin/strapi-admin';\n\nconst extendCTBInitialDataMiddleware: () => Middleware<\n  object,\n  ReturnType<Store['getState']>\n> = () => {\n  return () => (next) => (action) => {\n    if (\n      action.type === 'ContentTypeBuilder/FormModal/SET_DATA_TO_EDIT' &&\n      action.modalType === 'contentType'\n    ) {\n      const i18n = { localized: false };\n\n      const pluginOptions = action.data.pluginOptions\n        ? { ...action.data.pluginOptions, i18n }\n        : { i18n };\n\n      const data = { ...action.data, pluginOptions };\n\n      if (action.actionType === 'create') {\n        return next({ ...action, data });\n      }\n\n      // Override the action if the pluginOption config does not contain i18n\n      // In this case we need to set the proper initialData shape\n      if (!action.data.pluginOptions?.i18n?.localized) {\n        return next({ ...action, data });\n      }\n    }\n\n    // action is not the one we want to override\n    return next(action);\n  };\n};\n\nexport { extendCTBInitialDataMiddleware };\n", "/* eslint-disable check-file/filename-naming-convention */\nimport * as qs from 'qs';\nimport { matchPath } from 'react-router-dom';\n\nimport type { RBACMiddleware } from '@strapi/admin/strapi-admin';\n\nconst localeMiddleware: RBACMiddleware = (ctx) => (next) => (permissions) => {\n  const match = matchPath('/content-manager/:collectionType/:model?/:id', ctx.pathname);\n\n  if (!match) {\n    return next(permissions);\n  }\n\n  const search = qs.parse(ctx.search);\n\n  if (typeof search !== 'object') {\n    return next(permissions);\n  }\n\n  if (!('plugins' in search && typeof search.plugins === 'object')) {\n    return next(permissions);\n  }\n\n  if (\n    !(\n      'i18n' in search.plugins &&\n      typeof search.plugins.i18n === 'object' &&\n      !Array.isArray(search.plugins.i18n)\n    )\n  ) {\n    return next(permissions);\n  }\n\n  const { locale } = search.plugins.i18n;\n\n  if (typeof locale !== 'string') {\n    return next(permissions);\n  }\n\n  const revisedPermissions = permissions.filter(\n    (permission) =>\n      !permission.properties?.locales || permission.properties.locales.includes(locale)\n  );\n\n  return next(revisedPermissions);\n};\n\nexport { localeMiddleware };\n", "type TradOptions = Record<string, string>;\n\nconst prefixPluginTranslations = (trad: TradOptions, pluginId: string): TradOptions => {\n  if (!pluginId) {\n    throw new TypeError(\"pluginId can't be empty\");\n  }\n  return Object.keys(trad).reduce((acc, current) => {\n    acc[`${pluginId}.${current}`] = trad[current];\n    return acc;\n  }, {} as TradOptions);\n};\n\nexport { prefixPluginTranslations };\n", "import omit from 'lodash/omit';\n\nimport { LOCALIZED_FIELDS, doesPluginOptionsHaveI18nLocalized } from './fields';\n\nimport type { Schema } from '@strapi/types';\n\n/* -------------------------------------------------------------------------------------------------\n * mutateCTBContentTypeSchema\n * -----------------------------------------------------------------------------------------------*/\n\n// TODO: refactor for CTB refactors\nconst mutateCTBContentTypeSchema = (\n  nextSchema: {\n    pluginOptions: Schema.ContentType['pluginOptions'];\n    attributes: Schema.Attribute.AnyAttribute[];\n    uid?: string;\n  },\n  prevSchema?: {\n    pluginOptions: Schema.ContentType['pluginOptions'];\n    attributes: Schema.Attribute.AnyAttribute[];\n    uid?: string;\n  }\n) => {\n  if (!prevSchema) {\n    return nextSchema;\n  }\n\n  // Don't perform mutations components\n  if (!doesPluginOptionsHaveI18nLocalized(nextSchema.pluginOptions)) {\n    return nextSchema;\n  }\n\n  const isNextSchemaLocalized = nextSchema.pluginOptions.i18n.localized;\n  const isPrevSchemaLocalized = doesPluginOptionsHaveI18nLocalized(prevSchema?.pluginOptions)\n    ? prevSchema?.pluginOptions.i18n.localized\n    : false;\n\n  // No need to perform modification on the schema, if the i18n feature was not changed\n  // at the ct level\n  if (isNextSchemaLocalized && isPrevSchemaLocalized) {\n    return nextSchema;\n  }\n\n  if (isNextSchemaLocalized) {\n    const attributes = addLocalisationToFields(nextSchema.attributes);\n\n    return {\n      ...nextSchema,\n      attributes,\n    };\n  }\n\n  // Remove the i18n object from the pluginOptions\n  if (!isNextSchemaLocalized) {\n    const pluginOptions = omit(nextSchema.pluginOptions, 'i18n');\n    const attributes = disableAttributesLocalisation(nextSchema.attributes);\n\n    return {\n      ...nextSchema,\n      pluginOptions,\n      attributes,\n    };\n  }\n\n  return nextSchema;\n};\n\n/* -------------------------------------------------------------------------------------------------\n * addLocalisationToFields\n * -----------------------------------------------------------------------------------------------*/\n\nconst addLocalisationToFields = (attributes: Schema.Attribute.AnyAttribute[]) => {\n  return attributes.map((currentAttribute) => {\n    if (LOCALIZED_FIELDS.includes(currentAttribute.type)) {\n      const i18n = { localized: true };\n\n      const pluginOptions = currentAttribute.pluginOptions\n        ? { ...currentAttribute.pluginOptions, i18n }\n        : { i18n };\n\n      return { ...currentAttribute, pluginOptions };\n    }\n\n    return currentAttribute;\n  });\n};\n\n/* -------------------------------------------------------------------------------------------------\n * disableAttributesLocalisation\n * -----------------------------------------------------------------------------------------------*/\n\nconst disableAttributesLocalisation = (attributes: Schema.Attribute.AnyAttribute[]) => {\n  return attributes.map((currentAttribute) => {\n    return omit(currentAttribute, 'pluginOptions.i18n');\n  });\n};\n\nexport { mutateCTBContentTypeSchema };\n", "import get from 'lodash/get';\nimport * as yup from 'yup';\n\nimport { CheckboxConfirmation } from './components/CheckboxConfirmation';\nimport {\n  BulkLocalePublishAction,\n  BulkLocaleUnpublishAction,\n  DeleteLocaleAction,\n  LocalePickerAction,\n  FillFromAnotherLocaleAction,\n} from './components/CMHeaderActions';\nimport {\n  DeleteModalAdditionalInfo,\n  PublishModalAdditionalInfo,\n  UnpublishModalAdditionalInfo,\n} from './components/CMListViewModalsAdditionalInformation';\nimport { LocalePicker } from './components/LocalePicker';\nimport { PERMISSIONS } from './constants';\nimport { mutateEditViewHook } from './contentManagerHooks/editView';\nimport { addColumnToTableHook } from './contentManagerHooks/listView';\nimport { addLocaleToReleasesHook } from './contentReleasesHooks/releaseDetailsView';\nimport { extendCTBAttributeInitialDataMiddleware } from './middlewares/extendCTBAttributeInitialData';\nimport { extendCTBInitialDataMiddleware } from './middlewares/extendCTBInitialData';\nimport { localeMiddleware } from './middlewares/rbac-middleware';\nimport { pluginId } from './pluginId';\nimport { i18nApi } from './services/api';\nimport { LOCALIZED_FIELDS } from './utils/fields';\nimport { getTranslation } from './utils/getTranslation';\nimport { prefixPluginTranslations } from './utils/prefixPluginTranslations';\nimport { mutateCTBContentTypeSchema } from './utils/schemas';\n\nimport type { DocumentActionComponent } from '@strapi/content-manager/strapi-admin';\n\n// eslint-disable-next-line import/no-default-export\nexport default {\n  register(app: any) {\n    app.addMiddlewares([extendCTBAttributeInitialDataMiddleware, extendCTBInitialDataMiddleware]);\n    app.addMiddlewares([() => i18nApi.middleware]);\n    app.addReducers({\n      [i18nApi.reducerPath]: i18nApi.reducer,\n    });\n    app.addRBACMiddleware([localeMiddleware]);\n    app.registerPlugin({\n      id: pluginId,\n      name: pluginId,\n    });\n  },\n  bootstrap(app: any) {\n    // // Hook that adds a column into the CM's LV table\n    app.registerHook('Admin/CM/pages/ListView/inject-column-in-table', addColumnToTableHook);\n    app.registerHook('Admin/CM/pages/EditView/mutate-edit-view-layout', mutateEditViewHook);\n    // Hooks that checks if the locale is present in the release\n    app.registerHook(\n      'ContentReleases/pages/ReleaseDetails/add-locale-in-releases',\n      addLocaleToReleasesHook\n    );\n\n    // Add the settings link\n    app.addSettingsLink('global', {\n      intlLabel: {\n        id: getTranslation('plugin.name'),\n        defaultMessage: 'Internationalization',\n      },\n      id: 'internationalization',\n      to: 'internationalization',\n      Component: () =>\n        import('./pages/SettingsPage').then((mod) => ({ default: mod.ProtectedSettingsPage })),\n      permissions: PERMISSIONS.accessMain,\n    });\n\n    const contentManager = app.getPlugin('content-manager');\n\n    contentManager.apis.addDocumentHeaderAction([LocalePickerAction, FillFromAnotherLocaleAction]);\n    contentManager.apis.addDocumentAction((actions: DocumentActionComponent[]) => {\n      const indexOfDeleteAction = actions.findIndex((action) => action.type === 'delete');\n      actions.splice(indexOfDeleteAction, 0, DeleteLocaleAction);\n      return actions;\n    });\n\n    contentManager.apis.addDocumentAction((actions: DocumentActionComponent[]) => {\n      // When enabled the bulk locale publish action should be the first action\n      // in 'More Document Actions' and therefore the third action in the array\n      actions.splice(2, 0, BulkLocalePublishAction);\n      actions.splice(5, 0, BulkLocaleUnpublishAction);\n      return actions;\n    });\n\n    contentManager.injectComponent('listView', 'actions', {\n      name: 'i18n-locale-filter',\n      Component: LocalePicker,\n    });\n\n    contentManager.injectComponent('listView', 'publishModalAdditionalInfos', {\n      name: 'i18n-publish-bullets-in-modal',\n      Component: PublishModalAdditionalInfo,\n    });\n\n    contentManager.injectComponent('listView', 'unpublishModalAdditionalInfos', {\n      name: 'i18n-unpublish-bullets-in-modal',\n      Component: UnpublishModalAdditionalInfo,\n    });\n\n    contentManager.injectComponent('listView', 'deleteModalAdditionalInfos', {\n      name: 'i18n-delete-bullets-in-modal',\n      Component: DeleteModalAdditionalInfo,\n    });\n\n    const ctbPlugin = app.getPlugin('content-type-builder');\n\n    if (ctbPlugin) {\n      const ctbFormsAPI = ctbPlugin.apis.forms;\n      ctbFormsAPI.addContentTypeSchemaMutation(mutateCTBContentTypeSchema);\n      ctbFormsAPI.components.add({ id: 'checkboxConfirmation', component: CheckboxConfirmation });\n\n      ctbFormsAPI.extendContentType({\n        validator: () => ({\n          i18n: yup.object().shape({\n            localized: yup.bool(),\n          }),\n        }),\n        form: {\n          advanced() {\n            return [\n              {\n                name: 'pluginOptions.i18n.localized',\n                description: {\n                  id: getTranslation('plugin.schema.i18n.localized.description-content-type'),\n                  defaultMessage: 'Allows translating an entry into different languages',\n                },\n                type: 'checkboxConfirmation',\n                intlLabel: {\n                  id: getTranslation('plugin.schema.i18n.localized.label-content-type'),\n                  defaultMessage: 'Localization',\n                },\n              },\n            ];\n          },\n        },\n      });\n\n      ctbFormsAPI.extendFields(LOCALIZED_FIELDS, {\n        form: {\n          advanced({ contentTypeSchema, forTarget, type, step }: any) {\n            if (forTarget !== 'contentType') {\n              return [];\n            }\n\n            const hasI18nEnabled = get(\n              contentTypeSchema,\n              ['pluginOptions', 'i18n', 'localized'],\n              false\n            );\n\n            if (!hasI18nEnabled) {\n              return [];\n            }\n\n            if (type === 'component' && step === '1') {\n              return [];\n            }\n\n            return [\n              {\n                name: 'pluginOptions.i18n.localized',\n                description: {\n                  id: getTranslation('plugin.schema.i18n.localized.description-field'),\n                  defaultMessage: 'The field can have different values in each locale',\n                },\n                type: 'checkbox',\n                intlLabel: {\n                  id: getTranslation('plugin.schema.i18n.localized.label-field'),\n                  defaultMessage: 'Enable localization for this field',\n                },\n              },\n            ];\n          },\n        },\n      });\n    }\n  },\n  async registerTrads({ locales }: { locales: string[] }) {\n    const importedTrads = await Promise.all(\n      locales.map((locale) => {\n        return import(`./translations/${locale}.json`)\n          .then(({ default: data }) => {\n            return {\n              data: prefixPluginTranslations(data, pluginId),\n              locale,\n            };\n          })\n          .catch(() => {\n            return {\n              data: {},\n              locale,\n            };\n          });\n      })\n    );\n\n    return Promise.resolve(importedTrads);\n  },\n};\n", "import * as React from 'react';\n\nimport {\n  useAP<PERSON>rror<PERSON><PERSON><PERSON>,\n  useNotification,\n  useQueryParams,\n  useRB<PERSON>,\n  isFetchError,\n} from '@strapi/admin/strapi-admin';\nimport { unstable_useDocumentLayout as useDocumentLayout } from '@strapi/content-manager/strapi-admin';\nimport {\n  Box,\n  Button,\n  Flex,\n  SingleSelect,\n  SingleSelectOption,\n  EmptyStateLayout,\n  LinkButton,\n  Field,\n  Modal,\n} from '@strapi/design-system';\nimport { PaperPlane } from '@strapi/icons';\nimport { EmptyDocuments } from '@strapi/icons/symbols';\nimport { useFormik } from 'formik';\nimport { useIntl } from 'react-intl';\nimport { Link as ReactRouterLink } from 'react-router-dom';\nimport * as yup from 'yup';\n\nimport { CreateReleaseAction } from '../../../shared/contracts/release-actions';\nimport { PERMISSIONS } from '../constants';\nimport { useCreateReleaseActionMutation, useGetReleasesForEntryQuery } from '../services/release';\n\nimport { ReleaseActionOptions } from './ReleaseActionOptions';\n\nimport type {\n  DocumentActionComponent,\n  DocumentActionProps,\n} from '@strapi/content-manager/strapi-admin';\nimport type { UID } from '@strapi/types';\n\n/* -------------------------------------------------------------------------------------------------\n * AddActionToReleaseModal\n * -----------------------------------------------------------------------------------------------*/\nexport const RELEASE_ACTION_FORM_SCHEMA = yup.object().shape({\n  type: yup.string().oneOf(['publish', 'unpublish']).required(),\n  releaseId: yup.string().required(),\n});\n\nexport interface FormValues {\n  type: CreateReleaseAction.Request['body']['type'];\n  releaseId: CreateReleaseAction.Request['params']['releaseId'];\n}\n\nexport const INITIAL_VALUES = {\n  type: 'publish',\n  releaseId: '',\n} satisfies FormValues;\n\ninterface AddActionToReleaseModalProps {\n  contentType: string;\n  documentId?: string;\n  onInputChange: (field: keyof FormValues, value: string | number) => void;\n  values: FormValues;\n}\n\nexport const NoReleases = () => {\n  const { formatMessage } = useIntl();\n  return (\n    <EmptyStateLayout\n      icon={<EmptyDocuments width=\"16rem\" />}\n      content={formatMessage({\n        id: 'content-releases.content-manager-edit-view.add-to-release.no-releases-message',\n        defaultMessage:\n          'No available releases. Open the list of releases and create a new one from there.',\n      })}\n      action={\n        <LinkButton\n          to={{\n            pathname: '/plugins/content-releases',\n          }}\n          tag={ReactRouterLink}\n          variant=\"secondary\"\n        >\n          {formatMessage({\n            id: 'content-releases.content-manager-edit-view.add-to-release.redirect-button',\n            defaultMessage: 'Open the list of releases',\n          })}\n        </LinkButton>\n      }\n      shadow=\"none\"\n    />\n  );\n};\n\nconst AddActionToReleaseModal = ({\n  contentType,\n  documentId,\n  onInputChange,\n  values,\n}: AddActionToReleaseModalProps) => {\n  const { formatMessage } = useIntl();\n  const [{ query }] = useQueryParams<{ plugins?: { i18n?: { locale?: string } } }>();\n  const locale = query.plugins?.i18n?.locale;\n\n  // Get all 'pending' releases that do not have the entry attached\n  const response = useGetReleasesForEntryQuery({\n    contentType,\n    entryDocumentId: documentId,\n    hasEntryAttached: false,\n    locale,\n  });\n\n  const releases = response.data?.data;\n\n  if (releases?.length === 0) {\n    return <NoReleases />;\n  }\n\n  return (\n    <Flex direction=\"column\" alignItems=\"stretch\" gap={2}>\n      <Box paddingBottom={6}>\n        <Field.Root required>\n          <Field.Label>\n            {formatMessage({\n              id: 'content-releases.content-manager-edit-view.add-to-release.select-label',\n              defaultMessage: 'Select a release',\n            })}\n          </Field.Label>\n          <SingleSelect\n            required\n            placeholder={formatMessage({\n              id: 'content-releases.content-manager-edit-view.add-to-release.select-placeholder',\n              defaultMessage: 'Select',\n            })}\n            name=\"releaseId\"\n            onChange={(value) => onInputChange('releaseId', value)}\n            value={values.releaseId}\n          >\n            {releases?.map((release) => (\n              <SingleSelectOption key={release.id} value={release.id}>\n                {release.name}\n              </SingleSelectOption>\n            ))}\n          </SingleSelect>\n        </Field.Root>\n      </Box>\n      <Field.Label>\n        {formatMessage({\n          id: 'content-releases.content-manager-edit-view.add-to-release.action-type-label',\n          defaultMessage: 'What do you want to do with this entry?',\n        })}\n      </Field.Label>\n      <ReleaseActionOptions\n        selected={values.type}\n        handleChange={(e) => onInputChange('type', e.target.value)}\n        name=\"type\"\n      />\n    </Flex>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * ReleaseActionModalForm\n * -----------------------------------------------------------------------------------------------*/\n\nconst ReleaseActionModalForm: DocumentActionComponent = ({\n  documentId,\n  document,\n  model,\n  collectionType,\n}: DocumentActionProps) => {\n  const { formatMessage } = useIntl();\n  const { allowedActions } = useRBAC(PERMISSIONS);\n  const { canCreateAction } = allowedActions;\n  const [createReleaseAction, { isLoading }] = useCreateReleaseActionMutation();\n  const { toggleNotification } = useNotification();\n  const { formatAPIError } = useAPIErrorHandler();\n  const [{ query }] = useQueryParams<{ plugins?: { i18n?: { locale?: string } } }>();\n  const locale = query.plugins?.i18n?.locale;\n\n  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>, onClose: () => void) => {\n    try {\n      await formik.handleSubmit(e);\n      onClose();\n    } catch (error) {\n      if (isFetchError(error)) {\n        // Handle axios error\n        toggleNotification({\n          type: 'danger',\n          message: formatAPIError(error),\n        });\n      } else {\n        // Handle generic error\n        toggleNotification({\n          type: 'danger',\n          message: formatMessage({\n            id: 'notification.error',\n            defaultMessage: 'An error occurred',\n          }),\n        });\n      }\n    }\n  };\n\n  const formik = useFormik({\n    initialValues: INITIAL_VALUES,\n    validationSchema: RELEASE_ACTION_FORM_SCHEMA,\n    onSubmit: async (values: FormValues) => {\n      if (collectionType === 'collection-types' && !documentId) {\n        throw new Error('Document id is required');\n      }\n\n      const response = await createReleaseAction({\n        body: {\n          type: values.type,\n          contentType: model as UID.ContentType,\n          entryDocumentId: documentId,\n          locale,\n        },\n        params: { releaseId: values.releaseId },\n      });\n\n      if ('data' in response) {\n        // Handle success\n        toggleNotification({\n          type: 'success',\n          message: formatMessage({\n            id: 'content-releases.content-manager-edit-view.add-to-release.notification.success',\n            defaultMessage: 'Entry added to release',\n          }),\n        });\n\n        return;\n      }\n\n      if ('error' in response) {\n        throw response.error;\n      }\n    },\n  });\n\n  const {\n    edit: { options },\n  } = useDocumentLayout(model);\n\n  // Project is not EE or contentType does not have draftAndPublish enabled\n  if (!window.strapi.isEE || !options?.draftAndPublish || !canCreateAction) {\n    return null;\n  }\n\n  if (collectionType === 'collection-types' && (!documentId || documentId === 'create')) {\n    return null;\n  }\n\n  return {\n    label: formatMessage({\n      id: 'content-releases.content-manager-edit-view.add-to-release',\n      defaultMessage: 'Add to release',\n    }),\n    icon: <PaperPlane />,\n    // Entry is creating so we don't want to allow adding it to a release\n    disabled: !document,\n    position: ['panel', 'table-row'],\n    dialog: {\n      type: 'modal',\n      title: formatMessage({\n        id: 'content-releases.content-manager-edit-view.add-to-release',\n        defaultMessage: 'Add to release',\n      }),\n      content: (\n        <AddActionToReleaseModal\n          contentType={model}\n          documentId={documentId}\n          onInputChange={formik.setFieldValue}\n          values={formik.values}\n        />\n      ),\n      footer: ({ onClose }) => (\n        <Modal.Footer>\n          <Button onClick={onClose} variant=\"tertiary\" name=\"cancel\">\n            {formatMessage({\n              id: 'content-releases.content-manager-edit-view.add-to-release.cancel-button',\n              defaultMessage: 'Cancel',\n            })}\n          </Button>\n          <Button\n            type=\"submit\"\n            // @ts-expect-error - formik ReactEvent types don't match button onClick types as they expect a MouseEvent\n            onClick={(e) => handleSubmit(e, onClose)}\n            disabled={!formik.values.releaseId}\n            loading={isLoading}\n          >\n            {formatMessage({\n              id: 'content-releases.content-manager-edit-view.add-to-release.continue-button',\n              defaultMessage: 'Continue',\n            })}\n          </Button>\n        </Modal.Footer>\n      ),\n    },\n  };\n};\n\nexport { ReleaseActionModalForm };\n", "import * as React from 'react';\n\nimport {\n  useAPIErrorH<PERSON><PERSON>,\n  useNotification,\n  useQueryParams,\n  useRBAC,\n  isFetchError,\n} from '@strapi/admin/strapi-admin';\nimport { unstable_useContentManagerContext as useContentManagerContext } from '@strapi/content-manager/strapi-admin';\nimport {\n  Box,\n  Button,\n  Flex,\n  SingleSelect,\n  SingleSelectOption,\n  Modal,\n  Field,\n} from '@strapi/design-system';\nimport { Formik, Form } from 'formik';\nimport { useIntl } from 'react-intl';\n\nimport { CreateManyReleaseActions } from '../../../shared/contracts/release-actions';\nimport { PERMISSIONS as releasePermissions } from '../constants';\nimport { useCreateManyReleaseActionsMutation, useGetReleasesQuery } from '../services/release';\n\nimport {\n  type FormValues,\n  INITIAL_VALUES,\n  RELEASE_ACTION_FORM_SCHEMA,\n  NoReleases,\n} from './ReleaseActionModal';\nimport { ReleaseActionOptions } from './ReleaseActionOptions';\n\nimport type { BulkActionComponent } from '@strapi/content-manager/strapi-admin';\nimport type { UID } from '@strapi/types';\n\nconst getContentPermissions = (subject: string) => {\n  const permissions = {\n    publish: [\n      {\n        action: 'plugin::content-manager.explorer.publish',\n        subject,\n        id: '',\n        actionParameters: {},\n        properties: {},\n        conditions: [],\n      },\n    ],\n  };\n\n  return permissions;\n};\n\nconst ReleaseAction: BulkActionComponent = ({ documents, model }) => {\n  const { formatMessage } = useIntl();\n  const { toggleNotification } = useNotification();\n  const { formatAPIError } = useAPIErrorHandler();\n  const [{ query }] = useQueryParams<{ plugins?: { i18n?: { locale?: string } } }>();\n  const contentPermissions = getContentPermissions(model);\n  const {\n    allowedActions: { canPublish },\n  } = useRBAC(contentPermissions);\n  const {\n    allowedActions: { canCreate },\n  } = useRBAC(releasePermissions);\n  const { hasDraftAndPublish } = useContentManagerContext();\n\n  // Get all the releases not published\n  const response = useGetReleasesQuery();\n  const releases = response.data?.data;\n  const [createManyReleaseActions, { isLoading }] = useCreateManyReleaseActionsMutation();\n  const documentIds = documents.map((doc) => doc.documentId);\n\n  const handleSubmit = async (values: FormValues) => {\n    const locale = query.plugins?.i18n?.locale;\n\n    const releaseActionEntries: CreateManyReleaseActions.Request['body'] = documentIds.map(\n      (entryDocumentId) => ({\n        type: values.type,\n        contentType: model as UID.ContentType,\n        entryDocumentId,\n        locale,\n      })\n    );\n\n    const response = await createManyReleaseActions({\n      body: releaseActionEntries,\n      params: { releaseId: values.releaseId },\n    });\n\n    if ('data' in response) {\n      // Handle success\n\n      const notificationMessage = formatMessage(\n        {\n          id: 'content-releases.content-manager-list-view.add-to-release.notification.success.message',\n          defaultMessage:\n            '{entriesAlreadyInRelease} out of {totalEntries} entries were already in the release.',\n        },\n        {\n          entriesAlreadyInRelease: response.data.meta.entriesAlreadyInRelease,\n          totalEntries: response.data.meta.totalEntries,\n        }\n      );\n\n      const notification = {\n        type: 'success' as const,\n        title: formatMessage(\n          {\n            id: 'content-releases.content-manager-list-view.add-to-release.notification.success.title',\n            defaultMessage: 'Successfully added to release.',\n          },\n          {\n            entriesAlreadyInRelease: response.data.meta.entriesAlreadyInRelease,\n            totalEntries: response.data.meta.totalEntries,\n          }\n        ),\n        message: response.data.meta.entriesAlreadyInRelease ? notificationMessage : '',\n      };\n\n      toggleNotification(notification);\n\n      return true;\n    }\n\n    if ('error' in response) {\n      if (isFetchError(response.error)) {\n        // Handle fetch error\n        toggleNotification({\n          type: 'warning',\n          message: formatAPIError(response.error),\n        });\n      } else {\n        // Handle generic error\n        toggleNotification({\n          type: 'warning',\n          message: formatMessage({ id: 'notification.error', defaultMessage: 'An error occurred' }),\n        });\n      }\n    }\n  };\n\n  if (!hasDraftAndPublish || !canCreate || !canPublish) return null;\n\n  return {\n    actionType: 'release',\n    variant: 'tertiary',\n    label: formatMessage({\n      id: 'content-manager-list-view.add-to-release',\n      defaultMessage: 'Add to Release',\n    }),\n    dialog: {\n      type: 'modal',\n      title: formatMessage({\n        id: 'content-manager-list-view.add-to-release',\n        defaultMessage: 'Add to Release',\n      }),\n      content: ({ onClose }) => {\n        return (\n          <Formik\n            onSubmit={async (values) => {\n              const data = await handleSubmit(values);\n              if (data) {\n                return onClose();\n              }\n            }}\n            validationSchema={RELEASE_ACTION_FORM_SCHEMA}\n            initialValues={INITIAL_VALUES}\n          >\n            {({ values, setFieldValue }) => (\n              <Form>\n                {releases?.length === 0 ? (\n                  <NoReleases />\n                ) : (\n                  <Modal.Body>\n                    <Flex direction=\"column\" alignItems=\"stretch\" gap={2}>\n                      <Box paddingBottom={6}>\n                        <Field.Root required>\n                          <Field.Label>\n                            {formatMessage({\n                              id: 'content-releases.content-manager-list-view.add-to-release.select-label',\n                              defaultMessage: 'Select a release',\n                            })}\n                          </Field.Label>\n                          <SingleSelect\n                            placeholder={formatMessage({\n                              id: 'content-releases.content-manager-list-view.add-to-release.select-placeholder',\n                              defaultMessage: 'Select',\n                            })}\n                            onChange={(value) => setFieldValue('releaseId', value)}\n                            value={values.releaseId}\n                          >\n                            {releases?.map((release) => (\n                              <SingleSelectOption key={release.id} value={release.id}>\n                                {release.name}\n                              </SingleSelectOption>\n                            ))}\n                          </SingleSelect>\n                        </Field.Root>\n                      </Box>\n                      <Field.Label>\n                        {formatMessage({\n                          id: 'content-releases.content-manager-list-view.add-to-release.action-type-label',\n                          defaultMessage: 'What do you want to do with these entries?',\n                        })}\n                      </Field.Label>\n                      <ReleaseActionOptions\n                        selected={values.type}\n                        handleChange={(e) => setFieldValue('type', e.target.value)}\n                        name=\"type\"\n                      />\n                    </Flex>\n                  </Modal.Body>\n                )}\n                <Modal.Footer>\n                  <Button onClick={onClose} variant=\"tertiary\" name=\"cancel\">\n                    {formatMessage({\n                      id: 'content-releases.content-manager-list-view.add-to-release.cancel-button',\n                      defaultMessage: 'Cancel',\n                    })}\n                  </Button>\n                  {/** * TODO: Ideally we would use isValid from Formik to disable the button,\n                  however currently it always returns true * for yup.string().required(), even when\n                  the value is falsy (including empty string) */}\n                  <Button type=\"submit\" disabled={!values.releaseId} loading={isLoading}>\n                    {formatMessage({\n                      id: 'content-releases.content-manager-list-view.add-to-release.continue-button',\n                      defaultMessage: 'Continue',\n                    })}\n                  </Button>\n                </Modal.Footer>\n              </Form>\n            )}\n          </Formik>\n        );\n      },\n    },\n  };\n};\n\nexport { ReleaseAction };\n", "import * as React from 'react';\n\nimport { useTable, useQueryParams } from '@strapi/admin/strapi-admin';\nimport { ListFieldLayout, ListLayout } from '@strapi/content-manager/strapi-admin';\nimport { Box, Popover, Typography, Button, Link } from '@strapi/design-system';\nimport { CaretDown } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\n\nimport { useGetMappedEntriesInReleasesQuery } from '../services/release';\n\nimport type { Modules, UID } from '@strapi/types';\n\n/* -------------------------------------------------------------------------------------------------\n * useReleasesList\n * -----------------------------------------------------------------------------------------------*/\ninterface QueryParams {\n  plugins?: {\n    i18n?: {\n      locale: string;\n    };\n  };\n}\n\nconst useReleasesList = (contentTypeUid: UID.ContentType, documentId: Modules.Documents.ID) => {\n  const listViewData = useTable('ListView', (state) => state.rows);\n  const documentIds = listViewData.map((entry) => entry.documentId);\n  const [{ query }] = useQueryParams();\n  const locale = (query as QueryParams)?.plugins?.i18n?.locale || undefined;\n\n  const response = useGetMappedEntriesInReleasesQuery(\n    { contentTypeUid, documentIds, locale },\n    { skip: !documentIds || !contentTypeUid || documentIds.length === 0 }\n  );\n\n  const mappedEntriesInReleases = response.data || {};\n\n  return mappedEntriesInReleases?.[documentId] || [];\n};\n\n/* -------------------------------------------------------------------------------------------------\n * addColumnToTableHook\n * -----------------------------------------------------------------------------------------------*/\n\ninterface AddColumnToTableHookArgs {\n  layout: ListLayout;\n  displayedHeaders: ListFieldLayout[];\n}\n\nconst addColumnToTableHook = ({ displayedHeaders, layout }: AddColumnToTableHookArgs) => {\n  const { options } = layout;\n\n  if (!options?.draftAndPublish) {\n    return { displayedHeaders, layout };\n  }\n\n  return {\n    displayedHeaders: [\n      ...displayedHeaders,\n      {\n        searchable: false,\n        sortable: false,\n        name: 'releases',\n        label: {\n          id: 'content-releases.content-manager.list-view.releases.header',\n          defaultMessage: 'To be released in',\n        },\n        cellFormatter: (\n          props: Modules.Documents.AnyDocument,\n          _: any,\n          { model }: { model: UID.ContentType }\n        ) => <ReleaseListCell {...props} model={model} />,\n      },\n    ],\n    layout,\n  };\n};\n\n/* -------------------------------------------------------------------------------------------------\n * ReleaseListCell\n * -----------------------------------------------------------------------------------------------*/\n\ninterface ReleaseListCellProps extends Modules.Documents.AnyDocument {\n  documentId: Modules.Documents.ID;\n  model: UID.ContentType;\n}\n\nconst ReleaseListCell = ({ documentId, model }: ReleaseListCellProps) => {\n  const releases = useReleasesList(model, documentId);\n  const { formatMessage } = useIntl();\n\n  return (\n    <Popover.Root>\n      <Popover.Trigger>\n        <Button\n          variant=\"ghost\"\n          onClick={(e: React.MouseEvent<HTMLElement>) => e.stopPropagation()}\n          // TODO: find a way in the DS to define the widht and height of the icon\n          endIcon={releases.length > 0 ? <CaretDown width=\"1.2rem\" height=\"1.2rem\" /> : null}\n        >\n          <Typography\n            style={{ maxWidth: '252px', cursor: 'pointer' }}\n            textColor=\"neutral800\"\n            fontWeight=\"regular\"\n          >\n            {releases.length > 0\n              ? formatMessage(\n                  {\n                    id: 'content-releases.content-manager.list-view.releases-number',\n                    defaultMessage: '{number} {number, plural, one {release} other {releases}}',\n                  },\n                  {\n                    number: releases.length,\n                  }\n                )\n              : '-'}\n          </Typography>\n        </Button>\n      </Popover.Trigger>\n      <Popover.Content>\n        <ul>\n          {releases.map(({ id, name }) => (\n            <Box key={id} padding={3} tag=\"li\">\n              <Link href={`/admin/plugins/content-releases/${id}`} isExternal={false}>\n                {name}\n              </Link>\n            </Box>\n          ))}\n        </ul>\n      </Popover.Content>\n    </Popover.Root>\n  );\n};\n\nexport { ReleaseListCell, addColumnToTableHook };\nexport type { ReleaseListCellProps };\n", "import { useRBAC, useQueryParams } from '@strapi/admin/strapi-admin';\nimport { unstable_useDocumentLayout as useDocumentLayout } from '@strapi/content-manager/strapi-admin';\nimport { Box, Flex, Typography } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\n\nimport { PERMISSIONS } from '../constants';\nimport { useGetReleasesForEntryQuery } from '../services/release';\nimport { getTimezoneOffset } from '../utils/time';\n\nimport { ReleaseActionMenu } from './ReleaseActionMenu';\n\nimport type { PanelComponent, PanelComponentProps } from '@strapi/content-manager/strapi-admin';\n\nconst Panel: PanelComponent = ({\n  model,\n  document,\n  documentId,\n  collectionType,\n}: PanelComponentProps) => {\n  const [{ query }] = useQueryParams<{ plugins: { i18n: { locale: string } } }>();\n  const locale = query.plugins?.i18n?.locale;\n\n  const {\n    edit: { options },\n  } = useDocumentLayout(model);\n  const { formatMessage, formatDate, formatTime } = useIntl();\n\n  const { allowedActions } = useRBAC(PERMISSIONS);\n  const { canRead, canDeleteAction } = allowedActions;\n\n  const response = useGetReleasesForEntryQuery(\n    {\n      contentType: model,\n      entryDocumentId: documentId,\n      locale,\n      hasEntryAttached: true,\n    },\n    {\n      skip: !document,\n    }\n  );\n  const releases = response.data?.data;\n\n  const getReleaseColorVariant = (\n    actionType: 'publish' | 'unpublish',\n    shade: '100' | '200' | '600'\n  ) => {\n    if (actionType === 'unpublish') {\n      return `secondary${shade}`;\n    }\n\n    return `success${shade}`;\n  };\n\n  // Project is not EE or contentType does not have draftAndPublish enabled\n  if (!window.strapi.isEE || !options?.draftAndPublish || !canRead) {\n    return null;\n  }\n\n  if (collectionType === 'collection-types' && (!documentId || documentId === 'create')) {\n    return null;\n  }\n\n  if (!releases || releases.length === 0) {\n    return null;\n  }\n\n  return {\n    title: formatMessage({\n      id: 'content-releases.plugin.name',\n      defaultMessage: 'Releases',\n    }),\n    content: (\n      <Flex direction=\"column\" alignItems=\"stretch\" gap={3} width=\"100%\">\n        {releases?.map((release) => (\n          <Flex\n            key={release.id}\n            direction=\"column\"\n            alignItems=\"start\"\n            borderWidth=\"1px\"\n            borderStyle=\"solid\"\n            borderColor={getReleaseColorVariant(release.actions[0].type, '200')}\n            overflow=\"hidden\"\n            hasRadius\n          >\n            <Box\n              paddingTop={3}\n              paddingBottom={3}\n              paddingLeft={4}\n              paddingRight={4}\n              background={getReleaseColorVariant(release.actions[0].type, '100')}\n              width=\"100%\"\n            >\n              <Typography\n                fontSize={1}\n                variant=\"pi\"\n                textColor={getReleaseColorVariant(release.actions[0].type, '600')}\n              >\n                {formatMessage(\n                  {\n                    id: 'content-releases.content-manager-edit-view.list-releases.title',\n                    defaultMessage:\n                      '{isPublish, select, true {Will be published in} other {Will be unpublished in}}',\n                  },\n                  { isPublish: release.actions[0].type === 'publish' }\n                )}\n              </Typography>\n            </Box>\n            <Flex padding={4} direction=\"column\" gap={2} width=\"100%\" alignItems=\"flex-start\">\n              <Typography fontSize={2} fontWeight=\"bold\" variant=\"omega\" textColor=\"neutral700\">\n                {release.name}\n              </Typography>\n              {release.scheduledAt && release.timezone && (\n                <Typography variant=\"pi\" textColor=\"neutral600\">\n                  {formatMessage(\n                    {\n                      id: 'content-releases.content-manager-edit-view.scheduled.date',\n                      defaultMessage: '{date} at {time} ({offset})',\n                    },\n                    {\n                      date: formatDate(new Date(release.scheduledAt), {\n                        day: '2-digit',\n                        month: '2-digit',\n                        year: 'numeric',\n                        timeZone: release.timezone,\n                      }),\n                      time: formatTime(new Date(release.scheduledAt), {\n                        hourCycle: 'h23',\n                        timeZone: release.timezone,\n                      }),\n                      offset: getTimezoneOffset(release.timezone, new Date(release.scheduledAt)),\n                    }\n                  )}\n                </Typography>\n              )}\n              {canDeleteAction ? (\n                <ReleaseActionMenu.Root hasTriggerBorder>\n                  <ReleaseActionMenu.EditReleaseItem releaseId={release.id} />\n                  <ReleaseActionMenu.DeleteReleaseActionItem\n                    releaseId={release.id}\n                    actionId={release.actions[0].id}\n                  />\n                </ReleaseActionMenu.Root>\n              ) : null}\n            </Flex>\n          </Flex>\n        ))}\n      </Flex>\n    ),\n  };\n};\n\nexport { Panel };\n", "type TradOptions = Record<string, string>;\n\nconst prefixPluginTranslations = (trad: TradOptions, pluginId: string): TradOptions => {\n  if (!pluginId) {\n    throw new TypeError(\"pluginId can't be empty\");\n  }\n  return Object.keys(trad).reduce((acc, current) => {\n    acc[`${pluginId}.${current}`] = trad[current];\n    return acc;\n  }, {} as TradOptions);\n};\n\nexport { prefixPluginTranslations };\n", "import { PaperPlane } from '@strapi/icons';\n\nimport { ReleaseAction } from './components/ReleaseAction';\nimport { ReleaseActionModalForm } from './components/ReleaseActionModal';\nimport { addColumnToTableHook } from './components/ReleaseListCell';\nimport { Panel as ReleasesPanel } from './components/ReleasesPanel';\nimport { PERMISSIONS } from './constants';\nimport { pluginId } from './pluginId';\nimport { prefixPluginTranslations } from './utils/prefixPluginTranslations';\n\nimport type { StrapiApp } from '@strapi/admin/strapi-admin';\nimport type {\n  DocumentActionComponent,\n  BulkActionComponent,\n} from '@strapi/content-manager/strapi-admin';\nimport type { Plugin } from '@strapi/types';\n\n// eslint-disable-next-line import/no-default-export\nconst admin: Plugin.Config.AdminInput = {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  register(app: StrapiApp) {\n    /**\n     * Hook that adds the locale column in the Release Details table\n     * @constant\n     * @type {string}\n     */\n    app.createHook('ContentReleases/pages/ReleaseDetails/add-locale-in-releases');\n\n    if (window.strapi.features.isEnabled('cms-content-releases')) {\n      app.addMenuLink({\n        to: `plugins/${pluginId}`,\n        icon: PaperPlane,\n        intlLabel: {\n          id: `${pluginId}.plugin.name`,\n          defaultMessage: 'Releases',\n        },\n        Component: () => import('./pages/App').then((mod) => ({ default: mod.App })),\n        permissions: PERMISSIONS.main,\n        position: 2,\n      });\n\n      // Insert the releases container into the CM's sidebar on the Edit View\n      const contentManagerPluginApis = app.getPlugin('content-manager').apis;\n      if (\n        'addEditViewSidePanel' in contentManagerPluginApis &&\n        typeof contentManagerPluginApis.addEditViewSidePanel === 'function'\n      ) {\n        contentManagerPluginApis.addEditViewSidePanel([ReleasesPanel]);\n      }\n\n      // Insert the \"add to release\" action into the CM's Edit View\n      if (\n        'addDocumentAction' in contentManagerPluginApis &&\n        typeof contentManagerPluginApis.addDocumentAction === 'function'\n      ) {\n        contentManagerPluginApis.addDocumentAction((actions: DocumentActionComponent[]) => {\n          const indexOfDeleteAction = actions.findIndex((action) => action.type === 'unpublish');\n          actions.splice(indexOfDeleteAction, 0, ReleaseActionModalForm);\n          return actions;\n        });\n      }\n\n      app.addSettingsLink('global', {\n        id: pluginId,\n        to: 'releases',\n        intlLabel: {\n          id: `${pluginId}.plugin.name`,\n          defaultMessage: 'Releases',\n        },\n        permissions: [],\n        async Component() {\n          const { ProtectedReleasesSettingsPage } = await import('./pages/ReleasesSettingsPage');\n          return { default: ProtectedReleasesSettingsPage };\n        },\n      });\n\n      if (\n        'addBulkAction' in contentManagerPluginApis &&\n        typeof contentManagerPluginApis.addBulkAction === 'function'\n      ) {\n        contentManagerPluginApis.addBulkAction((actions: BulkActionComponent[]) => {\n          // We want to add this action to just before the delete action all the time\n          const deleteActionIndex = actions.findIndex((action) => action.type === 'delete');\n\n          actions.splice(deleteActionIndex, 0, ReleaseAction);\n          return actions;\n        });\n      }\n\n      // Hook that adds a column into the CM's LV table\n      app.registerHook('Admin/CM/pages/ListView/inject-column-in-table', addColumnToTableHook);\n    } else if (\n      !window.strapi.features.isEnabled('cms-content-releases') &&\n      window.strapi?.flags?.promoteEE\n    ) {\n      app.addSettingsLink('global', {\n        id: pluginId,\n        to: '/plugins/purchase-content-releases',\n        intlLabel: {\n          id: `${pluginId}.plugin.name`,\n          defaultMessage: 'Releases',\n        },\n        permissions: [],\n        async Component() {\n          const { PurchaseContentReleases } = await import('./pages/PurchaseContentReleases');\n          return { default: PurchaseContentReleases };\n        },\n        licenseOnly: true,\n      });\n    }\n  },\n  async registerTrads({ locales }: { locales: string[] }) {\n    const importedTrads = await Promise.all(\n      locales.map((locale) => {\n        return import(`./translations/${locale}.json`)\n          .then(({ default: data }) => {\n            return {\n              data: prefixPluginTranslations(data, 'content-releases'),\n              locale,\n            };\n          })\n          .catch(() => {\n            return {\n              data: {},\n              locale,\n            };\n          });\n      })\n    );\n\n    return Promise.resolve(importedTrads);\n  },\n};\n\n// eslint-disable-next-line import/no-default-export\nexport default admin;\n", "import type { SanitizedAdminUser } from '@strapi/admin/strapi-admin';\n\n/**\n * Retrieves the display name of an admin panel user\n */\nconst getDisplayName = ({\n  firstname,\n  lastname,\n  username,\n  email,\n}: Partial<\n  Pick<SanitizedAdminUser, 'firstname' | 'lastname' | 'username' | 'email'>\n> = {}): string => {\n  if (username) {\n    return username;\n  }\n\n  // firstname is not required if the user is created with a username\n  if (firstname) {\n    return `${firstname} ${lastname ?? ''}`.trim();\n  }\n\n  return email ?? '';\n};\n\nexport { getDisplayName };\n", "export const STAGE_ATTRIBUTE_NAME = 'strapi_stage';\nexport const ASSIGNEE_ATTRIBUTE_NAME = 'strapi_assignee';\n", "import * as React from 'react';\n\nimport {\n  useNotification,\n  useAPIError<PERSON><PERSON><PERSON>,\n  useRBAC,\n  useAdminUsers,\n  useQueryParams,\n} from '@strapi/admin/strapi-admin';\nimport { unstable_useDocument } from '@strapi/content-manager/strapi-admin';\nimport { Combobox, ComboboxOption, Field, VisuallyHidden } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\nimport { useParams } from 'react-router-dom';\n\nimport { useTypedSelector } from '../../../../../modules/hooks';\nimport { useUpdateAssigneeMutation } from '../../../../../services/content-manager';\nimport { buildValidParams } from '../../../../../utils/api';\nimport { getDisplayName } from '../../../../../utils/users';\n\nimport { ASSIGNEE_ATTRIBUTE_NAME } from './constants';\n\nconst AssigneeSelect = ({ isCompact }: { isCompact?: boolean }) => {\n  const {\n    collectionType = '',\n    id,\n    slug: model = '',\n  } = useParams<{ collectionType: string; slug: string; id: string }>();\n  const permissions = useTypedSelector((state) => state.admin_app.permissions);\n  const { formatMessage } = useIntl();\n  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();\n  const { toggleNotification } = useNotification();\n  const {\n    allowedActions: { canRead },\n    isLoading: isLoadingPermissions,\n  } = useRBAC(permissions.settings?.users);\n  const [{ query }] = useQueryParams();\n  const params = React.useMemo(() => buildValidParams(query), [query]);\n  const {\n    data,\n    isLoading: isLoadingUsers,\n    isError,\n  } = useAdminUsers(undefined, {\n    skip: isLoadingPermissions || !canRead,\n  });\n  const { document } = unstable_useDocument(\n    {\n      collectionType,\n      model,\n      documentId: id,\n    },\n    {\n      skip: !id && collectionType !== 'single-types',\n    }\n  );\n\n  const users = data?.users || [];\n\n  const currentAssignee = document ? document[ASSIGNEE_ATTRIBUTE_NAME] : null;\n\n  const [updateAssignee, { error, isLoading: isMutating }] = useUpdateAssigneeMutation();\n\n  if (!collectionType || !model || !document?.documentId) {\n    return null;\n  }\n\n  const handleChange = async (assigneeId: string | null) => {\n    // a simple way to avoid erroneous updates\n    if (currentAssignee?.id === assigneeId) {\n      return;\n    }\n\n    const res = await updateAssignee({\n      slug: collectionType,\n      model,\n      id: document.documentId,\n      params,\n      data: {\n        id: assigneeId ? parseInt(assigneeId, 10) : null,\n      },\n    });\n\n    if ('data' in res) {\n      toggleNotification({\n        type: 'success',\n        message: formatMessage({\n          id: 'content-manager.reviewWorkflows.assignee.notification.saved',\n          defaultMessage: 'Assignee updated',\n        }),\n      });\n    }\n\n    if (isCompact && 'error' in res) {\n      toggleNotification({\n        type: 'danger',\n        message: formatAPIError(res.error),\n      });\n    }\n  };\n\n  const isDisabled =\n    (!isLoadingPermissions && !isLoadingUsers && users.length === 0) || !document.documentId;\n  const isLoading = isLoadingUsers || isLoadingPermissions || isMutating;\n\n  const assigneeLabel = formatMessage({\n    id: 'content-manager.reviewWorkflows.assignee.label',\n    defaultMessage: 'Assignee',\n  });\n  const assigneeClearLabel = formatMessage({\n    id: 'content-manager.reviewWorkflows.assignee.clear',\n    defaultMessage: 'Clear assignee',\n  });\n  const assigneePlaceholder = formatMessage({\n    id: 'content-manager.reviewWorkflows.assignee.placeholder',\n    defaultMessage: 'Select…',\n  });\n\n  if (isCompact) {\n    return (\n      <Field.Root name={ASSIGNEE_ATTRIBUTE_NAME} id={ASSIGNEE_ATTRIBUTE_NAME}>\n        <VisuallyHidden>\n          <Field.Label>{assigneeLabel}</Field.Label>\n        </VisuallyHidden>\n        <Combobox\n          clearLabel={assigneeClearLabel}\n          disabled={isDisabled}\n          value={currentAssignee ? currentAssignee.id.toString() : null}\n          onChange={handleChange}\n          onClear={() => handleChange(null)}\n          placeholder={assigneePlaceholder}\n          loading={isLoading || isLoadingPermissions || isMutating}\n          size=\"S\"\n        >\n          {users.map((user) => {\n            return (\n              <ComboboxOption\n                key={user.id}\n                value={user.id.toString()}\n                textValue={getDisplayName(user)}\n              >\n                {getDisplayName(user)}\n              </ComboboxOption>\n            );\n          })}\n        </Combobox>\n      </Field.Root>\n    );\n  }\n\n  return (\n    <Field.Root\n      name={ASSIGNEE_ATTRIBUTE_NAME}\n      id={ASSIGNEE_ATTRIBUTE_NAME}\n      error={\n        ((isError &&\n          canRead &&\n          formatMessage({\n            id: 'content-manager.reviewWorkflows.assignee.error',\n            defaultMessage: 'An error occurred while fetching users',\n          })) ||\n          (error && formatAPIError(error))) ??\n        undefined\n      }\n    >\n      <Field.Label>{assigneeLabel}</Field.Label>\n      <Combobox\n        clearLabel={assigneeClearLabel}\n        disabled={\n          (!isLoadingPermissions && !isLoading && users.length === 0) || !document.documentId\n        }\n        value={currentAssignee ? currentAssignee.id.toString() : null}\n        onChange={handleChange}\n        onClear={() => handleChange(null)}\n        placeholder={assigneePlaceholder}\n        loading={isLoading || isLoadingPermissions || isMutating}\n      >\n        {users.map((user) => {\n          return (\n            <ComboboxOption\n              key={user.id}\n              value={user.id.toString()}\n              textValue={getDisplayName(user)}\n            >\n              {getDisplayName(user)}\n            </ComboboxOption>\n          );\n        })}\n      </Combobox>\n      <Field.Error />\n    </Field.Root>\n  );\n};\n\nexport { AssigneeSelect };\n", "import * as React from 'react';\n\nimport { useNotification, use<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, useQueryParams } from '@strapi/admin/strapi-admin';\nimport { useLicenseLimits } from '@strapi/admin/strapi-admin/ee';\nimport { unstable_useDocument } from '@strapi/content-manager/strapi-admin';\nimport {\n  SingleSelect,\n  type SingleSelectProps,\n  SingleSelectOption,\n  Field,\n  Flex,\n  Loader,\n  Typography,\n  VisuallyHidden,\n  Tooltip,\n} from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\nimport { useParams } from 'react-router-dom';\n\nimport { Stage } from '../../../../../../../shared/contracts/review-workflows';\nimport { LimitsModal, LimitsModalProps } from '../../../../../components/LimitsModal';\nimport {\n  CHARGEBEE_STAGES_PER_WORKFLOW_ENTITLEMENT_NAME,\n  CHARGEBEE_WORKFLOW_ENTITLEMENT_NAME,\n} from '../../../../../constants';\nimport { useGetStagesQuery, useUpdateStageMutation } from '../../../../../services/content-manager';\nimport { buildValidParams } from '../../../../../utils/api';\nimport { getStageColorByHex } from '../../../../../utils/colors';\n\nimport { STAGE_ATTRIBUTE_NAME } from './constants';\n\nimport type { Data } from '@strapi/types';\n\n/* -------------------------------------------------------------------------------------------------\n * LimitModals\n * -----------------------------------------------------------------------------------------------*/\n\nconst WorkflowLimitModal = ({ open, onOpenChange }: LimitsModalProps) => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <LimitsModal.Root open={open} onOpenChange={onOpenChange}>\n      <LimitsModal.Title>\n        {formatMessage({\n          id: 'content-manager.reviewWorkflows.workflows.limit.title',\n          defaultMessage: 'You’ve reached the limit of workflows in your plan',\n        })}\n      </LimitsModal.Title>\n\n      <LimitsModal.Body>\n        {formatMessage({\n          id: 'content-manager.reviewWorkflows.workflows.limit.body',\n          defaultMessage: 'Delete a workflow or contact Sales to enable more workflows.',\n        })}\n      </LimitsModal.Body>\n    </LimitsModal.Root>\n  );\n};\n\nconst StageLimitModal = ({ open, onOpenChange }: LimitsModalProps) => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <LimitsModal.Root open={open} onOpenChange={onOpenChange}>\n      <LimitsModal.Title>\n        {formatMessage({\n          id: 'content-manager.reviewWorkflows.stages.limit.title',\n          defaultMessage: 'You have reached the limit of stages for this workflow in your plan',\n        })}\n      </LimitsModal.Title>\n\n      <LimitsModal.Body>\n        {formatMessage({\n          id: 'content-manager.reviewWorkflows.stages.limit.body',\n          defaultMessage: 'Try deleting some stages or contact Sales to enable more stages.',\n        })}\n      </LimitsModal.Body>\n    </LimitsModal.Root>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * StageSelect\n * -----------------------------------------------------------------------------------------------*/\n\nconst Select = ({\n  stages,\n  activeWorkflowStage,\n  isLoading,\n  ...props\n}: SingleSelectProps & { stages: Stage[]; activeWorkflowStage: Stage; isLoading: boolean }) => {\n  const { formatMessage } = useIntl();\n  const { themeColorName } = getStageColorByHex(activeWorkflowStage?.color) ?? {};\n\n  return (\n    <SingleSelect\n      disabled={stages.length === 0}\n      placeholder={formatMessage({\n        id: 'content-manager.reviewWorkflows.assignee.placeholder',\n        defaultMessage: 'Select…',\n      })}\n      startIcon={\n        activeWorkflowStage && (\n          <Flex\n            tag=\"span\"\n            height={2}\n            background={activeWorkflowStage?.color}\n            borderColor={themeColorName === 'neutral0' ? 'neutral150' : undefined}\n            hasRadius\n            shrink={0}\n            width={2}\n            marginRight=\"-3px\"\n          />\n        )\n      }\n      // @ts-expect-error – `customizeContent` is not correctly typed in the DS.\n      customizeContent={() => {\n        return (\n          <Flex tag=\"span\" justifyContent=\"space-between\" alignItems=\"center\" width=\"100%\">\n            <Typography textColor=\"neutral800\" ellipsis lineHeight=\"inherit\">\n              {activeWorkflowStage?.name ?? ''}\n            </Typography>\n            {isLoading ? <Loader small style={{ display: 'flex' }} data-testid=\"loader\" /> : null}\n          </Flex>\n        );\n      }}\n      {...props}\n    >\n      {stages.map(({ id, color, name }) => {\n        const { themeColorName } = getStageColorByHex(color) ?? {};\n\n        return (\n          <SingleSelectOption\n            key={id}\n            startIcon={\n              <Flex\n                height={2}\n                background={color}\n                borderColor={themeColorName === 'neutral0' ? 'neutral150' : undefined}\n                hasRadius\n                shrink={0}\n                width={2}\n              />\n            }\n            value={id}\n            textValue={name}\n          >\n            {name}\n          </SingleSelectOption>\n        );\n      })}\n    </SingleSelect>\n  );\n};\n\nexport const StageSelect = ({ isCompact }: { isCompact?: boolean }) => {\n  const {\n    collectionType = '',\n    slug: model = '',\n    id = '',\n  } = useParams<{\n    collectionType: string;\n    slug: string;\n    id: string;\n  }>();\n  const { formatMessage } = useIntl();\n  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();\n  const { toggleNotification } = useNotification();\n  const [{ query }] = useQueryParams();\n  const params = React.useMemo(() => buildValidParams(query), [query]);\n  const { document, isLoading: isLoadingDocument } = unstable_useDocument(\n    {\n      collectionType,\n      model,\n      documentId: id,\n    },\n    {\n      skip: !id && collectionType !== 'single-types',\n    }\n  );\n\n  const { data, isLoading: isLoadingStages } = useGetStagesQuery(\n    {\n      slug: collectionType,\n      model: model,\n      // @ts-expect-error – `id` is not correctly typed in the DS.\n      id: document?.documentId,\n      params,\n    },\n    {\n      skip: !document?.documentId,\n    }\n  );\n\n  const { meta, stages = [] } = data ?? {};\n\n  const { getFeature } = useLicenseLimits();\n  const [showLimitModal, setShowLimitModal] = React.useState<'stage' | 'workflow' | null>(null);\n\n  const limits = getFeature<string>('review-workflows') ?? {};\n\n  const activeWorkflowStage = document ? document[STAGE_ATTRIBUTE_NAME] : null;\n\n  const [updateStage, { error }] = useUpdateStageMutation();\n\n  const handleChange = async (stageId: Data.ID) => {\n    try {\n      /**\n       * If the current license has a limit:\n       * check if the total count of workflows exceeds that limit and display\n       * the limits modal.\n       *\n       * If the current license does not have a limit (e.g. offline license):\n       * do nothing (for now).\n       *\n       */\n\n      if (\n        limits?.[CHARGEBEE_WORKFLOW_ENTITLEMENT_NAME] &&\n        parseInt(limits[CHARGEBEE_WORKFLOW_ENTITLEMENT_NAME], 10) < (meta?.workflowCount ?? 0)\n      ) {\n        setShowLimitModal('workflow');\n\n        /**\n         * If the current license has a limit:\n         * check if the total count of stages exceeds that limit and display\n         * the limits modal.\n         *\n         * If the current license does not have a limit (e.g. offline license):\n         * do nothing (for now).\n         *\n         */\n      } else if (\n        limits?.[CHARGEBEE_STAGES_PER_WORKFLOW_ENTITLEMENT_NAME] &&\n        parseInt(limits[CHARGEBEE_STAGES_PER_WORKFLOW_ENTITLEMENT_NAME], 10) < stages.length\n      ) {\n        setShowLimitModal('stage');\n      } else {\n        if (document?.documentId) {\n          const res = await updateStage({\n            model,\n            id: document.documentId,\n            slug: collectionType,\n            params,\n            data: { id: stageId },\n          });\n\n          if ('data' in res) {\n            toggleNotification({\n              type: 'success',\n              message: formatMessage({\n                id: 'content-manager.reviewWorkflows.stage.notification.saved',\n                defaultMessage: 'Review stage updated',\n              }),\n            });\n          }\n\n          if (isCompact && 'error' in res) {\n            toggleNotification({\n              type: 'danger',\n              message: formatAPIError(res.error),\n            });\n          }\n        }\n      }\n    } catch (error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({\n          id: 'content-manager.reviewWorkflows.stage.notification.error',\n          defaultMessage: 'An error occurred while updating the review stage',\n        }),\n      });\n    }\n  };\n\n  const isLoading = isLoadingStages || isLoadingDocument;\n\n  const reviewStageLabel = formatMessage({\n    id: 'content-manager.reviewWorkflows.stage.label',\n    defaultMessage: 'Review stage',\n  });\n  const reviewStageHint =\n    !isLoading &&\n    stages.length === 0 &&\n    // TODO: Handle errors and hints\n    formatMessage({\n      id: 'content-manager.reviewWorkflows.stages.no-transition',\n      defaultMessage: 'You don’t have the permission to update this stage.',\n    });\n\n  if (isCompact) {\n    return (\n      <>\n        <Tooltip label={reviewStageHint}>\n          <Field.Root name={STAGE_ATTRIBUTE_NAME} id={STAGE_ATTRIBUTE_NAME}>\n            <>\n              <VisuallyHidden>\n                <Field.Label>{reviewStageLabel}</Field.Label>\n              </VisuallyHidden>\n              <Select\n                stages={stages}\n                activeWorkflowStage={activeWorkflowStage}\n                isLoading={isLoading}\n                size=\"S\"\n                disabled={stages.length === 0}\n                value={activeWorkflowStage?.id}\n                onChange={handleChange}\n                placeholder={formatMessage({\n                  id: 'content-manager.reviewWorkflows.assignee.placeholder',\n                  defaultMessage: 'Select…',\n                })}\n              />\n            </>\n          </Field.Root>\n        </Tooltip>\n        <WorkflowLimitModal\n          open={showLimitModal === 'workflow'}\n          onOpenChange={() => setShowLimitModal(null)}\n        />\n        <StageLimitModal\n          open={showLimitModal === 'stage'}\n          onOpenChange={() => setShowLimitModal(null)}\n        />\n      </>\n    );\n  }\n\n  return (\n    <>\n      <Field.Root\n        hint={reviewStageHint}\n        error={(error && formatAPIError(error)) || undefined}\n        name={STAGE_ATTRIBUTE_NAME}\n        id={STAGE_ATTRIBUTE_NAME}\n      >\n        <Field.Label>{reviewStageLabel}</Field.Label>\n        <Select\n          stages={stages}\n          activeWorkflowStage={activeWorkflowStage}\n          isLoading={isLoading}\n          disabled={stages.length === 0}\n          value={activeWorkflowStage?.id}\n          onChange={handleChange}\n          placeholder={formatMessage({\n            id: 'content-manager.reviewWorkflows.assignee.placeholder',\n            defaultMessage: 'Select…',\n          })}\n        />\n        <Field.Hint />\n        <Field.Error />\n      </Field.Root>\n      <WorkflowLimitModal\n        open={showLimitModal === 'workflow'}\n        onOpenChange={() => setShowLimitModal(null)}\n      />\n      <StageLimitModal\n        open={showLimitModal === 'stage'}\n        onOpenChange={() => setShowLimitModal(null)}\n      />\n    </>\n  );\n};\n", "import { unstable_useDocumentLayout as useDocumentLayout } from '@strapi/content-manager/strapi-admin';\nimport { Flex } from '@strapi/design-system';\nimport { useParams } from 'react-router-dom';\n\nimport { AssigneeSelect } from './AssigneeSelect';\nimport { StageSelect } from './StageSelect';\n\nconst Header = () => {\n  const {\n    slug = '',\n    id,\n    collectionType,\n  } = useParams<{\n    collectionType: string;\n    slug: string;\n    id: string;\n  }>();\n\n  const {\n    edit: { options },\n  } = useDocumentLayout(slug);\n\n  if (\n    !window.strapi.isEE ||\n    !options?.reviewWorkflows ||\n    (collectionType !== 'single-types' && !id) ||\n    id === 'create'\n  ) {\n    return null;\n  }\n\n  return (\n    <Flex gap={2}>\n      <AssigneeSelect isCompact />\n      <StageSelect isCompact />\n    </Flex>\n  );\n};\n\nHeader.type = 'preview';\n\nexport { Header };\n", "import { unstable_useDocumentLayout as useDocumentLayout } from '@strapi/content-manager/strapi-admin';\nimport { Flex } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\nimport { useParams } from 'react-router-dom';\n\nimport { AssigneeSelect } from './AssigneeSelect';\nimport { StageSelect } from './StageSelect';\n\nimport type { PanelComponent } from '@strapi/content-manager/strapi-admin';\n\nconst Panel: PanelComponent = () => {\n  const {\n    slug = '',\n    id,\n    collectionType,\n  } = useParams<{\n    collectionType: string;\n    slug: string;\n    id: string;\n  }>();\n\n  const {\n    edit: { options },\n  } = useDocumentLayout(slug);\n  const { formatMessage } = useIntl();\n\n  if (\n    !window.strapi.isEE ||\n    !options?.reviewWorkflows ||\n    (collectionType !== 'single-types' && !id) ||\n    id === 'create'\n  ) {\n    return null;\n  }\n\n  return {\n    title: formatMessage({\n      id: 'content-manager.containers.edit.panels.review-workflows.title',\n      defaultMessage: 'Review Workflows',\n    }),\n    content: (\n      <Flex direction=\"column\" gap={2} alignItems=\"stretch\" width=\"100%\">\n        <AssigneeSelect />\n        <StageSelect />\n      </Flex>\n    ),\n  };\n};\n\n// @ts-expect-error – this is fine, we like to label the core panels / actions.\nPanel.type = 'review-workflows';\n\nexport { Panel };\n", "import { SanitizedAdminUser } from '@strapi/admin/strapi-admin';\nimport { Box, Flex, Typography } from '@strapi/design-system';\n\nimport { STAGE_COLOR_DEFAULT } from '../../../../constants';\nimport { getStageColorByHex } from '../../../../utils/colors';\nimport { getDisplayName } from '../../../../utils/users';\n\ninterface StageColumnProps {\n  documentId?: string;\n  id?: number;\n  strapi_stage?: {\n    color?: string;\n    name: string;\n  };\n}\n\nconst StageColumn = (props: StageColumnProps) => {\n  const { color = STAGE_COLOR_DEFAULT, name } = props.strapi_stage ?? {};\n  const { themeColorName } = getStageColorByHex(color) ?? {};\n\n  return (\n    <Flex alignItems=\"center\" gap={2} maxWidth=\"30rem\">\n      <Box\n        height={2}\n        background={color}\n        borderColor={themeColorName === 'neutral0' ? 'neutral150' : undefined}\n        hasRadius\n        shrink={0}\n        width={2}\n      />\n\n      <Typography fontWeight=\"regular\" textColor=\"neutral700\" ellipsis>\n        {name}\n      </Typography>\n    </Flex>\n  );\n};\n\ninterface AssigneeColumnProps {\n  documentId?: string;\n  id?: number;\n  strapi_assignee?: Pick<\n    SanitizedAdminUser,\n    'firstname' | 'lastname' | 'username' | 'email'\n  > | null;\n}\n\nconst AssigneeColumn = (props: AssigneeColumnProps) => {\n  const { strapi_assignee: user } = props;\n  return <Typography textColor=\"neutral800\">{user ? getDisplayName(user) : '-'}</Typography>;\n};\n\nexport { StageColumn, AssigneeColumn };\nexport type { StageColumnProps, AssigneeColumnProps };\n", "import { AssigneeFilter } from './components/AssigneeFilter';\nimport { StageFilter } from './components/StageFilter';\nimport { AssigneeColumn, StageColumn } from './components/TableColumns';\nimport { ASSIGNEE_ATTRIBUTE_NAME, STAGE_ATTRIBUTE_NAME } from './id/components/constants';\n\nimport type { Filters } from '@strapi/admin/strapi-admin';\nimport type { ListFieldLayout } from '@strapi/content-manager/strapi-admin';\nimport type { MessageDescriptor } from 'react-intl';\n\nexport const REVIEW_WORKFLOW_COLUMNS = [\n  {\n    name: STAGE_ATTRIBUTE_NAME,\n    attribute: {\n      type: 'relation',\n      relation: 'oneToMany',\n      target: 'admin::review-workflow-stage',\n    },\n    label: {\n      id: 'review-workflows.containers.list.table-headers.reviewWorkflows.stage',\n      defaultMessage: 'Review stage',\n    },\n    searchable: false,\n    sortable: true,\n    mainField: {\n      name: 'name',\n      type: 'string',\n    },\n    cellFormatter: (props) => <StageColumn {...props} />,\n  },\n  {\n    name: ASSIGNEE_ATTRIBUTE_NAME,\n    attribute: {\n      type: 'relation',\n      target: 'admin::user',\n      relation: 'oneToMany',\n    },\n    label: {\n      id: 'review-workflows.containers.list.table-headers.reviewWorkflows.assignee',\n      defaultMessage: 'Assignee',\n    },\n    searchable: false,\n    sortable: true,\n    mainField: {\n      name: 'firstname',\n      type: 'string',\n    },\n    cellFormatter: (props) => <AssigneeColumn {...props} />,\n  },\n] satisfies Array<Omit<ListFieldLayout, 'label'> & { label: MessageDescriptor }>;\n\nexport const REVIEW_WORKFLOW_FILTERS = [\n  {\n    mainField: {\n      name: 'name',\n      type: 'string',\n    },\n    input: StageFilter,\n    label: {\n      id: 'review-workflows.containers.list.table-headers.reviewWorkflows.stage',\n      defaultMessage: 'Review stage',\n    },\n    name: 'strapi_stage',\n    type: 'relation',\n  },\n\n  {\n    type: 'relation',\n    mainField: {\n      name: 'id',\n      type: 'integer',\n    },\n    input: AssigneeFilter,\n    operators: [\n      {\n        label: {\n          id: 'components.FilterOptions.FILTER_TYPES.$eq',\n          defaultMessage: 'is',\n        },\n        value: '$eq',\n      },\n      {\n        label: {\n          id: 'components.FilterOptions.FILTER_TYPES.$ne',\n          defaultMessage: 'is not',\n        },\n        value: '$ne',\n      },\n    ],\n    label: {\n      id: 'review-workflows.containers.list.table-headers.reviewWorkflows.assignee.label',\n      defaultMessage: 'Assignee',\n    },\n    name: 'strapi_assignee',\n  },\n] satisfies Array<\n  Omit<Filters.Filter, 'label' | 'operators'> & {\n    label: MessageDescriptor;\n    operators?: Array<{ value: string; label: MessageDescriptor }>;\n  }\n>;\n", "import { REVIEW_WORKFLOW_COLUMNS } from '../routes/content-manager/model/constants';\n\nimport type { ListFieldLayout, ListLayout } from '@strapi/content-manager/strapi-admin';\n\n/* -------------------------------------------------------------------------------------------------\n * addColumnToTableHook\n * -----------------------------------------------------------------------------------------------*/\ninterface AddColumnToTableHookArgs {\n  layout: ListLayout;\n  displayedHeaders: ListFieldLayout[];\n}\n\nconst addColumnToTableHook = ({ displayedHeaders, layout }: AddColumnToTableHookArgs) => {\n  const { options } = layout;\n\n  if (!options.reviewWorkflows) {\n    return { displayedHeaders, layout };\n  }\n\n  return {\n    displayedHeaders: [...displayedHeaders, ...REVIEW_WORKFLOW_COLUMNS],\n    layout,\n  };\n};\n\nexport { addColumnToTableHook };\n", "type TradOptions = Record<string, string>;\n\nconst prefixPluginTranslations = (trad: TradOptions, pluginId: string): TradOptions => {\n  if (!pluginId) {\n    throw new TypeError(\"pluginId can't be empty\");\n  }\n  return Object.keys(trad).reduce((acc, current) => {\n    acc[`${pluginId}.${current}`] = trad[current];\n    return acc;\n  }, {} as TradOptions);\n};\n\nexport { prefixPluginTranslations };\n", "import { PLUGIN_ID, FEATURE_ID } from './constants';\nimport { Header } from './routes/content-manager/model/id/components/Header';\nimport { Panel } from './routes/content-manager/model/id/components/Panel';\nimport { StageSelect } from './routes/content-manager/model/id/components/StageSelect';\nimport { addColumnToTableHook } from './utils/cm-hooks';\nimport { prefixPluginTranslations } from './utils/translations';\n\nimport type { StrapiApp } from '@strapi/admin/strapi-admin';\nimport type { Plugin } from '@strapi/types';\n\nconst admin: Plugin.Config.AdminInput = {\n  register(app: StrapiApp) {\n    if (window.strapi.features.isEnabled(FEATURE_ID)) {\n      app.registerHook('Admin/CM/pages/ListView/inject-column-in-table', addColumnToTableHook);\n\n      const contentManagerPluginApis = app.getPlugin('content-manager').apis;\n\n      if (\n        'addEditViewSidePanel' in contentManagerPluginApis &&\n        typeof contentManagerPluginApis.addEditViewSidePanel === 'function'\n      ) {\n        contentManagerPluginApis.addEditViewSidePanel([Panel]);\n      }\n\n      app.addSettingsLink('global', {\n        id: PLUGIN_ID,\n        to: `review-workflows`,\n        intlLabel: {\n          id: `${PLUGIN_ID}.plugin.name`,\n          defaultMessage: 'Review Workflows',\n        },\n        permissions: [],\n        async Component() {\n          const { Router } = await import('./router');\n          return { default: Router };\n        },\n      });\n    } else if (!window.strapi.features.isEnabled(FEATURE_ID) && window.strapi?.flags?.promoteEE) {\n      app.addSettingsLink('global', {\n        id: PLUGIN_ID,\n        to: `purchase-review-workflows`,\n        intlLabel: {\n          id: `${PLUGIN_ID}.plugin.name`,\n          defaultMessage: 'Review Workflows',\n        },\n        licenseOnly: true,\n        permissions: [],\n        async Component() {\n          const { PurchaseReviewWorkflows } = await import('./routes/purchase-review-workflows');\n          return { default: PurchaseReviewWorkflows };\n        },\n      });\n    }\n  },\n  bootstrap(app: StrapiApp) {\n    if (window.strapi.features.isEnabled(FEATURE_ID)) {\n      app.getPlugin('content-manager').injectComponent('preview', 'actions', {\n        name: 'review-workflows-assignee',\n        Component: Header,\n      });\n    }\n  },\n  async registerTrads({ locales }: { locales: string[] }) {\n    const importedTrads = await Promise.all(\n      locales.map((locale) => {\n        return import(`./translations/${locale}.json`)\n          .then(({ default: data }) => {\n            return {\n              data: prefixPluginTranslations(data, PLUGIN_ID),\n              locale,\n            };\n          })\n          .catch(() => {\n            return {\n              data: {},\n              locale,\n            };\n          });\n      })\n    );\n\n    return Promise.resolve(importedTrads);\n  },\n};\n\n// eslint-disable-next-line import/no-default-export\nexport default admin;\n", "import { RenderAdminArgs, renderAdmin } from '@strapi/admin/strapi-admin';\nimport contentTypeBuilder from '@strapi/content-type-builder/strapi-admin';\nimport contentManager from '@strapi/content-manager/strapi-admin';\nimport email from '@strapi/email/strapi-admin';\nimport upload from '@strapi/upload/strapi-admin';\nimport i18n from '@strapi/i18n/strapi-admin';\nimport contentReleases from '@strapi/content-releases/strapi-admin';\nimport reviewWorkflows from '@strapi/review-workflows/strapi-admin';\n\nconst render = (mountNode: HTMLElement | null, { plugins, ...restArgs }: RenderAdminArgs) => {\n  return renderAdmin(mountNode, {\n    ...restArgs,\n    plugins: {\n      'content-manager': contentManager,\n      'content-type-builder': contentTypeBuilder,\n      email,\n      upload,\n      contentReleases,\n      i18n,\n      reviewWorkflows,\n      ...plugins,\n    },\n  });\n};\n\nexport { render as renderAdmin };\nexport type { RenderAdminArgs };\n\nexport * from '@strapi/admin/strapi-admin';\n\nexport {\n  unstable_useDocumentLayout,\n  unstable_useDocumentActions,\n  unstable_useDocument,\n  unstable_useContentManagerContext,\n  useDocumentRBAC,\n} from '@strapi/content-manager/strapi-admin';\n\nexport {\n  private_useAutoReloadOverlayBlocker,\n  private_AutoReloadOverlayBlockerProvider,\n} from '@strapi/content-type-builder/strapi-admin';\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAEA,QAAI,UAAU,OAAO,UAAU;AAC/B,QAAI,kBAAkB;AAEtB,QAAI,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,IACb;AAEA,WAAO,UAAU;AAAA,MACb,WAAW,OAAO;AAAA,MAClB,YAAY;AAAA,QACR,SAAS,SAAU,OAAO;AACtB,iBAAO,QAAQ,KAAK,OAAO,iBAAiB,GAAG;AAAA,QACnD;AAAA,QACA,SAAS,SAAU,OAAO;AACtB,iBAAO,OAAO,KAAK;AAAA,QACvB;AAAA,MACJ;AAAA,MACA,SAAS,OAAO;AAAA,MAChB,SAAS,OAAO;AAAA,IACpB;AAAA;AAAA;;;ACtBA;AAAA;AAAA;AAEA,QAAI,UAAU;AAEd,QAAIA,OAAM,OAAO,UAAU;AAC3B,QAAI,UAAU,MAAM;AAEpB,QAAI,WAAY,WAAY;AACxB,UAAI,QAAQ,CAAC;AACb,eAAS,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AAC1B,cAAM,KAAK,QAAQ,IAAI,KAAK,MAAM,MAAM,EAAE,SAAS,EAAE,GAAG,YAAY,CAAC;AAAA,MACzE;AAEA,aAAO;AAAA,IACX,EAAE;AAEF,QAAI,eAAe,SAASC,cAAa,OAAO;AAC5C,aAAO,MAAM,SAAS,GAAG;AACrB,YAAI,OAAO,MAAM,IAAI;AACrB,YAAI,MAAM,KAAK,IAAI,KAAK,IAAI;AAE5B,YAAI,QAAQ,GAAG,GAAG;AACd,cAAI,YAAY,CAAC;AAEjB,mBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AACjC,gBAAI,OAAO,IAAI,CAAC,MAAM,aAAa;AAC/B,wBAAU,KAAK,IAAI,CAAC,CAAC;AAAA,YACzB;AAAA,UACJ;AAEA,eAAK,IAAI,KAAK,IAAI,IAAI;AAAA,QAC1B;AAAA,MACJ;AAAA,IACJ;AAEA,QAAI,gBAAgB,SAASC,eAAc,QAAQ,SAAS;AACxD,UAAI,MAAM,WAAW,QAAQ,eAAe,uBAAO,OAAO,IAAI,IAAI,CAAC;AACnE,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,EAAE,GAAG;AACpC,YAAI,OAAO,OAAO,CAAC,MAAM,aAAa;AAClC,cAAI,CAAC,IAAI,OAAO,CAAC;AAAA,QACrB;AAAA,MACJ;AAEA,aAAO;AAAA,IACX;AAEA,QAAI,QAAQ,SAASC,OAAM,QAAQ,QAAQ,SAAS;AAEhD,UAAI,CAAC,QAAQ;AACT,eAAO;AAAA,MACX;AAEA,UAAI,OAAO,WAAW,UAAU;AAC5B,YAAI,QAAQ,MAAM,GAAG;AACjB,iBAAO,KAAK,MAAM;AAAA,QACtB,WAAW,UAAU,OAAO,WAAW,UAAU;AAC7C,cAAK,YAAY,QAAQ,gBAAgB,QAAQ,oBAAqB,CAACH,KAAI,KAAK,OAAO,WAAW,MAAM,GAAG;AACvG,mBAAO,MAAM,IAAI;AAAA,UACrB;AAAA,QACJ,OAAO;AACH,iBAAO,CAAC,QAAQ,MAAM;AAAA,QAC1B;AAEA,eAAO;AAAA,MACX;AAEA,UAAI,CAAC,UAAU,OAAO,WAAW,UAAU;AACvC,eAAO,CAAC,MAAM,EAAE,OAAO,MAAM;AAAA,MACjC;AAEA,UAAI,cAAc;AAClB,UAAI,QAAQ,MAAM,KAAK,CAAC,QAAQ,MAAM,GAAG;AACrC,sBAAc,cAAc,QAAQ,OAAO;AAAA,MAC/C;AAEA,UAAI,QAAQ,MAAM,KAAK,QAAQ,MAAM,GAAG;AACpC,eAAO,QAAQ,SAAU,MAAM,GAAG;AAC9B,cAAIA,KAAI,KAAK,QAAQ,CAAC,GAAG;AACrB,gBAAI,aAAa,OAAO,CAAC;AACzB,gBAAI,cAAc,OAAO,eAAe,YAAY,QAAQ,OAAO,SAAS,UAAU;AAClF,qBAAO,CAAC,IAAIG,OAAM,YAAY,MAAM,OAAO;AAAA,YAC/C,OAAO;AACH,qBAAO,KAAK,IAAI;AAAA,YACpB;AAAA,UACJ,OAAO;AACH,mBAAO,CAAC,IAAI;AAAA,UAChB;AAAA,QACJ,CAAC;AACD,eAAO;AAAA,MACX;AAEA,aAAO,OAAO,KAAK,MAAM,EAAE,OAAO,SAAU,KAAK,KAAK;AAClD,YAAI,QAAQ,OAAO,GAAG;AAEtB,YAAIH,KAAI,KAAK,KAAK,GAAG,GAAG;AACpB,cAAI,GAAG,IAAIG,OAAM,IAAI,GAAG,GAAG,OAAO,OAAO;AAAA,QAC7C,OAAO;AACH,cAAI,GAAG,IAAI;AAAA,QACf;AACA,eAAO;AAAA,MACX,GAAG,WAAW;AAAA,IAClB;AAEA,QAAI,SAAS,SAAS,mBAAmB,QAAQ,QAAQ;AACrD,aAAO,OAAO,KAAK,MAAM,EAAE,OAAO,SAAU,KAAK,KAAK;AAClD,YAAI,GAAG,IAAI,OAAO,GAAG;AACrB,eAAO;AAAA,MACX,GAAG,MAAM;AAAA,IACb;AAEA,QAAI,SAAS,SAAU,KAAK,SAAS,SAAS;AAC1C,UAAI,iBAAiB,IAAI,QAAQ,OAAO,GAAG;AAC3C,UAAI,YAAY,cAAc;AAE1B,eAAO,eAAe,QAAQ,kBAAkB,QAAQ;AAAA,MAC5D;AAEA,UAAI;AACA,eAAO,mBAAmB,cAAc;AAAA,MAC5C,SAAS,GAAG;AACR,eAAO;AAAA,MACX;AAAA,IACJ;AAEA,QAAI,SAAS,SAASC,QAAO,KAAK,gBAAgB,SAAS,MAAM,QAAQ;AAGrE,UAAI,IAAI,WAAW,GAAG;AAClB,eAAO;AAAA,MACX;AAEA,UAAI,SAAS;AACb,UAAI,OAAO,QAAQ,UAAU;AACzB,iBAAS,OAAO,UAAU,SAAS,KAAK,GAAG;AAAA,MAC/C,WAAW,OAAO,QAAQ,UAAU;AAChC,iBAAS,OAAO,GAAG;AAAA,MACvB;AAEA,UAAI,YAAY,cAAc;AAC1B,eAAO,OAAO,MAAM,EAAE,QAAQ,mBAAmB,SAAU,IAAI;AAC3D,iBAAO,WAAW,SAAS,GAAG,MAAM,CAAC,GAAG,EAAE,IAAI;AAAA,QAClD,CAAC;AAAA,MACL;AAEA,UAAI,MAAM;AACV,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,EAAE,GAAG;AACpC,YAAI,IAAI,OAAO,WAAW,CAAC;AAE3B,YACI,MAAM,MACH,MAAM,MACN,MAAM,MACN,MAAM,OACL,KAAK,MAAQ,KAAK,MAClB,KAAK,MAAQ,KAAK,MAClB,KAAK,MAAQ,KAAK,OAClB,WAAW,QAAQ,YAAY,MAAM,MAAQ,MAAM,KACzD;AACE,iBAAO,OAAO,OAAO,CAAC;AACtB;AAAA,QACJ;AAEA,YAAI,IAAI,KAAM;AACV,gBAAM,MAAM,SAAS,CAAC;AACtB;AAAA,QACJ;AAEA,YAAI,IAAI,MAAO;AACX,gBAAM,OAAO,SAAS,MAAQ,KAAK,CAAE,IAAI,SAAS,MAAQ,IAAI,EAAK;AACnE;AAAA,QACJ;AAEA,YAAI,IAAI,SAAU,KAAK,OAAQ;AAC3B,gBAAM,OAAO,SAAS,MAAQ,KAAK,EAAG,IAAI,SAAS,MAAS,KAAK,IAAK,EAAK,IAAI,SAAS,MAAQ,IAAI,EAAK;AACzG;AAAA,QACJ;AAEA,aAAK;AACL,YAAI,UAAa,IAAI,SAAU,KAAO,OAAO,WAAW,CAAC,IAAI;AAE7D,eAAO,SAAS,MAAQ,KAAK,EAAG,IAC1B,SAAS,MAAS,KAAK,KAAM,EAAK,IAClC,SAAS,MAAS,KAAK,IAAK,EAAK,IACjC,SAAS,MAAQ,IAAI,EAAK;AAAA,MACpC;AAEA,aAAO;AAAA,IACX;AAEA,QAAI,UAAU,SAASC,SAAQ,OAAO;AAClC,UAAI,QAAQ,CAAC,EAAE,KAAK,EAAE,GAAG,MAAM,GAAG,MAAM,IAAI,CAAC;AAC7C,UAAI,OAAO,CAAC;AAEZ,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,EAAE,GAAG;AACnC,YAAI,OAAO,MAAM,CAAC;AAClB,YAAI,MAAM,KAAK,IAAI,KAAK,IAAI;AAE5B,YAAI,OAAO,OAAO,KAAK,GAAG;AAC1B,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AAClC,cAAI,MAAM,KAAK,CAAC;AAChB,cAAI,MAAM,IAAI,GAAG;AACjB,cAAI,OAAO,QAAQ,YAAY,QAAQ,QAAQ,KAAK,QAAQ,GAAG,MAAM,IAAI;AACrE,kBAAM,KAAK,EAAE,KAAU,MAAM,IAAI,CAAC;AAClC,iBAAK,KAAK,GAAG;AAAA,UACjB;AAAA,QACJ;AAAA,MACJ;AAEA,mBAAa,KAAK;AAElB,aAAO;AAAA,IACX;AAEA,QAAI,WAAW,SAASC,UAAS,KAAK;AAClC,aAAO,OAAO,UAAU,SAAS,KAAK,GAAG,MAAM;AAAA,IACnD;AAEA,QAAI,WAAW,SAASC,UAAS,KAAK;AAClC,UAAI,CAAC,OAAO,OAAO,QAAQ,UAAU;AACjC,eAAO;AAAA,MACX;AAEA,aAAO,CAAC,EAAE,IAAI,eAAe,IAAI,YAAY,YAAY,IAAI,YAAY,SAAS,GAAG;AAAA,IACzF;AAEA,QAAI,UAAU,SAASC,SAAQ,GAAG,GAAG;AACjC,aAAO,CAAC,EAAE,OAAO,GAAG,CAAC;AAAA,IACzB;AAEA,QAAI,WAAW,SAASC,UAAS,KAAK,IAAI;AACtC,UAAI,QAAQ,GAAG,GAAG;AACd,YAAI,SAAS,CAAC;AACd,iBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK,GAAG;AACpC,iBAAO,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC;AAAA,QAC1B;AACA,eAAO;AAAA,MACX;AACA,aAAO,GAAG,GAAG;AAAA,IACjB;AAEA,WAAO,UAAU;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA;AAAA;;;AC3PA;AAAA;AAAA;AAEA,QAAI,iBAAiB;AACrB,QAAI,QAAQ;AACZ,QAAI,UAAU;AACd,QAAIC,OAAM,OAAO,UAAU;AAE3B,QAAI,wBAAwB;AAAA,MACxB,UAAU,SAAS,SAAS,QAAQ;AAChC,eAAO,SAAS;AAAA,MACpB;AAAA,MACA,OAAO;AAAA,MACP,SAAS,SAAS,QAAQ,QAAQ,KAAK;AACnC,eAAO,SAAS,MAAM,MAAM;AAAA,MAChC;AAAA,MACA,QAAQ,SAAS,OAAO,QAAQ;AAC5B,eAAO;AAAA,MACX;AAAA,IACJ;AAEA,QAAI,UAAU,MAAM;AACpB,QAAI,OAAO,MAAM,UAAU;AAC3B,QAAI,cAAc,SAAU,KAAK,cAAc;AAC3C,WAAK,MAAM,KAAK,QAAQ,YAAY,IAAI,eAAe,CAAC,YAAY,CAAC;AAAA,IACzE;AAEA,QAAI,QAAQ,KAAK,UAAU;AAE3B,QAAI,gBAAgB,QAAQ,SAAS;AACrC,QAAI,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,WAAW;AAAA,MACX,SAAS;AAAA,MACT,iBAAiB;AAAA,MACjB,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,SAAS,MAAM;AAAA,MACf,kBAAkB;AAAA,MAClB,QAAQ;AAAA,MACR,WAAW,QAAQ,WAAW,aAAa;AAAA;AAAA,MAE3C,SAAS;AAAA,MACT,eAAe,SAAS,cAAc,MAAM;AACxC,eAAO,MAAM,KAAK,IAAI;AAAA,MAC1B;AAAA,MACA,WAAW;AAAA,MACX,oBAAoB;AAAA,IACxB;AAEA,QAAI,wBAAwB,SAASC,uBAAsB,GAAG;AAC1D,aAAO,OAAO,MAAM,YACb,OAAO,MAAM,YACb,OAAO,MAAM,aACb,OAAO,MAAM,YACb,OAAO,MAAM;AAAA,IACxB;AAEA,QAAI,WAAW,CAAC;AAEhB,QAAIC,aAAY,SAASA,WACrB,QACA,QACA,qBACA,gBACA,oBACA,WACA,SACA,QACA,MACA,WACA,eACA,QACA,WACA,kBACA,SACA,aACF;AACE,UAAI,MAAM;AAEV,UAAI,QAAQ;AACZ,UAAI,OAAO;AACX,UAAI,WAAW;AACf,cAAQ,QAAQ,MAAM,IAAI,QAAQ,OAAO,UAAkB,CAAC,UAAU;AAElE,YAAI,MAAM,MAAM,IAAI,MAAM;AAC1B,gBAAQ;AACR,YAAI,OAAO,QAAQ,aAAa;AAC5B,cAAI,QAAQ,MAAM;AACd,kBAAM,IAAI,WAAW,qBAAqB;AAAA,UAC9C,OAAO;AACH,uBAAW;AAAA,UACf;AAAA,QACJ;AACA,YAAI,OAAO,MAAM,IAAI,QAAQ,MAAM,aAAa;AAC5C,iBAAO;AAAA,QACX;AAAA,MACJ;AAEA,UAAI,OAAO,WAAW,YAAY;AAC9B,cAAM,OAAO,QAAQ,GAAG;AAAA,MAC5B,WAAW,eAAe,MAAM;AAC5B,cAAM,cAAc,GAAG;AAAA,MAC3B,WAAW,wBAAwB,WAAW,QAAQ,GAAG,GAAG;AACxD,cAAM,MAAM,SAAS,KAAK,SAAUC,QAAO;AACvC,cAAIA,kBAAiB,MAAM;AACvB,mBAAO,cAAcA,MAAK;AAAA,UAC9B;AACA,iBAAOA;AAAA,QACX,CAAC;AAAA,MACL;AAEA,UAAI,QAAQ,MAAM;AACd,YAAI,oBAAoB;AACpB,iBAAO,WAAW,CAAC,mBAAmB,QAAQ,QAAQ,SAAS,SAAS,SAAS,OAAO,MAAM,IAAI;AAAA,QACtG;AAEA,cAAM;AAAA,MACV;AAEA,UAAI,sBAAsB,GAAG,KAAK,MAAM,SAAS,GAAG,GAAG;AACnD,YAAI,SAAS;AACT,cAAI,WAAW,mBAAmB,SAAS,QAAQ,QAAQ,SAAS,SAAS,SAAS,OAAO,MAAM;AACnG,iBAAO,CAAC,UAAU,QAAQ,IAAI,MAAM,UAAU,QAAQ,KAAK,SAAS,SAAS,SAAS,SAAS,MAAM,CAAC,CAAC;AAAA,QAC3G;AACA,eAAO,CAAC,UAAU,MAAM,IAAI,MAAM,UAAU,OAAO,GAAG,CAAC,CAAC;AAAA,MAC5D;AAEA,UAAI,SAAS,CAAC;AAEd,UAAI,OAAO,QAAQ,aAAa;AAC5B,eAAO;AAAA,MACX;AAEA,UAAI;AACJ,UAAI,wBAAwB,WAAW,QAAQ,GAAG,GAAG;AAEjD,YAAI,oBAAoB,SAAS;AAC7B,gBAAM,MAAM,SAAS,KAAK,OAAO;AAAA,QACrC;AACA,kBAAU,CAAC,EAAE,OAAO,IAAI,SAAS,IAAI,IAAI,KAAK,GAAG,KAAK,OAAO,OAAe,CAAC;AAAA,MACjF,WAAW,QAAQ,MAAM,GAAG;AACxB,kBAAU;AAAA,MACd,OAAO;AACH,YAAI,OAAO,OAAO,KAAK,GAAG;AAC1B,kBAAU,OAAO,KAAK,KAAK,IAAI,IAAI;AAAA,MACvC;AAEA,UAAI,iBAAiB,kBAAkB,QAAQ,GAAG,KAAK,IAAI,WAAW,IAAI,SAAS,OAAO;AAE1F,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,EAAE,GAAG;AACrC,YAAI,MAAM,QAAQ,CAAC;AACnB,YAAI,QAAQ,OAAO,QAAQ,YAAY,OAAO,IAAI,UAAU,cAAc,IAAI,QAAQ,IAAI,GAAG;AAE7F,YAAI,aAAa,UAAU,MAAM;AAC7B;AAAA,QACJ;AAEA,YAAI,YAAY,QAAQ,GAAG,IACrB,OAAO,wBAAwB,aAAa,oBAAoB,gBAAgB,GAAG,IAAI,iBACvF,kBAAkB,YAAY,MAAM,MAAM,MAAM,MAAM;AAE5D,oBAAY,IAAI,QAAQ,IAAI;AAC5B,YAAI,mBAAmB,eAAe;AACtC,yBAAiB,IAAI,UAAU,WAAW;AAC1C,oBAAY,QAAQD;AAAA,UAChB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,wBAAwB,WAAW,oBAAoB,QAAQ,GAAG,IAAI,OAAO;AAAA,UAC7E;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACJ,CAAC;AAAA,MACL;AAEA,aAAO;AAAA,IACX;AAEA,QAAI,4BAA4B,SAASE,2BAA0B,MAAM;AACrE,UAAI,CAAC,MAAM;AACP,eAAO;AAAA,MACX;AAEA,UAAI,KAAK,YAAY,QAAQ,OAAO,KAAK,YAAY,eAAe,OAAO,KAAK,YAAY,YAAY;AACpG,cAAM,IAAI,UAAU,+BAA+B;AAAA,MACvD;AAEA,UAAI,UAAU,KAAK,WAAW,SAAS;AACvC,UAAI,OAAO,KAAK,YAAY,eAAe,KAAK,YAAY,WAAW,KAAK,YAAY,cAAc;AAClG,cAAM,IAAI,UAAU,mEAAmE;AAAA,MAC3F;AAEA,UAAI,SAAS,QAAQ,SAAS;AAC9B,UAAI,OAAO,KAAK,WAAW,aAAa;AACpC,YAAI,CAACJ,KAAI,KAAK,QAAQ,YAAY,KAAK,MAAM,GAAG;AAC5C,gBAAM,IAAI,UAAU,iCAAiC;AAAA,QACzD;AACA,iBAAS,KAAK;AAAA,MAClB;AACA,UAAI,YAAY,QAAQ,WAAW,MAAM;AAEzC,UAAI,SAAS,SAAS;AACtB,UAAI,OAAO,KAAK,WAAW,cAAc,QAAQ,KAAK,MAAM,GAAG;AAC3D,iBAAS,KAAK;AAAA,MAClB;AAEA,aAAO;AAAA,QACH,gBAAgB,OAAO,KAAK,mBAAmB,YAAY,KAAK,iBAAiB,SAAS;AAAA,QAC1F,WAAW,OAAO,KAAK,cAAc,cAAc,SAAS,YAAY,CAAC,CAAC,KAAK;AAAA,QAC/E;AAAA,QACA,iBAAiB,OAAO,KAAK,oBAAoB,YAAY,KAAK,kBAAkB,SAAS;AAAA,QAC7F,WAAW,OAAO,KAAK,cAAc,cAAc,SAAS,YAAY,KAAK;AAAA,QAC7E,QAAQ,OAAO,KAAK,WAAW,YAAY,KAAK,SAAS,SAAS;AAAA,QAClE,SAAS,OAAO,KAAK,YAAY,aAAa,KAAK,UAAU,SAAS;AAAA,QACtE,kBAAkB,OAAO,KAAK,qBAAqB,YAAY,KAAK,mBAAmB,SAAS;AAAA,QAChG;AAAA,QACA;AAAA,QACA;AAAA,QACA,eAAe,OAAO,KAAK,kBAAkB,aAAa,KAAK,gBAAgB,SAAS;AAAA,QACxF,WAAW,OAAO,KAAK,cAAc,YAAY,KAAK,YAAY,SAAS;AAAA,QAC3E,MAAM,OAAO,KAAK,SAAS,aAAa,KAAK,OAAO;AAAA,QACpD,oBAAoB,OAAO,KAAK,uBAAuB,YAAY,KAAK,qBAAqB,SAAS;AAAA,MAC1G;AAAA,IACJ;AAEA,WAAO,UAAU,SAAU,QAAQ,MAAM;AACrC,UAAI,MAAM;AACV,UAAI,UAAU,0BAA0B,IAAI;AAE5C,UAAI;AACJ,UAAI;AAEJ,UAAI,OAAO,QAAQ,WAAW,YAAY;AACtC,iBAAS,QAAQ;AACjB,cAAM,OAAO,IAAI,GAAG;AAAA,MACxB,WAAW,QAAQ,QAAQ,MAAM,GAAG;AAChC,iBAAS,QAAQ;AACjB,kBAAU;AAAA,MACd;AAEA,UAAI,OAAO,CAAC;AAEZ,UAAI,OAAO,QAAQ,YAAY,QAAQ,MAAM;AACzC,eAAO;AAAA,MACX;AAEA,UAAI;AACJ,UAAI,QAAQ,KAAK,eAAe,uBAAuB;AACnD,sBAAc,KAAK;AAAA,MACvB,WAAW,QAAQ,aAAa,MAAM;AAClC,sBAAc,KAAK,UAAU,YAAY;AAAA,MAC7C,OAAO;AACH,sBAAc;AAAA,MAClB;AAEA,UAAI,sBAAsB,sBAAsB,WAAW;AAC3D,UAAI,QAAQ,oBAAoB,QAAQ,OAAO,KAAK,mBAAmB,WAAW;AAC9E,cAAM,IAAI,UAAU,+CAA+C;AAAA,MACvE;AACA,UAAI,iBAAiB,wBAAwB,WAAW,QAAQ,KAAK;AAErE,UAAI,CAAC,SAAS;AACV,kBAAU,OAAO,KAAK,GAAG;AAAA,MAC7B;AAEA,UAAI,QAAQ,MAAM;AACd,gBAAQ,KAAK,QAAQ,IAAI;AAAA,MAC7B;AAEA,UAAI,cAAc,eAAe;AACjC,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,EAAE,GAAG;AACrC,YAAI,MAAM,QAAQ,CAAC;AAEnB,YAAI,QAAQ,aAAa,IAAI,GAAG,MAAM,MAAM;AACxC;AAAA,QACJ;AACA,oBAAY,MAAME;AAAA,UACd,IAAI,GAAG;AAAA,UACP;AAAA,UACA;AAAA,UACA;AAAA,UACA,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ,SAAS,QAAQ,UAAU;AAAA,UACnC,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR;AAAA,QACJ,CAAC;AAAA,MACL;AAEA,UAAI,SAAS,KAAK,KAAK,QAAQ,SAAS;AACxC,UAAI,SAAS,QAAQ,mBAAmB,OAAO,MAAM;AAErD,UAAI,QAAQ,iBAAiB;AACzB,YAAI,QAAQ,YAAY,cAAc;AAElC,oBAAU;AAAA,QACd,OAAO;AAEH,oBAAU;AAAA,QACd;AAAA,MACJ;AAEA,aAAO,OAAO,SAAS,IAAI,SAAS,SAAS;AAAA,IACjD;AAAA;AAAA;;;AC/TA;AAAA;AAAA;AAEA,QAAI,QAAQ;AAEZ,QAAIG,OAAM,OAAO,UAAU;AAC3B,QAAI,UAAU,MAAM;AAEpB,QAAI,WAAW;AAAA,MACX,WAAW;AAAA,MACX,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,iBAAiB;AAAA,MACjB,OAAO;AAAA,MACP,SAAS,MAAM;AAAA,MACf,WAAW;AAAA,MACX,OAAO;AAAA,MACP,mBAAmB;AAAA,MACnB,0BAA0B;AAAA,MAC1B,gBAAgB;AAAA,MAChB,aAAa;AAAA,MACb,cAAc;AAAA,MACd,oBAAoB;AAAA,IACxB;AAEA,QAAI,2BAA2B,SAAU,KAAK;AAC1C,aAAO,IAAI,QAAQ,aAAa,SAAU,IAAI,WAAW;AACrD,eAAO,OAAO,aAAa,SAAS,WAAW,EAAE,CAAC;AAAA,MACtD,CAAC;AAAA,IACL;AAEA,QAAI,kBAAkB,SAAU,KAAK,SAAS;AAC1C,UAAI,OAAO,OAAO,QAAQ,YAAY,QAAQ,SAAS,IAAI,QAAQ,GAAG,IAAI,IAAI;AAC1E,eAAO,IAAI,MAAM,GAAG;AAAA,MACxB;AAEA,aAAO;AAAA,IACX;AAOA,QAAI,cAAc;AAGlB,QAAI,kBAAkB;AAEtB,QAAI,cAAc,SAAS,uBAAuB,KAAK,SAAS;AAC5D,UAAI,MAAM,CAAC;AACX,UAAI,WAAW,QAAQ,oBAAoB,IAAI,QAAQ,OAAO,EAAE,IAAI;AACpE,UAAI,QAAQ,QAAQ,mBAAmB,WAAW,SAAY,QAAQ;AACtE,UAAI,QAAQ,SAAS,MAAM,QAAQ,WAAW,KAAK;AACnD,UAAI,YAAY;AAChB,UAAI;AAEJ,UAAI,UAAU,QAAQ;AACtB,UAAI,QAAQ,iBAAiB;AACzB,aAAK,IAAI,GAAG,IAAI,MAAM,QAAQ,EAAE,GAAG;AAC/B,cAAI,MAAM,CAAC,EAAE,QAAQ,OAAO,MAAM,GAAG;AACjC,gBAAI,MAAM,CAAC,MAAM,iBAAiB;AAC9B,wBAAU;AAAA,YACd,WAAW,MAAM,CAAC,MAAM,aAAa;AACjC,wBAAU;AAAA,YACd;AACA,wBAAY;AACZ,gBAAI,MAAM;AAAA,UACd;AAAA,QACJ;AAAA,MACJ;AAEA,WAAK,IAAI,GAAG,IAAI,MAAM,QAAQ,EAAE,GAAG;AAC/B,YAAI,MAAM,WAAW;AACjB;AAAA,QACJ;AACA,YAAI,OAAO,MAAM,CAAC;AAElB,YAAI,mBAAmB,KAAK,QAAQ,IAAI;AACxC,YAAI,MAAM,qBAAqB,KAAK,KAAK,QAAQ,GAAG,IAAI,mBAAmB;AAE3E,YAAI,KAAK;AACT,YAAI,QAAQ,IAAI;AACZ,gBAAM,QAAQ,QAAQ,MAAM,SAAS,SAAS,SAAS,KAAK;AAC5D,gBAAM,QAAQ,qBAAqB,OAAO;AAAA,QAC9C,OAAO;AACH,gBAAM,QAAQ,QAAQ,KAAK,MAAM,GAAG,GAAG,GAAG,SAAS,SAAS,SAAS,KAAK;AAC1E,gBAAM,MAAM;AAAA,YACR,gBAAgB,KAAK,MAAM,MAAM,CAAC,GAAG,OAAO;AAAA,YAC5C,SAAU,YAAY;AAClB,qBAAO,QAAQ,QAAQ,YAAY,SAAS,SAAS,SAAS,OAAO;AAAA,YACzE;AAAA,UACJ;AAAA,QACJ;AAEA,YAAI,OAAO,QAAQ,4BAA4B,YAAY,cAAc;AACrE,gBAAM,yBAAyB,GAAG;AAAA,QACtC;AAEA,YAAI,KAAK,QAAQ,KAAK,IAAI,IAAI;AAC1B,gBAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,IAAI;AAAA,QACjC;AAEA,YAAIA,KAAI,KAAK,KAAK,GAAG,GAAG;AACpB,cAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,GAAG,GAAG;AAAA,QAC1C,OAAO;AACH,cAAI,GAAG,IAAI;AAAA,QACf;AAAA,MACJ;AAEA,aAAO;AAAA,IACX;AAEA,QAAI,cAAc,SAAU,OAAO,KAAK,SAAS,cAAc;AAC3D,UAAI,OAAO,eAAe,MAAM,gBAAgB,KAAK,OAAO;AAE5D,eAAS,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AACxC,YAAI;AACJ,YAAI,OAAO,MAAM,CAAC;AAElB,YAAI,SAAS,QAAQ,QAAQ,aAAa;AACtC,gBAAM,CAAC,EAAE,OAAO,IAAI;AAAA,QACxB,OAAO;AACH,gBAAM,QAAQ,eAAe,uBAAO,OAAO,IAAI,IAAI,CAAC;AACpD,cAAI,YAAY,KAAK,OAAO,CAAC,MAAM,OAAO,KAAK,OAAO,KAAK,SAAS,CAAC,MAAM,MAAM,KAAK,MAAM,GAAG,EAAE,IAAI;AACrG,cAAIC,SAAQ,SAAS,WAAW,EAAE;AAClC,cAAI,CAAC,QAAQ,eAAe,cAAc,IAAI;AAC1C,kBAAM,EAAE,GAAG,KAAK;AAAA,UACpB,WACI,CAAC,MAAMA,MAAK,KACT,SAAS,aACT,OAAOA,MAAK,MAAM,aAClBA,UAAS,MACR,QAAQ,eAAeA,UAAS,QAAQ,aAC9C;AACE,kBAAM,CAAC;AACP,gBAAIA,MAAK,IAAI;AAAA,UACjB,WAAW,cAAc,aAAa;AAClC,gBAAI,SAAS,IAAI;AAAA,UACrB;AAAA,QACJ;AAEA,eAAO;AAAA,MACX;AAEA,aAAO;AAAA,IACX;AAEA,QAAI,YAAY,SAAS,qBAAqB,UAAU,KAAK,SAAS,cAAc;AAChF,UAAI,CAAC,UAAU;AACX;AAAA,MACJ;AAGA,UAAI,MAAM,QAAQ,YAAY,SAAS,QAAQ,eAAe,MAAM,IAAI;AAIxE,UAAI,WAAW;AACf,UAAI,QAAQ;AAIZ,UAAI,UAAU,QAAQ,QAAQ,KAAK,SAAS,KAAK,GAAG;AACpD,UAAI,SAAS,UAAU,IAAI,MAAM,GAAG,QAAQ,KAAK,IAAI;AAIrD,UAAI,OAAO,CAAC;AACZ,UAAI,QAAQ;AAER,YAAI,CAAC,QAAQ,gBAAgBD,KAAI,KAAK,OAAO,WAAW,MAAM,GAAG;AAC7D,cAAI,CAAC,QAAQ,iBAAiB;AAC1B;AAAA,UACJ;AAAA,QACJ;AAEA,aAAK,KAAK,MAAM;AAAA,MACpB;AAIA,UAAI,IAAI;AACR,aAAO,QAAQ,QAAQ,MAAM,UAAU,MAAM,KAAK,GAAG,OAAO,QAAQ,IAAI,QAAQ,OAAO;AACnF,aAAK;AACL,YAAI,CAAC,QAAQ,gBAAgBA,KAAI,KAAK,OAAO,WAAW,QAAQ,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,GAAG;AAC9E,cAAI,CAAC,QAAQ,iBAAiB;AAC1B;AAAA,UACJ;AAAA,QACJ;AACA,aAAK,KAAK,QAAQ,CAAC,CAAC;AAAA,MACxB;AAIA,UAAI,SAAS;AACT,aAAK,KAAK,MAAM,IAAI,MAAM,QAAQ,KAAK,IAAI,GAAG;AAAA,MAClD;AAEA,aAAO,YAAY,MAAM,KAAK,SAAS,YAAY;AAAA,IACvD;AAEA,QAAI,wBAAwB,SAASE,uBAAsB,MAAM;AAC7D,UAAI,CAAC,MAAM;AACP,eAAO;AAAA,MACX;AAEA,UAAI,KAAK,YAAY,QAAQ,KAAK,YAAY,UAAa,OAAO,KAAK,YAAY,YAAY;AAC3F,cAAM,IAAI,UAAU,+BAA+B;AAAA,MACvD;AAEA,UAAI,OAAO,KAAK,YAAY,eAAe,KAAK,YAAY,WAAW,KAAK,YAAY,cAAc;AAClG,cAAM,IAAI,UAAU,mEAAmE;AAAA,MAC3F;AACA,UAAI,UAAU,OAAO,KAAK,YAAY,cAAc,SAAS,UAAU,KAAK;AAE5E,aAAO;AAAA,QACH,WAAW,OAAO,KAAK,cAAc,cAAc,SAAS,YAAY,CAAC,CAAC,KAAK;AAAA,QAC/E,iBAAiB,OAAO,KAAK,oBAAoB,YAAY,KAAK,kBAAkB,SAAS;AAAA,QAC7F,aAAa,OAAO,KAAK,gBAAgB,YAAY,KAAK,cAAc,SAAS;AAAA,QACjF,YAAY,OAAO,KAAK,eAAe,WAAW,KAAK,aAAa,SAAS;AAAA,QAC7E;AAAA,QACA,iBAAiB,OAAO,KAAK,oBAAoB,YAAY,KAAK,kBAAkB,SAAS;AAAA,QAC7F,OAAO,OAAO,KAAK,UAAU,YAAY,KAAK,QAAQ,SAAS;AAAA,QAC/D,SAAS,OAAO,KAAK,YAAY,aAAa,KAAK,UAAU,SAAS;AAAA,QACtE,WAAW,OAAO,KAAK,cAAc,YAAY,MAAM,SAAS,KAAK,SAAS,IAAI,KAAK,YAAY,SAAS;AAAA;AAAA,QAE5G,OAAQ,OAAO,KAAK,UAAU,YAAY,KAAK,UAAU,QAAS,CAAC,KAAK,QAAQ,SAAS;AAAA,QACzF,mBAAmB,KAAK,sBAAsB;AAAA,QAC9C,0BAA0B,OAAO,KAAK,6BAA6B,YAAY,KAAK,2BAA2B,SAAS;AAAA,QACxH,gBAAgB,OAAO,KAAK,mBAAmB,WAAW,KAAK,iBAAiB,SAAS;AAAA,QACzF,aAAa,KAAK,gBAAgB;AAAA,QAClC,cAAc,OAAO,KAAK,iBAAiB,YAAY,KAAK,eAAe,SAAS;AAAA,QACpF,oBAAoB,OAAO,KAAK,uBAAuB,YAAY,KAAK,qBAAqB,SAAS;AAAA,MAC1G;AAAA,IACJ;AAEA,WAAO,UAAU,SAAU,KAAK,MAAM;AAClC,UAAI,UAAU,sBAAsB,IAAI;AAExC,UAAI,QAAQ,MAAM,QAAQ,QAAQ,OAAO,QAAQ,aAAa;AAC1D,eAAO,QAAQ,eAAe,uBAAO,OAAO,IAAI,IAAI,CAAC;AAAA,MACzD;AAEA,UAAI,UAAU,OAAO,QAAQ,WAAW,YAAY,KAAK,OAAO,IAAI;AACpE,UAAI,MAAM,QAAQ,eAAe,uBAAO,OAAO,IAAI,IAAI,CAAC;AAIxD,UAAI,OAAO,OAAO,KAAK,OAAO;AAC9B,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AAClC,YAAI,MAAM,KAAK,CAAC;AAChB,YAAI,SAAS,UAAU,KAAK,QAAQ,GAAG,GAAG,SAAS,OAAO,QAAQ,QAAQ;AAC1E,cAAM,MAAM,MAAM,KAAK,QAAQ,OAAO;AAAA,MAC1C;AAEA,UAAI,QAAQ,gBAAgB,MAAM;AAC9B,eAAO;AAAA,MACX;AAEA,aAAO,MAAM,QAAQ,GAAG;AAAA,IAC5B;AAAA;AAAA;;;ACtQA,IAAAC,eAAA;AAAA;AAAA;AAEA,QAAIC,aAAY;AAChB,QAAIC,SAAQ;AACZ,QAAI,UAAU;AAEd,WAAO,UAAU;AAAA,MACb;AAAA,MACA,OAAOA;AAAA,MACP,WAAWD;AAAA,IACf;AAAA;AAAA;;;ICPaE,WAAW;EACtB,CAAC,gCAAgC,GAAGC;EACpC,CAAC,0CAA0C,GAAGC;AAChD;;;;;ICyCaC,WAAoB;EAC/BC,YAAY;IACVC,QAAQ,CAAA;IACRC,IAAI,EAAEC,IAAIC,UAAS,GAAE;AACnB,UAAI,CAAC,KAAKH,OAAOE,EAAAA,GAAK;AACpB,aAAKF,OAAOE,EAAAA,IAAMC;MACpB;IACF;EACF;EACAC,OAAO;IACLC,WAAW,CAYX;IACAC,aAAa;MACXC,YAAY,CAAA;MACZC,MAAM;QACJC,UAAU,CAAA;QACVC,MAAM,CAAA;MACR;IACF;IACAP,WAAW;MACTI,YAAY,CAAA;MACZC,MAAM;QACJC,UAAU,CAAA;QACVC,MAAM,CAAA;MACR;IACF;EACF;EACAC,4BAA4B,CAAA;EAC5BC,6BAA6BC,IAAE;AAC7B,SAAKF,2BAA2BG,KAAKD,EAAAA;EACvC;EACAE,kBAAkB,EAAEC,WAAWR,MAAM,EAAEC,UAAUC,KAAI,EAAE,GAAE;AACvD,UAAM,EAAEJ,YAAW,IAAK,KAAKF;AAE7B,QAAIY,WAAW;AACbV,kBAAYC,WAAWO,KAAKE,SAAAA;IAC9B;AACAV,gBAAYE,KAAKC,SAASK,KAAKL,QAAAA;AAC/BH,gBAAYE,KAAKE,KAAKI,KAAKJ,IAAAA;EAC7B;EACAO,aAAaC,QAAQ,EAAEF,WAAWR,MAAM,EAAEC,UAAUC,KAAI,EAAE,GAAE;AAC1D,UAAMS,WAAW,KAAKf,MAAMC;AAE5Ba,WAAOE,QAAQ,CAACC,UAAAA;AACd,UAAI,CAACF,SAASE,KAAAA,GAAQ;AACpBF,iBAASE,KAAAA,IAAS;UAChBd,YAAY,CAAA;UACZC,MAAM;YACJC,UAAU,CAAA;YAGVC,MAAM,CAAA;UAGR;QACF;MACF;AAEA,UAAIM,WAAW;AACbG,iBAASE,KAAM,EAACd,WAAWO,KAAKE,SAAAA;MAClC;AACAG,eAASE,KAAAA,EAAOb,KAAKC,SAASK,KAAKL,QAAAA;AACnCU,eAASE,KAAAA,EAAOb,KAAKE,KAAKI,KAAKJ,IAAAA;IACjC,CAAA;EACF;EAEAY,gBAAgBC,QAAQC,QAAQ,MAAI;AAClC,UAAMC,oBAAgBC,WAAAA,SAAI,KAAKtB,OAAO;MAAImB,GAAAA;MAAQ;MAAQ;IAAW,GAAE,CAAA,CAAE,EAAEI,OACzE,CAACC,KAAUC,YAAAA;AACT,YAAMC,WAAWD,QAAQL,KAAAA;AAEzB,aAAO;QAAII,GAAAA;QAAQE,GAAAA;MAAS;IAC9B,GACA,CAAA,CAAE;AAGJ,WAAOL;EACT;EAEAM,yBAAyBC,gBAAgBhB,cAAciB,eAAa;AAElE,QAAI,CAACjB,UAAW,QAAOgB;AAGvB,WAAOA,eAAeE,MAAM;MAAEC,SAAaC,QAAM,EAAGF,MAAMlB,UAAUiB,aAAAA,CAAAA;IAAgB,CAAA;EACtF;EAEAI,cAAcd,QAAQe,cAAcC,MAAI;AACtC,UAAMhC,iBAAamB,WAAAA,SAAI,KAAKtB,OAAO;MAAImB,GAAAA;MAAQ;IAAa,GAAE,CAAA,CAAE;AAEhE,UAAMiB,qBAAqBjC,WAAWoB,OAAO,CAACC,KAAUC,YAAAA;AACtD,YAAMY,oBAAoBZ,QAAQU,IAAAA;AAElC,aAAO;QAAE,GAAGX;QAAK,GAAGa;MAAkB;IACxC,GAAG,CAAA,CAAC;AAEJ,WAAOH,UAAUJ,MAAM;MAAEQ,eAAmBN,QAAM,EAAGF,MAAMM,kBAAAA;IAAoB,CAAA;EACjF;EACAG,wBAAwBC,MAA+BC,aAAoC;AACzF,QAAIC,mBAAeC,iBAAAA,SAAUH,IAAAA;AAE7B,UAAMI,cAAUD,iBAAAA,SAAUF,WAAAA;AAE1B,SAAKlC,2BAA2BS,QAAQ,CAACP,OAAAA;AACvCiC,qBAAejC,GAAGiC,cAAcE,OAAAA;IAClC,CAAA;AAEA,WAAOF;EACT;AACF;;;ACrKMG,IAAAA,2BAA2B,CAACC,MAAmBC,cAAAA;AAInD,SAAOC,OAAOC,KAAKH,IAAAA,EAAMI,OAAO,CAACC,KAAKC,YAAAA;AACpCD,QAAI,GAAGJ,SAAAA,IAAYK,OAAQ,EAAC,IAAIN,KAAKM,OAAQ;AAC7C,WAAOD;EACT,GAAG,CAAA,CAAC;AACN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACCA,IAAAE,SAAe;EACbC,SAASC,KAAc;AACrBA,QAAIC,YAAYC,QAAAA;AAChBF,QAAIG,YAAY;MACdC,IAAI,WAAWC,QAAAA;MACfC,MAAMC;MACNC,WAAW;QACTC,IAAI,GAAGJ,QAAS;QAChBK,gBAAgB;MAClB;MACAC,aAAaC,YAAYC;MACzBC,WAAW,MAAM,OAAO,mBAAA;MACxBC,UAAU;IACZ,CAAA;AAEAf,QAAIgB,eAAe;MACjBP,IAAIJ;MACJY,MAAMZ;;MAENa,MAAM;QACJC,OAAOC;MACT;IACF,CAAA;EACF;EACAC,YAAa;EAAA;EACb,MAAMC,cAAc,EAAEC,QAAO,GAAyB;AACpD,UAAMC,gBAAgB,MAAMC,QAAQC,IAClCH,QAAQI,IAAI,CAACC,WAAAA;AACX,aAAO,kCAAO,kBAAkBA,MAAO,OAAM,EAC1CC,KAAK,CAAC,EAAEC,SAASC,KAAI,MAAE;AACtB,eAAO;UACLA,MAAMC,yBAAyBD,MAAM1B,QAAAA;UACrCuB;QACF;MACF,CAAA,EACCK,MAAM,MAAA;AACL,eAAO;UACLF,MAAM,CAAA;UACNH;QACF;MACF,CAAA;IACJ,CAAA,CAAA;AAGF,WAAOH,QAAQS,QAAQV,aAAAA;EACzB;AACF;;;ACvDMW,IAAAA,4BAA2B,CAACC,MAAmBC,cAAAA;AAInD,SAAOC,OAAOC,KAAKH,IAAAA,EAAMI,OAAO,CAACC,KAAKC,YAAAA;AACpCD,QAAI,GAAGJ,SAAAA,IAAYK,OAAQ,EAAC,IAAIN,KAAKM,OAAQ;AAC7C,WAAOD;EACT,GAAG,CAAA,CAAC;AACN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACLA,IAAME,QAAkC;;;EAGtCC,SAASC,KAAQ;AAEfA,QAAIC,qBACF;MACEC,IAAI;MACJC,WAAW;QAAED,IAAI;QAAmCE,gBAAgB;MAAe;OAErF;MACE;QACED,WAAW;UACTD,IAAI;UACJE,gBAAgB;QAClB;QACAF,IAAI;QACJG,IAAI;QACJC,WAAW,MACT,OAAO,wBAAA,EAAoBC,KAAK,CAACC,SAAS;UACxCC,SAASD,IAAIE;UACf;QACFC,aAAaC,aAAYC;MAC3B;IACD,CAAA;AAEHb,QAAIc,eAAe;MACjBZ,IAAI;MACJa,MAAM;IACR,CAAA;EACF;;EAEAC,YAAa;EAAA;EACb,MAAMC,cAAc,EAAEC,QAAO,GAAyB;AACpD,UAAMC,gBAAgB,MAAMC,QAAQC,IAClCH,QAAQI,IAAI,CAACC,WAAAA;AACX,aAAOC,mCAAO,kBAAkBD,MAAO,OAAM,EAC1ChB,KAAK,CAAC,EAAEE,SAASgB,KAAI,MAAE;AACtB,eAAO;UACLA,MAAMC,0BAAyBD,MAAM,OAAA;UACrCF;QACF;MACF,CAAA,EACCI,MAAM,MAAA;AACL,eAAO;UACLF,MAAM,CAAA;UACNF;QACF;MACF,CAAA;IACJ,CAAA,CAAA;AAGF,WAAOH,QAAQQ,QAAQT,aAAAA;EACzB;AACF;;;;;;;;;;;;;AClDO,IAAMU,sBAAsB,CAACC,iBAAAA;AAClC,QAAM,EAAEC,WAAU,IAAKC,YAAAA;AACvB,QAAM,EACJC,QAAQ,EAAEC,MAAMD,OAAM,EAAE,IACtBE,UAAAA;AAEJ,QAAM,CAACC,aAAaC,cAAAA,IAAwBC,eAAgB;IAC1DC,MAAM;IACNC,MAAM;IACNC,UAAU;IACVC,SAAS;MACPC,MAAM,CAAA;IACR;IACA,GAAGb;EACL,CAAA;AAEAc,EAAMC,gBAAU,MAAA;AACd,QAAIZ,UAAU,UAAUA,UAAU,cAAcA,QAAQ;AACtDI,qBAAe,CAACS,eAAe;QAC7B,GAAGA;QACHN,MAAMP,OAAOO;QACbC,UAAUR,OAAOQ;QACnB;IACF;KACC;IAACR;EAAO,CAAA;AAEX,QAAMc,sBAAsB,CAACC,gBAAAA;AAC3B,QAAIA,aAAa;AACfjB,iBAAW,iCAAiC;QAC1CkB,UAAU;QACVC,QAAQC,OAAOC,KAAKJ,YAAYA,YAAYK,SAAS,CAAA,CAAE,EAAE,CAAE;MAC7D,CAAA;AACAhB,qBAAe,CAACiB,UAAU;QAAE,GAAGA;QAAMf,MAAM;QAAGG,SAAS;UAAEC,MAAMK;QAAY;QAAE;IAC/E;EACF;AAEA,QAAMO,uBAAuB,CAACd,aAAAA;AAC5BJ,mBAAe,CAACiB,UAAU;MACxB,GAAGA;MACHb,UAAU,OAAOA,aAAa,WAAWe,SAASf,UAAU,EAAMA,IAAAA;MAClEF,MAAM;MACR;EACF;AAEA,QAAMkB,kBAAkB,CAAClB,SAAAA;AACvBF,mBAAe,CAACiB,UAAU;MAAE,GAAGA;MAAMf;MAAK;EAC5C;AAEA,QAAMmB,mBAAmB,CAAClB,SAAAA;AACxB,QAAIA,MAAM;AACRT,iBAAW,+BAA+B;QACxCkB,UAAU;QACVT;MACF,CAAA;AACAH,qBAAe,CAACiB,UAAU;QAAE,GAAGA;QAAMd;QAAK;IAC5C;EACF;AAEA,QAAMmB,qBAAqB,CAACC,OAAAA;AAC1B,QAAIA,IAAI;AACNvB,qBAAe,CAACiB,UAAU;QAAE,GAAGA;QAAMM;QAAIrB,MAAM;QAAE;WAC5C;AACL,YAAMsB,WAAkB;QAAEtB,MAAM;MAAE;AAElCY,aAAOC,KAAKhB,WAAa0B,EAAAA,QAAQ,CAACC,QAAAA;AAChC,YAAI,CAAC;UAAC;UAAQ;UAAMC,SAASD,GAAM,GAAA;AAChCF,mBAAyDE,GAAI,IAAG,YAE/DA,GAAI;QACR;MACF,CAAA;AAEA1B,qBAAewB,QAAAA;IACjB;EACF;AAEA,QAAMI,qBAAqB,CAACC,QAAyBC,eAAAA;AACnD9B,mBAAe,CAACiB,UAAU;MAAE,GAAGA;MAAMY,QAAQA,UAAU;MAAMC;MAAW;EAC1E;AAEA,SAAO;IACL;MAAE/B;MAAagC,cAAUC,qBAAUjC,aAAa;QAAEkC,QAAQ;MAAM,CAAA;IAAG;IACnE;MACEC,iBAAiBxB;MACjByB,gBAAgBP;MAChBQ,cAAchB;MACdiB,kBAAkBnB;MAClBoB,cAAcjB;MACdkB,gBAAgBjB;IAClB;EACD;AACH;;;ACpGO,IAAMkB,kBAAkB,CAACC,UAAAA;AAC9B,MAAI,CAACA,OAAO;AACV,WAAO,CAAA;EACT;AAEA,SAAOA,MAAMC,IAAI,CAACC,SAASA,KAAKC,UAAU,GAAGD,KAAKE,SAAS,CAAA,CAAA;AAC7D;;;ACWO,IAAMC,kBAAkB,CAACC,aAA8BC,UAAAA;AAC5D,MAAI,CAACD,aAAa;AAChB,WAAOC;EACT;AAEA,QAAMC,gBAAgBC,gBAAgBH,WAAAA;AAEtC,QAAMI,eAAeH,MAAMI,OAAO,CAACC,SAAAA;;AACjC,UAAMC,YAAWD,kCAAME,SAANF,mBAAYG,MAAM,KAAK;AAExC,QAAI,CAACF,UAAU;AACb,aAAO;IACT;AAEA,QAAIL,cAAcQ,SAAS,MAAA,KAAW,CAAC;MAAC;MAAS;MAAS;MAASA,SAASH,QAAW,GAAA;AACrF,aAAO;IACT;AAEA,WAAOL,cAAcQ,SAASH,QAAAA;EAChC,CAAA;AAEA,SAAOH;AACT;;;;;;ACvCA,IAAMO,OAAO,CAAaC,OAAYC,UAAkBC,aAAAA;AACtD,MAAIA,YAAYF,MAAMG,QAAQ;AAC5BD,eAAWF,MAAMG,SAAS;EAC5B;AACAH,QAAMI,OAAOF,UAAU,GAAGF,MAAMI,OAAOH,UAAU,CAAE,EAAC,CAAE,CAAA;AAEtD,SAAOD;AACT;AAEaK,IAAAA,cAAc,CAAaL,OAAYM,QAAeC,WAAAA;AACjE,QAAML,WAAWI,SAAQC;AAEzB,SAAOR,KAAKC,OAAOM,QAAOJ,QAAAA;AAC5B;;;;;;ACUO,IAAMM,sBAAsB,CAACC,WAAAA;;AAClC,QAAMC,OAAyB;IAC7B;MACEC,IAAI;MACJC,OAAO;QAAED,IAAIE,QAAQ,aAAA;QAAgBC,gBAAgB;MAAgB;IACvE;EACD;AAED,OAAIL,sCAAQM,WAARN,mBAAgBM,QAAQ;AAC1BL,SAAKM,KAAK,CAAA,CAAE;EACd;AAEA,MAAIP,iCAAQM,QAAQ;AAClBL,SAAKM,KAAK;MACRL,IAAIF,OAAOM,OAAOJ;MAClBC,OAAOH,OAAOM,OAAOE;MACrBC,MAAMT,OAAOM,OAAOG;IACtB,CAAA;EACF;AAEA,MAAIT,QAAQ;AACVC,SAAKM,KAAK;MACRL,IAAIF,OAAOE;MACXC,OAAOH,OAAOQ;MACdC,MAAMT,OAAOS;IACf,CAAA;EACF;AAEA,SAAOR;AACT;;;;;;;;;ICRaS,UAAU,CAAC,EAAEC,gBAAgBC,gBAAe,MAAgB;AACvE,QAAM,CAACC,MAAMC,OAAAA,IAAiBC,gBAAS,KAAA;AACvC,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,aACEC,yBAACC,QAAQC,MAAI;IAACP;IAAYQ,cAAcP;;UACtCQ,wBAACH,QAAQI,SAAO;QACd,cAAAD,wBAACE,QAAAA;UAAOC,SAAQ;UAAWC,eAAWJ,wBAACK,eAAAA,CAAAA,CAAAA;UAAWC,MAAK;oBACpDZ,cAAc;YAAEa,IAAI;YAAqBC,gBAAgB;UAAU,CAAA;;;UAGxER,wBAACS,eAAAA;QACCC,UAAU,MAAMlB,QAAQ,CAACmB,SAAS,CAACA,IAAAA;QACnCC;QACAC,SAASxB;QACTyB,UAAUxB;;MAGXD,sBACCW,wBAACe,YAAAA;QACC1B;QACA2B,eAAeJ;QACfK,gBAAgB3B;;;;AAK1B;;;;IC/Da4B,WAAW,CAAC,EAAEC,kBAAkBC,SAAQ,MAAiB;AACpE,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,QAAMC,eAAe,CAACC,UAAAA;AACpBL,qBAAiBM,OAAOD,KAAAA,CAAAA;EAC1B;AAEA,aACEE,0BAACC,MAAAA;;UACCD,0BAACE,cAAAA;QACCC,MAAK;QACLC,cAAYT,cAAc;UACxBU,IAAI;UACJC,gBAAgB;QAClB,CAAA;QACAC,UAAUV;QACVC,OAAOJ,SAASc,SAAQ;;cAExBC,yBAACC,oBAAAA;YAAmBZ,OAAM;YAAK,UAAA;;cAC/BW,yBAACC,oBAAAA;YAAmBZ,OAAM;YAAK,UAAA;;cAC/BW,yBAACC,oBAAAA;YAAmBZ,OAAM;YAAK,UAAA;;cAC/BW,yBAACC,oBAAAA;YAAmBZ,OAAM;YAAM,UAAA;;;;UAElCW,yBAACE,KAAAA;QAAIC,aAAa;QAChB,cAAAH,yBAACI,YAAAA;UAAWC,WAAU;UAAaC,KAAI;UAAQC,SAAQ;oBACpDrB,cAAc;YACbU,IAAI;YACJC,gBAAgB;UAClB,CAAA;;;;;AAKV;;;;;;;;ACrCA,IAAMW,oBAA0BC,qBAAc;EAAEC,YAAY;EAAGC,WAAW;AAAE,CAAA;IAC/DC,gBAAgB,MAAYC,kBAAWL,iBAAmB;AAS1DM,IAAAA,aAAa,CAAC,EACzBC,UACAL,YACAC,WACAK,QAAQ,aAAY,MACJ;AAChB,QAAMC,kBAAwBC,eAAQ,OAAO;IAAER;IAAYC;EAAU,IAAI;IAACD;IAAYC;EAAU,CAAA;AAEhG,aACEQ,yBAACX,kBAAkBY,UAAQ;IAACC,OAAOJ;IACjC,cAAAE,yBAACG,KAAAA;MAAIC,KAAI;MAAMC,cAAYR;MACzB,cAAAG,yBAACM,MAAAA;QAAKF,KAAI;QAAKG,KAAK;QACjBX;;;;AAKX;;;ACVA,IAAMY,iBAAiBC,GAAOC,UAAAA;;;AAI9B,IAAMC,oBAAoBC;aACb,CAAC,EAAEC,MAAK,MAAOA,MAAMC,OAAO,CAAA,CAAE;mBACxB,CAAC,EAAED,MAAK,MAAOA,MAAME,YAAY;gBACpC,CAAC,EAAEC,SAASH,MAAK,MAAQG,UAAUH,MAAMI,QAAQC,eAAeC,MAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BA8BjE,CAACC,UAAUA,MAAMP,MAAMQ,OAAOC,UAAU;;;;AAKlE,IAAMC,oBAAoBd,GAAOe;IAC7Bb,iBAAkB;;AAGtB,IAAMc,iBAAiBhB,GAAOiB;IAC1Bf,iBAAkB;;AAGtBY,kBAAkBI,eAAe;EAAEC,MAAM;AAAS;AAElD,IAAMC,kBAAkBpB,GAAOc,iBAAAA;WACpB,CAAC,EAAEV,OAAOG,QAAO,MAAQA,UAAUH,MAAMQ,OAAOS,aAAajB,MAAMQ,OAAOU,UAAU;gBAC/E,CAAC,EAAElB,OAAOG,QAAO,MAAQA,UAAUH,MAAMQ,OAAOW,WAAWb,MAAW;;;kBAGpE,CAAC,EAAEN,MAAK,MAAOA,MAAMI,QAAQC,YAAY;;;AAI3D,IAAMe,oBAAoBxB,GAAOc,iBAAAA;;;YAGrB,CAACW,MAAOA,EAAE,eAAgB,IAAGA,EAAErB,MAAMQ,OAAOc,aAAaD,EAAErB,MAAMQ,OAAOe,UAAU;;;;;;cAMhF,CAACF,MAAOA,EAAE,eAAgB,IAAGA,EAAErB,MAAMQ,OAAOc,aAAaD,EAAErB,MAAMQ,OAAOgB,UAAU;;;;IAI5F,CAACH,MACDA,EAAE,eAAA,IACE;;QAGAf,MAAU;;AAGlB,IAAMmB,cAAc7B,GAAOgB,cAAAA;WAChB,CAAC,EAAEZ,MAAK,MAAOA,MAAMQ,OAAOU,UAAU;;AAWjD,IAAMQ,eAAe,CAAC,EAAEC,UAAU,GAAGpB,MAA4B,MAAA;AAC/D,QAAM,EAAEqB,WAAU,IAAKC,cAAAA;AAEvB,QAAMC,WAAWF,eAAe;AAEhC,aACEG,yBAACC,MAAAA;IACC,cAAAC,0BAACb,mBAAAA;MAAkBc,iBAAeJ;MAAUK,UAAUL,WAAW,KAAKxB;MAAY,GAAGC;;YACnFwB,yBAACK,gBAAAA;UAAgBT;;YACjBI,yBAACM,eAAAA;UAAYC,eAAW;;;;;AAIhC;AAEA,IAAMC,WAAW,CAAC,EAAEZ,UAAU,GAAGpB,MAA4B,MAAA;AAC3D,QAAM,EAAEqB,YAAYY,UAAS,IAAKX,cAAAA;AAElC,QAAMC,WAAWF,eAAeY;AAEhC,aACET,yBAACC,MAAAA;IACC,cAAAC,0BAACb,mBAAAA;MAAkBc,iBAAeJ;MAAUK,UAAUL,WAAW,KAAKxB;MAAY,GAAGC;;YACnFwB,yBAACK,gBAAAA;UAAgBT;;YACjBI,yBAACU,eAAAA;UAAaH,eAAW;;;;;AAIjC;AAEA,IAAMI,WAAW,CAAC,EAAEC,QAAQhB,UAAU,GAAGpB,MAAsB,MAAA;AAC7D,QAAM,EAAEqB,WAAU,IAAKC,cAAAA;AAEvB,QAAMe,WAAWhB,eAAee;AAEhC,aACEZ,yBAACC,MAAAA;IACC,cAAAC,0BAACjB,iBAAAA;MAAiB,GAAGT;MAAOJ,SAASyC;;YACnCb,yBAACK,gBAAAA;UAAgBT;;YACjBI,yBAACpC,gBAAAA;UAAe2C,eAAW;UAACO,SAAQ;UAAKC,YAAYF,WAAW,SAAS;UACtED,UAAAA;;;;;AAKX;AAMA,IAAMI,OAAO,CAAC,EAAEpB,UAAU,GAAGpB,MAAAA,UAC3BwB,yBAACC,MAAAA;EACC,cAAAC,0BAACR,aAAAA;IAAa,GAAGlB;IAAOyC,IAAG;;UACzBjB,yBAACK,gBAAAA;QAAgBT;;UACjBI,yBAACpC,gBAAAA;QAAe2C,eAAW;QAACW,OAAK;QAAC,UAAA;;;;;AAe3BC,IAAAA,mBAAmB,CAAC,EAC/BtB,YACAuB,cACAC,YAAY,EAAEZ,UAAS,EAAE,MACH;AACtB,QAAM,EAAEa,cAAa,IAAKC,QAAAA;AAE1B,QAAMC,qBAAqB3B,aAAa;AACxC,QAAM4B,iBAAiB5B,aAAa;AAEpC,QAAM6B,aAAa;QACjB1B,yBAACW,UAAAA;MAECC,QAAQ;MACRe,SAAS,MAAA;AACPP,qBAAa,CAAA;MACf;gBAECE,cACC;QAAEM,IAAI;QAA+BC,gBAAgB;SACrD;QAAEC,MAAM;MAAE,CAAA;IARP,GAAA,CAAA;EAWR;AAED,MAAIrB,aAAa,GAAG;AAClB,UAAMsB,QAAQC,MAAMC,KAAK;MAAEC,QAAQzB;KAChC0B,EAAAA,IAAI,CAACC,GAAGC,MAAMA,IAAI,CAAA,EAClBF,IAAI,CAACvB,WAAAA;AACJ,iBACEZ,yBAACW,UAAAA;QAAsBC;QAAgBe,SAAS,MAAMP,aAAaR,MAAAA;kBAChEU,cACC;UAAEM,IAAI;UAA+BC,gBAAgB;WACrD;UAAEC,MAAMlB;QAAO,CAAA;MAHJA,GAAAA,MAAAA;IAOnB,CAAA;AAEF,eACEV,0BAACoC,YAAAA;MAAWzC;MAAwBY;;YAClCT,yBAACL,cAAAA;UAAagC,SAAS,MAAMP,aAAaI,kBAAAA;oBACvCF,cAAc;YACbM,IAAI;YACJC,gBAAgB;UAClB,CAAA;;QAEDE;YACD/B,yBAACQ,UAAAA;UAASmB,SAAS,MAAMP,aAAaK,cAAAA;oBACnCH,cAAc;YACbM,IAAI;YACJC,gBAAgB;UAClB,CAAA;;;;EAIR;AAEA,MAAIU,qBAA+B,CAAA;AACnC,QAAMC,YAA2B,CAAA;AACjC,MAAIC,oBAA8B,CAAA;AAClC,QAAMC,cAA6B,CAAA;AAEnC,MAAIjC,YAAY,GAAG;AACjB+B,cAAUG,SACR3C,yBAACW,UAAAA;MAAyBC,QAAQH;MAAWkB,SAAS,MAAMP,aAAaX,SAAAA;gBACtEa,cACC;QAAEM,IAAI;QAA+BC,gBAAgB;SACrD;QAAEC,MAAMrB;MAAU,CAAA;IAHPA,GAAAA,SAAAA,CAAAA;EAOnB;AAEA,MAAIZ,eAAe,KAAKY,aAAa,GAAG;AACtC8B,yBAAqB;MAAC;IAAE;EAC1B;AAEA,MAAI1C,eAAe,KAAKY,aAAa,GAAG;AACtC,QAAIA,cAAc,GAAG;AACnB8B,2BAAqB;QAAC;QAAG;QAAG;MAAE;eACrB9B,cAAc,GAAG;AAC1B8B,2BAAqB;QAAC;MAAE;WACnB;AACLA,2BAAqB;QAAC;QAAG;MAAE;IAC7B;EACF;AAEA,MAAI1C,eAAe,KAAKY,aAAa,GAAG;AACtC8B,yBAAqB;MAAC;IAAE;EAC1B;AAEA,MAAI1C,eAAeY,aAAaA,aAAa,GAAG;AAC9CgC,wBAAoB;MAAChC,YAAY;IAAE;EACrC;AAEA,MAAIZ,eAAeY,YAAY,KAAKA,YAAY,GAAG;AACjDgC,wBAAoB;MAAC5C,aAAa;MAAGA;MAAYA,aAAa;IAAE;EAClE;AAEA,MAAIA,eAAeY,YAAY,KAAKA,YAAY,KAAKZ,aAAa,GAAG;AACnE4C,wBAAoB;MAAC5C,aAAa;MAAGA,aAAa;MAAGA;MAAYA,aAAa;IAAE;EAClF;AAEA,MAAIA,eAAeY,YAAY,KAAKA,YAAY,GAAG;AACjDgC,wBAAoB;MAAC5C;MAAYA,aAAa;IAAE;EAClD;AAEA4C,oBAAkBG,QAAQ,CAAChC,WAAAA;AACzB4B,cAAUK,YACR3C,0BAACS,UAAAA;MAAsBC;MAAgBe,SAAS,MAAMP,aAAaR,MAAAA;;QAAS;QAC9DA;;IADCA,GAAAA,MAAAA,CAAAA;EAInB,CAAA;AAEA2B,qBAAmBK,QAAQ,CAAChC,WAAAA;AAC1Bc,eAAWiB,SACT3C,yBAACW,UAAAA;MAAsBC;MAAgBe,SAAS,MAAMP,aAAaR,MAAAA;gBAChEU,cACC;QAAEM,IAAI;QAA+BC,gBAAgB;SACrD;QAAEC,MAAMlB;MAAO,CAAA;IAHJA,GAAAA,MAAAA,CAAAA;EAOnB,CAAA;AAEA,MACE,CAAC;IAAC;IAAG;EAAE,EAACkC,SAASjD,UACjBA,KAAAA,cAAcY,YAAY,KAC1BiB,WAAWQ,SAASM,UAAUN,SAAS,GACvC;AACA,UAAMa,sBAAsB;MAAClD,aAAa;MAAGA;MAAYA,aAAa;IAAE;AAExEkD,wBAAoBH,QAAQ,CAAChC,WAAAA;AAC3B8B,kBAAYC,SACV3C,yBAACW,UAAAA;QAAsBC;QAAgBe,SAAS,MAAMP,aAAaR,MAAAA;kBAChEU,cACC;UAAEM,IAAI;UAA+BC,gBAAgB;WACrD;UAAEC,MAAMlB;QAAO,CAAA;MAHJA,GAAAA,MAAAA,CAAAA;IAOnB,CAAA;EACF;AAEA,QAAMoC,+BACJvC,YAAY,KAAMA,cAAc,MAAMZ,eAAe,KAAKA,eAAe;AAC3E,QAAMoD,uBAAuBP,YAAYR,SAAS,KAAKrC,aAAa,KAAKY,YAAY;AAErF,QAAMyC,wBAAwBD,uBAC1BxC,YAAYZ,aAAa,IACzBY,YAAYiB,WAAWQ,SAASM,UAAUN;AAC9C,QAAMiB,kBAAkBF,uBACpBxC,YAAYiB,WAAWQ,SAASM,UAAUN,SAC1CzB,YAAYZ,aAAa;AAE7B,aACEK,0BAACoC,YAAAA;IAAWzC;IAAwBY;;UAClCT,yBAACL,cAAAA;QAAagC,SAAS,MAAMP,aAAaI,kBAAAA;kBACvCF,cAAc;UACbM,IAAI;UACJC,gBAAgB;QAClB,CAAA;;MAEDH;MACAuB,4BACCjD,yBAACgB,MAAAA;kBACEM,cACC;UACEM,IAAI;UACJC,gBAAgB;WAElB;UAAEjB,QAAQsC;QAAsB,CAAA;;MAIrCR;MACAM,oCACChD,yBAACgB,MAAAA;kBACEM,cACC;UACEM,IAAI;UACJC,gBAAgB;WAElB;UAAEjB,QAAQuC;QAAgB,CAAA;;MAI/BX;UACDxC,yBAACQ,UAAAA;QAASmB,SAAS,MAAMP,aAAaK,cAAAA;kBACnCH,cAAc;UACbM,IAAI;UACJC,gBAAgB;QAClB,CAAA;;;;AAIR;;;;;;AC9WO,IAAMuB,cAAc,CAAC,EAAEC,gBAAgBC,aAAa,KAAI,MAAoB;AACjF,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,WAAU,IAAKC,YAAAA;AACvB,QAAM,CAACC,QAAQC,SAAU,IAASC,gBAAS,CAAC,CAACP,UAAAA;AAC7C,QAAM,CAACQ,OAAOC,QAAAA,IAAkBF,gBAASP,cAAc,EAAA;AACvD,QAAMU,aAAmBC,cAAuB,IAAA;AAEhDC,EAAMC,uBAAgB,MAAA;AACpB,QAAIR,QAAQ;AACVS,iBAAW,MAAA;;AACTJ,+BAAWK,YAAXL,mBAAoBM,cAAc,aAAlCN,mBAA4CO;SAC3C,CAAA;IACL;KACC;IAACZ;EAAO,CAAA;AAEX,QAAMa,eAAe,MAAA;AACnBZ,cAAU,CAACa,SAAS,CAACA,IAAAA;EACvB;AAEA,QAAMC,cAAc,MAAA;AAClBF,iBAAAA;AACAnB,mBAAe,IAAA;EACjB;AAEA,QAAMsB,eAAe,CAACC,MAAAA;AACpBA,MAAEC,eAAc;AAChBD,MAAEE,gBAAe;AAEjBrB,eAAW,iCAAiC;MAAEsB,UAAU;IAAkB,CAAA;AAC1E1B,mBAAeS,KAAAA;EACjB;AAEA,MAAIH,QAAQ;AACV,eACEqB,yBAACC,OAAAA;MAAIC,KAAKlB;MACR,cAAAgB,yBAACG,YAAAA;QAAWC,UAAUT;QACpB,cAAAK,yBAACK,WAAAA;UACCC,MAAK;UACLC,SAASb;UACTc,UAAU,CAACZ,MAAMb,SAASa,EAAEa,OAAO3B,KAAK;UACxC4B,YAAYnC,cAAc;YACxBoC,IAAIC,QAAQ,oBAAA;YACZC,gBAAgB;UAClB,CAAA;UACAC,cAAW;UACXC,MAAK;UACLjC;UACAkC,aAAazC,cAAc;YACzBoC,IAAIC,QAAQ,oBAAA;YACZC,gBAAgB;UAClB,CAAA;oBAECtC,cAAc;YAAEoC,IAAIC,QAAQ,cAAA;YAAiBC,gBAAgB;UAAsB,CAAA;;;;EAK9F;AAEA,aACEb,yBAACiB,YAAAA;IAAWC,OAAM;IAASC,SAAS3B;IAClC,cAAAQ,yBAACoB,cAAAA,CAAAA,CAAAA;;AAGP;;;ACjFaC,IAAAA,eAAe,CAACC,cAAwBC,OAAO,OAAE;AAC5D,MAAI,CAACA,KAAM,QAAO;AAElB,QAAMC,WAAWD,KAAKE,MAAM,GAAA,EAAK,CAAE;AAEnC,SACEH,aAAaI,SAASF,QAAAA,KACrBF,aAAaI,SAAS,MAAA,KAAW,CAAC;IAAC;IAAS;IAAS;EAAQ,EAACA,SAASF,QAAAA;AAE5E;;;ACsCA,IAAMG,qBAAqBC,GAAOC,UAAAA;;;AAIlC,IAAMC,kBAAkBF,GAAOG,GAAAA;;;cAGjB,CAAC,EAAEC,MAAK,MAAOA,MAAMC,OAAOC,UAAU;;;;AA2E7C,IAAMC,aAAa,CAAC,EACzBC,eAAe,CAAA,GACfC,QAAQC,WACRC,WACAC,SACAC,UAAU,CAAA,GACVC,WAAW,OACXC,YACAC,iBACAC,cACAC,kBACAC,gBACAC,cACAC,gBACAC,aACAC,cACAC,kBACAC,eACAC,YACAC,aACAC,eAAc,MACE;;AAChB,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,CAACC,MAAMC,OAAQ,IAAGC,mBAAmBC,iBAAiBC,WAAWC,YAAYC,IAAI;AACvF,QAAMC,aAAaP,SAASK,YAAYC;AAExC,QAAM,EAAEE,MAAMC,eAAeC,WAAWC,uBAAsB,IAAKC,UACjEhB,2CAAaiB,QACb;IACEC,SAASjC,WAAW,CAAC,EAACe,2CAAaiB;EACrC,CAAA;AAGF,QAAME,gBAAgBC,gBAAgBvC,YAAAA;AACtC,QAAMC,SAASC,UAAUsC,IAAI,CAACC,WAAW;IACvC,GAAGA;IACHC,cAAcA,aAAaJ,eAAeG,+BAAOE,IAAAA;IACjDC,MAAM;IACR;AAEA,QAAMC,cAAc,CAACX,yBACjBY,oBAAoBd,aACpBe,IAAAA;AAEJ,QAAMC,kBAAkBC,gBAAgBjD,cAAcC,MAAAA;AACtD,QAAMiD,sBACJF,gBAAgBG,SAAS,KACzB/B,eAAe+B,SAAS,KACxBH,gBAAgBI,MACd,CAACX,UAAUrB,eAAeiC,UAAU,CAACC,cAAcA,UAAUC,OAAOd,MAAMc,EAAE,MAAM,EAAC;AAEvF,QAAMC,uBAAuBR,gBAAgBS,KAC3C,CAAChB,UAAUrB,eAAeiC,UAAU,CAACC,cAAcA,UAAUC,OAAOd,MAAMc,EAAE,MAAM,EAAC;AAErF,QAAMG,cAAc,CAAC,EAACvC,2CAAawC;AACnC,QAAMC,cAAc,CAAC,GAACzC,sDAAa0C,YAAb1C,mBAAsB2C,SAAtB3C,mBAA4BgC,WAAUhC,YAAY0C,QAAQC,KAAKX,SAAS;AAC9F,QAAMY,yBAAyBL,eAAeE;AAC9C,QAAMI,aAAa/D,OAAOkD;AAC1B,QAAMc,cAAc5D,QAAQ8C;AAC5B,QAAMe,wBAAwB,IAAIC,SAAAA;AAGhCxD,mBAAe,EAAA;AACfE,mBAAkBsD,GAAAA,IAAAA;EACpB;AAEA,aACEC,0BAACzE,KAAAA;;MACEqB,wBACCqD,yBAAC1E,KAAAA;QAAI2E,eAAe;QAClB,cAAAF,0BAACG,MAAAA;UAAKC,gBAAe;UAAgBC,YAAW;;aAC5CT,aAAa,KAAKC,cAAc,KAAKL,oBACrCQ,0BAACG,MAAAA;cAAKG,KAAK;cAAGC,MAAK;;gBAChBrE,YAAYwB,kBACXuC,yBAACE,MAAAA;kBACCK,aAAa;kBACbC,cAAc;kBACdC,YAAW;kBACXC,WAAS;kBACTC,aAAY;kBACZC,QAAO;kBAEP,cAAAZ,yBAACa,cAAAA;oBACCC,cAAY9D,cAAc;sBACxBkC,IAAI6B,QAAQ,mBAAA;sBACZC,gBAAgB;oBAClB,CAAA;oBACAC,SACE,CAACpC,uBAAuBM,uBACpB,kBACAN;oBAENqC,iBAAiBvE;;;gBAItBc,kBAAcuC,yBAACmB,YAAAA;kBAAW5E;kBAA4B6E,OAAOtE,2CAAauE;;oBAC3ErB,yBAACsB,SAAAA;kBACCC,iBAAgBzE,gDAAa0C,YAAb1C,mBAAsB2C;kBACtCtD;;;;aAKJwD,aAAa,KAAKC,cAAc,KAAKP,oBACrCU,0BAACG,MAAAA;cAAKsB,YAAW;cAAOC,QAAQ;cAAGpB,KAAK;;oBACtCL,yBAAC3E,iBAAAA;kBAAgBqG,YAAY;kBAAGzB,eAAe;kBAC7C,cAAAD,yBAAC2B,YAAAA;oBACCC,OACEnE,aACIT,cAAc;sBACZkC,IAAI;sBACJ8B,gBAAgB;oBAClB,CAAA,IACAhE,cAAc;sBACZkC,IAAI;sBACJ8B,gBAAgB;oBAClB,CAAA;oBAENa,SAAS,MAAM1E,QAAQM,aAAaF,YAAYuE,OAAOvE,YAAYC,IAAI;8BAEtEC,iBAAauC,yBAAC+B,eAAAA,CAAAA,CAAAA,QAAU/B,yBAACgC,eAAAA,CAAAA,CAAAA;;;oBAG9BhC,yBAACiC,aAAAA;kBAAY3F;kBAAgC4F,YAAYpF,YAAYwC,MAAM;;;;;;;MAOpFvD,YAAWyC,2CAAaM,WAAUN,YAAYM,SAAS,KAAKnB,qBAC3DqC,yBAAC1E,KAAAA;QAAIoG,YAAY;QACf,cAAA1B,yBAACmC,aAAAA;UACC3F;UACAoF,OAAO5E,cAAc;YACnBkC,IAAI6B,QAAQ,8BAAA;YACZC,gBAAgB;UAClB,CAAA;UACAxC;UACA4D,iBAAiBtF,2CAAaiB;;;MAKnC4B,eAAe,KAAKC,gBAAgB,SACnCI,yBAAC1E,KAAAA;QAAI2E,eAAe;QAClB,cAAAD,yBAACqC,aAAAA;UACCC,MAAK;UACLC,OAAO;UACPC,QACE1G,aACA,CAACyD,eACD,CAACF,mBACCW,yBAACyC,QAAAA;YAAOC,SAAQ;YAAYC,eAAW3C,yBAAC4C,eAAAA,CAAAA,CAAAA;YAASf,SAAS3F;sBACvDc,cAAc;cACbkC,IAAI6B,QAAQ,2BAAA;cACZC,gBAAgB;YAClB,CAAA;;UAIN6B;;YAEEnD,yBACI1C,cAAc;cACZkC,IAAI6B,QAAQ,oCAAA;cACZC,gBAAgB;aAElBlF,IAAAA,aAAa,CAACuD,cACZrC,cAAc;cACZkC,IAAI6B,QAAQ,mBAAA;cACZC,gBAAgB;YAClB,CAAA,IACAhE,cAAc;cACZkC,IAAI6B,QAAQ,kCAAA;cACZC,gBAAgB;YAClB,CAAA;;;;MAMb,CAACvD,eAAemC,cAAc,KAAKD,aAAa,UAC/CK,yBAAC8C,WAAAA;QACCnH;QACAgE;QACAC;QACAmD,eAAe,CAAClE,uBAAuBM;QACvC6D,0BAA0B;QAC1BzG;QACAC,gBAAgBqD;QAChBpD;QACAC;QACAuG,aAAarG;QACbsG,aAAavG;QACbwG,MACE;UAAInH,GAAAA,QAAQmC,IAAI,CAACJ,YAAY;YAAE,GAAGA;YAAQQ,MAAM;YAAS;UAAQ3C,GAAAA;QAAO;QAI1EwH,UAAUrG;QACVsG,yBAAyB,CAACpH;QAC1BqH,YAAWxG,2CAAauE,SAAQ;;MAInC5D,kBACCsC,0BAAAwD,8BAAA;;UACG3D,cAAc,SACbI,yBAACwD,gBAAAA;YACCC,QACI,0BAA2B9D,aAAa,KAAM,CAACD,2BAC/C1C,cACE;cACEkC,IAAI6B,QAAQ,oBAAA;cACZC,gBAAgB;eAElB;cAAEuB,OAAO3C;aAEb,KAAA;sBAGD5D,QAAQmC,IAAI,CAACJ,WAAAA;;AACZ,yBACEiC,yBAAC0D,KAAKC,MAAI;gBACRC,KAAK;gBAELC,WAAU;gBACVzD,YAAW;gBAEX,cAAAJ,yBAAC8D,YAAAA;kBACCC,WAAWhG,OAAOiG;kBAClB9E,IAAI,UAAUnB,OAAOmB,EAAE;kBACvB2C,SAAS,MAAMhC,sBAAsB9B,OAAOmB,IAAInB,OAAOkG,IAAI;kBAC3DC,aACExH,oBACEsD,yBAAC2B,YAAAA;oBACCwC,aAAa;oBACbvC,OAAO5E,cAAc;sBACnBkC,IAAI6B,QAAQ,kBAAA;sBACZC,gBAAgB;oBAClB,CAAA;oBACAa,SAAS,MAAMnF,aAAaqB,MAAAA;oBAE5B,cAAAiC,yBAACoE,eAAAA,CAAAA,CAAAA;;kBAKP,cAAApE,yBAACqE,gBAAAA;oBACC,cAAArE,yBAACsE,sBAAAA;sBACCzC,SAAS,MAAMhC,sBAAsB9B,OAAOmB,IAAInB,OAAOkG,IAAI;sBAE3D,cAAAlE,0BAACG,MAAAA;wBAAKqE,KAAI;wBAAKV,WAAU;wBAASzD,YAAW;wBAAQoE,UAAS;;8BAC5DzE,0BAAC7E,oBAAAA;4BACCuJ,YAAW;4BACXC,UAAQ;4BACRC,WAAU;;8BAET5G,OAAOiG;kCAGRhE,yBAAC4E,gBAAAA;gCAAe,UAAA;;;;8BAElB5E,yBAAC9E,oBAAAA;4BACCqJ,KAAI;4BACJI,WAAU;4BACVjC,SAAQ;4BACRgC,UAAQ;sCAEP1H,cACC;8BACEkC,IAAI6B,QAAQ,sBAAA;8BACZC,gBACE;+BAEJ;8BACEpB,cAAa7B,MAAAA,OAAO8G,aAAP9G,gBAAAA,IAAiBwE;8BAC9BuC,aAAY/G,MAAAA,OAAOgH,UAAPhH,gBAAAA,IAAcwE;4BAC5B,CAAA;;;;;;;cArDP,GAAA,UAAUxE,OAAOmB,EAAE,EAAE;YA8DhC,CAAA;;UAIHS,aAAa,KAAKC,cAAc,SAC/BI,yBAAC1E,KAAAA;YAAIoG,YAAY;YACf,cAAA1B,yBAACgF,SAAAA,CAAAA,CAAAA;;UAIJrF,aAAa,SACZK,yBAAC1E,KAAAA;YAAIoG,YAAY;YACf,cAAA1B,yBAACiF,eAAAA;cACCtJ;cACA2G,MAAK;cACL1G;cACAgB;cACAG;cACAN;cACAgH,QACI,CAAC/D,0BAA2BA,0BAA0BE,cAAc,MACpE9C,YAAYoI,SAAS,KACrBlI,cACE;gBACEkC,IAAI6B,QAAQ,mBAAA;gBACZC,gBAAgB;iBAElB;gBAAEuB,OAAO5C;eAEb,KAAA;;;;;MAQX9C,WAAWsI,YAAY,SACtBpF,0BAACG,MAAAA;QAAKC,gBAAe;QAAgBuB,YAAY;QAAG0D,UAAS;QAAWC,QAAQ;;cAC9ErF,yBAACsF,UAAAA;YACCC,UAAUzI,YAAYyI;YACtBlJ;;cAEF2D,yBAACwF,kBAAAA;YACCC,YAAY3I,YAAYoI;YACxB9I;YACAS;;;;;;AAMZ;;;;IC9ca6I,eAAe,CAAC,EAAEC,SAASC,WAAU,MAAqB;AACrE,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,aACEC,0BAACC,MAAMC,QAAM;;UACXC,yBAACC,QAAAA;QAAOC,SAAST;QAASU,SAAQ;kBAC/BR,cAAc;UAAES,IAAI;UAAgCC,gBAAgB;QAAS,CAAA;;MAE/EX,kBACCM,yBAACC,QAAAA;QAAOC,SAASR;kBACdC,cAAc;UAAES,IAAI;UAAiBC,gBAAgB;QAAS,CAAA;;;;AAKzE;;;;;ACRO,IAAMC,eAAe,CAAC,EAC3BC,gBACAC,eACAC,eAAc,MACI;AAClB,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,aACEC,0BAACC,MAAAA;IAAKC,WAAU;IAASC,YAAW;IAAUC,KAAK;;UACjDJ,0BAACC,MAAAA;QAAKG,KAAK;QAAGF,WAAU;QAASC,YAAW;;cAC1CE,yBAACC,YAAAA;YAAWC,SAAQ;YAAKC,YAAW;YAAOC,WAAU;sBAClDX,cACC;cACEY,IAAIC,QAAQ,uBAAA;cACZC,gBACE;eAEJ;cAAEC,QAAQlB,eAAemB;YAAO,CAAA;;cAGpCT,yBAACC,YAAAA;YAAWC,SAAQ;YAAKE,WAAU;sBAChCX,cAAc;cACbY,IAAIC,QAAQ,uCAAA;cACZC,gBAAgB;YAClB,CAAA;;;;UAIJP,yBAACU,eAAAA;QACCC,MAAK;QACLC,QAAQtB;QACRC;QACAD;QACAE;;;;AAIR;;;AChBA,IAAMqB,cAAcC,GAAOC,IAAAA;;gBAEX,MAAM,mBAAmB;;AA4BlC,IAAMC,eAAe,CAAC,EAC3BC,eAAe,CAAA,GACfC,WAAW,MACXC,SACAC,YACAC,aACAC,gBACAC,YACAC,WAAW,OACXC,0BAA0B,CAAA,GAC1BC,gBAAe,MACG;AAClB,QAAM,CAACC,aAAaC,cAAAA,IAAwBC,gBAAmCC,MAAAA;AAC/E,QAAM,CAACC,cAAcC,eAAAA,IAAyBH,gBAAgCC,MAAAA;AAC9E,QAAM,EAAEG,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EACJC,SACAC,WACAC,WAAWC,sBACXC,WACAC,aACAC,YAAW,IACTC,2BAAAA;AAEJ,QAAM,CACJ,EAAEC,YAAW,GACb,EACEC,iBACAC,cACAC,kBACAC,cACAC,gBACA1B,gBAAgB2B,oBAAmB,CACpC,IACCC,oBAAoB;IAAEC,QAAQjC;EAAS,CAAA;AAE3C,QAAM,EACJkC,MAAM,EAAEC,YAAYC,SAASC,OAAM,IAAK,CAAA,GACxClB,WAAWmB,iBACXC,OAAOC,YAAW,IAChBC,UAAU;IAAEC,UAAU,CAACzB;IAAS0B,OAAOlB;EAAY,CAAA;AAEvD,QAAM,EACJS,MAAMU,SACNzB,WAAW0B,kBACXN,OAAOO,aAAY,IACjBC,WAAW;IACbC,SAAS/B,WAAW,CAACgC,oBAAoBxB,WAAAA,MAAiBU,yCAAYe,UAAS;IAC/EP,OAAOlB;EACT,CAAA;AAEA,QAAM,CACJ0B,gBACA,EAAEC,WAAWC,YAAYC,eAAeC,gBAAgBC,iBAAgB,CAAE,IACxEC,kBAAkB;IAAC;KAAOlD,uBAAAA;AAE9B,QAAMmD,wBAAwB,MAAA;AAC5B,UAAMC,gBAAgBC,gBAAgB7D,cAAcsC,MAAAA;AAEpD,QAAI,CAAC/B,UAAU;AACb,aAAOM;IACT;AAGA,UAAMiD,kBAAkBF,cAAcG,OACpC,CAACC,UAAUZ,eAAea,UAAU,CAACC,kBAAkBA,cAAcC,OAAOH,MAAMG,EAAE,MAAM,EAAC;AAG7F,QAAIL,gBAAgBM,SAAS,GAAG;AAC9BX,uBAAiBK,eAAAA;WACZ;AACLN,qBAAeI,aAAAA;IACjB;EACF;AAEA,QAAMS,oBAAoB,CAACL,UAAAA;AACzB,WAAOzD,WAAW8C,UAAUW,KAAAA,IAAkBV,WAAWU,KAAAA;EAC3D;AAEA,QAAM5C,YAAYC,wBAAwBkB,mBAAmBO;AAC7D,QAAMwB,WAAW7B,eAAeM;AAEhC,QAAM,CAACwB,WAAWC,YAAAA,IAAsB5D,gBACtCwC,eAAegB,SAAS,IAAI,aAAa,QAAA;AAG3C,MAAIhD,WAAW;AACb,eACEqD,0BAAAC,8BAAA;;YACEC,yBAACC,MAAMC,QAAM;wBACXF,yBAACC,MAAME,OAAK;sBACT9D,cAAc;cACbmD,IAAIY,QAAQ,2BAAA;cACZC,gBAAgB;YAClB,CAAA;;;YAGJL,yBAAC/E,aAAAA;UAAYqF,gBAAe;UAASC,YAAY;UAAGC,eAAe;UACjE,cAAAR,yBAACS,QAAAA;sBACEpE,cAAc;cACbmD,IAAIY,QAAQ,mBAAA;cACZC,gBAAgB;YAClB,CAAA;;;YAGJL,yBAACU,cAAAA;UAAanF;;;;EAGpB;AAEA,MAAIoE,UAAU;AACZ,eACEG,0BAAAC,8BAAA;;YACEC,yBAACC,MAAMC,QAAM;wBACXF,yBAACC,MAAME,OAAK;sBACT9D,cAAc;cACbmD,IAAIY,QAAQ,2BAAA;cACZC,gBAAgB;YAClB,CAAA;;;YAGJL,yBAACW,KAAKC,OAAK,CAAA,CAAA;YACXZ,yBAACU,cAAAA;UAAanF;;;;EAGpB;AAEA,MAAI,CAACgB,SAAS;AACZ,eACEuD,0BAAAC,8BAAA;;YACEC,yBAACC,MAAMC,QAAM;wBACXF,yBAACC,MAAME,OAAK;sBACT9D,cAAc;cACbmD,IAAIY,QAAQ,2BAAA;cACZC,gBAAgB;YAClB,CAAA;;;YAGJL,yBAACW,KAAKE,eAAa,CAAA,CAAA;YACnBb,yBAACU,cAAAA;UAAanF;;;;EAGpB;AAEA,MAAIQ,aAAa;AACf,eACEiE,yBAACc,kBAAAA;MACCvF,SAAS,MAAMS,eAAeE,MAAAA;MAC9BmD,OAAOtD;MACPY;MACAC;MACAC;MACAf;;EAGN;AAEA,MAAIK,cAAc;AAChB,eACE6D,yBAACe,mBAAAA;MACCxD,QAAQpB;MACRZ,SAAS,MAAMa,gBAAgBF,MAAAA;MAC/B8E,UAAS;MACTC,gBAAgBlE,2CAAaQ;;EAGnC;AAEA,QAAM2D,iBAAiB,CAACC,YAAoBC,cAAAA;AAC1C,UAAMC,SAASD,YAAYD;AAC3B,UAAMG,qBAAqB7C,eAAe8C,MAAK;AAC/C,UAAMC,aAAaC,YAAmBH,oBAAoBH,YAAYE,MAAAA;AACtEzC,kBAAc4C,UAAAA;EAChB;AAEA,QAAME,qBAAqB,CAACpG,WAAkBqG,eAAAA;AAC5CjG,mBAAeJ,SAAAA;AACf,QAAI+B,qBAAqB;AACvBA,0BAAoB/B,WAAUqG,UAAAA;IAChC;EACF;AAEA,aACE7B,0BAAAC,8BAAA;;UACEC,yBAACC,MAAMC,QAAM;sBACXF,yBAACC,MAAME,OAAK;oBACT9D,cAAc;YACbmD,IAAIY,QAAQ,2BAAA;YACZC,gBAAgB;UAClB,CAAA;;;UAIJP,0BAAC8B,UAAAA;QAASC,SAAQ;QAASC,OAAOlC;QAAWmC,eAAelC;;cAC1DC,0BAAC3E,MAAAA;YAAK6G,aAAa;YAAGC,cAAc;YAAG1B,YAAY;YAAGD,gBAAe;;kBACnER,0BAACoC,KAAKC,MAAI;;sBACRnC,yBAACkC,KAAKE,SAAO;oBAACN,OAAM;8BACjBzF,cAAc;sBACbmD,IAAIY,QAAQ,kBAAA;sBACZC,gBAAgB;oBAClB,CAAA;;sBAEFP,0BAACoC,KAAKE,SAAO;oBAACN,OAAM;;sBACjBzF,cAAc;wBACbmD,IAAIY,QAAQ,2BAAA;wBACZC,gBAAgB;sBAClB,CAAA;0BACAL,yBAACqC,OAAAA;wBAAMC,YAAY;wBAAI7D,UAAAA,eAAegB;;;;;;kBAG1CK,0BAAC3E,MAAAA;gBAAKoH,KAAK;;sBACTvC,yBAACwC,QAAAA;oBACCX,SAAQ;oBACRY,SAAS,MAAMhH,YAAY;sBAAEH,UAAUyB,2CAAaQ;oBAAO,CAAA;8BAE1DlB,cAAc;sBACbmD,IAAIY,QAAQ,yCAAA;sBACZC,gBAAgB;oBAClB,CAAA;;sBAEFL,yBAACwC,QAAAA;oBAAOC,SAAS,MAAMjH,WAAW;sBAAEF,UAAUyB,2CAAaQ;oBAAO,CAAA;8BAC/DlB,cAAc;sBACbmD,IAAIY,QAAQ,qCAAA;sBACZC,gBAAgB;oBAClB,CAAA;;;;;;cAINL,yBAAC0C,SAAAA,CAAAA,CAAAA;cACD5C,0BAACG,MAAM0C,MAAI;;kBACT3C,yBAACkC,KAAKU,SAAO;gBAACd,OAAM;gBAClB,cAAA9B,yBAAC6C,YAAAA;kBACCxH;kBACAsC;kBACAnB;kBACAD;kBACA2B;kBACA4E,eAAepD;kBACfjB;kBACA7C;kBACAmH,kBAAkB/D;kBAClBgE,aAAahH;kBACbiH,cAAc7G;kBACdqB;kBACAV;kBACAvB;kBACAwB,iBAAiB,CAACkG,YAChBlG,gBAAiBkG,OAAAA;kBAEnBxH,gBAAgBgG;kBAChBzE;kBACAC;kBACAC,cAAc,CAACgG,SAA6BhG,aAAcgG,IAAAA;kBAC1D/F;;;kBAGJ4C,yBAACkC,KAAKU,SAAO;gBAACd,OAAM;gBAClB,cAAA9B,yBAACoD,cAAAA;kBACC3E;kBACAqE,eAAepD;kBACf2D,gBAAgBnC;;;;;;;UAKxBlB,yBAACU,cAAAA;QAAanF;QAAkBI,YAAY,MAAMA,WAAW8C,cAAAA;;;;AAGnE;AAMO,IAAM6E,cAAc,CAAC,EAAEC,OAAO,OAAOhI,SAAS,GAAGiI,UAA6B,MAAA;AACnF,aACExD,yBAACC,MAAMwD,MAAI;IAACF;IAAYG,cAAcnI;kBACpCyE,yBAACC,MAAM2C,SAAO;MACZ,cAAA5C,yBAAC5E,cAAAA;QAAaG;QAAmB,GAAGiI;;;;AAI5C;AAEA,IAAM5B,WAAW1G,GAAOgH,KAAKuB,IAAI;;;;;;;ACvVjC,IAAME,QAAQ;EACZC,aAAa;EACbC,aAAa;EACbC,cAAc;AAChB;AAUO,IAAMC,qBAAqB,CAAC,EACjCC,SACAC,gBACAC,eAAe;EAAC;EAAS;EAAU;EAAU;EAAS,MAC9B;AACxB,QAAM,CAACC,MAAMC,OAAQ,IAASC,gBAASV,MAAMC,WAAW;AACxD,QAAM,CAACU,UAAUC,WAAAA,IAAqBF,gBAAwB,IAAA;AAE9D,UAAQF,MAAAA;IACN,KAAKR,MAAMC;AACT,iBACEY,0BAACC,aAAAA;QACCP;QACAI;QACAI,MAAI;QACJV;QACAW,YAAYV;QACZW,YAAY,MAAMR,QAAQT,MAAME,WAAW;QAC3CgB,aAAa,MAAMT,QAAQT,MAAMG,YAAY;QAC7CgB,gBAAgB,CAACR,cAAaC,YAAYD,SAAAA;QAC1CS,UAAQ;;IAId,KAAKpB,MAAMG;AACT,iBACEU,0BAACQ,kBAAAA;QACCN,MAAI;QACJV,SAAS,MAAMI,QAAQT,MAAMC,WAAW;QACxCqB,gBAAgBX;;IAItB;AACE,iBACEE,0BAACU,mBAAAA;QAAkBR,MAAI;QAACV,SAAS,MAAMI,QAAQT,MAAMC,WAAW;QAAGU;;EAEzE;AACF;;;;;;;;;;;;;;AChDA,IAAMa,WAAWC,GAAOC,IAAAA;;;AAIxB,IAAMC,sBAAsBF,GAAOG,GAAAA;;;;;;;AAQnC,IAAMC,sBAAsBJ,GAAOG,GAAAA;;;;;;AAOtBE,IAAAA,gBAAgB,CAAC,EAAEC,MAAK,MAAwB;;AAC3D,OAAIA,WAAMC,SAAND,mBAAYE,SAASC,UAAUC,QAAQ;AACzC,eACEC,0BAACT,qBAAAA;MAAoBU,QAAO;MAC1B,cAAAD,0BAACE,cAAAA;QACCC,KAAKC,eAAeT,OAAO,IAAA;QAC3BC,MAAMD,MAAMC;QACZS,KAAKV,MAAMW,mBAAmBX,MAAMY;;;EAI5C;AAEA,OAAIZ,WAAMC,SAAND,mBAAYE,SAASC,UAAUU,QAAQ;AACzC,eACER,0BAACP,qBAAAA;MACC,cAAAO,0BAACS,cAAAA;QACCN,KAAKC,eAAeT,OAAO,IAAA;QAC3BU,KAAKV,MAAMW,mBAAmBX,MAAMY;;;EAI5C;AAEA,OAAIZ,WAAMC,SAAND,mBAAYE,SAASC,UAAUY,QAAQ;AACzC,UAAMC,WAAWP,eAAeT,OAAO,IAAA;AACvC,QAAI,CAACgB,SAAU,QAAO;AAGtB,UAAMC,iBAAiB,GAAGD,QAAS,GAAEA,SAASd,SAAS,GAAO,IAAA,MAAM,GAAA,aAAgBF,MAAMkB,SAAS;AAEnG,eACEb,0BAACR,KAAAA;MACCsB,KAAI;MACJC,WAAU;MACVC,UAAS;MACTC,KAAKL;MACLP,KAAKV,MAAMW,mBAAmBX,MAAMY;;EAG1C;AAEA,aACEP,0BAACZ,UAAAA;IAAS8B,OAAM;IAAOjB,QAAO;IAAOkB,gBAAe;IAASC,WAAS;IACnEzB,YAAAA,WAAM0B,QAAN1B,mBAAWE,SAAS,cACnBG,0BAACsB,eAAAA;MAAQC,cAAY5B,MAAMW,mBAAmBX,MAAMY;MAAMW,OAAM;MAAOjB,QAAO;aAE9ED,0BAACwB,eAAAA;MAAKD,cAAY5B,MAAMW,mBAAmBX,MAAMY;MAAMW,OAAM;MAAOjB,QAAO;;;AAInF;;;;;ACjEO,IAAMwB,uBAAuB,CAAC,EACnCC,OACAC,eACAC,YACAC,YAAW,MACe;AAC1B,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,aACEC,2BAACC,iBAAAA;;MACEL,kBACCM,0BAACC,YAAAA;QACCC,OAAON,cAAc;UACnBO,IAAIC,QAAQ,kBAAA;UACZC,gBAAgB;QAClB,CAAA;QACAC,SAAS,MAAMZ,WAAWF,KAAAA;QAE1B,cAAAQ,0BAACO,eAAAA,CAAAA,CAAAA;;UAILP,0BAACQ,gBAAAA;QAAeC,KAAKC,4BAA4BlB,MAAMiB,GAAG;;MAEzDhB,qBACCO,0BAACC,YAAAA;QACCC,OAAON,cAAc;UACnBO,IAAI;UACJE,gBAAgB;QAClB,CAAA;QACAC,SAAS,MAAMb,cAAcD,KAAAA;QAE7B,cAAAQ,0BAACW,cAAAA,CAAAA,CAAAA;;MAIJhB,mBACCK,0BAACC,YAAAA;QACCC,OAAON,cAAc;UACnBO,IAAIC,QAAQ,mBAAA;UACZC,gBAAgB;QAClB,CAAA;QACAC,SAASX;QAET,cAAAK,0BAACY,eAAAA,CAAAA,CAAAA;;;;AAKX;;;;;;ACrDA,IAAMC,sBAAsBC,GAAOC,UAAAA;;;AAY5B,IAAMC,kBAAkB,CAAC,EAC9BC,WAAW,OACXC,SACAC,YAAW,MACU;AACrB,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,CAACC,UAAUC,WAAAA,IAAqBC,gBAAS,KAAA;AAE/C,QAAMC,kBAAkB,CAACC,MAAAA;AACvBA,MAAEC,eAAc;AAChBJ,gBAAY,IAAA;EACd;AAEA,QAAMK,kBAAkB,CAACF,MAAAA;AACvB,QAAI,CAACA,EAAEG,cAAcC,SAASJ,EAAEK,aAAa,GAAW;AACtDR,kBAAY,KAAA;IACd;EACF;AAEA,QAAMS,iBAAiB,CAACN,MAAAA;AACtBA,MAAEC,eAAc;EAClB;AAEA,QAAMM,aAAa,CAACP,MAAAA;;AAClBA,MAAEC,eAAc;AAEhB,SAAID,4BAAGQ,iBAAHR,mBAAiBS,OAAO;AAC1B,YAAMA,QAAQT,EAAEQ,aAAaC;AAC7B,YAAMC,SAA8B,CAAA;AAEpC,eAASC,IAAI,GAAGA,IAAIF,MAAMG,QAAQD,KAAK;AACrC,cAAME,OAAOJ,MAAMK,KAAKH,CAAAA;AACxB,YAAIE,MAAM;AACR,gBAAME,QAAQC,eAAeH,MAAMI,YAAYC,QAAQ;AAEvDR,iBAAOS,KAAKJ,KAAAA;QACd;MACF;AAEAtB,kBAAYiB,MAAAA;IACd;AAEAb,gBAAY,KAAA;EACd;AAEA,aACEuB,2BAACC,MAAAA;IACCC,aAAa1B,WAAW,WAAW2B;IACnCC,aAAa5B,WAAW,QAAQ2B;IAChCE,aAAa7B,WAAW,eAAe2B;IACvCG,WAAU;IACVC,gBAAe;IACfC,YAAW;IACXC,QAAO;IACPC,OAAM;IACNC,KAAI;IACJC,MAAK;IACLzC;IACAC;IACAyC,aAAalC;IACbmC,aAAahC;IACbiC,YAAY7B;IACZ8B,QAAQ7B;IACR8B,KAAK;IACLC,OAAO;MAAEC,QAAQhD,WAAW,gBAAgB;IAAU;;UAEtDiD,0BAACC,eAAAA;QACCC,eAAW;QACXZ,OAAM;QACND,QAAO;QACPc,MAAMpD,WAAW,eAAe;;UAElCiD,0BAACrD,qBAAAA;QACCyD,SAAQ;QACRC,YAAW;QACXC,WAAU;QACVR,OAAO;UAAES,WAAW;QAAS;QAC7BhB,KAAI;kBAEHrC,cAAc;UACbsD,IAAIC,QAAQ,+BAAA;UACZC,gBAAgB;QAClB,CAAA;;;;AAIR;;;ICrEaC,iBAAuBC,kBAClC,CACE,EACEC,QACAC,WAAW,OACXC,OACAC,MACAC,OACAC,aACAC,YACAC,eACAC,+BACAC,aACAC,aACAC,QACAC,YACAC,WAAW,OACXC,oBACAC,gBAAe,GAEjBC,iBAAAA;AAEA,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,CAACC,gBAAgBC,iBAAAA,IAA2BC,gBAAS,KAAA;AAE3D,QAAMC,eAAetB,OAAOc,kBAAmB;AAE/C,aACES,2BAAAC,+BAAA;;UACEC,0BAACC,eAAAA;QACCC,KAAKX;QACLZ;QACAC;QACAuB,gBAAgBN,6CAAcO;QAC9BC,eAAehB;QACfiB,eAAed,cAAc;UAC3Be,IAAIC,QAAQ,yCAAA;UACZC,gBAAgB;QAClB,CAAA;QACAC,WAAWlB,cAAc;UACvBe,IAAIC,QAAQ,qCAAA;UACZC,gBAAgB;QAClB,CAAA;QACAvB;QACAC;QACAT;QACAD;QACAW;QACAuB,SACEd,mBACEG,0BAACY,sBAAAA;UACCC,OAAOhB;UACPf,eAAeN,WAAWsC,SAAYhC;UACtCD,YAAYL,WAAWsC,SAAYjC;UACnCI,aAAaA,cAAc,MAAMU,kBAAkB,IAAQmB,IAAAA;QAE3DA,CAAAA,IAAAA;kBAGLvC,OAAOwC,WAAW,QACjBf,0BAACgB,eAAAA;UACCrC,OAAOa,cACL;YACEe,IAAIC,QAAQ,8BAAA;YACZC,gBAAgB;aAElB;YAAEQ,GAAG;YAAGC,GAAG;UAAE,CAAA;UAGf,cAAAlB,0BAACmB,iBAAAA;YACC3C;YACA4C,SAASvC;YACTG;;QAIJT,CAAAA,IAAAA,OAAO8C,IAAI,CAACR,OAAOS,eACjBtB,0BAACgB,eAAAA;UAECrC,OAAOa,cACL;YACEe,IAAIC,QAAQ,8BAAA;YACZC,gBAAgB;aAElB;YAAEQ,GAAGK,SAAQ;YAAGJ,GAAG3C,OAAOwC;UAAO,CAAA;UAGnC,cAAAf,0BAACuB,eAAAA;YAAcV;;QATVA,GAAAA,MAAMN,EAAE,CAAA;;UAcrBP,0BAACwB,iBAAAA;QACCC,MAAM/B;QACNgC,SAAS,CAACC,gBAAAA;AACRhC,4BAAkB,KAAA;AAGlB,cAAIgC,gBAAgB,MAAM;AACxB5C,0CAAAA;UACF;AACA,cAAI4C,eAAe,OAAOA,gBAAgB,WAAW;AACnD1C,uDAAc0C;UAChB;QACF;QACAd,OAAOhB;QACP+B,WAAS;QACTC,aAAW;QACXC,aAAW;QACXxC;;;;AAIR,CACA;;;AC3IF,IAAMyC,SAAQ;EACZC,aAAa;EACbC,aAAa;EACbC,cAAc;AAChB;AAeaC,IAAAA,oBAA0BC,kBACrC,CACE,EACEC,WAAW,EAAEC,eAAe,MAAMC,WAAW,MAAK,IAAK,CAAA,GACvDC,OACAC,MACAC,WAAW,OACXC,cAAcC,QACdC,MAAAA,OACAC,WAAW,MAAK,GAElBC,iBAAAA;AAEA,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,UAAUC,OAAOC,MAAK,IAAKC,SAASR,KAAAA;AAC5C,QAAM,CAACS,eAAeC,gBAAAA,IAA0BC,gBAA2B,CAAA,CAAE;AAC7E,QAAM,CAACC,MAAMC,OAAAA,IAAiBF,gBAA6BZ,MAAAA;AAC3D,QAAM,CAACe,eAAeC,gBAAAA,IAA0BJ,gBAAS,CAAA;AACzD,QAAM,CAACK,eAAeC,gBAAiB,IAASN,gBAAQ;AACxD,QAAM,CAACO,UAAUC,WAAAA,IAAqBR,gBAAwB,IAAA;AAC9D,QAAM,EAAES,mBAAkB,IAAKC,gBAAAA;AAE/BC,EAAMC,iBAAU,MAAA;AAEd,QAAIX,SAASb,QAAW;AACtBW,uBAAiB,CAAA,CAAE;IACrB;KACC;IAACE;EAAK,CAAA;AAET,MAAIY,iBAAyB,CAAA;AAE7B,MAAIC,MAAMC,QAAQpB,KAAQ,GAAA;AACxBkB,qBAAiBlB;EACnB,WAAWA,OAAO;AAChBkB,qBAAiB;MAAClB;IAAM;EAC1B;AAEA,QAAMqB,mBAAmB,CAACC,uBAAAA;AACxB,UAAMtB,SAAQZ,WAAWkC,qBAAqBA,mBAAmB,CAAE;AACnEvB,aAASL,OAAMM,MAAAA;AACfO,YAAQd,MAAAA;EACV;AAEA,QAAM8B,oCAAoC,MAAA;AACxC,QAAIC;AAEJ,QAAIpC,UAAU;AACZ,YAAMkC,qBAAqBJ,eAAeO,OACxC,CAACC,GAAGC,eAAeA,eAAenB,aAAAA;AAEpCgB,kBAAYF,mBAAmBM,SAAS,IAAIN,qBAAqB;WAC5D;AACLE,kBAAY;IACd;AAEA,UAAMxB,SAAQwB;AACdzB,aAASL,OAAMM,MAAAA;AAEfS,qBAAiB,CAAA;EACnB;AAEA,QAAMoB,oBAAoB,CAACC,UAAAA;AACzB,QAAIN;AAEJ,QAAIpC,UAAU;AACZ,YAAMkC,qBAAqBJ,eAAeO,OAAO,CAACM,cAAcA,UAAUC,OAAOF,MAAME,EAAE;AAEzFR,kBAAYF,mBAAmBM,SAAS,IAAIN,qBAAqB;WAC5D;AACLE,kBAAY;IACd;AAEAzB,aAASL,OAAM8B,SAAAA;AAEff,qBAAiB,CAAA;EACnB;AAEA,QAAMwB,kBAAkB,CAACH,UAAAA;AACvB,UAAMR,qBAAqBJ,eAAegB,IAAI,CAACH,cAC7CA,UAAUC,OAAOF,MAAME,KAAKF,QAAQC,SAAAA;AAGtChC,aAASL,OAAMN,WAAWkC,qBAAqBA,mBAAmB,CAAE,CAAA;EACtE;AAEA,QAAMa,sBAAsB,CAC1BC,QACAC,aAAAA;AAEA,UAAMC,gBAAgBC,gBAAgBpD,cAAciD,MAAAA;AAEpD,QAAIE,cAAcV,SAAS,GAAG;AAC5BS,eAASC,aAAAA;WACJ;AACLxB,yBAAmB;QACjB0B,MAAM;QACNC,SAAS;QACTC,SAAS7C,cACP;UACEmC,IAAIW,QAAQ,kCAAA;UACZC,gBAAgB;WAElB;UACEC,YAAY1D,gBAAgB,CAAA,GAAI2D,KAAK,GAAA;QACvC,CAAA;MAEJ,CAAA;IACF;EACF;AAEA,QAAMC,kBAAkB,CAACX,WAAAA;AACvBD,wBAAoBC,QAAQ,CAACE,kBAAAA;AAC3B3B,uBAAiB2B,aAAAA;AACjB/B,cAAQ3B,OAAME,WAAW;IAC3B,CAAA;EACF;AAEA,MAAIM,YAAY8B,eAAeU,SAAS,GAAG;AACzCvC,YAAQ,GAAGA,KAAM,KAAImB,gBAAgB,CAAE,MAAKU,eAAeU,MAAM;EACnE;AAEA,QAAMoB,aAAa,MAAA;AACjBvC,qBAAiB,CAACwC,YAAaA,UAAU/B,eAAeU,SAAS,IAAIqB,UAAU,IAAI,CAAA;EACrF;AAEA,QAAMC,iBAAiB,MAAA;AACrBzC,qBAAiB,CAACwC,YAAaA,UAAU,IAAIA,UAAU,IAAI/B,eAAeU,SAAS,CAAA;EACrF;AAEA,QAAMuB,6BAA6B,CAAChD,mBAAAA;AAClCC,qBAAiB,CAACgD,SAAS;MAAIA,GAAAA;MAASjD,GAAAA;IAAc,CAAA;EACxD;AAEA,MAAIkD,0BAA0BnC;AAE9B,MAAIf,cAAcyB,SAAS,GAAG;AAC5B,UAAM0B,uBAAuBf,gBAAgBpD,cAAcgB,aAAAA;AAE3DkD,8BAA0BjE,WACtB;MAAIkE,GAAAA;MAAyBpC,GAAAA;QAC7B;MAACoC,qBAAqB,CAAE;IAAC;EAC/B;AAEA,aACEC,2BAAAC,+BAAA;;UACEC,0BAACC,gBAAAA;QACCC,KAAK/D;QACLwC,QAAQlB;QACR3B;QACAF;QACAG;QACAoE,eAAe/B;QACfgC,+BAA+BtC;QAC/BuC,YAAY,MAAMvD,QAAQ3B,OAAMC,WAAW;QAC3CkF,aAAahB;QACbiB,aAAa/B;QACbgC,QAAQjB;QACRkB,YAAYhB;QACZjD;QACAX;QACAK;QACAwE,oBAAoB3D;QACpB4D,iBAAgB;;MAGjB9D,SAAS1B,OAAMC,mBACd4E,0BAACY,aAAAA;QACClF;QACAkE;QACAzC;QACA0D,SAAS,MAAA;AACP/D,kBAAQd,MAAAA;AACRoB,sBAAY,IAAA;QACd;QACA0D,MAAMjE,SAAS1B,OAAMC;QACrB2F,YAAYnD;QACZjC;QACA0E,YAAY,MAAMvD,QAAQ3B,OAAME,WAAW;QAC3C2F,aAAa,MAAMlE,QAAQ3B,OAAMG,YAAY;QAC7C2F,gBAAgB,CAACC,WAAW9D,YAAY8D,MAAAA;QACxCP,iBAAgB;;MAInB9D,SAAS1B,OAAME,mBACd2E,0BAACmB,mBAAAA;QACCL,MAAMjE,SAAS1B,OAAME;QACrBwF,SAAS,MAAM/D,QAAQ3B,OAAMC,WAAW;QACxCgG,oBAAoBnE;QACpBoE,kBAAkB3B;QAClBiB,iBAAgB;QAChBxD;QACAuB;;MAIH7B,SAAS1B,OAAMG,oBACd0E,0BAACsB,kBAAAA;QACCR,MAAMjE,SAAS1B,OAAMG;QACrBuF,SAAS,MAAM/D,QAAQ3B,OAAMC,WAAW;QACxCmG,gBAAgBpE;;;;AAK1B,CACA;;;;;;AC7OK,IAAMqE,4BAA2B,CAACC,MAAoBC,cAAAA;AAC3D,MAAI,CAACA,WAAU;AACb,UAAM,IAAIC,UAAU,yBAAA;EACtB;AAEA,SAAOC,OAAOC,KAAKJ,IAAAA,EAAMK,OAAO,CAACC,KAAmBC,YAAAA;AAClDD,QAAI,GAAGL,SAAAA,IAAYM,OAAQ,EAAC,IAAIP,KAAKO,OAAQ;AAE7C,WAAOD;EACT,GAAG,CAAA,CAAC;AACN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACCA,IAAME,OAAOC,UAAUC,OAAOF;AAE9B,IAAMG,SAAkC;EACtCC,SAASC,KAAc;AACrBA,QAAIC,YAAY;MACdC,IAAI,WAAWC,SAAAA;MACfC,MAAMC;MACNC,WAAW;QACTC,IAAI,GAAGJ,SAAS;QAChBK,gBAAgB;MAClB;MACAC,aAAaC,aAAYC;MACzBC,WAAW,MAAM,OAAO,mBAAA,EAAmBC,KAAK,CAACC,SAAS;QAAEC,SAASD,IAAIE;QAAO;MAChFC,UAAU;IACZ,CAAA;AAEAjB,QAAIkB,gBAAgB,UAAU;MAC5BX,IAAI;MACJL,IAAI;MACJI,WAAW;QACTC,IAAIY,QAAQ,aAAA;QACZX,gBAAgB;MAClB;MACA,MAAMI,YAAAA;AACJ,cAAM,EAAEQ,sBAAqB,IAAK,MAAM,OAAO,4BAAA;AAC/C,eAAO;UAAEL,SAASK;QAAsB;MAC1C;MACAX,aAAaC,aAAYW;IAC3B,CAAA;AAEArB,QAAIsB,UAAU;MACZC,MAAM;MACNX,WAAWY;IACb,CAAA;AACAxB,QAAIyB,cAAc;MAChB;QACE9B,MAAM;QACNiB,WAAWc;MACb;IACD,CAAA;AAED1B,QAAI2B,eAAe;MACjBpB,IAAIJ;MACJR;IACF,CAAA;EACF;EACA,MAAMiC,cAAc,EAAEC,QAAO,GAAyB;AACpD,UAAMC,gBAAgB,MAAMC,QAAQC,IAClCH,QAAQI,IAAI,CAACC,WAAAA;AACX,aAAO,kCAAO,kBAAkBA,MAAO,OAAM,EAC1CrB,KAAK,CAAC,EAAEE,SAASoB,KAAI,MAAE;AACtB,eAAO;UACLA,MAAMC,0BAAyBD,MAAMhC,SAAAA;UACrC+B;QACF;MACF,CAAA,EACCG,MAAM,MAAA;AACL,eAAO;UACLF,MAAM,CAAA;UACND;QACF;MACF,CAAA;IACJ,CAAA,CAAA;AAGF,WAAOH,QAAQO,QAAQR,aAAAA;EACzB;AACF;;;;;;;;ACzEA,IAAMS,uBAAsBC,GAAOC,UAAAA;;;AAiBnC,IAAMC,uBAAuB,CAAC,EAC5BC,aACAC,aAAa,OACbC,WACAC,MAAAA,OACAC,UACAC,MAAK,MACqB;AAC1B,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,CAACC,QAAQC,SAAAA,IAAmBC,iBAAS,KAAA;AAE3C,QAAMC,eAAe,CAACN,WAAAA;AACpB,QAAIJ,cAAcI,QAAO;AACvB,aAAOD,SAAS;QAAEQ,QAAQ;UAAET,MAAAA;UAAME,OAAAA;UAAOQ,MAAM;QAAW;MAAE,CAAA;IAC9D;AAEA,QAAI,CAACR,QAAO;AACV,aAAOI,UAAU,IAAA;IACnB;AAEA,WAAO;EACT;AAEA,QAAMK,gBAAgB,MAAA;AACpBV,aAAS;MAAEQ,QAAQ;QAAET,MAAAA;QAAME,OAAO;QAAOQ,MAAM;MAAW;IAAE,CAAA;EAC9D;AAEA,QAAME,QAAQb,UAAUc,KACpBV,cACE;IAAEU,IAAId,UAAUc;IAAIC,gBAAgBf,UAAUe;KAC9C;IAAE,GAAGf,UAAUgB;GAEjBf,IAAAA;AAEJ,QAAMgB,OAAOnB,cACTM,cACE;IAAEU,IAAIhB,YAAYgB;IAAIC,gBAAgBjB,YAAYiB;KAClD;IAAE,GAAGjB,YAAYkB;GAEnB,IAAA;AAEJ,aACEE,2BAACC,OAAOC,MAAI;IAACC,MAAMf;IAAQgB,cAAcf;;UACvCW,2BAACK,MAAMH,MAAI;QAACH;QAAYhB,MAAMA;;cAC5BuB,0BAACC,cAAAA;YAASC,iBAAiBjB;YAAckB,SAASxB;YAC/CU,UAAAA;;cAEHW,0BAACD,MAAMK,MAAI,CAAA,CAAA;;;UAEbV,2BAACC,OAAOU,SAAO;;cACbL,0BAACL,OAAOW,QAAM;sBACX1B,cAAc;cACbU,IAAIiB,eAAe,kCAAA;cACnBhB,gBAAgB;YAClB,CAAA;;cAEFS,0BAACL,OAAOa,MAAI;YAACC,UAAMT,0BAACU,cAAAA,CAAAA,CAAAA;YAClB,cAAAhB,2BAACiB,MAAAA;cAAKC,WAAU;cAASC,YAAW;cAAUC,KAAK;;oBACjDd,0BAACW,MAAAA;kBAAKI,gBAAe;kBACnB,cAAAf,0BAAC9B,sBAAAA;8BACEU,cAAc;sBACbU,IAAIiB,eAAe,oCAAA;sBACnBhB,gBACE;oBACJ,CAAA;;;oBAGJS,0BAACW,MAAAA;kBAAKI,gBAAe;kBACnB,cAAAf,0BAAC5B,YAAAA;oBAAW4C,YAAW;8BACpBpC,cAAc;sBACbU,IAAIiB,eAAe,iCAAA;sBACnBhB,gBAAgB;oBAClB,CAAA;;;;;;cAKRG,2BAACC,OAAOsB,QAAM;;kBACZjB,0BAACL,OAAOuB,QAAM;gBACZ,cAAAlB,0BAACmB,QAAAA;kBAAOC,SAAQ;4BACbxC,cAAc;oBACbU,IAAI;oBACJC,gBAAgB;kBAClB,CAAA;;;kBAGJS,0BAACL,OAAO0B,QAAM;gBACZ,cAAArB,0BAACmB,QAAAA;kBAAOC,SAAQ;kBAAeE,SAASlC;4BACrCR,cAAc;oBACbU,IAAIiB,eAAe,2CAAA;oBACnBhB,gBAAgB;kBAClB,CAAA;;;;;;;;;AAOd;;;;;;;;;;;AC1HA,IAAMgC,mBAAmB;EACvB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACD;AAED,IAAMC,qCAAqC,CACzCC,aAC6CC,WAAAA,SAAID,MAAM;EAAC;EAAQ;AAAY,CAAA;;;AC3BxEE,IAAAA,aAAa,CAACC,QAAgBA,IAAIC,OAAO,CAAA,EAAGC,YAAW,IAAKF,IAAIG,MAAM,CAAA;;;ACuB3E,IACKC,UAAmB,MAAA;AAEvB,QAAMC,SAASC,UAAAA;AAEf,QAAMC,kBAAkBC,QAAQ,WAAW,CAACC,UAAUA,MAAMC,WAAW;AACvE,QAAMC,UAAgBC,gBAAQ,MAAA;AAC5B,UAAMF,cAAcH,gBAAgBM,OAAO,CAACC,eAAeA,WAAWC,YAAYV,OAAOW,IAAI;AAE7F,WAAON,YAAYO,OACjB,CAACC,KAAKJ,eAAAA;;AACJ,YAAM,CAACK,eAAgB,IAAGL,WAAWM,OAAOC,MAAM,GAAA,EAAKC,MAAM,EAAC;AAE9D,aAAO;QACL,GAAGJ;QACH,CAAC,MAAMK,WAAWJ,eAAiB,CAAA,EAAC,KAAGL,gBAAWU,eAAXV,mBAAuBW,YAAW,CAAA;MAC3E;OAEF;MAAEC,WAAW,CAAA;MAAIC,SAAS,CAAA;MAAIC,WAAW,CAAA;MAAIC,WAAW,CAAA;MAAIC,YAAY,CAAA;IAAG,CAAA;KAE5E;IAACzB,OAAOW;IAAMT;EAAgB,CAAA;AAGjC,QAAM,EAAEwB,OAAM,IAAKC,YACjB;;IAEEC,gBAAgB5B,OAAO4B;IACvBC,OAAO7B,OAAOW;KAEhB;IACEmB,MAAM;EACR,CAAA;AAGF,MAAIC,mCAAmCL,iCAAQM,aAAgB,GAAA;AAC7D,WAAO;MACLC,SAASP,OAAOM,cAAcE,KAAKC;MACnC,GAAG7B;IACL;EACF;AAEA,SAAO;IACL2B,SAAS;IACT,GAAG3B;EACL;AACF;;;AChEA,IAAM8B,eAAeC,QAAQC,gBAAgB;EAC3CC,kBAAkB;EAClBC,WAAW,CAACC,aAAa;IACvBC,2BAA2BD,QAAQE,MAKjC;MACAA,OAAO,CAAC,EAAEC,OAAO,GAAGC,OAAAA,OAAc;QAChCC,KAAK,qCAAqCF,KAAAA;QAC1CG,QAAQ;QACRC,QAAQ;UACNH;QACF;;MAEFI,mBAAmB,CAACC,aAAsDA,SAASC;IACrF,CAAA;;AAEJ,CAAA;AAEM,IAAA,EAAEC,kCAAiC,IAAKhB;;;ACrBxCiB,IAAAA,YAAY,CAChBC,MACAC,QACAC,eAAAA;AAEA,QAAMC,cAAcC,aAAaJ,MAAM;IACrC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACD,CAAA;AAED,QAAMK,wCAAwCC,0BAC5CH,aACAF,QACAC,YACA;IAAC;IAAY;EAAW,CAAA;AAG1B,SAAOG;AACT;AAEA,IAAMD,eAAe,CAACJ,MAAYO,WAAAA;AAChC,SAAOC,OAAOC,KAAKT,IAAAA,EAAMU,OAAO,CAACC,KAAKC,YAAAA;AACpC,QAAIL,OAAOM,SAASD,OAAU,GAAA;AAC5B,aAAOD;IACT;AACAA,QAAIC,OAAAA,IAAWZ,KAAKY,OAAQ;AAC5B,WAAOD;EACT,GAAG,CAAA,CAAC;AACN;AAEA,IAAML,4BAA4B,CAChCN,MACAC,QACAC,YACAK,WAAAA;AAEA,SAAOC,OAAOC,KAAKT,IAAAA,EAAMU,OAAO,CAACC,KAAKC,YAAAA;AACpC,UAAME,YAAYb,OAAOc,WAAWH,OAAAA,KAAY;MAAEI,MAAMC;IAAU;AAElE,QAAIV,OAAOM,SAASC,UAAUE,IAAI,GAAG;AACnC,aAAOL;IACT;AAEA,QAAIG,UAAUE,SAAS,eAAe;AACpCL,UAAIC,OAAQ,IAAGZ,KAAKY,OAAAA,EAASM,IAAI,CAACC,gBAAqBC,WAAAA;AACrD,cAAM,EAAEC,IAAIC,GAAG,GAAGC,KAAM,IAAGjB,0BACzBa,gBACAjB,WAAWiB,eAAeK,WAAW,GACrCtB,YACAK,MAAAA;AAGF,eAAO;UACL,GAAGgB;UACHE,cAAcL,SAAQ;QACxB;MACF,CAAA;IACF,WAAWN,UAAUE,SAAS,aAAa;AACzC,YAAM,EAAEU,YAAYC,UAAS,IAAKb;AAElC,UAAIY,YAAY;AACdf,YAAIC,OAAAA,KAAYZ,KAAKY,OAAQ,KAAI,CAAA,GAAIM,IAAI,CAACU,WAAgBR,WAAAA;AACxD,gBAAM,EAAEC,IAAIC,GAAG,GAAGC,KAAAA,IAASjB,0BACzBsB,WACA1B,WAAWyB,SAAAA,GACXzB,YACAK,MAAAA;AAGF,iBAAO;YACL,GAAGgB;YACHE,cAAcL,SAAQ;UACxB;QACF,CAAA;aACK;AACL,cAAM,EAAEC,IAAIC,GAAG,GAAGC,KAAAA,IAASjB,0BACzBN,KAAKY,OAAAA,KAAY,CAAA,GACjBV,WAAWyB,SAAAA,GACXzB,YACAK,MAAAA;AAGFI,YAAIC,OAAAA,IAAWW;MACjB;WACK;AACLZ,UAAIC,OAAAA,IAAWZ,KAAKY,OAAQ;IAC9B;AAEA,WAAOD;EACT,GAAG,CAAA,CAAC;AACN;;;;;;ACtEA,IAAMkB,2BAA2B,CAACC,WAAAA;AAChC,SACE,OAAOA,WAAW,YAAYA,WAAW,QAAQ,QAAQA,UAAU,oBAAoBA;AAE3F;AAEA,IAAMC,sBAAsB,CAAC,EAC3BC,SAAS,SACTC,kBACAC,OAAM,MACmB;AACzB,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAM1B,QAAMC,cAAc,CAACC,KAAaC,UAAAA;AAChC,QAAI,OAAOA,UAAU,UAAU;AAC7B,aAAO,GAAGD,GAAAA,KAAQC,KAAAA;eACTV,yBAAyBU,KAAQ,GAAA;AAC1C,aAAO,GAAGD,GAAAA,KAAQH,cAAcI,KAAAA,CAAAA;IAClC,WAAWC,MAAMC,QAAQF,KAAQ,GAAA;AAC/B,aAAOA,MAAMG,IAAI,CAACC,MAAMN,YAAYC,KAAKK,CAAIC,CAAAA,EAAAA,KAAK,GAAA;eACzC,OAAOL,UAAU,YAAY,CAACC,MAAMC,QAAQF,KAAQ,GAAA;AAC7D,aAAOM,OAAOC,QAAQP,KAAAA,EACnBG,IAAI,CAAC,CAACK,GAAGJ,CAAAA,MAAON,YAAYU,GAAGJ,CAAAA,CAAAA,EAC/BC,KAAK,GAAA;WACH;AAIL,aAAO;IACT;EACF;AAEA,MAAIX,kBAAkB;AACpB,UAAMe,2BAA2BH,OAAOC,QAAQb,gBAAAA,EAC7CS,IAAI,CAAC,CAACJ,KAAKC,KAAM,MAAA;AAChB,aAAOF,YAAYC,KAAKC,KAAAA;IAC1B,CAAA,EACCK,KAAK,GAAA;AAER,eACEK,2BAACC,MAAAA;MAAKC,KAAK;;YACTC,0BAACC,eAAAA;UAAYC,MAAK;;YAClBF,0BAACG,aAAAA;UAAQC,OAAOR;UACd,cAAAI,0BAACK,YAAAA;YACCC,UAAU;YACVC,WAAU;YACVC,SAAQ;YACRC,YAAW;YACXC,UAAQ;YAEPd,UAAAA;;;;;EAKX;AAEA,QAAMe,mBAAmB,MAAA;AACvB,QAAI7B,WAAW,gBAAgB;AAC7B,UAAIF,WAAW,aAAa;AAC1B,eAAO;UACLgC,UAAMZ,0BAACa,eAAAA;YAAYX,MAAK;;UACxBY,MAAM/B,cAAc;YAClBgC,IAAI;YACJC,gBAAgB;UAClB,CAAA;UACAT,WAAW;UACXE,YAAY;QACd;iBACS7B,WAAW,YAAY;AAChC,eAAO;UACLgC,UAAMZ,0BAACiB,eAAAA;YAAuBf,MAAK;;UACnCY,MAAM/B,cAAc;YAClBgC,IAAI;YACJC,gBAAgB;UAClB,CAAA;QACF;aACK;AACL,eAAO;UACLJ,UAAMZ,0BAACa,eAAAA;YAAYX,MAAK;;UACxBY,MAAM/B,cAAc;YAClBgC,IAAI;YACJC,gBAAgB;UAClB,CAAA;QACF;MACF;WACK;AACL,UAAIpC,WAAW,SAAS;AACtB,eAAO;UACLgC,UAAMZ,0BAACa,eAAAA;YAAYX,MAAK;;UACxBY,MAAM/B,cAAc;YAClBgC,IAAI;YACJC,gBAAgB;UAClB,CAAA;UACAT,WAAW;UACXE,YAAY;QACd;aACK;AACL,eAAO;UACLG,UAAMZ,0BAACa,eAAAA;YAAYX,MAAK;;UACxBY,MAAM/B,cAAc;YAClBgC,IAAI;YACJC,gBAAgB;UAClB,CAAA;UACAT,WAAW;UACXE,YAAY;QACd;MACF;IACF;EACF;AAEA,QAAM,EAAEG,MAAME,MAAMP,YAAY,cAAcE,aAAa,SAAQ,IAAKE,iBAAAA;AAExE,aACEd,2BAACC,MAAAA;IAAKC,KAAK;;MACRa;UACDZ,0BAACK,YAAAA;QAAWE;QAAsBE;QAC/BK,UAAAA;;;;AAIT;AAMA,IAAMI,YAAY,CAACC,eAA4BnB,0BAACK,YAAAA;EAAWI,YAAW;EAAQU,UAAAA;;AAiB9E,IAAMC,wBAAwB,CAAC,EAC7BC,SACAC,MACAC,iBACA1C,mBAAmB,CAAA,GACnBC,OAAM,MACqB;AAC3B,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,QAAMwC,eAAeC,SACnB,yBACA,CAACC,UAAUA,MAAMF,YAAY;AAG/B,QAAMG,2BAA2B,MAAA;AAC/B,UAAMC,wBAAwBN,KAAKO,OAA+B,CAACC,KAAK,EAAEC,QAAQnD,OAAM,MAAE;AACxFkD,UAAIC,MAAAA,IAAUnD;AACd,aAAOkD;IACT,GAAG,CAAA,CAAC;AACJ,UAAME,oBAAoBvC,OAAOwC,KAAKpD,gBAAAA;AAEtC,UAAMqD,iBAAiBV,aAAaW,OAClC,CAAC,EAAEJ,OAAM,MAAOH,sBAAsBG,MAAO,MAAK,WAAA,EAClDK;AAEF,UAAMC,aAAab,aAAaW,OAC9B,CAAC,EAAEJ,OAAM,OACNH,sBAAsBG,MAAO,MAAK,WACjCH,sBAAsBG,MAAAA,MAAY,eACpC,CAACC,kBAAkBM,SAASP,MAAAA,CAAAA,EAC9BK;AAEF,UAAMG,kBAAkBP,kBAAkBI;AAC1C,UAAMI,YACJ1D,WAAW,iBACP,+EACA;AAEN,UAAMkC,iBACJlC,WAAW,iBACP,wUACA;AAEN,WAAOC,cACL;MACEgC,IAAIyB;MACJxB;OAEF;MACEuB;MACAF;MACAH;MACAO,GAAGvB;IACL,CAAA;EAEJ;AAEA,aACErB,2BAAC6C,MAAMC,MAAI;;UACT3C,0BAACK,YAAAA;QAAYsB,UAAAA,yBAAAA;;UACb3B,0BAAC4C,KAAAA;QAAIC,WAAW;sBACdhD,2BAACiD,MAAMC,SAAO;;gBACZlD,2BAACiD,MAAME,MAAI;;oBACThD,0BAAC8C,MAAMG,oBAAkB,CAAA,CAAA;gBACxB5B,QAAQ/B,IAAI,CAAC4D,aACZlD,0BAAC8C,MAAMK,YAAU;kBAAkB,GAAGD;gBAAfA,GAAAA,KAAKE,IAAI,CAAA;;;gBAGpCpD,0BAAC8C,MAAMH,MAAI;wBACRrB,KAAKhC,IAAI,CAAC,EAAEyC,QAAQnD,OAAM,GAAIyE,WAAAA;;AAC7B,sBAAMC,SAAQzE,qDAAmBkD,YAAW;AAE5C,sBAAMwB,gBACJ3E,WAAW,UAAU,YAAYA,WAAW,cAAc,YAAY;AAExE,2BACEiB,2BAACiD,MAAMU,KAAG;;wBACRxD,0BAAC8C,MAAMW,cAAY;sBAAC1C,IAAIgB;sBAAQ2B,cAAY,UAAU3B,MAAAA;;wBACtD/B,0BAAC8C,MAAMa,MAAI;sBACT,cAAA3D,0BAACK,YAAAA;wBAAWG,SAAQ;wBAAQD,WAAU;wBACnCnB,UAAAA,MAAMC,QAAQkC,eACXA,KAAAA,qBAAgBqC,KAAK,CAACC,gBAAgBA,YAAYC,SAAS/B,MAAAA,MAA3DR,mBAAoE6B,OACpErB;;;wBAGR/B,0BAAC8C,MAAMa,MAAI;sBACT,cAAA3D,0BAAC4C,KAAAA;wBAAImB,SAAQ;wBACX,cAAA/D,0BAACgE,QAAAA;0BACCD,SAAQ;0BACRE,aAAY;0BACZC,cAAa;0BACbC,YAAW;0BACXC,eAAc;0BACdC,MAAM;0BACN7D,SAAS+C;0BAET,cAAAvD,0BAACK,YAAAA;4BAAWiE,KAAI;4BAAO9D,SAAQ;4BAAKC,YAAW;sCAC5C8D,WAAW3F,MAAAA;;;;;wBAKpBoB,0BAAC8C,MAAMa,MAAI;sBACT,cAAA3D,0BAACrB,qBAAAA;wBAAoBE,kBAAkByE;wBAAO1E;wBAAgBE;;;wBAEhEkB,0BAAC8C,MAAMa,MAAI;sBACT,cAAA3D,0BAACwE,YAAAA;wBACCF,KAAKG;wBACLC,IAAI;0BACFC,YAAQC,uBAAU;4BAAEC,SAAS;8BAAEC,MAAM;gCAAE/C;8BAAO;4BAAE;0BAAE,CAAA;wBACpD;wBACA3B,OAAOrB,cACL;0BACEgC,IAAIgE,eAAe,4BAAA;0BACnB/D,gBAAgB;2BAElB;0BACEoC,MAAMrB;wBACR,CAAA;wBAEFvB,SAAQ;wBAER,cAAAR,0BAACgF,eAAAA,CAAAA,CAAAA;;;;gBA9CS3B,GAAAA,MAAAA;cAmDpB,CAAA;;;;;;;AAMZ;;;AC9PA,IAAM4B,iBAAqE;EACzEC,OAAO;EACPC,WAAW;EACXC,UAAU;AACZ;AAEA,IAAMC,eAAe,CAAC,EACpBC,0BACAC,QACAC,QACAC,YAAW,MACO;AAClB,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,MAAI,CAACF,aAAa;AAChB,WAAOC,cACL;MACEE,IAAIC,eAAe,sCAAA;MACnBC,gBAAgB;OAElB;MACEC,MAAM,CAACR,gBAA4BS,0BAACC,KAAAA;QAAGV,UAAAA;;MACvCA,QAAQA,OAAOW;IACjB,CAAA;EAEJ;AAEA,aACEC,2BAACC,MAAAA;IAAKC,OAAM;IAAOC,KAAK;IAAGC,gBAAe;;UACxCP,0BAACQ,YAAAA;QAAYjB,UAAAA,OAAOW;;MACnBZ,+BACCU,0BAACS,QAAAA;QACCC,SAAQ;QACRC,aAAY;QACZC,cAAa;QACbC,YAAW;QACXC,eAAc;QACdC,MAAK;QACLC,SAAS/B,eAAeO,MAAO;QAE/B,cAAAQ,0BAACQ,YAAAA;UAAWS,KAAI;UAAOD,SAAQ;UAAKE,YAAW;oBAC5CC,WAAW3B,MAAAA;;MAGd,CAAA,IAAA;;;AAGV;AAEA,IAAM4B,qBAAqB,CAAC,EAC1BC,UACAC,MACAC,OACAC,gBACAC,WAAU,MACQ;;AAClB,QAAM,EAAE/B,cAAa,IAAKC,QAAAA;AAC1B,QAAM,CAAC,EAAE+B,MAAK,GAAIC,QAAAA,IAAYC,eAAAA;AAC9B,QAAM,EAAEC,SAASC,WAAWC,QAAO,IAAKC,QAAAA;AACxC,QAAM,EAAEC,MAAMC,UAAU,CAAA,EAAE,IAAKC,mBAAAA;AAC/B,QAAMC,wBAAuBV,iBAAMW,YAANX,mBAAeY,SAAfZ,mBAAqBnC;AAClD,QAAM,EAAEgD,OAAM,IAAKC,YAAY;IAC7BjB;IACAC;IACAC;IACAgB,QAAQ;MAAElD,QAAQ6C;IAAqB;EACzC,CAAA;AAEA,QAAMM,eAAqBC,oBACzB,CAACC,UAAAA;AACCjB,aAAS;MACPU,SAAS;QACP,GAAGX,MAAMW;QACTC,MAAM;UACJ/C,QAAQqD;QACV;MACF;IACF,CAAA;KAEF;IAAClB,MAAMW;IAASV;EAAS,CAAA;AAG3BkB,EAAMC,kBAAU,MAAA;AACd,QAAI,CAACC,MAAMC,QAAQd,OAAAA,KAAY,CAACL,SAAS;AACvC;IACF;AAKA,UAAMoB,kBAAkBf,QAAQgB,KAAK,CAACC,QAAQA,IAAIC,SAAShB,oBAAAA;AAC3D,UAAMiB,gBAAgBnB,QAAQgB,KAAK,CAAC3D,WAAWA,OAAO+D,SAAS;AAC/D,QAAI,CAACL,oBAAmBI,+CAAeD,OAAM;AAC3CV,mBAAaW,cAAcD,IAAI;IACjC;KACC;IAACV;IAAcb;IAASK;IAASE;EAAqB,CAAA;AAEzD,QAAMmB,gBAAgBR,MAAMC,QAAQd,OAChCA,IAAAA,QAAQgB,KAAK,CAAC3D,WAAWA,OAAO6D,SAAShB,oBACzCoB,IAAAA;AAEJ,QAAMC,oBAAoB;IACxB;MAAEjE,QAAQkE,kBAAkBrC,UAAUC,IAAAA;MAAO/B,QAAQgE,+CAAeH;IAAK;IACrE/B,IAAAA,qCAAUsC,kBAAiB,CAAA;EAChC;AAED,MAAI,CAAC9B,WAAW,CAACkB,MAAMC,QAAQd,OAAYA,KAAAA,QAAQ0B,WAAW,GAAG;AAC/D,WAAO;EACT;AAEA,QAAMC,mBAAmB3B,QAAQ4B,OAAO,CAACvE,WAAAA;AAKvC,WAAOwC,QAAQgC,SAASxE,OAAO6D,IAAI;EACrC,CAAA;AAEA,SAAO;IACLY,OAAOtE,cAAc;MACnBE,IAAIC,eAAe,sCAAA;MACnBC,gBAAgB;IAClB,CAAA;IACAmE,SAASJ,iBAAiBK,IAAI,CAAC3E,WAAAA;;AAC7B,YAAM4E,wBAAwBV,kBAAkBW,KAAK,CAACC,QAAQA,IAAI9E,WAAWA,OAAO6D,IAAI;AAExF,YAAMkB,mBAAmBb,kBAAkBP,KAAK,CAACmB,QAC/C,YAAYA,MAAMA,IAAI9E,WAAWA,OAAO6D,OAAO,KAAA;AAGjD,YAAMmB,qBAAqBD,mBAAmBvC,UAAUD;AAExD,aAAO;QACL0C,UAAU,CAACD,mBAAmBR,SAASxE,OAAO6D,IAAI;QAClDR,OAAOrD,OAAO6D;QACdY,WACEhE,0BAACX,cAAAA;UACCC,0BAA0B,CAAC,GAACiD,MAAAA,iCAAQ0B,YAAR1B,gBAAAA,IAAiBkC;UAC7ClF;UACAC,QAAQ8E,qDAAkB9E;UAC1BC,aAAa0E;;QAGjBO,WAAW,CAACP,4BAAwBnE,0BAAC2E,eAAU,CAAA,CAAA,IAAA;MACjD;IACF,CAAA;IACAC,kBAAkB,MAAMrB,+CAAerD;IACvC2E,UAAUnC;IACVE,OAAOW;EACT;AACF;AAIA,IAAMG,oBAAoB,CACxBrC,UACAC,SAAAA;AAEA,QAAMwD,YAAYzD,qCAAU7B;AAC5B,QAAMuF,YAAWzD,6BAAM0D,oBAAmB,CAAA;AAK1C,MAAI,CAACF,WAAW;AACd,WAAO;EACT;AAKA,MAAIA,cAAc,WAAWC,SAAS7B,KAAK,CAACmB,QAAQA,IAAIY,gBAAgB,IAAO,GAAA;AAC7E,WAAO;EACT;AAEA,SAAOH;AACT;AAMA,IAAMI,8BAA8B,CAAC,EACnCzD,YACAH,MACAC,OACAC,eAAc,MACI;;AAClB,QAAM,EAAE9B,cAAa,IAAKC,QAAAA;AAC1B,QAAM,CAAC,EAAE+B,MAAK,CAAE,IAAIE,eAAAA;AACpB,QAAM,EAAEC,QAAO,IAAKG,QAAAA;AACpB,QAAMI,wBAAuBV,iBAAMW,YAANX,mBAAeY,SAAfZ,mBAAqBnC;AAClD,QAAM,CAAC4F,gBAAgBC,iBAAAA,IAA2BC,iBAAwB,IAAA;AAC1E,QAAMC,YAAYC,QAAQ,yBAAyB,CAACC,UAAUA,MAAMF,SAAS;AAE7E,QAAM,EAAEG,YAAW,IAAKC,mBAAAA;AACxB,QAAM,EAAEnD,QAAQoD,WAAU,IAAKnD,YAAY;IACzCjB;IACAE;IACAD;IACAiB,QAAQ;MAAElD,QAAQ6C;IAAqB;EACzC,CAAA;AACA,QAAM,EAAEH,MAAMC,UAAU,CAAA,EAAE,IAAKC,mBAAAA;AAE/B,QAAMyD,mBAAmB7C,MAAMC,QAAQd,OAAAA,IACnCA,QAAQ4B,OAAO,CAACvE,WAAW+B,6BAAMsE,iBAAiBxB,KAAK,CAACyB,MAAMA,EAAEtG,WAAWA,OAAO6D,KAAI,IACtF,CAAA;AAEJ,QAAM0C,iBAAiB,CAACC,YAAwB,YAAA;AAC9C,UAAMC,WAAW,MAAMP,YAAY;MACjCjE;MACAD;MACAE;MACAgB,QAAQ;QAAElD,QAAQ4F;MAAe;IACnC,CAAA;AACA,QAAI,CAACa,YAAY,CAACzD,QAAQ;AACxB;IACF;AAEA,UAAM,EAAEN,KAAI,IAAK+D;AAEjB,UAAMC,cAAcC,UAAUjE,MAAMM,QAAQoD,UAAAA;AAE5CL,cAAUW,WAAAA;AAEVF,YAAAA;EACF;AAEA,MAAI,CAAClE,SAAS;AACZ,WAAO;EACT;AAEA,SAAO;IACLsE,MAAM;IACNC,UAAMpG,0BAACqG,eAAAA,CAAAA,CAAAA;IACP7B,UAAUoB,iBAAiBhC,WAAW;IACtCI,OAAOtE,cAAc;MACnBE,IAAIC,eAAe,gCAAA;MACnBC,gBAAgB;IAClB,CAAA;IACAwG,QAAQ;MACNH,MAAM;MACNI,OAAO7G,cAAc;QACnBE,IAAIC,eAAe,mCAAA;QACnBC,gBAAgB;MAClB,CAAA;MACA0G,SAAS,CAAC,EAAET,QAAO,UACjB5F,2BAAAsG,+BAAA;;cACEzG,0BAAC0G,OAAOC,MAAI;YACV,cAAAxG,2BAACC,MAAAA;cAAKwG,WAAU;cAAStG,KAAK;;oBAC5BN,0BAAC6G,cAAAA;kBAAcxG,OAAM;kBAAOyG,QAAO;kBAAOC,MAAK;;oBAC/C/G,0BAACQ,YAAAA;kBAAWwG,WAAU;4BACnBtH,cAAc;oBACbE,IAAIC,eAAe,kCAAA;oBACnBC,gBACE;kBACJ,CAAA;;oBAEFK,2BAAC8G,MAAMC,MAAI;kBAAC7G,OAAM;;wBAChBL,0BAACiH,MAAME,OAAK;gCACTzH,cAAc;wBACbE,IAAIC,eAAe,yCAAA;wBACnBC,gBAAgB;sBAClB,CAAA;;wBAEFE,0BAACoH,cAAAA;sBACCxE,OAAOuC;sBACPkC,aAAa3H,cAAc;wBACzBE,IAAIC,eAAe,+CAAA;wBACnBC,gBAAgB;sBAClB,CAAA;;sBAEAwH,UAAU,CAAC1E,UAAUwC,kBAAkBxC,KAAAA;sBAEtCgD,UAAAA,iBAAiB1B,IAAI,CAAC3E,eACrBS,0BAACuH,oBAAAA;wBAAqC3E,OAAOrD,OAAO6D;wBACjD7D,UAAAA,OAAOW;sBADeX,GAAAA,OAAO6D,IAAI,CAAA;;;;;;;cAQ9CpD,0BAAC0G,OAAOc,QAAM;YACZ,cAAArH,2BAACC,MAAAA;cAAKE,KAAK;cAAGD,OAAM;;oBAClBL,0BAACyH,QAAAA;kBAAOC,MAAK;kBAAO1G,SAAQ;kBAAW2G,SAAS5B;4BAC7CrG,cAAc;oBACbE,IAAIC,eAAe,kCAAA;oBACnBC,gBAAgB;kBAClB,CAAA;;oBAEFE,0BAACyH,QAAAA;kBAAOC,MAAK;kBAAO1G,SAAQ;kBAAU2G,SAAS7B,eAAeC,OAAAA;4BAC3DrG,cAAc;oBACbE,IAAIC,eAAe,kCAAA;oBACnBC,gBAAgB;kBAClB,CAAA;;;;;;;IAMZ;EACF;AACF;AAMA,IAAM8H,qBAA8C,CAAC,EACnDvG,UACAI,YACAF,OACAC,eAAc,MACf;;AACC,QAAM,EAAE9B,cAAa,IAAKC,QAAAA;AAC1B,QAAMkI,WAAWC,YAAAA;AACjB,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,EAAEC,QAAQC,cAAcC,UAAS,IAAKzC,mBAAAA;AAC5C,QAAM,EAAE7D,SAASuG,UAAS,IAAKpG,QAAAA;AAG/B,QAAM,CAAC,EAAEN,MAAK,CAAE,IAAIE,eAAAA;AACpB,QAAM,EAAEK,MAAMC,UAAU,CAAA,EAAE,IAAKC,mBAAAA;AAC/B,QAAMC,wBAAuBV,iBAAMW,YAANX,mBAAeY,SAAfZ,mBAAqBnC;AAClD,QAAMA,SAAS,EAAE,WAAW2C,YAAYA,QAAQgB,KAAK,CAACC,QAAQA,IAAIC,SAAShB,oBAAAA;AAE3E,MAAI,CAACP,SAAS;AACZ,WAAO;EACT;AAEA,SAAO;IACL2C,WACE,qCAAWjF,WAAU,CAAC6I,UAAUrE,SAAS1C,SAAS9B,MAAM,KAAM,CAAC8B,YAAY,CAACA,SAASzB;IACvFyI,UAAU;MAAC;MAAU;IAAY;IACjCrE,OAAOtE,cACL;MACEE,IAAIC,eAAe,sBAAA;MACnBC,gBAAgB;OAElB;MAAEP,QAAQA,UAAUA,OAAOW;IAAK,CAAA;IAElCkG,UAAMpG,0BAACsI,aAAAA,CAAAA,CAAAA;IACPtH,SAAS;IACTsF,QAAQ;MACNH,MAAM;MACNI,OAAO7G,cAAc;QACnBE,IAAIC,eAAe,6BAAA;QACnBC,gBAAgB;MAClB,CAAA;MACA0G,aACErG,2BAACC,MAAAA;QAAKwG,WAAU;QAAStG,KAAK;;cAC5BN,0BAAC6G,cAAAA;YAAcxG,OAAM;YAAOyG,QAAO;YAAOC,MAAK;;cAC/C/G,0BAACQ,YAAAA;YAAWS,KAAI;YAAID,SAAQ;YAAQgG,WAAU;sBAC3CtH,cAAc;cACbE,IAAIC,eAAe,4BAAA;cACnBC,gBAAgB;YAClB,CAAA;;;;MAINyI,SAASJ;MACTK,WAAW,YAAA;AACT,cAAMC;;;UAGHjH,mBAAmB,kBAAkB,CAACC,cAAe,EAACJ,qCAAU9B;;AAEnE,YAAIkJ,gBAAgB;AAClBC,kBAAQC,MACN,qHAAA;AAGFZ,6BAAmB;YACjBa,SAASlJ,cAAc;cACrBE,IAAIC,eAAe,sBAAA;cACnBC,gBAAgB;YAClB,CAAA;YACAqG,MAAM;UACR,CAAA;AAEA;QACF;AAEA,cAAM0C,MAAM,MAAMX,aAAa;UAC7BzG;UACAF;UACAC;UACAiB,QAAQ;YAAElD,QAAQ8B,SAAS9B;UAAO;QACpC,CAAA;AAEA,YAAI,EAAE,WAAWsJ,MAAM;AACrBhB,mBAAS;YAAEiB,UAAU,MAAMtH,cAAAA,IAAkBD,KAAAA;aAAW;YAAEwH,SAAS;UAAK,CAAA;QAC1E;MACF;IACF;EACF;AACF;AAiBA,IAAMC,mBAA4C,CAAC,EACjD3H,UACAI,YACAF,OACAC,gBACAyH,OAAM,MACsB;;AAC5B,QAAM1J,UAAS8B,qCAAU9B,WAAU;AACnC,QAAM,CAAC,EAAEmC,MAAK,CAAE,IAAIE,eAAAA;AAEpB,QAAMa,SAAeyG,gBAAQ,MAAMC,iBAAiBzH,KAAQ,GAAA;IAACA;EAAM,CAAA;AACnE,QAAM0H,mBAAmB1H,MAAMlC,WAAW;AAE1C,QAAM,EAAEE,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEkC,SAASwH,WAAU,IAAKrH,QAAAA;AAChC,QAAM,EAAE+F,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,EAAEsB,yBAAyBC,eAAc,IAAKC,mBAAAA;AAEpD,QAAM,CAACC,cAAcC,eAAAA,IAAyBrE,iBAAgB,CAAA,CAAE;AAChE,QAAM,CAACsE,iCAAiCC,kCAAAA,IAChCvE,iBAAkB,KAAA;AAE1B,QAAM,EAAEwE,aAAaC,mBAAmBC,eAAeC,oBAAmB,IACxEtE,mBAAAA;AAEF,QAAM,EAAEnD,QAAQ0H,SAAQ,IAAKzH,YAC3B;IACEjB;IACAC;IACAC;IACAgB,QAAQ;MACNlD;IACF;KAEF;;IAEE2K,MAAM;EACR,CAAA;AAGF,QAAM,EAAEjI,MAAMkI,kBAAkB,CAAA,EAAE,IAAKhI,mBAAmBN,UAAU2B,SAAY4G,SAAAA;AAEhF,QAAMC,UAAU;IACd;MACErG,OAAOtE,cAAc;QACnBE,IAAI;QACJE,gBAAgB;MAClB,CAAA;MACAI,MAAM;IACR;IACA;MACE8D,OAAOtE,cAAc;QACnBE,IAAIC,eAAe,6BAAA;QACnBC,gBAAgB;MAClB,CAAA;MACAI,MAAM;IACR;IACA;MACE8D,OAAOtE,cAAc;QACnBE,IAAIC,eAAe,yCAAA;QACnBC,gBAAgB;MAClB,CAAA;MACAI,MAAM;IACR;EACD;AAID,QAAM,CAACoK,MAAMC,gBAAAA,IAA0BrB,gBAAQ,MAAA;AAC7C,QAAI,CAAC7H,UAAU;AACb,aAAO;QAAC,CAAA;QAAI,CAAA;MAAG;IACjB;AAEA,UAAMsC,gBAAgBtC,SAASsC,iBAAiB,CAAA;AAIhD,UAAMzB,UAA0ByB,cAAcO,IAAI,CAACG,QAAAA;AACjD,YAAM,EAAE9E,QAAAA,SAAQC,OAAM,IAAK6E;AAC3B,aAAO;QAAE9E,QAAAA;QAAQC;MAAO;IAC1B,CAAA;AAGA0C,YAAQsI,QAAQ;MACdjL,QAAQ8B,SAAS9B;MACjBC,QAAQ6B,SAAS7B;IACnB,CAAA;AAGA,UAAMiL,eAAe;MAACpJ;MAAasC,GAAAA;IAAc;AACjD,UAAM+G,SAASD,aAAaE,OAAmB,CAACC,MAAMvJ,cAAAA;AACpD,UAAI,CAACA,WAAU;AACb,eAAOuJ;MACT;AAGA,YAAMC,aAAaZ,SAAS5I,SAAAA;AAC5B,UAAIwJ,eAAe,MAAM;AACvBD,aAAKvJ,UAAS9B,MAAM,IAAIsL;MAC1B;AACA,aAAOD;IACT,GAAG,CAAA,CAAC;AAEJ,WAAO;MAAC1I;MAASwI;IAAO;KACvB;IAACrJ;IAAU4I;EAAS,CAAA;AAEvB,QAAMa,gBAAgB7B,WAAW;AACjC,QAAM8B,mBAAmBtB,aAAakB,OAAO,CAACK,KAAeC,gBAAAA;AAC3D,UAAMC;;MAEJ,CAACJ,iBAAiB,CAACK,OAAOC,KAAKb,gBAAkBxG,EAAAA,SAASkH,YAAY1L,MAAM;;AAE9E,UAAM8L,kBAAkBP,gBACpBG,YAAYzL,WAAW,eAAe0L,gBACtCD,YAAYzL,WAAW,WAAW0L;AAEtC,QAAIG,iBAAiB;AACnBL,UAAIM,KAAKL,YAAY1L,MAAM;IAC7B;AAEA,WAAOyL;EACT,GAAG,CAAA,CAAE;AAIL,QAAMO,4BAA4B;AAClC,QAAM,EACJtJ,MAAMuJ,sBAAsB,GAC5BrD,WAAWsD,yBACX9C,OAAO+C,sBAAqB,IAC1BC,kCACF;IACEpK;IACAqK,aAAa;MAACnK;IAAY;IAC1BlC,QAAQwL;KAEV;IACEb,MAAM,CAACqB;EACT,CAAA;AAGF1I,EAAMC,kBAAU,MAAA;AACd,QAAI4I,uBAAuB;AACzB3D,yBAAmB;QACjB5B,MAAM;QACNyC,SAASW,eAAemC,qBAAAA;MAC1B,CAAA;IACF;KACC;IAACA;IAAuB3D;IAAoBwB;EAAe,CAAA;AAE9D,MAAI,GAAChH,sCAAQ0B,YAAR1B,mBAAiBkC,kBAAiB;AACrC,WAAO;EACT;AAEA,MAAI,CAAC5C,SAAS;AACZ,WAAO;EACT;AAEA,MAAI,CAACJ,YAAY;AACf,WAAO;EACT;AAKA,QAAMoK,UAAU,YAAA;AACd,UAAM/B,kBAAkB;MACtBvI;MACAqK,aAAa;QAACnK;MAAW;MACzBgB,QAAQ;QACN,GAAGA;QACHlD,QAAQwL;MACV;IACF,CAAA;AAEArB,oBAAgB,CAAA,CAAE;EACpB;AAEA,QAAMoC,YAAY,YAAA;AAChB,UAAM9B,oBAAoB;MACxBzI;MACAqK,aAAa;QAACnK;MAAW;MACzBgB,QAAQ;QACN,GAAGA;QACHlD,QAAQwL;MACV;IACF,CAAA;AAEArB,oBAAgB,CAAA,CAAE;EACpB;AAEA,QAAMqC,eAAe,YAAA;AACnB,QAAIP,sBAAsB,GAAG;AAC3B5B,yCAAmC,IAAA;IACrC,WAAWkB,eAAe;AACxB,YAAMe,QAAAA;WACD;AACL,YAAMC,UAAAA;IACR;EACF;AAEA,MAAInC,iCAAiC;AACnC,WAAO;MACL3F,OAAOtE,cAAc;QACnBE,IAAI;QACJE,gBAAgB;MAClB,CAAA;MACAkB,SAAS;MACTsF,QAAQ;QACN0F,UAAU,MAAA;AACRpC,6CAAmC,KAAA;QACrC;QACApB,WAAW,YAAA;AACT,gBAAMqD,QAAAA;AAENjC,6CAAmC,KAAA;QACrC;QACAzD,MAAM;QACNI,OAAO7G,cAAc;UACnBE,IAAIC,eAAe,8BAAA;UACnBC,gBAAgB;QAClB,CAAA;QACA0G,aACErG,2BAACC,MAAAA;UAAKwG,WAAU;UAASqF,YAAW;UAAS3L,KAAK;;gBAChDN,0BAAC6G,cAAAA;cAAcxG,OAAM;cAASyG,QAAO;cAASC,MAAK;;gBACnD/G,0BAACQ,YAAAA;cAAWwG,WAAU;wBACnBtH,cAAc;gBACbE,IAAIC,eAAe,6CAAA;gBACnBC,gBACE;cACJ,CAAA;;gBAEFE,0BAACQ,YAAAA;cAAWwG,WAAU;wBACnBtH,cAAc;gBACbE,IAAIC,eAAe,4CAAA;gBACnBC,gBAAgB;cAClB,CAAA;;;;MAIR;IACF;EACF;AAEA,QAAMoM,gBAAgBzC,aACnBvF,IAAI,CAAC,EAAE3E,QAAAA,QAAM,MAAOA,OAAAA,EACpB4M,MAAM,CAAC5M,YAAW8J,WAAWtF,SAASxE,OAAAA,CAAAA;AAEzC,SAAO;IACLyE,OAAOtE,cAAc;MACnBE,IAAIC,eAAe,wBAAwBiL,gBAAgB,YAAY,WAAA,QAAmB;MAC1FhL,gBAAgB,GAAGgL,gBAAgB,YAAY,WAAA;IACjD,CAAA;IACA9J,SAAS8J,gBAAgB,cAAc;IACvC1E,MAAM0E,oBAAgB9K,0BAACoM,eAAAA,CAAAA,CAAAA,QAAcpM,0BAACqM,eAAAA,CAAAA,CAAAA;IACtC7H,UAAU4E,oBAAoBC,WAAWzF,WAAW;IACpDyE,UAAU;MAAC;IAAQ;IACnB/B,QAAQ;MACNH,MAAM;MACNI,OAAO7G,cAAc;QACnBE,IAAIC,eAAe,wBAAwBiL,gBAAgB,YAAY,WAAA,QAAmB;QAC1FhL,gBAAgB,GAAGgL,gBAAgB,YAAY,WAAA;MACjD,CAAA;MACAtE,SAAS,MAAA;AACP,mBACExG,0BAACsM,MAAMpF,MAAI;UACTmD;UACAC,MAAMA,KAAKpG,IAAI,CAACqI,SAAS;YACvB,GAAGA;YACH3M,IAAI2M,IAAIhN;YACV;UACAkK;UACA+C,sBAAsB,CAACC,sBAAsB/C,gBAAgB+C,iBAAAA;UAE7D,cAAAzM,0BAAC0M,uBAAAA;YACCnC;YACAF;YACAC;YACAH;YACAlB,QAAQA,UAAU;;;MAI1B;MACA0D,QAAQ,UACN3M,0BAAC4M,MAAMpF,QAAM;QAACjH,gBAAe;QAC3B,cAAAP,0BAACyH,QAAAA;UACCc,SAASkD;UACTjH,UAAU,CAAC0H,iBAAiBnB,iBAAiBnH,WAAW;UACxD5C,SAAQ;UACR2G,SAASoE;oBAERrM,cAAc;YACbE,IAAIkL,gBAAgB,sBAAsB;YAC1ChL,gBAAgBgL,gBAAgB,YAAY;UAC9C,CAAA;;;IAIR;EACF;AACF;AAKA,IAAM+B,0BAAmD,CAACC,UAAAA;AACxD,SAAO9D,iBAAiB;IAAEC,QAAQ;IAAgB,GAAG6D;EAAM,CAAA;AAC7D;AAKA,IAAMC,4BAAqD,CAACD,UAAAA;AAC1D,SAAO9D,iBAAiB;IAAEC,QAAQ;IAAkB,GAAG6D;EAAM,CAAA;AAC/D;AAMA,IAAMxE,cAAc0E,GAAOC,YAAAA;;;;;;;;;ACnxB3B,IAAMC,WAAW,CAACC,WAAAA;AAChB,aACEC,0BAACC,YAAAA;IAAWC,YAAW;IAAWC,WAAU;IACzCJ,UAAAA;;AAGP;AAEA,IAAMK,4BAA4B,MAAA;AAChC,QAAM,EAAEC,QAAO,IAAKC,QAAAA;AACpB,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,MAAI,CAACH,SAAS;AACZ,WAAO;EACT;AAEA,aACEL,0BAACC,YAAAA;IAAWE,WAAU;cACnBI,cACC;MACEE,IAAIC,eAAe,6CAAA;MACnBC,gBACE;OAEJ;MACEC,IAAId;IACN,CAAA;;AAIR;AAEA,IAAMe,6BAA6B,MAAA;AACjC,QAAM,EAAER,QAAO,IAAKC,QAAAA;AACpB,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,MAAI,CAACH,SAAS;AACZ,WAAO;EACT;AAEA,aACEL,0BAACC,YAAAA;IAAWE,WAAU;cACnBI,cACC;MACEE,IAAIC,eAAe,8CAAA;MACnBC,gBACE;OAEJ;MACEC,IAAId;IACN,CAAA;;AAIR;AAEA,IAAMgB,+BAA+B,MAAA;AACnC,QAAM,EAAET,QAAO,IAAKC,QAAAA;AACpB,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,MAAI,CAACH,SAAS;AACZ,WAAO;EACT;AAEA,aACEL,0BAACC,YAAAA;IAAWE,WAAU;cACnBI,cACC;MACEE,IAAIC,eAAe,gDAAA;MACnBC,gBACE;OAEJ;MACEC,IAAId;IACN,CAAA;;AAIR;;;;;ACtEA,IAAMiB,eAAe,MAAA;;AACnB,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,CAAC,EAAEC,MAAK,GAAIC,QAAAA,IAAYC,eAAAA;AAE9B,QAAM,EAAEC,SAASC,SAASC,UAAS,IAAKC,QAAAA;AACxC,QAAM,EAAEC,MAAMC,UAAU,CAAA,EAAE,IAAKC,mBAAmBC,QAAW;IAC3DC,MAAM,CAACR;EACT,CAAA;AAEA,QAAMS,eAAqBC,oBACzB,CAACC,MAAcC,UAAU,UAAK;AAC5Bd,aACE;MACEe,MAAM;MACNC,SAAS;QAAE,GAAGjB,MAAMiB;QAASC,MAAM;UAAEC,QAAQL;QAAK;MAAE;IACtD,GACA,QACAC,OAAAA;KAGJ;IAACf,MAAMiB;IAAShB;EAAS,CAAA;AAG3BmB,EAAMC,kBAAU,MAAA;;AACd,QAAI,CAACC,MAAMC,QAAQf,OAAAA,KAAY,CAACL,SAAS;AACvC;IACF;AAKA,UAAMqB,wBAAuBxB,OAAAA,MAAAA,MAAMiB,YAANjB,gBAAAA,IAAekB,SAAflB,gBAAAA,IAAqBmB;AAClD,UAAMM,kBAAkBjB,QAAQkB,KAAK,CAACC,QAAQA,IAAIb,SAASU,oBAAAA;AAC3D,UAAMI,gBAAgBpB,QAAQkB,KAAK,CAACP,WAAWA,OAAOU,SAAS;AAC/D,QAAI,CAACJ,oBAAmBG,+CAAed,OAAM;AAC3CF,mBAAagB,cAAcd,MAAM,IAAA;IACnC;KACC;IAACX;IAASS;IAAcJ;KAASR,iBAAMiB,YAANjB,mBAAekB,SAAflB,mBAAqBmB;EAAO,CAAA;AAEhE,MAAI,CAAChB,WAAW,CAACmB,MAAMC,QAAQf,OAAYA,KAAAA,QAAQsB,WAAW,GAAG;AAC/D,WAAO;EACT;AAEA,QAAMC,mBAAmBvB,QAAQwB,OAAO,CAACb,WAAAA;AAMvC,WAAOd,UAAU4B,SAASd,OAAOL,IAAI,KAAKV,QAAQ6B,SAASd,OAAOL,IAAI;EACxE,CAAA;AAEA,aACEoB,0BAACC,cAAAA;IACCC,MAAK;IACLC,cAAYvC,cAAc;MACxBwC,IAAIC,eAAe,uBAAA;MACnBC,gBAAgB;IAClB,CAAA;IACAC,SAAOzC,iBAAMiB,YAANjB,mBAAekB,SAAflB,mBAAqBmB,aAAUX,aAAQkB,KAAK,CAACP,WAAWA,OAAOU,SAAS,MAAzCrB,mBAA4CM;;IAElF4B,UAAU9B;IAETmB,UAAAA,iBAAiBY,IAAI,CAACxB,eACrBe,0BAACU,oBAAAA;MAAmCH,OAAOtB,OAAOL;MAC/CK,UAAAA,OAAO0B;IADe1B,GAAAA,OAAOmB,EAAE,CAAA;;AAM1C;;;;;ACtEA,IAAMQ,qBAAqB,CAAC,EAAEC,OAAM,MAAsB;AAExD,MACE,EAAE,UAAUA,OAAOC,YAClB,OAAOD,OAAOC,QAAQC,SAAS,YAC9BF,OAAOC,QAAQC,SAAS,QACxB,eAAeF,OAAOC,QAAQC,QAC9B,CAACF,OAAOC,QAAQC,KAAKC,WACvB;AACA,WAAO;MAAEH;IAAO;EAClB;AAEA,QAAMI,aAAaC,OAAOC,QAAQN,OAAOI,UAAU,EAAEG,OACnD,CAACC,KAAK,CAACC,KAAKC,eAAgB,MAAA;AAC1B,WAAO;MACL,GAAGF;MACH,CAACC,GAAAA,GAAM;QACL,GAAGC;QACHV,QAAQU,gBAAgBV,OAAOW,IAAI,CAACC,QAAQA,IAAID,IAAIE,qBAAAA,CAAAA;MACtD;IACF;EACF,GACA,CAAA,CAAC;AAGH,SAAO;IACLb,QAAQ;MACN,GAAGA;MACHI;MACAJ,QAAQA,OAAOA,OAAOW,IAAI,CAACG,UAAUA,MAAMH,IAAI,CAACC,QAAQA,IAAID,IAAIE,qBAAAA,CAAAA,CAAAA;IAClE;EACF;AACF;AAEA,IAAMA,wBAAwB,CAACE,UAAAA;AAC7B,QAAMC,mBAAmBC,2BAA2BF,MAAMG,UAAUC,aAAa,IAC7EJ,MAAMG,UAAUC,cAAcjB,KAAKC,YACnC;AAEJ,QAAMiB,mBAAmB;IACvBC,OAAO;MACLC,IAAIN,mBACAO,eAAe,iBAAA,IACfA,eAAe,qBAAA;MACnBC,gBAAgBR,mBACZ,iDACA;IACN;IACAS,MAAMT,uBAAmBU,0BAACC,eAAW,CAAA,CAAA,IAAA;EACvC;AAEA,SAAO;IACL,GAAGZ;IACHa,aAAaZ,uBAAmBU,0BAACG,aAAAA;MAAa,GAAGT;IAAuB,CAAA,IAAA;EAC1E;AACF;AAEA,IAAMH,6BAA6B,CACjCa,eAAAA;AAEA,MAAI,CAACA,YAAY;AACf,WAAO;EACT;AAEA,SACE,UAAUA,cACV,OAAOA,WAAW5B,SAAS,YAC3B4B,WAAW5B,SAAS,QACpB,eAAe4B,WAAW5B;AAE9B;AAWA,IAAM2B,cAAc,CAAC,EAAER,OAAOI,KAAI,MAAoB;AACpD,QAAM,EAAEM,cAAa,IAAKC,QAAAA;AAE1B,aACEC,2BAACC,MAAAA;IAAKC,KAAI;;UACRT,0BAACU,gBAAAA;QAAeD,KAAI;kBAAQJ,cAAcV,KAAAA;;MACnCgB,qBAAaZ,MAA4B;QAC9C,eAAe;QACfa,WAAW;MACb,CAAA;;;AAGN;AAEA,IAAMJ,OAAOK,GAAOC,IAAAA;;;;;YAKR,CAAC,EAAEC,MAAK,MAAOA,MAAMC,OAAOC,UAAU;;;cAGpC,CAAC,EAAEF,MAAK,MAAOA,MAAMC,OAAOC,UAAU;;;;;;;;;;AC3GpD,IAAMC,iBAAiB,CAAC,EAAEC,QAAQC,eAAeC,cAAa,MAAuB;AACnF,QAAM,EAAEF,QAAQG,SAAQ,IAAKC,QAAAA;AAC7B,QAAM,EAAEC,MAAMC,UAAU,CAAA,EAAE,IAAKC,mBAAAA;AAC/B,QAAMC,YAAYC,YAAYN,UAAU;IACtCO,aAAa;EACf,CAAA;AAEA,MAAI,CAACC,MAAMC,QAAQN,OAAAA,KAAY,CAACJ,eAAe;AAC7C,WAAO;EACT;AAEA,QAAMW,mBAAmBX,cAAcY,IAAI,CAACC,QAAQA,IAAIf,MAAM;AAE9D,QAAMgB,qBAAqBV,QACxBW,OAAiB,CAACC,KAAKlB,WAAAA;AACtB,UAAMmB,gBAAgB;MAAClB;MAAkBY,GAAAA;MAAkBO,KAAK,CAACL,QAAAA;AAC/D,aAAOA,QAAQf,OAAOqB;IACxB,CAAA;AAEA,QAAIF,eAAe;AACjBD,UAAII,KAAKtB,MAAAA;IACX;AAEA,WAAOkB;EACT,GAAG,CAAA,CAAE,EACJJ,IAAI,CAACd,WAAAA;AACJ,QAAIA,OAAOuB,WAAW;AACpB,aAAO,GAAGvB,OAAOwB,IAAI;IACvB;AAEA,WAAOxB,OAAOwB;GAEfC,EAAAA,SAAS,CAACC,GAAGC,MAAMnB,UAAUoB,QAAQF,GAAGC,CAAAA,CAAAA;AAE3C,aACEE,2BAACC,QAAQC,MAAI;;UACXC,0BAACF,QAAQG,SAAO;QACd,cAAAD,0BAACE,QAAAA;UAAOC,SAAQ;UAAQC,MAAK;UAASC,SAAS,CAACC,MAAMA,EAAEC,gBAAe;UACrE,cAAAV,2BAACW,MAAAA;YAAKC,UAAS;YAAOC,YAAW;YAASC,gBAAe;YAASC,YAAW;;kBAC3EZ,0BAACa,YAAAA;gBAAWC,WAAU;gBAAaC,UAAQ;gBAACC,aAAa;gBACtDhC,UAAAA,mBAAmBiC,KAAK,IAAA;;kBAE3BjB,0BAACQ,MAAAA;gBACC,cAAAR,0BAACkB,eAAAA;kBAAUC,OAAM;kBAASC,QAAO;;;;;;;UAKzCpB,0BAACF,QAAQuB,SAAO;QAACC,YAAY;QAC3B,cAAAtB,0BAACuB,MAAAA;UACEvC,UAAAA,mBAAmBF,IAAI,CAACU,cACvBQ,0BAACwB,KAAAA;YAAeC,SAAS;YAAGC,KAAI;YAC9B,cAAA1B,0BAACa,YAAAA;cAAYrB,UAAAA;;UADLA,GAAAA,KAAAA,CAAAA;;;;;AAQtB;;;ACxDA,IAAMmC,uBAAuB,CAAC,EAAEC,kBAAkBC,OAAM,MAA4B;AAClF,QAAM,EAAEC,QAAO,IAAKD;AAEpB,QAAME,mBAAmBC,mCAAmCF,OAAAA,IACxDA,QAAQG,KAAKC,YACb;AAEJ,MAAI,CAACH,kBAAkB;AACrB,WAAO;MAAEH;MAAkBC;IAAO;EACpC;AAEA,SAAO;IACLD,kBAAkB;MACbA,GAAAA;MACH;QACEO,WAAW;UAAEC,MAAM;QAAS;QAC5BC,OAAO;UACLC,IAAIC,eAAe,8BAAA;UACnBC,gBAAgB;QAClB;QACAC,YAAY;QACZC,UAAU;QACVC,MAAM;;QAENC,eAAe,CAACC,OAAOC,SAASC,aAASC,0BAACC,gBAAAA;UAAgB,GAAGJ;UAAQ,GAAGE;;MAC1E;IACD;IACDlB;EACF;AACF;;;AC1BA,IAAMqB,0BAA0B,CAAC,EAAEC,mBAAmB,CAAA,EAAE,MAA+B;AACrF,SAAO;IACLA,kBAAkB;MACbA,GAAAA;MACH;QACEC,OAAO;UACLC,IAAI;UACJC,gBAAgB;QAClB;QACAC,MAAM;MACR;IACD;IACDC,gBAAgB;EAClB;AACF;;;;AC3BA,IAAMC,0CAGF,MAAA;AACF,SAAO,CAAC,EAAEC,SAAQ,MAChB,CAACC,SACD,CAACC,WAAAA;AACC,UAAMC,gBAAgB,CAAC,EAAEC,IAAG,MAAmB;;AAG7C,UAAI;AACF,cAAMC,QAAQL,SAAAA;AAEd,cAAMM,YAAOC,YAAAA,SAAIF,OAAO;UACtB;UACA;UACA;UACAD;QACD,CAAA;AAED,YAAI,CAACE,SAAQA,MAAKE,cAAc,eAAe;AAC7C,iBAAOP,KAAKC,MAAAA;QACd;AAEA,cAAMO,qBAAiBF,YAAAA,SAAID,OAAM;UAAC;UAAiB;UAAQ;WAAc,KAAA;AAEzE,YAAIG,gBAAgB;AAClB,iBAAOR,KAAK;YACV,GAAGC;YACHQ,SAAS;cACP,GAAGR,OAAOQ;cACVC,SAAS;gBACPC,eAAe;kBACb,KAAIV,4CAAQQ,YAARR,mBAAiBS,YAAjBT,mBAA0BU,kBAAiB,CAAA;kBAC/CC,MAAM;oBACJC,WAAW;kBACb;gBACF;cACF;YACF;UACF,CAAA;QACF;AAEA,eAAOb,KAAKC,MAAAA;MACd,SAASa,KAAK;AACZ,eAAOd,KAAKC,MAAAA;MACd;IACF;AAEA,UAAM,EAAEQ,UAAU,CAAA,GAAIJ,KAAI,IAAKJ,UAAU,CAAA;AAEzC,QACEI,SAAS,sCACT,CAAC;MAAC;MAAY;MAAaU,SAASN,QAAQO,aAAa,KACzD,CAACP,QAAQQ,WACT;AACA,aAAOf,cAAc;QACnBC,KAAKM,QAAQN;MACf,CAAA;IACF;AAEA,QAAIE,SAAS,wCAAwC,CAACI,QAAQQ,WAAW;AACvE,aAAOf,cAAc;QACnBC,KAAKM,QAAQN;MACf,CAAA;IACF;AAEA,QACEE,SAAS,4DACTA,SAAS,0CACT;AACA,aAAOH,cAAc;QACnBC,KAAKM,QAAQN;MACf,CAAA;IACF;AAEA,WAAOH,KAAKC,MAAAA;EACd;AACJ;;;AChFA,IAAMiB,iCAGF,MAAA;AACF,SAAO,MAAM,CAACC,SAAS,CAACC,WAAAA;AAJ1B;AAKI,QACEA,OAAOC,SAAS,mDAChBD,OAAOE,cAAc,eACrB;AACA,YAAMC,OAAO;QAAEC,WAAW;MAAM;AAEhC,YAAMC,gBAAgBL,OAAOM,KAAKD,gBAC9B;QAAE,GAAGL,OAAOM,KAAKD;QAAeF;UAChC;QAAEA;MAAK;AAEX,YAAMG,OAAO;QAAE,GAAGN,OAAOM;QAAMD;MAAc;AAE7C,UAAIL,OAAOO,eAAe,UAAU;AAClC,eAAOR,KAAK;UAAE,GAAGC;UAAQM;QAAK,CAAA;MAChC;AAIA,UAAI,GAACN,kBAAOM,KAAKD,kBAAZL,mBAA2BG,SAA3BH,mBAAiCI,YAAW;AAC/C,eAAOL,KAAK;UAAE,GAAGC;UAAQM;QAAK,CAAA;MAChC;IACF;AAGA,WAAOP,KAAKC,MAAAA;EACd;AACF;;;;AC5BA,IAAMQ,mBAAmC,CAACC,QAAQ,CAACC,SAAS,CAACC,gBAAAA;AAC3D,QAAMC,QAAQC,UAAU,gDAAgDJ,IAAIK,QAAQ;AAEpF,MAAI,CAACF,OAAO;AACV,WAAOF,KAAKC,WAAAA;EACd;AAEA,QAAMI,SAAYC,SAAMP,IAAIM,MAAM;AAElC,MAAI,OAAOA,WAAW,UAAU;AAC9B,WAAOL,KAAKC,WAAAA;EACd;AAEA,MAAI,EAAE,aAAaI,UAAU,OAAOA,OAAOE,YAAY,WAAW;AAChE,WAAOP,KAAKC,WAAAA;EACd;AAEA,MACE,EACE,UAAUI,OAAOE,WACjB,OAAOF,OAAOE,QAAQC,SAAS,YAC/B,CAACC,MAAMC,QAAQL,OAAOE,QAAQC,IAAI,IAEpC;AACA,WAAOR,KAAKC,WAAAA;EACd;AAEA,QAAM,EAAEU,OAAM,IAAKN,OAAOE,QAAQC;AAElC,MAAI,OAAOG,WAAW,UAAU;AAC9B,WAAOX,KAAKC,WAAAA;EACd;AAEA,QAAMW,qBAAqBX,YAAYY,OACrC,CAACC,eAAAA;;AACC,cAACA,gBAAWC,eAAXD,mBAAuBE,YAAWF,WAAWC,WAAWC,QAAQC,SAASN,MAAAA;GAAAA;AAG9E,SAAOX,KAAKY,kBAAAA;AACd;;;AC3CMM,IAAAA,4BAA2B,CAACC,MAAmBC,cAAAA;AAInD,SAAOC,OAAOC,KAAKH,IAAAA,EAAMI,OAAO,CAACC,KAAKC,YAAAA;AACpCD,QAAI,GAAGJ,SAAAA,IAAYK,OAAQ,EAAC,IAAIN,KAAKM,OAAQ;AAC7C,WAAOD;EACT,GAAG,CAAA,CAAC;AACN;;;;ACCME,IAAAA,6BAA6B,CACjCC,YAKAC,eAAAA;AAMA,MAAI,CAACA,YAAY;AACf,WAAOD;EACT;AAGA,MAAI,CAACE,mCAAmCF,WAAWG,aAAa,GAAG;AACjE,WAAOH;EACT;AAEA,QAAMI,wBAAwBJ,WAAWG,cAAcE,KAAKC;AAC5D,QAAMC,wBAAwBL,mCAAmCD,yCAAYE,aAAAA,IACzEF,yCAAYE,cAAcE,KAAKC,YAC/B;AAIJ,MAAIF,yBAAyBG,uBAAuB;AAClD,WAAOP;EACT;AAEA,MAAII,uBAAuB;AACzB,UAAMI,aAAaC,wBAAwBT,WAAWQ,UAAU;AAEhE,WAAO;MACL,GAAGR;MACHQ;IACF;EACF;AAGA,MAAI,CAACJ,uBAAuB;AAC1B,UAAMD,oBAAgBO,YAAAA,SAAKV,WAAWG,eAAe,MAAA;AACrD,UAAMK,aAAaG,8BAA8BX,WAAWQ,UAAU;AAEtE,WAAO;MACL,GAAGR;MACHG;MACAK;IACF;EACF;AAEA,SAAOR;AACT;AAMA,IAAMS,0BAA0B,CAACD,eAAAA;AAC/B,SAAOA,WAAWI,IAAI,CAACC,qBAAAA;AACrB,QAAIC,iBAAiBC,SAASF,iBAAiBG,IAAI,GAAG;AACpD,YAAMX,OAAO;QAAEC,WAAW;MAAK;AAE/B,YAAMH,gBAAgBU,iBAAiBV,gBACnC;QAAE,GAAGU,iBAAiBV;QAAeE;UACrC;QAAEA;MAAK;AAEX,aAAO;QAAE,GAAGQ;QAAkBV;MAAc;IAC9C;AAEA,WAAOU;EACT,CAAA;AACF;AAMA,IAAMF,gCAAgC,CAACH,eAAAA;AACrC,SAAOA,WAAWI,IAAI,CAACC,qBAAAA;AACrB,eAAOH,YAAAA,SAAKG,kBAAkB,oBAAA;EAChC,CAAA;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7DA,IAAAI,SAAe;EACbC,SAASC,KAAQ;AACfA,QAAIC,eAAe;MAACC;MAAyCC;IAA+B,CAAA;AAC5FH,QAAIC,eAAe;MAAC,MAAMG,QAAQC;IAAW,CAAA;AAC7CL,QAAIM,YAAY;MACd,CAACF,QAAQG,WAAW,GAAGH,QAAQI;IACjC,CAAA;AACAR,QAAIS,kBAAkB;MAACC;IAAiB,CAAA;AACxCV,QAAIW,eAAe;MACjBC,IAAIC;MACJC,MAAMD;IACR,CAAA;EACF;EACAE,UAAUf,KAAQ;AAEhBA,QAAIgB,aAAa,kDAAkDC,oBAAAA;AACnEjB,QAAIgB,aAAa,mDAAmDE,kBAAAA;AAEpElB,QAAIgB,aACF,+DACAG,uBAAAA;AAIFnB,QAAIoB,gBAAgB,UAAU;MAC5BC,WAAW;QACTT,IAAIU,eAAe,aAAA;QACnBC,gBAAgB;MAClB;MACAX,IAAI;MACJY,IAAI;MACJC,WAAW,MACT,OAAO,4BAAA,EAAwBC,KAAK,CAACC,SAAS;QAAEC,SAASD,IAAIE;QAAsB;MACrFC,aAAaC,aAAYC;IAC3B,CAAA;AAEA,UAAMC,iBAAiBjC,IAAIkC,UAAU,iBAAA;AAErCD,mBAAeE,KAAKC,wBAAwB;MAACC;MAAoBC;IAA4B,CAAA;AAC7FL,mBAAeE,KAAKI,kBAAkB,CAACC,YAAAA;AACrC,YAAMC,sBAAsBD,QAAQE,UAAU,CAACC,WAAWA,OAAOC,SAAS,QAAA;AAC1EJ,cAAQK,OAAOJ,qBAAqB,GAAGK,kBAAAA;AACvC,aAAON;IACT,CAAA;AAEAP,mBAAeE,KAAKI,kBAAkB,CAACC,YAAAA;AAGrCA,cAAQK,OAAO,GAAG,GAAGE,uBAAAA;AACrBP,cAAQK,OAAO,GAAG,GAAGG,yBAAAA;AACrB,aAAOR;IACT,CAAA;AAEAP,mBAAegB,gBAAgB,YAAY,WAAW;MACpDnC,MAAM;MACNW,WAAWyB;IACb,CAAA;AAEAjB,mBAAegB,gBAAgB,YAAY,+BAA+B;MACxEnC,MAAM;MACNW,WAAW0B;IACb,CAAA;AAEAlB,mBAAegB,gBAAgB,YAAY,iCAAiC;MAC1EnC,MAAM;MACNW,WAAW2B;IACb,CAAA;AAEAnB,mBAAegB,gBAAgB,YAAY,8BAA8B;MACvEnC,MAAM;MACNW,WAAW4B;IACb,CAAA;AAEA,UAAMC,YAAYtD,IAAIkC,UAAU,sBAAA;AAEhC,QAAIoB,WAAW;AACb,YAAMC,cAAcD,UAAUnB,KAAKqB;AACnCD,kBAAYE,6BAA6BC,0BAAAA;AACzCH,kBAAYI,WAAWC,IAAI;QAAEhD,IAAI;QAAwBiD,WAAWC;MAAqB,CAAA;AAEzFP,kBAAYQ,kBAAkB;QAC5BC,WAAW,OAAO;UAChBC,MAAUC,QAAM,EAAGC,MAAM;YACvBC,WAAeC,OAAI;UACrB,CAAA;;QAEFC,MAAM;UACJC,WAAAA;AACE,mBAAO;cACL;gBACEzD,MAAM;gBACN0D,aAAa;kBACX5D,IAAIU,eAAe,uDAAA;kBACnBC,gBAAgB;gBAClB;gBACAqB,MAAM;gBACNvB,WAAW;kBACTT,IAAIU,eAAe,iDAAA;kBACnBC,gBAAgB;gBAClB;cACF;YACD;UACH;QACF;MACF,CAAA;AAEAgC,kBAAYkB,aAAaC,kBAAkB;QACzCJ,MAAM;UACJC,SAAS,EAAEI,mBAAmBC,WAAWhC,MAAMiC,KAAI,GAAO;AACxD,gBAAID,cAAc,eAAe;AAC/B,qBAAO,CAAA;YACT;AAEA,kBAAME,qBAAiBC,YAAAA,SACrBJ,mBACA;cAAC;cAAiB;cAAQ;eAC1B,KAAA;AAGF,gBAAI,CAACG,gBAAgB;AACnB,qBAAO,CAAA;YACT;AAEA,gBAAIlC,SAAS,eAAeiC,SAAS,KAAK;AACxC,qBAAO,CAAA;YACT;AAEA,mBAAO;cACL;gBACE/D,MAAM;gBACN0D,aAAa;kBACX5D,IAAIU,eAAe,gDAAA;kBACnBC,gBAAgB;gBAClB;gBACAqB,MAAM;gBACNvB,WAAW;kBACTT,IAAIU,eAAe,0CAAA;kBACnBC,gBAAgB;gBAClB;cACF;YACD;UACH;QACF;MACF,CAAA;IACF;EACF;EACA,MAAMyD,cAAc,EAAEC,QAAO,GAAyB;AACpD,UAAMC,gBAAgB,MAAMC,QAAQC,IAClCH,QAAQI,IAAI,CAACC,WAAAA;AACX,aAAOC,mCAAO,kBAAkBD,MAAO,OAAM,EAC1C5D,KAAK,CAAC,EAAEE,SAAS4D,KAAI,MAAE;AACtB,eAAO;UACLA,MAAMC,0BAAyBD,MAAM3E,SAAAA;UACrCyE;QACF;MACF,CAAA,EACCI,MAAM,MAAA;AACL,eAAO;UACLF,MAAM,CAAA;UACNF;QACF;MACF,CAAA;IACJ,CAAA,CAAA;AAGF,WAAOH,QAAQQ,QAAQT,aAAAA;EACzB;AACF;;;;;;;;;AC9JaU,IAAAA,6BAAiCC,QAAM,EAAGC,MAAM;EAC3DC,MAAUC,QAAM,EAAGC,MAAM;IAAC;IAAW;EAAY,CAAA,EAAEC,SAAQ;EAC3DC,WAAeH,QAAM,EAAGE,SAAQ;AAClC,CAAG;IAOUE,iBAAiB;EAC5BL,MAAM;EACNI,WAAW;AACb;IASaE,aAAa,MAAA;AACxB,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,aACEC,0BAACC,kBAAAA;IACCC,UAAMF,0BAACG,cAAAA;MAAeC,OAAM;;IAC5BC,SAASP,cAAc;MACrBQ,IAAI;MACJC,gBACE;IACJ,CAAA;IACAC,YACER,0BAACS,YAAAA;MACCC,IAAI;QACFC,UAAU;MACZ;MACAC,KAAKC;MACLC,SAAQ;gBAEPhB,cAAc;QACbQ,IAAI;QACJC,gBAAgB;MAClB,CAAA;;IAGJQ,QAAO;;AAGb;AAEA,IAAMC,0BAA0B,CAAC,EAC/BC,aACAC,YACAC,eACAC,OAAM,MACuB;;AAC7B,QAAM,EAAEtB,cAAa,IAAKC,QAAAA;AAC1B,QAAM,CAAC,EAAEsB,MAAK,CAAE,IAAIC,eAAAA;AACpB,QAAMC,UAASF,iBAAMG,YAANH,mBAAeI,SAAfJ,mBAAqBE;AAGpC,QAAMG,WAAWC,4BAA4B;IAC3CV;IACAW,iBAAiBV;IACjBW,kBAAkB;IAClBN;EACF,CAAA;AAEA,QAAMO,YAAWJ,cAASK,SAATL,mBAAeK;AAEhC,OAAID,qCAAUE,YAAW,GAAG;AAC1B,eAAOhC,0BAACH,YAAAA,CAAAA,CAAAA;EACV;AAEA,aACEoC,2BAACC,MAAAA;IAAKC,WAAU;IAASC,YAAW;IAAUC,KAAK;;UACjDrC,0BAACsC,KAAAA;QAAIC,eAAe;sBAClBN,2BAACO,MAAMC,MAAI;UAAC/C,UAAQ;;gBAClBM,0BAACwC,MAAME,OAAK;wBACT5C,cAAc;gBACbQ,IAAI;gBACJC,gBAAgB;cAClB,CAAA;;gBAEFP,0BAAC2C,cAAAA;cACCjD,UAAQ;cACRkD,aAAa9C,cAAc;gBACzBQ,IAAI;gBACJC,gBAAgB;cAClB,CAAA;cACAsC,MAAK;cACLC,UAAU,CAACC,UAAU5B,cAAc,aAAa4B,KAAAA;cAChDA,OAAO3B,OAAOzB;wBAEbmC,qCAAUkB,IAAI,CAACC,gBACdjD,0BAACkD,oBAAAA;gBAAoCH,OAAOE,QAAQ3C;gBACjD2C,UAAAA,QAAQJ;cADcI,GAAAA,QAAQ3C,EAAE;;;;;UAO3CN,0BAACwC,MAAME,OAAK;kBACT5C,cAAc;UACbQ,IAAI;UACJC,gBAAgB;QAClB,CAAA;;UAEFP,0BAACmD,sBAAAA;QACCC,UAAUhC,OAAO7B;QACjB8D,cAAc,CAACC,MAAMnC,cAAc,QAAQmC,EAAEC,OAAOR,KAAK;QACzDF,MAAK;;;;AAIb;AAMA,IAAMW,yBAAkD,CAAC,EACvDtC,YACAuC,UACAC,OACAC,eAAc,MACM;;AACpB,QAAM,EAAE7D,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAE6D,eAAc,IAAKC,QAAQC,YAAAA;AACnC,QAAM,EAAEC,gBAAe,IAAKH;AAC5B,QAAM,CAACI,qBAAqB,EAAEC,UAAS,CAAE,IAAIC,+BAAAA;AAC7C,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,EAAEC,eAAc,IAAKC,mBAAAA;AAC3B,QAAM,CAAC,EAAEjD,MAAK,CAAE,IAAIC,eAAAA;AACpB,QAAMC,UAASF,iBAAMG,YAANH,mBAAeI,SAAfJ,mBAAqBE;AAEpC,QAAMgD,eAAe,OAAOjB,GAAqCkB,YAAAA;AAC/D,QAAI;AACF,YAAMC,OAAOF,aAAajB,CAAAA;AAC1BkB,cAAAA;IACF,SAASE,OAAO;AACd,UAAIC,aAAaD,KAAQ,GAAA;AAEvBP,2BAAmB;UACjB5E,MAAM;UACNqF,SAASP,eAAeK,KAAAA;QAC1B,CAAA;aACK;AAELP,2BAAmB;UACjB5E,MAAM;UACNqF,SAAS9E,cAAc;YACrBQ,IAAI;YACJC,gBAAgB;UAClB,CAAA;QACF,CAAA;MACF;IACF;EACF;AAEA,QAAMkE,SAASI,UAAU;IACvBC,eAAelF;IACfmF,kBAAkB3F;IAClB4F,UAAU,OAAO5D,WAAAA;AACf,UAAIuC,mBAAmB,sBAAsB,CAACzC,YAAY;AACxD,cAAM,IAAI+D,MAAM,yBAAA;MAClB;AAEA,YAAMvD,WAAW,MAAMsC,oBAAoB;QACzCkB,MAAM;UACJ3F,MAAM6B,OAAO7B;UACb0B,aAAayC;UACb9B,iBAAiBV;UACjBK;QACF;QACA4D,QAAQ;UAAExF,WAAWyB,OAAOzB;QAAU;MACxC,CAAA;AAEA,UAAI,UAAU+B,UAAU;AAEtByC,2BAAmB;UACjB5E,MAAM;UACNqF,SAAS9E,cAAc;YACrBQ,IAAI;YACJC,gBAAgB;UAClB,CAAA;QACF,CAAA;AAEA;MACF;AAEA,UAAI,WAAWmB,UAAU;AACvB,cAAMA,SAASgD;MACjB;IACF;EACF,CAAA;AAEA,QAAM,EACJU,MAAM,EAAEC,QAAO,EAAE,IACfC,kBAAkB5B,KAAAA;AAGtB,MAAI,CAAC6B,OAAOC,OAAOC,QAAQ,EAACJ,mCAASK,oBAAmB,CAAC3B,iBAAiB;AACxE,WAAO;EACT;AAEA,MAAIJ,mBAAmB,uBAAuB,CAACzC,cAAcA,eAAe,WAAW;AACrF,WAAO;EACT;AAEA,SAAO;IACLyE,OAAO7F,cAAc;MACnBQ,IAAI;MACJC,gBAAgB;IAClB,CAAA;IACAL,UAAMF,0BAAC4F,eAAAA,CAAAA,CAAAA;;IAEPC,UAAU,CAACpC;IACXqC,UAAU;MAAC;MAAS;IAAY;IAChCC,QAAQ;MACNxG,MAAM;MACNyG,OAAOlG,cAAc;QACnBQ,IAAI;QACJC,gBAAgB;MAClB,CAAA;MACAF,aACEL,0BAACgB,yBAAAA;QACCC,aAAayC;QACbxC;QACAC,eAAesD,OAAOwB;QACtB7E,QAAQqD,OAAOrD;;MAGnB8E,QAAQ,CAAC,EAAE1B,QAAO,UAChBvC,2BAACkE,MAAMC,QAAM;;cACXpG,0BAACqG,QAAAA;YAAOC,SAAS9B;YAAS1D,SAAQ;YAAW+B,MAAK;sBAC/C/C,cAAc;cACbQ,IAAI;cACJC,gBAAgB;YAClB,CAAA;;cAEFP,0BAACqG,QAAAA;YACC9G,MAAK;;YAEL+G,SAAS,CAAChD,MAAMiB,aAAajB,GAAGkB,OAAAA;YAChCqB,UAAU,CAACpB,OAAOrD,OAAOzB;YACzB4G,SAAStC;sBAERnE,cAAc;cACbQ,IAAI;cACJC,gBAAgB;YAClB,CAAA;;;;IAIR;EACF;AACF;;;ACxQA,IAAMiG,wBAAwB,CAACC,YAAAA;AAC7B,QAAMC,cAAc;IAClBC,SAAS;MACP;QACEC,QAAQ;QACRH;QACAI,IAAI;QACJC,kBAAkB,CAAA;QAClBC,YAAY,CAAA;QACZC,YAAY,CAAA;MACd;IACD;EACH;AAEA,SAAON;AACT;AAEA,IAAMO,gBAAqC,CAAC,EAAEC,WAAWC,MAAK,MAAE;;AAC9D,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,EAAEC,eAAc,IAAKC,mBAAAA;AAC3B,QAAM,CAAC,EAAEC,MAAK,CAAE,IAAIC,eAAAA;AACpB,QAAMC,qBAAqBpB,sBAAsBW,KAAAA;AACjD,QAAM,EACJU,gBAAgB,EAAEC,WAAU,EAAE,IAC5BC,QAAQH,kBAAAA;AACZ,QAAM,EACJC,gBAAgB,EAAEG,UAAS,EAAE,IAC3BD,QAAQE,YAAAA;AACZ,QAAM,EAAEC,mBAAkB,IAAKC,yBAAAA;AAG/B,QAAMC,WAAWC,oBAAAA;AACjB,QAAMC,YAAWF,cAASG,SAATH,mBAAeG;AAChC,QAAM,CAACC,0BAA0B,EAAEC,UAAS,CAAE,IAAIC,oCAAAA;AAClD,QAAMC,cAAczB,UAAU0B,IAAI,CAACC,QAAQA,IAAIC,UAAU;AAEzD,QAAMC,eAAe,OAAOC,WAAAA;;AAC1B,UAAMC,UAASvB,MAAAA,MAAAA,MAAMwB,YAANxB,gBAAAA,IAAeyB,SAAfzB,mBAAqBuB;AAEpC,UAAMG,uBAAiET,YAAYC,IACjF,CAACS,qBAAqB;MACpBC,MAAMN,OAAOM;MACbC,aAAapC;MACbkC;MACAJ;MACF;AAGF,UAAMb,YAAW,MAAMI,yBAAyB;MAC9CgB,MAAMJ;MACNK,QAAQ;QAAEC,WAAWV,OAAOU;MAAU;IACxC,CAAA;AAEA,QAAI,UAAUtB,WAAU;AAGtB,YAAMuB,sBAAsBvC,cAC1B;QACEP,IAAI;QACJ+C,gBACE;SAEJ;QACEC,yBAAyBzB,UAASG,KAAKuB,KAAKD;QAC5CE,cAAc3B,UAASG,KAAKuB,KAAKC;MACnC,CAAA;AAGF,YAAMC,eAAe;QACnBV,MAAM;QACNW,OAAO7C,cACL;UACEP,IAAI;UACJ+C,gBAAgB;WAElB;UACEC,yBAAyBzB,UAASG,KAAKuB,KAAKD;UAC5CE,cAAc3B,UAASG,KAAKuB,KAAKC;QACnC,CAAA;QAEFG,SAAS9B,UAASG,KAAKuB,KAAKD,0BAA0BF,sBAAsB;MAC9E;AAEArC,yBAAmB0C,YAAAA;AAEnB,aAAO;IACT;AAEA,QAAI,WAAW5B,WAAU;AACvB,UAAI+B,aAAa/B,UAASgC,KAAK,GAAG;AAEhC9C,2BAAmB;UACjBgC,MAAM;UACNY,SAAS1C,eAAeY,UAASgC,KAAK;QACxC,CAAA;aACK;AAEL9C,2BAAmB;UACjBgC,MAAM;UACNY,SAAS9C,cAAc;YAAEP,IAAI;YAAsB+C,gBAAgB;UAAoB,CAAA;QACzF,CAAA;MACF;IACF;EACF;AAEA,MAAI,CAAC1B,sBAAsB,CAACF,aAAa,CAACF,WAAY,QAAO;AAE7D,SAAO;IACLuC,YAAY;IACZC,SAAS;IACTC,OAAOnD,cAAc;MACnBP,IAAI;MACJ+C,gBAAgB;IAClB,CAAA;IACAY,QAAQ;MACNlB,MAAM;MACNW,OAAO7C,cAAc;QACnBP,IAAI;QACJ+C,gBAAgB;MAClB,CAAA;MACAa,SAAS,CAAC,EAAEC,QAAO,MAAE;AACnB,mBACEC,0BAACC,QAAAA;UACCC,UAAU,OAAO7B,WAAAA;AACf,kBAAMT,OAAO,MAAMQ,aAAaC,MAAAA;AAChC,gBAAIT,MAAM;AACR,qBAAOmC,QAAAA;YACT;UACF;UACAI,kBAAkBC;UAClBC,eAAeC;UAEd,UAAA,CAAC,EAAEjC,QAAQkC,cAAa,UACvBC,2BAACC,MAAAA;;eACE9C,qCAAU+C,YAAW,QACpBV,0BAACW,YAED,CAAA,CAAA,QAAAX,0BAACY,MAAMC,MAAI;gBACT,cAAAL,2BAACM,MAAAA;kBAAKC,WAAU;kBAASC,YAAW;kBAAUC,KAAK;;wBACjDjB,0BAACkB,KAAAA;sBAAIC,eAAe;oCAClBX,2BAACY,MAAMC,MAAI;wBAACC,UAAQ;;8BAClBtB,0BAACoB,MAAMG,OAAK;sCACT9E,cAAc;8BACbP,IAAI;8BACJ+C,gBAAgB;4BAClB,CAAA;;8BAEFe,0BAACwB,cAAAA;4BACCC,aAAahF,cAAc;8BACzBP,IAAI;8BACJ+C,gBAAgB;4BAClB,CAAA;4BACAyC,UAAU,CAACC,UAAUpB,cAAc,aAAaoB,KAAAA;4BAChDA,OAAOtD,OAAOU;sCAEbpB,qCAAUM,IAAI,CAAC2D,gBACd5B,0BAAC6B,oBAAAA;8BAAoCF,OAAOC,QAAQ1F;8BACjD0F,UAAAA,QAAQE;4BADcF,GAAAA,QAAQ1F,EAAE;;;;;wBAO3C8D,0BAACoB,MAAMG,OAAK;gCACT9E,cAAc;wBACbP,IAAI;wBACJ+C,gBAAgB;sBAClB,CAAA;;wBAEFe,0BAAC+B,sBAAAA;sBACCC,UAAU3D,OAAOM;sBACjBsD,cAAc,CAACC,MAAM3B,cAAc,QAAQ2B,EAAEC,OAAOR,KAAK;sBACzDG,MAAK;;;;;kBAKbtB,2BAACI,MAAMwB,QAAM;;sBACXpC,0BAACqC,QAAAA;oBAAOC,SAASvC;oBAASJ,SAAQ;oBAAWmC,MAAK;8BAC/CrF,cAAc;sBACbP,IAAI;sBACJ+C,gBAAgB;oBAClB,CAAA;;sBAKFe,0BAACqC,QAAAA;oBAAO1D,MAAK;oBAAS4D,UAAU,CAAClE,OAAOU;oBAAWyD,SAAS1E;8BACzDrB,cAAc;sBACbP,IAAI;sBACJ+C,gBAAgB;oBAClB,CAAA;;;;;;;MAOd;IACF;EACF;AACF;;;;;ACxNA,IAAMwD,kBAAkB,CAACC,gBAAiCC,eAAAA;;AACxD,QAAMC,eAAeC,SAAS,YAAY,CAACC,UAAUA,MAAMC,IAAI;AAC/D,QAAMC,cAAcJ,aAAaK,IAAI,CAACC,UAAUA,MAAMP,UAAU;AAChE,QAAM,CAAC,EAAEQ,MAAK,CAAE,IAAIC,eAAAA;AACpB,QAAMC,WAAUF,0CAAuBG,YAAvBH,mBAAgCI,SAAhCJ,mBAAsCE,WAAUG;AAEhE,QAAMC,WAAWC,mCACf;IAAEhB;IAAgBM;IAAaK;KAC/B;IAAEM,MAAM,CAACX,eAAe,CAACN,kBAAkBM,YAAYY,WAAW;EAAE,CAAA;AAGtE,QAAMC,0BAA0BJ,SAASK,QAAQ,CAAA;AAEjD,UAAOD,mEAA0BlB,gBAAe,CAAA;AAClD;AAWA,IAAMoB,wBAAuB,CAAC,EAAEC,kBAAkBC,OAAM,MAA4B;AAClF,QAAM,EAAEC,QAAO,IAAKD;AAEpB,MAAI,EAACC,mCAASC,kBAAiB;AAC7B,WAAO;MAAEH;MAAkBC;IAAO;EACpC;AAEA,SAAO;IACLD,kBAAkB;MACbA,GAAAA;MACH;QACEI,YAAY;QACZC,UAAU;QACVC,MAAM;QACNC,OAAO;UACLC,IAAI;UACJC,gBAAgB;QAClB;QACAC,eAAe,CACbC,OACAC,GACA,EAAEC,MAAK,UACJC,0BAACC,iBAAAA;UAAiB,GAAGJ;UAAOE;;MACnC;IACD;IACDZ;EACF;AACF;AAWA,IAAMc,kBAAkB,CAAC,EAAEpC,YAAYkC,MAAK,MAAwB;AAClE,QAAMG,WAAWvC,gBAAgBoC,OAAOlC,UAAAA;AACxC,QAAM,EAAEsC,cAAa,IAAKC,QAAAA;AAE1B,aACEC,2BAACC,QAAQC,MAAI;;UACXP,0BAACM,QAAQE,SAAO;QACd,cAAAR,0BAACS,QAAAA;UACCC,SAAQ;UACRC,SAAS,CAACC,MAAqCA,EAAEC,gBAAe;;UAEhEC,SAASZ,SAASpB,SAAS,QAAIkB,0BAACe,eAAAA;YAAUC,OAAM;YAASC,QAAO;UAAc,CAAA,IAAA;UAE9E,cAAAjB,0BAACkB,YAAAA;YACCC,OAAO;cAAEC,UAAU;cAASC,QAAQ;YAAU;YAC9CC,WAAU;YACVC,YAAW;sBAEVrB,SAASpB,SAAS,IACfqB,cACE;cACET,IAAI;cACJC,gBAAgB;eAElB;cACE6B,QAAQtB,SAASpB;aAGrB,IAAA;;;;UAIVkB,0BAACM,QAAQmB,SAAO;QACd,cAAAzB,0BAAC0B,MAAAA;oBACExB,SAAS/B,IAAI,CAAC,EAAEuB,IAAIF,MAAAA,MAAI,UACvBQ,0BAAC2B,KAAAA;YAAaC,SAAS;YAAGC,KAAI;YAC5B,cAAA7B,0BAAC8B,OAAAA;cAAKC,MAAM,mCAAmCrC,EAAAA;cAAMsC,YAAY;cAC9DxC,UAAAA;;UAFKE,GAAAA,EAAAA,CAAAA;;;;;AAUtB;;;;ACtHMuC,IAAAA,QAAwB,CAAC,EAC7BC,OACAC,UACAC,YACAC,eAAc,MACM;;AACpB,QAAM,CAAC,EAAEC,MAAK,CAAE,IAAIC,eAAAA;AACpB,QAAMC,UAASF,iBAAMG,YAANH,mBAAeI,SAAfJ,mBAAqBE;AAEpC,QAAM,EACJG,MAAM,EAAEC,QAAO,EAAE,IACfC,kBAAkBX,KAAAA;AACtB,QAAM,EAAEY,eAAeC,YAAYC,WAAU,IAAKC,QAAAA;AAElD,QAAM,EAAEC,eAAc,IAAKC,QAAQC,YAAAA;AACnC,QAAM,EAAEC,SAASC,gBAAe,IAAKJ;AAErC,QAAMK,WAAWC,4BACf;IACEC,aAAavB;IACbwB,iBAAiBtB;IACjBI;IACAmB,kBAAkB;KAEpB;IACEC,MAAM,CAACzB;EACT,CAAA;AAEF,QAAM0B,YAAWN,cAASO,SAATP,mBAAeO;AAEhC,QAAMC,yBAAyB,CAC7BC,YACAC,UAAAA;AAEA,QAAID,eAAe,aAAa;AAC9B,aAAO,YAAYC,KAAAA;IACrB;AAEA,WAAO,UAAUA,KAAAA;EACnB;AAGA,MAAI,CAACC,OAAOC,OAAOC,QAAQ,EAACxB,mCAASyB,oBAAmB,CAAChB,SAAS;AAChE,WAAO;EACT;AAEA,MAAIhB,mBAAmB,uBAAuB,CAACD,cAAcA,eAAe,WAAW;AACrF,WAAO;EACT;AAEA,MAAI,CAACyB,YAAYA,SAASS,WAAW,GAAG;AACtC,WAAO;EACT;AAEA,SAAO;IACLC,OAAOzB,cAAc;MACnB0B,IAAI;MACJC,gBAAgB;IAClB,CAAA;IACAC,aACEC,0BAACC,MAAAA;MAAKC,WAAU;MAASC,YAAW;MAAUC,KAAK;MAAGC,OAAM;gBACzDnB,qCAAUoB,IAAI,CAACC,gBACdC,2BAACP,MAAAA;QAECC,WAAU;QACVC,YAAW;QACXM,aAAY;QACZC,aAAY;QACZC,aAAavB,uBAAuBmB,QAAQK,QAAQ,CAAE,EAACC,MAAM,KAAA;QAC7DC,UAAS;QACTC,WAAS;;cAETf,0BAACgB,KAAAA;YACCC,YAAY;YACZC,eAAe;YACfC,aAAa;YACbC,cAAc;YACdC,YAAYjC,uBAAuBmB,QAAQK,QAAQ,CAAE,EAACC,MAAM,KAAA;YAC5DR,OAAM;YAEN,cAAAL,0BAACsB,YAAAA;cACCC,UAAU;cACVC,SAAQ;cACRC,WAAWrC,uBAAuBmB,QAAQK,QAAQ,CAAE,EAACC,MAAM,KAAA;wBAE1D1C,cACC;gBACE0B,IAAI;gBACJC,gBACE;iBAEJ;gBAAE4B,WAAWnB,QAAQK,QAAQ,CAAE,EAACC,SAAS;cAAU,CAAA;;;cAIzDL,2BAACP,MAAAA;YAAK0B,SAAS;YAAGzB,WAAU;YAASE,KAAK;YAAGC,OAAM;YAAOF,YAAW;;kBACnEH,0BAACsB,YAAAA;gBAAWC,UAAU;gBAAGK,YAAW;gBAAOJ,SAAQ;gBAAQC,WAAU;gBAClElB,UAAAA,QAAQsB;;cAEVtB,QAAQuB,eAAevB,QAAQwB,gBAC9B/B,0BAACsB,YAAAA;gBAAWE,SAAQ;gBAAKC,WAAU;0BAChCtD,cACC;kBACE0B,IAAI;kBACJC,gBAAgB;mBAElB;kBACEkC,MAAM5D,WAAW,IAAI6D,KAAK1B,QAAQuB,WAAW,GAAG;oBAC9CI,KAAK;oBACLC,OAAO;oBACPC,MAAM;oBACNC,UAAU9B,QAAQwB;kBACpB,CAAA;kBACAO,MAAMjE,WAAW,IAAI4D,KAAK1B,QAAQuB,WAAW,GAAG;oBAC9CS,WAAW;oBACXF,UAAU9B,QAAQwB;kBACpB,CAAA;kBACAS,QAAQC,kBAAkBlC,QAAQwB,UAAU,IAAIE,KAAK1B,QAAQuB,WAAW,CAAA;gBAC1E,CAAA;;cAILnD,sBACC6B,2BAACkC,kBAAkBC,MAAI;gBAACC,kBAAgB;;sBACtC5C,0BAAC0C,kBAAkBG,iBAAe;oBAACC,WAAWvC,QAAQV;;sBACtDG,0BAAC0C,kBAAkBK,yBAAuB;oBACxCD,WAAWvC,QAAQV;oBACnBmD,UAAUzC,QAAQK,QAAQ,CAAA,EAAGf;;;cAG/B,CAAA,IAAA;;;;MAnEDU,GAAAA,QAAQV,EAAE;;EAyEzB;AACF;;;ACpJMoD,IAAAA,4BAA2B,CAACC,MAAmBC,cAAAA;AAInD,SAAOC,OAAOC,KAAKH,IAAAA,EAAMI,OAAO,CAACC,KAAKC,YAAAA;AACpCD,QAAI,GAAGJ,SAAAA,IAAYK,OAAQ,EAAC,IAAIN,KAAKM,OAAQ;AAC7C,WAAOD;EACT,GAAG,CAAA,CAAC;AACN;;;;;;;;;;;;;;;;;ACQA,IAAME,SAAkC;;EAEtCC,SAASC,KAAc;;AAMrBA,QAAIC,WAAW,6DAAA;AAEf,QAAIC,OAAOC,OAAOC,SAASC,UAAU,sBAAyB,GAAA;AAC5DL,UAAIM,YAAY;QACdC,IAAI,WAAWC,SAAAA;QACfC,MAAMC;QACNC,WAAW;UACTC,IAAI,GAAGJ,SAAS;UAChBK,gBAAgB;QAClB;QACAC,WAAW,MAAM,OAAO,mBAAA,EAAeC,KAAK,CAACC,SAAS;UAAEC,SAASD,IAAIE;UAAI;QACzEC,aAAaC,aAAYC;QACzBC,UAAU;MACZ,CAAA;AAGA,YAAMC,2BAA2BvB,IAAIwB,UAAU,iBAAA,EAAmBC;AAClE,UACE,0BAA0BF,4BAC1B,OAAOA,yBAAyBG,yBAAyB,YACzD;AACAH,iCAAyBG,qBAAqB;UAACC;QAAc,CAAA;MAC/D;AAGA,UACE,uBAAuBJ,4BACvB,OAAOA,yBAAyBK,sBAAsB,YACtD;AACAL,iCAAyBK,kBAAkB,CAACC,YAAAA;AAC1C,gBAAMC,sBAAsBD,QAAQE,UAAU,CAACC,WAAWA,OAAOC,SAAS,WAAA;AAC1EJ,kBAAQK,OAAOJ,qBAAqB,GAAGK,sBAAAA;AACvC,iBAAON;QACT,CAAA;MACF;AAEA7B,UAAIoC,gBAAgB,UAAU;QAC5BxB,IAAIJ;QACJD,IAAI;QACJI,WAAW;UACTC,IAAI,GAAGJ,SAAS;UAChBK,gBAAgB;QAClB;QACAM,aAAa,CAAA;QACb,MAAML,YAAAA;AACJ,gBAAM,EAAEuB,8BAA6B,IAAK,MAAM,OAAO,oCAAA;AACvD,iBAAO;YAAEpB,SAASoB;UAA8B;QAClD;MACF,CAAA;AAEA,UACE,mBAAmBd,4BACnB,OAAOA,yBAAyBe,kBAAkB,YAClD;AACAf,iCAAyBe,cAAc,CAACT,YAAAA;AAEtC,gBAAMU,oBAAoBV,QAAQE,UAAU,CAACC,WAAWA,OAAOC,SAAS,QAAA;AAExEJ,kBAAQK,OAAOK,mBAAmB,GAAGC,aAAAA;AACrC,iBAAOX;QACT,CAAA;MACF;AAGA7B,UAAIyC,aAAa,kDAAkDC,qBAAAA;IACrE,WACE,CAACxC,OAAOC,OAAOC,SAASC,UAAU,sBAClCH,OAAAA,kBAAOC,WAAPD,mBAAeyC,UAAfzC,mBAAsB0C,YACtB;AACA5C,UAAIoC,gBAAgB,UAAU;QAC5BxB,IAAIJ;QACJD,IAAI;QACJI,WAAW;UACTC,IAAI,GAAGJ,SAAS;UAChBK,gBAAgB;QAClB;QACAM,aAAa,CAAA;QACb,MAAML,YAAAA;AACJ,gBAAM,EAAE+B,wBAAuB,IAAK,MAAM,OAAO,uCAAA;AACjD,iBAAO;YAAE5B,SAAS4B;UAAwB;QAC5C;QACAC,aAAa;MACf,CAAA;IACF;EACF;EACA,MAAMC,cAAc,EAAEC,QAAO,GAAyB;AACpD,UAAMC,gBAAgB,MAAMC,QAAQC,IAClCH,QAAQI,IAAI,CAACC,WAAAA;AACX,aAAO,kCAAO,kBAAkBA,MAAO,OAAM,EAC1CtC,KAAK,CAAC,EAAEE,SAASqC,KAAI,MAAE;AACtB,eAAO;UACLA,MAAMC,0BAAyBD,MAAM,kBAAA;UACrCD;QACF;MACF,CAAA,EACCG,MAAM,MAAA;AACL,eAAO;UACLF,MAAM,CAAA;UACND;QACF;MACF,CAAA;IACJ,CAAA,CAAA;AAGF,WAAOH,QAAQO,QAAQR,aAAAA;EACzB;AACF;;;;;;;;;;AC/HMS,IAAAA,iBAAiB,CAAC,EACtBC,WACAC,UACAC,UACAC,MAAK,IAGH,CAAA,MAAE;AACJ,MAAID,UAAU;AACZ,WAAOA;EACT;AAGA,MAAIF,WAAW;AACb,WAAO,GAAGA,SAAU,IAAGC,YAAY,EAAA,GAAKG,KAAI;EAC9C;AAEA,SAAOD,SAAS;AAClB;;;ACvBO,IAAME,uBAAuB;AAC7B,IAAMC,0BAA0B;;;ACoBvC,IAAMC,iBAAiB,CAAC,EAAEC,UAAS,MAA2B;;AAC5D,QAAM,EACJC,iBAAiB,IACjBC,IACAC,MAAMC,QAAQ,GAAE,IACdC,UAAAA;AACJ,QAAMC,cAAcC,iBAAiB,CAACC,UAAUA,MAAMC,UAAUH,WAAW;AAC3E,QAAM,EAAEI,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,yBAAyBC,eAAc,IAAKC,mBAAAA;AACpD,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,EACJC,gBAAgB,EAAEC,QAAO,GACzBC,WAAWC,qBAAoB,IAC7BC,SAAQf,iBAAYgB,aAAZhB,mBAAsBiB,KAAAA;AAClC,QAAM,CAAC,EAAEC,MAAK,CAAE,IAAIC,eAAAA;AACpB,QAAMC,SAAeC,gBAAQ,MAAMC,kBAAiBJ,KAAQ,GAAA;IAACA;EAAM,CAAA;AACnE,QAAM,EACJK,MACAV,WAAWW,gBACXC,QAAO,IACLC,cAAcC,QAAW;IAC3BC,MAAMd,wBAAwB,CAACF;EACjC,CAAA;AACA,QAAM,EAAEiB,SAAQ,IAAKC,YACnB;IACEnC;IACAG;IACAiC,YAAYnC;KAEd;IACEgC,MAAM,CAAChC,MAAMD,mBAAmB;EAClC,CAAA;AAGF,QAAMsB,SAAQM,6BAAMN,UAAS,CAAA;AAE7B,QAAMe,kBAAkBH,WAAWA,SAASI,uBAAAA,IAA2B;AAEvE,QAAM,CAACC,gBAAgB,EAAEC,OAAOtB,WAAWuB,WAAU,CAAE,IAAIC,0BAAAA;AAE3D,MAAI,CAAC1C,kBAAkB,CAACG,SAAS,EAAC+B,qCAAUE,aAAY;AACtD,WAAO;EACT;AAEA,QAAMO,eAAe,OAAOC,eAAAA;AAE1B,SAAIP,mDAAiBpC,QAAO2C,YAAY;AACtC;IACF;AAEA,UAAMC,MAAM,MAAMN,eAAe;MAC/BrC,MAAMF;MACNG;MACAF,IAAIiC,SAASE;MACbX;MACAG,MAAM;QACJ3B,IAAI2C,aAAaE,SAASF,YAAY,EAAM,IAAA;MAC9C;IACF,CAAA;AAEA,QAAI,UAAUC,KAAK;AACjB/B,yBAAmB;QACjBiC,MAAM;QACNC,SAASvC,cAAc;UACrBR,IAAI;UACJgD,gBAAgB;QAClB,CAAA;MACF,CAAA;IACF;AAEA,QAAIlD,aAAa,WAAW8C,KAAK;AAC/B/B,yBAAmB;QACjBiC,MAAM;QACNC,SAASpC,eAAeiC,IAAIL,KAAK;MACnC,CAAA;IACF;EACF;AAEA,QAAMU,aACH,CAAC/B,wBAAwB,CAACU,kBAAkBP,MAAM6B,WAAW,KAAM,CAACjB,SAASE;AAChF,QAAMlB,YAAYW,kBAAkBV,wBAAwBsB;AAE5D,QAAMW,gBAAgB3C,cAAc;IAClCR,IAAI;IACJgD,gBAAgB;EAClB,CAAA;AACA,QAAMI,qBAAqB5C,cAAc;IACvCR,IAAI;IACJgD,gBAAgB;EAClB,CAAA;AACA,QAAMK,sBAAsB7C,cAAc;IACxCR,IAAI;IACJgD,gBAAgB;EAClB,CAAA;AAEA,MAAIlD,WAAW;AACb,eACEwD,2BAACC,MAAMC,MAAI;MAACC,MAAMpB;MAAyBrC,IAAIqC;;YAC7CqB,0BAACC,gBAAAA;wBACCD,0BAACH,MAAMK,OAAK;YAAET,UAAAA;;;YAEhBO,0BAACG,UAAAA;UACCC,YAAYV;UACZW,UAAUd;UACVe,OAAO5B,kBAAkBA,gBAAgBpC,GAAGiE,SAAQ,IAAK;UACzDC,UAAUxB;UACVyB,SAAS,MAAMzB,aAAa,IAAA;UAC5B0B,aAAaf;UACbgB,SAASpD,aAAaC,wBAAwBsB;UAC9C8B,MAAK;oBAEJjD,MAAMkD,IAAI,CAACC,SAAAA;AACV,uBACEd,0BAACe,QAAAA;cAECT,OAAOQ,KAAKxE,GAAGiE,SAAQ;cACvBS,WAAWC,eAAeH,IAAAA;wBAEzBG,eAAeH,IAAAA;YAJXA,GAAAA,KAAKxE,EAAE;UAOlB,CAAA;;;;EAIR;AAEA,aACEsD,2BAACC,MAAMC,MAAI;IACTC,MAAMpB;IACNrC,IAAIqC;IACJE,QACG,WACCvB,WACAR,cAAc;MACZR,IAAI;MACJgD,gBAAgB;KAEjBT,KAAAA,SAAS5B,eAAe4B,KAAAA,MAC3BR;;UAGF2B,0BAACH,MAAMK,OAAK;QAAET,UAAAA;;UACdO,0BAACG,UAAAA;QACCC,YAAYV;QACZW,UACG,CAAC7C,wBAAwB,CAACD,aAAaI,MAAM6B,WAAW,KAAM,CAACjB,SAASE;QAE3E6B,OAAO5B,kBAAkBA,gBAAgBpC,GAAGiE,SAAQ,IAAK;QACzDC,UAAUxB;QACVyB,SAAS,MAAMzB,aAAa,IAAA;QAC5B0B,aAAaf;QACbgB,SAASpD,aAAaC,wBAAwBsB;kBAE7CnB,MAAMkD,IAAI,CAACC,SAAAA;AACV,qBACEd,0BAACe,QAAAA;YAECT,OAAOQ,KAAKxE,GAAGiE,SAAQ;YACvBS,WAAWC,eAAeH,IAAAA;sBAEzBG,eAAeH,IAAAA;UAJXA,GAAAA,KAAKxE,EAAE;QAOlB,CAAA;;UAEF0D,0BAACH,MAAMqB,OAAK,CAAA,CAAA;;;AAGlB;;;;;ACzJA,IAAMC,qBAAqB,CAAC,EAAEC,MAAMC,aAAY,MAAoB;AAClE,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,aACEC,2BAACC,YAAYC,MAAI;IAACN;IAAYC;;UAC5BM,0BAACF,YAAYG,OAAK;kBACfN,cAAc;UACbO,IAAI;UACJC,gBAAgB;QAClB,CAAA;;UAGFH,0BAACF,YAAYM,MAAI;kBACdT,cAAc;UACbO,IAAI;UACJC,gBAAgB;QAClB,CAAA;;;;AAIR;AAEA,IAAME,kBAAkB,CAAC,EAAEZ,MAAMC,aAAY,MAAoB;AAC/D,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,aACEC,2BAACC,YAAYC,MAAI;IAACN;IAAYC;;UAC5BM,0BAACF,YAAYG,OAAK;kBACfN,cAAc;UACbO,IAAI;UACJC,gBAAgB;QAClB,CAAA;;UAGFH,0BAACF,YAAYM,MAAI;kBACdT,cAAc;UACbO,IAAI;UACJC,gBAAgB;QAClB,CAAA;;;;AAIR;AAMA,IAAMG,SAAS,CAAC,EACdC,QACAC,qBACAC,WACA,GAAGC,MACqF,MAAA;AACxF,QAAM,EAAEf,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEe,eAAc,IAAKC,mBAAmBJ,2DAAqBK,KAAAA,KAAU,CAAA;AAE7E,aACEb,0BAACc,cAAAA;IACCC,UAAUR,OAAOS,WAAW;IAC5BC,aAAatB,cAAc;MACzBO,IAAI;MACJC,gBAAgB;IAClB,CAAA;IACAe,WACEV,2BACER,0BAACmB,MAAAA;MACCC,KAAI;MACJC,QAAQ;MACRC,YAAYd,2DAAqBK;MACjCU,aAAaZ,mBAAmB,aAAa,eAAea;MAC5DC,WAAS;MACTC,QAAQ;MACRC,OAAO;MACPC,aAAY;;;IAKlBC,kBAAkB,MAAA;AAChB,iBACEhC,2BAACsB,MAAAA;QAAKC,KAAI;QAAOU,gBAAe;QAAgBC,YAAW;QAASJ,OAAM;;cACxE3B,0BAACgC,YAAAA;YAAWC,WAAU;YAAaC,UAAQ;YAACC,YAAW;YACpD3B,WAAAA,2DAAqB4B,SAAQ;;UAE/B3B,gBAAYT,0BAACqC,QAAAA;YAAOC,OAAK;YAACC,OAAO;cAAEC,SAAS;YAAO;YAAGC,eAAY;UAAc,CAAA,IAAA;;;IAGvF;IACC,GAAG/B;cAEHH,OAAOmC,IAAI,CAAC,EAAExC,IAAIW,OAAOuB,MAAAA,MAAI,MAAE;AAC9B,YAAM,EAAEzB,gBAAAA,gBAAc,IAAKC,mBAAmBC,KAAAA,KAAU,CAAA;AAExD,iBACEb,0BAAC2C,oBAAAA;QAECzB,eACElB,0BAACmB,MAAAA;UACCE,QAAQ;UACRC,YAAYT;UACZU,aAAaZ,oBAAmB,aAAa,eAAea;UAC5DC,WAAS;UACTC,QAAQ;UACRC,OAAO;;QAGXiB,OAAO1C;QACP2C,WAAWT;QAEVA,UAAAA;MAdIlC,GAAAA,EAAAA;IAiBX,CAAA;;AAGN;AAEa4C,IAAAA,cAAc,CAAC,EAAEC,UAAS,MAA2B;AAChE,QAAM,EACJC,iBAAiB,IACjBC,MAAMC,QAAQ,IACdhD,KAAK,GAAE,IACLiD,UAAAA;AAKJ,QAAM,EAAExD,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEwD,yBAAyBC,eAAc,IAAKC,mBAAAA;AACpD,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,CAAC,EAAEC,MAAK,CAAE,IAAIC,eAAAA;AACpB,QAAMC,SAAeC,gBAAQ,MAAMC,kBAAiBJ,KAAQ,GAAA;IAACA;EAAM,CAAA;AACnE,QAAM,EAAEK,UAAUrD,WAAWsD,kBAAiB,IAAKC,YACjD;IACEhB;IACAE;IACAe,YAAY/D;KAEd;IACEgE,MAAM,CAAChE,MAAM8C,mBAAmB;EAClC,CAAA;AAGF,QAAM,EAAEmB,MAAM1D,WAAW2D,gBAAe,IAAKC,kBAC3C;IACEpB,MAAMD;IACNE;;IAEAhD,IAAI4D,qCAAUG;IACdN;KAEF;IACEO,MAAM,EAACJ,qCAAUG;EACnB,CAAA;AAGF,QAAM,EAAEK,MAAM/D,SAAS,CAAA,EAAE,IAAK4D,QAAQ,CAAA;AAEtC,QAAM,EAAEI,WAAU,IAAKC,iBAAAA;AACvB,QAAM,CAACC,gBAAgBC,iBAAAA,IAA2BC,iBAAsC,IAAA;AAExF,QAAMC,SAASL,WAAmB,kBAAA,KAAuB,CAAA;AAEzD,QAAM/D,sBAAsBsD,WAAWA,SAASe,oBAAAA,IAAwB;AAExE,QAAM,CAACC,aAAa,EAAEC,MAAK,CAAE,IAAIC,uBAAAA;AAEjC,QAAMC,eAAe,OAAOC,YAAAA;AAC1B,QAAI;AAWF,WACEN,iCAASO,yCACTC,SAASR,OAAOO,mCAAAA,GAAsC,EAAOb,MAAAA,6BAAMe,kBAAiB,IACpF;AACAX,0BAAkB,UAAA;MAUjB,YAEDE,iCAASU,oDACTF,SAASR,OAAOU,8CAA+C,GAAE,EAAM/E,IAAAA,OAAOS,QAC9E;AACA0D,0BAAkB,OAAA;aACb;AACL,YAAIZ,qCAAUG,YAAY;AACxB,gBAAMsB,MAAM,MAAMT,YAAY;YAC5B5B;YACAhD,IAAI4D,SAASG;YACbhB,MAAMD;YACNW;YACAQ,MAAM;cAAEjE,IAAIgF;YAAQ;UACtB,CAAA;AAEA,cAAI,UAAUK,KAAK;AACjBhC,+BAAmB;cACjBiC,MAAM;cACNC,SAAS9F,cAAc;gBACrBO,IAAI;gBACJC,gBAAgB;cAClB,CAAA;YACF,CAAA;UACF;AAEA,cAAI4C,aAAa,WAAWwC,KAAK;AAC/BhC,+BAAmB;cACjBiC,MAAM;cACNC,SAASpC,eAAekC,IAAIR,KAAK;YACnC,CAAA;UACF;QACF;MACF;IACF,SAASA,QAAO;AACdxB,yBAAmB;QACjBiC,MAAM;QACNC,SAAS9F,cAAc;UACrBO,IAAI;UACJC,gBAAgB;QAClB,CAAA;MACF,CAAA;IACF;EACF;AAEA,QAAMM,YAAY2D,mBAAmBL;AAErC,QAAM2B,mBAAmB/F,cAAc;IACrCO,IAAI;IACJC,gBAAgB;EAClB,CAAA;AACA,QAAMwF,kBACJ,CAAClF,aACDF,OAAOS,WAAW;EAElBrB,cAAc;IACZO,IAAI;IACJC,gBAAgB;EAClB,CAAA;AAEF,MAAI4C,WAAW;AACb,eACElD,2BAAA+F,+BAAA;;YACE5F,0BAAC6F,aAAAA;UAAQC,OAAOH;wBACd3F,0BAAC+F,MAAMhG,MAAI;YAACqC,MAAMyC;YAAsB3E,IAAI2E;0BAC1ChF,2BAAA+F,+BAAA;;oBACE5F,0BAACgG,gBAAAA;gCACChG,0BAAC+F,MAAME,OAAK;oBAAEP,UAAAA;;;oBAEhB1F,0BAACM,QAAAA;kBACCC;kBACAC;kBACAC;kBACAyF,MAAK;kBACLnF,UAAUR,OAAOS,WAAW;kBAC5B4B,OAAOpC,2DAAqBN;kBAC5BiG,UAAUlB;kBACVhE,aAAatB,cAAc;oBACzBO,IAAI;oBACJC,gBAAgB;kBAClB,CAAA;;;;;;YAKRH,0BAACR,oBAAAA;UACCC,MAAMgF,mBAAmB;UACzB/E,cAAc,MAAMgF,kBAAkB,IAAA;;YAExC1E,0BAACK,iBAAAA;UACCZ,MAAMgF,mBAAmB;UACzB/E,cAAc,MAAMgF,kBAAkB,IAAA;;;;EAI9C;AAEA,aACE7E,2BAAA+F,+BAAA;;UACE/F,2BAACkG,MAAMhG,MAAI;QACTqG,MAAMT;QACNZ,OAAQA,SAAS1B,eAAe0B,KAAWvD,KAAAA;QAC3CY,MAAMyC;QACN3E,IAAI2E;;cAEJ7E,0BAAC+F,MAAME,OAAK;YAAEP,UAAAA;;cACd1F,0BAACM,QAAAA;YACCC;YACAC;YACAC;YACAM,UAAUR,OAAOS,WAAW;YAC5B4B,OAAOpC,2DAAqBN;YAC5BiG,UAAUlB;YACVhE,aAAatB,cAAc;cACzBO,IAAI;cACJC,gBAAgB;YAClB,CAAA;;cAEFH,0BAAC+F,MAAMM,MAAI,CAAA,CAAA;cACXrG,0BAAC+F,MAAMO,OAAK,CAAA,CAAA;;;UAEdtG,0BAACR,oBAAAA;QACCC,MAAMgF,mBAAmB;QACzB/E,cAAc,MAAMgF,kBAAkB,IAAA;;UAExC1E,0BAACK,iBAAAA;QACCZ,MAAMgF,mBAAmB;QACzB/E,cAAc,MAAMgF,kBAAkB,IAAA;;;;AAI9C;;;ACnWA,IAAM6B,SAAS,MAAA;AACb,QAAM,EACJC,OAAO,IACPC,IACAC,eAAc,IACZC,UAAAA;AAMJ,QAAM,EACJC,MAAM,EAAEC,QAAO,EAAE,IACfC,kBAAkBN,IAAAA;AAEtB,MACE,CAACO,OAAOC,OAAOC,QACf,EAACJ,mCAASK,oBACTR,mBAAmB,kBAAkB,CAACD,MACvCA,OAAO,UACP;AACA,WAAO;EACT;AAEA,aACEU,2BAACC,MAAAA;IAAKC,KAAK;;UACTC,0BAACC,gBAAAA;QAAeC,WAAS;;UACzBF,0BAACG,aAAAA;QAAYD,WAAS;;;;AAG5B;AAEAjB,OAAOmB,OAAO;;;;AC7Bd,IAAMC,SAAwB,MAAA;AAC5B,QAAM,EACJC,OAAO,IACPC,IACAC,eAAc,IACZC,UAAAA;AAMJ,QAAM,EACJC,MAAM,EAAEC,QAAO,EAAE,IACfC,kBAAkBN,IAAAA;AACtB,QAAM,EAAEO,cAAa,IAAKC,QAAAA;AAE1B,MACE,CAACC,OAAOC,OAAOC,QACf,EAACN,mCAASO,oBACTV,mBAAmB,kBAAkB,CAACD,MACvCA,OAAO,UACP;AACA,WAAO;EACT;AAEA,SAAO;IACLY,OAAON,cAAc;MACnBN,IAAI;MACJa,gBAAgB;IAClB,CAAA;IACAC,aACEC,2BAACC,MAAAA;MAAKC,WAAU;MAASC,KAAK;MAAGC,YAAW;MAAUC,OAAM;;YAC1DC,0BAACC,gBAAAA,CAAAA,CAAAA;YACDD,0BAACE,aAAAA,CAAAA,CAAAA;;;EAGP;AACF;AAGAzB,OAAM0B,OAAO;;;;;;;;AClCb,IAAMC,cAAc,CAACC,UAAAA;AACnB,QAAM,EAAEC,QAAQC,qBAAqBC,MAAAA,MAAI,IAAKH,MAAMI,gBAAgB,CAAA;AACpE,QAAM,EAAEC,eAAc,IAAKC,mBAAmBL,KAAAA,KAAU,CAAA;AAExD,aACEM,2BAACC,MAAAA;IAAKC,YAAW;IAASC,KAAK;IAAGC,UAAS;;UACzCC,0BAACC,KAAAA;QACCC,QAAQ;QACRC,YAAYd;QACZe,aAAaX,mBAAmB,aAAa,eAAeY;QAC5DC,WAAS;QACTC,QAAQ;QACRC,OAAO;;UAGTR,0BAACS,YAAAA;QAAWC,YAAW;QAAUC,WAAU;QAAaC,UAAQ;QAC7DrB,UAAAA;;;;AAIT;AAWA,IAAMsB,iBAAiB,CAACzB,UAAAA;AACtB,QAAM,EAAE0B,iBAAiBC,KAAI,IAAK3B;AAClC,aAAOY,0BAACS,YAAAA;IAAWE,WAAU;IAAcI,UAAAA,OAAOC,eAAeD,IAAQ,IAAA;;AAC3E;;;ICzCaE,0BAA0B;EACrC;IACEC,MAAMC;IACNC,WAAW;MACTC,MAAM;MACNC,UAAU;MACVC,QAAQ;IACV;IACAC,OAAO;MACLC,IAAI;MACJC,gBAAgB;IAClB;IACAC,YAAY;IACZC,UAAU;IACVC,WAAW;MACTX,MAAM;MACNG,MAAM;IACR;IACAS,eAAe,CAACC,cAAUC,0BAACC,aAAAA;MAAa,GAAGF;;EAC7C;EACA;IACEb,MAAMgB;IACNd,WAAW;MACTC,MAAM;MACNE,QAAQ;MACRD,UAAU;IACZ;IACAE,OAAO;MACLC,IAAI;MACJC,gBAAgB;IAClB;IACAC,YAAY;IACZC,UAAU;IACVC,WAAW;MACTX,MAAM;MACNG,MAAM;IACR;IACAS,eAAe,CAACC,cAAUC,0BAACG,gBAAAA;MAAgB,GAAGJ;;EAChD;;;;ACnCF,IAAMK,wBAAuB,CAAC,EAAEC,kBAAkBC,OAAM,MAA4B;AAClF,QAAM,EAAEC,QAAO,IAAKD;AAEpB,MAAI,CAACC,QAAQC,iBAAiB;AAC5B,WAAO;MAAEH;MAAkBC;IAAO;EACpC;AAEA,SAAO;IACLD,kBAAkB;MAAIA,GAAAA;MAAqBI,GAAAA;IAAwB;IACnEH;EACF;AACF;;;ACrBMI,IAAAA,4BAA2B,CAACC,MAAmBC,cAAAA;AAInD,SAAOC,OAAOC,KAAKH,IAAAA,EAAMI,OAAO,CAACC,KAAKC,YAAAA;AACpCD,QAAI,GAAGJ,SAAAA,IAAYK,OAAQ,EAAC,IAAIN,KAAKM,OAAQ;AAC7C,WAAOD;EACT,GAAG,CAAA,CAAC;AACN;;;;;;;;;;;;;;;;;ACAA,IAAME,SAAkC;EACtCC,SAASC,KAAc;;AACrB,QAAIC,OAAOC,OAAOC,SAASC,UAAUC,UAAa,GAAA;AAChDL,UAAIM,aAAa,kDAAkDC,qBAAAA;AAEnE,YAAMC,2BAA2BR,IAAIS,UAAU,iBAAA,EAAmBC;AAElE,UACE,0BAA0BF,4BAC1B,OAAOA,yBAAyBG,yBAAyB,YACzD;AACAH,iCAAyBG,qBAAqB;UAACC;QAAM,CAAA;MACvD;AAEAZ,UAAIa,gBAAgB,UAAU;QAC5BC,IAAIC;QACJC,IAAI;QACJC,WAAW;UACTH,IAAI,GAAGC,SAAU;UACjBG,gBAAgB;QAClB;QACAC,aAAa,CAAA;QACb,MAAMC,YAAAA;AACJ,gBAAM,EAAEC,OAAM,IAAK,MAAM,OAAO,sBAAA;AAChC,iBAAO;YAAEC,SAASD;UAAO;QAC3B;MACF,CAAA;IACF,WAAW,CAACpB,OAAOC,OAAOC,SAASC,UAAUC,UAAeJ,OAAAA,kBAAOC,WAAPD,mBAAesB,UAAftB,mBAAsBuB,YAAW;AAC3FxB,UAAIa,gBAAgB,UAAU;QAC5BC,IAAIC;QACJC,IAAI;QACJC,WAAW;UACTH,IAAI,GAAGC,SAAU;UACjBG,gBAAgB;QAClB;QACAO,aAAa;QACbN,aAAa,CAAA;QACb,MAAMC,YAAAA;AACJ,gBAAM,EAAEM,wBAAuB,IAAK,MAAM,OAAO,yCAAA;AACjD,iBAAO;YAAEJ,SAASI;UAAwB;QAC5C;MACF,CAAA;IACF;EACF;EACAC,UAAU3B,KAAc;AACtB,QAAIC,OAAOC,OAAOC,SAASC,UAAUC,UAAa,GAAA;AAChDL,UAAIS,UAAU,iBAAA,EAAmBmB,gBAAgB,WAAW,WAAW;QACrEC,MAAM;QACNT,WAAWU;MACb,CAAA;IACF;EACF;EACA,MAAMC,cAAc,EAAEC,QAAO,GAAyB;AACpD,UAAMC,gBAAgB,MAAMC,QAAQC,IAClCH,QAAQI,IAAI,CAACC,WAAAA;AACX,aAAOC,mCAAO,kBAAkBD,MAAO,OAAM,EAC1CE,KAAK,CAAC,EAAEjB,SAASkB,KAAI,MAAE;AACtB,eAAO;UACLA,MAAMC,0BAAyBD,MAAMzB,SAAAA;UACrCsB;QACF;MACF,CAAA,EACCK,MAAM,MAAA;AACL,eAAO;UACLF,MAAM,CAAA;UACNH;QACF;MACF,CAAA;IACJ,CAAA,CAAA;AAGF,WAAOH,QAAQS,QAAQV,aAAAA;EACzB;AACF;;;AC1EA,IAAMW,SAAS,CAACC,WAA+B,EAAEC,SAAS,GAAGC,SAA2B,MAAA;AACtF,SAAOC,YAAYH,WAAW;IAC5B,GAAGE;IACHD,SAAS;MACP,mBAAmBG;MACnB,wBAAwBC;MACxBC;MACAC,QAAAA;MACAC,iBAAAA;MACAC,MAAAA;MACAC,iBAAAA;MACA,GAAGT;IACL;EACF,CAAA;AACF;", "names": ["has", "compactQueue", "arrayToObject", "merge", "encode", "compact", "isRegExp", "<PERSON><PERSON><PERSON><PERSON>", "combine", "maybeMap", "has", "isNonNullishPrimitive", "stringify", "value", "normalizeStringifyOptions", "has", "index", "normalizeParseOptions", "require_lib", "stringify", "parse", "reducers", "formModalReducer", "dataManagerProviderReducer", "formsAPI", "components", "inputs", "add", "id", "component", "types", "attribute", "contentType", "validators", "form", "advanced", "base", "contentTypeSchemaMutations", "addContentTypeSchemaMutation", "cb", "push", "extendContentType", "validator", "extendFields", "fields", "formType", "for<PERSON>ach", "field", "getAdvancedForm", "target", "props", "sectionsToAdd", "get", "reduce", "acc", "current", "sections", "makeCustomFieldValidator", "attributeShape", "validatorArgs", "shape", "options", "object", "makeValidator", "initShape", "args", "pluginOptionsShape", "pluginOptionShape", "pluginOptions", "mutateContentTypeSchema", "data", "initialData", "enhancedData", "cloneDeep", "refData", "prefixPluginTranslations", "trad", "pluginId", "Object", "keys", "reduce", "acc", "current", "index", "register", "app", "addReducers", "reducers", "addMenuLink", "to", "pluginId", "icon", "Layout", "intlLabel", "id", "defaultMessage", "permissions", "PERMISSIONS", "main", "Component", "position", "registerPlugin", "name", "apis", "forms", "formsAPI", "bootstrap", "registerTrads", "locales", "importedTrads", "Promise", "all", "map", "locale", "then", "default", "data", "prefixPluginTranslations", "catch", "resolve", "prefixPluginTranslations", "trad", "pluginId", "Object", "keys", "reduce", "acc", "current", "admin", "register", "app", "createSettingSection", "id", "intlLabel", "defaultMessage", "to", "Component", "then", "mod", "default", "ProtectedSettingsPage", "permissions", "PERMISSIONS", "settings", "registerPlugin", "name", "bootstrap", "registerTrads", "locales", "importedTrads", "Promise", "all", "map", "locale", "__variableDynamicImportRuntime1__", "data", "prefixPluginTranslations", "catch", "resolve", "useModalQueryParams", "initialState", "trackUsage", "useTracking", "config", "data", "useConfig", "queryObject", "setQueryObject", "useState", "page", "sort", "pageSize", "filters", "$and", "React", "useEffect", "prev<PERSON><PERSON><PERSON>", "handleChangeFilters", "nextFilters", "location", "filter", "Object", "keys", "length", "prev", "handleChangePageSize", "parseInt", "handeChangePage", "handleChangeSort", "handleChangeSearch", "_q", "newState", "for<PERSON>ach", "key", "includes", "handleChangeFolder", "folder", "folderPath", "<PERSON><PERSON><PERSON><PERSON>", "stringify", "encode", "onChangeFilters", "onChangeFolder", "onChangePage", "onChangePageSize", "onChangeSort", "onChangeSearch", "toSingularTypes", "types", "map", "type", "substring", "length", "getAllowedFiles", "pluralTypes", "files", "singularTypes", "toSingularTypes", "allowedFiles", "filter", "file", "fileType", "mime", "split", "includes", "move", "array", "oldIndex", "newIndex", "length", "splice", "moveElement", "index", "offset", "getBreadcrumbDataCM", "folder", "data", "id", "label", "getTrad", "defaultMessage", "parent", "push", "name", "path", "Filters", "appliedFilters", "onChangeFilters", "open", "<PERSON><PERSON><PERSON>", "useState", "formatMessage", "useIntl", "_jsxs", "Popover", "Root", "onOpenChange", "_jsx", "<PERSON><PERSON>", "<PERSON><PERSON>", "variant", "startIcon", "Filter", "size", "id", "defaultMessage", "FilterPopover", "onToggle", "prev", "displayedFilters", "filters", "onSubmit", "FilterList", "filtersSchema", "onRemoveFilter", "PageSize", "onChangePageSize", "pageSize", "formatMessage", "useIntl", "handleChange", "value", "Number", "_jsxs", "Flex", "SingleSelect", "size", "aria-label", "id", "defaultMessage", "onChange", "toString", "_jsx", "SingleSelectOption", "Box", "paddingLeft", "Typography", "textColor", "tag", "htmlFor", "PaginationContext", "createContext", "activePage", "pageCount", "usePagination", "useContext", "Pagination", "children", "label", "paginationValue", "useMemo", "_jsx", "Provider", "value", "Box", "tag", "aria-label", "Flex", "gap", "PaginationText", "styled", "Typography", "linkWrapperStyles", "css", "theme", "spaces", "borderRadius", "$active", "shadows", "filterShadow", "undefined", "props", "colors", "primary600", "LinkWrapperButton", "button", "LinkWrapperDiv", "div", "defaultProps", "type", "PageLinkWrapper", "primary700", "neutral800", "neutral0", "ActionLinkWrapper", "p", "neutral300", "neutral600", "neutral700", "DotsWrapper", "PreviousLink", "children", "activePage", "usePagination", "disabled", "_jsx", "li", "_jsxs", "aria-disabled", "tabIndex", "VisuallyHidden", "ChevronLeft", "aria-hidden", "NextLink", "pageCount", "ChevronRight", "PageLink", "number", "isActive", "variant", "fontWeight", "Dots", "as", "small", "Pagin<PERSON><PERSON><PERSON>er", "onChangePage", "pagination", "formatMessage", "useIntl", "previousActivePage", "nextActivePage", "firstLinks", "onClick", "id", "defaultMessage", "page", "links", "Array", "from", "length", "map", "_", "i", "Pagination", "firstLinksToCreate", "lastLinks", "lastLinksToCreate", "middleLinks", "push", "for<PERSON>ach", "unshift", "includes", "middleLinksToCreate", "shouldShowDotsAfterFirstLink", "shouldShowMiddleDots", "beforeDotsLinksLength", "afterDotsLength", "SearchAsset", "onChangeSearch", "queryValue", "formatMessage", "useIntl", "trackUsage", "useTracking", "isOpen", "setIsOpen", "useState", "value", "setValue", "wrapperRef", "useRef", "React", "useLayoutEffect", "setTimeout", "current", "querySelector", "focus", "handleToggle", "prev", "handleClear", "handleSubmit", "e", "preventDefault", "stopPropagation", "location", "_jsx", "div", "ref", "SearchForm", "onSubmit", "Searchbar", "name", "onClear", "onChange", "target", "<PERSON><PERSON><PERSON><PERSON>", "id", "getTrad", "defaultMessage", "aria-label", "size", "placeholder", "IconButton", "label", "onClick", "Search", "isSelectable", "allowedTypes", "mime", "fileType", "split", "includes", "TypographyMaxWidth", "styled", "Typography", "ActionContainer", "Box", "theme", "colors", "neutral500", "BrowseStep", "allowedTypes", "assets", "rawAssets", "canCreate", "canRead", "folders", "multiple", "onAddAsset", "onChangeFilters", "onChangePage", "onChangePageSize", "onChangeSearch", "onChangeSort", "onChangeFolder", "onEditAsset", "onEditFolder", "onSelectAllAsset", "onSelectAsset", "pagination", "queryObject", "selectedAssets", "formatMessage", "useIntl", "view", "<PERSON><PERSON><PERSON><PERSON>", "usePersistentState", "localStorageKeys", "modalView", "viewOptions", "GRID", "is<PERSON><PERSON><PERSON><PERSON><PERSON>", "data", "currentFolder", "isLoading", "isCurrentFolderLoading", "useFolder", "folder", "enabled", "singularTypes", "toSingularTypes", "map", "asset", "isSelectable", "mime", "type", "breadcrumbs", "getBreadcrumbDataCM", "undefined", "allAllowedAsset", "getAllowedFiles", "areAllAssetSelected", "length", "every", "findIndex", "currAsset", "id", "hasSomeAssetSelected", "some", "isSearching", "_q", "isFiltering", "filters", "$and", "isSearchingOrFiltering", "assetCount", "folderCount", "handleClickFolderCard", "args", "_jsxs", "_jsx", "paddingBottom", "Flex", "justifyContent", "alignItems", "gap", "wrap", "paddingLeft", "paddingRight", "background", "hasRadius", "borderColor", "height", "Checkbox", "aria-label", "getTrad", "defaultMessage", "checked", "onCheckedChange", "SortPicker", "value", "sort", "Filters", "appliedFilters", "marginLeft", "shrink", "paddingTop", "IconButton", "label", "onClick", "LIST", "List", "GridIcon", "SearchAsset", "queryValue", "Breadcrumbs", "currentFolderId", "EmptyAssets", "size", "count", "action", "<PERSON><PERSON>", "variant", "startIcon", "Plus", "content", "TableList", "indeterminate", "isFolderSelectionAllowed", "onSelectOne", "onSelectAll", "rows", "selected", "shouldDisableBulkSelect", "sortQuery", "_Fragment", "FolderGridList", "title", "Grid", "<PERSON><PERSON>", "col", "direction", "FolderCard", "aria<PERSON><PERSON><PERSON>", "name", "path", "cardActions", "withTooltip", "Pencil", "FolderCardBody", "FolderCardBodyAction", "tag", "max<PERSON><PERSON><PERSON>", "fontWeight", "ellipsis", "textColor", "VisuallyHidden", "children", "filesCount", "files", "Divider", "AssetGridList", "page", "pageCount", "position", "zIndex", "PageSize", "pageSize", "Pagin<PERSON><PERSON><PERSON>er", "activePage", "<PERSON><PERSON><PERSON><PERSON>er", "onClose", "onValidate", "formatMessage", "useIntl", "_jsxs", "Modal", "Footer", "_jsx", "<PERSON><PERSON>", "onClick", "variant", "id", "defaultMessage", "SelectedStep", "selectedAssets", "onSelectAsset", "onReorderAsset", "formatMessage", "useIntl", "_jsxs", "Flex", "direction", "alignItems", "gap", "_jsx", "Typography", "variant", "fontWeight", "textColor", "id", "getTrad", "defaultMessage", "number", "length", "AssetGridList", "size", "assets", "LoadingBody", "styled", "Flex", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "allowedTypes", "folderId", "onClose", "onAddAsset", "onAddFolder", "onChangeFolder", "onValidate", "multiple", "initiallySelectedAssets", "trackedLocation", "assetToEdit", "setAssetToEdit", "useState", "undefined", "folderToEdit", "setFolderToEdit", "formatMessage", "useIntl", "canRead", "canCreate", "isLoading", "isLoadingPermissions", "canUpdate", "canCopyLink", "canDownload", "useMediaLibraryPermissions", "queryObject", "onChangeFilters", "onChangePage", "onChangePageSize", "onChangeSort", "onChangeSearch", "onChangeFolderParam", "useModalQueryParams", "folder", "data", "pagination", "results", "assets", "isLoadingAssets", "error", "errorAssets", "useAssets", "<PERSON><PERSON><PERSON>", "query", "folders", "isLoadingFolders", "errorFolders", "useFolders", "enabled", "containsAssetFilter", "page", "selectedAssets", "selectOne", "selectOnly", "setSelections", "selectMultiple", "deselectMultiple", "useSelectionState", "handleSelectAllAssets", "allowedAssets", "getAllowedFiles", "alreadySelected", "filter", "asset", "findIndex", "selectedAsset", "id", "length", "handleSelectAsset", "<PERSON><PERSON><PERSON><PERSON>", "activeTab", "setActiveTab", "_jsxs", "_Fragment", "_jsx", "Modal", "Header", "Title", "getTrad", "defaultMessage", "justifyContent", "paddingTop", "paddingBottom", "Loader", "<PERSON><PERSON><PERSON><PERSON>er", "Page", "Error", "NoPermissions", "EditAssetContent", "EditFolderContent", "location", "parentFolderId", "handleMoveItem", "hoverIndex", "destIndex", "offset", "orderedAssetsClone", "slice", "nextAssets", "moveElement", "handleFolderChange", "folderPath", "TabsRoot", "variant", "value", "onValueChange", "paddingLeft", "paddingRight", "Tabs", "List", "<PERSON><PERSON>", "Badge", "marginLeft", "gap", "<PERSON><PERSON>", "onClick", "Divider", "Body", "Content", "BrowseStep", "onSelectAsset", "onSelectAllAsset", "onEditAsset", "onEditFolder", "filters", "sort", "SelectedStep", "onReorderAsset", "AssetDialog", "open", "restProps", "Root", "onOpenChange", "STEPS", "AssetSelect", "AssetUpload", "FolderCreate", "MediaLibraryDialog", "onClose", "onSelectAssets", "allowedTypes", "step", "setStep", "useState", "folderId", "setFolderId", "_jsx", "AssetDialog", "open", "onValidate", "onAddAsset", "onAddFolder", "onChangeFolder", "multiple", "EditFolderDialog", "parentFolderId", "UploadAssetDialog", "DocAsset", "styled", "Flex", "VideoPreviewWrapper", "Box", "AudioPreviewWrapper", "CarouselAsset", "asset", "mime", "includes", "AssetType", "Video", "_jsx", "height", "VideoPreview", "url", "createAssetUrl", "alt", "alternativeText", "name", "Audio", "AudioPreview", "Image", "assetUrl", "cacheBustedUrl", "updatedAt", "tag", "maxHeight", "max<PERSON><PERSON><PERSON>", "src", "width", "justifyContent", "hasRadius", "ext", "FilePdf", "aria-label", "File", "CarouselAssetActions", "asset", "onDeleteAsset", "onAddAsset", "onEditAsset", "formatMessage", "useIntl", "_jsxs", "CarouselActions", "_jsx", "IconButton", "label", "id", "getTrad", "defaultMessage", "onClick", "Plus", "CopyLinkButton", "url", "prefixFileUrlWithBackendUrl", "Trash", "Pencil", "TextAlignTypography", "styled", "Typography", "EmptyStateAsset", "disabled", "onClick", "onDropAsset", "formatMessage", "useIntl", "dragOver", "setDragOver", "useState", "handleDragEnter", "e", "preventDefault", "handleDragLeave", "currentTarget", "contains", "relatedTarget", "handleDragOver", "handleDrop", "dataTransfer", "files", "assets", "i", "length", "file", "item", "asset", "rawFileToAsset", "AssetSource", "Computer", "push", "_jsxs", "Flex", "borderStyle", "undefined", "borderWidth", "borderColor", "direction", "justifyContent", "alignItems", "height", "width", "tag", "type", "onDragEnter", "onDragLeave", "onDragOver", "onDrop", "gap", "style", "cursor", "_jsx", "PicturePlus", "aria-hidden", "fill", "variant", "fontWeight", "textColor", "textAlign", "id", "getTrad", "defaultMessage", "CarouselAssets", "forwardRef", "assets", "disabled", "error", "hint", "label", "labelAction", "onAddAsset", "onDeleteAsset", "onDeleteAssetFromMediaLibrary", "onDropAsset", "onEditAsset", "onNext", "onPrevious", "required", "selectedAssetIndex", "trackedLocation", "forwardedRef", "formatMessage", "useIntl", "isEditingAsset", "setIsEditingAsset", "useState", "currentAsset", "_jsxs", "_Fragment", "_jsx", "CarouselInput", "ref", "secondaryLabel", "name", "selectedSlide", "previousLabel", "id", "getTrad", "defaultMessage", "next<PERSON><PERSON><PERSON>", "actions", "CarouselAssetActions", "asset", "undefined", "length", "CarouselSlide", "n", "m", "EmptyStateAsset", "onClick", "map", "index", "CarouselAsset", "EditAssetDialog", "open", "onClose", "editedAsset", "canUpdate", "canCopyLink", "canDownload", "STEPS", "AssetSelect", "AssetUpload", "FolderCreate", "MediaLibraryInput", "forwardRef", "attribute", "allowedTypes", "multiple", "label", "hint", "disabled", "labelAction", "undefined", "name", "required", "forwardedRef", "formatMessage", "useIntl", "onChange", "value", "error", "useField", "uploadedFiles", "setUploadedFiles", "useState", "step", "setStep", "selectedIndex", "setSelectedIndex", "droppedAssets", "setDroppedAssets", "folderId", "setFolderId", "toggleNotification", "useNotification", "React", "useEffect", "selectedAssets", "Array", "isArray", "handleValidation", "nextSelectedAssets", "handleDeleteAssetFromMediaLibrary", "nextValue", "filter", "_", "assetIndex", "length", "handleDeleteAsset", "asset", "prevAsset", "id", "handleAssetEdit", "map", "validateAssetsTypes", "assets", "callback", "allowedAssets", "getAllowedFiles", "type", "timeout", "message", "getTrad", "defaultMessage", "fileTypes", "join", "handleAssetDrop", "handleNext", "current", "handlePrevious", "handleFilesUploadSucceeded", "prev", "initiallySelectedAssets", "allowedUploadedFiles", "_jsxs", "_Fragment", "_jsx", "CarouselAssets", "ref", "onDeleteAsset", "onDeleteAssetFromMediaLibrary", "onAddAsset", "onDropAsset", "onEditAsset", "onNext", "onPrevious", "selectedAssetIndex", "trackedLocation", "AssetDialog", "onClose", "open", "onValidate", "onAddFolder", "onChangeFolder", "folder", "UploadAssetDialog", "initialAssetsToAdd", "addUploadedFiles", "EditFolderDialog", "parentFolderId", "prefixPluginTranslations", "trad", "pluginId", "TypeError", "Object", "keys", "reduce", "acc", "current", "name", "pluginPkg", "strapi", "admin", "register", "app", "addMenuLink", "to", "pluginId", "icon", "Images", "intlLabel", "id", "defaultMessage", "permissions", "PERMISSIONS", "main", "Component", "then", "mod", "default", "Upload", "position", "addSettingsLink", "getTrad", "ProtectedSettingsPage", "settings", "addFields", "type", "MediaLibraryInput", "addComponents", "MediaLibraryDialog", "registerPlugin", "registerTrads", "locales", "importedTrads", "Promise", "all", "map", "locale", "data", "prefixPluginTranslations", "catch", "resolve", "TextAlignTypography", "styled", "Typography", "CheckboxConfirmation", "description", "isCreating", "intlLabel", "name", "onChange", "value", "formatMessage", "useIntl", "isOpen", "setIsOpen", "useState", "handleChange", "target", "type", "handleConfirm", "label", "id", "defaultMessage", "values", "hint", "_jsxs", "Dialog", "Root", "open", "onOpenChange", "Field", "_jsx", "Checkbox", "onCheckedChange", "checked", "Hint", "Content", "Header", "getTranslation", "Body", "icon", "WarningCircle", "Flex", "direction", "alignItems", "gap", "justifyContent", "fontWeight", "Footer", "Cancel", "<PERSON><PERSON>", "variant", "Action", "onClick", "LOCALIZED_FIELDS", "doesPluginOptionsHaveI18nLocalized", "opts", "has", "capitalize", "str", "char<PERSON>t", "toUpperCase", "slice", "useI18n", "params", "useParams", "userPermissions", "useAuth", "state", "permissions", "actions", "useMemo", "filter", "permission", "subject", "slug", "reduce", "acc", "actionShorthand", "action", "split", "slice", "capitalize", "properties", "locales", "canCreate", "canRead", "canUpdate", "canDelete", "canPublish", "schema", "useDocument", "collectionType", "model", "skip", "doesPluginOptionsHaveI18nLocalized", "pluginOptions", "hasI18n", "i18n", "localized", "relationsApi", "i18nApi", "injectEndpoints", "overrideExisting", "endpoints", "builder", "getManyDraftRelationCount", "query", "model", "params", "url", "method", "config", "transformResponse", "response", "data", "useGetManyDraftRelationCountQuery", "cleanData", "data", "schema", "components", "cleanedData", "removeFields", "cleanedDataWithoutPasswordAndRelation", "recursiveRemoveFieldTypes", "fields", "Object", "keys", "reduce", "acc", "current", "includes", "attribute", "attributes", "type", "undefined", "map", "componentValue", "index", "id", "_", "rest", "__component", "__temp_key__", "repeatable", "component", "compoData", "isErrorMessageDescriptor", "object", "EntryValidationText", "status", "validationErrors", "action", "formatMessage", "useIntl", "getErrorStr", "key", "value", "Array", "isArray", "map", "v", "join", "Object", "entries", "k", "validationErrorsMessages", "_jsxs", "Flex", "gap", "_jsx", "CrossCircle", "fill", "<PERSON><PERSON><PERSON>", "label", "Typography", "max<PERSON><PERSON><PERSON>", "textColor", "variant", "fontWeight", "ellipsis", "getStatusMessage", "icon", "CheckCircle", "text", "id", "defaultMessage", "ArrowsCounterClockwise", "BoldChunk", "chunks", "BulkLocaleActionModal", "headers", "rows", "localesMetadata", "selectedRows", "useTable", "state", "getFormattedCountMessage", "currentStatusByLocale", "reduce", "acc", "locale", "localesWithErrors", "keys", "publishedCount", "filter", "length", "draftCount", "includes", "with<PERSON>rrors<PERSON>ount", "messageId", "b", "Modal", "Body", "Box", "marginTop", "Table", "Content", "Head", "HeaderCheckboxCell", "head", "<PERSON><PERSON><PERSON><PERSON>", "name", "index", "error", "statusVariant", "Row", "CheckboxCell", "aria-label", "Cell", "find", "localeEntry", "code", "display", "Status", "paddingLeft", "paddingRight", "paddingTop", "paddingBottom", "size", "tag", "capitalize", "IconButton", "Link", "to", "search", "stringify", "plugins", "i18n", "getTranslation", "Pencil", "statusVariants", "draft", "published", "modified", "LocaleOption", "isDraftAndPublishEnabled", "locale", "status", "entryExists", "formatMessage", "useIntl", "id", "getTranslation", "defaultMessage", "bold", "_jsx", "b", "name", "_jsxs", "Flex", "width", "gap", "justifyContent", "Typography", "Status", "display", "paddingLeft", "paddingRight", "paddingTop", "paddingBottom", "size", "variant", "tag", "fontWeight", "capitalize", "LocalePickerAction", "document", "meta", "model", "collectionType", "documentId", "query", "<PERSON><PERSON><PERSON><PERSON>", "useQueryParams", "hasI18n", "canCreate", "canRead", "useI18n", "data", "locales", "useGetLocalesQuery", "currentDesiredLocale", "plugins", "i18n", "schema", "useDocument", "params", "handleSelect", "useCallback", "value", "React", "useEffect", "Array", "isArray", "doesLocaleExist", "find", "loc", "code", "defaultLocale", "isDefault", "currentLocale", "undefined", "allCurrentLocales", "getDocumentStatus", "localizations", "length", "displayedLocales", "filter", "includes", "label", "options", "map", "entryWithLocaleExists", "some", "doc", "currentLocaleDoc", "permissionsToCheck", "disabled", "draftAndPublish", "startIcon", "Plus", "customizeContent", "onSelect", "doc<PERSON><PERSON>us", "statuses", "availableStatus", "publishedAt", "FillFromAnotherLocaleAction", "localeSelected", "setLocaleSelected", "useState", "set<PERSON><PERSON><PERSON>", "useForm", "state", "getDocument", "useDocumentActions", "components", "availableLocales", "l", "fillFromLocale", "onClose", "response", "cleanedData", "cleanData", "type", "icon", "Earth", "dialog", "title", "content", "_Fragment", "Dialog", "Body", "direction", "WarningCircle", "height", "fill", "textAlign", "Field", "Root", "Label", "SingleSelect", "placeholder", "onChange", "SingleSelectOption", "Footer", "<PERSON><PERSON>", "flex", "onClick", "DeleteLocaleAction", "navigate", "useNavigate", "toggleNotification", "useNotification", "delete", "deleteAction", "isLoading", "canDelete", "position", "StyledTrash", "loading", "onConfirm", "unableToDelete", "console", "error", "message", "res", "pathname", "replace", "BulkLocaleAction", "action", "useMemo", "buildValidParams", "isOnPublishedTab", "canPublish", "_unstableFormatAPIError", "formatAPIError", "useAPIErrorHandler", "selectedRows", "setSelectedRows", "isDraftRelationConfirmationOpen", "setIsDraftRelationConfirmationOpen", "publishMany", "publishManyAction", "unpublishMany", "unpublishManyAction", "validate", "skip", "localesMetadata", "skipToken", "headers", "rows", "validationErrors", "unshift", "allDocuments", "errors", "reduce", "errs", "validation", "isBulkPublish", "localesForAction", "acc", "selectedRow", "isValidLocale", "Object", "keys", "shouldAddLocale", "push", "enableDraftRelationsCount", "draftRelationsCount", "isDraftRelationsLoading", "isDraftRelationsError", "useGetManyDraftRelationCountQuery", "documentIds", "publish", "unpublish", "handleAction", "onCancel", "alignItems", "hasPermission", "every", "ListPlus", "Cross", "Table", "row", "onSelectedRowsChange", "tableSelectedRows", "BulkLocaleActionModal", "footer", "Modal", "BulkLocalePublishAction", "props", "BulkLocaleUnpublishAction", "styled", "Trash", "Emphasis", "chunks", "_jsx", "Typography", "fontWeight", "textColor", "DeleteModalAdditionalInfo", "hasI18n", "useI18n", "formatMessage", "useIntl", "id", "getTranslation", "defaultMessage", "em", "PublishModalAdditionalInfo", "UnpublishModalAdditionalInfo", "LocalePicker", "formatMessage", "useIntl", "query", "<PERSON><PERSON><PERSON><PERSON>", "useQueryParams", "hasI18n", "canRead", "canCreate", "useI18n", "data", "locales", "useGetLocalesQuery", "undefined", "skip", "handleChange", "useCallback", "code", "replace", "page", "plugins", "i18n", "locale", "React", "useEffect", "Array", "isArray", "currentDesiredLocale", "doesLocaleExist", "find", "loc", "defaultLocale", "isDefault", "length", "displayedLocales", "filter", "includes", "_jsx", "SingleSelect", "size", "aria-label", "id", "getTranslation", "defaultMessage", "value", "onChange", "map", "SingleSelectOption", "name", "mutateEditViewHook", "layout", "options", "i18n", "localized", "components", "Object", "entries", "reduce", "acc", "key", "componentLayout", "map", "row", "addLabelActionToField", "panel", "field", "isFieldLocalized", "doesFieldHaveI18nPluginOpt", "attribute", "pluginOptions", "labelActionProps", "title", "id", "getTranslation", "defaultMessage", "icon", "_jsx", "Earth", "labelAction", "LabelAction", "pluginOpts", "formatMessage", "useIntl", "_jsxs", "Span", "tag", "VisuallyHidden", "cloneElement", "focusable", "styled", "Flex", "theme", "colors", "neutral500", "LocaleListCell", "locale", "currentLocale", "localizations", "language", "useIntl", "data", "locales", "useGetLocalesQuery", "formatter", "useCollator", "sensitivity", "Array", "isArray", "availableLocales", "map", "loc", "localesForDocument", "reduce", "acc", "createdLocale", "find", "code", "push", "isDefault", "name", "toSorted", "a", "b", "compare", "_jsxs", "Popover", "Root", "_jsx", "<PERSON><PERSON>", "<PERSON><PERSON>", "variant", "type", "onClick", "e", "stopPropagation", "Flex", "min<PERSON><PERSON><PERSON>", "alignItems", "justifyContent", "fontWeight", "Typography", "textColor", "ellipsis", "marginRight", "join", "CaretDown", "width", "height", "Content", "sideOffset", "ul", "Box", "padding", "tag", "addColumnToTableHook", "displayedHeaders", "layout", "options", "isFieldLocalized", "doesPluginOptionsHaveI18nLocalized", "i18n", "localized", "attribute", "type", "label", "id", "getTranslation", "defaultMessage", "searchable", "sortable", "name", "cell<PERSON>ormatt<PERSON>", "props", "_header", "meta", "_jsx", "LocaleListCell", "addLocaleToReleasesHook", "displayedHeaders", "label", "id", "defaultMessage", "name", "hasI18nEnabled", "extendCTBAttributeInitialDataMiddleware", "getState", "next", "action", "enhanceAction", "uid", "store", "type", "get", "modelType", "hasi18nEnabled", "payload", "options", "pluginOptions", "i18n", "localized", "err", "includes", "attributeType", "isEditing", "extendCTBInitialDataMiddleware", "next", "action", "type", "modalType", "i18n", "localized", "pluginOptions", "data", "actionType", "localeMiddleware", "ctx", "next", "permissions", "match", "matchPath", "pathname", "search", "parse", "plugins", "i18n", "Array", "isArray", "locale", "revisedPermissions", "filter", "permission", "properties", "locales", "includes", "prefixPluginTranslations", "trad", "pluginId", "Object", "keys", "reduce", "acc", "current", "mutateCTBContentTypeSchema", "nextSchema", "prevSchema", "doesPluginOptionsHaveI18nLocalized", "pluginOptions", "isNextSchemaLocalized", "i18n", "localized", "isPrevSchemaLocalized", "attributes", "addLocalisationToFields", "omit", "disableAttributesLocalisation", "map", "currentAttribute", "LOCALIZED_FIELDS", "includes", "type", "index", "register", "app", "addMiddlewares", "extendCTBAttributeInitialDataMiddleware", "extendCTBInitialDataMiddleware", "i18nApi", "middleware", "addReducers", "reducerPath", "reducer", "addRBACMiddleware", "localeMiddleware", "registerPlugin", "id", "pluginId", "name", "bootstrap", "registerHook", "addColumnToTableHook", "mutateEditViewHook", "addLocaleToReleasesHook", "addSettingsLink", "intlLabel", "getTranslation", "defaultMessage", "to", "Component", "then", "mod", "default", "ProtectedSettingsPage", "permissions", "PERMISSIONS", "accessMain", "contentManager", "getPlugin", "apis", "addDocumentHeaderAction", "LocalePickerAction", "FillFromAnotherLocaleAction", "addDocumentAction", "actions", "indexOfDeleteAction", "findIndex", "action", "type", "splice", "DeleteLocaleAction", "BulkLocalePublishAction", "BulkLocaleUnpublishAction", "injectComponent", "LocalePicker", "PublishModalAdditionalInfo", "UnpublishModalAdditionalInfo", "DeleteModalAdditionalInfo", "ctbPlugin", "ctbFormsAPI", "forms", "addContentTypeSchemaMutation", "mutateCTBContentTypeSchema", "components", "add", "component", "CheckboxConfirmation", "extendContentType", "validator", "i18n", "object", "shape", "localized", "bool", "form", "advanced", "description", "extendFields", "LOCALIZED_FIELDS", "contentTypeSchema", "for<PERSON><PERSON><PERSON>", "step", "hasI18nEnabled", "get", "registerTrads", "locales", "importedTrads", "Promise", "all", "map", "locale", "__variableDynamicImportRuntime1__", "data", "prefixPluginTranslations", "catch", "resolve", "RELEASE_ACTION_FORM_SCHEMA", "object", "shape", "type", "string", "oneOf", "required", "releaseId", "INITIAL_VALUES", "NoReleases", "formatMessage", "useIntl", "_jsx", "EmptyStateLayout", "icon", "EmptyDocuments", "width", "content", "id", "defaultMessage", "action", "LinkButton", "to", "pathname", "tag", "ReactRouterLink", "variant", "shadow", "AddActionToReleaseModal", "contentType", "documentId", "onInputChange", "values", "query", "useQueryParams", "locale", "plugins", "i18n", "response", "useGetReleasesForEntryQuery", "entryDocumentId", "has<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "releases", "data", "length", "_jsxs", "Flex", "direction", "alignItems", "gap", "Box", "paddingBottom", "Field", "Root", "Label", "SingleSelect", "placeholder", "name", "onChange", "value", "map", "release", "SingleSelectOption", "ReleaseActionOptions", "selected", "handleChange", "e", "target", "ReleaseActionModalForm", "document", "model", "collectionType", "allowedActions", "useRBAC", "PERMISSIONS", "canCreateAction", "createReleaseAction", "isLoading", "useCreateReleaseActionMutation", "toggleNotification", "useNotification", "formatAPIError", "useAPIErrorHandler", "handleSubmit", "onClose", "formik", "error", "isFetchError", "message", "useFormik", "initialValues", "validationSchema", "onSubmit", "Error", "body", "params", "edit", "options", "useDocumentLayout", "window", "strapi", "isEE", "draftAndPublish", "label", "PaperPlane", "disabled", "position", "dialog", "title", "setFieldValue", "footer", "Modal", "Footer", "<PERSON><PERSON>", "onClick", "loading", "getContentPermissions", "subject", "permissions", "publish", "action", "id", "actionParameters", "properties", "conditions", "ReleaseAction", "documents", "model", "formatMessage", "useIntl", "toggleNotification", "useNotification", "formatAPIError", "useAPIErrorHandler", "query", "useQueryParams", "contentPermissions", "allowedActions", "canPublish", "useRBAC", "canCreate", "releasePermissions", "hasDraftAndPublish", "useContentManagerContext", "response", "useGetReleasesQuery", "releases", "data", "createManyReleaseActions", "isLoading", "useCreateManyReleaseActionsMutation", "documentIds", "map", "doc", "documentId", "handleSubmit", "values", "locale", "plugins", "i18n", "releaseActionEntries", "entryDocumentId", "type", "contentType", "body", "params", "releaseId", "notificationMessage", "defaultMessage", "entriesAlreadyInRelease", "meta", "totalEntries", "notification", "title", "message", "isFetchError", "error", "actionType", "variant", "label", "dialog", "content", "onClose", "_jsx", "<PERSON><PERSON>", "onSubmit", "validationSchema", "RELEASE_ACTION_FORM_SCHEMA", "initialValues", "INITIAL_VALUES", "setFieldValue", "_jsxs", "Form", "length", "NoReleases", "Modal", "Body", "Flex", "direction", "alignItems", "gap", "Box", "paddingBottom", "Field", "Root", "required", "Label", "SingleSelect", "placeholder", "onChange", "value", "release", "SingleSelectOption", "name", "ReleaseActionOptions", "selected", "handleChange", "e", "target", "Footer", "<PERSON><PERSON>", "onClick", "disabled", "loading", "useReleasesList", "contentTypeUid", "documentId", "listViewData", "useTable", "state", "rows", "documentIds", "map", "entry", "query", "useQueryParams", "locale", "plugins", "i18n", "undefined", "response", "useGetMappedEntriesInReleasesQuery", "skip", "length", "mappedEntriesInReleases", "data", "addColumnToTableHook", "displayedHeaders", "layout", "options", "draftAndPublish", "searchable", "sortable", "name", "label", "id", "defaultMessage", "cell<PERSON>ormatt<PERSON>", "props", "_", "model", "_jsx", "ReleaseListCell", "releases", "formatMessage", "useIntl", "_jsxs", "Popover", "Root", "<PERSON><PERSON>", "<PERSON><PERSON>", "variant", "onClick", "e", "stopPropagation", "endIcon", "CaretDown", "width", "height", "Typography", "style", "max<PERSON><PERSON><PERSON>", "cursor", "textColor", "fontWeight", "number", "Content", "ul", "Box", "padding", "tag", "Link", "href", "isExternal", "Panel", "model", "document", "documentId", "collectionType", "query", "useQueryParams", "locale", "plugins", "i18n", "edit", "options", "useDocumentLayout", "formatMessage", "formatDate", "formatTime", "useIntl", "allowedActions", "useRBAC", "PERMISSIONS", "canRead", "canDeleteAction", "response", "useGetReleasesForEntryQuery", "contentType", "entryDocumentId", "has<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skip", "releases", "data", "getReleaseColorVariant", "actionType", "shade", "window", "strapi", "isEE", "draftAndPublish", "length", "title", "id", "defaultMessage", "content", "_jsx", "Flex", "direction", "alignItems", "gap", "width", "map", "release", "_jsxs", "borderWidth", "borderStyle", "borderColor", "actions", "type", "overflow", "hasRadius", "Box", "paddingTop", "paddingBottom", "paddingLeft", "paddingRight", "background", "Typography", "fontSize", "variant", "textColor", "isPublish", "padding", "fontWeight", "name", "scheduledAt", "timezone", "date", "Date", "day", "month", "year", "timeZone", "time", "hourCycle", "offset", "getTimezoneOffset", "ReleaseActionMenu", "Root", "hasTriggerBorder", "EditReleaseItem", "releaseId", "DeleteReleaseActionItem", "actionId", "prefixPluginTranslations", "trad", "pluginId", "Object", "keys", "reduce", "acc", "current", "admin", "register", "app", "createHook", "window", "strapi", "features", "isEnabled", "addMenuLink", "to", "pluginId", "icon", "PaperPlane", "intlLabel", "id", "defaultMessage", "Component", "then", "mod", "default", "App", "permissions", "PERMISSIONS", "main", "position", "contentManagerPluginApis", "getPlugin", "apis", "addEditViewSidePanel", "ReleasesPanel", "addDocumentAction", "actions", "indexOfDeleteAction", "findIndex", "action", "type", "splice", "ReleaseActionModalForm", "addSettingsLink", "ProtectedReleasesSettingsPage", "addBulkAction", "deleteActionIndex", "ReleaseAction", "registerHook", "addColumnToTableHook", "flags", "promoteEE", "PurchaseContentReleases", "licenseOnly", "registerTrads", "locales", "importedTrads", "Promise", "all", "map", "locale", "data", "prefixPluginTranslations", "catch", "resolve", "getDisplayName", "firstname", "lastname", "username", "email", "trim", "STAGE_ATTRIBUTE_NAME", "ASSIGNEE_ATTRIBUTE_NAME", "AssigneeSelect", "isCompact", "collectionType", "id", "slug", "model", "useParams", "permissions", "useTypedSelector", "state", "admin_app", "formatMessage", "useIntl", "_unstableFormatAPIError", "formatAPIError", "useAPIErrorHandler", "toggleNotification", "useNotification", "allowedActions", "canRead", "isLoading", "isLoadingPermissions", "useRBAC", "settings", "users", "query", "useQueryParams", "params", "useMemo", "buildValidParams", "data", "isLoadingUsers", "isError", "useAdminUsers", "undefined", "skip", "document", "unstable_useDocument", "documentId", "currentAssignee", "ASSIGNEE_ATTRIBUTE_NAME", "updateAssignee", "error", "isMutating", "useUpdateAssigneeMutation", "handleChange", "assigneeId", "res", "parseInt", "type", "message", "defaultMessage", "isDisabled", "length", "assignee<PERSON><PERSON><PERSON>", "assigneeClear<PERSON>abel", "assigneePlaceholder", "_jsxs", "Field", "Root", "name", "_jsx", "VisuallyHidden", "Label", "Combobox", "<PERSON><PERSON><PERSON><PERSON>", "disabled", "value", "toString", "onChange", "onClear", "placeholder", "loading", "size", "map", "user", "ComboboxOption", "textValue", "getDisplayName", "Error", "WorkflowLimitModal", "open", "onOpenChange", "formatMessage", "useIntl", "_jsxs", "LimitsModal", "Root", "_jsx", "Title", "id", "defaultMessage", "Body", "StageLimitModal", "Select", "stages", "activeWorkflowStage", "isLoading", "props", "themeColorName", "getStageColorByHex", "color", "SingleSelect", "disabled", "length", "placeholder", "startIcon", "Flex", "tag", "height", "background", "borderColor", "undefined", "hasRadius", "shrink", "width", "marginRight", "customizeContent", "justifyContent", "alignItems", "Typography", "textColor", "ellipsis", "lineHeight", "name", "Loader", "small", "style", "display", "data-testid", "map", "SingleSelectOption", "value", "textValue", "StageSelect", "isCompact", "collectionType", "slug", "model", "useParams", "_unstableFormatAPIError", "formatAPIError", "useAPIErrorHandler", "toggleNotification", "useNotification", "query", "useQueryParams", "params", "useMemo", "buildValidParams", "document", "isLoadingDocument", "unstable_useDocument", "documentId", "skip", "data", "isLoadingStages", "useGetStagesQuery", "meta", "getFeature", "useLicenseLimits", "showLimitModal", "setShowLimitModal", "useState", "limits", "STAGE_ATTRIBUTE_NAME", "updateStage", "error", "useUpdateStageMutation", "handleChange", "stageId", "CHARGEBEE_WORKFLOW_ENTITLEMENT_NAME", "parseInt", "workflowCount", "CHARGEBEE_STAGES_PER_WORKFLOW_ENTITLEMENT_NAME", "res", "type", "message", "reviewStageLabel", "reviewStageHint", "_Fragment", "<PERSON><PERSON><PERSON>", "label", "Field", "VisuallyHidden", "Label", "size", "onChange", "hint", "Hint", "Error", "Header", "slug", "id", "collectionType", "useParams", "edit", "options", "useDocumentLayout", "window", "strapi", "isEE", "reviewWorkflows", "_jsxs", "Flex", "gap", "_jsx", "AssigneeSelect", "isCompact", "StageSelect", "type", "Panel", "slug", "id", "collectionType", "useParams", "edit", "options", "useDocumentLayout", "formatMessage", "useIntl", "window", "strapi", "isEE", "reviewWorkflows", "title", "defaultMessage", "content", "_jsxs", "Flex", "direction", "gap", "alignItems", "width", "_jsx", "AssigneeSelect", "StageSelect", "type", "StageColumn", "props", "color", "STAGE_COLOR_DEFAULT", "name", "strapi_stage", "themeColorName", "getStageColorByHex", "_jsxs", "Flex", "alignItems", "gap", "max<PERSON><PERSON><PERSON>", "_jsx", "Box", "height", "background", "borderColor", "undefined", "hasRadius", "shrink", "width", "Typography", "fontWeight", "textColor", "ellipsis", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "strapi_assignee", "user", "getDisplayName", "REVIEW_WORKFLOW_COLUMNS", "name", "STAGE_ATTRIBUTE_NAME", "attribute", "type", "relation", "target", "label", "id", "defaultMessage", "searchable", "sortable", "mainField", "cell<PERSON>ormatt<PERSON>", "props", "_jsx", "StageColumn", "ASSIGNEE_ATTRIBUTE_NAME", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addColumnToTableHook", "displayedHeaders", "layout", "options", "reviewWorkflows", "REVIEW_WORKFLOW_COLUMNS", "prefixPluginTranslations", "trad", "pluginId", "Object", "keys", "reduce", "acc", "current", "admin", "register", "app", "window", "strapi", "features", "isEnabled", "FEATURE_ID", "registerHook", "addColumnToTableHook", "contentManagerPluginApis", "getPlugin", "apis", "addEditViewSidePanel", "Panel", "addSettingsLink", "id", "PLUGIN_ID", "to", "intlLabel", "defaultMessage", "permissions", "Component", "Router", "default", "flags", "promoteEE", "licenseOnly", "PurchaseReviewWorkflows", "bootstrap", "injectComponent", "name", "Header", "registerTrads", "locales", "importedTrads", "Promise", "all", "map", "locale", "__variableDynamicImportRuntime2__", "then", "data", "prefixPluginTranslations", "catch", "resolve", "render", "mountNode", "plugins", "restArgs", "renderAdmin", "contentManager", "contentTypeBuilder", "email", "upload", "contentReleases", "i18n", "reviewWorkflows"]}