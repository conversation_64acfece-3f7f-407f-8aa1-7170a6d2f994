import {
  useField
} from "./chunk-VWIQ6S3Y.js";
import {
  CheckboxImpl,
  DatePicker,
  DateTimePicker,
  Field,
  JSONInput,
  NumberInput,
  SingleSelect,
  SingleSelectOption,
  TextInput,
  Textarea,
  TimePicker,
  Toggle,
  useComposedRefs,
  useIntl
} from "./chunk-4YYUDJZ2.js";
import {
  useLocation
} from "./chunk-S65ZWNEO.js";
import {
  ForwardRef$3B,
  ForwardRef$3D
} from "./chunk-QW5ZDDTU.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/admin/src/hooks/useFocusInputField.mjs
var import_react = __toESM(require_react(), 1);
var useFocusInputField = (name) => {
  const { search: searchString } = useLocation();
  const search = (0, import_react.useMemo)(() => new URLSearchParams(searchString), [
    searchString
  ]);
  const [field, setField] = (0, import_react.useState)(null);
  (0, import_react.useEffect)(() => {
    if (search.has("field") && search.get("field") === name && field) {
      field.focus();
      field.scrollIntoView({
        block: "center"
      });
    }
  }, [
    search,
    name,
    field
  ]);
  return setField;
};

// node_modules/@strapi/admin/dist/admin/admin/src/components/FormInputs/Renderer.mjs
var import_jsx_runtime13 = __toESM(require_jsx_runtime(), 1);
var import_react12 = __toESM(require_react(), 1);

// node_modules/@strapi/admin/dist/admin/admin/src/components/FormInputs/Boolean.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react2 = __toESM(require_react(), 1);
var BooleanInput = (0, import_react2.forwardRef)(({ name, required, label, hint, labelAction, ...props }, ref) => {
  const { formatMessage } = useIntl();
  const field = useField(name);
  const fieldRef = useFocusInputField(name);
  const composedRefs = useComposedRefs(ref, fieldRef);
  return (0, import_jsx_runtime.jsxs)(Field.Root, {
    error: field.error,
    name,
    hint,
    required,
    maxWidth: "320px",
    children: [
      (0, import_jsx_runtime.jsx)(Field.Label, {
        action: labelAction,
        children: label
      }),
      (0, import_jsx_runtime.jsx)(Toggle, {
        ref: composedRefs,
        checked: field.value === null ? null : field.value || false,
        offLabel: formatMessage({
          id: "app.components.ToggleCheckbox.off-label",
          defaultMessage: "False"
        }),
        onLabel: formatMessage({
          id: "app.components.ToggleCheckbox.on-label",
          defaultMessage: "True"
        }),
        onChange: field.onChange,
        ...props
      }),
      (0, import_jsx_runtime.jsx)(Field.Hint, {}),
      (0, import_jsx_runtime.jsx)(Field.Error, {})
    ]
  });
});
var MemoizedBooleanInput = (0, import_react2.memo)(BooleanInput);

// node_modules/@strapi/admin/dist/admin/admin/src/components/FormInputs/Checkbox.mjs
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_react3 = __toESM(require_react(), 1);
var CheckboxInput = (0, import_react3.forwardRef)(({ name, required, label, hint, type: _type, ...props }, ref) => {
  const field = useField(name);
  const fieldRef = useFocusInputField(name);
  const composedRefs = useComposedRefs(ref, fieldRef);
  return (0, import_jsx_runtime2.jsxs)(Field.Root, {
    error: field.error,
    name,
    hint,
    required,
    children: [
      (0, import_jsx_runtime2.jsx)(CheckboxImpl, {
        onCheckedChange: (checked) => field.onChange(name, !!checked),
        ref: composedRefs,
        checked: field.value,
        ...props,
        children: label || props["aria-label"]
      }),
      (0, import_jsx_runtime2.jsx)(Field.Hint, {}),
      (0, import_jsx_runtime2.jsx)(Field.Error, {})
    ]
  });
});
var MemoizedCheckboxInput = (0, import_react3.memo)(CheckboxInput);

// node_modules/@strapi/admin/dist/admin/admin/src/components/FormInputs/Date.mjs
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var DateInput = React.forwardRef(({ name, required, label, hint, labelAction, type: _type, ...props }, ref) => {
  const { formatMessage } = useIntl();
  const field = useField(name);
  const fieldRef = useFocusInputField(name);
  const composedRefs = useComposedRefs(ref, fieldRef);
  const [lastValidDate, setLastValidDate] = React.useState(null);
  const value = typeof field.value === "string" ? new Date(field.value) : field.value;
  const handleDateChange = (date) => {
    if (!date) {
      field.onChange(name, null);
      setLastValidDate(null);
      return;
    }
    const utcDate = toUTCMidnight(date);
    field.onChange(name, utcDate.toISOString());
    setLastValidDate(utcDate);
  };
  return (0, import_jsx_runtime3.jsxs)(Field.Root, {
    error: field.error,
    name,
    hint,
    required,
    children: [
      (0, import_jsx_runtime3.jsx)(Field.Label, {
        action: labelAction,
        children: label
      }),
      (0, import_jsx_runtime3.jsx)(DatePicker, {
        ref: composedRefs,
        clearLabel: formatMessage({
          id: "clearLabel",
          defaultMessage: "Clear"
        }),
        onChange: handleDateChange,
        onClear: () => {
          field.onChange(name, null);
          setLastValidDate(null);
          return;
        },
        onBlur: () => {
          if (field.value && !value) {
            field.onChange(name, (lastValidDate == null ? void 0 : lastValidDate.toISOString()) ?? null);
          }
        },
        value,
        ...props
      }),
      (0, import_jsx_runtime3.jsx)(Field.Hint, {}),
      (0, import_jsx_runtime3.jsx)(Field.Error, {})
    ]
  });
});
var toUTCMidnight = (date) => {
  return new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
};
var MemoizedDateInput = React.memo(DateInput);

// node_modules/@strapi/admin/dist/admin/admin/src/components/FormInputs/DateTime.mjs
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var import_react4 = __toESM(require_react(), 1);
var DateTimeInput = (0, import_react4.forwardRef)(({ name, required, label, hint, labelAction, ...props }, ref) => {
  const { formatMessage } = useIntl();
  const field = useField(name);
  const fieldRef = useFocusInputField(name);
  const composedRefs = useComposedRefs(ref, fieldRef);
  const value = typeof field.value === "string" ? new Date(field.value) : field.value;
  return (0, import_jsx_runtime4.jsxs)(Field.Root, {
    error: field.error,
    name,
    hint,
    required,
    children: [
      (0, import_jsx_runtime4.jsx)(Field.Label, {
        action: labelAction,
        children: label
      }),
      (0, import_jsx_runtime4.jsx)(DateTimePicker, {
        ref: composedRefs,
        clearLabel: formatMessage({
          id: "clearLabel",
          defaultMessage: "Clear"
        }),
        onChange: (date) => {
          field.onChange(name, date ? date.toISOString() : null);
        },
        onClear: () => field.onChange(name, null),
        value,
        ...props
      }),
      (0, import_jsx_runtime4.jsx)(Field.Hint, {}),
      (0, import_jsx_runtime4.jsx)(Field.Error, {})
    ]
  });
});
var MemoizedDateTimeInput = (0, import_react4.memo)(DateTimeInput);

// node_modules/@strapi/admin/dist/admin/admin/src/components/FormInputs/Email.mjs
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
var import_react5 = __toESM(require_react(), 1);
var EmailInput = (0, import_react5.forwardRef)(({ name, required, label, hint, labelAction, ...props }, ref) => {
  const field = useField(name);
  const fieldRef = useFocusInputField(name);
  const composedRefs = useComposedRefs(ref, fieldRef);
  return (0, import_jsx_runtime5.jsxs)(Field.Root, {
    error: field.error,
    name,
    hint,
    required,
    children: [
      (0, import_jsx_runtime5.jsx)(Field.Label, {
        action: labelAction,
        children: label
      }),
      (0, import_jsx_runtime5.jsx)(TextInput, {
        ref: composedRefs,
        autoComplete: "email",
        onChange: field.onChange,
        value: field.value,
        ...props,
        type: "email"
      }),
      (0, import_jsx_runtime5.jsx)(Field.Hint, {}),
      (0, import_jsx_runtime5.jsx)(Field.Error, {})
    ]
  });
});
var MemoizedEmailInput = (0, import_react5.memo)(EmailInput);

// node_modules/@strapi/admin/dist/admin/admin/src/components/FormInputs/Enumeration.mjs
var import_jsx_runtime6 = __toESM(require_jsx_runtime(), 1);
var import_react6 = __toESM(require_react(), 1);
var EnumerationInput = (0, import_react6.forwardRef)(({ name, required, label, hint, labelAction, options = [], ...props }, ref) => {
  const { formatMessage } = useIntl();
  const field = useField(name);
  const fieldRef = useFocusInputField(name);
  const composedRefs = useComposedRefs(ref, fieldRef);
  return (0, import_jsx_runtime6.jsxs)(Field.Root, {
    error: field.error,
    name,
    hint,
    required,
    children: [
      (0, import_jsx_runtime6.jsx)(Field.Label, {
        action: labelAction,
        children: label
      }),
      (0, import_jsx_runtime6.jsxs)(SingleSelect, {
        ref: composedRefs,
        onChange: (value) => {
          field.onChange(name, value);
        },
        value: field.value,
        ...props,
        children: [
          (0, import_jsx_runtime6.jsx)(SingleSelectOption, {
            value: "",
            disabled: required,
            hidden: required,
            children: formatMessage({
              id: "components.InputSelect.option.placeholder",
              defaultMessage: "Choose here"
            })
          }),
          options.map(({ value, label: label2, disabled, hidden }) => {
            return (0, import_jsx_runtime6.jsx)(SingleSelectOption, {
              value,
              disabled,
              hidden,
              children: label2 ?? value
            }, value);
          })
        ]
      }),
      (0, import_jsx_runtime6.jsx)(Field.Hint, {}),
      (0, import_jsx_runtime6.jsx)(Field.Error, {})
    ]
  });
});
var MemoizedEnumerationInput = (0, import_react6.memo)(EnumerationInput);

// node_modules/@strapi/admin/dist/admin/admin/src/components/FormInputs/Json.mjs
var import_jsx_runtime7 = __toESM(require_jsx_runtime(), 1);
var React2 = __toESM(require_react(), 1);
var JsonInput = React2.forwardRef(({ name, required, label, hint, labelAction, ...props }, ref) => {
  const field = useField(name);
  const fieldRef = useFocusInputField(name);
  const composedRefs = useComposedRefs(ref, fieldRef);
  return (0, import_jsx_runtime7.jsxs)(Field.Root, {
    error: field.error,
    name,
    hint,
    required,
    children: [
      (0, import_jsx_runtime7.jsx)(Field.Label, {
        action: labelAction,
        children: label
      }),
      (0, import_jsx_runtime7.jsx)(JSONInput, {
        ref: composedRefs,
        value: typeof field.value == "object" ? JSON.stringify(field.value, null, 2) : field.value,
        onChange: (json) => {
          const value = required && !json.length ? null : json;
          field.onChange(name, value);
        },
        minHeight: `25.2rem`,
        maxHeight: `50.4rem`,
        ...props
      }),
      (0, import_jsx_runtime7.jsx)(Field.Hint, {}),
      (0, import_jsx_runtime7.jsx)(Field.Error, {})
    ]
  });
});
var MemoizedJsonInput = React2.memo(JsonInput);

// node_modules/@strapi/admin/dist/admin/admin/src/components/FormInputs/Number.mjs
var import_jsx_runtime8 = __toESM(require_jsx_runtime(), 1);
var import_react7 = __toESM(require_react(), 1);
var NumberInputImpl = (0, import_react7.forwardRef)(({ name, required, label, hint, labelAction, type, ...props }, ref) => {
  const field = useField(name);
  const fieldRef = useFocusInputField(name);
  const composedRefs = useComposedRefs(ref, fieldRef);
  return (0, import_jsx_runtime8.jsxs)(Field.Root, {
    error: field.error,
    name,
    hint,
    required,
    children: [
      (0, import_jsx_runtime8.jsx)(Field.Label, {
        action: labelAction,
        children: label
      }),
      (0, import_jsx_runtime8.jsx)(NumberInput, {
        ref: composedRefs,
        onValueChange: (value) => {
          field.onChange(name, value ?? null);
        },
        step: type === "float" || type == "decimal" ? 0.01 : 1,
        value: field.value ?? void 0,
        ...props
      }),
      (0, import_jsx_runtime8.jsx)(Field.Hint, {}),
      (0, import_jsx_runtime8.jsx)(Field.Error, {})
    ]
  });
});
var MemoizedNumberInput = (0, import_react7.memo)(NumberInputImpl);

// node_modules/@strapi/admin/dist/admin/admin/src/components/FormInputs/Password.mjs
var import_jsx_runtime9 = __toESM(require_jsx_runtime(), 1);
var import_react8 = __toESM(require_react(), 1);
var PasswordInput = (0, import_react8.forwardRef)(({ name, required, label, hint, labelAction, ...props }, ref) => {
  const [showPassword, setShowPassword] = (0, import_react8.useState)(false);
  const { formatMessage } = useIntl();
  const field = useField(name);
  const fieldRef = useFocusInputField(name);
  const composedRefs = useComposedRefs(ref, fieldRef);
  return (0, import_jsx_runtime9.jsxs)(Field.Root, {
    error: field.error,
    name,
    hint,
    required,
    children: [
      (0, import_jsx_runtime9.jsx)(Field.Label, {
        action: labelAction,
        children: label
      }),
      (0, import_jsx_runtime9.jsx)(TextInput, {
        ref: composedRefs,
        autoComplete: "password",
        endAction: (0, import_jsx_runtime9.jsx)(Field.Action, {
          label: formatMessage({
            id: "Auth.form.password.show-password",
            defaultMessage: "Show password"
          }),
          onClick: () => {
            setShowPassword((prev) => !prev);
          },
          children: showPassword ? (0, import_jsx_runtime9.jsx)(ForwardRef$3D, {
            fill: "neutral500"
          }) : (0, import_jsx_runtime9.jsx)(ForwardRef$3B, {
            fill: "neutral500"
          })
        }),
        onChange: field.onChange,
        value: field.value,
        ...props,
        type: showPassword ? "text" : "password"
      }),
      (0, import_jsx_runtime9.jsx)(Field.Hint, {}),
      (0, import_jsx_runtime9.jsx)(Field.Error, {})
    ]
  });
});
var MemoizedPasswordInput = (0, import_react8.memo)(PasswordInput);

// node_modules/@strapi/admin/dist/admin/admin/src/components/FormInputs/String.mjs
var import_jsx_runtime10 = __toESM(require_jsx_runtime(), 1);
var import_react9 = __toESM(require_react(), 1);
var StringInput = (0, import_react9.forwardRef)(({ name, required, label, hint, labelAction, ...props }, ref) => {
  const field = useField(name);
  const fieldRef = useFocusInputField(name);
  const composedRefs = useComposedRefs(ref, fieldRef);
  return (0, import_jsx_runtime10.jsxs)(Field.Root, {
    error: field.error,
    name,
    hint,
    required,
    children: [
      (0, import_jsx_runtime10.jsx)(Field.Label, {
        action: labelAction,
        children: label
      }),
      (0, import_jsx_runtime10.jsx)(TextInput, {
        ref: composedRefs,
        onChange: field.onChange,
        value: field.value ?? "",
        ...props
      }),
      (0, import_jsx_runtime10.jsx)(Field.Hint, {}),
      (0, import_jsx_runtime10.jsx)(Field.Error, {})
    ]
  });
});
var MemoizedStringInput = (0, import_react9.memo)(StringInput);

// node_modules/@strapi/admin/dist/admin/admin/src/components/FormInputs/Textarea.mjs
var import_jsx_runtime11 = __toESM(require_jsx_runtime(), 1);
var import_react10 = __toESM(require_react(), 1);
var TextareaInput = (0, import_react10.forwardRef)(({ name, required, label, hint, labelAction, ...props }, ref) => {
  const field = useField(name);
  const fieldRef = useFocusInputField(name);
  const composedRefs = useComposedRefs(ref, fieldRef);
  return (0, import_jsx_runtime11.jsxs)(Field.Root, {
    error: field.error,
    name,
    hint,
    required,
    children: [
      (0, import_jsx_runtime11.jsx)(Field.Label, {
        action: labelAction,
        children: label
      }),
      (0, import_jsx_runtime11.jsx)(Textarea, {
        ref: composedRefs,
        onChange: field.onChange,
        value: field.value ?? "",
        ...props
      }),
      (0, import_jsx_runtime11.jsx)(Field.Hint, {}),
      (0, import_jsx_runtime11.jsx)(Field.Error, {})
    ]
  });
});
var MemoizedTextareaInput = (0, import_react10.memo)(TextareaInput);

// node_modules/@strapi/admin/dist/admin/admin/src/components/FormInputs/Time.mjs
var import_jsx_runtime12 = __toESM(require_jsx_runtime(), 1);
var import_react11 = __toESM(require_react(), 1);
var TimeInput = (0, import_react11.forwardRef)(({ name, required, label, hint, labelAction, ...props }, ref) => {
  const { formatMessage } = useIntl();
  const field = useField(name);
  const fieldRef = useFocusInputField(name);
  const composedRefs = useComposedRefs(ref, fieldRef);
  return (0, import_jsx_runtime12.jsxs)(Field.Root, {
    error: field.error,
    name,
    hint,
    required,
    children: [
      (0, import_jsx_runtime12.jsx)(Field.Label, {
        action: labelAction,
        children: label
      }),
      (0, import_jsx_runtime12.jsx)(TimePicker, {
        ref: composedRefs,
        clearLabel: formatMessage({
          id: "clearLabel",
          defaultMessage: "Clear"
        }),
        onChange: (time) => {
          field.onChange(name, `${time}:00.000`);
        },
        onClear: () => field.onChange(name, void 0),
        value: field.value ?? "",
        ...props
      }),
      (0, import_jsx_runtime12.jsx)(Field.Hint, {}),
      (0, import_jsx_runtime12.jsx)(Field.Error, {})
    ]
  });
});
var MemoizedTimeInput = (0, import_react11.memo)(TimeInput);

// node_modules/@strapi/admin/dist/admin/admin/src/components/FormInputs/Renderer.mjs
var InputRenderer = (0, import_react12.memo)((0, import_react12.forwardRef)((props, forwardRef14) => {
  switch (props.type) {
    case "biginteger":
    case "timestamp":
    case "string":
    case "uid":
      return (0, import_jsx_runtime13.jsx)(MemoizedStringInput, {
        ref: forwardRef14,
        ...props
      });
    case "boolean":
      return (0, import_jsx_runtime13.jsx)(MemoizedBooleanInput, {
        ref: forwardRef14,
        ...props
      });
    case "checkbox":
      return (0, import_jsx_runtime13.jsx)(MemoizedCheckboxInput, {
        ref: forwardRef14,
        ...props
      });
    case "datetime":
      return (0, import_jsx_runtime13.jsx)(MemoizedDateTimeInput, {
        ref: forwardRef14,
        ...props
      });
    case "date":
      return (0, import_jsx_runtime13.jsx)(MemoizedDateInput, {
        ref: forwardRef14,
        ...props
      });
    case "decimal":
    case "float":
    case "integer":
      return (0, import_jsx_runtime13.jsx)(MemoizedNumberInput, {
        ref: forwardRef14,
        ...props
      });
    case "json":
      return (0, import_jsx_runtime13.jsx)(MemoizedJsonInput, {
        ref: forwardRef14,
        ...props
      });
    case "email":
      return (0, import_jsx_runtime13.jsx)(MemoizedEmailInput, {
        ref: forwardRef14,
        ...props
      });
    case "enumeration":
      return (0, import_jsx_runtime13.jsx)(MemoizedEnumerationInput, {
        ref: forwardRef14,
        ...props
      });
    case "password":
      return (0, import_jsx_runtime13.jsx)(MemoizedPasswordInput, {
        ref: forwardRef14,
        ...props
      });
    case "text":
      return (0, import_jsx_runtime13.jsx)(MemoizedTextareaInput, {
        ref: forwardRef14,
        ...props
      });
    case "time":
      return (0, import_jsx_runtime13.jsx)(MemoizedTimeInput, {
        ref: forwardRef14,
        ...props
      });
    default:
      return (0, import_jsx_runtime13.jsx)(NotSupportedField, {
        ref: forwardRef14,
        ...props
      });
  }
}));
var NotSupportedField = (0, import_react12.forwardRef)(({ label, hint, name, required, type, labelAction }, ref) => {
  const { error } = useField(name);
  const fieldRef = useFocusInputField(name);
  const composedRefs = useComposedRefs(ref, fieldRef);
  return (0, import_jsx_runtime13.jsxs)(Field.Root, {
    error,
    name,
    hint,
    required,
    children: [
      (0, import_jsx_runtime13.jsx)(Field.Label, {
        action: labelAction,
        children: label
      }),
      (0, import_jsx_runtime13.jsx)(TextInput, {
        ref: composedRefs,
        disabled: true,
        placeholder: `Unsupported field type: ${type}`,
        required,
        type: "text",
        value: ""
      }),
      (0, import_jsx_runtime13.jsx)(Field.Hint, {}),
      (0, import_jsx_runtime13.jsx)(Field.Error, {})
    ]
  });
});
var MemoizedInputRenderer = (0, import_react12.memo)(InputRenderer);

export {
  useFocusInputField,
  MemoizedStringInput,
  MemoizedInputRenderer
};
//# sourceMappingURL=chunk-QQ4VIATE.js.map
