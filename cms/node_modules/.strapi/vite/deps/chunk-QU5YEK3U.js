import {
  useTracking
} from "./chunk-USP7YHPW.js";
import {
  useInitQuery,
  useProjectSettingsQuery,
  useUpdateProjectSettingsMutation
} from "./chunk-LLJ4ZRUJ.js";
import {
  useRBAC
} from "./chunk-EYSFNMMY.js";
import {
  Page,
  useAPIErrorHandler
} from "./chunk-3UVBSCPH.js";
import {
  useAuth
} from "./chunk-JXTOCMQW.js";
import {
  useTypedSelector
} from "./chunk-B33LMLYV.js";
import {
  useNotification
} from "./chunk-4EY5FDQG.js";
import {
  $c512c27ab02ef895$export$fd42f52fd3ae1109,
  useIntl
} from "./chunk-4YYUDJZ2.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/admin/src/features/Configuration.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var [ConfigurationContextProvider, useConfiguration] = $c512c27ab02ef895$export$fd42f52fd3ae1109("ConfigurationContext");
var ConfigurationProvider = ({ children, defaultAuthLogo, defaultMenuLogo, showReleaseNotification = false }) => {
  const { trackUsage } = useTracking();
  const { formatMessage } = useIntl();
  const { toggleNotification } = useNotification();
  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();
  const permissions = useTypedSelector((state) => {
    var _a;
    return (_a = state.admin_app.permissions.settings) == null ? void 0 : _a["project-settings"];
  });
  const token = useAuth("ConfigurationProvider", (state) => state.token);
  const { allowedActions: { canRead } } = useRBAC(permissions);
  const { data: { authLogo: customAuthLogo, menuLogo: customMenuLogo } = {}, error, isLoading } = useInitQuery();
  React.useEffect(() => {
    if (error) {
      toggleNotification({
        type: "danger",
        message: formatMessage({
          id: "app.containers.App.notification.error.init"
        })
      });
    }
  }, [
    error,
    formatMessage,
    toggleNotification
  ]);
  const { data, isSuccess } = useProjectSettingsQuery(void 0, {
    skip: !token || !canRead
  });
  const [updateProjectSettingsMutation] = useUpdateProjectSettingsMutation();
  const updateProjectSettings = React.useCallback(async (body) => {
    var _a, _b;
    const formData = new FormData();
    Object.entries(body).forEach(([key, value]) => {
      if (value == null ? void 0 : value.rawFile) {
        formData.append(key, value.rawFile);
      } else if (value === null) {
        formData.append(key, JSON.stringify(value));
      }
    });
    const res = await updateProjectSettingsMutation(formData);
    if ("data" in res) {
      const updatedMenuLogo = !!res.data.menuLogo && !!((_a = body.menuLogo) == null ? void 0 : _a.rawFile);
      const updatedAuthLogo = !!res.data.authLogo && !!((_b = body.authLogo) == null ? void 0 : _b.rawFile);
      if (updatedMenuLogo) {
        trackUsage("didChangeLogo", {
          logo: "menu"
        });
      }
      if (updatedAuthLogo) {
        trackUsage("didChangeLogo", {
          logo: "auth"
        });
      }
      toggleNotification({
        type: "success",
        message: formatMessage({
          id: "app",
          defaultMessage: "Saved"
        })
      });
    } else {
      toggleNotification({
        type: "danger",
        message: formatAPIError(res.error)
      });
    }
  }, [
    formatAPIError,
    formatMessage,
    toggleNotification,
    trackUsage,
    updateProjectSettingsMutation
  ]);
  if (isLoading) {
    return (0, import_jsx_runtime.jsx)(Page.Loading, {});
  }
  return (0, import_jsx_runtime.jsx)(ConfigurationContextProvider, {
    showReleaseNotification,
    logos: {
      menu: {
        custom: isSuccess ? data == null ? void 0 : data.menuLogo : {
          url: customMenuLogo ?? ""
        },
        default: defaultMenuLogo
      },
      auth: {
        custom: isSuccess ? data == null ? void 0 : data.authLogo : {
          url: customAuthLogo ?? ""
        },
        default: defaultAuthLogo
      }
    },
    updateProjectSettings,
    children
  });
};

export {
  useConfiguration,
  ConfigurationProvider
};
//# sourceMappingURL=chunk-QU5YEK3U.js.map
