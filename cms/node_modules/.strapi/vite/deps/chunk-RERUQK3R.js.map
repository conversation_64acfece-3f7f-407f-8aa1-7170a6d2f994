{"version": 3, "sources": ["../../../@strapi/admin/dist/admin/admin/src/translations/en.json.mjs"], "sourcesContent": ["var Analytics = \"Analytics\";\nvar Documentation = \"Documentation\";\nvar Email = \"Email\";\nvar Password = \"Password\";\nvar Provider = \"Provider\";\nvar ResetPasswordToken = \"Reset Password Token\";\nvar Role = \"Role\";\nvar Username = \"Username\";\nvar Users = \"Users\";\nvar anErrorOccurred = \"Whoops! Something went wrong. Please, try again.\";\nvar noPreview = \"No preview available\";\nvar clearLabel = \"Clear\";\nvar dark = \"Dark\";\nvar light = \"Light\";\nvar or = \"OR\";\nvar selectButtonTitle = \"Select\";\nvar skipToContent = \"Skip to content\";\nvar submit = \"Submit\";\nvar en = {\n    Analytics: Analytics,\n    \"Auth.components.Oops.text\": \"Your account has been suspended.\",\n    \"Auth.components.Oops.text.admin\": \"If this is a mistake, please contact your administrator.\",\n    \"Auth.components.Oops.title\": \"Oops...\",\n    \"Auth.form.active.label\": \"Active\",\n    \"Auth.form.button.forgot-password\": \"Send Email\",\n    \"Auth.form.button.go-home\": \"GO BACK HOME\",\n    \"Auth.form.button.login\": \"Login\",\n    \"Auth.form.button.login.providers.error\": \"We cannot connect you through the selected provider.\",\n    \"Auth.form.button.login.strapi\": \"Log in via Strapi\",\n    \"Auth.form.button.password-recovery\": \"Password Recovery\",\n    \"Auth.form.button.register\": \"Let's start\",\n    \"Auth.form.confirmPassword.label\": \"Confirm Password\",\n    \"Auth.form.currentPassword.label\": \"Current Password\",\n    \"Auth.form.email.label\": \"Email\",\n    \"Auth.form.email.placeholder\": \"e.g. <EMAIL>\",\n    \"Auth.form.error.blocked\": \"Your account has been blocked by the administrator.\",\n    \"Auth.form.error.code.provide\": \"Incorrect code provided.\",\n    \"Auth.form.error.confirmed\": \"Your account email is not confirmed.\",\n    \"Auth.form.error.email.invalid\": \"This email is invalid.\",\n    \"Auth.form.error.email.provide\": \"Please provide your username or your email.\",\n    \"Auth.form.error.email.taken\": \"Email is already taken.\",\n    \"Auth.form.error.invalid\": \"Identifier or password invalid.\",\n    \"Auth.form.error.params.provide\": \"Incorrect params provided.\",\n    \"Auth.form.error.password.format\": \"Your password cannot contain the symbol `$` more than three times.\",\n    \"Auth.form.error.password.local\": \"This user never set a local password, please login via the provider used during account creation.\",\n    \"Auth.form.error.password.matching\": \"Passwords do not match.\",\n    \"Auth.form.error.password.provide\": \"Please provide your password.\",\n    \"Auth.form.error.ratelimit\": \"Too many attempts, please try again in a minute.\",\n    \"Auth.form.error.user.not-exist\": \"This email does not exist.\",\n    \"Auth.form.error.username.taken\": \"Username is already taken.\",\n    \"Auth.form.firstname.label\": \"First name\",\n    \"Auth.form.firstname.placeholder\": \"e.g. Kai\",\n    \"Auth.form.forgot-password.email.label\": \"Enter your email\",\n    \"Auth.form.forgot-password.email.label.success\": \"Email successfully sent to\",\n    \"Auth.form.lastname.label\": \"Last name\",\n    \"Auth.form.lastname.placeholder\": \"e.g. Doe\",\n    \"Auth.form.password.hide-password\": \"Hide password\",\n    \"Auth.form.password.hint\": \"Must be at least 8 characters, 1 uppercase, 1 lowercase & 1 number\",\n    \"Auth.form.password.show-password\": \"Show password\",\n    \"Auth.form.register.news.label\": \"Keep me updated about new features & upcoming improvements (by doing this you accept the {terms} and the {policy}).\",\n    \"Auth.form.register.subtitle\": \"Credentials are only used to authenticate in Strapi. All saved data will be stored in your database.\",\n    \"Auth.form.rememberMe.label\": \"Remember me\",\n    \"Auth.form.username.label\": \"Username\",\n    \"Auth.form.username.placeholder\": \"e.g. Kai_Doe\",\n    \"Auth.form.welcome.subtitle\": \"Log in to your Strapi account\",\n    \"Auth.form.welcome.title\": \"Welcome to Strapi!\",\n    \"Auth.link.forgot-password\": \"Forgot your password?\",\n    \"Auth.link.ready\": \"Ready to sign in?\",\n    \"Auth.link.signin\": \"Sign in\",\n    \"Auth.link.signin.account\": \"Already have an account?\",\n    \"Auth.login.sso.divider\": \"Or login with\",\n    \"Auth.login.sso.loading\": \"Loading providers...\",\n    \"Auth.login.sso.subtitle\": \"Login to your account via SSO\",\n    \"Auth.privacy-policy-agreement.policy\": \"privacy policy\",\n    \"Auth.privacy-policy-agreement.terms\": \"terms\",\n    \"Auth.reset-password.title\": \"Reset password\",\n    \"Content Manager\": \"Content Manager\",\n    \"Content Type Builder\": \"Content-Types Builder\",\n    Documentation: Documentation,\n    Email: Email,\n    \"Files Upload\": \"Files Upload\",\n    \"HomePage.head.title\": \"Homepage\",\n    \"HomePage.header.title\": \"Hello {name}\",\n    \"HomePage.header.subtitle\": \"Welcome to your administration panel\",\n    \"HomePage.widget.loading\": \"Loading widget content\",\n    \"HomePage.widget.error\": \"Couldn't load widget content.\",\n    \"HomePage.widget.no-data\": \"No content found.\",\n    \"HomePage.widget.no-permissions\": \"You don’t have the permission to see this widget\",\n    \"Media Library\": \"Media Library\",\n    \"New entry\": \"New entry\",\n    Password: Password,\n    Provider: Provider,\n    ResetPasswordToken: ResetPasswordToken,\n    Role: Role,\n    \"Roles & Permissions\": \"Roles & Permissions\",\n    \"Roles.ListPage.notification.delete-all-not-allowed\": \"Some roles could not be deleted since they are associated with users\",\n    \"Roles.ListPage.notification.delete-not-allowed\": \"A role cannot be deleted if associated with users\",\n    \"Roles.RoleRow.select-all\": \"Select {name} for bulk actions\",\n    \"Roles.RoleRow.user-count\": \"{number, plural, =0 {# user} one {# user} other {# users}}\",\n    \"Roles.components.List.empty.withSearch\": \"There is no role corresponding to the search ({search})...\",\n    \"Settings.PageTitle\": \"Settings — {name}\",\n    \"Settings.apiTokens.ListView.headers.createdAt\": \"Created at\",\n    \"Settings.apiTokens.ListView.headers.description\": \"Description\",\n    \"Settings.apiTokens.ListView.headers.lastUsedAt\": \"Last used\",\n    \"Settings.apiTokens.ListView.headers.name\": \"Name\",\n    \"Settings.apiTokens.ListView.headers.type\": \"Token type\",\n    \"Settings.apiTokens.addFirstToken\": \"Add your first API Token\",\n    \"Settings.apiTokens.addNewToken\": \"Add new API Token\",\n    \"Settings.apiTokens.create\": \"Create new API Token\",\n    \"Settings.apiTokens.createPage.BoundRoute.title\": \"Bound route to\",\n    \"Settings.apiTokens.createPage.permissions.description\": \"Only actions bound by a route are listed below.\",\n    \"Settings.apiTokens.createPage.permissions.header.hint\": \"Select the application's actions or the plugin's actions and click on the cog icon to display the bound route\",\n    \"Settings.apiTokens.createPage.permissions.header.title\": \"Advanced settings\",\n    \"Settings.apiTokens.createPage.permissions.title\": \"Permissions\",\n    \"Settings.apiTokens.createPage.title\": \"Create API Token\",\n    \"Settings.apiTokens.description\": \"List of generated tokens to consume the API\",\n    \"Settings.apiTokens.emptyStateLayout\": \"You don’t have any content yet...\",\n    \"Settings.apiTokens.regenerate\": \"Regenerate\",\n    \"Settings.apiTokens.title\": \"API Tokens\",\n    \"Settings.apiTokens.lastHour\": \"last hour\",\n    \"Settings.application.customization\": \"Customization\",\n    \"Settings.application.customization.auth-logo.carousel-hint\": \"Replace the logo in the authentication pages\",\n    \"Settings.application.customization.carousel-hint\": \"Change the admin panel logo (Max dimension: {dimension}x{dimension}, Max file size: {size}KB)\",\n    \"Settings.application.customization.carousel-slide.label\": \"Logo slide\",\n    \"Settings.application.customization.carousel.auth-logo.title\": \"Auth logo\",\n    \"Settings.application.customization.carousel.change-action\": \"Change logo\",\n    \"Settings.application.customization.carousel.menu-logo.title\": \"Menu logo\",\n    \"Settings.application.customization.carousel.reset-action\": \"Reset logo\",\n    \"Settings.application.customization.carousel.title\": \"Logo\",\n    \"Settings.application.customization.menu-logo.carousel-hint\": \"Replace the logo in the main navigation\",\n    \"Settings.application.customization.modal.cancel\": \"Cancel\",\n    \"Settings.application.customization.modal.pending\": \"Pending logo\",\n    \"Settings.application.customization.modal.pending.card-badge\": \"image\",\n    \"Settings.application.customization.modal.pending.choose-another\": \"Choose another logo\",\n    \"Settings.application.customization.modal.pending.subtitle\": \"Manage the chosen logo before uploading it\",\n    \"Settings.application.customization.modal.pending.title\": \"Logo ready to upload\",\n    \"Settings.application.customization.modal.pending.upload\": \"Upload logo\",\n    \"Settings.application.customization.modal.tab.label\": \"How do you want to upload your assets?\",\n    \"Settings.application.customization.modal.upload\": \"Upload logo\",\n    \"Settings.application.customization.modal.upload.cta.browse\": \"Browse files\",\n    \"Settings.application.customization.modal.upload.drag-drop\": \"Drag and Drop here or\",\n    \"Settings.application.customization.modal.upload.error-format\": \"Wrong format uploaded (accepted formats only: jpeg, jpg, png, svg).\",\n    \"Settings.application.customization.modal.upload.error-network\": \"Network error\",\n    \"Settings.application.customization.modal.upload.error-size\": \"The file uploaded is too large (max dimension: {dimension}x{dimension}, max file size: {size}KB)\",\n    \"Settings.application.customization.modal.upload.file-validation\": \"Max dimension: {dimension}x{dimension}, Max size: {size}KB\",\n    \"Settings.application.customization.modal.upload.from-computer\": \"From computer\",\n    \"Settings.application.customization.modal.upload.from-url\": \"From URL\",\n    \"Settings.application.customization.modal.upload.from-url.input-label\": \"URL\",\n    \"Settings.application.customization.modal.upload.next\": \"Next\",\n    \"Settings.application.customization.size-details\": \"Max dimension: {dimension}×{dimension}, Max file size: {size}KB\",\n    \"Settings.application.description\": \"Administration panel’s global information\",\n    \"Settings.application.edition-title\": \"current edition\",\n    \"Settings.application.ee-or-ce\": \"{communityEdition, select, true {Community Edition} other {Enterprise Edition}}\",\n    \"Settings.application.ee.admin-seats.add-seats\": \"Manage seats\",\n    \"Settings.application.ee.admin-seats.support\": \"Contact sales\",\n    \"Settings.application.ee.admin-seats.at-limit-tooltip\": \"At limit: add seats to invite more users\",\n    \"Settings.application.ee.admin-seats.count\": \"<text>{enforcementUserCount}</text>/{permittedSeats}\",\n    \"Settings.application.get-help\": \"Get help\",\n    \"Settings.application.link-pricing\": \"See all pricing plans\",\n    \"Settings.application.link-upgrade\": \"Upgrade your admin panel\",\n    \"Settings.application.node-version\": \"node version\",\n    \"Settings.application.strapi-version\": \"Strapi version\",\n    \"Settings.application.strapiVersion\": \"Strapi version\",\n    \"Settings.application.title\": \"Overview\",\n    \"Settings.error\": \"Error\",\n    \"Settings.global\": \"Global Settings\",\n    \"Settings.permissions\": \"Administration panel\",\n    \"Settings.permissions.auditLogs.action\": \"Action\",\n    \"Settings.permissions.auditLogs.admin.auth.success\": \"Admin login\",\n    \"Settings.permissions.auditLogs.admin.logout\": \"Admin logout\",\n    \"Settings.permissions.auditLogs.component.create\": \"Create component\",\n    \"Settings.permissions.auditLogs.component.delete\": \"Delete component\",\n    \"Settings.permissions.auditLogs.component.update\": \"Update component\",\n    \"Settings.permissions.auditLogs.content-type.create\": \"Create content type\",\n    \"Settings.permissions.auditLogs.content-type.delete\": \"Delete content type\",\n    \"Settings.permissions.auditLogs.content-type.update\": \"Update content type\",\n    \"Settings.permissions.auditLogs.date\": \"Date\",\n    \"Settings.permissions.auditLogs.details\": \"Log Details\",\n    \"Settings.permissions.auditLogs.entry.create\": \"Create entry{model, select, undefined {} other { ({model})}}\",\n    \"Settings.permissions.auditLogs.entry.delete\": \"Delete entry{model, select, undefined {} other { ({model})}}\",\n    \"Settings.permissions.auditLogs.entry.publish\": \"Publish entry {model, select, undefined {} other {({model})}}\",\n    \"Settings.permissions.auditLogs.entry.unpublish\": \"Unpublish entry{model, select, undefined {} other { ({model})}}\",\n    \"Settings.permissions.auditLogs.entry.update\": \"Update entry{model, select, undefined {} other { ({model})}}\",\n    \"Settings.permissions.auditLogs.filters.combobox.aria-label\": \"Search and select an option to filter\",\n    \"Settings.permissions.auditLogs.listview.header.subtitle\": \"Logs of all the activities that happened in your environment\",\n    \"Settings.permissions.auditLogs.not-available\": \"Audit Logs is only available as part of a paid plan. Upgrade to get a searchable and filterable display of all activities.\",\n    \"Settings.permissions.auditLogs.media.create\": \"Create media\",\n    \"Settings.permissions.auditLogs.media.delete\": \"Delete media\",\n    \"Settings.permissions.auditLogs.media.update\": \"Update media\",\n    \"Settings.permissions.auditLogs.payload\": \"Payload\",\n    \"Settings.permissions.auditLogs.permission.create\": \"Create permission\",\n    \"Settings.permissions.auditLogs.permission.delete\": \"Delete permission\",\n    \"Settings.permissions.auditLogs.permission.update\": \"Update permission\",\n    \"Settings.permissions.auditLogs.role.create\": \"Create role\",\n    \"Settings.permissions.auditLogs.role.delete\": \"Delete role\",\n    \"Settings.permissions.auditLogs.role.update\": \"Update role\",\n    \"Settings.permissions.auditLogs.user\": \"User\",\n    \"Settings.permissions.auditLogs.user.create\": \"Create user\",\n    \"Settings.permissions.auditLogs.user.delete\": \"Delete user\",\n    \"Settings.permissions.auditLogs.user.fullname\": \"{firstname} {lastname}\",\n    \"Settings.permissions.auditLogs.user.update\": \"Update user\",\n    \"Settings.permissions.auditLogs.userId\": \"User ID\",\n    \"Settings.permissions.category\": \"Permissions settings for the {category}\",\n    \"Settings.permissions.category.plugins\": \"Permissions settings for the {category} plugin\",\n    \"Settings.permissions.conditions.anytime\": \"Anytime\",\n    \"Settings.permissions.conditions.apply\": \"Apply\",\n    \"Settings.permissions.conditions.can\": \"Can\",\n    \"Settings.permissions.conditions.conditions\": \"Conditions\",\n    \"Settings.permissions.conditions.define-conditions\": \"Define conditions\",\n    \"Settings.permissions.conditions.links\": \"Links\",\n    \"Settings.permissions.conditions.no-actions\": \"You first need to select actions (create, read, update, ...) before defining conditions on them.\",\n    \"Settings.permissions.conditions.none-selected\": \"Anytime\",\n    \"Settings.permissions.conditions.or\": \"OR\",\n    \"Settings.permissions.conditions.when\": \"When\",\n    \"Settings.permissions.select-all-by-permission\": \"Select all {label} permissions\",\n    \"Settings.permissions.select-by-permission\": \"Select {label} permission\",\n    \"Settings.permissions.users.active\": \"Active\",\n    \"Settings.permissions.users.create\": \"Invite new user\",\n    \"Settings.permissions.users.email\": \"Email\",\n    \"Settings.permissions.users.firstname\": \"Firstname\",\n    \"Settings.permissions.users.form.sso\": \"Connect with SSO\",\n    \"Settings.permissions.users.sso.provider.error\": \"An error occurred while requesting the SSO settings\",\n    \"Settings.permissions.users.form.sso.description\": \"When enabled (ON), users can login via SSO\",\n    \"Settings.permissions.users.inactive\": \"Inactive\",\n    \"Settings.permissions.users.lastname\": \"Lastname\",\n    \"Settings.permissions.users.listview.header.subtitle\": \"All the users who have access to the Strapi admin panel\",\n    \"Settings.permissions.users.roles\": \"Roles\",\n    \"Settings.permissions.users.strapi-author\": \"Author\",\n    \"Settings.permissions.users.strapi-editor\": \"Editor\",\n    \"Settings.permissions.users.strapi-super-admin\": \"Super Admin\",\n    \"Settings.permissions.users.tabs.label\": \"Tabs Permissions\",\n    \"Settings.permissions.users.user-status\": \"User status\",\n    \"Settings.permissions.users.username\": \"Username\",\n    \"Settings.profile.form.notify.data.loaded\": \"Your profile data has been loaded\",\n    \"Settings.profile.form.section.experience.clear.select\": \"Clear the interface language selected\",\n    \"Settings.profile.form.section.experience.here\": \"here\",\n    \"Settings.profile.form.section.experience.interfaceLanguage\": \"Interface language\",\n    \"Settings.profile.form.section.experience.interfaceLanguage.hint\": \"This will only display your own interface in the chosen language.\",\n    \"Settings.profile.form.section.experience.interfaceLanguageHelp\": \"Preference changes will apply only to you. More information is available {here}.\",\n    \"Settings.profile.form.section.experience.mode.hint\": \"Displays your interface in the chosen mode.\",\n    \"Settings.profile.form.section.experience.mode.label\": \"Interface mode\",\n    \"Settings.profile.form.section.experience.mode.option-label\": \"{name} mode\",\n    \"Settings.profile.form.section.experience.mode.option-system-label\": \"Use system settings\",\n    \"Settings.profile.form.section.experience.title\": \"Experience\",\n    \"Settings.profile.form.section.head.title\": \"User profile\",\n    \"Settings.profile.form.section.profile.page.title\": \"Profile page\",\n    \"Settings.roles.create.description\": \"Define the rights given to the role\",\n    \"Settings.roles.create.title\": \"Create a role\",\n    \"Settings.roles.created\": \"Role created\",\n    \"Settings.roles.edit.title\": \"Edit a role\",\n    \"Settings.roles.form.button.users-with-role\": \"{number, plural, =0 {# users} one {# user} other {# users}} with this role\",\n    \"Settings.roles.form.created\": \"Created\",\n    \"Settings.roles.form.description\": \"Name and description of the role\",\n    \"Settings.roles.form.permission.property-label\": \"{label} permissions\",\n    \"Settings.roles.form.permissions.attributesPermissions\": \"Fields permissions\",\n    \"Settings.roles.form.permissions.create\": \"Create\",\n    \"Settings.roles.form.permissions.delete\": \"Delete\",\n    \"Settings.roles.form.permissions.publish\": \"Publish\",\n    \"Settings.roles.form.permissions.read\": \"Read\",\n    \"Settings.roles.form.permissions.update\": \"Update\",\n    \"Settings.roles.list.button.add\": \"Add new role\",\n    \"Settings.roles.list.description\": \"List of roles\",\n    \"Settings.roles.title.singular\": \"role\",\n    \"Settings.sso.description\": \"Configure the settings for the Single Sign-On feature.\",\n    \"Settings.sso.form.defaultRole.description\": \"It will attach the new authenticated user to the selected role\",\n    \"Settings.sso.form.defaultRole.description-not-allowed\": \"You need to have the permission to read the admin roles\",\n    \"Settings.sso.form.defaultRole.label\": \"Default role\",\n    \"Settings.sso.form.localAuthenticationLock.label\": \"Local authentication lock-out\",\n    \"Settings.sso.form.localAuthenticationLock.description\": \"Select the roles for which you want to disable the local authentication\",\n    \"Settings.sso.form.registration.description\": \"Create new user on SSO login if no account exists\",\n    \"Settings.sso.form.registration.label\": \"Auto-registration\",\n    \"Settings.sso.title\": \"Single Sign-On\",\n    \"Settings.sso.not-available\": \"SSO is only available as part of a paid plan. Upgrade to configure additional sign-in & sign-up methods for your administration panel.\",\n    \"Settings.content-history.title\": \"Content History\",\n    \"Settings.content-history.description\": \"Get more control over every step of your content’s lifecycle.\",\n    \"Settings.content-history.not-available\": \"Content History is only available as part of a paid plan. Upgrade to get full control over your content's lifecycle.\",\n    \"Settings.tokens.Button.cancel\": \"Cancel\",\n    \"Settings.tokens.Button.regenerate\": \"Regenerate\",\n    \"Settings.tokens.ListView.headers.createdAt\": \"Created at\",\n    \"Settings.tokens.ListView.headers.description\": \"Description\",\n    \"Settings.tokens.ListView.headers.lastUsedAt\": \"Last used\",\n    \"Settings.tokens.ListView.headers.name\": \"Name\",\n    \"Settings.tokens.RegenerateDialog.title\": \"Regenerate token\",\n    \"Settings.tokens.copy.editMessage\": \"For security reasons, you can only see your token once.\",\n    \"Settings.tokens.copy.editTitle\": \"This token isn’t accessible anymore.\",\n    \"Settings.tokens.copy.lastWarning\": \"Make sure to copy this token, you won’t be able to see it again!\",\n    \"Settings.tokens.duration.30-days\": \"30 days\",\n    \"Settings.tokens.duration.7-days\": \"7 days\",\n    \"Settings.tokens.duration.90-days\": \"90 days\",\n    \"Settings.tokens.duration.expiration-date\": \"Expiration date\",\n    \"Settings.tokens.duration.unlimited\": \"Unlimited\",\n    \"Settings.tokens.form.description\": \"Description\",\n    \"Settings.tokens.form.duration\": \"Token duration\",\n    \"Settings.tokens.form.name\": \"Name\",\n    \"Settings.tokens.form.type\": \"Token type\",\n    \"Settings.tokens.notification.copied\": \"Token copied to clipboard.\",\n    \"Settings.tokens.popUpWarning.message\": \"Are you sure you want to regenerate this token?\",\n    \"Settings.tokens.regenerate\": \"Regenerate\",\n    \"Settings.tokens.types.custom\": \"Custom\",\n    \"Settings.tokens.types.full-access\": \"Full access\",\n    \"Settings.tokens.types.read-only\": \"Read-only\",\n    \"Settings.transferTokens.ListView.headers.type\": \"Token type\",\n    \"Settings.transferTokens.addFirstToken\": \"Add your first Transfer Token\",\n    \"Settings.transferTokens.addNewToken\": \"Add new Transfer Token\",\n    \"Settings.transferTokens.create\": \"Create new Transfer Token\",\n    \"Settings.transferTokens.createPage.title\": \"Create Transfer Token\",\n    \"Settings.transferTokens.description\": \"List of generated transfer tokens\",\n    \"Settings.transferTokens.emptyStateLayout\": \"You don’t have any content yet...\",\n    \"Settings.transferTokens.title\": \"Transfer Tokens\",\n    \"Settings.webhooks.create\": \"Create a webhook\",\n    \"Settings.webhooks.create.header\": \"Create new header\",\n    \"Settings.webhooks.created\": \"Webhook created\",\n    \"Settings.webhooks.event.publish-tooltip\": \"This event only exists for contents with Draft/Publish system enabled\",\n    \"Settings.webhooks.event.select\": \"Select event\",\n    \"Settings.webhooks.events.isLoading\": \"Events loading\",\n    \"Settings.webhooks.events.create\": \"Create\",\n    \"Settings.webhooks.events.update\": \"Update\",\n    \"Settings.webhooks.events.delete\": \"Delete webhook\",\n    \"Settings.webhooks.form.events\": \"Events\",\n    \"Settings.webhooks.form.headers\": \"Headers\",\n    \"Settings.webhooks.form.url\": \"URL\",\n    \"Settings.webhooks.headers.remove\": \"Remove header row {number}\",\n    \"Settings.webhooks.key\": \"Key\",\n    \"Settings.webhooks.list.button.add\": \"Create new webhook\",\n    \"Settings.webhooks.list.description\": \"Get POST changes notifications\",\n    \"Settings.webhooks.list.empty.description\": \"No webhooks found\",\n    \"Settings.webhooks.list.empty.link\": \"See our documentation\",\n    \"Settings.webhooks.list.empty.title\": \"There are no webhooks yet\",\n    \"Settings.webhooks.list.th.actions\": \"actions\",\n    \"Settings.webhooks.list.th.status\": \"status\",\n    \"Settings.webhooks.list.loading.success\": \"Webhooks have been loaded\",\n    \"Settings.webhooks.singular\": \"webhook\",\n    \"Settings.webhooks.title\": \"Webhooks\",\n    \"Settings.webhooks.to.delete\": \"{webhooksToDeleteLength, plural, one {# webhook} other {# webhooks}} selected\",\n    \"Settings.webhooks.trigger\": \"Trigger\",\n    \"Settings.webhooks.trigger.cancel\": \"Cancel trigger\",\n    \"Settings.webhooks.trigger.pending\": \"Pending…\",\n    \"Settings.webhooks.trigger.save\": \"Please save to trigger\",\n    \"Settings.webhooks.trigger.success\": \"Success!\",\n    \"Settings.webhooks.trigger.success.label\": \"Trigger succeeded\",\n    \"Settings.webhooks.trigger.test\": \"Test-trigger\",\n    \"Settings.webhooks.trigger.title\": \"Save before Trigger\",\n    \"Settings.webhooks.value\": \"Value\",\n    \"Settings.webhooks.validation.name.required\": \"Name is required\",\n    \"Settings.webhooks.validation.name.regex\": \"The name must start with a letter and only contain letters, numbers, spaces and underscores\",\n    \"Settings.webhooks.validation.url.required\": \"Url is required\",\n    \"Settings.webhooks.validation.url.regex\": \"The value must be a valid Url\",\n    \"Settings.webhooks.validation.key\": \"Key is required\",\n    \"Settings.webhooks.validation.value\": \"Value is required\",\n    \"Settings.page.PurchaseAudit-logs.description\": \"Track and review changes with your team\",\n    \"Settings.page.PurchaseAudit-logs.perks1\": \"Easily track changes\",\n    \"Settings.page.PurchaseAudit-logs.perks2\": \"Review changes with ease\",\n    \"Settings.page.PurchaseAudit-logs.perks3\": \"Maintain security and compliance\",\n    \"Settings.page.PurchaseContent-history.description\": \"Instantly revert content changes\",\n    \"Settings.page.PurchaseContent-history.perks1\": \"Browse your content history\",\n    \"Settings.page.PurchaseContent-history.perks2\": \"Revert changes in one click\",\n    \"Settings.page.PurchaseContent-history.perks3\": \"Track changes across locales\",\n    \"Settings.page.PurchaseSSO.description\": \"Simplify authentication for your team\",\n    \"Settings.page.PurchaseSSO.perks1\": \"Unified authentication\",\n    \"Settings.page.PurchaseSSO.perks2\": \"Enhanced security\",\n    \"Settings.page.PurchaseSSO.perks3\": \"Support for webhooks\",\n    \"Settings.page.purchase.upgrade.cta\": \"Upgrade\",\n    \"Settings.page.purchase.learn-more.cta\": \"Learn more\",\n    \"Usecase.back-end\": \"Back-end developer\",\n    \"Usecase.button.skip\": \"Skip this question\",\n    \"Usecase.content-creator\": \"Content Creator\",\n    \"Usecase.front-end\": \"Front-end developer\",\n    \"Usecase.full-stack\": \"Full-stack developer\",\n    \"Usecase.input.work-type\": \"What type of work do you do?\",\n    \"Usecase.notification.success.project-created\": \"Project has been successfully created\",\n    \"Usecase.other\": \"Other\",\n    \"Usecase.title\": \"Tell us a bit more about yourself\",\n    Username: Username,\n    \"Users & Permissions\": \"Users & Permissions\",\n    Users: Users,\n    \"Users.components.List.empty\": \"There is no users...\",\n    \"Users.components.List.empty.withFilters\": \"There is no users with the applied filters...\",\n    \"Users.components.List.empty.withSearch\": \"There is no users corresponding to the search ({search})...\",\n    \"admin.pages.MarketPlacePage.sort.label\": \"Sort by\",\n    \"admin.pages.MarketPlacePage.filters.categories\": \"Categories\",\n    \"admin.pages.MarketPlacePage.filters.categoriesSelected\": \"{count, plural, =0 {No categories} one {# category} other {# categories}} selected\",\n    \"admin.pages.MarketPlacePage.filters.collections\": \"Collections\",\n    \"admin.pages.MarketPlacePage.filters.collectionsSelected\": \"{count, plural, =0 {No Collections} one {# Collection} other {# Collections}} selected\",\n    \"admin.pages.MarketPlacePage.head\": \"Marketplace — Plugins\",\n    \"admin.pages.MarketPlacePage.missingPlugin.description\": \"Tell us what plugin you are looking for and we'll let our community plugin developers know in case they are in search for inspiration!\",\n    \"admin.pages.MarketPlacePage.missingPlugin.title\": \"Missing a plugin?\",\n    \"admin.pages.MarketPlacePage.offline.subtitle\": \"You need to be connected to the Internet to access Strapi Market.\",\n    \"admin.pages.MarketPlacePage.offline.title\": \"You are offline\",\n    \"admin.pages.MarketPlacePage.plugin.copy\": \"Copy install command\",\n    \"admin.pages.MarketPlacePage.plugin.copy.success\": \"Install command ready to be pasted in your terminal\",\n    \"admin.pages.MarketPlacePage.plugin.downloads\": \"This plugin has {downloadsCount} weekly downloads\",\n    \"admin.pages.MarketPlacePage.plugin.githubStars\": \"This plugin was starred {starsCount} on GitHub\",\n    \"admin.pages.MarketPlacePage.plugin.info\": \"Learn more\",\n    \"admin.pages.MarketPlacePage.plugin.info.label\": \"Learn more about {pluginName}\",\n    \"admin.pages.MarketPlacePage.plugin.info.text\": \"More\",\n    \"admin.pages.MarketPlacePage.plugin.installed\": \"Installed\",\n    \"admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi\": \"Made by Strapi\",\n    \"admin.pages.MarketPlacePage.plugin.tooltip.verified\": \"Plugin verified by Strapi\",\n    \"admin.pages.MarketPlacePage.plugin.version\": \"Update your Strapi version: \\\"{strapiAppVersion}\\\" to: \\\"{versionRange}\\\"\",\n    \"admin.pages.MarketPlacePage.plugin.version.null\": \"Unable to verify compatibility with your Strapi version: \\\"{strapiAppVersion}\\\"\",\n    \"admin.pages.MarketPlacePage.plugins\": \"Plugins\",\n    \"admin.pages.MarketPlacePage.provider.downloads\": \"This provider has {downloadsCount} weekly downloads\",\n    \"admin.pages.MarketPlacePage.provider.githubStars\": \"This provider was starred {starsCount} on GitHub\",\n    \"admin.pages.MarketPlacePage.providers\": \"Providers\",\n    \"admin.pages.MarketPlacePage.search.clear\": \"Clear the search\",\n    \"admin.pages.MarketPlacePage.search.empty\": \"No result for \\\"{target}\\\"\",\n    \"admin.pages.MarketPlacePage.search.placeholder\": \"Search\",\n    \"admin.pages.MarketPlacePage.sort.alphabetical\": \"Alphabetical order\",\n    \"admin.pages.MarketPlacePage.sort.alphabetical.selected\": \"Sort by alphabetical order\",\n    \"admin.pages.MarketPlacePage.sort.githubStars\": \"Number of GitHub stars\",\n    \"admin.pages.MarketPlacePage.sort.githubStars.selected\": \"Sort by GitHub stars\",\n    \"admin.pages.MarketPlacePage.sort.newest\": \"Newest\",\n    \"admin.pages.MarketPlacePage.sort.newest.selected\": \"Sort by newest\",\n    \"admin.pages.MarketPlacePage.sort.npmDownloads\": \"Number of downloads\",\n    \"admin.pages.MarketPlacePage.sort.npmDownloads.selected\": \"Sort by npm downloads\",\n    \"admin.pages.MarketPlacePage.submit.plugin.link\": \"Submit plugin\",\n    \"admin.pages.MarketPlacePage.submit.provider.link\": \"Submit provider\",\n    \"admin.pages.MarketPlacePage.subtitle\": \"Get more out of Strapi\",\n    \"admin.pages.MarketPlacePage.tab-group.label\": \"Plugins and Providers for Strapi\",\n    anErrorOccurred: anErrorOccurred,\n    noPreview: noPreview,\n    \"app.component.CopyToClipboard.label\": \"Copy to clipboard\",\n    \"app.component.search.label\": \"Search for {target}\",\n    \"app.component.table.duplicate\": \"Duplicate {target}\",\n    \"app.component.table.edit\": \"Edit {target}\",\n    \"app.component.table.read\": \"Read {target}\",\n    \"app.component.table.select.one-entry\": \"Select {target}\",\n    \"app.component.table.view\": \"{target} details\",\n    \"app.components.BlockLink.blog\": \"Blog\",\n    \"app.components.BlockLink.blog.content\": \"Read the latest news about Strapi and the ecosystem.\",\n    \"app.components.BlockLink.cloud\": \"Strapi Cloud\",\n    \"app.components.BlockLink.cloud.content\": \"Fully-managed cloud hosting for your Strapi project.\",\n    \"app.components.BlockLink.code\": \"Code examples\",\n    \"app.components.BlockLink.code.content\": \"Learn by testing real projects developed the community.\",\n    \"app.components.BlockLink.documentation.content\": \"Discover the essential concepts, guides and instructions.\",\n    \"app.components.BlockLink.tutorial\": \"Tutorials\",\n    \"app.components.BlockLink.tutorial.content\": \"Follow step-by-step instructions to use and customize Strapi.\",\n    \"app.components.Button.cancel\": \"Cancel\",\n    \"app.components.Button.confirm\": \"Confirm\",\n    \"app.components.Button.reset\": \"Reset\",\n    \"app.components.ComingSoonPage.comingSoon\": \"Coming soon\",\n    \"app.components.ConfirmDialog.title\": \"Confirmation\",\n    \"app.components.DownloadInfo.download\": \"Download in progress...\",\n    \"app.components.DownloadInfo.text\": \"This could take a minute. Thanks for your patience.\",\n    \"app.components.EmptyAttributes.title\": \"There are no fields yet\",\n    \"app.components.EmptyStateLayout.content-document\": \"No content found\",\n    \"app.components.EmptyStateLayout.content-permissions\": \"You don't have the permissions to access that content\",\n    \"app.components.FreeTrialEndedModal.button.downgrade\": \"Downgrade to Community\",\n    \"app.components.FreeTrialEndedModal.button.upgrade\": \"Stay on the Growth plan\",\n    \"app.components.FreeTrialEndedModal.description\": \"Your access to Growth plan features such as Content history, Releases and Single sign-On (SSO) has expired.\",\n    \"app.components.FreeTrialEndedModal.notice.item1\": \"Downgrading will remove access to the above features.\",\n    \"app.components.FreeTrialEndedModal.notice.item2\": \"Document version history will be deleted.\",\n    \"app.components.FreeTrialEndedModal.notice.item3\": \"All releases will be erased.\",\n    \"app.components.FreeTrialEndedModal.notice.item4\": \"If you downgrade ensure to set a root admin password to keep access to the admin panel.\",\n    \"app.components.FreeTrialEndedModal.notice.title\": \"Important to know:\",\n    \"app.components.FreeTrialEndedModal.title\": \"Your trial has ended\",\n    \"app.components.FreeTrialWelcomeModal.button\": \"Start exploring\",\n    \"app.components.FreeTrialWelcomeModal.description1\": \"For the next 30 days, you will have full access to advanced features like Content History, Releases and Single Sign-On (SSO) – everything you need to explore the power of Strapi CMS.\",\n    \"app.components.FreeTrialWelcomeModal.description2\": \"Use this time to build, customize, and test your content workflows with complete flexibility!\",\n    \"app.components.FreeTrialWelcomeModal.title\": \"We're glad to have you on board\",\n    \"app.components.GuidedTour.CM.create.content\": \"<p>Create and manage all the content here in the Content Manager.</p><p>Ex: Taking the Blog website example further, one can write an Article, save and publish it as they like.</p><p>💡 Quick tip — Don't forget to hit publish on the content you create.</p>\",\n    \"app.components.GuidedTour.CM.create.title\": \"⚡️ Create content\",\n    \"app.components.GuidedTour.CM.success.content\": \"<p>Awesome, one last step to go!</p><b>🚀 See content in action</b>\",\n    \"app.components.GuidedTour.CM.success.cta.title\": \"Test the API\",\n    \"app.components.GuidedTour.CM.success.title\": \"Step 2: Completed ✅\",\n    \"app.components.GuidedTour.CTB.create.content\": \"<p>Collection Types help you manage several entries, Single Types are suitable to manage only one entry.</p> <p>Ex: For a Blog website, Articles would be a Collection Type whereas a Homepage would be a Single Type.</p>\",\n    \"app.components.GuidedTour.CTB.create.cta.title\": \"Build a Collection Type\",\n    \"app.components.GuidedTour.CTB.create.title\": \"🧠 Create a first Collection Type\",\n    \"app.components.GuidedTour.CTB.success.content\": \"<p>Good going!</p><b>⚡️ What would you like to share with the world?</b>\",\n    \"app.components.GuidedTour.CTB.success.title\": \"Step 1: Completed ✅\",\n    \"app.components.GuidedTour.apiTokens.create.content\": \"<p>Generate an authentication token here and retrieve the content you just created.</p>\",\n    \"app.components.GuidedTour.apiTokens.create.cta.title\": \"Generate an API Token\",\n    \"app.components.GuidedTour.apiTokens.create.title\": \"🚀 See content in action\",\n    \"app.components.GuidedTour.apiTokens.success.content\": \"<p>See content in action by making an HTTP request:</p><ul><li><p>To this URL: <light>https://'<'YOUR_DOMAIN'>'/api/'<'YOUR_CT'>'</light></p></li><li><p>With the header: <light>Authorization: bearer '<'YOUR_API_TOKEN'>'</light></p></li></ul><p>For more ways to interact with content, see the <documentationLink>documentation</documentationLink>.</p>\",\n    \"app.components.GuidedTour.apiTokens.success.cta.title\": \"Go back to homepage\",\n    \"app.components.GuidedTour.apiTokens.success.title\": \"Step 3: Completed ✅\",\n    \"app.components.GuidedTour.create-content\": \"Create content\",\n    \"app.components.GuidedTour.home.CM.title\": \"⚡️ What would you like to share with the world?\",\n    \"app.components.GuidedTour.home.CTB.cta.title\": \"Go to the Content type Builder\",\n    \"app.components.GuidedTour.home.CTB.title\": \"🧠 Build the content structure\",\n    \"app.components.GuidedTour.home.apiTokens.cta.title\": \"Test the API\",\n    \"app.components.GuidedTour.skip\": \"Skip the tour\",\n    \"app.components.GuidedTour.title\": \"3 steps to get started\",\n    \"app.components.ImgPreview.hint\": \"Drag & drop your file into this area or {browse} for a file to upload\",\n    \"app.components.ImgPreview.hint.browse\": \"browse\",\n    \"app.components.InputFile.newFile\": \"Add new file\",\n    \"app.components.InputFileDetails.open\": \"Open in a new tab\",\n    \"app.components.InputFileDetails.originalName\": \"Original name:\",\n    \"app.components.InputFileDetails.remove\": \"Remove this file\",\n    \"app.components.InputFileDetails.size\": \"Size:\",\n    \"app.components.InstallPluginPage.Download.description\": \"It might take a few seconds to download and install the plugin.\",\n    \"app.components.InstallPluginPage.Download.title\": \"Downloading...\",\n    \"app.components.InstallPluginPage.description\": \"Extend your app effortlessly.\",\n    \"app.components.LeftMenu.collapse\": \"Collapse the navbar\",\n    \"app.components.LeftMenu.expand\": \"Expand the navbar\",\n    \"app.components.LeftMenu.general\": \"General\",\n    \"app.components.LeftMenu.logo.alt\": \"Application logo\",\n    \"app.components.LeftMenu.logout\": \"Log out\",\n    \"app.components.LeftMenu.navbrand.title\": \"Strapi Dashboard\",\n    \"app.components.LeftMenu.navbrand.workplace\": \"Workplace\",\n    \"app.components.LeftMenu.plugins\": \"Plugins\",\n    \"app.components.LeftMenu.trialCountdown.endedAt\": \"Your trial ended on {date}.\",\n    \"app.components.LeftMenu.trialCountdown.endsAt\": \"Your trial ends on {date}.\",\n    \"app.components.LeftMenuFooter.help\": \"Help\",\n    \"app.components.LeftMenuFooter.poweredBy\": \"Powered by \",\n    \"app.components.LeftMenuLinkContainer.collectionTypes\": \"Collection Types\",\n    \"app.components.LeftMenuLinkContainer.configuration\": \"Configurations\",\n    \"app.components.LeftMenuLinkContainer.general\": \"General\",\n    \"app.components.LeftMenuLinkContainer.noPluginsInstalled\": \"No plugins installed yet\",\n    \"app.components.LeftMenuLinkContainer.plugins\": \"Plugins\",\n    \"app.components.LeftMenuLinkContainer.singleTypes\": \"Single Types\",\n    \"app.components.ListPluginsPage.deletePlugin.description\": \"It might take a few seconds to uninstall the plugin.\",\n    \"app.components.ListPluginsPage.deletePlugin.title\": \"Uninstalling\",\n    \"app.components.ListPluginsPage.description\": \"List of the installed plugins in the project.\",\n    \"app.components.ListPluginsPage.head.title\": \"List plugins\",\n    \"app.components.Logout.logout\": \"Logout\",\n    \"app.components.Logout.profile\": \"Profile\",\n    \"app.components.MarketplaceBanner\": \"Discover plugins built by the community, and many more awesome things to kickstart your project, on Strapi Marketplace.\",\n    \"app.components.MarketplaceBanner.image.alt\": \"A Strapi rocket logo\",\n    \"app.components.MarketplaceBanner.link\": \"Check it out now\",\n    \"app.components.NotFoundPage.back\": \"Back to homepage\",\n    \"app.components.NotFoundPage.description\": \"Not Found\",\n    \"app.components.NpsSurvey.banner-title\": \"How likely are you to recommend Strapi to a friend or colleague?\",\n    \"app.components.NpsSurvey.feedback-response\": \"Thank you very much for your feedback!\",\n    \"app.components.NpsSurvey.feedback-question\": \"Do you have any suggestion for improvements?\",\n    \"app.components.NpsSurvey.submit-feedback\": \"Submit Feedback\",\n    \"app.components.NpsSurvey.dismiss-survey-label\": \"Dismiss survey\",\n    \"app.components.NpsSurvey.no-recommendation\": \"Not at all likely\",\n    \"app.components.NpsSurvey.happy-to-recommend\": \"Extremely likely\",\n    \"app.components.Official\": \"Official\",\n    \"app.components.Onboarding.help.button\": \"Help button\",\n    \"app.components.Onboarding.label.completed\": \"% completed\",\n    \"app.components.Onboarding.link.build-content\": \"Build a content architecture\",\n    \"app.components.Onboarding.link.manage-content\": \"Add & manage content\",\n    \"app.components.Onboarding.link.manage-media\": \"Manage media\",\n    \"app.components.Onboarding.link.more-videos\": \"Watch more videos\",\n    \"app.components.Onboarding.title\": \"Get Started Videos\",\n    \"app.components.PluginCard.Button.label.download\": \"Download\",\n    \"app.components.PluginCard.Button.label.install\": \"Already installed\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed\": \"The autoReload feature needs to be enabled. Please start your app with `yarn develop`.\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.confirm\": \"I understand!\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.environment\": \"For security reasons, a plugin can only be downloaded in a development environment.\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.title\": \"Downloading is impossible\",\n    \"app.components.PluginCard.compatible\": \"Compatible with your app\",\n    \"app.components.PluginCard.compatibleCommunity\": \"Compatible with the community\",\n    \"app.components.PluginCard.more-details\": \"More details\",\n    \"app.components.ToggleCheckbox.off-label\": \"False\",\n    \"app.components.ToggleCheckbox.on-label\": \"True\",\n    \"app.components.UpsellBanner.button\": \"Upgrade now\",\n    \"app.components.UpsellBanner.intro\": \"Access to Growth plan features: \",\n    \"app.components.UpsellBanner.text\": \"As part of your trial, you can explore premium tools such as Content History, Releases, and Single Sign-On (SSO).\",\n    \"app.components.Users.MagicLink.connect\": \"Copy and share this link to give access to this user\",\n    \"app.components.Users.MagicLink.connect.sso\": \"Send this link to the user, the first login can be made via a SSO provider\",\n    \"app.components.Users.ModalCreateBody.block-title.details\": \"User details\",\n    \"app.components.Users.ModalCreateBody.block-title.roles\": \"User's roles\",\n    \"app.components.Users.ModalCreateBody.block-title.roles.description\": \"A user can have one or several roles\",\n    \"app.components.Users.SortPicker.button-label\": \"Sort by\",\n    \"app.components.Users.SortPicker.sortby.email_asc\": \"Email (A to Z)\",\n    \"app.components.Users.SortPicker.sortby.email_desc\": \"Email (Z to A)\",\n    \"app.components.Users.SortPicker.sortby.firstname_asc\": \"First name (A to Z)\",\n    \"app.components.Users.SortPicker.sortby.firstname_desc\": \"First name (Z to A)\",\n    \"app.components.Users.SortPicker.sortby.lastname_asc\": \"Last name (A to Z)\",\n    \"app.components.Users.SortPicker.sortby.lastname_desc\": \"Last name (Z to A)\",\n    \"app.components.Users.SortPicker.sortby.username_asc\": \"Username (A to Z)\",\n    \"app.components.Users.SortPicker.sortby.username_desc\": \"Username (Z to A)\",\n    \"app.components.listPlugins.button\": \"Add New Plugin\",\n    \"app.components.listPlugins.title.none\": \"No plugins installed\",\n    \"app.components.listPluginsPage.deletePlugin.error\": \"An error occurred while uninstalling the plugin\",\n    \"app.containers.App.notification.error.init\": \"An error occurred while requesting the API\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin\": \"If you do not receive this link, please contact your administrator.\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.text.email\": \"It can take a few minutes to receive your password recovery link.\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.title\": \"Email sent\",\n    \"app.containers.Users.EditPage.form.active.label\": \"Active\",\n    \"app.containers.Users.EditPage.header.label\": \"Edit {name}\",\n    \"app.containers.Users.EditPage.header.label-loading\": \"Edit user\",\n    \"app.containers.Users.EditPage.roles-bloc-title\": \"Attributed roles\",\n    \"app.containers.Users.ModalForm.footer.button-success\": \"Invite user\",\n    \"app.links.configure-view\": \"Configure the view\",\n    \"app.page.not.found\": \"Oops! We can't seem to find the page you're looking for...\",\n    \"app.static.links.cheatsheet\": \"CheatSheet\",\n    \"app.utils.SelectOption.defaultMessage\": \" \",\n    \"app.utils.add-filter\": \"Add filter\",\n    \"app.utils.close-label\": \"Close\",\n    \"app.utils.defaultMessage\": \" \",\n    \"app.utils.delete\": \"Delete\",\n    \"app.utils.duplicate\": \"Duplicate\",\n    \"app.utils.edit\": \"Edit\",\n    \"app.utils.errors.file-too-big.message\": \"The file is too big\",\n    \"app.utils.filter-value\": \"Filter value\",\n    \"app.utils.filters\": \"Filters\",\n    \"app.utils.notify.data-loaded\": \"The {target} has loaded\",\n    \"app.utils.placeholder.defaultMessage\": \" \",\n    \"app.utils.publish\": \"Publish\",\n    \"app.utils.refresh\": \"Refresh\",\n    \"app.utils.select-all\": \"Select all\",\n    \"app.utils.select-field\": \"Select field\",\n    \"app.utils.select-filter\": \"Select filter\",\n    \"app.utils.unpublish\": \"Unpublish\",\n    \"app.utils.published\": \"Published\",\n    \"app.utils.ready-to-publish\": \"Ready to publish\",\n    \"app.utils.already-published\": \"Already published\",\n    \"app.utils.ready-to-publish-changes\": \"Ready to publish changes\",\n    \"app.utils.ready-to-unpublish-changes\": \"Ready to unpublish\",\n    \"app.confirm.body\": \"Are you sure?\",\n    clearLabel: clearLabel,\n    \"coming.soon\": \"This content is currently under construction and will be back in a few weeks!\",\n    \"component.Input.error.validation.integer\": \"The value must be an integer\",\n    \"components.AutoReloadBlocker.description\": \"Run Strapi with one of the following commands:\",\n    \"components.AutoReloadBlocker.header\": \"Reload feature is required for this plugin.\",\n    \"components.ErrorBoundary.title\": \"Something went wrong...\",\n    \"components.FilterOptions.FILTER_TYPES.$contains\": \"contains\",\n    \"components.FilterOptions.FILTER_TYPES.$containsi\": \"contains (case insensitive)\",\n    \"components.FilterOptions.FILTER_TYPES.$endsWith\": \"ends with\",\n    \"components.FilterOptions.FILTER_TYPES.$endsWithi\": \"ends with (case insensitive)\",\n    \"components.FilterOptions.FILTER_TYPES.$eq\": \"is\",\n    \"components.FilterOptions.FILTER_TYPES.$eqi\": \"is (case insensitive)\",\n    \"components.FilterOptions.FILTER_TYPES.$gt\": \"is greater than\",\n    \"components.FilterOptions.FILTER_TYPES.$gte\": \"is greater than or equal to\",\n    \"components.FilterOptions.FILTER_TYPES.$lt\": \"is lower than\",\n    \"components.FilterOptions.FILTER_TYPES.$lte\": \"is lower than or equal to\",\n    \"components.FilterOptions.FILTER_TYPES.$ne\": \"is not\",\n    \"components.FilterOptions.FILTER_TYPES.$nei\": \"is not (case insensitive)\",\n    \"components.FilterOptions.FILTER_TYPES.$notContains\": \"does not contain\",\n    \"components.FilterOptions.FILTER_TYPES.$notContainsi\": \"does not contain (case insensitive)\",\n    \"components.FilterOptions.FILTER_TYPES.$notNull\": \"is not null\",\n    \"components.FilterOptions.FILTER_TYPES.$null\": \"is null\",\n    \"components.FilterOptions.FILTER_TYPES.$startsWith\": \"starts with\",\n    \"components.FilterOptions.FILTER_TYPES.$startsWithi\": \"starts with (case insensitive)\",\n    \"components.Input.error.attribute.key.taken\": \"This value already exists\",\n    \"components.Input.error.attribute.sameKeyAndName\": \"Can't be equal\",\n    \"components.Input.error.attribute.taken\": \"This field name already exists\",\n    \"components.Input.error.contain.lowercase\": \"Password must contain at least one lowercase character\",\n    \"components.Input.error.contain.number\": \"Password must contain at least one number\",\n    \"components.Input.error.contain.maxBytes\": \"Password must be less than 73 bytes\",\n    \"components.Input.error.contain.uppercase\": \"Password must contain at least one uppercase character\",\n    \"components.Input.error.contentTypeName.taken\": \"This name already exists\",\n    \"components.Input.error.custom-error\": \"{errorMessage} \",\n    \"components.Input.error.password.noMatch\": \"Passwords do not match\",\n    \"components.Input.error.validation.combobox.invalid\": \"The value provided is not valid\",\n    \"components.Input.error.validation.email\": \"This is not a valid email\",\n    \"components.Input.error.validation.json\": \"This doesn't match the JSON format\",\n    \"components.Input.error.validation.lowercase\": \"The value must be a lowercase string\",\n    \"components.Input.error.validation.max\": \"The value is too high (max: {max}).\",\n    \"components.Input.error.validation.maxLength\": \"The value is too long (max: {max}).\",\n    \"components.Input.error.validation.min\": \"The value is too low (min: {min}).\",\n    \"components.Input.error.validation.minLength\": \"The value is too short (min: {min}).\",\n    \"components.Input.error.validation.minSupMax\": \"Can't be superior\",\n    \"components.Input.error.validation.regex\": \"The value does not match the regex.\",\n    \"components.Input.error.validation.string\": \"This is not a valid string.\",\n    \"components.Input.error.validation.required\": \"This value is required.\",\n    \"components.Input.error.validation.unique\": \"This value is already used.\",\n    \"components.Input.error.validation.email.withField\": \"{field} is an invalid email\",\n    \"components.Input.error.validation.json.withField\": \"{field} doesn't match the JSON format\",\n    \"components.Input.error.validation.lowercase.withField\": \"{field} must be a lowercase string\",\n    \"components.Input.error.validation.max.withField\": \"{field} is too high.\",\n    \"components.Input.error.validation.maxLength.withField\": \"{field} is too long.\",\n    \"components.Input.error.validation.min.withField\": \"{field} is too low.\",\n    \"components.Input.error.validation.minLength.withField\": \"{field} is too short.\",\n    \"components.Input.error.validation.minSupMax.withField\": \"{field} can't be superior\",\n    \"components.Input.error.validation.regex.withField\": \"{field} does not match the regex.\",\n    \"components.Input.error.validation.required.withField\": \"{field} is required.\",\n    \"components.Input.error.validation.unique.withField\": \"{field} is already used.\",\n    \"components.InputSelect.option.placeholder\": \"Choose here\",\n    \"components.ListRow.empty\": \"There is no data to be shown.\",\n    \"components.NotAllowedInput.text\": \"No permissions to see this field\",\n    \"components.OverlayBlocker.description\": \"You're using a feature that needs the server to restart. The page will reload automatically.\",\n    \"components.OverlayBlocker.description.serverError\": \"The server should have restarted, please check your logs in the terminal.\",\n    \"components.OverlayBlocker.title\": \"Waiting for restart...\",\n    \"components.OverlayBlocker.title.serverError\": \"The restart is taking longer than expected\",\n    \"components.PageFooter.select\": \"Entries per page\",\n    \"components.ProductionBlocker.description\": \"For safety purposes we have to disable this plugin in other environments.\",\n    \"components.ProductionBlocker.header\": \"This plugin is only available in development.\",\n    \"components.ViewSettings.tooltip\": \"View settings\",\n    \"components.TableHeader.sort\": \"Sort on {label}\",\n    \"components.Blocks.modifiers.bold\": \"Bold\",\n    \"components.Blocks.modifiers.italic\": \"Italic\",\n    \"components.Blocks.modifiers.underline\": \"Underline\",\n    \"components.Blocks.modifiers.strikethrough\": \"Strikethrough\",\n    \"components.Blocks.modifiers.code\": \"Inline code\",\n    \"components.Blocks.link\": \"Link\",\n    \"components.Blocks.expand\": \"Expand\",\n    \"components.Blocks.collapse\": \"Collapse\",\n    \"components.Blocks.popover.text\": \"Text\",\n    \"components.Blocks.popover.text.placeholder\": \"Enter link text\",\n    \"components.Blocks.popover.link\": \"Link\",\n    \"components.Blocks.popover.link.placeholder\": \"Paste link\",\n    \"components.Blocks.popover.link.error\": \"Please enter valid link\",\n    \"components.Blocks.popover.remove\": \"Remove\",\n    \"components.Blocks.popover.edit\": \"Edit\",\n    \"components.Blocks.blocks.selectBlock\": \"Select a block\",\n    \"components.Blocks.blocks.text\": \"Text\",\n    \"components.Blocks.blocks.heading1\": \"Heading 1\",\n    \"components.Blocks.blocks.heading2\": \"Heading 2\",\n    \"components.Blocks.blocks.heading3\": \"Heading 3\",\n    \"components.Blocks.blocks.heading4\": \"Heading 4\",\n    \"components.Blocks.blocks.heading5\": \"Heading 5\",\n    \"components.Blocks.blocks.heading6\": \"Heading 6\",\n    \"components.Blocks.blocks.code\": \"Code block\",\n    \"components.Blocks.blocks.quote\": \"Quote\",\n    \"components.Blocks.blocks.image\": \"Image\",\n    \"components.Blocks.blocks.unorderedList\": \"Bulleted list\",\n    \"components.Blocks.blocks.orderedList\": \"Numbered list\",\n    \"components.Blocks.blocks.code.languageLabel\": \"Select a language\",\n    \"components.Blocks.dnd.instruction\": \"To reorder blocks, press Command or Control along with Shift and the Up or Down arrow keys\",\n    \"components.Blocks.dnd.reorder\": \"{item}, moved. New position in the editor: {position}.\",\n    \"components.Wysiwyg.ToggleMode.markdown-mode\": \"Markdown mode\",\n    \"components.Wysiwyg.ToggleMode.preview-mode\": \"Preview mode\",\n    \"components.Wysiwyg.collapse\": \"Collapse\",\n    \"components.Wysiwyg.blocks.code\": \"Code\",\n    \"components.Wysiwyg.selectOptions.H1\": \"Heading 1\",\n    \"components.Wysiwyg.selectOptions.H2\": \"Heading 2\",\n    \"components.Wysiwyg.selectOptions.H3\": \"Heading 3\",\n    \"components.Wysiwyg.selectOptions.H4\": \"Heading 4\",\n    \"components.Wysiwyg.selectOptions.H5\": \"Heading 5\",\n    \"components.Wysiwyg.selectOptions.H6\": \"Heading 6\",\n    \"components.Wysiwyg.selectOptions.title\": \"Headings\",\n    \"components.WysiwygBottomControls.charactersIndicators\": \"characters\",\n    \"components.WysiwygBottomControls.fullscreen\": \"Expand\",\n    \"components.WysiwygBottomControls.uploadFiles\": \"Drag & drop files, paste from the clipboard or {browse}.\",\n    \"components.WysiwygBottomControls.uploadFiles.browse\": \"select them\",\n    \"components.pagination.go-to\": \"Go to page {page}\",\n    \"components.pagination.go-to-next\": \"Go to next page\",\n    \"components.pagination.go-to-previous\": \"Go to previous page\",\n    \"components.pagination.remaining-links\": \"And {number} other links\",\n    \"components.popUpWarning.button.cancel\": \"No, cancel\",\n    \"components.popUpWarning.button.confirm\": \"Yes, confirm\",\n    \"components.popUpWarning.message\": \"Are you sure you want to delete this?\",\n    \"components.popUpWarning.title\": \"Please confirm\",\n    \"components.premiumFeature.title\": \"Premium feature\",\n    dark: dark,\n    \"form.button.continue\": \"Continue\",\n    \"form.button.done\": \"Done\",\n    \"global.actions\": \"Actions\",\n    \"global.auditLogs\": \"Audit Logs\",\n    \"global.back\": \"Back\",\n    \"global.cancel\": \"Cancel\",\n    \"global.change-password\": \"Change password\",\n    \"global.close\": \"Close\",\n    \"global.content-manager\": \"Content Manager\",\n    \"global.home\": \"Home\",\n    \"global.continue\": \"Continue\",\n    \"global.delete\": \"Delete\",\n    \"global.delete-target\": \"Delete {target}\",\n    \"global.description\": \"Description\",\n    \"global.details\": \"Details\",\n    \"global.disabled\": \"Disabled\",\n    \"global.documentation\": \"Documentation\",\n    \"global.enabled\": \"Enabled\",\n    \"global.error\": \"Something went wrong\",\n    \"global.finish\": \"Finish\",\n    \"global.last-change.redo\": \"Redo last change\",\n    \"global.last-change.undo\": \"Undo last change\",\n    \"global.last-changes.discard\": \"Discard last changes\",\n    \"global.marketplace\": \"Marketplace\",\n    \"global.more\": \"More\",\n    \"global.name\": \"Name\",\n    \"global.new\": \"New\",\n    \"global.none\": \"None\",\n    \"global.password\": \"Password\",\n    \"global.plugins\": \"Plugins\",\n    \"global.plugins.content-manager\": \"Content Manager\",\n    \"global.plugins.content-manager.description\": \"Quick way to see, edit and delete the data in your database.\",\n    \"global.plugins.content-type-builder\": \"Content Type Builder\",\n    \"global.plugins.content-type-builder.description\": \"Modelize the data structure of your API. Create new fields and relations in just a minute. The files are automatically created and updated in your project.\",\n    \"global.plugins.documentation\": \"Documentation\",\n    \"global.plugins.documentation.description\": \"Create an OpenAPI Document and visualize your API with SWAGGER UI.\",\n    \"global.plugins.email\": \"Email\",\n    \"global.plugins.email.description\": \"Configure your application to send emails.\",\n    \"global.plugins.graphql\": \"GraphQL\",\n    \"global.plugins.graphql.description\": \"Adds GraphQL endpoint with default API methods.\",\n    \"global.plugins.i18n\": \"Internationalization\",\n    \"global.plugins.i18n.description\": \"This plugin enables to create, to read and to update content in different languages, both from the Admin Panel and from the API.\",\n    \"global.plugins.sentry\": \"Sentry\",\n    \"global.plugins.sentry.description\": \"Send Strapi error events to Sentry.\",\n    \"global.plugins.upload\": \"Media Library\",\n    \"global.plugins.upload.description\": \"Media file management.\",\n    \"global.plugins.users-permissions\": \"Roles & Permissions\",\n    \"global.plugins.users-permissions.description\": \"Protect your API with a full authentication process based on JWT. This plugin comes also with an ACL strategy that allows you to manage the permissions between the groups of users.\",\n    \"global.profile\": \"Profile settings\",\n    \"global.prompt.unsaved\": \"Are you sure you want to leave this page? All your modifications will be lost\",\n    \"global.reset-password\": \"Reset password\",\n    \"global.roles\": \"Roles\",\n    \"global.save\": \"Save\",\n    \"global.search\": \"Search\",\n    \"global.see-more\": \"See more\",\n    \"global.select\": \"Select\",\n    \"global.select-all-entries\": \"Select all entries\",\n    \"global.settings\": \"Settings\",\n    \"global.type\": \"Type\",\n    \"global.users\": \"Users\",\n    \"global.fullname\": \"{firstname} {lastname}\",\n    \"global.learn-more\": \"Learn more\",\n    light: light,\n    \"notification.contentType.relations.conflict\": \"Content type has conflicting relations\",\n    \"notification.default.title\": \"Information:\",\n    \"notification.ee.warning.at-seat-limit.title\": \"{licenseLimitStatus, select, OVER_LIMIT {Over} AT_LIMIT {At}} seat limit ({currentUserCount}/{permittedSeats})\",\n    \"notification.ee.warning.over-.message\": \"Add seats to {licenseLimitStatus, select, OVER_LIMIT {invite} AT_LIMIT {re-enable}} Users. If you already did it but it's not reflected in Strapi yet, make sure to restart your app.\",\n    \"notification.error\": \"An error occurred\",\n    \"notification.error.invalid.configuration\": \"You have an invalid configuration, check your server log for more information.\",\n    \"notification.error.layout\": \"Couldn't retrieve the layout\",\n    \"notification.error.tokennamenotunique\": \"Name already assigned to another token\",\n    \"notification.form.error.fields\": \"The form contains some errors\",\n    \"notification.form.success.fields\": \"Changes saved\",\n    \"notification.link-copied\": \"Link copied into the clipboard\",\n    \"notification.permission.not-allowed-read\": \"You are not allowed to see this document\",\n    \"notification.success.apitokencreated\": \"API Token successfully created\",\n    \"notification.success.apitokenedited\": \"API Token successfully edited\",\n    \"notification.success.delete\": \"The item has been deleted\",\n    \"notification.success.saved\": \"Saved\",\n    \"notification.success.title\": \"Success:\",\n    \"notification.success.transfertokencreated\": \"Transfer Token successfully created\",\n    \"notification.success.transfertokenedited\": \"Transfer Token successfully edited\",\n    \"notification.version.update.message\": \"A new version of Strapi is available!\",\n    \"notification.warning.404\": \"404 - Not found\",\n    \"notification.warning.title\": \"Warning:\",\n    or: or,\n    \"request.error.model.unknown\": \"This model doesn't exist\",\n    selectButtonTitle: selectButtonTitle,\n    skipToContent: skipToContent,\n    submit: submit,\n    \"tours.contentManager.Introduction.title\": \"Content manager\",\n    \"tours.contentManager.Introduction.content\": \"Create and manage content from your collection types and single types.\",\n    \"tours.stepCount\": \"Step {currentStep} of {tourLength}\",\n    \"tours.skip\": \"Skip\",\n    \"tours.next\": \"Next\"\n};\n\nexport { Analytics, Documentation, Email, Password, Provider, ResetPasswordToken, Role, Username, Users, anErrorOccurred, clearLabel, dark, en as default, light, noPreview, or, selectButtonTitle, skipToContent, submit };\n//# sourceMappingURL=en.json.mjs.map\n"], "mappings": ";AAAA,IAAI,YAAY;AAChB,IAAI,gBAAgB;AACpB,IAAI,QAAQ;AACZ,IAAI,WAAW;AACf,IAAI,WAAW;AACf,IAAI,qBAAqB;AACzB,IAAI,OAAO;AACX,IAAI,WAAW;AACf,IAAI,QAAQ;AACZ,IAAI,kBAAkB;AACtB,IAAI,YAAY;AAChB,IAAI,aAAa;AACjB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,KAAK;AACT,IAAI,oBAAoB;AACxB,IAAI,gBAAgB;AACpB,IAAI,SAAS;AACb,IAAI,KAAK;AAAA,EACL;AAAA,EACA,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0CAA0C;AAAA,EAC1C,iCAAiC;AAAA,EACjC,sCAAsC;AAAA,EACtC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,iDAAiD;AAAA,EACjD,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,2BAA2B;AAAA,EAC3B,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA,EAChB,uBAAuB;AAAA,EACvB,yBAAyB;AAAA,EACzB,4BAA4B;AAAA,EAC5B,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,2BAA2B;AAAA,EAC3B,kCAAkC;AAAA,EAClC,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,4BAA4B;AAAA,EAC5B,4BAA4B;AAAA,EAC5B,0CAA0C;AAAA,EAC1C,sBAAsB;AAAA,EACtB,iDAAiD;AAAA,EACjD,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,kDAAkD;AAAA,EAClD,yDAAyD;AAAA,EACzD,yDAAyD;AAAA,EACzD,0DAA0D;AAAA,EAC1D,mDAAmD;AAAA,EACnD,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,uCAAuC;AAAA,EACvC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,sCAAsC;AAAA,EACtC,8DAA8D;AAAA,EAC9D,oDAAoD;AAAA,EACpD,2DAA2D;AAAA,EAC3D,+DAA+D;AAAA,EAC/D,6DAA6D;AAAA,EAC7D,+DAA+D;AAAA,EAC/D,4DAA4D;AAAA,EAC5D,qDAAqD;AAAA,EACrD,8DAA8D;AAAA,EAC9D,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,+DAA+D;AAAA,EAC/D,mEAAmE;AAAA,EACnE,6DAA6D;AAAA,EAC7D,0DAA0D;AAAA,EAC1D,2DAA2D;AAAA,EAC3D,sDAAsD;AAAA,EACtD,mDAAmD;AAAA,EACnD,8DAA8D;AAAA,EAC9D,6DAA6D;AAAA,EAC7D,gEAAgE;AAAA,EAChE,iEAAiE;AAAA,EACjE,8DAA8D;AAAA,EAC9D,mEAAmE;AAAA,EACnE,iEAAiE;AAAA,EACjE,4DAA4D;AAAA,EAC5D,wEAAwE;AAAA,EACxE,wDAAwD;AAAA,EACxD,mDAAmD;AAAA,EACnD,oCAAoC;AAAA,EACpC,sCAAsC;AAAA,EACtC,iCAAiC;AAAA,EACjC,iDAAiD;AAAA,EACjD,+CAA+C;AAAA,EAC/C,wDAAwD;AAAA,EACxD,6CAA6C;AAAA,EAC7C,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,sCAAsC;AAAA,EACtC,8BAA8B;AAAA,EAC9B,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,+CAA+C;AAAA,EAC/C,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,sDAAsD;AAAA,EACtD,sDAAsD;AAAA,EACtD,sDAAsD;AAAA,EACtD,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,+CAA+C;AAAA,EAC/C,8DAA8D;AAAA,EAC9D,2DAA2D;AAAA,EAC3D,gDAAgD;AAAA,EAChD,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,0CAA0C;AAAA,EAC1C,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,8CAA8C;AAAA,EAC9C,8CAA8C;AAAA,EAC9C,8CAA8C;AAAA,EAC9C,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,8CAA8C;AAAA,EAC9C,gDAAgD;AAAA,EAChD,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,qDAAqD;AAAA,EACrD,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,iDAAiD;AAAA,EACjD,mDAAmD;AAAA,EACnD,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,oCAAoC;AAAA,EACpC,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,iDAAiD;AAAA,EACjD,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,uCAAuC;AAAA,EACvC,4CAA4C;AAAA,EAC5C,yDAAyD;AAAA,EACzD,iDAAiD;AAAA,EACjD,8DAA8D;AAAA,EAC9D,mEAAmE;AAAA,EACnE,kEAAkE;AAAA,EAClE,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,8DAA8D;AAAA,EAC9D,qEAAqE;AAAA,EACrE,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,oDAAoD;AAAA,EACpD,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,mCAAmC;AAAA,EACnC,iDAAiD;AAAA,EACjD,yDAAyD;AAAA,EACzD,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,wCAAwC;AAAA,EACxC,0CAA0C;AAAA,EAC1C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,6CAA6C;AAAA,EAC7C,yDAAyD;AAAA,EACzD,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,yDAAyD;AAAA,EACzD,8CAA8C;AAAA,EAC9C,wCAAwC;AAAA,EACxC,sBAAsB;AAAA,EACtB,8BAA8B;AAAA,EAC9B,kCAAkC;AAAA,EAClC,wCAAwC;AAAA,EACxC,0CAA0C;AAAA,EAC1C,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,8CAA8C;AAAA,EAC9C,gDAAgD;AAAA,EAChD,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,oCAAoC;AAAA,EACpC,mCAAmC;AAAA,EACnC,oCAAoC;AAAA,EACpC,4CAA4C;AAAA,EAC5C,sCAAsC;AAAA,EACtC,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,uCAAuC;AAAA,EACvC,wCAAwC;AAAA,EACxC,8BAA8B;AAAA,EAC9B,gCAAgC;AAAA,EAChC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,iDAAiD;AAAA,EACjD,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,4CAA4C;AAAA,EAC5C,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,2CAA2C;AAAA,EAC3C,kCAAkC;AAAA,EAClC,sCAAsC;AAAA,EACtC,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,4CAA4C;AAAA,EAC5C,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,0CAA0C;AAAA,EAC1C,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,2BAA2B;AAAA,EAC3B,8CAA8C;AAAA,EAC9C,2CAA2C;AAAA,EAC3C,6CAA6C;AAAA,EAC7C,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,sCAAsC;AAAA,EACtC,gDAAgD;AAAA,EAChD,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,qDAAqD;AAAA,EACrD,gDAAgD;AAAA,EAChD,gDAAgD;AAAA,EAChD,gDAAgD;AAAA,EAChD,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,oCAAoC;AAAA,EACpC,oCAAoC;AAAA,EACpC,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,qBAAqB;AAAA,EACrB,sBAAsB;AAAA,EACtB,2BAA2B;AAAA,EAC3B,gDAAgD;AAAA,EAChD,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB;AAAA,EACA,uBAAuB;AAAA,EACvB;AAAA,EACA,+BAA+B;AAAA,EAC/B,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,kDAAkD;AAAA,EAClD,0DAA0D;AAAA,EAC1D,mDAAmD;AAAA,EACnD,2DAA2D;AAAA,EAC3D,oCAAoC;AAAA,EACpC,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,6CAA6C;AAAA,EAC7C,2CAA2C;AAAA,EAC3C,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,uDAAuD;AAAA,EACvD,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,uCAAuC;AAAA,EACvC,kDAAkD;AAAA,EAClD,oDAAoD;AAAA,EACpD,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,kDAAkD;AAAA,EAClD,iDAAiD;AAAA,EACjD,0DAA0D;AAAA,EAC1D,gDAAgD;AAAA,EAChD,yDAAyD;AAAA,EACzD,2CAA2C;AAAA,EAC3C,oDAAoD;AAAA,EACpD,iDAAiD;AAAA,EACjD,0DAA0D;AAAA,EAC1D,kDAAkD;AAAA,EAClD,oDAAoD;AAAA,EACpD,wCAAwC;AAAA,EACxC,+CAA+C;AAAA,EAC/C;AAAA,EACA;AAAA,EACA,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,4BAA4B;AAAA,EAC5B,wCAAwC;AAAA,EACxC,4BAA4B;AAAA,EAC5B,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,kCAAkC;AAAA,EAClC,0CAA0C;AAAA,EAC1C,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,4CAA4C;AAAA,EAC5C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,uDAAuD;AAAA,EACvD,qDAAqD;AAAA,EACrD,kDAAkD;AAAA,EAClD,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,4CAA4C;AAAA,EAC5C,+CAA+C;AAAA,EAC/C,qDAAqD;AAAA,EACrD,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,+CAA+C;AAAA,EAC/C,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,+CAA+C;AAAA,EAC/C,sDAAsD;AAAA,EACtD,wDAAwD;AAAA,EACxD,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,yDAAyD;AAAA,EACzD,qDAAqD;AAAA,EACrD,4CAA4C;AAAA,EAC5C,2CAA2C;AAAA,EAC3C,gDAAgD;AAAA,EAChD,4CAA4C;AAAA,EAC5C,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,mCAAmC;AAAA,EACnC,kDAAkD;AAAA,EAClD,iDAAiD;AAAA,EACjD,sCAAsC;AAAA,EACtC,2CAA2C;AAAA,EAC3C,wDAAwD;AAAA,EACxD,sDAAsD;AAAA,EACtD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,2DAA2D;AAAA,EAC3D,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,oCAAoC;AAAA,EACpC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,8CAA8C;AAAA,EAC9C,4CAA4C;AAAA,EAC5C,iDAAiD;AAAA,EACjD,8CAA8C;AAAA,EAC9C,+CAA+C;AAAA,EAC/C,2BAA2B;AAAA,EAC3B,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,iDAAiD;AAAA,EACjD,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,mCAAmC;AAAA,EACnC,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,+EAA+E;AAAA,EAC/E,qEAAqE;AAAA,EACrE,yEAAyE;AAAA,EACzE,mEAAmE;AAAA,EACnE,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,4DAA4D;AAAA,EAC5D,0DAA0D;AAAA,EAC1D,sEAAsE;AAAA,EACtE,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,qDAAqD;AAAA,EACrD,wDAAwD;AAAA,EACxD,yDAAyD;AAAA,EACzD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,qCAAqC;AAAA,EACrC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,oEAAoE;AAAA,EACpE,4DAA4D;AAAA,EAC5D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,wDAAwD;AAAA,EACxD,4BAA4B;AAAA,EAC5B,sBAAsB;AAAA,EACtB,+BAA+B;AAAA,EAC/B,yCAAyC;AAAA,EACzC,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,4BAA4B;AAAA,EAC5B,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,kBAAkB;AAAA,EAClB,yCAAyC;AAAA,EACzC,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,gCAAgC;AAAA,EAChC,wCAAwC;AAAA,EACxC,qBAAqB;AAAA,EACrB,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,8BAA8B;AAAA,EAC9B,+BAA+B;AAAA,EAC/B,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,oBAAoB;AAAA,EACpB;AAAA,EACA,eAAe;AAAA,EACf,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,+CAA+C;AAAA,EAC/C,qDAAqD;AAAA,EACrD,sDAAsD;AAAA,EACtD,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,0CAA0C;AAAA,EAC1C,4CAA4C;AAAA,EAC5C,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,4CAA4C;AAAA,EAC5C,gDAAgD;AAAA,EAChD,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,sDAAsD;AAAA,EACtD,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,2CAA2C;AAAA,EAC3C,4CAA4C;AAAA,EAC5C,8CAA8C;AAAA,EAC9C,4CAA4C;AAAA,EAC5C,qDAAqD;AAAA,EACrD,oDAAoD;AAAA,EACpD,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,yDAAyD;AAAA,EACzD,yDAAyD;AAAA,EACzD,qDAAqD;AAAA,EACrD,wDAAwD;AAAA,EACxD,sDAAsD;AAAA,EACtD,6CAA6C;AAAA,EAC7C,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,oCAAoC;AAAA,EACpC,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,oCAAoC;AAAA,EACpC,0BAA0B;AAAA,EAC1B,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA,EAC9B,kCAAkC;AAAA,EAClC,8CAA8C;AAAA,EAC9C,kCAAkC;AAAA,EAClC,8CAA8C;AAAA,EAC9C,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,wCAAwC;AAAA,EACxC,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,+CAA+C;AAAA,EAC/C,qCAAqC;AAAA,EACrC,iCAAiC;AAAA,EACjC,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,kCAAkC;AAAA,EAClC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,yDAAyD;AAAA,EACzD,+CAA+C;AAAA,EAC/C,gDAAgD;AAAA,EAChD,uDAAuD;AAAA,EACvD,+BAA+B;AAAA,EAC/B,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,yCAAyC;AAAA,EACzC,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC;AAAA,EACA,wBAAwB;AAAA,EACxB,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,0BAA0B;AAAA,EAC1B,gBAAgB;AAAA,EAChB,0BAA0B;AAAA,EAC1B,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,wBAAwB;AAAA,EACxB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,2BAA2B;AAAA,EAC3B,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,sBAAsB;AAAA,EACtB,eAAe;AAAA,EACf,eAAe;AAAA,EACf,cAAc;AAAA,EACd,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,kCAAkC;AAAA,EAClC,8CAA8C;AAAA,EAC9C,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,wBAAwB;AAAA,EACxB,oCAAoC;AAAA,EACpC,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,gDAAgD;AAAA,EAChD,kBAAkB;AAAA,EAClB,yBAAyB;AAAA,EACzB,yBAAyB;AAAA,EACzB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB;AAAA,EACA,+CAA+C;AAAA,EAC/C,8BAA8B;AAAA,EAC9B,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,sBAAsB;AAAA,EACtB,4CAA4C;AAAA,EAC5C,6BAA6B;AAAA,EAC7B,yCAAyC;AAAA,EACzC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,4CAA4C;AAAA,EAC5C,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,6CAA6C;AAAA,EAC7C,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA,EAC9B;AAAA,EACA,+BAA+B;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA,2CAA2C;AAAA,EAC3C,6CAA6C;AAAA,EAC7C,mBAAmB;AAAA,EACnB,cAAc;AAAA,EACd,cAAc;AAClB;", "names": []}