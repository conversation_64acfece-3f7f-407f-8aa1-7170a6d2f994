import {
  require_baseMap
} from "./chunk-T2YF43GM.js";
import {
  require_baseSlice
} from "./chunk-YXDCVYVT.js";
import {
  require_baseIteratee
} from "./chunk-XU64YSEO.js";
import {
  require_arrayMap,
  require_isArray
} from "./chunk-CE4VABH2.js";
import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/lodash/map.js
var require_map = __commonJS({
  "node_modules/lodash/map.js"(exports, module) {
    var arrayMap = require_arrayMap();
    var baseIteratee = require_baseIteratee();
    var baseMap = require_baseMap();
    var isArray = require_isArray();
    function map(collection, iteratee) {
      var func = isArray(collection) ? arrayMap : baseMap;
      return func(collection, baseIteratee(iteratee, 3));
    }
    module.exports = map;
  }
});

// node_modules/lodash/tail.js
var require_tail = __commonJS({
  "node_modules/lodash/tail.js"(exports, module) {
    var baseSlice = require_baseSlice();
    function tail(array) {
      var length = array == null ? 0 : array.length;
      return length ? baseSlice(array, 1, length) : [];
    }
    module.exports = tail;
  }
});

export {
  require_map,
  require_tail
};
//# sourceMappingURL=chunk-SGRJ2Y6F.js.map
