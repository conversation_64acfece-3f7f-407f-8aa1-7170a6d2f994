import {
  <PERSON>umn,
  LayoutContent,
  Logo,
  UnauthenticatedLayout
} from "./chunk-QQYLVHWD.js";
import {
  errorsTrads
} from "./chunk-IFOFBKTA.js";
import {
  create3 as create,
  create4 as create2,
  create6 as create3,
  require_camelCase
} from "./chunk-4WX3TY7V.js";
import {
  MemoizedInputRenderer
} from "./chunk-QQ4VIATE.js";
import {
  Form
} from "./chunk-VWIQ6S3Y.js";
import {
  useAuth
} from "./chunk-JXTOCMQW.js";
import {
  Box,
  Button,
  Flex,
  Link,
  Main,
  Typography,
  useIntl
} from "./chunk-4YYUDJZ2.js";
import {
  NavLink,
  useLocation,
  useNavigate
} from "./chunk-S65ZWNEO.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Auth/components/Login.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var import_camelCase = __toESM(require_camelCase(), 1);
var LOGIN_SCHEMA = create3().shape({
  email: create2().nullable().email({
    id: errorsTrads.email.id,
    defaultMessage: "Not a valid email"
  }).required(errorsTrads.required),
  password: create2().required(errorsTrads.required).nullable(),
  rememberMe: create().nullable()
});
var Login = ({ children }) => {
  const [apiError, setApiError] = React.useState();
  const { formatMessage } = useIntl();
  const { search: searchString } = useLocation();
  const query = React.useMemo(() => new URLSearchParams(searchString), [
    searchString
  ]);
  const navigate = useNavigate();
  const { login } = useAuth("Login", (auth) => auth);
  const handleLogin = async (body) => {
    setApiError(void 0);
    const res = await login(body);
    if ("error" in res) {
      const message = res.error.message ?? "Something went wrong";
      if ((0, import_camelCase.default)(message).toLowerCase() === "usernotactive") {
        navigate("/auth/oops");
        return;
      }
      setApiError(message);
    } else {
      const redirectTo = query.get("redirectTo");
      const redirectUrl = redirectTo ? decodeURIComponent(redirectTo) : "/";
      navigate(redirectUrl);
    }
  };
  return (0, import_jsx_runtime.jsx)(UnauthenticatedLayout, {
    children: (0, import_jsx_runtime.jsxs)(Main, {
      children: [
        (0, import_jsx_runtime.jsxs)(LayoutContent, {
          children: [
            (0, import_jsx_runtime.jsxs)(Column, {
              children: [
                (0, import_jsx_runtime.jsx)(Logo, {}),
                (0, import_jsx_runtime.jsx)(Box, {
                  paddingTop: 6,
                  paddingBottom: 1,
                  children: (0, import_jsx_runtime.jsx)(Typography, {
                    variant: "alpha",
                    tag: "h1",
                    children: formatMessage({
                      id: "Auth.form.welcome.title",
                      defaultMessage: "Welcome!"
                    })
                  })
                }),
                (0, import_jsx_runtime.jsx)(Box, {
                  paddingBottom: 7,
                  children: (0, import_jsx_runtime.jsx)(Typography, {
                    variant: "epsilon",
                    textColor: "neutral600",
                    children: formatMessage({
                      id: "Auth.form.welcome.subtitle",
                      defaultMessage: "Log in to your Strapi account"
                    })
                  })
                }),
                apiError ? (0, import_jsx_runtime.jsx)(Typography, {
                  id: "global-form-error",
                  role: "alert",
                  tabIndex: -1,
                  textColor: "danger600",
                  children: apiError
                }) : null
              ]
            }),
            (0, import_jsx_runtime.jsx)(Form, {
              method: "PUT",
              initialValues: {
                email: "",
                password: "",
                rememberMe: false
              },
              onSubmit: (values) => {
                handleLogin(values);
              },
              validationSchema: LOGIN_SCHEMA,
              children: (0, import_jsx_runtime.jsxs)(Flex, {
                direction: "column",
                alignItems: "stretch",
                gap: 6,
                children: [
                  [
                    {
                      label: formatMessage({
                        id: "Auth.form.email.label",
                        defaultMessage: "Email"
                      }),
                      name: "email",
                      placeholder: formatMessage({
                        id: "Auth.form.email.placeholder",
                        defaultMessage: "<EMAIL>"
                      }),
                      required: true,
                      type: "string"
                    },
                    {
                      label: formatMessage({
                        id: "global.password",
                        defaultMessage: "Password"
                      }),
                      name: "password",
                      required: true,
                      type: "password"
                    },
                    {
                      label: formatMessage({
                        id: "Auth.form.rememberMe.label",
                        defaultMessage: "Remember me"
                      }),
                      name: "rememberMe",
                      type: "checkbox"
                    }
                  ].map((field) => (0, import_jsx_runtime.jsx)(MemoizedInputRenderer, {
                    ...field
                  }, field.name)),
                  (0, import_jsx_runtime.jsx)(Button, {
                    fullWidth: true,
                    type: "submit",
                    children: formatMessage({
                      id: "Auth.form.button.login",
                      defaultMessage: "Login"
                    })
                  })
                ]
              })
            }),
            children
          ]
        }),
        (0, import_jsx_runtime.jsx)(Flex, {
          justifyContent: "center",
          children: (0, import_jsx_runtime.jsx)(Box, {
            paddingTop: 4,
            children: (0, import_jsx_runtime.jsx)(Link, {
              isExternal: false,
              tag: NavLink,
              to: "/auth/forgot-password",
              children: formatMessage({
                id: "Auth.link.forgot-password",
                defaultMessage: "Forgot your password?"
              })
            })
          })
        })
      ]
    })
  });
};

export {
  Login
};
//# sourceMappingURL=chunk-SWRLCKDU.js.map
