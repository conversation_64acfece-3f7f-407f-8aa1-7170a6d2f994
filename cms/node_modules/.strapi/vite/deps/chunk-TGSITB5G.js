import {
  lightTheme
} from "./chunk-4YYUDJZ2.js";

// node_modules/@strapi/review-workflows/dist/admin/utils/api.mjs
var isBaseQueryError = (error) => {
  return error.name !== void 0;
};
var buildValidParams = (query) => {
  if (!query) return query;
  const { plugins: _, ...validQueryParams } = {
    ...query,
    ...Object.values((query == null ? void 0 : query.plugins) ?? {}).reduce((acc, current) => Object.assign(acc, current), {})
  };
  return validQueryParams;
};

// node_modules/@strapi/review-workflows/dist/admin/utils/colors.mjs
var STAGE_COLORS = {
  primary600: "Blue",
  primary200: "Lilac",
  alternative600: "Violet",
  alternative200: "Lavender",
  success600: "Green",
  success200: "Pale Green",
  danger500: "Cherry",
  danger200: "Pink",
  warning600: "Orange",
  warning200: "Yellow",
  secondary600: "Teal",
  secondary200: "Baby Blue",
  neutral400: "Gray",
  neutral0: "White"
};
var getStageColorByHex = (hex) => {
  if (!hex) {
    return null;
  }
  const themeColors = Object.entries(lightTheme.colors).filter(([, value]) => value.toUpperCase() === hex.toUpperCase());
  const themeColorName = themeColors.reduce((acc, [name]) => {
    if (STAGE_COLORS == null ? void 0 : STAGE_COLORS[name]) {
      acc = name;
    }
    return acc;
  }, null);
  if (!themeColorName) {
    return null;
  }
  return {
    themeColorName,
    name: STAGE_COLORS[themeColorName]
  };
};
var AVAILABLE_COLORS = Object.entries(STAGE_COLORS).map(([themeColorName, name]) => ({
  hex: lightTheme.colors[themeColorName].toUpperCase(),
  name
}));

export {
  isBaseQueryError,
  buildValidParams,
  getStageColorByHex,
  AVAILABLE_COLORS
};
//# sourceMappingURL=chunk-TGSITB5G.js.map
