{"version": 3, "sources": ["../../../@strapi/review-workflows/admin/src/utils/api.ts", "../../../@strapi/review-workflows/admin/src/utils/colors.ts"], "sourcesContent": ["import { SerializedError } from '@reduxjs/toolkit';\nimport { type UnknownApiError, type ApiError } from '@strapi/admin/strapi-admin';\n\nexport type BaseQueryError = ApiError | UnknownApiError | SerializedError;\n\nconst isBaseQueryError = (error: BaseQueryError): error is ApiError | UnknownApiError => {\n  return error.name !== undefined;\n};\n\ninterface Query {\n  plugins?: Record<string, unknown>;\n  _q?: string;\n  [key: string]: any;\n}\n\n/**\n * This type extracts the plugin options from the query\n * and appends them to the root of the query\n */\ntype TransformedQuery<TQuery extends Query> = Omit<TQuery, 'plugins'> & {\n  [key: string]: string;\n};\n\n/**\n * @description\n * Creates a valid query params object for get requests\n * ie. plugins[18n][locale]=en becomes locale=en\n */\nconst buildValidParams = <TQuery extends Query>(query: TQuery): TransformedQuery<TQuery> => {\n  if (!query) return query;\n\n  // Extract pluginOptions from the query, they shouldn't be part of the URL\n  const { plugins: _, ...validQueryParams } = {\n    ...query,\n    ...Object.values(query?.plugins ?? {}).reduce<Record<string, string>>(\n      (acc, current) => Object.assign(acc, current),\n      {}\n    ),\n  };\n\n  return validQueryParams;\n};\n\nexport { isBaseQueryError, buildValidParams };\n", "import { lightTheme } from '@strapi/design-system';\nimport { DefaultTheme } from 'styled-components';\n\nconst STAGE_COLORS: Record<string, string> = {\n  primary600: 'Blue',\n  primary200: 'Lilac',\n  alternative600: 'Violet',\n  alternative200: 'Lavender',\n  success600: 'Green',\n  success200: 'Pale Green',\n  danger500: 'Cherry',\n  danger200: 'Pink',\n  warning600: 'Orange',\n  warning200: 'Yellow',\n  secondary600: 'Teal',\n  secondary200: 'Baby Blue',\n  neutral400: 'Gray',\n  neutral0: 'White',\n};\n\nconst getStageColorByHex = (hex?: string) => {\n  if (!hex) {\n    return null;\n  }\n\n  // there are multiple colors with the same hex code in the design tokens. In order to find\n  // the correct one we have to find all matching colors and then check, which ones are usable\n  // for stages.\n  const themeColors: [string, (keyof typeof STAGE_COLORS)[]][] = Object.entries(\n    lightTheme.colors\n  ).filter(([, value]) => value.toUpperCase() === hex.toUpperCase());\n\n  const themeColorName = themeColors.reduce(\n    (acc, [name]) => {\n      if (STAGE_COLORS?.[name]) {\n        acc = name;\n      }\n\n      return acc;\n    },\n    null as keyof typeof STAGE_COLORS | null\n  );\n\n  if (!themeColorName) {\n    return null;\n  }\n\n  return {\n    themeColorName,\n    name: STAGE_COLORS[themeColorName],\n  };\n};\n\nconst AVAILABLE_COLORS = Object.entries(STAGE_COLORS).map(([themeColorName, name]) => ({\n  hex: lightTheme.colors[themeColorName as keyof DefaultTheme['colors']].toUpperCase(),\n  name,\n}));\n\nexport { AVAILABLE_COLORS, getStageColorByHex };\n"], "mappings": ";;;;;AAKA,IAAMA,mBAAmB,CAACC,UAAAA;AACxB,SAAOA,MAAMC,SAASC;AACxB;AAqBA,IAAMC,mBAAmB,CAAuBC,UAAAA;AAC9C,MAAI,CAACA,MAAO,QAAOA;AAGnB,QAAM,EAAEC,SAASC,GAAG,GAAGC,iBAAAA,IAAqB;IAC1C,GAAGH;IACH,GAAGI,OAAOC,QAAOL,+BAAOC,YAAW,CAAA,CAAA,EAAIK,OACrC,CAACC,KAAKC,YAAYJ,OAAOK,OAAOF,KAAKC,OAAAA,GACrC,CAAA,CACD;EACH;AAEA,SAAOL;AACT;;;ACtCA,IAAMO,eAAuC;EAC3CC,YAAY;EACZC,YAAY;EACZC,gBAAgB;EAChBC,gBAAgB;EAChBC,YAAY;EACZC,YAAY;EACZC,WAAW;EACXC,WAAW;EACXC,YAAY;EACZC,YAAY;EACZC,cAAc;EACdC,cAAc;EACdC,YAAY;EACZC,UAAU;AACZ;AAEA,IAAMC,qBAAqB,CAACC,QAAAA;AAC1B,MAAI,CAACA,KAAK;AACR,WAAO;EACT;AAKA,QAAMC,cAAyDC,OAAOC,QACpEC,WAAWC,MAAM,EACjBC,OAAO,CAAC,CAAA,EAAGC,KAAM,MAAKA,MAAMC,YAAW,MAAOR,IAAIQ,YAAW,CAAA;AAE/D,QAAMC,iBAAiBR,YAAYS,OACjC,CAACC,KAAK,CAACC,IAAK,MAAA;AACV,QAAI5B,6CAAe4B,OAAO;AACxBD,YAAMC;IACR;AAEA,WAAOD;KAET,IAAA;AAGF,MAAI,CAACF,gBAAgB;AACnB,WAAO;EACT;AAEA,SAAO;IACLA;IACAG,MAAM5B,aAAayB,cAAe;EACpC;AACF;AAEA,IAAMI,mBAAmBX,OAAOC,QAAQnB,YAAc8B,EAAAA,IAAI,CAAC,CAACL,gBAAgBG,IAAK,OAAM;EACrFZ,KAAKI,WAAWC,OAAOI,cAAAA,EAAgDD,YAAW;EAClFI;EACF;", "names": ["isBaseQueryError", "error", "name", "undefined", "buildValidParams", "query", "plugins", "_", "validQueryParams", "Object", "values", "reduce", "acc", "current", "assign", "STAGE_COLORS", "primary600", "primary200", "alternative600", "alternative200", "success600", "success200", "danger500", "danger200", "warning600", "warning200", "secondary600", "secondary200", "neutral400", "neutral0", "getStageColorByHex", "hex", "themeColors", "Object", "entries", "lightTheme", "colors", "filter", "value", "toUpperCase", "themeColorName", "reduce", "acc", "name", "AVAILABLE_COLORS", "map"]}