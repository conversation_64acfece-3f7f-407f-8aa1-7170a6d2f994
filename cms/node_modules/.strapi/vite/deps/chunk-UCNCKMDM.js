import {
  pluginId
} from "./chunk-LR4RNYDQ.js";
import {
  useMutation,
  useQuery
} from "./chunk-BI5NNIZJ.js";
import {
  useFetchClient
} from "./chunk-G7IUBXYD.js";
import {
  useTracking
} from "./chunk-USP7YHPW.js";
import {
  useNotification
} from "./chunk-4EY5FDQG.js";
import {
  useIntl
} from "./chunk-4YYUDJZ2.js";

// node_modules/@strapi/upload/dist/admin/hooks/useConfig.mjs
var endpoint = `/${pluginId}/configuration`;
var queryKey = [
  pluginId,
  "configuration"
];
var useConfig = () => {
  const { trackUsage } = useTracking();
  const { formatMessage } = useIntl();
  const { toggleNotification } = useNotification();
  const { get, put } = useFetchClient();
  const config = useQuery(queryKey, async () => {
    const res = await get(endpoint);
    return res.data.data;
  }, {
    onError() {
      return toggleNotification({
        type: "danger",
        message: formatMessage({
          id: "notification.error"
        })
      });
    },
    /**
    * We're cementing that we always expect an object to be returned.
    */
    select: (data) => data || {}
  });
  const putMutation = useMutation(async (body) => {
    await put(endpoint, body);
  }, {
    onSuccess() {
      trackUsage("didEditMediaLibraryConfig");
      config.refetch();
    },
    onError() {
      return toggleNotification({
        type: "danger",
        message: formatMessage({
          id: "notification.error"
        })
      });
    }
  });
  return {
    config,
    mutateConfig: putMutation
  };
};

export {
  useConfig
};
//# sourceMappingURL=chunk-UCNCKMDM.js.map
