{"version": 3, "sources": ["../../../@strapi/upload/admin/src/hooks/useConfig.ts"], "sourcesContent": ["import { useTracking, useNotification, useFetchClient } from '@strapi/admin/strapi-admin';\nimport { useIntl } from 'react-intl';\nimport { useMutation, useQuery, UseMutationResult, UseQueryResult } from 'react-query';\n\nimport { GetConfiguration, UpdateConfiguration } from '../../../shared/contracts/configuration';\nimport { pluginId } from '../pluginId';\n\nconst endpoint = `/${pluginId}/configuration`;\nconst queryKey = [pluginId, 'configuration'];\n\nexport const useConfig = () => {\n  const { trackUsage } = useTracking();\n  const { formatMessage } = useIntl();\n  const { toggleNotification } = useNotification();\n  const { get, put } = useFetchClient();\n\n  const config: UseQueryResult<\n    GetConfiguration.Response['data']['data'] | GetConfiguration.Response['error']\n  > = useQuery(\n    queryKey,\n    async () => {\n      const res: GetConfiguration.Response = await get(endpoint);\n\n      return res.data.data;\n    },\n    {\n      onError() {\n        return toggleNotification({\n          type: 'danger',\n          message: formatMessage({ id: 'notification.error' }),\n        });\n      },\n      /**\n       * We're cementing that we always expect an object to be returned.\n       */\n      select: (data) => data || {},\n    }\n  );\n\n  const putMutation: UseMutationResult<\n    void,\n    UpdateConfiguration.Response['error'],\n    UpdateConfiguration.Request['body']\n  > = useMutation(\n    async (body) => {\n      await put<UpdateConfiguration.Response>(endpoint, body);\n    },\n    {\n      onSuccess() {\n        trackUsage('didEditMediaLibraryConfig');\n        config.refetch();\n      },\n      onError() {\n        return toggleNotification({\n          type: 'danger',\n          message: formatMessage({ id: 'notification.error' }),\n        });\n      },\n    }\n  );\n\n  return {\n    config,\n    mutateConfig: putMutation,\n  };\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAOA,IAAMA,WAAW,IAAIC,QAAAA;AACrB,IAAMC,WAAW;EAACD;EAAU;AAAgB;IAE/BE,YAAY,MAAA;AACvB,QAAM,EAAEC,WAAU,IAAKC,YAAAA;AACvB,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,EAAEC,KAAKC,IAAG,IAAKC,eAAAA;AAErB,QAAMC,SAEFC,SACFZ,UACA,YAAA;AACE,UAAMa,MAAiC,MAAML,IAAIV,QAAAA;AAEjD,WAAOe,IAAIC,KAAKA;KAElB;IACEC,UAAAA;AACE,aAAOT,mBAAmB;QACxBU,MAAM;QACNC,SAASb,cAAc;UAAEc,IAAI;QAAqB,CAAA;MACpD,CAAA;IACF;;;;IAIAC,QAAQ,CAACL,SAASA,QAAQ,CAAA;EAC5B,CAAA;AAGF,QAAMM,cAIFC,YACF,OAAOC,SAAAA;AACL,UAAMb,IAAkCX,UAAUwB,IAAAA;KAEpD;IACEC,YAAAA;AACErB,iBAAW,2BAAA;AACXS,aAAOa,QAAO;IAChB;IACAT,UAAAA;AACE,aAAOT,mBAAmB;QACxBU,MAAM;QACNC,SAASb,cAAc;UAAEc,IAAI;QAAqB,CAAA;MACpD,CAAA;IACF;EACF,CAAA;AAGF,SAAO;IACLP;IACAc,cAAcL;EAChB;AACF;", "names": ["endpoint", "pluginId", "query<PERSON><PERSON>", "useConfig", "trackUsage", "useTracking", "formatMessage", "useIntl", "toggleNotification", "useNotification", "get", "put", "useFetchClient", "config", "useQuery", "res", "data", "onError", "type", "message", "id", "select", "putMutation", "useMutation", "body", "onSuccess", "refetch", "mutateConfig"]}