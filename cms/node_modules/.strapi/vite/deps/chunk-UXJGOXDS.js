import {
  useCreateWorkflowMutation,
  useDeleteWorkflowMutation,
  useGetWorkflowsQuery,
  useUpdateWorkflowMutation
} from "./chunk-HXNPIIH5.js";
import {
  useDragLayer
} from "./chunk-HN5HEQBI.js";
import {
  Layouts
} from "./chunk-IL5LQ6CF.js";
import {
  Page,
  useAPIErrorHandler
} from "./chunk-3UVBSCPH.js";
import {
  useNotification
} from "./chunk-4EY5FDQG.js";
import {
  Box,
  Flex,
  Typography,
  useIntl
} from "./chunk-4YYUDJZ2.js";
import {
  ForwardRef$4T
} from "./chunk-QW5ZDDTU.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/review-workflows/dist/admin/routes/settings/components/Layout.mjs
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);

// node_modules/@strapi/review-workflows/dist/admin/routes/settings/constants.mjs
var DRAG_DROP_TYPES = {
  STAGE: "stage"
};

// node_modules/@strapi/review-workflows/dist/admin/routes/settings/components/StageDragPreview.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var StageDragPreview = ({ name }) => {
  return (0, import_jsx_runtime.jsxs)(Flex, {
    background: "primary100",
    borderStyle: "dashed",
    borderColor: "primary600",
    borderWidth: "1px",
    gap: 3,
    hasRadius: true,
    padding: 3,
    shadow: "tableShadow",
    width: "30rem",
    children: [
      (0, import_jsx_runtime.jsx)(Flex, {
        alignItems: "center",
        background: "neutral200",
        borderRadius: "50%",
        height: 6,
        justifyContent: "center",
        width: 6,
        children: (0, import_jsx_runtime.jsx)(ForwardRef$4T, {
          width: "0.8rem",
          fill: "neutral600"
        })
      }),
      (0, import_jsx_runtime.jsx)(Typography, {
        fontWeight: "bold",
        children: name
      })
    ]
  });
};

// node_modules/@strapi/review-workflows/dist/admin/routes/settings/components/Layout.mjs
function getStyle(initialOffset, currentOffset, mouseOffset) {
  if (!initialOffset || !currentOffset || !mouseOffset) {
    return {
      display: "none"
    };
  }
  const { x, y } = mouseOffset;
  return {
    transform: `translate(${x}px, ${y}px)`
  };
}
var DragLayerRendered = () => {
  const { itemType, isDragging, item, initialOffset, currentOffset, mouseOffset } = useDragLayer((monitor) => ({
    item: monitor.getItem(),
    itemType: monitor.getItemType(),
    initialOffset: monitor.getInitialSourceClientOffset(),
    currentOffset: monitor.getSourceClientOffset(),
    isDragging: monitor.isDragging(),
    mouseOffset: monitor.getClientOffset()
  }));
  if (!isDragging || itemType !== DRAG_DROP_TYPES.STAGE) {
    return null;
  }
  return (0, import_jsx_runtime2.jsx)(Box, {
    height: "100%",
    left: 0,
    position: "fixed",
    pointerEvents: "none",
    top: 0,
    zIndex: 100,
    width: "100%",
    children: (0, import_jsx_runtime2.jsxs)(Box, {
      style: getStyle(initialOffset, currentOffset, mouseOffset),
      children: [
        (0, import_jsx_runtime2.jsx)(StageDragPreview, {
          name: typeof item.item === "string" ? item.item : null
        }),
        ";"
      ]
    })
  });
};
var Root = ({ children }) => {
  return (0, import_jsx_runtime2.jsx)(Page.Main, {
    children: (0, import_jsx_runtime2.jsx)(Layouts.Content, {
      children
    })
  });
};
var Header = ({ title, subtitle, navigationAction, primaryAction }) => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime2.jsxs)(import_jsx_runtime2.Fragment, {
    children: [
      (0, import_jsx_runtime2.jsx)(Page.Title, {
        children: formatMessage({
          id: "Settings.PageTitle",
          defaultMessage: "Settings - {name}"
        }, {
          name: title
        })
      }),
      (0, import_jsx_runtime2.jsx)(Layouts.BaseHeader, {
        navigationAction,
        primaryAction,
        title,
        subtitle
      })
    ]
  });
};

// node_modules/@strapi/review-workflows/dist/admin/routes/settings/hooks/useReviewWorkflows.mjs
var React = __toESM(require_react(), 1);
var DEFAULT_UNEXPECTED_ERROR_MSG = {
  id: "notification.error",
  defaultMessage: "An error occurred, please try again"
};
var useReviewWorkflows = (params = {}) => {
  const { toggleNotification } = useNotification();
  const { formatMessage } = useIntl();
  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();
  const { skip = false, ...queryParams } = params;
  const { data, isLoading, error } = useGetWorkflowsQuery({
    ...queryParams
  }, {
    skip
  });
  React.useEffect(() => {
    if (error) {
      toggleNotification({
        type: "danger",
        message: formatAPIError(error)
      });
    }
  }, [
    error,
    formatAPIError,
    toggleNotification
  ]);
  const [createWorkflow] = useCreateWorkflowMutation();
  const create = React.useCallback(async (data2) => {
    try {
      const res = await createWorkflow({
        data: data2
      });
      if ("error" in res) {
        toggleNotification({
          type: "danger",
          message: formatAPIError(res.error)
        });
        return res;
      }
      toggleNotification({
        type: "success",
        message: formatMessage({
          id: "actions.created",
          defaultMessage: "Created workflow"
        })
      });
      return res;
    } catch (err) {
      toggleNotification({
        type: "danger",
        message: formatMessage(DEFAULT_UNEXPECTED_ERROR_MSG)
      });
      throw err;
    }
  }, [
    createWorkflow,
    formatAPIError,
    formatMessage,
    toggleNotification
  ]);
  const [updateWorkflow] = useUpdateWorkflowMutation();
  const update = React.useCallback(async (id, data2) => {
    try {
      const res = await updateWorkflow({
        id,
        data: data2
      });
      if ("error" in res) {
        toggleNotification({
          type: "danger",
          message: formatAPIError(res.error)
        });
        return res;
      }
      toggleNotification({
        type: "success",
        message: formatMessage({
          id: "actions.updated",
          defaultMessage: "Updated workflow"
        })
      });
      return res;
    } catch (err) {
      toggleNotification({
        type: "danger",
        message: formatMessage(DEFAULT_UNEXPECTED_ERROR_MSG)
      });
      throw err;
    }
  }, [
    formatAPIError,
    formatMessage,
    toggleNotification,
    updateWorkflow
  ]);
  const [deleteWorkflow] = useDeleteWorkflowMutation();
  const deleteAction = React.useCallback(async (id) => {
    try {
      const res = await deleteWorkflow({
        id
      });
      if ("error" in res) {
        toggleNotification({
          type: "danger",
          message: formatAPIError(res.error)
        });
        return;
      }
      toggleNotification({
        type: "success",
        message: formatMessage({
          id: "actions.deleted",
          defaultMessage: "Deleted workflow"
        })
      });
      return res.data;
    } catch (err) {
      toggleNotification({
        type: "danger",
        message: formatMessage(DEFAULT_UNEXPECTED_ERROR_MSG)
      });
      throw err;
    }
  }, [
    deleteWorkflow,
    formatAPIError,
    formatMessage,
    toggleNotification
  ]);
  const { workflows = [], meta } = data ?? {};
  return {
    // meta contains e.g. the total of all workflows. we can not use
    // the pagination object here, because the list is not paginated.
    meta,
    workflows,
    isLoading,
    error,
    create,
    delete: deleteAction,
    update
  };
};

export {
  DRAG_DROP_TYPES,
  DragLayerRendered,
  Root,
  Header,
  useReviewWorkflows
};
//# sourceMappingURL=chunk-UXJGOXDS.js.map
