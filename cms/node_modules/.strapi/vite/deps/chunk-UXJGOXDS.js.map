{"version": 3, "sources": ["../../../@strapi/review-workflows/admin/src/routes/settings/constants.ts", "../../../@strapi/review-workflows/admin/src/routes/settings/components/StageDragPreview.tsx", "../../../@strapi/review-workflows/admin/src/routes/settings/components/Layout.tsx", "../../../@strapi/review-workflows/admin/src/routes/settings/hooks/useReviewWorkflows.ts"], "sourcesContent": ["export type DragDropTypes = 'stage';\n\nexport const DRAG_DROP_TYPES: Record<Uppercase<DragDropTypes>, DragDropTypes> = {\n  STAGE: 'stage',\n};\n", "import { Flex, Typography } from '@strapi/design-system';\nimport { CaretDown } from '@strapi/icons';\n\ninterface StageDragPreviewType {\n  name: string | null;\n}\n\nconst StageDragPreview = ({ name }: StageDragPreviewType) => {\n  return (\n    <Flex\n      background=\"primary100\"\n      borderStyle=\"dashed\"\n      borderColor=\"primary600\"\n      borderWidth=\"1px\"\n      gap={3}\n      hasRadius\n      padding={3}\n      shadow=\"tableShadow\"\n      width=\"30rem\"\n    >\n      <Flex\n        alignItems=\"center\"\n        background=\"neutral200\"\n        borderRadius=\"50%\"\n        height={6}\n        justifyContent=\"center\"\n        width={6}\n      >\n        <CaretDown width=\"0.8rem\" fill=\"neutral600\" />\n      </Flex>\n\n      <Typography fontWeight=\"bold\">{name}</Typography>\n    </Flex>\n  );\n};\n\nexport { StageDragPreview };\nexport type { StageDragPreviewType };\n", "import * as React from 'react';\n\nimport { Page, Layouts } from '@strapi/admin/strapi-admin';\nimport { Box } from '@strapi/design-system';\nimport { XYCoord, useDragLayer } from 'react-dnd';\nimport { useIntl } from 'react-intl';\n\nimport { DRAG_DROP_TYPES } from '../constants';\n\nimport { StageDragPreview } from './StageDragPreview';\n\nfunction getStyle(\n  initialOffset: XYCoord | null,\n  currentOffset: XYCoord | null,\n  mouseOffset: XYCoord | null\n) {\n  if (!initialOffset || !currentOffset || !mouseOffset) {\n    return { display: 'none' };\n  }\n\n  const { x, y } = mouseOffset;\n\n  return {\n    transform: `translate(${x}px, ${y}px)`,\n  };\n}\n\nconst DragLayerRendered = () => {\n  const { itemType, isDragging, item, initialOffset, currentOffset, mouseOffset } = useDragLayer(\n    (monitor) => ({\n      item: monitor.getItem(),\n      itemType: monitor.getItemType(),\n      initialOffset: monitor.getInitialSourceClientOffset(),\n      currentOffset: monitor.getSourceClientOffset(),\n      isDragging: monitor.isDragging(),\n      mouseOffset: monitor.getClientOffset(),\n    })\n  );\n\n  if (!isDragging || itemType !== DRAG_DROP_TYPES.STAGE) {\n    return null;\n  }\n\n  return (\n    <Box\n      height=\"100%\"\n      left={0}\n      position=\"fixed\"\n      pointerEvents=\"none\"\n      top={0}\n      zIndex={100}\n      width=\"100%\"\n    >\n      <Box style={getStyle(initialOffset, currentOffset, mouseOffset)}>\n        <StageDragPreview name={typeof item.item === 'string' ? item.item : null} />;\n      </Box>\n    </Box>\n  );\n};\n\nconst Root: React.FC<React.PropsWithChildren> = ({ children }) => {\n  return (\n    <Page.Main>\n      <Layouts.Content>{children}</Layouts.Content>\n    </Page.Main>\n  );\n};\n\ninterface HeaderProps {\n  title: string;\n  navigationAction?: React.ReactNode;\n  primaryAction?: React.ReactNode;\n  secondaryAction?: React.ReactNode;\n  subtitle?: React.ReactNode;\n}\n\nconst Header: React.FC<HeaderProps> = ({ title, subtitle, navigationAction, primaryAction }) => {\n  const { formatMessage } = useIntl();\n  return (\n    <>\n      <Page.Title>\n        {formatMessage(\n          { id: 'Settings.PageTitle', defaultMessage: 'Settings - {name}' },\n          {\n            name: title,\n          }\n        )}\n      </Page.Title>\n      <Layouts.BaseHeader\n        navigationAction={navigationAction}\n        primaryAction={primaryAction}\n        title={title}\n        subtitle={subtitle}\n      />\n    </>\n  );\n};\n\nexport { DragLayerRendered, Header, Root };\n", "import * as React from 'react';\n\nimport { useAPIErrorHandler, useNotification } from '@strapi/admin/strapi-admin';\nimport { type MessageDescriptor, useIntl } from 'react-intl';\n\nimport {\n  GetWorkflowsParams,\n  useCreateWorkflowMutation,\n  useDeleteWorkflowMutation,\n  useGetWorkflowsQuery,\n  useUpdateWorkflowMutation,\n} from '../../../services/settings';\n\nimport type { Create, Update } from '../../../../../shared/contracts/review-workflows';\n\nconst DEFAULT_UNEXPECTED_ERROR_MSG = {\n  id: 'notification.error',\n  defaultMessage: 'An error occurred, please try again',\n} satisfies MessageDescriptor;\n\ntype UseReviewWorkflowsArgs = GetWorkflowsParams & {\n  skip?: boolean;\n};\n\nconst useReviewWorkflows = (params: UseReviewWorkflowsArgs = {}) => {\n  const { toggleNotification } = useNotification();\n  const { formatMessage } = useIntl();\n  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();\n\n  const { skip = false, ...queryParams } = params;\n\n  const { data, isLoading, error } = useGetWorkflowsQuery(\n    {\n      ...queryParams,\n    },\n    {\n      skip,\n    }\n  );\n\n  React.useEffect(() => {\n    if (error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatAPIError(error),\n      });\n    }\n  }, [error, formatAPIError, toggleNotification]);\n\n  const [createWorkflow] = useCreateWorkflowMutation();\n  const create = React.useCallback(\n    async (data: Create.Request['body']['data']) => {\n      try {\n        const res = await createWorkflow({ data });\n\n        if ('error' in res) {\n          toggleNotification({\n            type: 'danger',\n            message: formatAPIError(res.error),\n          });\n\n          return res;\n        }\n\n        toggleNotification({\n          type: 'success',\n          message: formatMessage({ id: 'actions.created', defaultMessage: 'Created workflow' }),\n        });\n\n        return res;\n      } catch (err) {\n        toggleNotification({\n          type: 'danger',\n          message: formatMessage(DEFAULT_UNEXPECTED_ERROR_MSG),\n        });\n\n        throw err;\n      }\n    },\n    [createWorkflow, formatAPIError, formatMessage, toggleNotification]\n  );\n\n  const [updateWorkflow] = useUpdateWorkflowMutation();\n  const update = React.useCallback(\n    async (id: string, data: Update.Request['body']['data']) => {\n      try {\n        const res = await updateWorkflow({ id, data });\n\n        if ('error' in res) {\n          toggleNotification({\n            type: 'danger',\n            message: formatAPIError(res.error),\n          });\n\n          return res;\n        }\n\n        toggleNotification({\n          type: 'success',\n          message: formatMessage({ id: 'actions.updated', defaultMessage: 'Updated workflow' }),\n        });\n\n        return res;\n      } catch (err) {\n        toggleNotification({\n          type: 'danger',\n          message: formatMessage(DEFAULT_UNEXPECTED_ERROR_MSG),\n        });\n\n        throw err;\n      }\n    },\n    [formatAPIError, formatMessage, toggleNotification, updateWorkflow]\n  );\n\n  const [deleteWorkflow] = useDeleteWorkflowMutation();\n  const deleteAction = React.useCallback(\n    async (id: string) => {\n      try {\n        const res = await deleteWorkflow({ id });\n\n        if ('error' in res) {\n          toggleNotification({\n            type: 'danger',\n            message: formatAPIError(res.error),\n          });\n\n          return;\n        }\n\n        toggleNotification({\n          type: 'success',\n          message: formatMessage({ id: 'actions.deleted', defaultMessage: 'Deleted workflow' }),\n        });\n\n        return res.data;\n      } catch (err) {\n        toggleNotification({\n          type: 'danger',\n          message: formatMessage(DEFAULT_UNEXPECTED_ERROR_MSG),\n        });\n\n        throw err;\n      }\n    },\n    [deleteWorkflow, formatAPIError, formatMessage, toggleNotification]\n  );\n\n  const { workflows = [], meta } = data ?? {};\n\n  return {\n    // meta contains e.g. the total of all workflows. we can not use\n    // the pagination object here, because the list is not paginated.\n    meta,\n    workflows,\n    isLoading,\n    error,\n    create,\n    delete: deleteAction,\n    update,\n  };\n};\n\nexport { useReviewWorkflows };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAEaA,kBAAmE;EAC9EC,OAAO;AACT;;;;ACGA,IAAMC,mBAAmB,CAAC,EAAEC,KAAI,MAAwB;AACtD,aACEC,yBAACC,MAAAA;IACCC,YAAW;IACXC,aAAY;IACZC,aAAY;IACZC,aAAY;IACZC,KAAK;IACLC,WAAS;IACTC,SAAS;IACTC,QAAO;IACPC,OAAM;;UAENC,wBAACV,MAAAA;QACCW,YAAW;QACXV,YAAW;QACXW,cAAa;QACbC,QAAQ;QACRC,gBAAe;QACfL,OAAO;QAEP,cAAAC,wBAACK,eAAAA;UAAUN,OAAM;UAASO,MAAK;;;UAGjCN,wBAACO,YAAAA;QAAWC,YAAW;QAAQpB,UAAAA;;;;AAGrC;;;ACvBA,SAASqB,SACPC,eACAC,eACAC,aAA2B;AAE3B,MAAI,CAACF,iBAAiB,CAACC,iBAAiB,CAACC,aAAa;AACpD,WAAO;MAAEC,SAAS;IAAO;EAC3B;AAEA,QAAM,EAAEC,GAAGC,EAAC,IAAKH;AAEjB,SAAO;IACLI,WAAW,aAAaF,CAAAA,OAAQC,CAAAA;EAClC;AACF;AAEA,IAAME,oBAAoB,MAAA;AACxB,QAAM,EAAEC,UAAUC,YAAYC,MAAMV,eAAeC,eAAeC,YAAW,IAAKS,aAChF,CAACC,aAAa;IACZF,MAAME,QAAQC,QAAO;IACrBL,UAAUI,QAAQE,YAAW;IAC7Bd,eAAeY,QAAQG,6BAA4B;IACnDd,eAAeW,QAAQI,sBAAqB;IAC5CP,YAAYG,QAAQH,WAAU;IAC9BP,aAAaU,QAAQK,gBAAe;IACtC;AAGF,MAAI,CAACR,cAAcD,aAAaU,gBAAgBC,OAAO;AACrD,WAAO;EACT;AAEA,aACEC,yBAACC,KAAAA;IACCC,QAAO;IACPC,MAAM;IACNC,UAAS;IACTC,eAAc;IACdC,KAAK;IACLC,QAAQ;IACRC,OAAM;IAEN,cAAAC,0BAACR,KAAAA;MAAIS,OAAO/B,SAASC,eAAeC,eAAeC,WAAAA;;YACjDkB,yBAACW,kBAAAA;UAAiBC,MAAM,OAAOtB,KAAKA,SAAS,WAAWA,KAAKA,OAAO;;QAAQ;;;;AAIpF;AAEA,IAAMuB,OAA0C,CAAC,EAAEC,SAAQ,MAAE;AAC3D,aACEd,yBAACe,KAAKC,MAAI;kBACRhB,yBAACiB,QAAQC,SAAO;MAAEJ;;;AAGxB;AAUMK,IAAAA,SAAgC,CAAC,EAAEC,OAAOC,UAAUC,kBAAkBC,cAAa,MAAE;AACzF,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,aACEhB,0BAAAiB,8BAAA;;UACE1B,yBAACe,KAAKY,OAAK;kBACRH,cACC;UAAEI,IAAI;UAAsBC,gBAAgB;WAC5C;UACEjB,MAAMQ;QACR,CAAA;;UAGJpB,yBAACiB,QAAQa,YAAU;QACjBR;QACAC;QACAH;QACAC;;;;AAIR;;;;ACjFA,IAAMU,+BAA+B;EACnCC,IAAI;EACJC,gBAAgB;AAClB;AAMA,IAAMC,qBAAqB,CAACC,SAAiC,CAAA,MAAE;AAC7D,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,yBAAyBC,eAAc,IAAKC,mBAAAA;AAEpD,QAAM,EAAEC,OAAO,OAAO,GAAGC,YAAAA,IAAgBT;AAEzC,QAAM,EAAEU,MAAMC,WAAWC,MAAK,IAAKC,qBACjC;IACE,GAAGJ;KAEL;IACED;EACF,CAAA;AAGFM,EAAMC,gBAAU,MAAA;AACd,QAAIH,OAAO;AACTX,yBAAmB;QACjBe,MAAM;QACNC,SAASX,eAAeM,KAAAA;MAC1B,CAAA;IACF;KACC;IAACA;IAAON;IAAgBL;EAAmB,CAAA;AAE9C,QAAM,CAACiB,cAAAA,IAAkBC,0BAAAA;AACzB,QAAMC,SAAeC,kBACnB,OAAOX,UAAAA;AACL,QAAI;AACF,YAAMY,MAAM,MAAMJ,eAAe;QAAER,MAAAA;MAAK,CAAA;AAExC,UAAI,WAAWY,KAAK;AAClBrB,2BAAmB;UACjBe,MAAM;UACNC,SAASX,eAAegB,IAAIV,KAAK;QACnC,CAAA;AAEA,eAAOU;MACT;AAEArB,yBAAmB;QACjBe,MAAM;QACNC,SAASd,cAAc;UAAEN,IAAI;UAAmBC,gBAAgB;QAAmB,CAAA;MACrF,CAAA;AAEA,aAAOwB;IACT,SAASC,KAAK;AACZtB,yBAAmB;QACjBe,MAAM;QACNC,SAASd,cAAcP,4BAAAA;MACzB,CAAA;AAEA,YAAM2B;IACR;KAEF;IAACL;IAAgBZ;IAAgBH;IAAeF;EAAmB,CAAA;AAGrE,QAAM,CAACuB,cAAAA,IAAkBC,0BAAAA;AACzB,QAAMC,SAAeL,kBACnB,OAAOxB,IAAYa,UAAAA;AACjB,QAAI;AACF,YAAMY,MAAM,MAAME,eAAe;QAAE3B;QAAIa,MAAAA;MAAK,CAAA;AAE5C,UAAI,WAAWY,KAAK;AAClBrB,2BAAmB;UACjBe,MAAM;UACNC,SAASX,eAAegB,IAAIV,KAAK;QACnC,CAAA;AAEA,eAAOU;MACT;AAEArB,yBAAmB;QACjBe,MAAM;QACNC,SAASd,cAAc;UAAEN,IAAI;UAAmBC,gBAAgB;QAAmB,CAAA;MACrF,CAAA;AAEA,aAAOwB;IACT,SAASC,KAAK;AACZtB,yBAAmB;QACjBe,MAAM;QACNC,SAASd,cAAcP,4BAAAA;MACzB,CAAA;AAEA,YAAM2B;IACR;KAEF;IAACjB;IAAgBH;IAAeF;IAAoBuB;EAAe,CAAA;AAGrE,QAAM,CAACG,cAAAA,IAAkBC,0BAAAA;AACzB,QAAMC,eAAqBR,kBACzB,OAAOxB,OAAAA;AACL,QAAI;AACF,YAAMyB,MAAM,MAAMK,eAAe;QAAE9B;MAAG,CAAA;AAEtC,UAAI,WAAWyB,KAAK;AAClBrB,2BAAmB;UACjBe,MAAM;UACNC,SAASX,eAAegB,IAAIV,KAAK;QACnC,CAAA;AAEA;MACF;AAEAX,yBAAmB;QACjBe,MAAM;QACNC,SAASd,cAAc;UAAEN,IAAI;UAAmBC,gBAAgB;QAAmB,CAAA;MACrF,CAAA;AAEA,aAAOwB,IAAIZ;IACb,SAASa,KAAK;AACZtB,yBAAmB;QACjBe,MAAM;QACNC,SAASd,cAAcP,4BAAAA;MACzB,CAAA;AAEA,YAAM2B;IACR;KAEF;IAACI;IAAgBrB;IAAgBH;IAAeF;EAAmB,CAAA;AAGrE,QAAM,EAAE6B,YAAY,CAAA,GAAIC,KAAI,IAAKrB,QAAQ,CAAA;AAEzC,SAAO;;;IAGLqB;IACAD;IACAnB;IACAC;IACAQ;IACAY,QAAQH;IACRH;EACF;AACF;", "names": ["DRAG_DROP_TYPES", "STAGE", "StageDragPreview", "name", "_jsxs", "Flex", "background", "borderStyle", "borderColor", "borderWidth", "gap", "hasRadius", "padding", "shadow", "width", "_jsx", "alignItems", "borderRadius", "height", "justifyContent", "CaretDown", "fill", "Typography", "fontWeight", "getStyle", "initialOffset", "currentOffset", "mouseOffset", "display", "x", "y", "transform", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "itemType", "isDragging", "item", "useDragLayer", "monitor", "getItem", "getItemType", "getInitialSourceClientOffset", "getSourceClientOffset", "getClientOffset", "DRAG_DROP_TYPES", "STAGE", "_jsx", "Box", "height", "left", "position", "pointerEvents", "top", "zIndex", "width", "_jsxs", "style", "StageDragPreview", "name", "Root", "children", "Page", "Main", "Layouts", "Content", "Header", "title", "subtitle", "navigationAction", "primaryAction", "formatMessage", "useIntl", "_Fragment", "Title", "id", "defaultMessage", "BaseHeader", "DEFAULT_UNEXPECTED_ERROR_MSG", "id", "defaultMessage", "useReviewWorkflows", "params", "toggleNotification", "useNotification", "formatMessage", "useIntl", "_unstableFormatAPIError", "formatAPIError", "useAPIErrorHandler", "skip", "queryParams", "data", "isLoading", "error", "useGetWorkflowsQuery", "React", "useEffect", "type", "message", "createWorkflow", "useCreateWorkflowMutation", "create", "useCallback", "res", "err", "updateWorkflow", "useUpdateWorkflowMutation", "update", "deleteWorkflow", "useDeleteWorkflowMutation", "deleteAction", "workflows", "meta", "delete"]}