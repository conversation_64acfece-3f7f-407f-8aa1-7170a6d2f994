import {
  require_isEqual
} from "./chunk-VYSYYPOB.js";
import {
  getIn,
  setIn
} from "./chunk-BHLYCXQ7.js";
import {
  createContext
} from "./chunk-BLJUR3JO.js";
import {
  fn
} from "./chunk-QOUV5O5E.js";
import {
  Box,
  Button,
  Dialog,
  useCallbackRef,
  useComposedRefs,
  useIntl
} from "./chunk-4YYUDJZ2.js";
import {
  useBlocker
} from "./chunk-S65ZWNEO.js";
import {
  ForwardRef$3
} from "./chunk-QW5ZDDTU.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/fractional-indexing/src/index.js
var BASE_62_DIGITS = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
function midpoint(a, b, digits) {
  const zero = digits[0];
  if (b != null && a >= b) {
    throw new Error(a + " >= " + b);
  }
  if (a.slice(-1) === zero || b && b.slice(-1) === zero) {
    throw new Error("trailing zero");
  }
  if (b) {
    let n = 0;
    while ((a[n] || zero) === b[n]) {
      n++;
    }
    if (n > 0) {
      return b.slice(0, n) + midpoint(a.slice(n), b.slice(n), digits);
    }
  }
  const digitA = a ? digits.indexOf(a[0]) : 0;
  const digitB = b != null ? digits.indexOf(b[0]) : digits.length;
  if (digitB - digitA > 1) {
    const midDigit = Math.round(0.5 * (digitA + digitB));
    return digits[midDigit];
  } else {
    if (b && b.length > 1) {
      return b.slice(0, 1);
    } else {
      return digits[digitA] + midpoint(a.slice(1), null, digits);
    }
  }
}
function validateInteger(int) {
  if (int.length !== getIntegerLength(int[0])) {
    throw new Error("invalid integer part of order key: " + int);
  }
}
function getIntegerLength(head) {
  if (head >= "a" && head <= "z") {
    return head.charCodeAt(0) - "a".charCodeAt(0) + 2;
  } else if (head >= "A" && head <= "Z") {
    return "Z".charCodeAt(0) - head.charCodeAt(0) + 2;
  } else {
    throw new Error("invalid order key head: " + head);
  }
}
function getIntegerPart(key) {
  const integerPartLength = getIntegerLength(key[0]);
  if (integerPartLength > key.length) {
    throw new Error("invalid order key: " + key);
  }
  return key.slice(0, integerPartLength);
}
function validateOrderKey(key, digits) {
  if (key === "A" + digits[0].repeat(26)) {
    throw new Error("invalid order key: " + key);
  }
  const i = getIntegerPart(key);
  const f = key.slice(i.length);
  if (f.slice(-1) === digits[0]) {
    throw new Error("invalid order key: " + key);
  }
}
function incrementInteger(x, digits) {
  validateInteger(x);
  const [head, ...digs] = x.split("");
  let carry = true;
  for (let i = digs.length - 1; carry && i >= 0; i--) {
    const d = digits.indexOf(digs[i]) + 1;
    if (d === digits.length) {
      digs[i] = digits[0];
    } else {
      digs[i] = digits[d];
      carry = false;
    }
  }
  if (carry) {
    if (head === "Z") {
      return "a" + digits[0];
    }
    if (head === "z") {
      return null;
    }
    const h = String.fromCharCode(head.charCodeAt(0) + 1);
    if (h > "a") {
      digs.push(digits[0]);
    } else {
      digs.pop();
    }
    return h + digs.join("");
  } else {
    return head + digs.join("");
  }
}
function decrementInteger(x, digits) {
  validateInteger(x);
  const [head, ...digs] = x.split("");
  let borrow = true;
  for (let i = digs.length - 1; borrow && i >= 0; i--) {
    const d = digits.indexOf(digs[i]) - 1;
    if (d === -1) {
      digs[i] = digits.slice(-1);
    } else {
      digs[i] = digits[d];
      borrow = false;
    }
  }
  if (borrow) {
    if (head === "a") {
      return "Z" + digits.slice(-1);
    }
    if (head === "A") {
      return null;
    }
    const h = String.fromCharCode(head.charCodeAt(0) - 1);
    if (h < "Z") {
      digs.push(digits.slice(-1));
    } else {
      digs.pop();
    }
    return h + digs.join("");
  } else {
    return head + digs.join("");
  }
}
function generateKeyBetween(a, b, digits = BASE_62_DIGITS) {
  if (a != null) {
    validateOrderKey(a, digits);
  }
  if (b != null) {
    validateOrderKey(b, digits);
  }
  if (a != null && b != null && a >= b) {
    throw new Error(a + " >= " + b);
  }
  if (a == null) {
    if (b == null) {
      return "a" + digits[0];
    }
    const ib2 = getIntegerPart(b);
    const fb2 = b.slice(ib2.length);
    if (ib2 === "A" + digits[0].repeat(26)) {
      return ib2 + midpoint("", fb2, digits);
    }
    if (ib2 < b) {
      return ib2;
    }
    const res = decrementInteger(ib2, digits);
    if (res == null) {
      throw new Error("cannot decrement any more");
    }
    return res;
  }
  if (b == null) {
    const ia2 = getIntegerPart(a);
    const fa2 = a.slice(ia2.length);
    const i2 = incrementInteger(ia2, digits);
    return i2 == null ? ia2 + midpoint(fa2, null, digits) : i2;
  }
  const ia = getIntegerPart(a);
  const fa = a.slice(ia.length);
  const ib = getIntegerPart(b);
  const fb = b.slice(ib.length);
  if (ia === ib) {
    return ia + midpoint(fa, fb, digits);
  }
  const i = incrementInteger(ia, digits);
  if (i == null) {
    throw new Error("cannot increment any more");
  }
  if (i < b) {
    return i;
  }
  return ia + midpoint(fa, null, digits);
}
function generateNKeysBetween(a, b, n, digits = BASE_62_DIGITS) {
  if (n === 0) {
    return [];
  }
  if (n === 1) {
    return [generateKeyBetween(a, b, digits)];
  }
  if (b == null) {
    let c2 = generateKeyBetween(a, b, digits);
    const result = [c2];
    for (let i = 0; i < n - 1; i++) {
      c2 = generateKeyBetween(c2, b, digits);
      result.push(c2);
    }
    return result;
  }
  if (a == null) {
    let c2 = generateKeyBetween(a, b, digits);
    const result = [c2];
    for (let i = 0; i < n - 1; i++) {
      c2 = generateKeyBetween(a, c2, digits);
      result.push(c2);
    }
    result.reverse();
    return result;
  }
  const mid = Math.floor(n / 2);
  const c = generateKeyBetween(a, b, digits);
  return [
    ...generateNKeysBetween(a, c, mid, digits),
    c,
    ...generateNKeysBetween(c, b, n - mid - 1, digits)
  ];
}

// node_modules/@strapi/admin/dist/admin/admin/src/components/Form.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var import_isEqual = __toESM(require_isEqual(), 1);
var ERR_MSG = "The Form Component has not been initialised, ensure you are using this hook within a Form component";
var [FormProvider, useForm] = createContext("Form", {
  disabled: false,
  errors: {},
  initialValues: {},
  isSubmitting: false,
  modified: false,
  addFieldRow: () => {
    throw new Error(ERR_MSG);
  },
  moveFieldRow: () => {
    throw new Error(ERR_MSG);
  },
  onChange: () => {
    throw new Error(ERR_MSG);
  },
  removeFieldRow: () => {
    throw new Error(ERR_MSG);
  },
  resetForm: () => {
    throw new Error(ERR_MSG);
  },
  setErrors: () => {
    throw new Error(ERR_MSG);
  },
  setValues: () => {
    throw new Error(ERR_MSG);
  },
  setSubmitting: () => {
    throw new Error(ERR_MSG);
  },
  validate: async () => {
    throw new Error(ERR_MSG);
  },
  values: {}
});
var Form = React.forwardRef(({ disabled = false, method, onSubmit, initialErrors, ...props }, ref) => {
  const formRef = React.useRef(null);
  const initialValues = React.useRef(props.initialValues ?? {});
  const [state, dispatch] = React.useReducer(reducer, {
    errors: initialErrors ?? {},
    isSubmitting: false,
    values: props.initialValues ?? {}
  });
  React.useEffect(() => {
    if (!(0, import_isEqual.default)(initialValues.current, props.initialValues)) {
      initialValues.current = props.initialValues ?? {};
      dispatch({
        type: "SET_INITIAL_VALUES",
        payload: props.initialValues ?? {}
      });
    }
  }, [
    props.initialValues
  ]);
  const setErrors = React.useCallback((errors) => {
    dispatch({
      type: "SET_ERRORS",
      payload: errors
    });
  }, []);
  const setValues = React.useCallback((values) => {
    dispatch({
      type: "SET_VALUES",
      payload: values
    });
  }, []);
  React.useEffect(() => {
    if (Object.keys(state.errors).length === 0) return;
    const ref2 = setTimeout(() => {
      const [firstError] = formRef.current.querySelectorAll("[data-strapi-field-error]");
      if (firstError) {
        const errorId = firstError.getAttribute("id");
        const formElementInError = formRef.current.querySelector(`[aria-describedby="${errorId}"]`);
        if (formElementInError && formElementInError instanceof HTMLElement) {
          formElementInError.focus();
        }
      }
    });
    return () => clearTimeout(ref2);
  }, [
    state.errors
  ]);
  const validate = React.useCallback(async (shouldSetErrors = true, options = {}) => {
    setErrors({});
    if (!props.validationSchema && !props.validate) {
      return {
        data: state.values
      };
    }
    try {
      let data;
      if (props.validationSchema) {
        data = await props.validationSchema.validate(state.values, {
          abortEarly: false
        });
      } else if (props.validate) {
        data = await props.validate(state.values, options);
      } else {
        throw new Error("No validation schema or validate function provided");
      }
      return {
        data
      };
    } catch (err) {
      if (isErrorYupValidationError(err)) {
        const errors = getYupValidationErrors(err);
        if (shouldSetErrors) {
          setErrors(errors);
        }
        return {
          errors
        };
      } else {
        if (true) {
          console.warn(`Warning: An unhandled error was caught during validation in <Form validationSchema />`, err);
        }
        throw err;
      }
    }
  }, [
    props,
    setErrors,
    state.values
  ]);
  const handleSubmit = async (e) => {
    e.stopPropagation();
    e.preventDefault();
    if (!onSubmit) {
      return;
    }
    dispatch({
      type: "SUBMIT_ATTEMPT"
    });
    try {
      const { data, errors } = await validate();
      if (errors) {
        setErrors(errors);
        throw new Error("Submission failed");
      }
      await onSubmit(data, {
        setErrors,
        setValues,
        resetForm
      });
      dispatch({
        type: "SUBMIT_SUCCESS"
      });
    } catch (err) {
      dispatch({
        type: "SUBMIT_FAILURE"
      });
      if (err instanceof Error && err.message === "Submission failed") {
        return;
      }
    }
  };
  const modified = React.useMemo(() => !(0, import_isEqual.default)(initialValues.current, state.values), [
    state.values
  ]);
  const handleChange = useCallbackRef((eventOrPath, v) => {
    if (typeof eventOrPath === "string") {
      dispatch({
        type: "SET_FIELD_VALUE",
        payload: {
          field: eventOrPath,
          value: v
        }
      });
      return;
    }
    const target = eventOrPath.target || eventOrPath.currentTarget;
    const { type, name, id, value, options, multiple } = target;
    const field = name || id;
    if (!field && true) {
      console.warn(`\`onChange\` was called with an event, but you forgot to pass a \`name\` or \`id'\` attribute to your input. The field to update cannot be determined`);
    }
    let val;
    if (/number|range/.test(type)) {
      const parsed = parseFloat(value);
      val = isNaN(parsed) ? "" : parsed;
    } else if (/checkbox/.test(type)) {
      val = !getIn(state.values, field);
    } else if (options && multiple) {
      val = Array.from(options).filter((el) => el.selected).map((el) => el.value);
    } else {
      if (value === "") {
        val = null;
      } else {
        val = value;
      }
    }
    if (field) {
      dispatch({
        type: "SET_FIELD_VALUE",
        payload: {
          field,
          value: val
        }
      });
    }
  });
  const addFieldRow = React.useCallback((field, value, addAtIndex) => {
    dispatch({
      type: "ADD_FIELD_ROW",
      payload: {
        field,
        value,
        addAtIndex
      }
    });
  }, []);
  const removeFieldRow = React.useCallback((field, removeAtIndex) => {
    dispatch({
      type: "REMOVE_FIELD_ROW",
      payload: {
        field,
        removeAtIndex
      }
    });
  }, []);
  const moveFieldRow = React.useCallback((field, fromIndex, toIndex) => {
    dispatch({
      type: "MOVE_FIELD_ROW",
      payload: {
        field,
        fromIndex,
        toIndex
      }
    });
  }, []);
  const resetForm = React.useCallback(() => {
    dispatch({
      type: "RESET_FORM",
      payload: {
        errors: {},
        isSubmitting: false,
        values: initialValues.current
      }
    });
  }, []);
  const setSubmitting = React.useCallback((isSubmitting) => {
    dispatch({
      type: "SET_ISSUBMITTING",
      payload: isSubmitting
    });
  }, []);
  const composedRefs = useComposedRefs(formRef, ref);
  return (0, import_jsx_runtime.jsx)(Box, {
    tag: "form",
    ref: composedRefs,
    method,
    noValidate: true,
    onSubmit: handleSubmit,
    width: props.width,
    height: props.height,
    children: (0, import_jsx_runtime.jsx)(FormProvider, {
      disabled,
      onChange: handleChange,
      initialValues: initialValues.current,
      modified,
      addFieldRow,
      moveFieldRow,
      removeFieldRow,
      resetForm,
      setErrors,
      setValues,
      setSubmitting,
      validate,
      ...state,
      children: typeof props.children === "function" ? props.children({
        modified,
        disabled,
        onChange: handleChange,
        ...state,
        setErrors,
        resetForm
      }) : props.children
    })
  });
});
var isErrorYupValidationError = (err) => typeof err === "object" && err !== null && "name" in err && typeof err.name === "string" && err.name === "ValidationError";
var getYupValidationErrors = (err) => {
  let errors = {};
  if (err.inner) {
    if (err.inner.length === 0) {
      return setIn(errors, err.path, err.message);
    }
    for (const error of err.inner) {
      if (!getIn(errors, error.path)) {
        errors = setIn(errors, error.path, error.message);
      }
    }
  }
  return errors;
};
var reducer = (state, action) => fn(state, (draft) => {
  var _a, _b, _c, _d, _e, _f;
  switch (action.type) {
    case "SET_INITIAL_VALUES":
      draft.values = action.payload;
      break;
    case "SET_VALUES":
      draft.values = action.payload;
      break;
    case "SUBMIT_ATTEMPT":
      draft.isSubmitting = true;
      break;
    case "SUBMIT_FAILURE":
      draft.isSubmitting = false;
      break;
    case "SUBMIT_SUCCESS":
      draft.isSubmitting = false;
      break;
    case "SET_FIELD_VALUE":
      draft.values = setIn(state.values, action.payload.field, action.payload.value);
      break;
    case "ADD_FIELD_ROW": {
      const currentField = getIn(state.values, action.payload.field, []);
      let position = action.payload.addAtIndex;
      if (position === void 0) {
        position = currentField.length;
      } else if (position < 0) {
        position = 0;
      }
      const [key] = generateNKeysBetween(position > 0 ? (_a = currentField.at(position - 1)) == null ? void 0 : _a.__temp_key__ : null, (_b = currentField.at(position)) == null ? void 0 : _b.__temp_key__, 1);
      draft.values = setIn(state.values, action.payload.field, currentField.toSpliced(position, 0, {
        ...action.payload.value,
        __temp_key__: key
      }));
      break;
    }
    case "MOVE_FIELD_ROW": {
      const { field, fromIndex, toIndex } = action.payload;
      const currentField = [
        ...getIn(state.values, field, [])
      ];
      const currentRow = currentField[fromIndex];
      const startKey = fromIndex > toIndex ? (_c = currentField[toIndex - 1]) == null ? void 0 : _c.__temp_key__ : (_d = currentField[toIndex]) == null ? void 0 : _d.__temp_key__;
      const endKey = fromIndex > toIndex ? (_e = currentField[toIndex]) == null ? void 0 : _e.__temp_key__ : (_f = currentField[toIndex + 1]) == null ? void 0 : _f.__temp_key__;
      const [newKey] = generateNKeysBetween(startKey, endKey, 1);
      currentField.splice(fromIndex, 1);
      currentField.splice(toIndex, 0, {
        ...currentRow,
        __temp_key__: newKey
      });
      draft.values = setIn(state.values, field, currentField);
      break;
    }
    case "REMOVE_FIELD_ROW": {
      const currentField = getIn(state.values, action.payload.field, []);
      let position = action.payload.removeAtIndex;
      if (position === void 0) {
        position = currentField.length - 1;
      } else if (position < 0) {
        position = 0;
      }
      const newValue = setIn(currentField, position.toString(), void 0).filter((val) => val);
      draft.values = setIn(state.values, action.payload.field, newValue.length > 0 ? newValue : []);
      break;
    }
    case "SET_ERRORS":
      if (!(0, import_isEqual.default)(state.errors, action.payload)) {
        draft.errors = action.payload;
      }
      break;
    case "SET_ISSUBMITTING":
      draft.isSubmitting = action.payload;
      break;
    case "RESET_FORM":
      draft.values = action.payload.values;
      draft.errors = action.payload.errors;
      draft.isSubmitting = action.payload.isSubmitting;
      break;
  }
});
function useField(path) {
  const { formatMessage } = useIntl();
  const initialValue = useForm("useField", (state) => getIn(state.initialValues, path));
  const value = useForm("useField", (state) => getIn(state.values, path));
  const handleChange = useForm("useField", (state) => state.onChange);
  const rawError = useForm("useField", (state) => getIn(state.errors, path));
  const error = useForm("useField", (state) => {
    const error2 = getIn(state.errors, path);
    if (isErrorMessageDescriptor(error2)) {
      const { values, ...message } = error2;
      return formatMessage(message, values);
    }
    return error2;
  });
  return {
    initialValue,
    /**
    * Errors can be a string, or a MessageDescriptor, so we need to handle both cases.
    * If it's anything else, we don't return it.
    */
    rawError,
    error: isErrorMessageDescriptor(error) ? formatMessage({
      id: error.id,
      defaultMessage: error.defaultMessage
    }, error.values) : typeof error === "string" ? error : void 0,
    onChange: handleChange,
    value
  };
}
var isErrorMessageDescriptor = (object) => {
  return typeof object === "object" && object !== null && !Array.isArray(object) && "id" in object && "defaultMessage" in object;
};
var Blocker = ({ onProceed = () => {
}, onCancel = () => {
} }) => {
  const { formatMessage } = useIntl();
  const modified = useForm("Blocker", (state) => state.modified);
  const isSubmitting = useForm("Blocker", (state) => state.isSubmitting);
  const blocker = useBlocker(({ currentLocation, nextLocation }) => {
    return !isSubmitting && modified && (currentLocation.pathname !== nextLocation.pathname || currentLocation.search !== nextLocation.search);
  });
  if (blocker.state === "blocked") {
    const handleCancel = (isOpen) => {
      if (!isOpen) {
        onCancel();
        blocker.reset();
      }
    };
    return (0, import_jsx_runtime.jsx)(Dialog.Root, {
      open: true,
      onOpenChange: handleCancel,
      children: (0, import_jsx_runtime.jsxs)(Dialog.Content, {
        children: [
          (0, import_jsx_runtime.jsx)(Dialog.Header, {
            children: formatMessage({
              id: "app.components.ConfirmDialog.title",
              defaultMessage: "Confirmation"
            })
          }),
          (0, import_jsx_runtime.jsx)(Dialog.Body, {
            icon: (0, import_jsx_runtime.jsx)(ForwardRef$3, {
              width: "24px",
              height: "24px",
              fill: "danger600"
            }),
            children: formatMessage({
              id: "global.prompt.unsaved",
              defaultMessage: "You have unsaved changes, are you sure you want to leave?"
            })
          }),
          (0, import_jsx_runtime.jsxs)(Dialog.Footer, {
            children: [
              (0, import_jsx_runtime.jsx)(Dialog.Cancel, {
                children: (0, import_jsx_runtime.jsx)(Button, {
                  variant: "tertiary",
                  children: formatMessage({
                    id: "app.components.Button.cancel",
                    defaultMessage: "Cancel"
                  })
                })
              }),
              (0, import_jsx_runtime.jsx)(Button, {
                onClick: () => {
                  onProceed();
                  blocker.proceed();
                },
                variant: "danger",
                children: formatMessage({
                  id: "app.components.Button.confirm",
                  defaultMessage: "Confirm"
                })
              })
            ]
          })
        ]
      })
    });
  }
  return null;
};

export {
  generateNKeysBetween,
  useForm,
  Form,
  getYupValidationErrors,
  useField,
  Blocker
};
//# sourceMappingURL=chunk-VWIQ6S3Y.js.map
