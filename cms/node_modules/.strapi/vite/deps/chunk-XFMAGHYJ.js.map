{"version": 3, "sources": ["../../../@strapi/admin/admin/src/pages/Settings/pages/Users/<USER>/MagicLinkWrapper.tsx"], "sourcesContent": ["import { IconButton } from '@strapi/design-system';\nimport { Duplicate } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\n\nimport { ContentBox } from '../../../../../components/ContentBox';\nimport { useNotification } from '../../../../../features/Notifications';\nimport { useClipboard } from '../../../../../hooks/useClipboard';\n\ninterface MagicLinkWrapperProps {\n  children: string;\n  target: string;\n}\n\nconst MagicLinkWrapper = ({ children, target }: MagicLinkWrapperProps) => {\n  const { toggleNotification } = useNotification();\n  const { formatMessage } = useIntl();\n  const { copy } = useClipboard();\n\n  const copyLabel = formatMessage({\n    id: 'app.component.CopyToClipboard.label',\n    defaultMessage: 'Copy to clipboard',\n  });\n\n  const handleClick = async (e: React.MouseEvent<HTMLButtonElement>) => {\n    e.preventDefault();\n\n    const didCopy = await copy(target);\n\n    if (didCopy) {\n      toggleNotification({\n        type: 'info',\n        message: formatMessage({ id: 'notification.link-copied' }),\n      });\n    }\n  };\n\n  return (\n    <ContentBox\n      endAction={\n        <IconButton label={copyLabel} variant=\"ghost\" onClick={handleClick}>\n          <Duplicate />\n        </IconButton>\n      }\n      title={target}\n      titleEllipsis\n      subtitle={children}\n      icon={<span style={{ fontSize: 32 }}>✉️</span>}\n      iconBackground=\"neutral100\"\n    />\n  );\n};\n\nexport { MagicLinkWrapper };\nexport type { MagicLinkWrapperProps };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAaA,IAAMA,mBAAmB,CAAC,EAAEC,UAAUC,OAAM,MAAyB;AACnE,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,KAAI,IAAKC,aAAAA;AAEjB,QAAMC,YAAYJ,cAAc;IAC9BK,IAAI;IACJC,gBAAgB;EAClB,CAAA;AAEA,QAAMC,cAAc,OAAOC,MAAAA;AACzBA,MAAEC,eAAc;AAEhB,UAAMC,UAAU,MAAMR,KAAKL,MAAAA;AAE3B,QAAIa,SAAS;AACXZ,yBAAmB;QACjBa,MAAM;QACNC,SAASZ,cAAc;UAAEK,IAAI;QAA2B,CAAA;MAC1D,CAAA;IACF;EACF;AAEA,aACEQ,wBAACC,YAAAA;IACCC,eACEF,wBAACG,YAAAA;MAAWC,OAAOb;MAAWc,SAAQ;MAAQC,SAASZ;MACrD,cAAAM,wBAACO,eAAAA,CAAAA,CAAAA;;IAGLC,OAAOxB;IACPyB,eAAa;IACbC,UAAU3B;IACV4B,UAAMX,wBAACY,QAAAA;MAAKC,OAAO;QAAEC,UAAU;MAAG;MAAG,UAAA;;IACrCC,gBAAe;;AAGrB;", "names": ["MagicLinkWrapper", "children", "target", "toggleNotification", "useNotification", "formatMessage", "useIntl", "copy", "useClipboard", "copyLabel", "id", "defaultMessage", "handleClick", "e", "preventDefault", "didCopy", "type", "message", "_jsx", "ContentBox", "endAction", "IconButton", "label", "variant", "onClick", "Duplicate", "title", "title<PERSON><PERSON><PERSON>", "subtitle", "icon", "span", "style", "fontSize", "iconBackground"]}