import {
  useCallbackRef
} from "./chunk-4YYUDJZ2.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/admin/src/hooks/useEnterprise.mjs
var React = __toESM(require_react(), 1);
function isEnterprise() {
  return window.strapi.isEE;
}
var useEnterprise = (ceData, eeCallback, opts = {}) => {
  const { defaultValue = null, combine = (_ceData, eeData) => eeData, enabled = true } = opts;
  const eeCallbackRef = useCallbackRef(eeCallback);
  const combineCallbackRef = useCallbackRef(combine);
  const [{ data }, setData] = React.useState({
    data: isEnterprise() && enabled ? defaultValue : ceData
  });
  React.useEffect(() => {
    async function importEE() {
      const eeData = await eeCallbackRef();
      const combinedValue = combineCallbackRef(ceData, eeData);
      setData({
        data: combinedValue ? combinedValue : eeData
      });
    }
    if (isEnterprise() && enabled) {
      importEE();
    }
  }, [
    ceData,
    eeCallbackRef,
    combineCallbackRef,
    enabled
  ]);
  return data;
};

export {
  useEnterprise
};
//# sourceMappingURL=chunk-Y7ATVFIR.js.map
