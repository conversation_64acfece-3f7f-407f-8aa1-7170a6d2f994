{"version": 3, "sources": ["../../../@strapi/admin/admin/src/selectors.ts"], "sourcesContent": ["import { createTypedSelector } from './core/store/hooks';\n\n/**\n * @deprecated\n *\n * Use `useTypedSelector` and access the state directly, this was only used so we knew\n * we were using the correct path. Which is state.admin_app.permissions\n */\nexport const selectAdminPermissions = createTypedSelector((state) => state.admin_app.permissions);\n"], "mappings": ";;;;;AAQaA,IAAAA,yBAAyBC,oBAAoB,CAACC,UAAUA,MAAMC,UAAUC,WAAW;", "names": ["selectAdminPermissions", "createTypedSelector", "state", "admin_app", "permissions"]}