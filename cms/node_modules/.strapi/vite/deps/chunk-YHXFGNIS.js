import {
  Badge,
  Flex,
  Typography
} from "./chunk-4YYUDJZ2.js";
import {
  ForwardRef$2t
} from "./chunk-QW5ZDDTU.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  dt
} from "./chunk-3CQBCJ3G.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/admin/src/components/GradientBadge.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var GradientBadge = dt(Badge)`
  background: linear-gradient(
    90deg,
    ${({ theme }) => theme.colors.primary600} 0%,
    ${({ theme }) => theme.colors.alternative600} 121.48%
  );
  padding: 0.4rem 1rem;
`;
var GradientBadgeWithIcon = ({ label }) => {
  return (0, import_jsx_runtime.jsx)(GradientBadge, {
    children: (0, import_jsx_runtime.jsxs)(Flex, {
      gap: 1,
      alignItems: "center",
      children: [
        (0, import_jsx_runtime.jsx)(ForwardRef$2t, {
          width: 16,
          height: 16,
          fill: "neutral0"
        }),
        (0, import_jsx_runtime.jsx)(Typography, {
          textColor: "neutral0",
          children: label
        })
      ]
    })
  });
};

export {
  GradientBadgeWithIcon
};
//# sourceMappingURL=chunk-YHXFGNIS.js.map
