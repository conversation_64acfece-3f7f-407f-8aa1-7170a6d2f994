{"version": 3, "sources": ["../../../@strapi/admin/ee/admin/src/constants.ts"], "sourcesContent": ["import type { SettingsMenu } from '../../../admin/src/constants';\nimport type { PermissionMap } from '../../../admin/src/types/permissions';\nimport type { RouteObject } from 'react-router-dom';\n\nexport const ADMIN_PERMISSIONS_EE = {\n  settings: {\n    auditLogs: {\n      main: [{ action: 'admin::audit-logs.read', subject: null }],\n      read: [{ action: 'admin::audit-logs.read', subject: null }],\n      update: [{ action: 'admin::audit-logs.update', subject: null }],\n    },\n    'review-workflows': {\n      main: [{ action: 'admin::review-workflows.read', subject: null }],\n      read: [{ action: 'admin::review-workflows.read', subject: null }],\n      create: [{ action: 'admin::review-workflows.create', subject: null }],\n      delete: [{ action: 'admin::review-workflows.delete', subject: null }],\n      update: [{ action: 'admin::review-workflows.update', subject: null }],\n    },\n    sso: {\n      main: [{ action: 'admin::provider-login.read', subject: null }],\n      read: [{ action: 'admin::provider-login.read', subject: null }],\n      update: [{ action: 'admin::provider-login.update', subject: null }],\n    },\n    releases: {\n      read: [\n        {\n          action: 'plugin::content-releases.settings.read',\n          subject: null,\n        },\n      ],\n      update: [\n        {\n          action: 'plugin::content-releases.settings.update',\n          subject: null,\n        },\n      ],\n    },\n  },\n} satisfies {\n  settings: Pick<PermissionMap['settings'], 'auditLogs' | 'review-workflows' | 'sso' | 'releases'>;\n};\n\n/**\n * Base EE routes, these are relative to the `root` of the app.\n * We use a function to get them so we're not looking at window\n * during build time.\n */\nexport const getEERoutes = (): RouteObject[] =>\n  window.strapi.isEE\n    ? [\n        {\n          path: 'auth/login/:authResponse',\n          lazy: async () => {\n            const { AuthResponse } = await import('./pages/AuthResponse');\n\n            return {\n              Component: AuthResponse,\n            };\n          },\n        },\n      ]\n    : [];\n\n// TODO: the constants.js file is imported before the React application is setup and\n// therefore `window.strapi` might not exist at import-time. We should probably define\n// which constant is available at which stage of the application lifecycle.\nexport const SETTINGS_LINKS_EE = (): SettingsMenu => ({\n  global: [\n    ...(window.strapi.features.isEnabled(window.strapi.features.SSO)\n      ? [\n          {\n            intlLabel: { id: 'Settings.sso.title', defaultMessage: 'Single Sign-On' },\n            to: '/settings/single-sign-on',\n            id: 'sso',\n          },\n        ]\n      : []),\n  ],\n\n  admin: [\n    ...(window.strapi.features.isEnabled(window.strapi.features.AUDIT_LOGS)\n      ? [\n          {\n            intlLabel: { id: 'global.auditLogs', defaultMessage: 'Audit Logs' },\n            to: '/settings/audit-logs?pageSize=50&page=1&sort=date:DESC',\n            id: 'auditLogs',\n          },\n        ]\n      : []),\n  ],\n});\n"], "mappings": ";IAIaA,uBAAuB;EAClCC,UAAU;IACRC,WAAW;MACTC,MAAM;QAAC;UAAEC,QAAQ;UAA0BC,SAAS;QAAK;MAAE;MAC3DC,MAAM;QAAC;UAAEF,QAAQ;UAA0BC,SAAS;QAAK;MAAE;MAC3DE,QAAQ;QAAC;UAAEH,QAAQ;UAA4BC,SAAS;QAAK;MAAE;IACjE;IACA,oBAAoB;MAClBF,MAAM;QAAC;UAAEC,QAAQ;UAAgCC,SAAS;QAAK;MAAE;MACjEC,MAAM;QAAC;UAAEF,QAAQ;UAAgCC,SAAS;QAAK;MAAE;MACjEG,QAAQ;QAAC;UAAEJ,QAAQ;UAAkCC,SAAS;QAAK;MAAE;MACrEI,QAAQ;QAAC;UAAEL,QAAQ;UAAkCC,SAAS;QAAK;MAAE;MACrEE,QAAQ;QAAC;UAAEH,QAAQ;UAAkCC,SAAS;QAAK;MAAE;IACvE;IACAK,KAAK;MACHP,MAAM;QAAC;UAAEC,QAAQ;UAA8BC,SAAS;QAAK;MAAE;MAC/DC,MAAM;QAAC;UAAEF,QAAQ;UAA8BC,SAAS;QAAK;MAAE;MAC/DE,QAAQ;QAAC;UAAEH,QAAQ;UAAgCC,SAAS;QAAK;MAAE;IACrE;IACAM,UAAU;MACRL,MAAM;QACJ;UACEF,QAAQ;UACRC,SAAS;QACX;MACD;MACDE,QAAQ;QACN;UACEH,QAAQ;UACRC,SAAS;QACX;MACD;IACH;EACF;AACF;IASaO,cAAc,MACzBC,OAAOC,OAAOC,OACV;EACE;IACEC,MAAM;IACNC,MAAM,YAAA;AACJ,YAAM,EAAEC,aAAY,IAAK,MAAM,OAAO,4BAAA;AAEtC,aAAO;QACLC,WAAWD;MACb;IACF;EACF;AACD,IACD,CAAA;AAKC,IAAME,oBAAoB,OAAqB;EACpDC,QAAQ;IACFR,GAAAA,OAAOC,OAAOQ,SAASC,UAAUV,OAAOC,OAAOQ,SAASE,GAAG,IAC3D;MACE;QACEC,WAAW;UAAEC,IAAI;UAAsBC,gBAAgB;QAAiB;QACxEC,IAAI;QACJF,IAAI;MACN;IACD,IACD,CAAA;EACL;EAEDG,OAAO;IACDhB,GAAAA,OAAOC,OAAOQ,SAASC,UAAUV,OAAOC,OAAOQ,SAASQ,UAAU,IAClE;MACE;QACEL,WAAW;UAAEC,IAAI;UAAoBC,gBAAgB;QAAa;QAClEC,IAAI;QACJF,IAAI;MACN;IACD,IACD,CAAA;EACL;AACH;", "names": ["ADMIN_PERMISSIONS_EE", "settings", "auditLogs", "main", "action", "subject", "read", "update", "create", "delete", "sso", "releases", "getEERoutes", "window", "strapi", "isEE", "path", "lazy", "AuthResponse", "Component", "SETTINGS_LINKS_EE", "global", "features", "isEnabled", "SSO", "intlLabel", "id", "defaultMessage", "to", "admin", "AUDIT_LOGS"]}