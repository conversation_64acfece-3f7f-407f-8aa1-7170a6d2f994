{"version": 3, "sources": ["../../../@strapi/admin/admin/src/components/PrivateRoute.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { Navigate, useLocation } from 'react-router-dom';\n\nimport { useAuth } from '../features/Auth';\n\ninterface PrivateRouteProps {\n  children: React.ReactNode;\n}\n\nconst PrivateRoute = ({ children }: PrivateRouteProps) => {\n  const token = useAuth('PrivateRoute', (state) => state.token);\n  const { pathname, search } = useLocation();\n\n  return token !== null ? (\n    children\n  ) : (\n    <Navigate\n      to={{\n        pathname: '/auth/login',\n        search:\n          pathname !== '/'\n            ? `?redirectTo=${encodeURIComponent(`${pathname}${search}`)}`\n            : undefined,\n      }}\n    />\n  );\n};\n\nexport { PrivateRoute };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAUA,IAAMA,eAAe,CAAC,EAAEC,SAAQ,MAAqB;AACnD,QAAMC,QAAQC,QAAQ,gBAAgB,CAACC,UAAUA,MAAMF,KAAK;AAC5D,QAAM,EAAEG,UAAUC,OAAM,IAAKC,YAAAA;AAE7B,SAAOL,UAAU,OACfD,eAEAO,wBAACC,UAAAA;IACCC,IAAI;MACFL,UAAU;MACVC,QACED,aAAa,MACT,eAAeM,mBAAmB,GAAGN,QAAAA,GAAWC,MAAAA,EAAQ,CAAA,KACxDM;IACR;;AAGN;", "names": ["PrivateRoute", "children", "token", "useAuth", "state", "pathname", "search", "useLocation", "_jsx", "Navigate", "to", "encodeURIComponent", "undefined"]}