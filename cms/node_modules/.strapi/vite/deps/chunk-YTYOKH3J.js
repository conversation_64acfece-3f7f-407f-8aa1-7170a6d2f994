import {
  usePersistentState
} from "./chunk-4QC3H4VA.js";
import {
  Form,
  Formik
} from "./chunk-BJKYWYYT.js";
import {
  create4 as create,
  create5 as create2,
  create6 as create3
} from "./chunk-4WX3TY7V.js";
import {
  useAppInfo
} from "./chunk-USP7YHPW.js";
import {
  useAuth
} from "./chunk-JXTOCMQW.js";
import {
  useNotification
} from "./chunk-4EY5FDQG.js";
import {
  Box,
  Button,
  Field,
  Flex,
  IconButton,
  Portal$1,
  Textarea,
  Typography,
  VisuallyHidden,
  useIntl
} from "./chunk-4YYUDJZ2.js";
import {
  ForwardRef$45
} from "./chunk-QW5ZDDTU.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  dt
} from "./chunk-3CQBCJ3G.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/admin/src/components/NpsSurvey.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var FieldWrapper = dt(Field.Root)`
  height: 3.2rem;
  width: 3.2rem;

  > label,
  ~ input {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }

  > label {
    color: inherit;
    cursor: pointer;
    padding: ${({ theme }) => theme.spaces[2]};
    text-align: center;
    vertical-align: middle;
  }

  &:hover,
  &:focus-within {
    background-color: ${({ theme }) => theme.colors.neutral0};
  }

  &:active,
  &.selected {
    color: ${({ theme }) => theme.colors.primary700};
    background-color: ${({ theme }) => theme.colors.neutral0};
    border-color: ${({ theme }) => theme.colors.primary700};
  }
`;
var delays = {
  postResponse: 90 * 24 * 60 * 60 * 1e3,
  postFirstDismissal: 14 * 24 * 60 * 60 * 1e3,
  postSubsequentDismissal: 90 * 24 * 60 * 60 * 1e3,
  display: 30 * 60 * 1e3
};
var ratingArray = [
  ...Array(11).keys()
];
var checkIfShouldShowSurvey = (settings) => {
  const { enabled, lastResponseDate, firstDismissalDate, lastDismissalDate } = settings;
  if (window.strapi.flags.nps === false) {
    return false;
  }
  if (enabled === false) {
    return false;
  }
  if (lastResponseDate) {
    const timeSinceLastResponse = Date.now() - new Date(lastResponseDate).getTime();
    if (timeSinceLastResponse >= delays.postResponse) {
      return true;
    }
    return false;
  }
  if (lastDismissalDate) {
    const timeSinceLastDismissal = Date.now() - new Date(lastDismissalDate).getTime();
    if (timeSinceLastDismissal >= delays.postSubsequentDismissal) {
      return true;
    }
    return false;
  }
  if (firstDismissalDate) {
    const timeSinceFirstDismissal = Date.now() - new Date(firstDismissalDate).getTime();
    if (timeSinceFirstDismissal >= delays.postFirstDismissal) {
      return true;
    }
    return false;
  }
  return true;
};
var NpsSurvey = () => {
  const { formatMessage } = useIntl();
  const { npsSurveySettings, setNpsSurveySettings } = useNpsSurveySettings();
  const [isFeedbackResponse, setIsFeedbackResponse] = React.useState(false);
  const { toggleNotification } = useNotification();
  const currentEnvironment = useAppInfo("NpsSurvey", (state) => state.currentEnvironment);
  const strapiVersion = useAppInfo("NpsSurvey", (state) => state.strapiVersion);
  const [surveyIsShown, setSurveyIsShown] = React.useState(checkIfShouldShowSurvey(npsSurveySettings));
  const [displaySurvey, setDisplaySurvey] = React.useState(false);
  React.useEffect(() => {
    const displayTime = setTimeout(() => {
      setDisplaySurvey(true);
    }, delays.display);
    return () => {
      clearTimeout(displayTime);
    };
  }, []);
  const { user } = useAuth("NpsSurvey", (auth) => auth);
  if (!displaySurvey) {
    return null;
  }
  if (!surveyIsShown) {
    return null;
  }
  const handleSubmitResponse = async ({ npsSurveyRating, npsSurveyFeedback }) => {
    try {
      const body = {
        email: typeof user === "object" && user.email ? user.email : "",
        rating: npsSurveyRating,
        comment: npsSurveyFeedback,
        environment: currentEnvironment,
        version: strapiVersion ?? void 0,
        license: window.strapi.projectType,
        isHostedOnStrapiCloud: process.env.STRAPI_HOSTING === "strapi.cloud"
      };
      const res = await fetch("https://analytics.strapi.io/submit-nps", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(body)
      });
      if (!res.ok) {
        throw new Error("Failed to submit NPS survey");
      }
      setNpsSurveySettings((settings) => ({
        ...settings,
        lastResponseDate: (/* @__PURE__ */ new Date()).toString(),
        firstDismissalDate: null,
        lastDismissalDate: null
      }));
      setIsFeedbackResponse(true);
      setTimeout(() => {
        setSurveyIsShown(false);
      }, 3e3);
    } catch (err) {
      toggleNotification({
        type: "danger",
        message: formatMessage({
          id: "notification.error",
          defaultMessage: "An error occurred"
        })
      });
    }
  };
  const handleDismiss = () => {
    setNpsSurveySettings((settings) => {
      const nextSettings = {
        ...settings,
        lastResponseDate: null
      };
      if (settings.firstDismissalDate) {
        nextSettings.lastDismissalDate = (/* @__PURE__ */ new Date()).toString();
      } else {
        nextSettings.firstDismissalDate = (/* @__PURE__ */ new Date()).toString();
      }
      return nextSettings;
    });
    setSurveyIsShown(false);
  };
  return (0, import_jsx_runtime.jsx)(Portal$1, {
    children: (0, import_jsx_runtime.jsx)(Formik, {
      initialValues: {
        npsSurveyFeedback: "",
        npsSurveyRating: null
      },
      onSubmit: handleSubmitResponse,
      validationSchema: create3({
        npsSurveyFeedback: create(),
        npsSurveyRating: create2().required()
      }),
      children: ({ values, handleChange, setFieldValue, isSubmitting }) => (0, import_jsx_runtime.jsx)(Form, {
        name: "npsSurveyForm",
        children: (0, import_jsx_runtime.jsx)(Flex, {
          hasRadius: true,
          direction: "column",
          padding: 4,
          borderColor: "primary200",
          background: "neutral0",
          shadow: "popupShadow",
          position: "fixed",
          bottom: 0,
          left: "50%",
          transform: "translateX(-50%)",
          zIndex: "200",
          width: "50%",
          children: isFeedbackResponse ? (0, import_jsx_runtime.jsx)(Typography, {
            fontWeight: "semiBold",
            children: formatMessage({
              id: "app.components.NpsSurvey.feedback-response",
              defaultMessage: "Thank you very much for your feedback!"
            })
          }) : (0, import_jsx_runtime.jsxs)(Box, {
            tag: "fieldset",
            width: "100%",
            borderWidth: 0,
            children: [
              (0, import_jsx_runtime.jsxs)(Flex, {
                justifyContent: "space-between",
                width: "100%",
                children: [
                  (0, import_jsx_runtime.jsx)(Box, {
                    marginLeft: "auto",
                    marginRight: "auto",
                    children: (0, import_jsx_runtime.jsx)(Typography, {
                      fontWeight: "semiBold",
                      tag: "legend",
                      children: formatMessage({
                        id: "app.components.NpsSurvey.banner-title",
                        defaultMessage: "How likely are you to recommend Strapi to a friend or colleague?"
                      })
                    })
                  }),
                  (0, import_jsx_runtime.jsx)(IconButton, {
                    onClick: handleDismiss,
                    withTooltip: false,
                    label: formatMessage({
                      id: "app.components.NpsSurvey.dismiss-survey-label",
                      defaultMessage: "Dismiss survey"
                    }),
                    children: (0, import_jsx_runtime.jsx)(ForwardRef$45, {})
                  })
                ]
              }),
              (0, import_jsx_runtime.jsxs)(Flex, {
                gap: 2,
                marginTop: 2,
                marginBottom: 2,
                justifyContent: "center",
                children: [
                  (0, import_jsx_runtime.jsx)(Typography, {
                    variant: "pi",
                    textColor: "neutral600",
                    children: formatMessage({
                      id: "app.components.NpsSurvey.no-recommendation",
                      defaultMessage: "Not at all likely"
                    })
                  }),
                  ratingArray.map((number) => {
                    return (0, import_jsx_runtime.jsx)(FieldWrapper, {
                      name: "npsSurveyRating",
                      className: values.npsSurveyRating === number ? "selected" : void 0,
                      hasRadius: true,
                      background: "primary100",
                      borderColor: "primary200",
                      color: "primary600",
                      position: "relative",
                      cursor: "pointer",
                      children: (0, import_jsx_runtime.jsxs)(Field.Label, {
                        children: [
                          (0, import_jsx_runtime.jsx)(VisuallyHidden, {
                            children: (0, import_jsx_runtime.jsx)(Field.Input, {
                              type: "radio",
                              checked: values.npsSurveyRating === number,
                              onChange: (e) => setFieldValue("npsSurveyRating", parseInt(e.target.value, 10)),
                              value: number
                            })
                          }),
                          number
                        ]
                      })
                    }, number);
                  }),
                  (0, import_jsx_runtime.jsx)(Typography, {
                    variant: "pi",
                    textColor: "neutral600",
                    children: formatMessage({
                      id: "app.components.NpsSurvey.happy-to-recommend",
                      defaultMessage: "Extremely likely"
                    })
                  })
                ]
              }),
              values.npsSurveyRating !== null && (0, import_jsx_runtime.jsxs)(Flex, {
                direction: "column",
                children: [
                  (0, import_jsx_runtime.jsx)(Box, {
                    marginTop: 2,
                    children: (0, import_jsx_runtime.jsx)(Field.Label, {
                      fontWeight: "semiBold",
                      fontSize: 2,
                      children: formatMessage({
                        id: "app.components.NpsSurvey.feedback-question",
                        defaultMessage: "Do you have any suggestion for improvements?"
                      })
                    })
                  }),
                  (0, import_jsx_runtime.jsx)(Box, {
                    width: "62%",
                    marginTop: 3,
                    marginBottom: 4,
                    children: (0, import_jsx_runtime.jsx)(Textarea, {
                      id: "npsSurveyFeedback",
                      width: "100%",
                      onChange: handleChange,
                      value: values.npsSurveyFeedback
                    })
                  }),
                  (0, import_jsx_runtime.jsx)(Button, {
                    marginBottom: 2,
                    type: "submit",
                    loading: isSubmitting,
                    children: formatMessage({
                      id: "app.components.NpsSurvey.submit-feedback",
                      defaultMessage: "Submit Feedback"
                    })
                  })
                ]
              })
            ]
          })
        })
      })
    })
  });
};
function useNpsSurveySettings() {
  const [npsSurveySettings, setNpsSurveySettings] = usePersistentState("STRAPI_NPS_SURVEY_SETTINGS", {
    enabled: true,
    lastResponseDate: null,
    firstDismissalDate: null,
    lastDismissalDate: null
  });
  return {
    npsSurveySettings,
    setNpsSurveySettings
  };
}

export {
  NpsSurvey,
  useNpsSurveySettings
};
//# sourceMappingURL=chunk-YTYOKH3J.js.map
