{"version": 3, "sources": ["../../../@strapi/admin/admin/src/pages/Settings/pages/Users/<USER>/MagicLinkCE.tsx", "../../../@strapi/admin/admin/src/pages/Settings/pages/Users/<USER>/SelectRoles.tsx"], "sourcesContent": ["import { useIntl } from 'react-intl';\n\nimport { getBasename } from '../../../../../core/utils/basename';\n\nimport { MagicLinkWrapper } from './MagicLinkWrapper';\n\ninterface MagicLinkCEProps {\n  registrationToken: string;\n}\n\nconst MagicLinkCE = ({ registrationToken }: MagicLinkCEProps) => {\n  const { formatMessage } = useIntl();\n  const target = `${\n    window.location.origin\n  }${getBasename()}/auth/register?registrationToken=${registrationToken}`;\n\n  return (\n    <MagicLinkWrapper target={target}>\n      {formatMessage({\n        id: 'app.components.Users.MagicLink.connect',\n        defaultMessage: 'Copy and share this link to give access to this user',\n      })}\n    </MagicLinkWrapper>\n  );\n};\n\nexport { MagicLinkCE };\nexport type { MagicLinkCEProps };\n", "import { Field, MultiSelect, MultiSelectOption } from '@strapi/design-system';\nimport { Loader as LoadingIcon } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\nimport { styled, keyframes } from 'styled-components';\n\nimport { useField } from '../../../../../components/Form';\nimport { useAdminRoles } from '../../../../../hooks/useAdminRoles';\n\ninterface SelectRolesProps {\n  disabled?: boolean;\n}\n\nconst SelectRoles = ({ disabled }: SelectRolesProps) => {\n  const { isLoading, roles } = useAdminRoles();\n\n  const { formatMessage } = useIntl();\n  const { value = [], onChange, error } = useField<string[]>('roles');\n\n  return (\n    <Field.Root\n      error={error}\n      hint={formatMessage({\n        id: 'app.components.Users.ModalCreateBody.block-title.roles.description',\n        defaultMessage: 'A user can have one or several roles',\n      })}\n      name=\"roles\"\n      required\n    >\n      <Field.Label>\n        {formatMessage({\n          id: 'app.components.Users.ModalCreateBody.block-title.roles',\n          defaultMessage: \"User's roles\",\n        })}\n      </Field.Label>\n      <MultiSelect\n        disabled={disabled}\n        onChange={(v) => {\n          onChange('roles', v);\n        }}\n        placeholder={formatMessage({\n          id: 'app.components.Select.placeholder',\n          defaultMessage: 'Select',\n        })}\n        startIcon={isLoading ? <Loader /> : undefined}\n        value={value.map((v) => v.toString())}\n        withTags\n      >\n        {roles.map((role) => {\n          return (\n            <MultiSelectOption key={role.id} value={role.id.toString()}>\n              {formatMessage({\n                id: `global.${role.code}`,\n                defaultMessage: role.name,\n              })}\n            </MultiSelectOption>\n          );\n        })}\n      </MultiSelect>\n      <Field.Error />\n      <Field.Hint />\n    </Field.Root>\n  );\n};\n\nconst rotation = keyframes`\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(359deg);\n  }\n`;\n\nconst LoadingWrapper = styled.div`\n  animation: ${rotation} 2s infinite linear;\n`;\n\nconst Loader = () => (\n  <LoadingWrapper>\n    <LoadingIcon />\n  </LoadingWrapper>\n);\n\nexport { SelectRoles };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,IAAMA,cAAc,CAAC,EAAEC,kBAAiB,MAAoB;AAC1D,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAMC,SAAS,GACbC,OAAOC,SAASC,MAAM,GACrBC,YAAAA,CAAAA,oCAAiDP,iBAAAA;AAEpD,aACEQ,wBAACC,kBAAAA;IAAiBN;cACfF,cAAc;MACbS,IAAI;MACJC,gBAAgB;IAClB,CAAA;;AAGN;;;;ACZA,IAAMC,cAAc,CAAC,EAAEC,SAAQ,MAAoB;AACjD,QAAM,EAAEC,WAAWC,MAAK,IAAKC,cAAAA;AAE7B,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,QAAQ,CAAA,GAAIC,UAAUC,MAAK,IAAKC,SAAmB,OAAA;AAE3D,aACEC,0BAACC,MAAMC,MAAI;IACTJ;IACAK,MAAMT,cAAc;MAClBU,IAAI;MACJC,gBAAgB;IAClB,CAAA;IACAC,MAAK;IACLC,UAAQ;;UAERC,yBAACP,MAAMQ,OAAK;kBACTf,cAAc;UACbU,IAAI;UACJC,gBAAgB;QAClB,CAAA;;UAEFG,yBAACE,aAAAA;QACCpB;QACAO,UAAU,CAACc,MAAAA;AACTd,mBAAS,SAASc,CAAAA;QACpB;QACAC,aAAalB,cAAc;UACzBU,IAAI;UACJC,gBAAgB;QAClB,CAAA;QACAQ,WAAWtB,gBAAYiB,yBAACM,QAAYC,CAAAA,CAAAA,IAAAA;QACpCnB,OAAOA,MAAMoB,IAAI,CAACL,MAAMA,EAAEM,SAAQ,CAAA;QAClCC,UAAQ;kBAEP1B,MAAMwB,IAAI,CAACG,SAAAA;AACV,qBACEX,yBAACY,mBAAAA;YAAgCxB,OAAOuB,KAAKf,GAAGa,SAAQ;sBACrDvB,cAAc;cACbU,IAAI,UAAUe,KAAKE,IAAI;cACvBhB,gBAAgBc,KAAKb;YACvB,CAAA;UAJsBa,GAAAA,KAAKf,EAAE;QAOnC,CAAA;;UAEFI,yBAACP,MAAMqB,OAAK,CAAA,CAAA;UACZd,yBAACP,MAAMsB,MAAI,CAAA,CAAA;;;AAGjB;AAEA,IAAMC,WAAWC;;;;;;;;AASjB,IAAMC,iBAAiBC,GAAOC;eACfJ,QAAS;;AAGxB,IAAMV,SAAS,UACbN,yBAACkB,gBAAAA;EACC,cAAAlB,yBAACqB,eAAAA,CAAAA,CAAAA;;", "names": ["MagicLinkCE", "registrationToken", "formatMessage", "useIntl", "target", "window", "location", "origin", "getBasename", "_jsx", "MagicLinkWrapper", "id", "defaultMessage", "SelectRoles", "disabled", "isLoading", "roles", "useAdminRoles", "formatMessage", "useIntl", "value", "onChange", "error", "useField", "_jsxs", "Field", "Root", "hint", "id", "defaultMessage", "name", "required", "_jsx", "Label", "MultiSelect", "v", "placeholder", "startIcon", "Loader", "undefined", "map", "toString", "withTags", "role", "MultiSelectOption", "code", "Error", "Hint", "rotation", "keyframes", "LoadingWrapper", "styled", "div", "LoadingIcon"]}