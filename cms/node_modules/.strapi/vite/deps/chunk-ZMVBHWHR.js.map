{"version": 3, "sources": ["../../../@strapi/admin/admin/src/components/SearchInput.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { IconButton, Searchbar, SearchForm } from '@strapi/design-system';\nimport { Search as SearchIcon } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\n\nimport { TrackingEvent, useTracking } from '../features/Tracking';\nimport { useQueryParams } from '../hooks/useQueryParams';\n\ninterface SearchInputProps {\n  disabled?: boolean;\n  label: string;\n  placeholder?: string;\n  trackedEvent?: TrackingEvent['name'] | null;\n  trackedEventDetails?: TrackingEvent['properties'];\n}\n\nconst SearchInput = ({\n  disabled,\n  label,\n  placeholder,\n  trackedEvent,\n  trackedEventDetails,\n}: SearchInputProps) => {\n  const inputRef = React.useRef<HTMLInputElement>(null);\n  const iconButtonRef = React.useRef<HTMLButtonElement>(null);\n\n  const [{ query }, setQuery] = useQueryParams<{ _q: string; page?: number }>();\n\n  const [value, setValue] = React.useState(query?._q || '');\n  const [isOpen, setIsOpen] = React.useState(!!value);\n\n  const { formatMessage } = useIntl();\n  const { trackUsage } = useTracking();\n\n  const handleToggle = () => setIsOpen((prev) => !prev);\n\n  React.useLayoutEffect(() => {\n    if (isOpen && inputRef.current) {\n      inputRef.current.focus();\n    }\n  }, [isOpen]);\n\n  const handleClear = () => {\n    setValue('');\n    setQuery({ _q: '' }, 'remove');\n  };\n\n  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {\n    e.preventDefault();\n\n    // Ensure value is a string\n    if (value) {\n      if (trackedEvent) {\n        trackUsage(trackedEvent, trackedEventDetails);\n      }\n      setQuery({ _q: encodeURIComponent(value), page: 1 });\n    } else {\n      handleToggle();\n      setQuery({ _q: '' }, 'remove');\n    }\n  };\n\n  if (isOpen) {\n    return (\n      <SearchForm onSubmit={handleSubmit}>\n        <Searchbar\n          ref={inputRef}\n          name=\"search\"\n          onChange={(e) => setValue(e.target.value)}\n          value={value}\n          clearLabel={formatMessage({ id: 'clearLabel', defaultMessage: 'Clear' })}\n          onClear={handleClear}\n          placeholder={placeholder}\n          onBlur={(e) => {\n            if (!e.currentTarget.contains(e.relatedTarget) && e.currentTarget.value === '') {\n              setIsOpen(false);\n            }\n          }}\n        >\n          {label}\n        </Searchbar>\n      </SearchForm>\n    );\n  }\n\n  return (\n    <IconButton\n      ref={iconButtonRef}\n      disabled={disabled}\n      label={formatMessage({ id: 'global.search', defaultMessage: 'Search' })}\n      onClick={handleToggle}\n    >\n      <SearchIcon />\n    </IconButton>\n  );\n};\n\nexport { SearchInput };\nexport type { SearchInputProps };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBA,IAAMA,cAAc,CAAC,EACnBC,UACAC,OACAC,aACAC,cACAC,oBAAmB,MACF;AACjB,QAAMC,WAAiBC,aAAyB,IAAA;AAChD,QAAMC,gBAAsBD,aAA0B,IAAA;AAEtD,QAAM,CAAC,EAAEE,MAAK,GAAIC,QAAAA,IAAYC,eAAAA;AAE9B,QAAM,CAACC,OAAOC,QAAS,IAASC,gBAASL,+BAAOM,OAAM,EAAA;AACtD,QAAM,CAACC,QAAQC,SAAU,IAASH,eAAS,CAAC,CAACF,KAAAA;AAE7C,QAAM,EAAEM,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,WAAU,IAAKC,YAAAA;AAEvB,QAAMC,eAAe,MAAML,UAAU,CAACM,SAAS,CAACA,IAAAA;AAEhDC,EAAMC,sBAAgB,MAAA;AACpB,QAAIT,UAAUV,SAASoB,SAAS;AAC9BpB,eAASoB,QAAQC,MAAK;IACxB;KACC;IAACX;EAAO,CAAA;AAEX,QAAMY,cAAc,MAAA;AAClBf,aAAS,EAAA;AACTH,aAAS;MAAEK,IAAI;OAAM,QAAA;EACvB;AAEA,QAAMc,eAAe,CAACC,MAAAA;AACpBA,MAAEC,eAAc;AAGhB,QAAInB,OAAO;AACT,UAAIR,cAAc;AAChBgB,mBAAWhB,cAAcC,mBAAAA;MAC3B;AACAK,eAAS;QAAEK,IAAIiB,mBAAmBpB,KAAAA;QAAQqB,MAAM;MAAE,CAAA;WAC7C;AACLX,mBAAAA;AACAZ,eAAS;QAAEK,IAAI;SAAM,QAAA;IACvB;EACF;AAEA,MAAIC,QAAQ;AACV,eACEkB,wBAACC,YAAAA;MAAWC,UAAUP;MACpB,cAAAK,wBAACG,WAAAA;QACCC,KAAKhC;QACLiC,MAAK;QACLC,UAAU,CAACV,MAAMjB,SAASiB,EAAEW,OAAO7B,KAAK;QACxCA;QACA8B,YAAYxB,cAAc;UAAEyB,IAAI;UAAcC,gBAAgB;QAAQ,CAAA;QACtEC,SAASjB;QACTzB;QACA2C,QAAQ,CAAChB,MAAAA;AACP,cAAI,CAACA,EAAEiB,cAAcC,SAASlB,EAAEmB,aAAa,KAAKnB,EAAEiB,cAAcnC,UAAU,IAAI;AAC9EK,sBAAU,KAAA;UACZ;QACF;QAECf,UAAAA;;;EAIT;AAEA,aACEgC,wBAACgB,YAAAA;IACCZ,KAAK9B;IACLP;IACAC,OAAOgB,cAAc;MAAEyB,IAAI;MAAiBC,gBAAgB;IAAS,CAAA;IACrEO,SAAS7B;IAET,cAAAY,wBAACkB,cAAAA,CAAAA,CAAAA;;AAGP;", "names": ["SearchInput", "disabled", "label", "placeholder", "trackedEvent", "trackedEventDetails", "inputRef", "useRef", "iconButtonRef", "query", "<PERSON><PERSON><PERSON><PERSON>", "useQueryParams", "value", "setValue", "useState", "_q", "isOpen", "setIsOpen", "formatMessage", "useIntl", "trackUsage", "useTracking", "handleToggle", "prev", "React", "useLayoutEffect", "current", "focus", "handleClear", "handleSubmit", "e", "preventDefault", "encodeURIComponent", "page", "_jsx", "SearchForm", "onSubmit", "Searchbar", "ref", "name", "onChange", "target", "<PERSON><PERSON><PERSON><PERSON>", "id", "defaultMessage", "onClear", "onBlur", "currentTarget", "contains", "relatedTarget", "IconButton", "onClick", "SearchIcon"]}