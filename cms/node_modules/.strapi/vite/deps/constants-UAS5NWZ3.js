import {
  SSOProviders
} from "./chunk-IOFTGBIB.js";
import {
  Column,
  LayoutContent,
  Logo,
  UnauthenticatedLayout
} from "./chunk-QQYLVHWD.js";
import "./chunk-QU5YEK3U.js";
import "./chunk-USP7YHPW.js";
import "./chunk-LLJ4ZRUJ.js";
import "./chunk-YXDCVYVT.js";
import "./chunk-EYSFNMMY.js";
import "./chunk-PQINNV4N.js";
import "./chunk-VYSYYPOB.js";
import "./chunk-3UVBSCPH.js";
import {
  useGetProvidersQuery
} from "./chunk-JXTOCMQW.js";
import "./chunk-B33LMLYV.js";
import "./chunk-XU64YSEO.js";
import "./chunk-IHIZNGTI.js";
import "./chunk-BHLYCXQ7.js";
import "./chunk-BLJUR3JO.js";
import "./chunk-CE4VABH2.js";
import "./chunk-QOUV5O5E.js";
import "./chunk-4EY5FDQG.js";
import {
  Box,
  Button,
  Divider,
  Flex,
  Link,
  Loader,
  Main,
  Typography,
  useIntl
} from "./chunk-4YYUDJZ2.js";
import {
  NavLink,
  Navigate,
  useNavigate
} from "./chunk-S65ZWNEO.js";
import "./chunk-FOD4ENRR.js";
import "./chunk-QW5ZDDTU.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  dt
} from "./chunk-3CQBCJ3G.js";
import "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/ee/admin/src/pages/AuthPage/components/Providers.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var Providers = () => {
  const navigate = useNavigate();
  const { formatMessage } = useIntl();
  const { isLoading, data: providers = [] } = useGetProvidersQuery(void 0, {
    skip: !window.strapi.features.isEnabled(window.strapi.features.SSO)
  });
  const handleClick = () => {
    navigate("/auth/login");
  };
  if (!window.strapi.features.isEnabled(window.strapi.features.SSO) || !isLoading && providers.length === 0) {
    return (0, import_jsx_runtime.jsx)(Navigate, {
      to: "/auth/login"
    });
  }
  return (0, import_jsx_runtime.jsx)(UnauthenticatedLayout, {
    children: (0, import_jsx_runtime.jsxs)(Main, {
      children: [
        (0, import_jsx_runtime.jsxs)(LayoutContent, {
          children: [
            (0, import_jsx_runtime.jsxs)(Column, {
              children: [
                (0, import_jsx_runtime.jsx)(Logo, {}),
                (0, import_jsx_runtime.jsx)(Box, {
                  paddingTop: 6,
                  paddingBottom: 1,
                  children: (0, import_jsx_runtime.jsx)(Typography, {
                    tag: "h1",
                    variant: "alpha",
                    children: formatMessage({
                      id: "Auth.form.welcome.title"
                    })
                  })
                }),
                (0, import_jsx_runtime.jsx)(Box, {
                  paddingBottom: 7,
                  children: (0, import_jsx_runtime.jsx)(Typography, {
                    variant: "epsilon",
                    textColor: "neutral600",
                    children: formatMessage({
                      id: "Auth.login.sso.subtitle"
                    })
                  })
                })
              ]
            }),
            (0, import_jsx_runtime.jsxs)(Flex, {
              direction: "column",
              alignItems: "stretch",
              gap: 7,
              children: [
                isLoading ? (0, import_jsx_runtime.jsx)(Flex, {
                  justifyContent: "center",
                  children: (0, import_jsx_runtime.jsx)(Loader, {
                    children: formatMessage({
                      id: "Auth.login.sso.loading"
                    })
                  })
                }) : (0, import_jsx_runtime.jsx)(SSOProviders, {
                  providers
                }),
                (0, import_jsx_runtime.jsxs)(Flex, {
                  children: [
                    (0, import_jsx_runtime.jsx)(DividerFull, {}),
                    (0, import_jsx_runtime.jsx)(Box, {
                      paddingLeft: 3,
                      paddingRight: 3,
                      children: (0, import_jsx_runtime.jsx)(Typography, {
                        variant: "sigma",
                        textColor: "neutral600",
                        children: formatMessage({
                          id: "or"
                        })
                      })
                    }),
                    (0, import_jsx_runtime.jsx)(DividerFull, {})
                  ]
                }),
                (0, import_jsx_runtime.jsx)(Button, {
                  fullWidth: true,
                  size: "L",
                  onClick: handleClick,
                  children: formatMessage({
                    id: "Auth.form.button.login.strapi"
                  })
                })
              ]
            })
          ]
        }),
        (0, import_jsx_runtime.jsx)(Flex, {
          justifyContent: "center",
          children: (0, import_jsx_runtime.jsx)(Box, {
            paddingTop: 4,
            children: (0, import_jsx_runtime.jsx)(Link, {
              tag: NavLink,
              to: "/auth/forgot-password",
              children: (0, import_jsx_runtime.jsx)(Typography, {
                variant: "pi",
                children: formatMessage({
                  id: "Auth.link.forgot-password"
                })
              })
            })
          })
        })
      ]
    })
  });
};
var DividerFull = dt(Divider)`
  flex: 1;
`;

// node_modules/@strapi/admin/dist/admin/ee/admin/src/pages/AuthPage/constants.mjs
var FORMS = {
  providers: Providers
};
export {
  FORMS
};
//# sourceMappingURL=constants-UAS5NWZ3.js.map
