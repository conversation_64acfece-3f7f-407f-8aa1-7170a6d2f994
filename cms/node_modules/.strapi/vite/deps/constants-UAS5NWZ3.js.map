{"version": 3, "sources": ["../../../@strapi/admin/ee/admin/src/pages/AuthPage/components/Providers.tsx", "../../../@strapi/admin/ee/admin/src/pages/AuthPage/constants.ts"], "sourcesContent": ["import { <PERSON>, Button, Divider, <PERSON>lex, Loader, <PERSON>, Typo<PERSON>, Link } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\nimport { NavLink, Navigate, useNavigate } from 'react-router-dom';\nimport { styled } from 'styled-components';\n\nimport { Logo } from '../../../../../../admin/src/components/UnauthenticatedLogo';\nimport {\n  Column,\n  LayoutContent,\n  UnauthenticatedLayout,\n} from '../../../../../../admin/src/layouts/UnauthenticatedLayout';\nimport { useGetProvidersQuery } from '../../../../../../admin/src/services/auth';\n\nimport { SSOProviders } from './SSOProviders';\n\nconst Providers = () => {\n  const navigate = useNavigate();\n  const { formatMessage } = useIntl();\n  const { isLoading, data: providers = [] } = useGetProvidersQuery(undefined, {\n    skip: !window.strapi.features.isEnabled(window.strapi.features.SSO),\n  });\n\n  const handleClick = () => {\n    navigate('/auth/login');\n  };\n\n  if (\n    !window.strapi.features.isEnabled(window.strapi.features.SSO) ||\n    (!isLoading && providers.length === 0)\n  ) {\n    return <Navigate to=\"/auth/login\" />;\n  }\n\n  return (\n    <UnauthenticatedLayout>\n      <Main>\n        <LayoutContent>\n          <Column>\n            <Logo />\n            <Box paddingTop={6} paddingBottom={1}>\n              <Typography tag=\"h1\" variant=\"alpha\">\n                {formatMessage({ id: 'Auth.form.welcome.title' })}\n              </Typography>\n            </Box>\n            <Box paddingBottom={7}>\n              <Typography variant=\"epsilon\" textColor=\"neutral600\">\n                {formatMessage({ id: 'Auth.login.sso.subtitle' })}\n              </Typography>\n            </Box>\n          </Column>\n          <Flex direction=\"column\" alignItems=\"stretch\" gap={7}>\n            {isLoading ? (\n              <Flex justifyContent=\"center\">\n                <Loader>{formatMessage({ id: 'Auth.login.sso.loading' })}</Loader>\n              </Flex>\n            ) : (\n              <SSOProviders providers={providers} />\n            )}\n            <Flex>\n              <DividerFull />\n              <Box paddingLeft={3} paddingRight={3}>\n                <Typography variant=\"sigma\" textColor=\"neutral600\">\n                  {formatMessage({ id: 'or' })}\n                </Typography>\n              </Box>\n              <DividerFull />\n            </Flex>\n            <Button fullWidth size=\"L\" onClick={handleClick}>\n              {formatMessage({ id: 'Auth.form.button.login.strapi' })}\n            </Button>\n          </Flex>\n        </LayoutContent>\n        <Flex justifyContent=\"center\">\n          <Box paddingTop={4}>\n            <Link tag={NavLink} to=\"/auth/forgot-password\">\n              <Typography variant=\"pi\">\n                {formatMessage({ id: 'Auth.link.forgot-password' })}\n              </Typography>\n            </Link>\n          </Box>\n        </Flex>\n      </Main>\n    </UnauthenticatedLayout>\n  );\n};\n\nconst DividerFull = styled(Divider)`\n  flex: 1;\n`;\n\nexport { Providers };\n", "import type { ComponentType } from 'react';\n\nimport { Providers } from './components/Providers';\n\ntype AuthType = 'providers';\n\ntype FormDictionary = Record<AuthType, ComponentType>;\n\nexport const FORMS = {\n  providers: Providers,\n} satisfies FormDictionary;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAeA,IAAMA,YAAY,MAAA;AAChB,QAAMC,WAAWC,YAAAA;AACjB,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,WAAWC,MAAMC,YAAY,CAAA,EAAE,IAAKC,qBAAqBC,QAAW;IAC1EC,MAAM,CAACC,OAAOC,OAAOC,SAASC,UAAUH,OAAOC,OAAOC,SAASE,GAAG;EACpE,CAAA;AAEA,QAAMC,cAAc,MAAA;AAClBf,aAAS,aAAA;EACX;AAEA,MACE,CAACU,OAAOC,OAAOC,SAASC,UAAUH,OAAOC,OAAOC,SAASE,GAAG,KAC3D,CAACV,aAAaE,UAAUU,WAAW,GACpC;AACA,eAAOC,wBAACC,UAAAA;MAASC,IAAG;;EACtB;AAEA,aACEF,wBAACG,uBAAAA;IACC,cAAAC,yBAACC,MAAAA;;YACCD,yBAACE,eAAAA;;gBACCF,yBAACG,QAAAA;;oBACCP,wBAACQ,MAAAA,CAAAA,CAAAA;oBACDR,wBAACS,KAAAA;kBAAIC,YAAY;kBAAGC,eAAe;kBACjC,cAAAX,wBAACY,YAAAA;oBAAWC,KAAI;oBAAKC,SAAQ;8BAC1B7B,cAAc;sBAAE8B,IAAI;oBAA0B,CAAA;;;oBAGnDf,wBAACS,KAAAA;kBAAIE,eAAe;kBAClB,cAAAX,wBAACY,YAAAA;oBAAWE,SAAQ;oBAAUE,WAAU;8BACrC/B,cAAc;sBAAE8B,IAAI;oBAA0B,CAAA;;;;;gBAIrDX,yBAACa,MAAAA;cAAKC,WAAU;cAASC,YAAW;cAAUC,KAAK;;gBAChDjC,gBACCa,wBAACiB,MAAAA;kBAAKI,gBAAe;kBACnB,cAAArB,wBAACsB,QAAAA;8BAAQrC,cAAc;sBAAE8B,IAAI;oBAAyB,CAAA;;yBAGxDf,wBAACuB,cAAAA;kBAAalC;;oBAEhBe,yBAACa,MAAAA;;wBACCjB,wBAACwB,aAAAA,CAAAA,CAAAA;wBACDxB,wBAACS,KAAAA;sBAAIgB,aAAa;sBAAGC,cAAc;sBACjC,cAAA1B,wBAACY,YAAAA;wBAAWE,SAAQ;wBAAQE,WAAU;kCACnC/B,cAAc;0BAAE8B,IAAI;wBAAK,CAAA;;;wBAG9Bf,wBAACwB,aAAAA,CAAAA,CAAAA;;;oBAEHxB,wBAAC2B,QAAAA;kBAAOC,WAAS;kBAACC,MAAK;kBAAIC,SAAShC;4BACjCb,cAAc;oBAAE8B,IAAI;kBAAgC,CAAA;;;;;;YAI3Df,wBAACiB,MAAAA;UAAKI,gBAAe;UACnB,cAAArB,wBAACS,KAAAA;YAAIC,YAAY;YACf,cAAAV,wBAAC+B,MAAAA;cAAKlB,KAAKmB;cAAS9B,IAAG;cACrB,cAAAF,wBAACY,YAAAA;gBAAWE,SAAQ;0BACjB7B,cAAc;kBAAE8B,IAAI;gBAA4B,CAAA;;;;;;;;AAQjE;AAEA,IAAMS,cAAcS,GAAOC,OAAAA;;;;;IC9EdC,QAAQ;EACnBC,WAAWC;AACb;", "names": ["Providers", "navigate", "useNavigate", "formatMessage", "useIntl", "isLoading", "data", "providers", "useGetProvidersQuery", "undefined", "skip", "window", "strapi", "features", "isEnabled", "SSO", "handleClick", "length", "_jsx", "Navigate", "to", "UnauthenticatedLayout", "_jsxs", "Main", "LayoutContent", "Column", "Logo", "Box", "paddingTop", "paddingBottom", "Typography", "tag", "variant", "id", "textColor", "Flex", "direction", "alignItems", "gap", "justifyContent", "Loader", "SSOProviders", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "paddingLeft", "paddingRight", "<PERSON><PERSON>", "fullWidth", "size", "onClick", "Link", "NavLink", "styled", "Divider", "FORMS", "providers", "Providers"]}