import "./chunk-PLDDJCW6.js";

// node_modules/@strapi/content-manager/dist/admin/translations/cs.json.mjs
var groups = "Skupiny";
var pageNotFound = "<PERSON>rán<PERSON> nenalezena";
var cs = {
  "EditRelations.title": "Relační data",
  "components.AddFilterCTA.add": "Filtry",
  "components.AddFilterCTA.hide": "Filtry",
  "components.DraggableAttr.edit": "Upravte kliknutím",
  "components.DynamicZone.pick-compo": "Vyberte jeden komponent",
  "components.EmptyAttributesBlock.button": "Přejít k nastavení",
  "components.EmptyAttributesBlock.description": "Můžete upravit svá nastavení",
  "components.FieldItem.linkToComponentLayout": "Nastavit rozložení komponentu",
  "components.FilterOptions.button.apply": "Aplikovat",
  "components.FiltersPickWrapper.PluginHeader.actions.apply": "Aplikovat",
  "components.FiltersPickWrapper.PluginHeader.actions.clearAll": "Smazat vše",
  "components.FiltersPickWrapper.PluginHeader.description": "Nastavit pravidla aplikování filtrů na záznamy",
  "components.FiltersPickWrapper.PluginHeader.title.filter": "Filtry",
  "components.FiltersPickWrapper.hide": "Skrýt",
  "components.LimitSelect.itemsPerPage": "Položek na stránku",
  "components.Search.placeholder": "Vyhledat záznam...",
  "components.SettingsViewWrapper.pluginHeader.description.edit-settings": "Upravit vzhled zobrazení úprav.",
  "components.SettingsViewWrapper.pluginHeader.description.list-settings": "Zvolte nastavení zobrazení záznamu.",
  "components.SettingsViewWrapper.pluginHeader.title": "Nastavit zobrazení - {name}",
  "components.TableDelete.delete": "Odstranit vše",
  "components.TableDelete.deleteSelected": "Odstranit výběr",
  "components.TableEmpty.withFilters": "Není zde žádný {contentType} se specifikovanými filtry...",
  "components.TableEmpty.withSearch": "Není zde žádný {contentType} odpovídající hledání ({search})...",
  "components.TableEmpty.withoutFilter": "Není zde žádný {contentType}...",
  "components.empty-repeatable": "Zatím zde není žádný záznam. Kliknutím na tlačítko níže jej přidáte.",
  "components.notification.info.maximum-requirement": "Již jste dosáhli maximálního počtu polí",
  "components.notification.info.minimum-requirement": "Pole bylo přidáno pro splnění minimálních požadavků",
  "components.reset-entry": "Zrušit záznam",
  "containers.Edit.Link.Layout": "Nastavit rozložení",
  "containers.Edit.Link.Model": "Upravit Typ obsahu",
  "containers.Edit.addAnItem": "Přidat záznam...",
  "containers.Edit.clickToJump": "Klikněte pro přechod k záznamu",
  "containers.Edit.delete": "Odstranit",
  "containers.Edit.editing": "Úpravy...",
  "containers.Edit.pluginHeader.title.new": "Vytvořit záznam",
  "containers.Edit.reset": "Resetovat",
  "containers.Edit.returnList": "Návrat do výpisu",
  "containers.Edit.seeDetails": "Detaily",
  "containers.Edit.submit": "Uložit",
  "containers.EditSettingsView.modal-form.edit-field": "Upravit pole",
  "containers.EditView.notification.errors": "Formulář obsahuje chyby",
  "containers.Home.introduction": "K úpravě vašich záznamů prosím přistupte skrz odkaz v levém menu. Tento zásuvný modul neobsahuje způsob jak upravit nastavení, stále na něm pracujeme.",
  "containers.Home.pluginHeaderDescription": "Spravujte své záznamy mocným a intuitivním rozhraním.",
  "containers.Home.pluginHeaderTitle": "Správce obsahu",
  "containers.List.errorFetchRecords": "Chyba",
  "containers.list.displayedFields": "Zobrazená pole",
  "containers.ListSettingsView.modal-form.edit-label": "Upravit popisek",
  "containers.SettingPage.add.field": "Vložit další pole",
  "containers.SettingPage.attributes": "Pole atributů",
  "containers.SettingPage.attributes.description": "Nastavit pořadí atributů",
  "containers.SettingPage.editSettings.description": "Přesuňte pole k vybudování rozložení",
  "containers.SettingPage.editSettings.entry.title": "Název záznamu",
  "containers.SettingPage.editSettings.entry.title.description": "Nastavit zobrazená pole záznamu",
  "containers.SettingPage.editSettings.title": "Upravit pohled (nastavení)",
  "containers.SettingPage.layout": "Rozložení",
  "containers.SettingPage.listSettings.title": "Zobrazení seznamu (nastavení)",
  "containers.SettingPage.settings": "Nastavení",
  "containers.SettingPage.view": "Zobrazení",
  "containers.SettingViewModel.pluginHeader.title": "Správce obsahu - {name}",
  "containers.SettingsPage.Block.contentType.description": "Upravit specifická nastavení",
  "containers.SettingsPage.Block.generalSettings.title": "Obecné",
  "containers.SettingsView.list.title": "Zobrazit nastavení",
  "emptyAttributes.title": "Zatím zde nejsou žádná pole",
  "error.attribute.key.taken": "Hodnota již existuje",
  "error.attribute.sameKeyAndName": "Hodnoty nesmí být stejné",
  "error.attribute.taken": "Pole se stejným názvem již existuje",
  "error.contentTypeName.taken": "Tento název již existuje",
  "error.model.fetch": "Při pokusu o načtení nastavení modelů došlo k chybě.",
  "error.record.create": "Při pokusu o vytvoření záznamu došlo k chybě.",
  "error.record.delete": "Při pokusu o smazání záznamu došlo k chybě.",
  "error.record.fetch": "Při pokusu o načtení záznamu došlo k chybě.",
  "error.record.update": "Při pokusu o aktualizaci záznamu došlo k chybě.",
  "error.records.count": "Při pokusu o sečtení záznamů došlo k chybě.",
  "error.records.fetch": "Při pokusu o načtení záznamů došlo k chybě.",
  "error.schema.generation": "Při pokusu o vygenerování schématu došlo k chybě.",
  "error.validation.json": "Toto není JSON",
  "error.validation.max": "Hodnota je příliš vysoká.",
  "error.validation.maxLength": "Hodnota je příliš dlouhá.",
  "error.validation.min": "Hodnota je příliš nízká.",
  "error.validation.minLength": "Hodnota je příliš krátká.",
  "error.validation.minSupMax": "Nemůže být větší",
  "error.validation.regex": "Hodnota neodpovídá poskytnutému regexu.",
  "error.validation.required": "Vstup hodnoty je povinná položka.",
  "form.Input.bulkActions": "Enable bulk actions",
  "form.Input.defaultSort": "Výchozí atribut řazení",
  "form.Input.description": "Popis",
  "form.Input.description.placeholder": "Zobrazit název v profilu",
  "form.Input.editable": "Upravitelné pole",
  "form.Input.filters": "Povolit filtry",
  "form.Input.label": "Štítek",
  "form.Input.label.inputDescription": "Tato hodnota přepíše štítek zobrazený v hlavičce tabulky",
  "form.Input.pageEntries": "Záznamů na stránku",
  "form.Input.placeholder": "Moje hodnota",
  "form.Input.placeholder.placeholder": "Moje hodnota",
  "form.Input.search": "Povolit vyhledávání",
  "form.Input.search.field": "Povolit vyhledávání na tomto poli",
  "form.Input.sort.field": "Povolit řazení na tomto poli",
  "form.Input.wysiwyg": "Zobrazit jako WYSIWYG",
  "global.displayedFields": "Zobrazená pole",
  groups,
  "groups.numbered": "Skupiny ({number})",
  "notification.error.displayedFields": "Alespoň jedno pole musí být zobrazeno",
  "notification.error.relationship.fetch": "Při načítání relačních vazeb došlo k chybě.",
  "notification.info.SettingPage.disableSort": "Musíte mít alespoň jeden atribut s povolením řazením.",
  "notification.info.minimumFields": "Je třeba mít zobrazeno alespoň jedno pole.",
  "notification.upload.error": "Při nahrávání vašich souborů došlo k chybě",
  pageNotFound,
  "plugin.description.long": "Zásuvný modul pro zobrazení, úpravu a mazání dat ve vaší databázi.",
  "plugin.description.short": "Zásuvný modul pro zobrazení, úpravu a mazání dat ve vaší databázi.",
  "popUpWarning.bodyMessage.contentType.delete": "Jste si jisti že chcete odstranit tento záznam?",
  "popUpWarning.bodyMessage.contentType.delete.all": "Jste si jisti, že chcete odstranit tyto záznamy?",
  "popUpWarning.warning.cancelAllSettings": "Jste si jisti, že chcete zrušit všechny vaše úpravy?",
  "popUpWarning.warning.updateAllSettings": "Toto změní všechna vaše nastavení",
  "success.record.delete": "Odstraněno",
  "success.record.save": "Uloženo"
};
export {
  cs as default,
  groups,
  pageNotFound
};
//# sourceMappingURL=cs.json-65YXLX2Y.js.map
