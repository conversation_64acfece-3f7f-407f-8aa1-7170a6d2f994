{"version": 3, "sources": ["../../../@strapi/content-manager/dist/admin/translations/cs.json.mjs"], "sourcesContent": ["var groups = \"Skupiny\";\nvar pageNotFound = \"Stránka nenalezena\";\nvar cs = {\n    \"EditRelations.title\": \"Relační data\",\n    \"components.AddFilterCTA.add\": \"Filtry\",\n    \"components.AddFilterCTA.hide\": \"Filtry\",\n    \"components.DraggableAttr.edit\": \"Upravte kliknutím\",\n    \"components.DynamicZone.pick-compo\": \"Vyberte jeden komponent\",\n    \"components.EmptyAttributesBlock.button\": \"Přejít k nastavení\",\n    \"components.EmptyAttributesBlock.description\": \"Můžete upravit svá nastavení\",\n    \"components.FieldItem.linkToComponentLayout\": \"Nastavit rozložení komponentu\",\n    \"components.FilterOptions.button.apply\": \"Aplikovat\",\n    \"components.FiltersPickWrapper.PluginHeader.actions.apply\": \"Aplikovat\",\n    \"components.FiltersPickWrapper.PluginHeader.actions.clearAll\": \"<PERSON><PERSON><PERSON><PERSON> vše\",\n    \"components.FiltersPickWrapper.PluginHeader.description\": \"Nastavit pravidla aplikování filtrů na záznamy\",\n    \"components.FiltersPickWrapper.PluginHeader.title.filter\": \"Filtry\",\n    \"components.FiltersPickWrapper.hide\": \"Skrýt\",\n    \"components.LimitSelect.itemsPerPage\": \"Položek na stránku\",\n    \"components.Search.placeholder\": \"Vyhledat záznam...\",\n    \"components.SettingsViewWrapper.pluginHeader.description.edit-settings\": \"Upravit vzhled zobrazení úprav.\",\n    \"components.SettingsViewWrapper.pluginHeader.description.list-settings\": \"Zvolte nastavení zobrazení záznamu.\",\n    \"components.SettingsViewWrapper.pluginHeader.title\": \"Nastavit zobrazení - {name}\",\n    \"components.TableDelete.delete\": \"Odstranit vše\",\n    \"components.TableDelete.deleteSelected\": \"Odstranit výběr\",\n    \"components.TableEmpty.withFilters\": \"Není zde žádný {contentType} se specifikovanými filtry...\",\n    \"components.TableEmpty.withSearch\": \"Není zde žádný {contentType} odpovídající hledání ({search})...\",\n    \"components.TableEmpty.withoutFilter\": \"Není zde žádný {contentType}...\",\n    \"components.empty-repeatable\": \"Zatím zde není žádný záznam. Kliknutím na tlačítko níže jej přidáte.\",\n    \"components.notification.info.maximum-requirement\": \"Již jste dosáhli maximálního počtu polí\",\n    \"components.notification.info.minimum-requirement\": \"Pole bylo přidáno pro splnění minimálních požadavků\",\n    \"components.reset-entry\": \"Zrušit záznam\",\n    \"containers.Edit.Link.Layout\": \"Nastavit rozložení\",\n    \"containers.Edit.Link.Model\": \"Upravit Typ obsahu\",\n    \"containers.Edit.addAnItem\": \"Přidat záznam...\",\n    \"containers.Edit.clickToJump\": \"Klikněte pro přechod k záznamu\",\n    \"containers.Edit.delete\": \"Odstranit\",\n    \"containers.Edit.editing\": \"Úpravy...\",\n    \"containers.Edit.pluginHeader.title.new\": \"Vytvořit záznam\",\n    \"containers.Edit.reset\": \"Resetovat\",\n    \"containers.Edit.returnList\": \"Návrat do výpisu\",\n    \"containers.Edit.seeDetails\": \"Detaily\",\n    \"containers.Edit.submit\": \"Uložit\",\n    \"containers.EditSettingsView.modal-form.edit-field\": \"Upravit pole\",\n    \"containers.EditView.notification.errors\": \"Formulář obsahuje chyby\",\n    \"containers.Home.introduction\": \"K úpravě vašich záznamů prosím přistupte skrz odkaz v levém menu. Tento zásuvný modul neobsahuje způsob jak upravit nastavení, stále na něm pracujeme.\",\n    \"containers.Home.pluginHeaderDescription\": \"Spravujte své záznamy mocným a intuitivním rozhraním.\",\n    \"containers.Home.pluginHeaderTitle\": \"Správce obsahu\",\n    \"containers.List.errorFetchRecords\": \"Chyba\",\n    \"containers.list.displayedFields\": \"Zobrazená pole\",\n    \"containers.ListSettingsView.modal-form.edit-label\": \"Upravit popisek\",\n    \"containers.SettingPage.add.field\": \"Vložit další pole\",\n    \"containers.SettingPage.attributes\": \"Pole atributů\",\n    \"containers.SettingPage.attributes.description\": \"Nastavit pořadí atributů\",\n    \"containers.SettingPage.editSettings.description\": \"Přesuňte pole k vybudování rozložení\",\n    \"containers.SettingPage.editSettings.entry.title\": \"Název záznamu\",\n    \"containers.SettingPage.editSettings.entry.title.description\": \"Nastavit zobrazená pole záznamu\",\n    \"containers.SettingPage.editSettings.title\": \"Upravit pohled (nastavení)\",\n    \"containers.SettingPage.layout\": \"Rozložení\",\n    \"containers.SettingPage.listSettings.title\": \"Zobrazení seznamu (nastavení)\",\n    \"containers.SettingPage.settings\": \"Nastavení\",\n    \"containers.SettingPage.view\": \"Zobrazení\",\n    \"containers.SettingViewModel.pluginHeader.title\": \"Správce obsahu - {name}\",\n    \"containers.SettingsPage.Block.contentType.description\": \"Upravit specifická nastavení\",\n    \"containers.SettingsPage.Block.generalSettings.title\": \"Obecné\",\n    \"containers.SettingsView.list.title\": \"Zobrazit nastavení\",\n    \"emptyAttributes.title\": \"Zatím zde nejsou žádná pole\",\n    \"error.attribute.key.taken\": \"Hodnota již existuje\",\n    \"error.attribute.sameKeyAndName\": \"Hodnoty nesmí být stejné\",\n    \"error.attribute.taken\": \"Pole se stejným názvem již existuje\",\n    \"error.contentTypeName.taken\": \"Tento název již existuje\",\n    \"error.model.fetch\": \"Při pokusu o načtení nastavení modelů došlo k chybě.\",\n    \"error.record.create\": \"Při pokusu o vytvoření záznamu došlo k chybě.\",\n    \"error.record.delete\": \"Při pokusu o smazání záznamu došlo k chybě.\",\n    \"error.record.fetch\": \"Při pokusu o načtení záznamu došlo k chybě.\",\n    \"error.record.update\": \"Při pokusu o aktualizaci záznamu došlo k chybě.\",\n    \"error.records.count\": \"Při pokusu o sečtení záznamů došlo k chybě.\",\n    \"error.records.fetch\": \"Při pokusu o načtení záznamů došlo k chybě.\",\n    \"error.schema.generation\": \"Při pokusu o vygenerování schématu došlo k chybě.\",\n    \"error.validation.json\": \"Toto není JSON\",\n    \"error.validation.max\": \"Hodnota je příliš vysoká.\",\n    \"error.validation.maxLength\": \"Hodnota je příliš dlouhá.\",\n    \"error.validation.min\": \"Hodnota je příliš nízká.\",\n    \"error.validation.minLength\": \"Hodnota je příliš krátká.\",\n    \"error.validation.minSupMax\": \"Nemůže být větší\",\n    \"error.validation.regex\": \"Hodnota neodpovídá poskytnutému regexu.\",\n    \"error.validation.required\": \"Vstup hodnoty je povinná položka.\",\n    \"form.Input.bulkActions\": \"Enable bulk actions\",\n    \"form.Input.defaultSort\": \"Výchozí atribut řazení\",\n    \"form.Input.description\": \"Popis\",\n    \"form.Input.description.placeholder\": \"Zobrazit název v profilu\",\n    \"form.Input.editable\": \"Upravitelné pole\",\n    \"form.Input.filters\": \"Povolit filtry\",\n    \"form.Input.label\": \"Štítek\",\n    \"form.Input.label.inputDescription\": \"Tato hodnota přepíše štítek zobrazený v hlavičce tabulky\",\n    \"form.Input.pageEntries\": \"Záznamů na stránku\",\n    \"form.Input.placeholder\": \"Moje hodnota\",\n    \"form.Input.placeholder.placeholder\": \"Moje hodnota\",\n    \"form.Input.search\": \"Povolit vyhledávání\",\n    \"form.Input.search.field\": \"Povolit vyhledávání na tomto poli\",\n    \"form.Input.sort.field\": \"Povolit řazení na tomto poli\",\n    \"form.Input.wysiwyg\": \"Zobrazit jako WYSIWYG\",\n    \"global.displayedFields\": \"Zobrazená pole\",\n    groups: groups,\n    \"groups.numbered\": \"Skupiny ({number})\",\n    \"notification.error.displayedFields\": \"Alespoň jedno pole musí být zobrazeno\",\n    \"notification.error.relationship.fetch\": \"Při načítání relačních vazeb došlo k chybě.\",\n    \"notification.info.SettingPage.disableSort\": \"Musíte mít alespoň jeden atribut s povolením řazením.\",\n    \"notification.info.minimumFields\": \"Je třeba mít zobrazeno alespoň jedno pole.\",\n    \"notification.upload.error\": \"Při nahrávání vašich souborů došlo k chybě\",\n    pageNotFound: pageNotFound,\n    \"plugin.description.long\": \"Zásuvný modul pro zobrazení, úpravu a mazání dat ve vaší databázi.\",\n    \"plugin.description.short\": \"Zásuvný modul pro zobrazení, úpravu a mazání dat ve vaší databázi.\",\n    \"popUpWarning.bodyMessage.contentType.delete\": \"Jste si jisti že chcete odstranit tento záznam?\",\n    \"popUpWarning.bodyMessage.contentType.delete.all\": \"Jste si jisti, že chcete odstranit tyto záznamy?\",\n    \"popUpWarning.warning.cancelAllSettings\": \"Jste si jisti, že chcete zrušit všechny vaše úpravy?\",\n    \"popUpWarning.warning.updateAllSettings\": \"Toto změní všechna vaše nastavení\",\n    \"success.record.delete\": \"Odstraněno\",\n    \"success.record.save\": \"Uloženo\"\n};\n\nexport { cs as default, groups, pageNotFound };\n//# sourceMappingURL=cs.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,SAAS;AACb,IAAI,eAAe;AACnB,IAAI,KAAK;AAAA,EACL,uBAAuB;AAAA,EACvB,+BAA+B;AAAA,EAC/B,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,4DAA4D;AAAA,EAC5D,+DAA+D;AAAA,EAC/D,0DAA0D;AAAA,EAC1D,2DAA2D;AAAA,EAC3D,sCAAsC;AAAA,EACtC,uCAAuC;AAAA,EACvC,iCAAiC;AAAA,EACjC,yEAAyE;AAAA,EACzE,yEAAyE;AAAA,EACzE,qDAAqD;AAAA,EACrD,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,uCAAuC;AAAA,EACvC,+BAA+B;AAAA,EAC/B,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,0BAA0B;AAAA,EAC1B,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,6BAA6B;AAAA,EAC7B,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,0CAA0C;AAAA,EAC1C,yBAAyB;AAAA,EACzB,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,qDAAqD;AAAA,EACrD,2CAA2C;AAAA,EAC3C,gCAAgC;AAAA,EAChC,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,qDAAqD;AAAA,EACrD,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,+DAA+D;AAAA,EAC/D,6CAA6C;AAAA,EAC7C,iCAAiC;AAAA,EACjC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,kDAAkD;AAAA,EAClD,yDAAyD;AAAA,EACzD,uDAAuD;AAAA,EACvD,sCAAsC;AAAA,EACtC,yBAAyB;AAAA,EACzB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,qCAAqC;AAAA,EACrC,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B;AAAA,EACA,mBAAmB;AAAA,EACnB,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B;AAAA,EACA,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,+CAA+C;AAAA,EAC/C,mDAAmD;AAAA,EACnD,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,yBAAyB;AAAA,EACzB,uBAAuB;AAC3B;", "names": []}