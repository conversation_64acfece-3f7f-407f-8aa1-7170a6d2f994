{"version": 3, "sources": ["../../../@strapi/plugin-users-permissions/dist/admin/translations/cs.json.mjs"], "sourcesContent": ["var cs = {\n    \"BoundRoute.title\": \"<PERSON>pojit adresu s\",\n    \"EditForm.inputSelect.description.role\": \"Připojí nově autentifikovaného uživatele ke svolené roli.\",\n    \"EditForm.inputSelect.label.role\": \"Vý<PERSON><PERSON>í role pro autentifikovaného uživatele\",\n    \"EditForm.inputToggle.description.email\": \"Zabránit uživateli vytvářet různé účty se stejným e-mailem a jinými poskytovateli autentifikace.\",\n    \"EditForm.inputToggle.description.email-confirmation\": \"Pokud je tato funkce povolena (ON), nově registrovaní uživatelé dostanou potvrzující e-mail.\",\n    \"EditForm.inputToggle.description.email-confirmation-redirection\": \"Po potvrzení e-mailu, zvolte kam budete přesměrováni.\",\n    \"EditForm.inputToggle.description.email-reset-password\": \"<PERSON><PERSON><PERSON> strán<PERSON> obnoven<PERSON> hesla vaší aplikace\",\n    \"EditForm.inputToggle.description.sign-up\": \"Pokud je tato mož<PERSON>a (OFF), není možno projít registrací. Nikdo se již nemůže připojit, bez ohledu jakého použije poskytovatele.\",\n    \"EditForm.inputToggle.label.email\": \"Jeden účet na e-mail\",\n    \"EditForm.inputToggle.label.email-confirmation\": \"Povolit potvrzení z e-mailu\",\n    \"EditForm.inputToggle.label.email-confirmation-redirection\": \"Adresa pro přesměrování\",\n    \"EditForm.inputToggle.label.email-reset-password\": \"Stránka pro obnovení hesla\",\n    \"EditForm.inputToggle.label.sign-up\": \"Povolit registrace\",\n    \"HeaderNav.link.advancedSettings\": \"Pokročilá nastavení\",\n    \"HeaderNav.link.emailTemplates\": \"E-mailové šablony\",\n    \"HeaderNav.link.providers\": \"Poskytovatelé\",\n    \"Plugin.permissions.plugins.description\": \"Nastavit všechny akce pro zásuvný modul {name}.\",\n    \"Plugins.header.description\": \"Pouze akce spojené s adresou jsou vypsány níže.\",\n    \"Plugins.header.title\": \"Povolení\",\n    \"Policies.header.hint\": \"Vyberte akce aplikace, nebo akce zásuvného modulu a klikněte na ikonku ozubeného kolečka pro zobrazení adresy s nimi spojenou.\",\n    \"Policies.header.title\": \"Pokročilá nastavení\",\n    \"PopUpForm.Email.email_templates.inputDescription\": \"Pokud si nejste jisti jak používat proměnné, {link}\",\n    \"PopUpForm.Email.options.from.email.label\": \"Odesilatelův e-mail\",\n    \"PopUpForm.Email.options.from.email.placeholder\": \"<EMAIL>\",\n    \"PopUpForm.Email.options.from.name.label\": \"Jméno odesilatele\",\n    \"PopUpForm.Email.options.from.name.placeholder\": \"Jan Novák\",\n    \"PopUpForm.Email.options.message.label\": \"Zpráva\",\n    \"PopUpForm.Email.options.object.label\": \"Předmět\",\n    \"PopUpForm.Email.options.response_email.label\": \"Response e-mail\",\n    \"PopUpForm.Email.options.response_email.placeholder\": \"<EMAIL>\",\n    \"PopUpForm.Providers.enabled.description\": \"If disabled, users won't be able to use this provider.\",\n    \"PopUpForm.Providers.enabled.label\": \"Povolit\",\n    \"PopUpForm.Providers.key.label\": \"Client ID\",\n    \"PopUpForm.Providers.key.placeholder\": \"TEXT\",\n    \"PopUpForm.Providers.redirectURL.front-end.label\": \"Adresa pro přesměrování na vaši front-end aplikaci\",\n    \"PopUpForm.Providers.secret.label\": \"Client Secret\",\n    \"PopUpForm.Providers.secret.placeholder\": \"TEXT\",\n    \"PopUpForm.Providers.subdomain.label\": \"Host URI (Subdomain)\",\n    \"PopUpForm.Providers.subdomain.placeholder\": \"my.subdomain.com\",\n    \"PopUpForm.header.edit.email-templates\": \"Upravit e-mailové šablony\",\n    \"notification.success.submit\": \"Nastavení bylo aktualizování\",\n    \"plugin.description.long\": \"Chraňte své API pomocí kompletního autentifikačního procesu, založeného na JWT. Tento zásuvný modul obsahuje ACL strategii, která vám umožní spravovat oprávnění mezi skupinami uživatelů.\",\n    \"plugin.description.short\": \"Chraňte své API pomocí kompletního autentifikačního procesu, založeného na JWT\",\n    \"plugin.name\": \"Role a oprávnění\"\n};\n\nexport { cs as default };\n//# sourceMappingURL=cs.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,KAAK;AAAA,EACL,oBAAoB;AAAA,EACpB,yCAAyC;AAAA,EACzC,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,uDAAuD;AAAA,EACvD,mEAAmE;AAAA,EACnE,yDAAyD;AAAA,EACzD,4CAA4C;AAAA,EAC5C,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,6DAA6D;AAAA,EAC7D,mDAAmD;AAAA,EACnD,sCAAsC;AAAA,EACtC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,0CAA0C;AAAA,EAC1C,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,oDAAoD;AAAA,EACpD,4CAA4C;AAAA,EAC5C,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,iDAAiD;AAAA,EACjD,yCAAyC;AAAA,EACzC,wCAAwC;AAAA,EACxC,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,iCAAiC;AAAA,EACjC,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,oCAAoC;AAAA,EACpC,0CAA0C;AAAA,EAC1C,uCAAuC;AAAA,EACvC,6CAA6C;AAAA,EAC7C,yCAAyC;AAAA,EACzC,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,eAAe;AACnB;", "names": []}