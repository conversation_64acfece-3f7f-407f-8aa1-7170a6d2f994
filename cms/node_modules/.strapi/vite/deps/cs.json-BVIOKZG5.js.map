{"version": 3, "sources": ["../../../@strapi/content-type-builder/dist/admin/translations/cs.json.mjs"], "sourcesContent": ["var configurations = \"nastavení\";\nvar from = \"od\";\nvar cs = {\n    \"attribute.boolean\": \"Boolean\",\n    \"attribute.boolean.description\": \"Yes/no, 1/0, true/false\",\n    \"attribute.component\": \"Komponent\",\n    \"attribute.component.description\": \"<PERSON><PERSON><PERSON> polí, které je možné opakovaně používat\",\n    \"attribute.date\": \"Datum a čas\",\n    \"attribute.date.description\": \"Dialog pro výběr datumu a času\",\n    \"attribute.datetime\": \"Datum a čas\",\n    \"attribute.dynamiczone\": \"Dynamická zóna\",\n    \"attribute.dynamiczone.description\": \"Umožňuje dynamicky zvolit komponenty při úpravách obsahu\",\n    \"attribute.email\": \"E-mailová adresa\",\n    \"attribute.email.description\": \"Pole s automatickou validací formátu e-mailové adresy\",\n    \"attribute.enumeration\": \"Výčet\",\n    \"attribute.enumeration.description\": \"Seznam hodnot s výběrem jedn<PERSON>\",\n    \"attribute.json\": \"JSON\",\n    \"attribute.json.description\": \"Data vo formátu JSON\",\n    \"attribute.media\": \"Soubory\",\n    \"attribute.media.description\": \"Např. obr<PERSON><PERSON>, videa, ...\",\n    \"attribute.null\": \" \",\n    \"attribute.number\": \"Číslo\",\n    \"attribute.number.description\": \"Čísla (celé, desetinné)\",\n    \"attribute.password\": \"Heslo\",\n    \"attribute.password.description\": \"Políčko pro zadání hesla\",\n    \"attribute.relation\": \"Relace\",\n    \"attribute.relation.description\": \"Určuje vztah k jinému Typu obsahu\",\n    \"attribute.richtext\": \"Textový editor\",\n    \"attribute.richtext.description\": \"Textové pole s možnostmi formátování\",\n    \"attribute.text\": \"Text\",\n    \"attribute.text.description\": \"Krátký nebo delší text\",\n    \"attribute.time\": \"Čas\",\n    \"attribute.uid\": \"UID\",\n    \"attribute.uid.description\": \"Unikátní identifikátor\",\n    \"button.attributes.add.another\": \"Přidat další pole\",\n    \"button.component.add\": \"Přidat komponent\",\n    \"button.component.create\": \"Vytvorit nový komponent\",\n    \"button.model.create\": \"Vytvořit nový Typ obsahu\",\n    \"component.repeatable\": \"(opakující)\",\n    \"components.componentSelect.no-component-available\": \"Už jste přidali všechny komponenty\",\n    \"components.componentSelect.no-component-available.with-search\": \"Nenašel se žádný komponent splňující výraz\",\n    \"components.componentSelect.value-component\": \"Označené komponenty: {number} (zadejte hledaný text)\",\n    \"components.componentSelect.value-components\": \"Označené komponenty: {number}\",\n    configurations: configurations,\n    \"contentType.collectionName.label\": \"Interní název\",\n    \"contentType.displayName.label\": \"Název\",\n    \"error.contentTypeName.reserved-name\": \"Tento název je vyhrazený a nemůže být použit\",\n    \"error.validation.minSupMax\": \"Nemůže být nadřazený\",\n    \"form.attribute.component.option.add\": \"Přidat komponent\",\n    \"form.attribute.component.option.create\": \"Vytvořit nový komponent\",\n    \"form.attribute.component.option.create.description\": \"Komponent je dostupný mezi všemi typy a komponenty.\",\n    \"form.attribute.component.option.repeatable\": \"Znovu použitelný komponent\",\n    \"form.attribute.component.option.repeatable.description\": \"Nejlepší pro několikanásobné instance (pole) hodnot, meta tagy, apod...\",\n    \"form.attribute.component.option.reuse-existing\": \"Použít existující komponent\",\n    \"form.attribute.component.option.reuse-existing.description\": \"Používejte už vytvořené komponenty pro uchování konzistentních dat mezi Typy obsahu.\",\n    \"form.attribute.component.option.single\": \"Jednoduchý komponent\",\n    \"form.attribute.component.option.single.description\": \"Vhodné pro seskupení políček, např. celá adresa\",\n    \"form.attribute.item.customColumnName\": \"Vlastné názvy stĺpcov\",\n    \"form.attribute.item.customColumnName.description\": \"Umožňuje přejmenovat databázový sloupec pro potřeby API\",\n    \"form.attribute.item.defineRelation.fieldName\": \"Název pole\",\n    \"form.attribute.item.enumeration.graphql\": \"Název pole pro GraphQL\",\n    \"form.attribute.item.enumeration.graphql.description\": \"Umožňuje přepsat přednastavené názvy názvy pro GraphQL\",\n    \"form.attribute.item.enumeration.placeholder\": \"Např.:\\nráno\\nden\\nvečer\",\n    \"form.attribute.item.enumeration.rules\": \"Hodnoty (jedna na řádek)\",\n    \"form.attribute.item.maximum\": \"Maximální hodnota\",\n    \"form.attribute.item.maximumLength\": \"Maximální délka\",\n    \"form.attribute.item.minimum\": \"Minimální hodnota\",\n    \"form.attribute.item.minimumLength\": \"Minimální délka\",\n    \"form.attribute.item.number.type\": \"Číselný formát\",\n    \"form.attribute.item.number.type.biginteger\": \"velké číslo (např.: 123456789)\",\n    \"form.attribute.item.number.type.decimal\": \"desetinné číslo (např.: 2.22)\",\n    \"form.attribute.item.number.type.float\": \"desetinné číslo (např.: 3.33333333)\",\n    \"form.attribute.item.number.type.integer\": \"celé číslo (např.: 10)\",\n    \"form.attribute.item.privateField\": \"Skryté pole\",\n    \"form.attribute.item.privateField.description\": \"Toto pole se nebude zobrazovat v API\",\n    \"form.attribute.item.requiredField\": \"Povinné pole\",\n    \"form.attribute.item.requiredField.description\": \"Nedovolí vytvořit záznam když toto pole zůstane prázdne\",\n    \"form.attribute.item.uniqueField\": \"Unikátní pole\",\n    \"form.attribute.item.uniqueField.description\": \"Nedovolí vytvořit záznam když už existuje jiný záznam se stejnou hodnotou\",\n    \"form.attribute.media.option.multiple\": \"Více souborů\",\n    \"form.attribute.media.option.multiple.description\": \"Vhodné pro galerii, seznam souborů na stáhnutí\",\n    \"form.attribute.media.option.single\": \"Jeden soubor\",\n    \"form.attribute.media.option.single.description\": \"Vhodné pro profilovou fotku nebo hlavní obrázek\",\n    \"form.attribute.settings.default\": \"Předvolená hodnota\",\n    \"form.attribute.text.option.long-text\": \"Dlouhý text\",\n    \"form.attribute.text.option.long-text.description\": \"Vhodné pro delší popisy. Přesné vyhledávání je vypnuté.\",\n    \"form.attribute.text.option.short-text\": \"Krátky text\",\n    \"form.attribute.text.option.short-text.description\": \"Vhodné pro nadpisy, názvy, URL adresy. Přesné vyhledávání je zapnuté.\",\n    \"form.button.add-components-to-dynamiczone\": \"Přidat komponenty do zóny\",\n    \"form.button.add-field\": \"Přidat další pole\",\n    \"form.button.add-first-field-to-created-component\": \"Přidat první pole do komponentu\",\n    \"form.button.add.field.to.component\": \"Přidat další pole do komponentu\",\n    \"form.button.cancel\": \"Zrušit\",\n    \"form.button.configure-component\": \"Nastavit komponent\",\n    \"form.button.configure-view\": \"Upravit vzhled\",\n    \"form.button.select-component\": \"Vybrat komponent\",\n    from: from,\n    \"modalForm.attribute.form.base.name.description\": \"Mezery v názvu pole nejsou povoleny\",\n    \"modalForm.attributes.select-component\": \"Vyberte komponent\",\n    \"modalForm.attributes.select-components\": \"Vyberte komponenty\",\n    \"modalForm.component.header-create\": \"Vytvorte komponent\",\n    \"modalForm.components.create-component.category.label\": \"Vyberte kategorii nebo zadejte název pro vytvoření nové\",\n    \"modalForm.components.icon.label\": \"Ikona\",\n    \"modalForm.editCategory.base.name.description\": \"Mezery v názvu kategorie nejsou povoleny\",\n    \"modalForm.header-edit\": \"Upravit {name}\",\n    \"modalForm.header.categories\": \"Kategorie\",\n    \"modalForm.header.back\": \"Zadní\",\n    \"modalForm.sub-header.addComponentToDynamicZone\": \"Přidat nový komponent do dynamické zóny\",\n    \"modalForm.sub-header.attribute.create\": \"Přidat nové pole {type}\",\n    \"modalForm.sub-header.attribute.create.step\": \"Přidat nový komponent ({step}/2)\",\n    \"modalForm.sub-header.attribute.edit\": \"Upravit {name}\",\n    \"modalForm.sub-header.chooseAttribute.collectionType\": \"Vyberte typ pole pro Typ obsahu\",\n    \"modalForm.sub-header.chooseAttribute.component\": \"Vyberte typ pole pro komponent\",\n    \"modelPage.attribute.relationWith\": \"Propojení s\",\n    \"notification.info.creating.notSaved\": \"Uložte změny před vytvořením nového Typu obsahu nebo komponentu\",\n    \"plugin.description.long\": \"Navrhněte strukturu webu jednoduše. Vytvořte nová pole a propojení během pár sekund. Soubory se automaticky vytvoří a upraví v rámci projektu.\",\n    \"plugin.description.short\": \"Navrhněte strukturu webu jednoduše.\",\n    \"popUpForm.navContainer.advanced\": \"Pokročilá nastavení\",\n    \"popUpForm.navContainer.base\": \"Základní nastavení\",\n    \"popUpWarning.bodyMessage.cancel-modifications\": \"Jste si jisti, že chcete zrušit úpravy?\",\n    \"popUpWarning.bodyMessage.cancel-modifications.with-components\": \"Jste si jisti, že chcete zrušit úpravy? Některé komponenty byly vytvořeny nebo upraveny...\",\n    \"popUpWarning.bodyMessage.category.delete\": \"Jste si jisti, že chcete odstranit tuto kategorii? Všechny komponentu budou rovněž odstraněny.\",\n    \"popUpWarning.bodyMessage.component.delete\": \"Jste si jisti, že chcete odstranit tento komponent?\",\n    \"popUpWarning.bodyMessage.contentType.delete\": \"Jste si jisti, že chcete odstranit tento Typ obsahu?\",\n    \"prompt.unsaved\": \"Jste si jisti, že chcete odejít? Všechny úpravy budou ztraceny.\",\n    \"relation.attributeName.placeholder\": \"Např: autor, kategorie, tag\",\n    \"relation.manyToMany\": \"má víc a patří všem\",\n    \"relation.manyToOne\": \"má víc\",\n    \"relation.manyWay\": \"má víc\",\n    \"relation.oneToMany\": \"patří více\",\n    \"relation.oneToOne\": \"má jeden a patří jednomu\",\n    \"relation.oneWay\": \"má jeden\"\n};\n\nexport { configurations, cs as default, from };\n//# sourceMappingURL=cs.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,iBAAiB;AACrB,IAAI,OAAO;AACX,IAAI,KAAK;AAAA,EACL,qBAAqB;AAAA,EACrB,iCAAiC;AAAA,EACjC,uBAAuB;AAAA,EACvB,mCAAmC;AAAA,EACnC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,mBAAmB;AAAA,EACnB,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,mBAAmB;AAAA,EACnB,+BAA+B;AAAA,EAC/B,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,gCAAgC;AAAA,EAChC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,wBAAwB;AAAA,EACxB,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,qDAAqD;AAAA,EACrD,iEAAiE;AAAA,EACjE,8CAA8C;AAAA,EAC9C,+CAA+C;AAAA,EAC/C;AAAA,EACA,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,sDAAsD;AAAA,EACtD,8CAA8C;AAAA,EAC9C,0DAA0D;AAAA,EAC1D,kDAAkD;AAAA,EAClD,8DAA8D;AAAA,EAC9D,0CAA0C;AAAA,EAC1C,sDAAsD;AAAA,EACtD,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,gDAAgD;AAAA,EAChD,2CAA2C;AAAA,EAC3C,uDAAuD;AAAA,EACvD,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+BAA+B;AAAA,EAC/B,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,8CAA8C;AAAA,EAC9C,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,oCAAoC;AAAA,EACpC,gDAAgD;AAAA,EAChD,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,sCAAsC;AAAA,EACtC,kDAAkD;AAAA,EAClD,mCAAmC;AAAA,EACnC,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,6CAA6C;AAAA,EAC7C,yBAAyB;AAAA,EACzB,oDAAoD;AAAA,EACpD,sCAAsC;AAAA,EACtC,sBAAsB;AAAA,EACtB,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,gCAAgC;AAAA,EAChC;AAAA,EACA,kDAAkD;AAAA,EAClD,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,qCAAqC;AAAA,EACrC,wDAAwD;AAAA,EACxD,mCAAmC;AAAA,EACnC,gDAAgD;AAAA,EAChD,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,kDAAkD;AAAA,EAClD,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,oCAAoC;AAAA,EACpC,uCAAuC;AAAA,EACvC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,iDAAiD;AAAA,EACjD,iEAAiE;AAAA,EACjE,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,+CAA+C;AAAA,EAC/C,kBAAkB;AAAA,EAClB,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,qBAAqB;AAAA,EACrB,mBAAmB;AACvB;", "names": []}