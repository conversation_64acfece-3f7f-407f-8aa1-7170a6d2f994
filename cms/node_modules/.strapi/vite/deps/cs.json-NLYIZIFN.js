import "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/admin/src/translations/cs.json.mjs
var Analytics = "Analýzy";
var Documentation = "Dokumentace";
var Email = "E-mailová adresa";
var Password = "Heslo";
var Provider = "Poskytovatel";
var ResetPasswordToken = "Token pro obnovu hesla";
var Role = "Role";
var Username = "Uživatelské jméno";
var Users = "Uživatelé";
var cs = {
  Analytics,
  "Auth.form.button.forgot-password": "Odeslat e-mail",
  "Auth.form.button.login": "Přihlásit se",
  "Auth.form.button.register": "Připraven ke startu",
  "Auth.form.email.label": "E-mailová adresa",
  "Auth.form.email.placeholder": "<EMAIL>",
  "Auth.form.error.blocked": "<PERSON><PERSON><PERSON> byl zab<PERSON>án správcem.",
  "Auth.form.error.code.provide": "<PERSON><PERSON> z<PERSON><PERSON> nesprávný kód.",
  "Auth.form.error.confirmed": "E-mail vašeho účtu není potvrzen.",
  "Auth.form.error.email.invalid": "Tento e-mail je neplatný.",
  "Auth.form.error.email.provide": "Zadejte své uživatelské jméno nebo e-mail.",
  "Auth.form.error.email.taken": "E-mail je již obsazen.",
  "Auth.form.error.invalid": "Identifikátor nebo heslo jsou neplatné.",
  "Auth.form.error.params.provide": "Byly zadány nesprávné parametry.",
  "Auth.form.error.password.format": "Vaše heslo nesmí obsahovat symbol `$` více než třikrát.",
  "Auth.form.error.password.local": "Tento uživatel nikdy nenastavil místní heslo, prosím přihlašte se přes poskytovatele použitého při vytváření účtu.",
  "Auth.form.error.password.matching": "Hesla se neshodují.",
  "Auth.form.error.password.provide": "Zadejte své heslo.",
  "Auth.form.error.ratelimit": "Příliš mnoho pokusů, prosím, zkuste to znovu za chvíli.",
  "Auth.form.error.user.not-exist": "Tento e-mail neexistuje.",
  "Auth.form.error.username.taken": "Uživatelské jméno je již obsazeno.",
  "Auth.form.forgot-password.email.label": "Zadejte svůj e-mail",
  "Auth.form.forgot-password.email.label.success": "E-mail úspěšně odeslán na",
  "Auth.form.register.news.label": "Informujte mě o nových funkcích a připravovaných vylepšeních (tím souhlasíte s {terms} a {policy}).",
  "Auth.form.rememberMe.label": "Zapamatovat si mě",
  "Auth.form.username.label": "Uživatelské jméno",
  "Auth.form.username.placeholder": "Jan Novák",
  "Auth.link.forgot-password": "Zapomněli jste své heslo?",
  "Auth.link.ready": "Jste připraveni se přihlásit?",
  "Auth.privacy-policy-agreement.policy": "zásady ochrany osobních údajů",
  "Auth.privacy-policy-agreement.terms": "podmínky",
  "Content Manager": "Správce obsahu",
  Documentation,
  Email,
  "Files Upload": "Nahrát soubory",
  "HomePage.head.title": "Úvodní stránka",
  "HomePage.roadmap": "Podívejte se na náš harmonogram prací",
  "HomePage.welcome.congrats": "Gratulujeme!",
  "HomePage.welcome.congrats.content": "Jste poprvé přihlášeni jako správce. Chcete-li objevit výkonné funkce poskytované Strapi,",
  "New entry": "Nový záznam",
  Password,
  Provider,
  ResetPasswordToken,
  Role,
  Username,
  Users,
  "Users & Permissions": "Uživatelé a oprávnění",
  "app.components.BlockLink.code": "Příklady kódu",
  "app.components.Button.cancel": "Zrušit",
  "app.components.ComingSoonPage.comingSoon": "Již brzy",
  "app.components.DownloadInfo.download": "Probíhá stahování...",
  "app.components.DownloadInfo.text": "Může to chvíli trvat. Díky za trpělivost.",
  "app.components.EmptyAttributes.title": "Zatím zde nejsou žádná pole",
  "app.components.HomePage.button.blog": "VÍCE NA BLOGU",
  "app.components.HomePage.community": "Najděte komunitu na webu",
  "app.components.HomePage.community.content": "Diskutujte s členy, přispěvateli a vývojáři na různých kanálech.",
  "app.components.HomePage.welcome": "Vítejte na palubě!",
  "app.components.HomePage.welcome.again": "Vítejte ",
  "app.components.HomePage.welcomeBlock.content": "Jsme rádi, že jste součástí komunity. Neustále hledáme zpětnou vazbu, neváhejte nám proto poslat přímou zprávu na ",
  "app.components.HomePage.welcomeBlock.content.again": "Doufáme, že zdařile pokračujete na svém projektu... Nebojte se přečíst novinky o Strapi. Uděláme vše pro vylepšení produktu na základě vaší zpětné vazby.",
  "app.components.HomePage.welcomeBlock.content.issues": "témata.",
  "app.components.HomePage.welcomeBlock.content.raise": " nebo zvýšit ",
  "app.components.ImgPreview.hint": "Přetáhněte soubor do této oblasti nebo {browse} pro nahrání",
  "app.components.ImgPreview.hint.browse": "procházet",
  "app.components.InputFile.newFile": "Přidat nový soubor",
  "app.components.InputFileDetails.open": "Otevřít v nové kartě",
  "app.components.InputFileDetails.originalName": "Původní název:",
  "app.components.InputFileDetails.remove": "Odstranit tento soubor",
  "app.components.InputFileDetails.size": "Velikost:",
  "app.components.InstallPluginPage.Download.description": "Může to trvat několik sekund, než se stáhne a nainstaluje zásuvný modul.",
  "app.components.InstallPluginPage.Download.title": "Stahování...",
  "app.components.InstallPluginPage.description": "Rozšiřte snadno svou aplikaci.",
  "app.components.LeftMenuFooter.help": "Nápověda",
  "app.components.LeftMenuFooter.poweredBy": "Běží na ",
  "app.components.LeftMenuLinkContainer.configuration": "Konfigurace",
  "app.components.LeftMenuLinkContainer.general": "Obecné",
  "app.components.LeftMenuLinkContainer.noPluginsInstalled": "Zatím nebyly instalovány žádné zásuvné moduly",
  "app.components.LeftMenuLinkContainer.plugins": "Zásuvné moduly",
  "app.components.ListPluginsPage.description": "Seznam instalovaných zásuvných modulů v projektu.",
  "app.components.ListPluginsPage.head.title": "Seznam zásuvných modulů",
  "app.components.Logout.logout": "Odhlásit se",
  "app.components.Logout.profile": "Profil",
  "app.components.NotFoundPage.back": "Zpět na úvodní stránku",
  "app.components.NotFoundPage.description": "Nenalezeno",
  "app.components.Official": "Oficiální",
  "app.components.Onboarding.label.completed": "% dokončeno",
  "app.components.Onboarding.title": "Videa Jak začít",
  "app.components.PluginCard.Button.label.download": "Stáhnout",
  "app.components.PluginCard.Button.label.install": "Již instalováno",
  "app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed": "Funkce automatického obnovení musí být vypnuta. Spusťte aplikaci příkazem `yarn develop`.",
  "app.components.PluginCard.PopUpWarning.install.impossible.confirm": "Rozumím!",
  "app.components.PluginCard.PopUpWarning.install.impossible.environment": "Z bezpečnostních důvodů lze zásuvný modul stáhnout pouze ve vývojářském prostředí.",
  "app.components.PluginCard.PopUpWarning.install.impossible.title": "Stahování není možné",
  "app.components.PluginCard.compatible": "Kompatibilní s vaší aplikací",
  "app.components.PluginCard.compatibleCommunity": "Kompatibilní s komunitou",
  "app.components.PluginCard.more-details": "Více podrobností",
  "app.components.listPlugins.button": "Přidat nový zásuvný modul",
  "app.components.listPlugins.title.none": "Nejsou instalovány žádné zásuvné moduly",
  "app.components.listPluginsPage.deletePlugin.error": "Došlo k chybě při odinstalaci zásuvného modulu",
  "app.containers.App.notification.error.init": "Při provádění požadavku na API došlo k chybě",
  "app.links.configure-view": "Upravit vzhled",
  "app.utils.SelectOption.defaultMessage": " ",
  "app.utils.defaultMessage": " ",
  "app.utils.placeholder.defaultMessage": " ",
  "component.Input.error.validation.integer": "Tato hodnota musí být celé číslo",
  "components.AutoReloadBlocker.description": "Spusťte Strapi jedním z následujících příkazů:",
  "components.AutoReloadBlocker.header": "Pro tento zásuvný modul musí být zapnuta funkce znovu načítání.",
  "components.ErrorBoundary.title": "Něco se pokazilo...",
  "components.Input.error.attribute.key.taken": "Tato hodnota již existuje",
  "components.Input.error.attribute.sameKeyAndName": "Hodnoty nesmí být stejné",
  "components.Input.error.attribute.taken": "Název tohoto pole již existuje",
  "components.Input.error.contentTypeName.taken": "Tento název již existuje",
  "components.Input.error.custom-error": "{errorMessage} ",
  "components.Input.error.password.noMatch": "Hesla se neshodují",
  "components.Input.error.validation.email": "Toto není e-mailová adresa",
  "components.Input.error.validation.json": "Toto se neshoduje s formátem JSON",
  "components.Input.error.validation.max": "Hodnota je příliš vysoká {max}.",
  "components.Input.error.validation.maxLength": "Hodnota je příliš dlouhá {max}.",
  "components.Input.error.validation.min": "Hodnota je příliš nízká {min}.",
  "components.Input.error.validation.minLength": "Hodnota je příliš krátká {min}.",
  "components.Input.error.validation.minSupMax": "Nemůže být nadřazený",
  "components.Input.error.validation.regex": "Hodnota neodpovídá požadovanému vzoru.",
  "components.Input.error.validation.required": "Tato hodnota je povinná.",
  "components.Input.error.validation.unique": "Tato hodnota již byla použita.",
  "components.InputSelect.option.placeholder": "Vyberte zde",
  "components.ListRow.empty": "Žádná data k zobrazení.",
  "components.OverlayBlocker.description": "Používáte funkcionalitu, která potřebuje restartovat server. Počkejte, až se server opět spustí.",
  "components.OverlayBlocker.description.serverError": "Server by měl být restartován, zkontrolujte prosím protokoly v terminálu.",
  "components.OverlayBlocker.title": "Čekání na restartování...",
  "components.OverlayBlocker.title.serverError": "Restartování trvá déle, než se očekávalo",
  "components.PageFooter.select": "položek na stránku",
  "components.ProductionBlocker.description": "Z bezpečnostních důvodů musíme tento zásuvný modul v jiných prostředích zakázat.",
  "components.ProductionBlocker.header": "Tento zásuvný modul je dostupný pouze ve vývoji.",
  "components.Wysiwyg.collapse": "Sbalit",
  "components.Wysiwyg.selectOptions.H1": "Nadpis H1",
  "components.Wysiwyg.selectOptions.H2": "Nadpis H2",
  "components.Wysiwyg.selectOptions.H3": "Nadpis H3",
  "components.Wysiwyg.selectOptions.H4": "Nadpis H4",
  "components.Wysiwyg.selectOptions.H5": "Nadpis H5",
  "components.Wysiwyg.selectOptions.H6": "Nadpis H6",
  "components.Wysiwyg.selectOptions.title": "Přidat název",
  "components.WysiwygBottomControls.charactersIndicators": "znaky",
  "components.WysiwygBottomControls.fullscreen": "Rozbalit",
  "components.WysiwygBottomControls.uploadFiles": "Přetáhněte soubory, vložte ze schránky nebo {browse}.",
  "components.WysiwygBottomControls.uploadFiles.browse": "vyberte je",
  "components.popUpWarning.message": "Opravdu to chcete smazat?",
  "components.popUpWarning.title": "Prosím potvrďte",
  "form.button.done": "OK",
  "notification.error": "Došlo k chybě",
  "notification.error.layout": "Nelze načíst rozložení",
  "notification.form.error.fields": "Formulář obsahuje chyby",
  "request.error.model.unknown": "Tento model neexistuje"
};
export {
  Analytics,
  Documentation,
  Email,
  Password,
  Provider,
  ResetPasswordToken,
  Role,
  Username,
  Users,
  cs as default
};
//# sourceMappingURL=cs.json-NLYIZIFN.js.map
