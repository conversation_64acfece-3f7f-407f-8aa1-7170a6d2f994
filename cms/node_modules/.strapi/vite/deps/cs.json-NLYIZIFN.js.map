{"version": 3, "sources": ["../../../@strapi/admin/dist/admin/admin/src/translations/cs.json.mjs"], "sourcesContent": ["var Analytics = \"Analýzy\";\nvar Documentation = \"Dokumentace\";\nvar Email = \"E-mailová adresa\";\nvar Password = \"<PERSON><PERSON><PERSON>\";\nvar Provider = \"Poskytovatel\";\nvar ResetPasswordToken = \"Token pro obnovu hesla\";\nvar Role = \"Role\";\nvar Username = \"Uživatelské jméno\";\nvar Users = \"Uživatelé\";\nvar cs = {\n    Analytics: Analytics,\n    \"Auth.form.button.forgot-password\": \"Odeslat e-mail\",\n    \"Auth.form.button.login\": \"Přihlásit se\",\n    \"Auth.form.button.register\": \"Připraven ke startu\",\n    \"Auth.form.email.label\": \"E-mailová adresa\",\n    \"Auth.form.email.placeholder\": \"<EMAIL>\",\n    \"Auth.form.error.blocked\": \"<PERSON><PERSON><PERSON> byl zablo<PERSON> správcem.\",\n    \"Auth.form.error.code.provide\": \"<PERSON>l z<PERSON><PERSON> nespr<PERSON>vn<PERSON> kód.\",\n    \"Auth.form.error.confirmed\": \"E-mail vašeho ú<PERSON> není potvrzen.\",\n    \"Auth.form.error.email.invalid\": \"Tento e-mail je neplatný.\",\n    \"Auth.form.error.email.provide\": \"Zadejte své uživatelské jméno nebo e-mail.\",\n    \"Auth.form.error.email.taken\": \"E-mail je již obsazen.\",\n    \"Auth.form.error.invalid\": \"Identifikátor nebo heslo jsou neplatné.\",\n    \"Auth.form.error.params.provide\": \"Byly zadány nesprávné parametry.\",\n    \"Auth.form.error.password.format\": \"Vaše heslo nesmí obsahovat symbol `$` více než třikrát.\",\n    \"Auth.form.error.password.local\": \"Tento uživatel nikdy nenastavil místní heslo, prosím přihlašte se přes poskytovatele použitého při vytváření účtu.\",\n    \"Auth.form.error.password.matching\": \"Hesla se neshodují.\",\n    \"Auth.form.error.password.provide\": \"Zadejte své heslo.\",\n    \"Auth.form.error.ratelimit\": \"Příliš mnoho pokusů, prosím, zkuste to znovu za chvíli.\",\n    \"Auth.form.error.user.not-exist\": \"Tento e-mail neexistuje.\",\n    \"Auth.form.error.username.taken\": \"Uživatelské jméno je již obsazeno.\",\n    \"Auth.form.forgot-password.email.label\": \"Zadejte svůj e-mail\",\n    \"Auth.form.forgot-password.email.label.success\": \"E-mail úspěšně odeslán na\",\n    \"Auth.form.register.news.label\": \"Informujte mě o nových funkcích a připravovaných vylepšeních (tím souhlasíte s {terms} a {policy}).\",\n    \"Auth.form.rememberMe.label\": \"Zapamatovat si mě\",\n    \"Auth.form.username.label\": \"Uživatelské jméno\",\n    \"Auth.form.username.placeholder\": \"Jan Novák\",\n    \"Auth.link.forgot-password\": \"Zapomněli jste své heslo?\",\n    \"Auth.link.ready\": \"Jste připraveni se přihlásit?\",\n    \"Auth.privacy-policy-agreement.policy\": \"zásady ochrany osobních údajů\",\n    \"Auth.privacy-policy-agreement.terms\": \"podmínky\",\n    \"Content Manager\": \"Správce obsahu\",\n    Documentation: Documentation,\n    Email: Email,\n    \"Files Upload\": \"Nahrát soubory\",\n    \"HomePage.head.title\": \"Úvodní stránka\",\n    \"HomePage.roadmap\": \"Podívejte se na náš harmonogram prací\",\n    \"HomePage.welcome.congrats\": \"Gratulujeme!\",\n    \"HomePage.welcome.congrats.content\": \"Jste poprvé přihlášeni jako správce. Chcete-li objevit výkonné funkce poskytované Strapi,\",\n    \"New entry\": \"Nový záznam\",\n    Password: Password,\n    Provider: Provider,\n    ResetPasswordToken: ResetPasswordToken,\n    Role: Role,\n    Username: Username,\n    Users: Users,\n    \"Users & Permissions\": \"Uživatelé a oprávnění\",\n    \"app.components.BlockLink.code\": \"Příklady kódu\",\n    \"app.components.Button.cancel\": \"Zrušit\",\n    \"app.components.ComingSoonPage.comingSoon\": \"Již brzy\",\n    \"app.components.DownloadInfo.download\": \"Probíhá stahování...\",\n    \"app.components.DownloadInfo.text\": \"Může to chvíli trvat. Díky za trpělivost.\",\n    \"app.components.EmptyAttributes.title\": \"Zatím zde nejsou žádná pole\",\n    \"app.components.HomePage.button.blog\": \"VÍCE NA BLOGU\",\n    \"app.components.HomePage.community\": \"Najděte komunitu na webu\",\n    \"app.components.HomePage.community.content\": \"Diskutujte s členy, přispěvateli a vývojáři na různých kanálech.\",\n    \"app.components.HomePage.welcome\": \"Vítejte na palubě!\",\n    \"app.components.HomePage.welcome.again\": \"Vítejte \",\n    \"app.components.HomePage.welcomeBlock.content\": \"Jsme rádi, že jste součástí komunity. Neustále hledáme zpětnou vazbu, neváhejte nám proto poslat přímou zprávu na \",\n    \"app.components.HomePage.welcomeBlock.content.again\": \"Doufáme, že zdařile pokračujete na svém projektu... Nebojte se přečíst novinky o Strapi. Uděláme vše pro vylepšení produktu na základě vaší zpětné vazby.\",\n    \"app.components.HomePage.welcomeBlock.content.issues\": \"témata.\",\n    \"app.components.HomePage.welcomeBlock.content.raise\": \" nebo zvýšit \",\n    \"app.components.ImgPreview.hint\": \"Přetáhněte soubor do této oblasti nebo {browse} pro nahrání\",\n    \"app.components.ImgPreview.hint.browse\": \"procházet\",\n    \"app.components.InputFile.newFile\": \"Přidat nový soubor\",\n    \"app.components.InputFileDetails.open\": \"Otevřít v nové kartě\",\n    \"app.components.InputFileDetails.originalName\": \"Původní název:\",\n    \"app.components.InputFileDetails.remove\": \"Odstranit tento soubor\",\n    \"app.components.InputFileDetails.size\": \"Velikost:\",\n    \"app.components.InstallPluginPage.Download.description\": \"Může to trvat několik sekund, než se stáhne a nainstaluje zásuvný modul.\",\n    \"app.components.InstallPluginPage.Download.title\": \"Stahování...\",\n    \"app.components.InstallPluginPage.description\": \"Rozšiřte snadno svou aplikaci.\",\n    \"app.components.LeftMenuFooter.help\": \"Nápověda\",\n    \"app.components.LeftMenuFooter.poweredBy\": \"Běží na \",\n    \"app.components.LeftMenuLinkContainer.configuration\": \"Konfigurace\",\n    \"app.components.LeftMenuLinkContainer.general\": \"Obecné\",\n    \"app.components.LeftMenuLinkContainer.noPluginsInstalled\": \"Zatím nebyly instalovány žádné zásuvné moduly\",\n    \"app.components.LeftMenuLinkContainer.plugins\": \"Zásuvné moduly\",\n    \"app.components.ListPluginsPage.description\": \"Seznam instalovaných zásuvných modulů v projektu.\",\n    \"app.components.ListPluginsPage.head.title\": \"Seznam zásuvných modulů\",\n    \"app.components.Logout.logout\": \"Odhlásit se\",\n    \"app.components.Logout.profile\": \"Profil\",\n    \"app.components.NotFoundPage.back\": \"Zpět na úvodní stránku\",\n    \"app.components.NotFoundPage.description\": \"Nenalezeno\",\n    \"app.components.Official\": \"Oficiální\",\n    \"app.components.Onboarding.label.completed\": \"% dokončeno\",\n    \"app.components.Onboarding.title\": \"Videa Jak začít\",\n    \"app.components.PluginCard.Button.label.download\": \"Stáhnout\",\n    \"app.components.PluginCard.Button.label.install\": \"Již instalováno\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed\": \"Funkce automatického obnovení musí být vypnuta. Spusťte aplikaci příkazem `yarn develop`.\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.confirm\": \"Rozumím!\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.environment\": \"Z bezpečnostních důvodů lze zásuvný modul stáhnout pouze ve vývojářském prostředí.\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.title\": \"Stahování není možné\",\n    \"app.components.PluginCard.compatible\": \"Kompatibilní s vaší aplikací\",\n    \"app.components.PluginCard.compatibleCommunity\": \"Kompatibilní s komunitou\",\n    \"app.components.PluginCard.more-details\": \"Více podrobností\",\n    \"app.components.listPlugins.button\": \"Přidat nový zásuvný modul\",\n    \"app.components.listPlugins.title.none\": \"Nejsou instalovány žádné zásuvné moduly\",\n    \"app.components.listPluginsPage.deletePlugin.error\": \"Došlo k chybě při odinstalaci zásuvného modulu\",\n    \"app.containers.App.notification.error.init\": \"Při provádění požadavku na API došlo k chybě\",\n    \"app.links.configure-view\": \"Upravit vzhled\",\n    \"app.utils.SelectOption.defaultMessage\": \" \",\n    \"app.utils.defaultMessage\": \" \",\n    \"app.utils.placeholder.defaultMessage\": \" \",\n    \"component.Input.error.validation.integer\": \"Tato hodnota musí být celé číslo\",\n    \"components.AutoReloadBlocker.description\": \"Spusťte Strapi jedním z následujících příkazů:\",\n    \"components.AutoReloadBlocker.header\": \"Pro tento zásuvný modul musí být zapnuta funkce znovu načítání.\",\n    \"components.ErrorBoundary.title\": \"Něco se pokazilo...\",\n    \"components.Input.error.attribute.key.taken\": \"Tato hodnota již existuje\",\n    \"components.Input.error.attribute.sameKeyAndName\": \"Hodnoty nesmí být stejné\",\n    \"components.Input.error.attribute.taken\": \"Název tohoto pole již existuje\",\n    \"components.Input.error.contentTypeName.taken\": \"Tento název již existuje\",\n    \"components.Input.error.custom-error\": \"{errorMessage} \",\n    \"components.Input.error.password.noMatch\": \"Hesla se neshodují\",\n    \"components.Input.error.validation.email\": \"Toto není e-mailová adresa\",\n    \"components.Input.error.validation.json\": \"Toto se neshoduje s formátem JSON\",\n    \"components.Input.error.validation.max\": \"Hodnota je příliš vysoká {max}.\",\n    \"components.Input.error.validation.maxLength\": \"Hodnota je příliš dlouhá {max}.\",\n    \"components.Input.error.validation.min\": \"Hodnota je příliš nízká {min}.\",\n    \"components.Input.error.validation.minLength\": \"Hodnota je příliš krátká {min}.\",\n    \"components.Input.error.validation.minSupMax\": \"Nemůže být nadřazený\",\n    \"components.Input.error.validation.regex\": \"Hodnota neodpovídá požadovanému vzoru.\",\n    \"components.Input.error.validation.required\": \"Tato hodnota je povinná.\",\n    \"components.Input.error.validation.unique\": \"Tato hodnota již byla použita.\",\n    \"components.InputSelect.option.placeholder\": \"Vyberte zde\",\n    \"components.ListRow.empty\": \"Žádná data k zobrazení.\",\n    \"components.OverlayBlocker.description\": \"Používáte funkcionalitu, která potřebuje restartovat server. Počkejte, až se server opět spustí.\",\n    \"components.OverlayBlocker.description.serverError\": \"Server by měl být restartován, zkontrolujte prosím protokoly v terminálu.\",\n    \"components.OverlayBlocker.title\": \"Čekání na restartování...\",\n    \"components.OverlayBlocker.title.serverError\": \"Restartování trvá déle, než se očekávalo\",\n    \"components.PageFooter.select\": \"položek na stránku\",\n    \"components.ProductionBlocker.description\": \"Z bezpečnostních důvodů musíme tento zásuvný modul v jiných prostředích zakázat.\",\n    \"components.ProductionBlocker.header\": \"Tento zásuvný modul je dostupný pouze ve vývoji.\",\n    \"components.Wysiwyg.collapse\": \"Sbalit\",\n    \"components.Wysiwyg.selectOptions.H1\": \"Nadpis H1\",\n    \"components.Wysiwyg.selectOptions.H2\": \"Nadpis H2\",\n    \"components.Wysiwyg.selectOptions.H3\": \"Nadpis H3\",\n    \"components.Wysiwyg.selectOptions.H4\": \"Nadpis H4\",\n    \"components.Wysiwyg.selectOptions.H5\": \"Nadpis H5\",\n    \"components.Wysiwyg.selectOptions.H6\": \"Nadpis H6\",\n    \"components.Wysiwyg.selectOptions.title\": \"Přidat název\",\n    \"components.WysiwygBottomControls.charactersIndicators\": \"znaky\",\n    \"components.WysiwygBottomControls.fullscreen\": \"Rozbalit\",\n    \"components.WysiwygBottomControls.uploadFiles\": \"Přetáhněte soubory, vložte ze schránky nebo {browse}.\",\n    \"components.WysiwygBottomControls.uploadFiles.browse\": \"vyberte je\",\n    \"components.popUpWarning.message\": \"Opravdu to chcete smazat?\",\n    \"components.popUpWarning.title\": \"Prosím potvrďte\",\n    \"form.button.done\": \"OK\",\n    \"notification.error\": \"Došlo k chybě\",\n    \"notification.error.layout\": \"Nelze načíst rozložení\",\n    \"notification.form.error.fields\": \"Formulář obsahuje chyby\",\n    \"request.error.model.unknown\": \"Tento model neexistuje\"\n};\n\nexport { Analytics, Documentation, Email, Password, Provider, ResetPasswordToken, Role, Username, Users, cs as default };\n//# sourceMappingURL=cs.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,YAAY;AAChB,IAAI,gBAAgB;AACpB,IAAI,QAAQ;AACZ,IAAI,WAAW;AACf,IAAI,WAAW;AACf,IAAI,qBAAqB;AACzB,IAAI,OAAO;AACX,IAAI,WAAW;AACf,IAAI,QAAQ;AACZ,IAAI,KAAK;AAAA,EACL;AAAA,EACA,oCAAoC;AAAA,EACpC,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,yCAAyC;AAAA,EACzC,iDAAiD;AAAA,EACjD,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,mBAAmB;AAAA,EACnB;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA,EAChB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,6BAA6B;AAAA,EAC7B,qCAAqC;AAAA,EACrC,aAAa;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,iCAAiC;AAAA,EACjC,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,sCAAsC;AAAA,EACtC,2CAA2C;AAAA,EAC3C,sDAAsD;AAAA,EACtD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,gDAAgD;AAAA,EAChD,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,oCAAoC;AAAA,EACpC,2CAA2C;AAAA,EAC3C,2BAA2B;AAAA,EAC3B,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,+EAA+E;AAAA,EAC/E,qEAAqE;AAAA,EACrE,yEAAyE;AAAA,EACzE,mEAAmE;AAAA,EACnE,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,0CAA0C;AAAA,EAC1C,qCAAqC;AAAA,EACrC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,4BAA4B;AAAA,EAC5B,yCAAyC;AAAA,EACzC,4BAA4B;AAAA,EAC5B,wCAAwC;AAAA,EACxC,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,0CAA0C;AAAA,EAC1C,gDAAgD;AAAA,EAChD,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,4BAA4B;AAAA,EAC5B,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,+BAA+B;AAAA,EAC/B,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,yDAAyD;AAAA,EACzD,+CAA+C;AAAA,EAC/C,gDAAgD;AAAA,EAChD,uDAAuD;AAAA,EACvD,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,+BAA+B;AACnC;", "names": []}