{"version": 3, "sources": ["../../../@strapi/plugin-users-permissions/dist/admin/translations/de.json.mjs"], "sourcesContent": ["var de = {\n    \"BoundRoute.title\": \"Pfad gebunden an\",\n    \"EditForm.inputSelect.description.role\": \"<PERSON> Rolle, die neu authentifizierten Benutzern automatisch zugewiesen wird.\",\n    \"EditForm.inputSelect.label.role\": \"Standardrolle für authentifizierte Benutzer\",\n    \"EditForm.inputToggle.description.email\": \"Verbiete das Anlegen verschiedener Accounts mit der gleichen E-Mail-Adresse bei unterschiedlichen Anmeldemethoden.\",\n    \"EditForm.inputToggle.description.email-confirmation\": \"Wenn aktiviert (ON) erhalten neu registrierte Benutzer eine Bestätigungs-E-Mail.\",\n    \"EditForm.inputToggle.description.email-confirmation-redirection\": \"Nachdem Sie die E-Mail bestätigt haben, wähle wohin sie weitergeleitet wird.\",\n    \"EditForm.inputToggle.description.email-reset-password\": \"URL deiner Passwort-Zurücksetzen-Seite deiner Anwendung\",\n    \"EditForm.inputToggle.description.sign-up\": \"<PERSON><PERSON> (OFF), wird der Registrationsprozess unterbunden. Niemand kann sich mehr registrieren.\",\n    \"EditForm.inputToggle.label.email\": \"Ein Account pro E-Mail-Adresse\",\n    \"EditForm.inputToggle.label.email-confirmation\": \"Aktiviere E-Mail Benachrichtigungen\",\n    \"EditForm.inputToggle.label.email-confirmation-redirection\": \"Weiterleitungs-URL\",\n    \"EditForm.inputToggle.label.email-reset-password\": \"Passwort-Zurücksetzen-Seite\",\n    \"EditForm.inputToggle.label.sign-up\": \"Registration ermöglichen\",\n    \"Email.template.email_confirmation\": \"Bestätigung der E-Mail-Addresse\",\n    \"HeaderNav.link.advancedSettings\": \"Erweiterte Einstellungen\",\n    \"HeaderNav.link.emailTemplates\": \"E-Mail-Templates\",\n    \"HeaderNav.link.providers\": \"Methoden\",\n    \"Plugin.permissions.plugins.description\": \"Definiere die möglichen Aktionen des {name} Plugins.\",\n    \"Plugins.header.description\": \"Nur Aktionen, die an einen Pfad gebunden sind, werden hier gelistet.\",\n    \"Plugins.header.title\": \"Berechtigungen\",\n    \"Policies.header.hint\": \"Wähle eine Aktion aus und klicke auf das Zahnrad, um den an diese Aktion gebundenen Pfad anzuzeigen\",\n    \"Policies.header.title\": \"Fortgeschrittene Einstellungen\",\n    \"PopUpForm.Email.email_templates.inputDescription\": \"{link} für mehr Informationen\",\n    \"PopUpForm.Email.link.documentation\": \"Lies die Dokumentation\",\n    \"PopUpForm.Email.options.from.email.label\": \"E-Mail-Adresse des Absenders\",\n    \"PopUpForm.Email.options.from.email.placeholder\": \"<EMAIL>\",\n    \"PopUpForm.Email.options.from.name.label\": \"Name des Absenders\",\n    \"PopUpForm.Email.options.from.name.placeholder\": \"Kai Doe\",\n    \"PopUpForm.Email.options.message.label\": \"Nachricht\",\n    \"PopUpForm.Email.options.object.label\": \"Betreff\",\n    \"PopUpForm.Email.options.object.placeholder\": \"Bitte bestätige deine E-Mail-Adresse für %APP_NAME%\",\n    \"PopUpForm.Email.options.response_email.label\": \"Antwort E-Mail-Adresse\",\n    \"PopUpForm.Email.options.response_email.placeholder\": \"<EMAIL>\",\n    \"PopUpForm.Providers.enabled.description\": \"Wenn deaktiviert, kann diese Methode nicht verwendet werden.\",\n    \"PopUpForm.Providers.enabled.label\": \"Aktivieren\",\n    \"PopUpForm.Providers.key.label\": \"Client ID\",\n    \"PopUpForm.Providers.key.placeholder\": \"TEXT\",\n    \"PopUpForm.Providers.redirectURL.front-end.label\": \"Die URL zur Weiterleitung zu deiner Frontend-App\",\n    \"PopUpForm.Providers.redirectURL.label\": \"Die Weiterleitungs-URL für die App-Einstellungen von {provider}\",\n    \"PopUpForm.Providers.secret.label\": \"Client Secret\",\n    \"PopUpForm.Providers.secret.placeholder\": \"TEXT\",\n    \"PopUpForm.Providers.subdomain.label\": \"Host URI (Subdomain)\",\n    \"PopUpForm.Providers.subdomain.placeholder\": \"my.subdomain.com\",\n    \"PopUpForm.header.edit.email-templates\": \"E-Mail-Templates bearbeiten\",\n    \"PopUpForm.header.edit.providers\": \"Anbieter bearbeiten\",\n    \"Settings.roles.deleted\": \"Rolle gelöscht\",\n    \"Settings.roles.edited\": \"Rolle bearbeitet\",\n    \"Settings.section-label\": \"Nutzer- & Berechtigungen-Plugin\",\n    \"notification.success.submit\": \"Einstellungen aktualisiert\",\n    \"plugin.description.long\": \"Beschütze deine API mit einem vollständigen Authentifikationsprozess basierend auf JWT. Zudem bietet dieses Plugin eine ACL-Strategie, die erlaubt, die Berechtigungen für Benutzergruppen festzulegen.\",\n    \"plugin.description.short\": \"Beschütze deine API mit einem vollständigen Authentifikationsprozess basierend auf JWT.\",\n    \"plugin.name\": \"Nutzer- & Berechtigungen-Plugin\",\n    \"popUpWarning.button.cancel\": \"Abbrechen\",\n    \"popUpWarning.button.confirm\": \"Bestätigen\",\n    \"popUpWarning.title\": \"Bitte bestätigen\",\n    \"popUpWarning.warning.cancel\": \"Willst du wirklich alle deine Änderungen verwerfen?\"\n};\n\nexport { de as default };\n//# sourceMappingURL=de.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,KAAK;AAAA,EACL,oBAAoB;AAAA,EACpB,yCAAyC;AAAA,EACzC,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,uDAAuD;AAAA,EACvD,mEAAmE;AAAA,EACnE,yDAAyD;AAAA,EACzD,4CAA4C;AAAA,EAC5C,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,6DAA6D;AAAA,EAC7D,mDAAmD;AAAA,EACnD,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,0CAA0C;AAAA,EAC1C,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,oDAAoD;AAAA,EACpD,sCAAsC;AAAA,EACtC,4CAA4C;AAAA,EAC5C,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,iDAAiD;AAAA,EACjD,yCAAyC;AAAA,EACzC,wCAAwC;AAAA,EACxC,8CAA8C;AAAA,EAC9C,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,iCAAiC;AAAA,EACjC,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,0CAA0C;AAAA,EAC1C,uCAAuC;AAAA,EACvC,6CAA6C;AAAA,EAC7C,yCAAyC;AAAA,EACzC,mCAAmC;AAAA,EACnC,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,eAAe;AAAA,EACf,8BAA8B;AAAA,EAC9B,+BAA+B;AAAA,EAC/B,sBAAsB;AAAA,EACtB,+BAA+B;AACnC;", "names": []}