{"version": 3, "sources": ["../../../@strapi/admin/dist/admin/admin/src/translations/de.json.mjs"], "sourcesContent": ["var Analytics = \"Analytics\";\nvar Documentation = \"Dokumentation\";\nvar Email = \"E-Mail\";\nvar Password = \"Passwort\";\nvar Provider = \"Methode\";\nvar ResetPasswordToken = \"Passwort-Token zurücksetzen\";\nvar Role = \"Rolle\";\nvar Username = \"Benutzername\";\nvar Users = \"Benutzer\";\nvar anErrorOccurred = \"Ups! Ein unbekannter Fehler ist aufgetreten. Versuche es erneut.\";\nvar clearLabel = \"Zurücksetzen\";\nvar or = \"ODER\";\nvar skipToContent = \"Zu Inhalt springen\";\nvar submit = \"Senden\";\nvar de = {\n    Analytics: Analytics,\n    \"Auth.components.Oops.text\": \"Dein Account wurde deaktiviert\",\n    \"Auth.components.Oops.text.admin\": \"Falls das ein Fehler war, kontaktiere bitte deinen Administrator.\",\n    \"Auth.components.Oops.title\": \"Ups...\",\n    \"Auth.form.button.forgot-password\": \"E-Mail versenden\",\n    \"Auth.form.button.go-home\": \"ZURÜCK ZUR STARTSEITE\",\n    \"Auth.form.button.login\": \"Login\",\n    \"Auth.form.button.login.providers.error\": \"Durch den ausgewählten Anbieter können wir dich nicht verbinden\",\n    \"Auth.form.button.login.strapi\": \"Per Strapi einloggen\",\n    \"Auth.form.button.password-recovery\": \"Passwort zurücksetzen\",\n    \"Auth.form.button.register\": \"Los geht's\",\n    \"Auth.form.confirmPassword.label\": \"Passwort bestätigen\",\n    \"Auth.form.currentPassword.label\": \"Aktuelles Passwort\",\n    \"Auth.form.email.label\": \"E-Mail\",\n    \"Auth.form.email.placeholder\": \"z.B. <EMAIL>\",\n    \"Auth.form.error.blocked\": \"Dein Account wurde vom Administrator blockiert.\",\n    \"Auth.form.error.code.provide\": \"Ungültiger Code.\",\n    \"Auth.form.error.confirmed\": \"Deine Account E-Mail-Adresse ist nicht bestätigt.\",\n    \"Auth.form.error.email.invalid\": \"Diese E-Mail-Adresse ist ungültig.\",\n    \"Auth.form.error.email.provide\": \"Bitte nenne uns deinen Benutzernamen oder deine E-Mail-Adresse.\",\n    \"Auth.form.error.email.taken\": \"Diese E-Mail-Adresse wird bereits genutzt\",\n    \"Auth.form.error.invalid\": \"Ungültige Login-Daten.\",\n    \"Auth.form.error.params.provide\": \"Ungültige Parameter.\",\n    \"Auth.form.error.password.format\": \"Dein Passwort darf nicht mehr als dreimal das Symbol `$` enthalten.\",\n    \"Auth.form.error.password.local\": \"Dieser Benutzer hat kein lokales Passwort. Bitte logge dich mithilfe des Providers ein, der bei der Erstellung des Accounts genutzt wurde.\",\n    \"Auth.form.error.password.matching\": \"Passwörter sind nicht gleich.\",\n    \"Auth.form.error.password.provide\": \"Bitte gib dein Passwort ein.\",\n    \"Auth.form.error.ratelimit\": \"Zu viele Versuche, bitte versuche es in einer Minute erneut.\",\n    \"Auth.form.error.user.not-exist\": \"Diese E-Mail-Adresse ist nicht registriert.\",\n    \"Auth.form.error.username.taken\": \"Dieser Benutzername ist bereits vergeben\",\n    \"Auth.form.firstname.label\": \"Vorname\",\n    \"Auth.form.firstname.placeholder\": \"z.B. Max\",\n    \"Auth.form.forgot-password.email.label\": \"Gib deine E-Mail ein\",\n    \"Auth.form.forgot-password.email.label.success\": \"Eine E-Mail wurde erfolgreich verschickt an\",\n    \"Auth.form.lastname.label\": \"Nachname\",\n    \"Auth.form.lastname.placeholder\": \"z.B. Mustermann\",\n    \"Auth.form.password.hide-password\": \"Passwort ausblenden\",\n    \"Auth.form.password.hint\": \"Das Passwort muss mindestens 8 Zeichen, einen Großbuchstaben, einen Kleinbuchstaben und eine Zahl enthalten\",\n    \"Auth.form.password.show-password\": \"Passwort einblenden\",\n    \"Auth.form.register.news.label\": \"Halte mich über die neuen Features und anstehenden Verbesserungen auf dem Laufenden (damit werden die {terms} und die {policy} akzeptiert).\",\n    \"Auth.form.register.subtitle\": \"Deine Zugangsdaten werden nur verwendet, um dich im Admin Panel einzuloggen. Alle Daten werden in der lokalen Datenbank gespeichert.\",\n    \"Auth.form.rememberMe.label\": \"Eingeloggt bleiben\",\n    \"Auth.form.username.label\": \"Benutzername\",\n    \"Auth.form.username.placeholder\": \"z.B. Max_Mustermann\",\n    \"Auth.form.welcome.subtitle\": \"Logge dich in deinen Strapi Account ein\",\n    \"Auth.form.welcome.title\": \"Willkommen!\",\n    \"Auth.link.forgot-password\": \"Passwort vergessen?\",\n    \"Auth.link.ready\": \"Bereit für den Login?\",\n    \"Auth.link.signin\": \"Einloggen\",\n    \"Auth.link.signin.account\": \"Account bereits vorhanden?\",\n    \"Auth.login.sso.divider\": \"Oder einloggen mit\",\n    \"Auth.login.sso.loading\": \"Anbieter werden geladen...\",\n    \"Auth.login.sso.subtitle\": \"Per SSO einloggen\",\n    \"Auth.privacy-policy-agreement.policy\": \"Datenschutzerklärung\",\n    \"Auth.privacy-policy-agreement.terms\": \"Nutzungsbedingungen\",\n    \"Auth.reset-password.title\": \"Passwort zurücksetzen\",\n    \"Content Manager\": \"Inhalts-Manager\",\n    \"Content Type Builder\": \"Inhaltstyp-Editor\",\n    Documentation: Documentation,\n    Email: Email,\n    \"Files Upload\": \"Dateien hochladen\",\n    \"HomePage.head.title\": \"Startseite\",\n    \"HomePage.roadmap\": \"Siehe unsere Roadmap\",\n    \"HomePage.welcome.congrats\": \"Herzlichen Glückwunsch!\",\n    \"HomePage.welcome.congrats.content\": \"Angemeldet als erster Administrator. Um die leistungsstarken Funktionen von Strapi zu entdecken,\",\n    \"HomePage.welcome.congrats.content.bold\": \"empfehlen wir Ihren ersten Inhaltstyp zu erstellen.\",\n    \"Media Library\": \"Medienbibliothek\",\n    \"New entry\": \"Neuer Eintrag\",\n    Password: Password,\n    Provider: Provider,\n    ResetPasswordToken: ResetPasswordToken,\n    Role: Role,\n    \"Roles & Permissions\": \"Rollen & Berechtigungen\",\n    \"Roles.ListPage.notification.delete-all-not-allowed\": \"Manche Rollen konnten nicht gelöscht werden, da sie mit Benutzern verknüpft sind\",\n    \"Roles.ListPage.notification.delete-not-allowed\": \"Eine Rolle, die mit einem Benutzer verknüpft ist, kann nicht gelöscht werden\",\n    \"Roles.RoleRow.select-all\": \"Wähle {name} für Mehrfach-Aktionen\",\n    \"Roles.RoleRow.user-count\": \"Benutzer\",\n    \"Roles.components.List.empty.withSearch\": \"Es gibt keine Rolle die der Suche ({search}) entspricht...\",\n    \"Settings.PageTitle\": \"Einstellungen - {name}\",\n    \"Settings.apiTokens.addFirstToken\": \"Deinen ersten API-Token hinzufügen\",\n    \"Settings.apiTokens.addNewToken\": \"Neuen API-Token hinzufügen\",\n    \"Settings.tokens.copy.editMessage\": \"Aus Sicherheitsgründen kannst du deinen Token nur einmal sehen.\",\n    \"Settings.tokens.copy.editTitle\": \"Auf diesen Token kann nicht mehr zugegriffen werden.\",\n    \"Settings.tokens.copy.lastWarning\": \"Stell sicher, dass dieser Token kopiert wurde, da du ihn nicht noch einmal sehen können wirst!\",\n    \"Settings.apiTokens.create\": \"Neuen API-Token erstellen\",\n    \"Settings.apiTokens.description\": \"Liste der generierten Tokens mit Zugriff auf die API\",\n    \"Settings.apiTokens.emptyStateLayout\": \"Du hast noch keinen Inhalt...\",\n    \"Settings.tokens.notification.copied\": \"Token wurde in die Zwischenablage kopiert.\",\n    \"Settings.apiTokens.title\": \"API-Tokens\",\n    \"Settings.tokens.types.full-access\": \"Voller Zugriff\",\n    \"Settings.tokens.types.read-only\": \"Nur Lesezugriff\",\n    \"Settings.application.description\": \"Globale Informationen über die Administrationsoberfläche\",\n    \"Settings.application.edition-title\": \"Aktuelle Version\",\n    \"Settings.application.get-help\": \"Hilfe\",\n    \"Settings.application.link-pricing\": \"Alle Preisgestaltungen anzeigen\",\n    \"Settings.application.link-upgrade\": \"Deine Administrationsoberfläche aktualisieren\",\n    \"Settings.application.node-version\": \"node version\",\n    \"Settings.application.strapi-version\": \"strapi version\",\n    \"Settings.application.strapiVersion\": \"strapi version\",\n    \"Settings.application.title\": \"Übersicht\",\n    \"Settings.error\": \"Fehler\",\n    \"Settings.global\": \"Globale Einstellungen\",\n    \"Settings.permissions\": \"Administrationsoberfläche\",\n    \"Settings.permissions.category\": \"Berechtigungseinstellungen für die {category}\",\n    \"Settings.permissions.category.plugins\": \"Berechtigungseinstellungen für das {category} Plugin\",\n    \"Settings.permissions.conditions.anytime\": \"Jederzeit\",\n    \"Settings.permissions.conditions.apply\": \"Anwenden\",\n    \"Settings.permissions.conditions.can\": \"Kann\",\n    \"Settings.permissions.conditions.conditions\": \"Bedingungen definieren\",\n    \"Settings.permissions.conditions.links\": \"Links\",\n    \"Settings.permissions.conditions.no-actions\": \"Keine Aktionen\",\n    \"Settings.permissions.conditions.none-selected\": \"Jederzeit\",\n    \"Settings.permissions.conditions.or\": \"ODER\",\n    \"Settings.permissions.conditions.when\": \"Wenn\",\n    \"Settings.permissions.select-all-by-permission\": \"Wähle alle {label}-Berechtigungen\",\n    \"Settings.permissions.select-by-permission\": \"Wähle {label}-Berechtigung\",\n    \"Settings.permissions.users.create\": \"Benutzer einladen\",\n    \"Settings.permissions.users.email\": \"E-Mail\",\n    \"Settings.permissions.users.firstname\": \"Vorname\",\n    \"Settings.permissions.users.lastname\": \"Nachname\",\n    \"Settings.permissions.users.form.sso\": \"Mit SSO einloggen\",\n    \"Settings.permissions.users.form.sso.description\": \"Nutzer können sich per SSO einloggen wenn aktiviert (ON)\",\n    \"Settings.permissions.users.listview.header.subtitle\": \"Alle Nutzer mit Zugriff auf diese Administrationsoberfläche\",\n    \"Settings.permissions.users.tabs.label\": \"Tab Berechtigungen\",\n    \"Settings.profile.form.notify.data.loaded\": \"Deine Profildaten wurden geladen\",\n    \"Settings.profile.form.section.experience.clear.select\": \"Ausgewählte Sprache der Oberfläche zurücksetzen\",\n    \"Settings.profile.form.section.experience.here\": \"hier\",\n    \"Settings.profile.form.section.experience.interfaceLanguage\": \"Sprache der Oberfläche\",\n    \"Settings.profile.form.section.experience.interfaceLanguage.hint\": \"Dies wird die Oberfläche für dich in der ausgewählten Sprache darstellen.\",\n    \"Settings.profile.form.section.experience.interfaceLanguageHelp\": \"Diese Einstellungen werden nur für dich angewandt. Mehr Informationen dazu {here}.\",\n    \"Settings.profile.form.section.experience.mode.label\": \"Modus der Oberfläche\",\n    \"Settings.profile.form.section.experience.mode.hint\": \"Zeigt die Oberfläche im gewählten Modus.\",\n    \"Settings.profile.form.section.experience.mode.option-label\": \"{name}-Modus\",\n    \"Settings.profile.form.section.experience.title\": \"Bedienung\",\n    \"Settings.profile.form.section.head.title\": \"Nutzerprofil\",\n    \"Settings.profile.form.section.profile.page.title\": \"Profil-Seite\",\n    \"Settings.roles.create.description\": \"Die Berechtigungen einer Rolle festlegen\",\n    \"Settings.roles.create.title\": \"Rolle erstellen\",\n    \"Settings.roles.created\": \"Rolle erstellt\",\n    \"Settings.roles.edit.title\": \"Rolle bearbeiten\",\n    \"Settings.roles.form.button.users-with-role\": \"Benutzer mit dieser Rolle\",\n    \"Settings.roles.form.created\": \"Erstellt\",\n    \"Settings.roles.form.description\": \"Name und Beschreibung der Rolle\",\n    \"Settings.roles.form.permission.property-label\": \"{label} Berechtigungen\",\n    \"Settings.roles.form.permissions.attributesPermissions\": \"Felderberechtigungen\",\n    \"Settings.roles.form.permissions.create\": \"Erstellen\",\n    \"Settings.roles.form.permissions.delete\": \"Löschen\",\n    \"Settings.roles.form.permissions.publish\": \"Veröffentlichen\",\n    \"Settings.roles.form.permissions.read\": \"Lesen\",\n    \"Settings.roles.form.permissions.update\": \"Ändern\",\n    \"Settings.roles.list.button.add\": \"Neue Rolle hinzufügen\",\n    \"Settings.roles.list.description\": \"Liste der Rollen\",\n    \"Settings.roles.title.singular\": \"Rolle\",\n    \"Settings.sso.description\": \"Einstellungen des Single Sign-On Features konfigurieren.\",\n    \"Settings.sso.form.defaultRole.description\": \"Die Standard-Rolle wird dem neu erstellten Nutzer zugewiesen\",\n    \"Settings.sso.form.defaultRole.description-not-allowed\": \"Du hast keine Lese-Berechtigung für die Admin-Rollen\",\n    \"Settings.sso.form.defaultRole.label\": \"Standard-Rolle\",\n    \"Settings.sso.form.registration.description\": \"Neuen Nutzer beim Einloggen über SSO erstellen wenn noch kein Account existiert\",\n    \"Settings.sso.form.registration.label\": \"Auto-Registrierung\",\n    \"Settings.sso.title\": \"Single Sign-On\",\n    \"Settings.webhooks.create\": \"Webhook erstellen\",\n    \"Settings.webhooks.create.header\": \"Neuen Header erstellen\",\n    \"Settings.webhooks.created\": \"Webhook erstellt\",\n    \"Settings.webhooks.event.publish-tooltip\": \"Entwurf/Veröffentlichen muss für dieses Event aktiviert sein\",\n    \"Settings.webhooks.events.create\": \"Erstellen\",\n    \"Settings.webhooks.events.update\": \"Aktualisieren\",\n    \"Settings.webhooks.form.events\": \"Events\",\n    \"Settings.webhooks.form.headers\": \"Header\",\n    \"Settings.webhooks.form.url\": \"Url\",\n    \"Settings.webhooks.headers.remove\": \"Header {number} entfernen\",\n    \"Settings.webhooks.key\": \"Key\",\n    \"Settings.webhooks.list.button.add\": \"Neuen Webhook hinzufügen\",\n    \"Settings.webhooks.list.description\": \"POST-Benachrichtigungen bei Änderungen empfangen.\",\n    \"Settings.webhooks.list.empty.description\": \"Ersten dieser Liste hinzufügen.\",\n    \"Settings.webhooks.list.empty.link\": \"Siehe unsere Dokumentation\",\n    \"Settings.webhooks.list.empty.title\": \"Noch keine Webhooks\",\n    \"Settings.webhooks.list.th.actions\": \"Aktionen\",\n    \"Settings.webhooks.list.th.status\": \"Status\",\n    \"Settings.webhooks.singular\": \"Webhook\",\n    \"Settings.webhooks.title\": \"Webhooks\",\n    \"Settings.webhooks.to.delete\": \"{webhooksToDeleteLength, plural, one {# Datei} other {# Dateien}} ausgewählt\",\n    \"Settings.webhooks.trigger\": \"Trigger\",\n    \"Settings.webhooks.trigger.cancel\": \"Trigger abbrechen\",\n    \"Settings.webhooks.trigger.pending\": \"Ausstehend ...\",\n    \"Settings.webhooks.trigger.save\": \"Zur Ausführung speichern\",\n    \"Settings.webhooks.trigger.success\": \"Erfolg!\",\n    \"Settings.webhooks.trigger.success.label\": \"Trigger war erfolgreich\",\n    \"Settings.webhooks.trigger.test\": \"Test-Trigger\",\n    \"Settings.webhooks.trigger.title\": \"Speichere vor Trigger\",\n    \"Settings.webhooks.value\": \"Wert\",\n    \"Usecase.back-end\": \"Backend-Entwickler\",\n    \"Usecase.button.skip\": \"Diese Frage überspringen\",\n    \"Usecase.content-creator\": \"Content Creator\",\n    \"Usecase.front-end\": \"Frontend-Entwickler\",\n    \"Usecase.full-stack\": \"Full-stack-Entwickler\",\n    \"Usecase.input.work-type\": \"Welche Rolle beschreibt deine Position am besten?\",\n    \"Usecase.notification.success.project-created\": \"Projekt wurde erfolgreich erstellt\",\n    \"Usecase.other\": \"Sonstige\",\n    \"Usecase.title\": \"Erzähle uns etwas mehr von dir\",\n    Username: Username,\n    Users: Users,\n    \"Users & Permissions\": \"Benutzer & Berechtigungen\",\n    \"Users.components.List.empty\": \"Noch keine Nutzer…\",\n    \"Users.components.List.empty.withFilters\": \"Es gibt keine Nutzer die den Filtern entsprechen...\",\n    \"Users.components.List.empty.withSearch\": \"Es gibt keine Nutzer die der Suche ({search}) entsprechen...\",\n    \"admin.pages.MarketPlacePage.head\": \"Marketplace - Plugins\",\n    \"admin.pages.MarketPlacePage.plugin.copy\": \"Installations-Befehl kopieren\",\n    \"admin.pages.MarketPlacePage.plugin.copy.success\": \"Installations-Befehl ist bereit, in deinem Terminal eingefügt zu werden\",\n    \"admin.pages.MarketPlacePage.plugin.info\": \"Mehr erfahren\",\n    \"admin.pages.MarketPlacePage.plugin.info.label\": \"Mehr über {pluginName} erfahren\",\n    \"admin.pages.MarketPlacePage.plugin.info.text\": \"Mehr erfahren\",\n    \"admin.pages.MarketPlacePage.plugin.installed\": \"Installiert\",\n    \"admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi\": \"Erstellt von Strapi\",\n    \"admin.pages.MarketPlacePage.plugin.tooltip.verified\": \"Plugin verifiziert von Strapi\",\n    \"admin.pages.MarketPlacePage.search.clear\": \"Plugin-Suche zurücksetzen\",\n    \"admin.pages.MarketPlacePage.search.empty\": \"Keine Ergebnisse für \\\"{target}\\\"\",\n    \"admin.pages.MarketPlacePage.search.placeholder\": \"Suche nach einem Plugin\",\n    \"admin.pages.MarketPlacePage.submit.plugin.link\": \"Eigenes Plugin einreichen\",\n    \"admin.pages.MarketPlacePage.subtitle\": \"Mache mehr mit Strapi\",\n    anErrorOccurred: anErrorOccurred,\n    \"app.component.CopyToClipboard.label\": \"In Zwischenablage kopieren\",\n    \"app.component.search.label\": \"Suche nach {target}\",\n    \"app.component.table.duplicate\": \"Dupliziere {target}\",\n    \"app.component.table.edit\": \"Bearbeite {target}\",\n    \"app.component.table.select.one-entry\": \"Selektiere {target}\",\n    \"app.components.BlockLink.blog\": \"Blog\",\n    \"app.components.BlockLink.blog.content\": \"Lies die neuesten Nachrichten über Strapi und das Ökosystem.\",\n    \"app.components.BlockLink.code\": \"Code-Beispiele\",\n    \"app.components.BlockLink.code.content\": \"Lerne duch das Testen von echten Projekten, die von der Community entwickelt wurden.\",\n    \"app.components.BlockLink.documentation.content\": \"Entdecke grundlegende Konzepte, Anleitungen und Anweisungen.\",\n    \"app.components.BlockLink.tutorial\": \"Tutorials\",\n    \"app.components.BlockLink.tutorial.content\": \"Folge Schritt-für-Schritt Anleitungen die zeigen, wie du Strapi nutzen und anpassen kannst.\",\n    \"app.components.Button.cancel\": \"Abbrechen\",\n    \"app.components.Button.confirm\": \"Bestätigen\",\n    \"app.components.Button.reset\": \"Zurücksetzen\",\n    \"app.components.ComingSoonPage.comingSoon\": \"Bald verfügbar\",\n    \"app.components.ConfirmDialog.title\": \"Bestätigen\",\n    \"app.components.DownloadInfo.download\": \"Download wird ausgeführt...\",\n    \"app.components.DownloadInfo.text\": \"Dies könnte kurz dauern. Danke für deine Geduld.\",\n    \"app.components.EmptyAttributes.title\": \"Bisher gibt es noch keine Felder\",\n    \"app.components.EmptyStateLayout.content-document\": \"Kein Inhalt gefunden\",\n    \"app.components.EmptyStateLayout.content-permissions\": \"Du hast keine ausreichenden Berechtigungen, um auf diesen Inhalt zuzugreifen\",\n    \"app.components.GuidedTour.CM.create.content\": \"<p>Erstelle und verwalte all deinen Inhalt hier im Content Manager.</p><p>Beispiel: Im Beispiel der Blog-Website, kann man einen Artikel schreiben, speichern und veröffentlichen.</p><p>💡 Kleiner Tipp: Vergiss nicht, beim Inhalt den du erstellst, \\\"Veröffentlichen\\\" zu drücken.</p>\",\n    \"app.components.GuidedTour.CM.create.title\": \"⚡️ Erstelle Inhalt\",\n    \"app.components.GuidedTour.CM.success.content\": \"<p>Super, ein letzter Schritt noch!</p><b>🚀  Sehe deinen Inhalt in Aktion</b>\",\n    \"app.components.GuidedTour.CM.success.cta.title\": \"Teste die API\",\n    \"app.components.GuidedTour.CM.success.title\": \"Schritt 2: Abgeschlossen ✅\",\n    \"app.components.GuidedTour.CTB.create.content\": \"<p>Sammlungen helfen dir, mehrere Einträge zu managen, während Einzel-Einträge dazu da sind, nur einen Eintrag zu managen.</p> <p>Bsp: Für eine Blog-Website wären die Artikel eine Sammlung, während die Homepage ein Einzel-Eintrag wäre.</p>\",\n    \"app.components.GuidedTour.CTB.create.cta.title\": \"Einen Inhaltstyp bauen\",\n    \"app.components.GuidedTour.CTB.create.title\": \"🧠 Erstelle deinen ersten Inhaltstyp\",\n    \"app.components.GuidedTour.CTB.success.content\": \"<p>Sehr gut!</p><b>⚡️ Was willst du mit der Welt teilen?</b>\",\n    \"app.components.GuidedTour.CTB.success.title\": \"Schritt 1: Abgeschlossen ✅\",\n    \"app.components.GuidedTour.apiTokens.create.content\": \"<p>Generiere einen Authentifizierungs-Token hier und greife auf den Inhalt, den du gerade erstellt hast, zu.</p>\",\n    \"app.components.GuidedTour.apiTokens.create.cta.title\": \"Generiere einen API-Token\",\n    \"app.components.GuidedTour.apiTokens.create.title\": \"🚀 Sehe Inhalt in Aktion\",\n    \"app.components.GuidedTour.apiTokens.success.content\": \"<p>Sehe Inhalt in Aktion indem du einen HTTP-Request machst:</p><ul><li><p>Zu dieser URL: <light>https://'<'YOUR_DOMAIN'>'/api/'<'YOUR_CT'>'</light></p></li><li><p>Mit dem Header: <light>Authorization: bearer '<'YOUR_API_TOKEN'>'</light></p></li></ul><p>Mehr dazu, wie du mit deinem Inhalt interagierst, liest du in der <documentationLink>Dokumentation</documentationLink>.</p>\",\n    \"app.components.GuidedTour.apiTokens.success.cta.title\": \"Zurück zur Startseite\",\n    \"app.components.GuidedTour.apiTokens.success.title\": \"Schritt 3: Abgeschlossen ✅\",\n    \"app.components.GuidedTour.create-content\": \"Erstelle Inhalt\",\n    \"app.components.GuidedTour.home.CM.title\": \"⚡️ Was willst du mit der Welt teilen?\",\n    \"app.components.GuidedTour.home.CTB.cta.title\": \"Gehe zum Content type Builder\",\n    \"app.components.GuidedTour.home.CTB.title\": \"🧠 Baue die Struktur des Inhalts\",\n    \"app.components.GuidedTour.home.apiTokens.cta.title\": \"Teste die API\",\n    \"app.components.GuidedTour.skip\": \"Tour überspringen\",\n    \"app.components.GuidedTour.title\": \"3 Schritte zum Loslegen\",\n    \"app.components.HomePage.button.blog\": \"Mehr dazu im Blog\",\n    \"app.components.HomePage.community\": \"Finde die Community im Web\",\n    \"app.components.HomePage.community.content\": \"Diskutiere mit Teammitgliedern, Mitwirkenden und Entwicklern auf verschiedenen Kanälen.\",\n    \"app.components.HomePage.create\": \"Ersten Content-Type erstellen\",\n    \"app.components.HomePage.roadmap\": \"Zu unserer Roadmap\",\n    \"app.components.HomePage.welcome\": \"Willkommen an Bord 👋\",\n    \"app.components.HomePage.welcome.again\": \"Willkommen 👋\",\n    \"app.components.HomePage.welcomeBlock.content\": \"Wir freuen uns, dich als Mitglied der Community zu haben. Wir sind offen für Feedback, senden uns einfach eine direkt Nachricht in \",\n    \"app.components.HomePage.welcomeBlock.content.again\": \"Wir hoffen, dass du Fortschritte bei deinem Projekt machst ... Lese das Neueste über Strapi. Wir geben unser Bestes, um das Produkt auf der Grundlage deines Feedbacks zu verbessern.\",\n    \"app.components.HomePage.welcomeBlock.content.issues\": \"Ticket.\",\n    \"app.components.HomePage.welcomeBlock.content.raise\": \" oder eröffne \",\n    \"app.components.ImgPreview.hint\": \"Ziehe eine Datei hierher oder {browse} eine Datei zum hochladen aus\",\n    \"app.components.ImgPreview.hint.browse\": \"wähle\",\n    \"app.components.InputFile.newFile\": \"Neue Datei hinzufügen\",\n    \"app.components.InputFileDetails.open\": \"In einem neuen Tab öffnen\",\n    \"app.components.InputFileDetails.originalName\": \"Original Name:\",\n    \"app.components.InputFileDetails.remove\": \"Entferne diese Datei\",\n    \"app.components.InputFileDetails.size\": \"Größe:\",\n    \"app.components.InstallPluginPage.Download.description\": \"Es kann einige Sekunden dauern, bis das Plugin heruntergeladen und installiert ist.\",\n    \"app.components.InstallPluginPage.Download.title\": \"Herunterladen...\",\n    \"app.components.InstallPluginPage.description\": \"Erweitere deine App ganz einfach.\",\n    \"app.components.LeftMenu.collapse\": \"Navigationsleiste einklappen\",\n    \"app.components.LeftMenu.expand\": \"Navigationsleiste ausklappen\",\n    \"app.components.LeftMenu.logout\": \"Abmelden\",\n    \"app.components.LeftMenu.navbrand.title\": \"Strapi Dashboard\",\n    \"app.components.LeftMenu.navbrand.workplace\": \"Arbeitsplatz\",\n    \"app.components.LeftMenu.trialCountdown\": \"Dein Test läuft am {date} ab.\",\n    \"app.components.LeftMenuFooter.help\": \"Hilfe\",\n    \"app.components.LeftMenuFooter.poweredBy\": \"Powered by \",\n    \"app.components.LeftMenuLinkContainer.collectionTypes\": \"Sammlungen\",\n    \"app.components.LeftMenuLinkContainer.configuration\": \"Konfiguration\",\n    \"app.components.LeftMenuLinkContainer.general\": \"Allgemein\",\n    \"app.components.LeftMenuLinkContainer.noPluginsInstalled\": \"Noch keine Plugins installiert\",\n    \"app.components.LeftMenuLinkContainer.plugins\": \"Plugins\",\n    \"app.components.LeftMenuLinkContainer.singleTypes\": \"Einzel-Einträge\",\n    \"app.components.ListPluginsPage.deletePlugin.description\": \"Das Deinstallieren des Plugins kann einen Augenblick dauern.\",\n    \"app.components.ListPluginsPage.deletePlugin.title\": \"Deinstalliere\",\n    \"app.components.ListPluginsPage.description\": \"Liste aller im Projekt installierten Plugins.\",\n    \"app.components.ListPluginsPage.head.title\": \"Plugins anzeigen\",\n    \"app.components.Logout.logout\": \"Ausloggen\",\n    \"app.components.Logout.profile\": \"Profil\",\n    \"app.components.MarketplaceBanner\": \"Entdecke von der Community entwickelte Plugins und noch viel mehr Dinge, um deinem Projekt zu helfen, auf Strapi Awesome.\",\n    \"app.components.MarketplaceBanner.image.alt\": \"ein Strapi-Raketen-Logo\",\n    \"app.components.MarketplaceBanner.link\": \"Jetzt entdecken\",\n    \"app.components.NotFoundPage.back\": \"Zurück zur Homepage\",\n    \"app.components.NotFoundPage.description\": \"Nicht gefunden\",\n    \"app.components.Official\": \"Offiziell\",\n    \"app.components.Onboarding.help.button\": \"Hilfe-Button\",\n    \"app.components.Onboarding.label.completed\": \"% abgeschlossen\",\n    \"app.components.Onboarding.title\": \"Videos zum Einstieg\",\n    \"app.components.PluginCard.Button.label.download\": \"Download\",\n    \"app.components.PluginCard.Button.label.install\": \"Bereits installiert\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed\": \"Die AutoReload-Funktion muss nicht aktiviert sein. Bitte die App mit `yarn develop` starten.\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.confirm\": \"Ich verstehe, dass\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.environment\": \"Aus Sicherheitsgründen kann ein Plugin nur in einer Entwicklungsumgebung heruntergeladen werden.\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.title\": \"Das Herunterladen ist nicht möglich.\",\n    \"app.components.PluginCard.compatible\": \"Mit dieser App kompatibel\",\n    \"app.components.PluginCard.compatibleCommunity\": \"Mit der Community kompatibel\",\n    \"app.components.PluginCard.more-details\": \"Mehr Details\",\n    \"app.components.ToggleCheckbox.off-label\": \"Nein\",\n    \"app.components.ToggleCheckbox.on-label\": \"Ja\",\n    \"app.components.Users.MagicLink.connect\": \"Diesen Link dem Benutzer zum Registrieren schicken.\",\n    \"app.components.Users.MagicLink.connect.sso\": \"Sende dem Benutzer diesen Link, das erste Login kann über einen SSO-Anbeiter gemacht werden\",\n    \"app.components.Users.ModalCreateBody.block-title.details\": \"Details\",\n    \"app.components.Users.ModalCreateBody.block-title.roles\": \"Rolle des Benutzers\",\n    \"app.components.Users.ModalCreateBody.block-title.roles.description\": \"Dein Benutzer kann eine oder mehrere Rollen haben\",\n    \"app.components.Users.SortPicker.button-label\": \"Sortieren nach\",\n    \"app.components.Users.SortPicker.sortby.email_asc\": \"E-Mail (A nach Z)\",\n    \"app.components.Users.SortPicker.sortby.email_desc\": \"E-Mail (Z nach A)\",\n    \"app.components.Users.SortPicker.sortby.firstname_asc\": \"Vorname (A nach Z)\",\n    \"app.components.Users.SortPicker.sortby.firstname_desc\": \"Vorname (Z nach A)\",\n    \"app.components.Users.SortPicker.sortby.lastname_asc\": \"Nachname (A nach Z)\",\n    \"app.components.Users.SortPicker.sortby.lastname_desc\": \"Nachname (Z nach A)\",\n    \"app.components.Users.SortPicker.sortby.username_asc\": \"Benutzername (A nach Z)\",\n    \"app.components.Users.SortPicker.sortby.username_desc\": \"Benutzername (Z nach A)\",\n    \"app.components.listPlugins.button\": \"Neues Plugin hinzufügen\",\n    \"app.components.listPlugins.title.none\": \"Es ist kein Plugin installiert\",\n    \"app.components.listPluginsPage.deletePlugin.error\": \"Beim Entfernen des Plugins ist ein Fehler aufgetreten\",\n    \"app.containers.App.notification.error.init\": \"Beim Aufruf der API ist ein Fehler aufgetreten.\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin\": \"Bitte den Administrator kontaktieren, sollte der Link nicht ankommen.\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.text.email\": \"Es kann ein paar Minuten dauern, bis der Wiederherstellungslink ankommt.\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.title\": \"E-Mail versendet\",\n    \"app.containers.Users.EditPage.form.active.label\": \"Aktiv\",\n    \"app.containers.Users.EditPage.header.label\": \"Bearbeite {name}\",\n    \"app.containers.Users.EditPage.header.label-loading\": \"Bearbeite Nutzer\",\n    \"app.containers.Users.EditPage.roles-bloc-title\": \"Zugewiesene Rollen\",\n    \"app.containers.Users.ModalForm.footer.button-success\": \"Nutzer erstellen\",\n    \"app.links.configure-view\": \"Anzeige konfigurieren\",\n    \"app.page.not.found\": \"Oh nein! Wir konnten die Seite, nach der du suchst, nicht finden...\",\n    \"app.static.links.cheatsheet\": \"CheatSheet\",\n    \"app.utils.SelectOption.defaultMessage\": \" \",\n    \"app.utils.add-filter\": \"Filter hinzufügen\",\n    \"app.utils.close-label\": \"Schließen\",\n    \"app.utils.defaultMessage\": \" \",\n    \"app.utils.duplicate\": \"Duplizieren\",\n    \"app.utils.edit\": \"Bearbeiten\",\n    \"app.utils.errors.file-too-big.message\": \"Datei ist zu groß\",\n    \"app.utils.filter-value\": \"Filter-Wert\",\n    \"app.utils.filters\": \"Filter\",\n    \"app.utils.notify.data-loaded\": \"{target} wurde geladen\",\n    \"app.utils.placeholder.defaultMessage\": \" \",\n    \"app.utils.publish\": \"Veröffentlichen\",\n    \"app.utils.select-all\": \"Alles auswählen\",\n    \"app.utils.select-field\": \"Feld auswählen\",\n    \"app.utils.select-filter\": \"Filter auswählen\",\n    \"app.utils.unpublish\": \"Nicht veröffentlichen\",\n    clearLabel: clearLabel,\n    \"coming.soon\": \"Dieser Inhalt ist aktuell unter Bearbeitung und wird in ein paar Wochen zurück sein!\",\n    \"component.Input.error.validation.integer\": \"Der Wert muss eine Ganzzahl sein\",\n    \"components.AutoReloadBlocker.description\": \"Strapi mit einem der folgenden Befehle ausführen:\",\n    \"components.AutoReloadBlocker.header\": \"Dieses Plugin benötigt das Neuladen-Feature.\",\n    \"components.ErrorBoundary.title\": \"Etwas ist falsch gelaufen...\",\n    \"components.FilterOptions.FILTER_TYPES.$contains\": \"enthält\",\n    \"components.FilterOptions.FILTER_TYPES.$containsi\": \"enthält (Groß- und Kleinschreibung wird nicht beachtet)\",\n    \"components.FilterOptions.FILTER_TYPES.$endsWith\": \"endet mit\",\n    \"components.FilterOptions.FILTER_TYPES.$endsWithi\": \"endet mit (Groß- und Kleinschreibung wird nicht beachtet)\",\n    \"components.FilterOptions.FILTER_TYPES.$eq\": \"ist\",\n    \"components.FilterOptions.FILTER_TYPES.$eqi\": \"ist (Groß- und Kleinschreibung wird nicht beachtet)\",\n    \"components.FilterOptions.FILTER_TYPES.$gt\": \"ist größer als\",\n    \"components.FilterOptions.FILTER_TYPES.$gte\": \"is größer als oder gleich\",\n    \"components.FilterOptions.FILTER_TYPES.$lt\": \"is kleiner als\",\n    \"components.FilterOptions.FILTER_TYPES.$lte\": \"is kleiner als oder gleich\",\n    \"components.FilterOptions.FILTER_TYPES.$ne\": \"ist nicht\",\n    \"components.FilterOptions.FILTER_TYPES.$nei\": \"ist nicht (Groß- und Kleinschreibung wird nicht beachtet)\",\n    \"components.FilterOptions.FILTER_TYPES.$notContains\": \"enthält nicht\",\n    \"components.FilterOptions.FILTER_TYPES.$notContainsi\": \"enthält nicht (Groß- und Kleinschreibung wird nicht beachtet)\",\n    \"components.FilterOptions.FILTER_TYPES.$notNull\": \"ist nicht null\",\n    \"components.FilterOptions.FILTER_TYPES.$null\": \"ist null\",\n    \"components.FilterOptions.FILTER_TYPES.$startsWith\": \"startet mit\",\n    \"components.FilterOptions.FILTER_TYPES.$startsWithi\": \"startet mit (Groß- und Kleinschreibung wird nicht beachtet)\",\n    \"components.Input.error.attribute.key.taken\": \"Dieser Wert existiert bereits\",\n    \"components.Input.error.attribute.sameKeyAndName\": \"Darf nicht gleich sein\",\n    \"components.Input.error.attribute.taken\": \"Dieser Feldname ist bereits vergeben\",\n    \"components.Input.error.contain.lowercase\": \"Das Passwort muss mindestens einen Kleinbuchstaben enthalten\",\n    \"components.Input.error.contain.number\": \"Das Passwort muss mindestens eine Zahl enthalten\",\n    \"components.Input.error.contain.uppercase\": \"Das Passwort muss mindestens einen Großbuchstaben enthalten\",\n    \"components.Input.error.contentTypeName.taken\": \"Dieser Name existiert bereits\",\n    \"components.Input.error.custom-error\": \"{errorMessage} \",\n    \"components.Input.error.password.noMatch\": \"Passwörter stimmen nicht überein\",\n    \"components.Input.error.validation.email\": \"Das ist keine gültige E-Mail-Adresse\",\n    \"components.Input.error.validation.json\": \"Dies entspricht nicht dem JSON-Format.\",\n    \"components.Input.error.validation.lowercase\": \"Dieser Wert muss kleingeschreiben sein.\",\n    \"components.Input.error.validation.max\": \"Dieser Wert ist zu hoch {max}.\",\n    \"components.Input.error.validation.maxLength\": \"Dieser Wert ist zu lang {max}.\",\n    \"components.Input.error.validation.min\": \"Dieser Wert ist zu niedrig {min}.\",\n    \"components.Input.error.validation.minLength\": \"Dieser Wert ist zu kurz {min}.\",\n    \"components.Input.error.validation.minSupMax\": \"Darf nicht höher sein\",\n    \"components.Input.error.validation.regex\": \"Dieser Wert entspricht nicht dem RegEx.\",\n    \"components.Input.error.validation.required\": \"Die Eingabe dieses Wertes ist erforderlich.\",\n    \"components.Input.error.validation.unique\": \"Der Wert wird bereits genutzt.\",\n    \"components.InputSelect.option.placeholder\": \"Hier wählen\",\n    \"components.ListRow.empty\": \"Es gibt keine Daten.\",\n    \"components.NotAllowedInput.text\": \"Keine Berechtigung dieses Feld zu sehen\",\n    \"components.OverlayBlocker.description\": \"Es wird ein Feature verwendet, das einen Neustart des Servers erfordert. Bitte warte bis der Server neu gestartet wurde.\",\n    \"components.OverlayBlocker.description.serverError\": \"Der Server sollte neu gestartet sein, bitte Logs im Terminal überprüfen.\",\n    \"components.OverlayBlocker.title\": \"Warten auf Neustart.....\",\n    \"components.OverlayBlocker.title.serverError\": \"Der Neustart dauert länger als erwartet.\",\n    \"components.PageFooter.select\": \"Einträge pro Seite\",\n    \"components.ProductionBlocker.description\": \"Aus Sicherheitsgründen müssen wir dieses Plugin in anderen Umgebungen deaktivieren.\",\n    \"components.ProductionBlocker.header\": \"Dieses Plugin ist nur in der Entwicklungsumgebung verfügbar.\",\n    \"components.Search.placeholder\": \"Suche...\",\n    \"components.TableHeader.sort\": \"Sortiere nach {label}\",\n    \"components.Wysiwyg.ToggleMode.markdown-mode\": \"Markdown-Modus\",\n    \"components.Wysiwyg.ToggleMode.preview-mode\": \"Vorschau-Modus\",\n    \"components.Wysiwyg.collapse\": \"Verkleinern\",\n    \"components.Wysiwyg.selectOptions.H1\": \"Überschrift H1\",\n    \"components.Wysiwyg.selectOptions.H2\": \"Überschrift H2\",\n    \"components.Wysiwyg.selectOptions.H3\": \"Überschrift H3\",\n    \"components.Wysiwyg.selectOptions.H4\": \"Überschrift H4\",\n    \"components.Wysiwyg.selectOptions.H5\": \"Überschrift H5\",\n    \"components.Wysiwyg.selectOptions.H6\": \"Überschrift H6\",\n    \"components.Wysiwyg.selectOptions.title\": \"Überschrift hinzufügen\",\n    \"components.WysiwygBottomControls.charactersIndicators\": \"Zeichen\",\n    \"components.WysiwygBottomControls.fullscreen\": \"Vergrößern\",\n    \"components.WysiwygBottomControls.uploadFiles\": \"Datei hierher ziehen, {browse} eine Datei zum hochladen auswählen oder aus der Zwischenablage einfügen.\",\n    \"components.WysiwygBottomControls.uploadFiles.browse\": \"wähle\",\n    \"components.pagination.go-to\": \"Gehe zu Seite {page}\",\n    \"components.pagination.go-to-next\": \"Gehe zur nächsten Seite\",\n    \"components.pagination.go-to-previous\": \"Gehe zur vorherigen Seite\",\n    \"components.pagination.remaining-links\": \"Und {number} weitere Links\",\n    \"components.popUpWarning.button.cancel\": \"Nein,abbrechen\",\n    \"components.popUpWarning.button.confirm\": \"Ja,bestätigen\",\n    \"components.popUpWarning.message\": \"Wirklich löschen?\",\n    \"components.popUpWarning.title\": \"Bitte bestätigen\",\n    \"form.button.done\": \"Fertig\",\n    \"global.actions\": \"Aktionen\",\n    \"global.back\": \"Zurück\",\n    \"global.change-password\": \"Passwort ändern\",\n    \"global.content-manager\": \"Inhalts-Manager\",\n    \"global.continue\": \"Weiter\",\n    \"global.delete\": \"Löschen\",\n    \"global.delete-target\": \"Lösche {target}\",\n    \"global.description\": \"Beschreibung\",\n    \"global.details\": \"Details\",\n    \"global.disabled\": \"Deaktiviert\",\n    \"global.documentation\": \"Dokumentation\",\n    \"global.enabled\": \"Aktiviert\",\n    \"global.finish\": \"Fertig\",\n    \"global.marketplace\": \"Marketplace\",\n    \"global.name\": \"Name\",\n    \"global.none\": \"Keine\",\n    \"global.password\": \"Passwort\",\n    \"global.plugins\": \"Plugins\",\n    \"global.profile\": \"Profil\",\n    \"global.prompt.unsaved\": \"Seite wirklich verlassen? Alle Änderungen gehen hierdurch verloren.\",\n    \"global.reset-password\": \"Passwort zurücksetzen\",\n    \"global.roles\": \"Rollen\",\n    \"global.save\": \"Speichern\",\n    \"global.see-more\": \"Mehr anzeigen\",\n    \"global.select\": \"Auswählen\",\n    \"global.select-all-entries\": \"Wähle alle Einträge aus\",\n    \"global.settings\": \"Einstellungen\",\n    \"global.type\": \"Typ\",\n    \"global.users\": \"Benutzer\",\n    \"notification.contentType.relations.conflict\": \"Content Type hat Konflikt in Beziehungen\",\n    \"notification.default.title\": \"Information:\",\n    \"notification.error\": \"Ein Fehler ist aufgetreten\",\n    \"notification.error.layout\": \"Das Layout konnte nicht abgerufen werden.\",\n    \"notification.form.error.fields\": \"Das Formular enthält Fehler\",\n    \"notification.form.success.fields\": \"Änderungen gespeichert\",\n    \"notification.link-copied\": \"Link in die Zwischenablage kopiert\",\n    \"notification.permission.not-allowed-read\": \"Keine Berechtigung dieses Dokument einzusehen\",\n    \"notification.success.delete\": \"Eintrag wurde gelöscht\",\n    \"notification.success.saved\": \"Gespeichert\",\n    \"notification.success.title\": \"Erfolg:\",\n    \"notification.version.update.message\": \"Eine neue Strapi Version ist verfügbar\",\n    \"notification.warning.title\": \"Warnung:\",\n    or: or,\n    \"request.error.model.unknown\": \"Dieses Schema existiert nicht\",\n    skipToContent: skipToContent,\n    submit: submit\n};\n\nexport { Analytics, Documentation, Email, Password, Provider, ResetPasswordToken, Role, Username, Users, anErrorOccurred, clearLabel, de as default, or, skipToContent, submit };\n//# sourceMappingURL=de.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,YAAY;AAChB,IAAI,gBAAgB;AACpB,IAAI,QAAQ;AACZ,IAAI,WAAW;AACf,IAAI,WAAW;AACf,IAAI,qBAAqB;AACzB,IAAI,OAAO;AACX,IAAI,WAAW;AACf,IAAI,QAAQ;AACZ,IAAI,kBAAkB;AACtB,IAAI,aAAa;AACjB,IAAI,KAAK;AACT,IAAI,gBAAgB;AACpB,IAAI,SAAS;AACb,IAAI,KAAK;AAAA,EACL;AAAA,EACA,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0CAA0C;AAAA,EAC1C,iCAAiC;AAAA,EACjC,sCAAsC;AAAA,EACtC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,iDAAiD;AAAA,EACjD,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,2BAA2B;AAAA,EAC3B,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA,EAChB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,6BAA6B;AAAA,EAC7B,qCAAqC;AAAA,EACrC,0CAA0C;AAAA,EAC1C,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,4BAA4B;AAAA,EAC5B,4BAA4B;AAAA,EAC5B,0CAA0C;AAAA,EAC1C,sBAAsB;AAAA,EACtB,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,4BAA4B;AAAA,EAC5B,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,oCAAoC;AAAA,EACpC,sCAAsC;AAAA,EACtC,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,sCAAsC;AAAA,EACtC,8BAA8B;AAAA,EAC9B,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,yDAAyD;AAAA,EACzD,iDAAiD;AAAA,EACjD,8DAA8D;AAAA,EAC9D,mEAAmE;AAAA,EACnE,kEAAkE;AAAA,EAClE,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,8DAA8D;AAAA,EAC9D,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,oDAAoD;AAAA,EACpD,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,mCAAmC;AAAA,EACnC,iDAAiD;AAAA,EACjD,yDAAyD;AAAA,EACzD,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,wCAAwC;AAAA,EACxC,0CAA0C;AAAA,EAC1C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,6CAA6C;AAAA,EAC7C,yDAAyD;AAAA,EACzD,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,wCAAwC;AAAA,EACxC,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,2CAA2C;AAAA,EAC3C,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,4CAA4C;AAAA,EAC5C,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,2BAA2B;AAAA,EAC3B,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,qBAAqB;AAAA,EACrB,sBAAsB;AAAA,EACtB,2BAA2B;AAAA,EAC3B,gDAAgD;AAAA,EAChD,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,+BAA+B;AAAA,EAC/B,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,2CAA2C;AAAA,EAC3C,mDAAmD;AAAA,EACnD,2CAA2C;AAAA,EAC3C,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,uDAAuD;AAAA,EACvD,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,kDAAkD;AAAA,EAClD,kDAAkD;AAAA,EAClD,wCAAwC;AAAA,EACxC;AAAA,EACA,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,wCAAwC;AAAA,EACxC,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,4CAA4C;AAAA,EAC5C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,+CAA+C;AAAA,EAC/C,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,+CAA+C;AAAA,EAC/C,sDAAsD;AAAA,EACtD,wDAAwD;AAAA,EACxD,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,yDAAyD;AAAA,EACzD,qDAAqD;AAAA,EACrD,4CAA4C;AAAA,EAC5C,2CAA2C;AAAA,EAC3C,gDAAgD;AAAA,EAChD,4CAA4C;AAAA,EAC5C,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,0CAA0C;AAAA,EAC1C,sCAAsC;AAAA,EACtC,2CAA2C;AAAA,EAC3C,wDAAwD;AAAA,EACxD,sDAAsD;AAAA,EACtD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,2DAA2D;AAAA,EAC3D,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,oCAAoC;AAAA,EACpC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,2CAA2C;AAAA,EAC3C,2BAA2B;AAAA,EAC3B,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,+EAA+E;AAAA,EAC/E,qEAAqE;AAAA,EACrE,yEAAyE;AAAA,EACzE,mEAAmE;AAAA,EACnE,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,4DAA4D;AAAA,EAC5D,0DAA0D;AAAA,EAC1D,sEAAsE;AAAA,EACtE,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,qDAAqD;AAAA,EACrD,wDAAwD;AAAA,EACxD,yDAAyD;AAAA,EACzD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,qCAAqC;AAAA,EACrC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,oEAAoE;AAAA,EACpE,4DAA4D;AAAA,EAC5D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,wDAAwD;AAAA,EACxD,4BAA4B;AAAA,EAC5B,sBAAsB;AAAA,EACtB,+BAA+B;AAAA,EAC/B,yCAAyC;AAAA,EACzC,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,4BAA4B;AAAA,EAC5B,uBAAuB;AAAA,EACvB,kBAAkB;AAAA,EAClB,yCAAyC;AAAA,EACzC,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,gCAAgC;AAAA,EAChC,wCAAwC;AAAA,EACxC,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB;AAAA,EACA,eAAe;AAAA,EACf,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,+CAA+C;AAAA,EAC/C,qDAAqD;AAAA,EACrD,sDAAsD;AAAA,EACtD,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,0CAA0C;AAAA,EAC1C,4CAA4C;AAAA,EAC5C,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,gDAAgD;AAAA,EAChD,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,yDAAyD;AAAA,EACzD,+CAA+C;AAAA,EAC/C,gDAAgD;AAAA,EAChD,uDAAuD;AAAA,EACvD,+BAA+B;AAAA,EAC/B,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,yCAAyC;AAAA,EACzC,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,eAAe;AAAA,EACf,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,wBAAwB;AAAA,EACxB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,sBAAsB;AAAA,EACtB,eAAe;AAAA,EACf,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,yBAAyB;AAAA,EACzB,yBAAyB;AAAA,EACzB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,+CAA+C;AAAA,EAC/C,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,4CAA4C;AAAA,EAC5C,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B;AAAA,EACA,+BAA+B;AAAA,EAC/B;AAAA,EACA;AACJ;", "names": []}