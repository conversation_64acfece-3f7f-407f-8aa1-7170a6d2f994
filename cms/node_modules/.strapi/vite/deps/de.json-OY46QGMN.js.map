{"version": 3, "sources": ["../../../@strapi/content-type-builder/dist/admin/translations/de.json.mjs"], "sourcesContent": ["var configurations = \"Konfigurationen\";\nvar from = \"von\";\nvar de = {\n    \"attribute.boolean\": \"Boolean\",\n    \"attribute.boolean.description\": \"<PERSON>a oder nein, 1 oder 0, wahr oder falsch\",\n    \"attribute.component\": \"Komponente\",\n    \"attribute.component.description\": \"Gruppierung an Feldern, die wiederholt und wiederbenutzt werden kann\",\n    \"attribute.date\": \"Datum\",\n    \"attribute.date.description\": \"Eine Datums-Auswahl mit Stunden, Minuten und Sekunden\",\n    \"attribute.datetime\": \"Datum mit Uhrzeit\",\n    \"attribute.dynamiczone\": \"Dynamische Zone\",\n    \"attribute.dynamiczone.description\": \"Beliebige Komponenten beim Bearbeiten des Inhalts wählen\",\n    \"attribute.email\": \"E-Mail\",\n    \"attribute.email.description\": \"E-Mail-Feld mit Validierung\",\n    \"attribute.enumeration\": \"Enumeration\",\n    \"attribute.enumeration.description\": \"Aufzählung an Auswahlmöglichkeiten, von denen eine gewählt werden muss\",\n    \"attribute.json\": \"JSON\",\n    \"attribute.json.description\": \"Daten im JSON-Format\",\n    \"attribute.media\": \"Medien\",\n    \"attribute.media.description\": \"Dateien wie Bilder, Videos, etc\",\n    \"attribute.null\": \" \",\n    \"attribute.number\": \"Zahl\",\n    \"attribute.number.description\": \"Zahlen (ganzzahlig, Gleitkommazahl, dezimal)\",\n    \"attribute.password\": \"Passwort\",\n    \"attribute.password.description\": \"Passwort-Feld mit Verschlüsselung\",\n    \"attribute.relation\": \"Beziehung\",\n    \"attribute.relation.description\": \"Beziehung mit einem anderen Eintrag\",\n    \"attribute.richtext\": \"Formatierter Text\",\n    \"attribute.richtext.description\": \"Ein Text-Editor mit Formatierungsoptionen\",\n    \"attribute.text\": \"Text\",\n    \"attribute.text.description\": \"Ein- oder mehrzeiliger Text wie Titel oder Beschreibungen\",\n    \"attribute.time\": \"Uhrzeit\",\n    \"attribute.timestamp\": \"Zeitstempel\",\n    \"attribute.uid\": \"UID\",\n    \"attribute.uid.description\": \"Einzigartiger Identifier\",\n    \"button.attributes.add.another\": \"Weiteres Feld hinzufügen\",\n    \"button.component.add\": \"Komponente hinzufügen\",\n    \"button.component.create\": \"Neue Komponente erstellen\",\n    \"button.model.create\": \"Neue Sammlung erstellen\",\n    \"button.single-types.create\": \"Neuen Einzel-Eintrag erstellen\",\n    \"component.repeatable\": \"(wiederholbar)\",\n    \"components.SelectComponents.displayed-value\": \"{number, plural, =0 {# Komponenten} one {# Komponente} other {# Komponenten}} ausgewählt\",\n    \"components.componentSelect.no-component-available\": \"Du hast bereits alle Komponenten hinzugefügt\",\n    \"components.componentSelect.no-component-available.with-search\": \"Es gibt keine Komponenten, die diesem Begriff entsprechen\",\n    \"components.componentSelect.value-component\": \"{number} Komponente ausgewählt (Tippen um nach Komponente zu suchen)\",\n    \"components.componentSelect.value-components\": \"{number} Komponenten ausgewählt\",\n    configurations: configurations,\n    \"contentType.apiId-plural.description\": \"API-ID im Plural\",\n    \"contentType.apiId-plural.label\": \"Plural API ID\",\n    \"contentType.apiId-singular.description\": \"Die UID wird verwendet, um API-Routen und Datenbank-Tabellen/-Sammlungen zu erstellen\",\n    \"contentType.apiId-singular.label\": \"Singular API ID\",\n    \"contentType.collectionName.description\": \"Nützlich wenn sich der Name der Sammlung und der Tabellenname unterscheiden\",\n    \"contentType.collectionName.label\": \"Name der Sammlung\",\n    \"contentType.displayName.label\": \"Anzeigename\",\n    \"contentType.kind.change.warning\": \"Du hast die Art eines Inhaltstyps geändert: API wird resettet (Routen, Controller und Services werden überschrieben).\",\n    \"error.attributeName.reserved-name\": \"Dieser Name kann nicht für Attribute genutzt werden, da er andere Funktionalitäten beeinträchtigen würde\",\n    \"error.contentType.pluralName-used\": \"Dieser Wert kann nicht gleich sein wie der Singular-Wert\",\n    \"error.contentType.singularName-used\": \"Dieser Wert kann nicht gleich sein wie der Plural-Wert\",\n    \"error.contentTypeName.reserved-name\": \"Dieser Name kann nicht genutzt werden, da er andere Funktionalitäten beeinträchtigen würde\",\n    \"error.validation.enum-duplicate\": \"Doppelte Werte sind nicht erlaubt\",\n    \"error.validation.enum-empty-string\": \"Leere Werte sind nicht erlaubt\",\n    \"error.validation.enum-number\": \"Werte können nicht mit einer Zahl beginnen\",\n    \"error.validation.minSupMax\": \"Wert kann nicht höher sein\",\n    \"error.validation.positive\": \"Muss eine positive Zahl sein\",\n    \"error.validation.regex\": \"Regex-Pattern ist ungültig\",\n    \"error.validation.relation.targetAttribute-taken\": \"Dieser Name existiert bereits im Ziel-Typ\",\n    \"form.attribute.component.option.add\": \"Komponente hinzufügen\",\n    \"form.attribute.component.option.create\": \"Neue Komponente erstellen\",\n    \"form.attribute.component.option.create.description\": \"Eine Komponente ist überall verfügbar und wird unter Inhaltstypen und anderen Komponenten geteilt.\",\n    \"form.attribute.component.option.repeatable\": \"Wiederholbare Komponenten\",\n    \"form.attribute.component.option.repeatable.description\": \"Nützlich für mehrere Instanzen (Array) an Zutaten, Meta-Tags, etc...\",\n    \"form.attribute.component.option.reuse-existing\": \"Bereits existierende Komponente nutzen\",\n    \"form.attribute.component.option.reuse-existing.description\": \"Eine bereits erstellte Komponente benutzen, um Daten über Inhaltstypen hinweg konsistent zu halten.\",\n    \"form.attribute.component.option.single\": \"Einzelne Komponente\",\n    \"form.attribute.component.option.single.description\": \"Nützlich um Felder wie volle Addresse, Hauptinformationen, etc. zu grupppieren\",\n    \"form.attribute.item.customColumnName\": \"Eigener Spaltenname\",\n    \"form.attribute.item.customColumnName.description\": \"Dies ist nützlich, um Spalten in der Datenbank für Antworten der API umzubenennen\",\n    \"form.attribute.item.date.type.date\": \"Datum (Bsp: 01.01.{currentYear})\",\n    \"form.attribute.item.date.type.datetime\": \"Datum & Uhrzeit (Bsp: 01.01.{currentYear} 00:00)\",\n    \"form.attribute.item.date.type.time\": \"Uhrzeit (Bsp: 00:00)\",\n    \"form.attribute.item.defineRelation.fieldName\": \"Feldname\",\n    \"form.attribute.item.enumeration.graphql\": \"Namensüberschreibung für GraphQL\",\n    \"form.attribute.item.enumeration.graphql.description\": \"Ermöglicht, den standardmäßig generierten Namen für GraphQL zu überschreiben\",\n    \"form.attribute.item.enumeration.placeholder\": \"Bsp:\\nMorgen\\nMittag\\nAbend\",\n    \"form.attribute.item.enumeration.rules\": \"Werte (einer pro Zeile)\",\n    \"form.attribute.item.maximum\": \"Maximalwert\",\n    \"form.attribute.item.maximumLength\": \"Maximallänge\",\n    \"form.attribute.item.minimum\": \"Mindestwert\",\n    \"form.attribute.item.minimumLength\": \"Mindestlänge\",\n    \"form.attribute.item.number.type\": \"Zahlenformat\",\n    \"form.attribute.item.number.type.biginteger\": \"große Ganzzahl (ex: 123456789)\",\n    \"form.attribute.item.number.type.decimal\": \"dezimal (z.B.: 2.22)\",\n    \"form.attribute.item.number.type.float\": \"Gleitkommazahl (z.B.: 3.33333333)\",\n    \"form.attribute.item.number.type.integer\": \"ganzzahlig (z.B.: 10)\",\n    \"form.attribute.item.privateField\": \"Privates Feld\",\n    \"form.attribute.item.privateField.description\": \"Dieses Feld wird nicht in API-Abfragen angezeigt\",\n    \"form.attribute.item.requiredField\": \"Benötigtes Feld\",\n    \"form.attribute.item.requiredField.description\": \"Du wirst keinen Eintrag anlegen können, wenn dieses Feld leer ist\",\n    \"form.attribute.item.text.regex\": \"RegExp-Pattern\",\n    \"form.attribute.item.text.regex.description\": \"Der Text der Regular Expression\",\n    \"form.attribute.item.uniqueField\": \"Einzigartiges Feld\",\n    \"form.attribute.item.uniqueField.description\": \"Du wirst keinen Eintrag anlegen können, wenn es bereits einen Eintrag mit identischem Inhalt gibt\",\n    \"form.attribute.media.allowed-types\": \"Wähle erlaubte Arten von Medien\",\n    \"form.attribute.media.allowed-types.option-files\": \"Dateien\",\n    \"form.attribute.media.allowed-types.option-images\": \"Bilder\",\n    \"form.attribute.media.allowed-types.option-videos\": \"Videos\",\n    \"form.attribute.media.option.multiple\": \"Mehrere Medien\",\n    \"form.attribute.media.option.multiple.description\": \"Nützlich für Slider, Galerien oder Downloads von mehreren Dateien\",\n    \"form.attribute.media.option.single\": \"Einzelne Medien-Datei\",\n    \"form.attribute.media.option.single.description\": \"Nützlich für Profilbilder oder Cover-Bilder\",\n    \"form.attribute.settings.default\": \"Standardwert\",\n    \"form.attribute.text.option.long-text\": \"Mehrzeiliger Text\",\n    \"form.attribute.text.option.long-text.description\": \"Nützlich für Beschreibungen, Biographien. Exakte Suche ist deaktiviert\",\n    \"form.attribute.text.option.short-text\": \"Einzeiliger Text\",\n    \"form.attribute.text.option.short-text.description\": \"Nützlich für Titel, Namen, Links (URL). Ermöglicht exakte Suche.\",\n    \"form.button.add-components-to-dynamiczone\": \"Komponenten zur Zone hinzufügen\",\n    \"form.button.add-field\": \"Weiteres Feld hinzufügen\",\n    \"form.button.add-first-field-to-created-component\": \"Erstes Feld zur Komponente hinzufügen\",\n    \"form.button.add.field.to.collectionType\": \"Weiteres Feld zur Sammlung hinzufügen\",\n    \"form.button.add.field.to.component\": \"Weiteres Feld zur Komponente hinzufügen\",\n    \"form.button.add.field.to.contentType\": \"Weiteres Feld zum Inhaltstyp hinzufügen\",\n    \"form.button.add.field.to.singleType\": \"Weiteres Feld zum Einzel-Eintrag hinzufügen\",\n    \"form.button.cancel\": \"Abbrechen\",\n    \"form.button.collection-type.description\": \"Nützlich für mehrere Instanzen wie Artikel, Produkte, Kommentare, etc.\",\n    \"form.button.collection-type.name\": \"Inhalts-Typ\",\n    \"form.button.configure-component\": \"Komponente konfigurieren\",\n    \"form.button.configure-view\": \"Ansicht konfigurieren\",\n    \"form.button.select-component\": \"Komponente auswählen\",\n    \"form.button.single-type.description\": \"Nützlich für einzelne Instanz wie Über uns, Startseite, etc.\",\n    \"form.button.single-type.name\": \"Einzel-Eintrag\",\n    from: from,\n    \"menu.section.components.name\": \"Komponenten\",\n    \"menu.section.models.name\": \"Sammlungen\",\n    \"menu.section.single-types.name\": \"Einzel-Einträge\",\n    \"modalForm.attribute.form.base.name.description\": \"Leerzeichen sind im Name eines Attributs nicht erlaubt\",\n    \"modalForm.attribute.form.base.name.placeholder\": \"z.B. slug, seoUrl, canonicalUrl\",\n    \"modalForm.attribute.target-field\": \"Verknüpftes Feld\",\n    \"modalForm.attributes.select-component\": \"Wähle eine Komponente\",\n    \"modalForm.attributes.select-components\": \"Wähle die Komponenten\",\n    \"modalForm.collectionType.header-create\": \"Erstelle einen Inhalts-Typ\",\n    \"modalForm.component.header-create\": \"Erstelle eine Komponente\",\n    \"modalForm.components.create-component.category.label\": \"Wähle Kategorie oder gebe neuen Namen ein, um eine zu erstellen\",\n    \"modalForm.components.icon.label\": \"Icon\",\n    \"modalForm.editCategory.base.name.description\": \"Leerzeichen sind im Name einer Kategorie nicht erlaubt\",\n    \"modalForm.header-edit\": \"Bearbeite {name}\",\n    \"modalForm.header.categories\": \"Kategorien\",\n    \"modalForm.header.back\": \"Zurück\",\n    \"modalForm.singleType.header-create\": \"Erstelle einen Einzel-Eintrag\",\n    \"modalForm.sub-header.addComponentToDynamicZone\": \"Komponente zur dynamischen Zone hinzufügen\",\n    \"modalForm.sub-header.attribute.create\": \"Erstelle neues {type}-Feld\",\n    \"modalForm.sub-header.attribute.create.step\": \"Neue Komponente ({step}/2)\",\n    \"modalForm.sub-header.attribute.edit\": \"Bearbeite {name}\",\n    \"modalForm.sub-header.chooseAttribute.collectionType\": \"Wähle ein Feld für die Sammlung\",\n    \"modalForm.sub-header.chooseAttribute.component\": \"Wähle ein Feld für die Komponente\",\n    \"modalForm.sub-header.chooseAttribute.singleType\": \"Wähle ein Feld für den Einzel-Eintrag\",\n    \"modelPage.attribute.relation-polymorphic\": \"Beziehung (polymorph)\",\n    \"modelPage.attribute.relationWith\": \"Beziehung mit\",\n    \"notification.error.dynamiczone-min.validation\": \"Eine dynamische Zone braucht mindestens eine Komponente, bevor sie gespeichert werden kann\",\n    \"notification.info.autoreaload-disable\": \"Das autoReload-Feature wird für dieses Plugin benötigt. Starte deinen Server mit `strapi develop`\",\n    \"notification.info.creating.notSaved\": \"Bitte speichere deine Arbeit bevor du einen neuen Inhaltstyp oder eine neue Komponente erstellst\",\n    \"plugin.description.long\": \"Modelliere die Datenstruktur deiner API. Lege neue Felder und Beziehungen innerhalb von einer Minute an. Erforderliche Dateien werden automatisch in deinem Projekt angelegt und aktualisiert.\",\n    \"plugin.description.short\": \"Modelliere die Datenstruktur deiner API.\",\n    \"plugin.name\": \"Content-Type Builder\",\n    \"popUpForm.navContainer.advanced\": \"Fortgeschrittene Einstellungen\",\n    \"popUpForm.navContainer.base\": \"Grundeinstellungen\",\n    \"popUpWarning.bodyMessage.cancel-modifications\": \"Bist du dir sicher, dass du alle deine Änderungen abbrechen willst?\",\n    \"popUpWarning.bodyMessage.cancel-modifications.with-components\": \"Bist du dir sicher, dass du alle deine Änderungen abbrechen willst? Es wurden Komponenten erstellt oder bearbeitet...\",\n    \"popUpWarning.bodyMessage.category.delete\": \"Bist du dir sicher, dass du diese Kategorie löschen willst? Alle dazugehörigen Komponenten werden ebenfalls gelöscht.\",\n    \"popUpWarning.bodyMessage.component.delete\": \"Bist du dir sicher, dass du diese Komponente löschen willst?\",\n    \"popUpWarning.bodyMessage.contentType.delete\": \"Bist du sicher, dass du diesen Inhaltstyp löschen willst?\",\n    \"popUpWarning.draft-publish.button.confirm\": \"Ja, deaktivieren\",\n    \"popUpWarning.draft-publish.message\": \"Wenn du das Entwurf/Veröffentlichen-System deaktivierst werden alle Entwürfe gelöscht.\",\n    \"popUpWarning.draft-publish.second-message\": \"Bist du dir sicher, dass du es deaktivieren willst?\",\n    \"prompt.unsaved\": \"Bist du dir sicher, dass du diese Seite verlassen willst? Deine Änderungen werden verworfen.\",\n    \"relation.attributeName.placeholder\": \"z.B.: Autor, Kategorie\",\n    \"relation.manyToMany\": \"hat und gehört zu vielen\",\n    \"relation.manyToOne\": \"hat viele\",\n    \"relation.manyWay\": \"hat viele\",\n    \"relation.oneToMany\": \"gehört zu vielen\",\n    \"relation.oneToOne\": \"hat und gehört zu ein(-er/-em)\",\n    \"relation.oneWay\": \"hat ein(-e/-en)\",\n    \"table.button.no-fields\": \"Neues Feld hinzufügen\",\n    \"table.content.create-first-content-type\": \"Erstelle deinen ersten Inhalts-Typ\",\n    \"table.content.no-fields.collection-type\": \"Füge diesem Inhalts-Typ das erstes Feld hinzu\",\n    \"table.content.no-fields.component\": \"Füge dieser Komponente das erstes Feld hinzu\"\n};\n\nexport { configurations, de as default, from };\n//# sourceMappingURL=de.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,iBAAiB;AACrB,IAAI,OAAO;AACX,IAAI,KAAK;AAAA,EACL,qBAAqB;AAAA,EACrB,iCAAiC;AAAA,EACjC,uBAAuB;AAAA,EACvB,mCAAmC;AAAA,EACnC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,mBAAmB;AAAA,EACnB,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,mBAAmB;AAAA,EACnB,+BAA+B;AAAA,EAC/B,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,gCAAgC;AAAA,EAChC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,wBAAwB;AAAA,EACxB,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,+CAA+C;AAAA,EAC/C,qDAAqD;AAAA,EACrD,iEAAiE;AAAA,EACjE,8CAA8C;AAAA,EAC9C,+CAA+C;AAAA,EAC/C;AAAA,EACA,wCAAwC;AAAA,EACxC,kCAAkC;AAAA,EAClC,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,gCAAgC;AAAA,EAChC,8BAA8B;AAAA,EAC9B,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,mDAAmD;AAAA,EACnD,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,sDAAsD;AAAA,EACtD,8CAA8C;AAAA,EAC9C,0DAA0D;AAAA,EAC1D,kDAAkD;AAAA,EAClD,8DAA8D;AAAA,EAC9D,0CAA0C;AAAA,EAC1C,sDAAsD;AAAA,EACtD,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,sCAAsC;AAAA,EACtC,0CAA0C;AAAA,EAC1C,sCAAsC;AAAA,EACtC,gDAAgD;AAAA,EAChD,2CAA2C;AAAA,EAC3C,uDAAuD;AAAA,EACvD,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+BAA+B;AAAA,EAC/B,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,8CAA8C;AAAA,EAC9C,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,oCAAoC;AAAA,EACpC,gDAAgD;AAAA,EAChD,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,kCAAkC;AAAA,EAClC,8CAA8C;AAAA,EAC9C,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,sCAAsC;AAAA,EACtC,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,sCAAsC;AAAA,EACtC,kDAAkD;AAAA,EAClD,mCAAmC;AAAA,EACnC,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,6CAA6C;AAAA,EAC7C,yBAAyB;AAAA,EACzB,oDAAoD;AAAA,EACpD,2CAA2C;AAAA,EAC3C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,sBAAsB;AAAA,EACtB,2CAA2C;AAAA,EAC3C,oCAAoC;AAAA,EACpC,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,gCAAgC;AAAA,EAChC,uCAAuC;AAAA,EACvC,gCAAgC;AAAA,EAChC;AAAA,EACA,gCAAgC;AAAA,EAChC,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,kDAAkD;AAAA,EAClD,kDAAkD;AAAA,EAClD,oCAAoC;AAAA,EACpC,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,qCAAqC;AAAA,EACrC,wDAAwD;AAAA,EACxD,mCAAmC;AAAA,EACnC,gDAAgD;AAAA,EAChD,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,sCAAsC;AAAA,EACtC,kDAAkD;AAAA,EAClD,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,mDAAmD;AAAA,EACnD,4CAA4C;AAAA,EAC5C,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,eAAe;AAAA,EACf,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,iDAAiD;AAAA,EACjD,iEAAiE;AAAA,EACjE,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,+CAA+C;AAAA,EAC/C,6CAA6C;AAAA,EAC7C,sCAAsC;AAAA,EACtC,6CAA6C;AAAA,EAC7C,kBAAkB;AAAA,EAClB,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,0BAA0B;AAAA,EAC1B,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,qCAAqC;AACzC;", "names": []}