{"version": 3, "sources": ["../../../@strapi/content-manager/dist/admin/translations/de.json.mjs"], "sourcesContent": ["var groups = \"Gruppen\";\nvar models = \"Sammlungen\";\nvar pageNotFound = \"Seite nicht gefunden\";\nvar de = {\n    \"App.schemas.data-loaded\": \"Die Schemata wurden geladen\",\n    \"ListViewTable.relation-loaded\": \"Beziehungen wurden geladen\",\n    \"ListViewTable.relation-loading\": \"Beziehungen laden\",\n    \"ListViewTable.relation-more\": \"Diese Beziehung enthält mehr Einträge als angezeigt\",\n    \"EditRelations.title\": \"Beziehungs-Daten\",\n    \"HeaderLayout.button.label-add-entry\": \"Neuer Eintrag\",\n    \"api.id\": \"API ID\",\n    \"components.AddFilterCTA.add\": \"Filter\",\n    \"components.AddFilterCTA.hide\": \"Filter\",\n    \"components.DragHandle-label\": \"Ziehen\",\n    \"components.DraggableAttr.edit\": \"Klicken zum Bearbeiten\",\n    \"components.DraggableCard.delete.field\": \"<PERSON>ösche {item}\",\n    \"components.DraggableCard.edit.field\": \"Bearbeite {item}\",\n    \"components.DraggableCard.move.field\": \"Verschiebe {item}\",\n    \"components.ListViewTable.row-line\": \"Eintrag Zeile {number}\",\n    \"components.DynamicZone.ComponentPicker-label\": \"Wähle eine Komponente\",\n    \"components.DynamicZone.add-component\": \"Füge {componentName} eine Komponente hinzu\",\n    \"components.DynamicZone.delete-label\": \"Lösche {name}\",\n    \"components.DynamicZone.error-message\": \"Die Komponente enthält einen oder mehrere Fehler\",\n    \"components.DynamicZone.missing-components\": \"{number, plura, one {# Komponente} other {# Komponenten}} fehlen\",\n    \"components.DynamicZone.move-down-label\": \"Verschiebe Komponente nach unten\",\n    \"components.DynamicZone.move-up-label\": \"Verschiebe Komponente nach oben\",\n    \"components.DynamicZone.pick-compo\": \"Wähle eine Komponente\",\n    \"components.DynamicZone.required\": \"Komponente wird benötigt\",\n    \"components.EmptyAttributesBlock.button\": \"Gehe zu den Einstellungen\",\n    \"components.EmptyAttributesBlock.description\": \"Du kannst deine Einstellungen ändern\",\n    \"components.FieldItem.linkToComponentLayout\": \"Layout der Komponente anpassen\",\n    \"components.FieldSelect.label\": \"Füge Feld hinzu\",\n    \"components.FilterOptions.button.apply\": \"Anwenden\",\n    \"components.FiltersPickWrapper.PluginHeader.actions.apply\": \"Anwenden\",\n    \"components.FiltersPickWrapper.PluginHeader.actions.clearAll\": \"Alle löschen\",\n    \"components.FiltersPickWrapper.PluginHeader.description\": \"Lege die Bedingungen fest, unter denen die Einträge gefiltert werden sollen\",\n    \"components.FiltersPickWrapper.PluginHeader.title.filter\": \"Filter\",\n    \"components.FiltersPickWrapper.hide\": \"Ausblenden\",\n    \"components.LeftMenu.Search.label\": \"Suche nach einem Inhaltstyp\",\n    \"components.LeftMenu.collection-types\": \"Sammlungen\",\n    \"components.LeftMenu.single-types\": \"Einzel-Einträge\",\n    \"components.LimitSelect.itemsPerPage\": \"Einträge pro Seite\",\n    \"components.NotAllowedInput.text\": \"Keine Berechtigung dieses Feld anzusehen\",\n    \"components.RepeatableComponent.error-message\": \"Die Komponente(n) enthält einen/enthalten Fehler\",\n    \"components.Search.placeholder\": \"Suche nach einem Eintrag....\",\n    \"components.Select.draft-info-title\": \"Status: Entwurf\",\n    \"components.Select.publish-info-title\": \"State: Veröffentlicht\",\n    \"components.SettingsViewWrapper.pluginHeader.description.edit-settings\": \"Anpassen, wie die Bearbeitungsansicht aussieht.\",\n    \"components.SettingsViewWrapper.pluginHeader.description.list-settings\": \"Einstellungen der Listenansicht anpassen.\",\n    \"components.SettingsViewWrapper.pluginHeader.title\": \"Ansicht anpassen - {name}\",\n    \"components.TableDelete.delete\": \"Alle löschen\",\n    \"components.TableDelete.deleteSelected\": \"Ausgewählte löschen\",\n    \"components.TableDelete.label\": \"{number, plural, one {# Eintrag} other {# Einträge}} ausgewählt\",\n    \"components.TableEmpty.withFilters\": \"Es gibt keine {contentType} mit den verwendeten Filtern...\",\n    \"components.TableEmpty.withSearch\": \"Es gibt keine {contentType}, die der Suche ({search}) entsprechen...\",\n    \"components.TableEmpty.withoutFilter\": \"Es gibt keine {contentType}...\",\n    \"components.empty-repeatable\": \"Noch keine Einträge. Nutze den Button unten um einen hinzuzufügen.\",\n    \"components.notification.info.maximum-requirement\": \"Die maximale Anzahl an Feldern wurde erreicht\",\n    \"components.notification.info.minimum-requirement\": \"Es wurde ein Feld hinzugefügt um die minimale Anzahl zu erfüllen\",\n    \"components.repeatable.reorder.error\": \"Während dem Ändern der Reihenfolge der Komponenten ist ein Fehler aufgetreten, bitte versuche es erneut\",\n    \"components.reset-entry\": \"Eintrag zurücksetzen\",\n    \"components.uid.apply\": \"apply\",\n    \"components.uid.available\": \"available\",\n    \"components.uid.regenerate\": \"regenerate\",\n    \"components.uid.suggested\": \"suggested\",\n    \"components.uid.unavailable\": \"unavailable\",\n    \"containers.Edit.Link.Layout\": \"Layout anpassen\",\n    \"containers.Edit.Link.Model\": \"Sammlung bearbeiten\",\n    \"containers.Edit.addAnItem\": \"Füge ein Item hinzu...\",\n    \"containers.Edit.clickToJump\": \"Klicke, um zu einem Eintrag zu springen\",\n    \"containers.Edit.delete\": \"Löschen\",\n    \"containers.Edit.delete-entry\": \"Diesen Eintrag löschen\",\n    \"containers.Edit.editing\": \"Bearbeite...\",\n    \"containers.Edit.information\": \"Informationen\",\n    \"containers.Edit.information.by\": \"Von\",\n    \"containers.Edit.information.created\": \"Erstellt\",\n    \"containers.Edit.information.draftVersion\": \"Entwurf\",\n    \"containers.Edit.information.editing\": \"Bearbeite\",\n    \"containers.Edit.information.lastUpdate\": \"Letzte Änderung\",\n    \"containers.Edit.information.publishedVersion\": \"veröffentlichte Version\",\n    \"containers.Edit.pluginHeader.title.new\": \"Eintrag erstellen\",\n    \"containers.Edit.reset\": \"Zurücksetzen\",\n    \"containers.Edit.returnList\": \"Zu Liste zurückkehren\",\n    \"containers.Edit.seeDetails\": \"Details\",\n    \"containers.Edit.submit\": \"Speichern\",\n    \"containers.EditSettingsView.modal-form.edit-field\": \"Feld bearbeiten\",\n    \"containers.EditView.add.new-entry\": \"Eintrag hinzufügen\",\n    \"containers.EditView.notification.errors\": \"Das Formular enthält Fehler\",\n    \"containers.Home.introduction\": \"Um deine Einträge zu verwalten, klicke auf den entsprechenden Link im Menü links. Dieses Plugin ist noch in aktiver Entwicklung und seine Einstellungen können nicht optimal angepasst werden.\",\n    \"containers.Home.pluginHeaderDescription\": \"Verwalte deine Einträge mithilfe eines mächtigen und wunderschönen Interfaces.\",\n    \"containers.Home.pluginHeaderTitle\": \"Inhalts-Manager\",\n    \"containers.List.draft\": \"Entwurf\",\n    \"containers.List.errorFetchRecords\": \"Fehler\",\n    \"containers.List.published\": \"Veröffentlicht\",\n    \"containers.list.displayedFields\": \"Dargestellte Felder\",\n    \"containers.list.items\": \"{number, plural, one {Eintrag} other {Einträge}}\",\n    \"containers.list.table-headers.publishedAt\": \"Status\",\n    \"containers.ListSettingsView.modal-form.edit-label\": \"Beschriftung ändern\",\n    \"containers.SettingPage.add.field\": \"Ein weiteres Feld hinzufügen\",\n    \"containers.SettingPage.attributes\": \"Attribut-Felder\",\n    \"containers.SettingPage.attributes.description\": \"Reihenfolge der Attribute festlegen\",\n    \"containers.SettingPage.editSettings.description\": \"Ziehe die Felder via Drag & Drop, um das Layout zu erstellen\",\n    \"containers.SettingPage.editSettings.entry.title\": \"Anzeigefeld\",\n    \"containers.SettingPage.editSettings.entry.title.description\": \"Anzeigefeld der Einträge wählen\",\n    \"containers.SettingPage.editSettings.relation-field.description\": \"Setze das dargestellte Feld sowohl in der Bearbeiten-, als auch der Listenansicht\",\n    \"containers.SettingPage.editSettings.title\": \"Bearbeiten (einstellungen)\",\n    \"containers.SettingPage.layout\": \"Layout\",\n    \"containers.SettingPage.listSettings.description\": \"Konfiguriere die Einstellungen für diesen Collection Type\",\n    \"containers.SettingPage.listSettings.title\": \"Listenansicht (Einstellungen)\",\n    \"containers.SettingPage.pluginHeaderDescription\": \"Konfiguriere die spezifische Ansicht für diesen Collection Type\",\n    \"containers.SettingPage.settings\": \"Einstellungen\",\n    \"containers.SettingPage.view\": \"Ansicht\",\n    \"containers.SettingViewModel.pluginHeader.title\": \"Inhalts-Manager - {name}\",\n    \"containers.SettingsPage.Block.contentType.description\": \"Spezifische Einstellungen konfigurieren\",\n    \"containers.SettingsPage.Block.contentType.title\": \"Sammlungen\",\n    \"containers.SettingsPage.Block.generalSettings.description\": \"Standardoptionen für Sammlungen konfigurieren\",\n    \"containers.SettingsPage.Block.generalSettings.title\": \"Generell\",\n    \"containers.SettingsPage.pluginHeaderDescription\": \"Einstellungen für alle Sammlungen und Gruppen konfigurieren\",\n    \"containers.SettingsView.list.subtitle\": \"Layout und Darstellung für alle Sammlungen und Gruppen konfigurieren\",\n    \"containers.SettingsView.list.title\": \"Darstellungsoptionen\",\n    \"edit-settings-view.link-to-ctb.components\": \"Komponente bearbeiten\",\n    \"edit-settings-view.link-to-ctb.content-types\": \"Inhalts-Typ bearbeiten\",\n    \"emptyAttributes.button\": \"Zum Sammlungs-Editor\",\n    \"emptyAttributes.description\": \"Füge das erste Feld zur Sammlung hinzu\",\n    \"emptyAttributes.title\": \"Es gibt noch keine Felder\",\n    \"error.attribute.key.taken\": \"Dieser Wert existiert bereits\",\n    \"error.attribute.sameKeyAndName\": \"Darf nicht gleich sein\",\n    \"error.attribute.taken\": \"Dieser Feldname ist bereits vergeben\",\n    \"error.contentTypeName.taken\": \"Dieser Name existiert bereits\",\n    \"error.model.fetch\": \"Beim Abruf von model config fetch ist ein Fehler aufgetreten.\",\n    \"error.record.create\": \"Beim Anlegen eines Dokuments ist ein Fehler aufgetreten.\",\n    \"error.record.delete\": \"Beim Löschen eines Dokuments ist ein Fehler aufgetreten.\",\n    \"error.record.fetch\": \"Beim Abruf eines Dokuments ist ein Fehler aufgetreten.\",\n    \"error.record.update\": \"Beim Aktualisieren eines Dokuments ist ein Fehler aufgetreten.\",\n    \"error.records.count\": \"Beim Abrufen der Anzahl an Einträgen ist ein Fehler aufgetreten.\",\n    \"error.records.fetch\": \"Beim Abrufen von Dokumenten ist ein Fehler aufgetreten.\",\n    \"error.schema.generation\": \"Bei der Generierung des Schemas ist ein Fehler aufgetreten.\",\n    \"error.validation.json\": \"Dies ist kein JSON\",\n    \"error.validation.max\": \"Dieser Wert ist zu hoch.\",\n    \"error.validation.maxLength\": \"Dieser Wert ist zu lang.\",\n    \"error.validation.min\": \"Dieser Wert ist zu niedrig.\",\n    \"error.validation.minLength\": \"Dieser Wert ist zu kurz.\",\n    \"error.validation.minSupMax\": \"Darf nicht höher sein\",\n    \"error.validation.regex\": \"Dieser Wert entspricht nicht dem RegEx.\",\n    \"error.validation.required\": \"Dieser Wert ist erforderlich.\",\n    \"form.Input.bulkActions\": \"Bulk-Bearbeitung aktivieren\",\n    \"form.Input.defaultSort\": \"Standard-Sortierattribut\",\n    \"form.Input.description\": \"Beschreibung\",\n    \"form.Input.description.placeholder\": \"Zeige den Namen im Profil\",\n    \"form.Input.editable\": \"Editierbares Feld\",\n    \"form.Input.filters\": \"Filter aktivieren\",\n    \"form.Input.label\": \"Label\",\n    \"form.Input.label.inputDescription\": \"Dieser Wert überschreibt das im Kopf der Tabelle angezeigte Label.\",\n    \"form.Input.pageEntries\": \"Einträge pro Seite\",\n    \"form.Input.pageEntries.inputDescription\": \"Hinweis: Dieser Wert lässt sich durch die Sammlungs-Einstellungen überschreiben.\",\n    \"form.Input.placeholder\": \"Platzhalter\",\n    \"form.Input.placeholder.placeholder\": \"Mein unglaublicher Wert\",\n    \"form.Input.search\": \"Suche aktivieren\",\n    \"form.Input.search.field\": \"Suche in diesem Feld aktivieren\",\n    \"form.Input.sort.field\": \"Sortierung in diesem Feld aktivieren\",\n    \"form.Input.sort.order\": \"Standard-Reihenfolge\",\n    \"form.Input.wysiwyg\": \"Als visuellen Editor anzeigen\",\n    \"global.displayedFields\": \"Angezeigte Felder\",\n    groups: groups,\n    \"groups.numbered\": \"Gruppen ({number})\",\n    \"header.name\": \"Inhalt\",\n    \"link-to-ctb\": \"Modell bearbeiten\",\n    models: models,\n    \"models.numbered\": \"Sammlungen ({number})\",\n    \"notification.error.displayedFields\": \"Du benötigst mindestens ein dargestelltes Feld\",\n    \"notification.error.relationship.fetch\": \"Beim Abruf von Beziehungen ist ein Fehler aufgetreten.\",\n    \"notification.info.SettingPage.disableSort\": \"Du musst ein Attribut mit aktivierter Sortierung haben.\",\n    \"notification.info.minimumFields\": \"Du benötigst mindestens ein dargestelltes Feld\",\n    \"notification.upload.error\": \"Beim Hochladen deiner Dateien ist ein Fehler aufgetreten\",\n    pageNotFound: pageNotFound,\n    \"pages.ListView.header-subtitle\": \"{number, plural, one {# Eintrag} other {# Einträge}} gefunden\",\n    \"pages.NoContentType.button\": \"Erstelle deinen ersten Inhalts-Typ\",\n    \"pages.NoContentType.text\": \"Wenn du noch keinen Inhalt hast, empfehlen wir dir, zuerst einen Inhalts-Typ erstellen.\",\n    \"permissions.not-allowed.create\": \"Du hast nicht die erforderlichen Berechtigungen, um ein Dokument zu erstellen\",\n    \"permissions.not-allowed.update\": \"Du hast nicht die erforderlichen Berechtigungen, um dieses Dokument anzuschauen\",\n    \"plugin.description.long\": \"Greife schnell auf alle Daten in der Datenbank zu und ändere sie.\",\n    \"plugin.description.short\": \"Greife schnell auf alle Daten in der Datenbank zu und ändere sie.\",\n    \"popover.display-relations.label\": \"Beziehungen darstellen\",\n    \"success.record.delete\": \"Gelöscht\",\n    \"success.record.publish\": \"Veröffentlicht\",\n    \"success.record.save\": \"Gespeichert\",\n    \"success.record.unpublish\": \"Veröffentlichung zurückgenommen\",\n    \"utils.data-loaded\": \"{number, plural, =1 {Der Eintrag wurde} other {Die Einträge wurden}} erfolgreich geladen\",\n    \"popUpWarning.warning.publish-question\": \"Wollst du diesen Eintrag trotzdem veröffentlichen?\",\n    \"popUpwarning.warning.has-draft-relations.button-confirm\": \"Ja, veröffentlichen\",\n    \"popUpwarning.warning.has-draft-relations.message\": \"<b>{count, plural, =0 { von deinen Inhalts-Beziehungen sind} one { von deinen Inhalts-Beziehungen ist} other { von deinen Inhalts-Beziehungen sind}}</b> noch nicht veröffentlicht.<br></br>Das kann zu kaputten Links und Fehlern in deinem Projekt führen.\"\n};\n\nexport { de as default, groups, models, pageNotFound };\n//# sourceMappingURL=de.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,SAAS;AACb,IAAI,SAAS;AACb,IAAI,eAAe;AACnB,IAAI,KAAK;AAAA,EACL,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,uCAAuC;AAAA,EACvC,UAAU;AAAA,EACV,+BAA+B;AAAA,EAC/B,gCAAgC;AAAA,EAChC,+BAA+B;AAAA,EAC/B,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,gDAAgD;AAAA,EAChD,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,wCAAwC;AAAA,EACxC,6CAA6C;AAAA,EAC7C,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,gCAAgC;AAAA,EAChC,yCAAyC;AAAA,EACzC,4DAA4D;AAAA,EAC5D,+DAA+D;AAAA,EAC/D,0DAA0D;AAAA,EAC1D,2DAA2D;AAAA,EAC3D,sCAAsC;AAAA,EACtC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,uCAAuC;AAAA,EACvC,mCAAmC;AAAA,EACnC,gDAAgD;AAAA,EAChD,iCAAiC;AAAA,EACjC,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,yEAAyE;AAAA,EACzE,yEAAyE;AAAA,EACzE,qDAAqD;AAAA,EACrD,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,gCAAgC;AAAA,EAChC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,uCAAuC;AAAA,EACvC,+BAA+B;AAAA,EAC/B,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,uCAAuC;AAAA,EACvC,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,6BAA6B;AAAA,EAC7B,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA,EAC9B,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,6BAA6B;AAAA,EAC7B,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,kCAAkC;AAAA,EAClC,uCAAuC;AAAA,EACvC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,yBAAyB;AAAA,EACzB,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,qDAAqD;AAAA,EACrD,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,gCAAgC;AAAA,EAChC,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,6CAA6C;AAAA,EAC7C,qDAAqD;AAAA,EACrD,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,+DAA+D;AAAA,EAC/D,kEAAkE;AAAA,EAClE,6CAA6C;AAAA,EAC7C,iCAAiC;AAAA,EACjC,mDAAmD;AAAA,EACnD,6CAA6C;AAAA,EAC7C,kDAAkD;AAAA,EAClD,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,kDAAkD;AAAA,EAClD,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,6DAA6D;AAAA,EAC7D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,yCAAyC;AAAA,EACzC,sCAAsC;AAAA,EACtC,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,0BAA0B;AAAA,EAC1B,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,qCAAqC;AAAA,EACrC,0BAA0B;AAAA,EAC1B,2CAA2C;AAAA,EAC3C,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B;AAAA,EACA,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,eAAe;AAAA,EACf;AAAA,EACA,mBAAmB;AAAA,EACnB,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B;AAAA,EACA,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,4BAA4B;AAAA,EAC5B,qBAAqB;AAAA,EACrB,yCAAyC;AAAA,EACzC,2DAA2D;AAAA,EAC3D,oDAAoD;AACxD;", "names": []}