{"version": 3, "sources": ["../../../@strapi/upload/dist/admin/translations/de.json.mjs"], "sourcesContent": ["var de = {\n    \"bulk.select.label\": \"Wähle alle Dateien aus\",\n    \"button.next\": \"<PERSON><PERSON>\",\n    \"checkControl.crop-duplicate\": \"Duplizieren & zuschneiden\",\n    \"checkControl.crop-original\": \"Orginal zuschneiden\",\n    \"content.isLoading\": \"Lade die Datei-Liste.\",\n    \"control-card.add\": \"Hinzufügen\",\n    \"control-card.cancel\": \"Abbrechen\",\n    \"control-card.copy-link\": \"Link kopieren\",\n    \"control-card.crop\": \"Zuschneiden\",\n    \"control-card.download\": \"Herunterladen\",\n    \"control-card.edit\": \"Bearbeiten\",\n    \"control-card.replace-media\": \"Datei ersetzen\",\n    \"control-card.save\": \"Speichern\",\n    \"control-card.stop-crop\": \"Zuschneiden abbrechen\",\n    \"filter.add\": \"Filter hinzufügen\",\n    \"form.button.replace-media\": \"Datei ersetzen\",\n    \"form.input.description.file-alt\": \"Dieser Text wird angezeigt, wenn das Datei nicht angezeigt werden kann.\",\n    \"form.input.label.file-alt\": \"Alternativtext\",\n    \"form.input.label.file-caption\": \"Bildtext\",\n    \"form.input.label.file-name\": \"Dateiname\",\n    \"form.upload-url.error.url.invalid\": \"Eine URL ist ungültig\",\n    \"form.upload-url.error.url.invalids\": \"{number} URLs sind ungültig\",\n    \"header.actions.add-assets\": \"Neue Dateien hinzufügen\",\n    \"header.actions.upload-assets\": \"Dateien hochladen\",\n    \"header.actions.upload-new-asset\": \"Neue Dateien hochladen\",\n    \"header.content.assets-empty\": \"Keine Dateien\",\n    \"header.content.assets\": \"{numberFolders} Ordner - {numberAssets, plural, one {1 Datei} other {# Dateien}}\",\n    \"input.button.label\": \"Dateien durchsuchen\",\n    \"input.label\": \"Dateien hierhin ziehen oder\",\n    \"input.label-bold\": \"Drag & Drop\",\n    \"input.label-normal\": \"zum Hochladen oder\",\n    \"input.placeholder\": \"Klicke hier, um eine Datei auszuwählen oder Drag & Drop eine Datei in diesen Bereich.\",\n    \"input.placeholder.icon\": \"Ziehe die Datei in diese Zone\",\n    \"input.url.description\": \"Eine URL pro Zeile.\",\n    \"input.url.label\": \"URL\",\n    \"list.asset.at.finished\": \"Die Datei-Liste wurde geladen.\",\n    \"list.assets-empty.search\": \"Keine Ergebnisse gefunden\",\n    \"list.assets-empty.subtitle\": \"Füge welche der Liste hinzu.\",\n    \"list.assets-empty.title\": \"Keine Dateien vorhanden.\",\n    \"list.assets-empty.title-withSearch\": \"Es existieren keine Dateien für die gewählten Filterkriterien.\",\n    \"list.assets.empty\": \"Lade deine ersten Dateien hoch...\",\n    \"list.assets.empty.no-permissions\": \"Die Datei-Liste ist leer.\",\n    \"list.assets.loading-asset\": \"Lade die Vorschau für Medien: {path}\",\n    \"list.assets.not-supported-content\": \"Keine Vorschau verfügbar\",\n    \"list.assets.preview-asset\": \"Vorschau für das Video unter {path}\",\n    \"list.assets.selected\": \"{number, plural, =0 {Keine Dateien} one {1 Datei} other {# Dateien}} bereits hochgeladen\",\n    \"list.assets.type-not-allowed\": \"Dieser Dateityp ist nicht zulässig.\",\n    \"mediaLibraryInput.actions.nextSlide\": \"Nächste Folie\",\n    \"mediaLibraryInput.actions.previousSlide\": \"Vorherige Folie\",\n    \"mediaLibraryInput.placeholder\": \"Klicke, um eine Datei hinzuzufügen oder ziehe eine in diesen Bereich\",\n    \"mediaLibraryInput.slideCount\": \"{n} von {m} Folien\",\n    \"modal.file-details.date\": \"Datum\",\n    \"modal.file-details.dimensions\": \"Abmessungen\",\n    \"modal.file-details.extension\": \"Dateityp\",\n    \"modal.file-details.size\": \"Größe\",\n    \"modal.header.browse\": \"Dateien hochladen\",\n    \"modal.header.file-detail\": \"Details\",\n    \"modal.header.pending-assets\": \"Ausstehende Dateien\",\n    \"modal.header.select-files\": \"Wähle Dateien\",\n    \"modal.nav.browse\": \"durchsuchen\",\n    \"modal.nav.computer\": \"vom Computer\",\n    \"modal.nav.selected\": \"ausgewählt\",\n    \"modal.nav.url\": \"von einer URL\",\n    \"modal.remove.success-label\": \"Die Datei wurde erfolgreich entfernt.\",\n    \"modal.selected-list.sub-header-subtitle\": \"Drag & Drop, um die Dateien in diesem Bereich anzuordnen.\",\n    \"modal.upload-list.footer.button\": \"Lade {number, plural, one {# Datei} other {# Dateien}} in die Bibliothek\",\n    \"modal.upload-list.sub-header-subtitle\": \"Dateien bearbeiten vor dem Hochladen in die Bibliothek\",\n    \"modal.upload-list.sub-header.button\": \"Weitere Dateien hinzufügen\",\n    \"modal.upload.cancelled\": \"Upload manuell abgebrochen.\",\n    \"page.title\": \"Einstellungen - Medienbibliothek\",\n    \"permissions.not-allowed.update\": \"Du hast nicht die nötige Berechtigung, diese Datei zu bearbeiten.\",\n    \"plugin.description.long\": \"Medien-Dateiverwaltung.\",\n    \"plugin.description.short\": \"Dateiverwaltung.\",\n    \"plugin.name\": \"Medienbibliothek\",\n    \"search.clear.label\": \"Suchbegriff löschen\",\n    \"search.label\": \"Nach einer Datei suchen\",\n    \"search.placeholder\": \"Bsp: Der erste Hund auf dem Mond\",\n    \"settings.blockTitle\": \"Datei-Verwaltung\",\n    \"settings.form.autoOrientation.description\": \"Automatisch Bilder nach EXIF Richtungstag orientieren\",\n    \"settings.form.autoOrientation.label\": \"Automatische Bildorientierung aktivieren\",\n    \"settings.form.responsiveDimensions.description\": \"Generiere automatisch mehrere Formate (klein, mittel, groß) der hochgeladenen Datei.\",\n    \"settings.form.responsiveDimensions.label\": \"Automatische Formaterstellung aktivieren.\",\n    \"settings.form.sizeOptimization.description\": \"Diese Einstellung wird die Größe der Bilder reduzieren, dafür aber auch leicht die Bildqualität verringern.\",\n    \"settings.form.sizeOptimization.label\": \"Größenoptimierung\",\n    \"settings.form.videoPreview.description\": \"Generiere eine sechs Sekunden lange Vorschau (GIF) des Videos.\",\n    \"settings.form.videoPreview.label\": \"Vorschau\",\n    \"settings.header.label\": \"Medienbibliothek\",\n    \"settings.section.doc.label\": \"Dok\",\n    \"settings.section.image.label\": \"Bild\",\n    \"settings.section.video.label\": \"Video\",\n    \"settings.sub-header.label\": \"Einstellungen für die Medienbibliothek vornehmen\",\n    \"sort.created_at_asc\": \"Älteste Uploads\",\n    \"sort.created_at_desc\": \"Neuste Uploads\",\n    \"sort.label\": \"Sortieren nach\",\n    \"sort.name_asc\": \"Alphabetisch absteigend (A bis Z)\",\n    \"sort.name_desc\": \"Alphabetisch aufsteigend (Z bis A)\",\n    \"sort.updated_at_asc\": \"Älteste Änderungen\",\n    \"sort.updated_at_desc\": \"Neuste Änderungen\",\n    \"tabs.title\": \"Wie willst du deine Dateien hochladen?\",\n    \"window.confirm.close-modal.file\": \"Bist du sicher? Deine Änderungen gehen verloren.\",\n    \"window.confirm.close-modal.files\": \"Bist du sicher? Es gibt ein paar Dateien, welche noch nicht hochgeladen wurden.\"\n};\n\nexport { de as default };\n//# sourceMappingURL=de.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,KAAK;AAAA,EACL,qBAAqB;AAAA,EACrB,eAAe;AAAA,EACf,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,qBAAqB;AAAA,EACrB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,qBAAqB;AAAA,EACrB,8BAA8B;AAAA,EAC9B,qBAAqB;AAAA,EACrB,0BAA0B;AAAA,EAC1B,cAAc;AAAA,EACd,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,qBAAqB;AAAA,EACrB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,mBAAmB;AAAA,EACnB,0BAA0B;AAAA,EAC1B,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,sCAAsC;AAAA,EACtC,qBAAqB;AAAA,EACrB,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,qCAAqC;AAAA,EACrC,6BAA6B;AAAA,EAC7B,wBAAwB;AAAA,EACxB,gCAAgC;AAAA,EAChC,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,iCAAiC;AAAA,EACjC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,6BAA6B;AAAA,EAC7B,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,iBAAiB;AAAA,EACjB,8BAA8B;AAAA,EAC9B,2CAA2C;AAAA,EAC3C,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,0BAA0B;AAAA,EAC1B,cAAc;AAAA,EACd,kCAAkC;AAAA,EAClC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,eAAe;AAAA,EACf,sBAAsB;AAAA,EACtB,gBAAgB;AAAA,EAChB,sBAAsB;AAAA,EACtB,uBAAuB;AAAA,EACvB,6CAA6C;AAAA,EAC7C,uCAAuC;AAAA,EACvC,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,8CAA8C;AAAA,EAC9C,wCAAwC;AAAA,EACxC,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,yBAAyB;AAAA,EACzB,8BAA8B;AAAA,EAC9B,gCAAgC;AAAA,EAChC,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,cAAc;AAAA,EACd,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,cAAc;AAAA,EACd,mCAAmC;AAAA,EACnC,oCAAoC;AACxC;", "names": []}