{"version": 3, "sources": ["../../../@strapi/content-type-builder/dist/admin/translations/dk.json.mjs"], "sourcesContent": ["var configurations = \"konfigurationer\";\nvar from = \"fra\";\nvar dk = {\n    \"attribute.boolean\": \"Boolean\",\n    \"attribute.boolean.description\": \"<PERSON>a eller nej, 1 eller 0, sand eller falsk\",\n    \"attribute.component\": \"Komponent\",\n    \"attribute.component.description\": \"Gruppe af felter som kan gentages\",\n    \"attribute.date\": \"Dato\",\n    \"attribute.date.description\": \"En datovælger med timer, minutter og sekunder\",\n    \"attribute.datetime\": \"Dato - tid\",\n    \"attribute.dynamiczone\": \"Dynamisk zone\",\n    \"attribute.dynamiczone.description\": \"Dynamisk udvælg komponenter ved redigering af indhold\",\n    \"attribute.email\": \"E-mail\",\n    \"attribute.email.description\": \"E-mail felt med valideringsformat\",\n    \"attribute.enumeration\": \"Enumeration\",\n    \"attribute.enumeration.description\": \"Liste med værdier, vælg én\",\n    \"attribute.json\": \"JSON\",\n    \"attribute.json.description\": \"Data i JSON format\",\n    \"attribute.media\": \"Medie\",\n    \"attribute.media.description\": \"Filer som billeder, videoer, osv...\",\n    \"attribute.null\": \" \",\n    \"attribute.number\": \"Tal\",\n    \"attribute.number.description\": \"Tal (hele tal, kommatal)\",\n    \"attribute.password\": \"Kodeord\",\n    \"attribute.password.description\": \"Kodeord med kryptering\",\n    \"attribute.relation\": \"Relation\",\n    \"attribute.relation.description\": \"Reference til en dokument type\",\n    \"attribute.richtext\": \"RTE\",\n    \"attribute.richtext.description\": \"En rich-text-editor med formateringsmuligheder\",\n    \"attribute.text\": \"Tekst\",\n    \"attribute.text.description\": \"Korte eller lange tekster som titel eller beskrivelse\",\n    \"attribute.time\": \"Tid\",\n    \"attribute.timestamp\": \"Tidsstempel\",\n    \"attribute.uid\": \"UID\",\n    \"attribute.uid.description\": \"Unikt ID\",\n    \"button.attributes.add.another\": \"Tilføj et felt\",\n    \"button.component.add\": \"Tilføj et komponent\",\n    \"button.component.create\": \"Opret komponent\",\n    \"button.model.create\": \"Opret dokument type\",\n    \"button.single-types.create\": \"Opret enkelt type\",\n    \"component.repeatable\": \"(gentageligt)\",\n    \"components.SelectComponents.displayed-value\": \"{number, plural, =0 {# komponenter} one {# komponent} other {# komponenter}} valgt\",\n    \"components.componentSelect.no-component-available\": \"Du har allerede tilføjet alle dine komponenter\",\n    \"components.componentSelect.no-component-available.with-search\": \"Der er ingen komponenter som matcher din søgning\",\n    \"components.componentSelect.value-component\": \"{number} komponent valgt (skriv for at søge efter komponenter)\",\n    \"components.componentSelect.value-components\": \"{number} komponenter valgt\",\n    configurations: configurations,\n    \"contentType.apiId-plural.description\": \"API ID i flertal\",\n    \"contentType.apiId-plural.label\": \"API ID (flertal)\",\n    \"contentType.apiId-singular.description\": \"UID bliver brugt til at generere API routes og database tabeller/kollektion\",\n    \"contentType.apiId-singular.label\": \"API ID (ental)\",\n    \"contentType.collectionName.description\": \"Brugbar når navnet på din indholdstype og dit tabel navn er forskellige\",\n    \"contentType.collectionName.label\": \"Dokument navn\",\n    \"contentType.displayName.label\": \"Visningsnavn\",\n    \"contentType.kind.change.warning\": \"Du har lige ændret typen af en indholdstype: API bliver nustillet (ruter, controllere, og services bliver overskrevet).\",\n    \"error.attributeName.reserved-name\": \"Dette navn kan ikke bruges i din indholdstype, da det måske kan ødelægge andre funktioner\",\n    \"error.contentType.pluralName-used\": \"Denne værdi kan ikke være den samme som ved ental\",\n    \"error.contentType.singularName-used\": \"Denne værdi kan ikke være den samme som ved flertal\",\n    \"error.contentTypeName.reserved-name\": \"Dette navn kan ikke bruges i dit projekt, da det måske kan ødelægge andre funktioner\",\n    \"error.validation.enum-duplicate\": \"Duplikat værdi er ikke tilladt\",\n    \"error.validation.enum-empty-string\": \"Tomme strenge er ikke tilladt\",\n    \"error.validation.minSupMax\": \"Må ikke overstige\",\n    \"error.validation.positive\": \"Skal være et positivt tal\",\n    \"error.validation.regex\": \"Regex mønster er ikke gyldig\",\n    \"error.validation.relation.targetAttribute-taken\": \"Dette navn eksisterer allerede\",\n    \"form.attribute.component.option.add\": \"Tilføj et komponent\",\n    \"form.attribute.component.option.create\": \"Opret et komponent\",\n    \"form.attribute.component.option.create.description\": \"Et komponent er delt mellem typer og komponenter, det bliver tilgængeligt fra alle steder.\",\n    \"form.attribute.component.option.repeatable\": \"Gentageligt komponent\",\n    \"form.attribute.component.option.repeatable.description\": \"Bedst til gentagne værdier af f.eks. ingredienser, meta tags, osv...\",\n    \"form.attribute.component.option.reuse-existing\": \"Brug et eksisterende komponent\",\n    \"form.attribute.component.option.reuse-existing.description\": \"Genbrug en komponent der allerede er oprettet for at holde din data konsistent gennem indholdstyper.\",\n    \"form.attribute.component.option.single\": \"Enkelt komponent\",\n    \"form.attribute.component.option.single.description\": \"Bedst til at gruppere felter som fulde adresse, primær information osv...\",\n    \"form.attribute.item.customColumnName\": \"Specielle kolonne navne\",\n    \"form.attribute.item.customColumnName.description\": \"Dette er brugbart til at omdøbe database kolonne navne i et mere omfattende format til API svar\",\n    \"form.attribute.item.defineRelation.fieldName\": \"Felt navn\",\n    \"form.attribute.item.enumeration.graphql\": \"Navn overskrivning til GraphQL\",\n    \"form.attribute.item.enumeration.graphql.description\": \"Tillader dig at overskrive standard genereret navn til GraphQL\",\n    \"form.attribute.item.enumeration.placeholder\": \"F.eks.\\nmorgen\\nmiddag\\naften\",\n    \"form.attribute.item.enumeration.rules\": \"Værdier (én linje pr. værdi)\",\n    \"form.attribute.item.maximum\": \"Maks værdi\",\n    \"form.attribute.item.maximumLength\": \"Maks længde\",\n    \"form.attribute.item.minimum\": \"Minimum værdi\",\n    \"form.attribute.item.minimumLength\": \"Minimum længde\",\n    \"form.attribute.item.number.type\": \"Tal format\",\n    \"form.attribute.item.number.type.biginteger\": \"stort helt tal (f.eks. 123456789)\",\n    \"form.attribute.item.number.type.decimal\": \"decimal (f.eks. 2.22)\",\n    \"form.attribute.item.number.type.float\": \"float (f.eks. 3.33333333)\",\n    \"form.attribute.item.number.type.integer\": \"helt tal (f.eks. 10)\",\n    \"form.attribute.item.privateField\": \"Privat felt\",\n    \"form.attribute.item.privateField.description\": \"Dette flet bliver ikke vist i API svar\",\n    \"form.attribute.item.requiredField\": \"Påkrævet felt\",\n    \"form.attribute.item.requiredField.description\": \"Du kan ikke oprette et element hvis dette felt er tomt\",\n    \"form.attribute.item.text.regex\": \"RegExp mønster\",\n    \"form.attribute.item.text.regex.description\": \"Teksten til regular expression\",\n    \"form.attribute.item.uniqueField\": \"Unikt felt\",\n    \"form.attribute.item.uniqueField.description\": \"Du kan ikke oprette et element hvis der allerede findes et element med samme indhold\",\n    \"form.attribute.media.allowed-types\": \"Vælg tillade typer af medier\",\n    \"form.attribute.media.allowed-types.option-files\": \"Filer\",\n    \"form.attribute.media.allowed-types.option-images\": \"Billeder\",\n    \"form.attribute.media.allowed-types.option-videos\": \"Videoer\",\n    \"form.attribute.media.option.multiple\": \"Flere medier\",\n    \"form.attribute.media.option.multiple.description\": \"Best til gallerier, slideshows eller download af flere filer\",\n    \"form.attribute.media.option.single\": \"Enkelt medie\",\n    \"form.attribute.media.option.single.description\": \"Best til avatar, profil billeder eller cover\",\n    \"form.attribute.settings.default\": \"Standard værdi\",\n    \"form.attribute.text.option.long-text\": \"Lang tekst\",\n    \"form.attribute.text.option.long-text.description\": \"Bedst til beskrivelser, biografier. Præcis søgning er deaktiveret.\",\n    \"form.attribute.text.option.short-text\": \"Kort tekst\",\n    \"form.attribute.text.option.short-text.description\": \"Bedst til titler, navne, links (URL). Præcis søgning er aktiveret.\",\n    \"form.button.add-components-to-dynamiczone\": \"Tilføj komponenter til zonen\",\n    \"form.button.add-field\": \"Tilføj endnu et felt\",\n    \"form.button.add-first-field-to-created-component\": \"Tilføj det første felt til komponentet\",\n    \"form.button.add.field.to.collectionType\": \"Tilføj endnu et felt til dokument typen\",\n    \"form.button.add.field.to.component\": \"Tilføj endnu et felt til komponentet\",\n    \"form.button.add.field.to.contentType\": \"Tilføj endnu et felt til dokument typen\",\n    \"form.button.add.field.to.singleType\": \"Tilføj endnu et felt til enkelt typen\",\n    \"form.button.cancel\": \"Annuller\",\n    \"form.button.collection-type.description\": \"Bedst til Best for flere forkomster som artikler, produkter, kommentarer osv...\",\n    \"form.button.configure-component\": \"Konfigurér komponentet\",\n    \"form.button.configure-view\": \"Konfigurér visning\",\n    \"form.button.select-component\": \"Vælg et komponent\",\n    \"form.button.single-type.description\": \"Bedst til enkelte forekomster som \\\"om os\\\", forside osv...\",\n    from: from,\n    \"modalForm.attribute.form.base.name.description\": \"Mellemrum er ikke tilladt i navnet\",\n    \"modalForm.attribute.form.base.name.placeholder\": \"f.eks. slug, seoUrl, canonicalUrl\",\n    \"modalForm.attribute.target-field\": \"Vedhæftet felt\",\n    \"modalForm.attributes.select-component\": \"Vælg et komponent\",\n    \"modalForm.attributes.select-components\": \"Vælg komponenterne\",\n    \"modalForm.collectionType.header-create\": \"Opret en dokumenttype\",\n    \"modalForm.component.header-create\": \"Opret et komponent\",\n    \"modalForm.components.create-component.category.label\": \"Vælg en kategori eller intast et navn for at oprette en ny\",\n    \"modalForm.components.icon.label\": \"Ikon\",\n    \"modalForm.editCategory.base.name.description\": \"Mellemrum er ikke tilladt i kategori navnet\",\n    \"modalForm.header-edit\": \"Redigér {name}\",\n    \"modalForm.header.categories\": \"Kategorier\",\n    \"modalForm.header.back\": \"Tilbage\",\n    \"modalForm.singleType.header-create\": \"Opret en enkelt type\",\n    \"modalForm.sub-header.addComponentToDynamicZone\": \"Tilføj komponent til den dymaiske zone\",\n    \"modalForm.sub-header.attribute.create\": \"Tilføj {type} felt\",\n    \"modalForm.sub-header.attribute.create.step\": \"Tilføj komponent ({step}/2)\",\n    \"modalForm.sub-header.attribute.edit\": \"Redigér {name}\",\n    \"modalForm.sub-header.chooseAttribute.collectionType\": \"Vælg et felt til din dokument type\",\n    \"modalForm.sub-header.chooseAttribute.component\": \"Vælg et felt til dit komponent\",\n    \"modalForm.sub-header.chooseAttribute.singleType\": \"Vælg et felt til din enkelt type\",\n    \"modelPage.attribute.relation-polymorphic\": \"Relation (polymorphic)\",\n    \"modelPage.attribute.relationWith\": \"Relation med\",\n    \"notification.info.autoreaload-disable\": \"AutoReload funktionen er påkrævet for at bruge dette plugin. Start din server med `strapi develop`\",\n    \"notification.info.creating.notSaved\": \"Gem venligst dit arbejde inden du opretter en dokument type eller komponent\",\n    \"plugin.description.long\": \"Modellér data strukturen i dit API. Opret felter og relationer på få minutter. Filerne bliver automatisk oprettet og opdateret i dit projekt.\",\n    \"plugin.description.short\": \"Modellér data strukturen i dit API.\",\n    \"plugin.name\": \"Dokumenttype bygger\",\n    \"popUpForm.navContainer.advanced\": \"Avancerede indstillinger\",\n    \"popUpForm.navContainer.base\": \"Standard indstillinger\",\n    \"popUpWarning.bodyMessage.cancel-modifications\": \"Er du sikker på at du vil annullere dine ændringer?\",\n    \"popUpWarning.bodyMessage.cancel-modifications.with-components\": \"Er du sikker på at du vil annullere dine ændringer? Nogle komponenter er blevet oprettet eller redigeret...\",\n    \"popUpWarning.bodyMessage.category.delete\": \"Er du sikker på at du vil slette denne kategori? Alle komponenterne bliver også slettet.\",\n    \"popUpWarning.bodyMessage.component.delete\": \"Er du sikker på at du vil slette denne komponent?\",\n    \"popUpWarning.bodyMessage.contentType.delete\": \"Er du sikker på at du vil slette denne dokument type?\",\n    \"popUpWarning.draft-publish.button.confirm\": \"Ja, deaktivér\",\n    \"popUpWarning.draft-publish.message\": \"Hvis du deaktiverer udkast/offentliggør systemet, bliver dine udkast slettet.\",\n    \"popUpWarning.draft-publish.second-message\": \"Er du sikker på at du vil deaktivere det?\",\n    \"prompt.unsaved\": \"Er du sikker på at du vil stoppe? Alle dine ændringer går tabt.\",\n    \"relation.attributeName.placeholder\": \"F.eks. forfatter, kategori, tag\",\n    \"relation.manyToMany\": \"har og tilhører flere\",\n    \"relation.manyToOne\": \"har flere\",\n    \"relation.manyWay\": \"har flere\",\n    \"relation.oneToMany\": \"tilhører flere\",\n    \"relation.oneToOne\": \"har og tilhører én\",\n    \"relation.oneWay\": \"har én\",\n    \"table.button.no-fields\": \"Tilføj nyt felt\",\n    \"table.content.create-first-content-type\": \"Opret din først dokumenttype\",\n    \"table.content.no-fields.collection-type\": \"Tilføj dit første felt til denne dokumenttype\",\n    \"table.content.no-fields.component\": \"Tilføj dit første felt til dette komponent\"\n};\n\nexport { configurations, dk as default, from };\n//# sourceMappingURL=dk.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,iBAAiB;AACrB,IAAI,OAAO;AACX,IAAI,KAAK;AAAA,EACL,qBAAqB;AAAA,EACrB,iCAAiC;AAAA,EACjC,uBAAuB;AAAA,EACvB,mCAAmC;AAAA,EACnC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,mBAAmB;AAAA,EACnB,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,mBAAmB;AAAA,EACnB,+BAA+B;AAAA,EAC/B,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,gCAAgC;AAAA,EAChC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,wBAAwB;AAAA,EACxB,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,+CAA+C;AAAA,EAC/C,qDAAqD;AAAA,EACrD,iEAAiE;AAAA,EACjE,8CAA8C;AAAA,EAC9C,+CAA+C;AAAA,EAC/C;AAAA,EACA,wCAAwC;AAAA,EACxC,kCAAkC;AAAA,EAClC,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,8BAA8B;AAAA,EAC9B,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,mDAAmD;AAAA,EACnD,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,sDAAsD;AAAA,EACtD,8CAA8C;AAAA,EAC9C,0DAA0D;AAAA,EAC1D,kDAAkD;AAAA,EAClD,8DAA8D;AAAA,EAC9D,0CAA0C;AAAA,EAC1C,sDAAsD;AAAA,EACtD,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,gDAAgD;AAAA,EAChD,2CAA2C;AAAA,EAC3C,uDAAuD;AAAA,EACvD,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+BAA+B;AAAA,EAC/B,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,8CAA8C;AAAA,EAC9C,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,oCAAoC;AAAA,EACpC,gDAAgD;AAAA,EAChD,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,kCAAkC;AAAA,EAClC,8CAA8C;AAAA,EAC9C,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,sCAAsC;AAAA,EACtC,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,sCAAsC;AAAA,EACtC,kDAAkD;AAAA,EAClD,mCAAmC;AAAA,EACnC,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,6CAA6C;AAAA,EAC7C,yBAAyB;AAAA,EACzB,oDAAoD;AAAA,EACpD,2CAA2C;AAAA,EAC3C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,sBAAsB;AAAA,EACtB,2CAA2C;AAAA,EAC3C,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,gCAAgC;AAAA,EAChC,uCAAuC;AAAA,EACvC;AAAA,EACA,kDAAkD;AAAA,EAClD,kDAAkD;AAAA,EAClD,oCAAoC;AAAA,EACpC,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,qCAAqC;AAAA,EACrC,wDAAwD;AAAA,EACxD,mCAAmC;AAAA,EACnC,gDAAgD;AAAA,EAChD,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,sCAAsC;AAAA,EACtC,kDAAkD;AAAA,EAClD,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,mDAAmD;AAAA,EACnD,4CAA4C;AAAA,EAC5C,oCAAoC;AAAA,EACpC,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,eAAe;AAAA,EACf,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,iDAAiD;AAAA,EACjD,iEAAiE;AAAA,EACjE,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,+CAA+C;AAAA,EAC/C,6CAA6C;AAAA,EAC7C,sCAAsC;AAAA,EACtC,6CAA6C;AAAA,EAC7C,kBAAkB;AAAA,EAClB,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,0BAA0B;AAAA,EAC1B,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,qCAAqC;AACzC;", "names": []}