{"version": 3, "sources": ["../../../@strapi/admin/dist/admin/admin/src/translations/dk.json.mjs"], "sourcesContent": ["var Analytics = \"Statistik\";\nvar Documentation = \"Dokumentation\";\nvar Email = \"E-mail\";\nvar Password = \"Kodeord\";\nvar Provider = \"Provider\";\nvar ResetPasswordToken = \"Nulstil kodeord token\";\nvar Role = \"Rolle\";\nvar Username = \"Brugernavn\";\nvar Users = \"Brugere\";\nvar anErrorOccurred = \"Øv! Noget gik galt. Prøv venligst igen.\";\nvar clearLabel = \"Ryd\";\nvar or = \"ELLER\";\nvar skipToContent = \"Gå til indhold\";\nvar submit = \"Indsend\";\nvar dk = {\n    Analytics: Analytics,\n    \"Auth.components.Oops.text\": \"Din konto er blevet suspenderet\",\n    \"Auth.components.Oops.text.admin\": \"<PERSON>vis dette er en fejl, kontakt venligst din administrator.\",\n    \"Auth.components.Oops.title\": \"Åh nej...\",\n    \"Auth.form.button.forgot-password\": \"Send e-mail\",\n    \"Auth.form.button.go-home\": \"GÅ TILBAGE TIL HJEM\",\n    \"Auth.form.button.login\": \"Log ind\",\n    \"Auth.form.button.login.providers.error\": \"Vi kan ikke forbinde dig gennem den valgte provider.\",\n    \"Auth.form.button.login.strapi\": \"Log ind med Strapi\",\n    \"Auth.form.button.password-recovery\": \"Gendan kodeord\",\n    \"Auth.form.button.register\": \"KOM I GANG\",\n    \"Auth.form.confirmPassword.label\": \"Bekræft kodeord\",\n    \"Auth.form.currentPassword.label\": \"Nuværende kodeord\",\n    \"Auth.form.email.label\": \"E-mail\",\n    \"Auth.form.email.placeholder\": \"<EMAIL>\",\n    \"Auth.form.error.blocked\": \"Din konto er blevet blokeret af administratoren.\",\n    \"Auth.form.error.code.provide\": \"Forkert kode angivet.\",\n    \"Auth.form.error.confirmed\": \"Din e-mail er ikke bekræftet.\",\n    \"Auth.form.error.email.invalid\": \"Denne e-mail er forkert.\",\n    \"Auth.form.error.email.provide\": \"Angiv venligst dit brugernavn eller e-mail.\",\n    \"Auth.form.error.email.taken\": \"E-mailen er allerede taget.\",\n    \"Auth.form.error.invalid\": \"Brugernavn eller kodeord er ikke gyldig.\",\n    \"Auth.form.error.params.provide\": \"Forkerte parametre angivet.\",\n    \"Auth.form.error.password.format\": \"Dit kodeord må ikke indeholde tegnet `$` mere end 3 gange.\",\n    \"Auth.form.error.password.local\": \"Denne bruger har ikke angivet et lokalt kodeord, log venligst ind via link fra oprettelsen.\",\n    \"Auth.form.error.password.matching\": \"Kodeordene er ikke ens.\",\n    \"Auth.form.error.password.provide\": \"Angiv venligst dit kodeord.\",\n    \"Auth.form.error.ratelimit\": \"For mange forsøg, prøv igen om lidt.\",\n    \"Auth.form.error.user.not-exist\": \"Der findes ingen bruger med denne e-mail.\",\n    \"Auth.form.error.username.taken\": \"Brugernavnet er allerede taget.\",\n    \"Auth.form.firstname.label\": \"Fornavn\",\n    \"Auth.form.firstname.placeholder\": \"John\",\n    \"Auth.form.forgot-password.email.label\": \"Indtast din e-mail\",\n    \"Auth.form.forgot-password.email.label.success\": \"E-mail sendt til\",\n    \"Auth.form.lastname.label\": \"Efternavn\",\n    \"Auth.form.lastname.placeholder\": \"Doe\",\n    \"Auth.form.password.hide-password\": \"Skjul kodeord\",\n    \"Auth.form.password.hint\": \"Kodeord skal indeholde mindst 8 tegn, 1 stort bogstav, 1 lille bogstav og 1 tal\",\n    \"Auth.form.password.show-password\": \"Vis kodeord\",\n    \"Auth.form.register.news.label\": \"Hold mig opdateret omkring nye features og kommende forbedringer (ved at gøre dette accepterer du {terms} og {policy}).\",\n    \"Auth.form.register.subtitle\": \"Dit login bliver kun brugt til at autentificere dig selv i admin panelet. Alle gemte data bliver gemt i din egen database.\",\n    \"Auth.form.rememberMe.label\": \"Husk mig\",\n    \"Auth.form.username.label\": \"Brugernavn\",\n    \"Auth.form.username.placeholder\": \"John Doe\",\n    \"Auth.form.welcome.subtitle\": \"Log ind med din Strapi bruger\",\n    \"Auth.form.welcome.title\": \"Velkommen!\",\n    \"Auth.link.forgot-password\": \"Glemt dit kodeord?\",\n    \"Auth.link.ready\": \"Klar til at logge ind?\",\n    \"Auth.link.signin\": \"Log ind\",\n    \"Auth.link.signin.account\": \"Har du allerede en konto?\",\n    \"Auth.login.sso.divider\": \"Eller log ind med\",\n    \"Auth.login.sso.loading\": \"Henter providere...\",\n    \"Auth.login.sso.subtitle\": \"Log ind med SSO\",\n    \"Auth.privacy-policy-agreement.policy\": \"Priviatpolitik\",\n    \"Auth.privacy-policy-agreement.terms\": \"vilkår\",\n    \"Content Manager\": \"Indhold\",\n    \"Content Type Builder\": \"Indholdstyper\",\n    Documentation: Documentation,\n    Email: Email,\n    \"Files Upload\": \"Fil upload\",\n    \"HomePage.head.title\": \"Hjem\",\n    \"HomePage.roadmap\": \"Se vores roadmap\",\n    \"HomePage.welcome.congrats\": \"Tillykke!\",\n    \"HomePage.welcome.congrats.content\": \"Du er logget ind som den første administrator. For at lære de gode features som Strapi har,\",\n    \"HomePage.welcome.congrats.content.bold\": \"anbefaler vi at du opretter din første dokument type.\",\n    \"Media Library\": \"Medie bibliotek\",\n    \"New entry\": \"Nyt element\",\n    Password: Password,\n    Provider: Provider,\n    ResetPasswordToken: ResetPasswordToken,\n    Role: Role,\n    \"Roles & Permissions\": \"Roller og rettigheder\",\n    \"Roles.ListPage.notification.delete-all-not-allowed\": \"Nogle roller kunne ikke slettes da de er forbundet til en eller flere brugere\",\n    \"Roles.ListPage.notification.delete-not-allowed\": \"En rolle kan ikke slettes hvis den er forbundet til en bruger\",\n    \"Roles.RoleRow.select-all\": \"Vælg {name} for bulk handling\",\n    \"Roles.components.List.empty.withSearch\": \"Der er ingen rolle der matcher søgningen ({search})...\",\n    \"Settings.PageTitle\": \"Indstillinger - {name}\",\n    \"Settings.apiTokens.addFirstToken\": \"Tilføj dit første API token\",\n    \"Settings.apiTokens.addNewToken\": \"Tilføj nyt API token\",\n    \"Settings.tokens.copy.editMessage\": \"Af sikkerhedsmæssige årsager, kan du kun se dit token én gang.\",\n    \"Settings.tokens.copy.editTitle\": \"Dette token er ikke længere tilgængeligt.\",\n    \"Settings.tokens.copy.lastWarning\": \"Kopiér dette token, du kommer ikke til at se det igen!\",\n    \"Settings.apiTokens.create\": \"Tilføj element\",\n    \"Settings.apiTokens.description\": \"Liste over genererede tokens til at bruge API'et\",\n    \"Settings.apiTokens.emptyStateLayout\": \"Du har endnu ikke noget indhold...\",\n    \"Settings.tokens.notification.copied\": \"Token er kopieret til klippebordet.\",\n    \"Settings.apiTokens.title\": \"API Tokens\",\n    \"Settings.tokens.types.full-access\": \"Fuld adgang\",\n    \"Settings.tokens.types.read-only\": \"Read-only\",\n    \"Settings.application.description\": \"Se dit projekts detaljer\",\n    \"Settings.application.edition-title\": \"NUVÆRENDE udgave\",\n    \"Settings.application.get-help\": \"Få hjælp\",\n    \"Settings.application.link-pricing\": \"Se alle priser\",\n    \"Settings.application.link-upgrade\": \"Opgrader dit projekt\",\n    \"Settings.application.node-version\": \"NODE VERSION\",\n    \"Settings.application.strapi-version\": \"STRAPI VERSION\",\n    \"Settings.application.strapiVersion\": \"strapi version\",\n    \"Settings.application.title\": \"Applikation\",\n    \"Settings.error\": \"Fejl\",\n    \"Settings.global\": \"Globale indstillinger\",\n    \"Settings.permissions\": \"Rettigheder\",\n    \"Settings.permissions.category\": \"Rettighedsindstillinger for {category}\",\n    \"Settings.permissions.category.plugins\": \"Rettighedsindstilling for {category} plugin\",\n    \"Settings.permissions.conditions.anytime\": \"Altid\",\n    \"Settings.permissions.conditions.apply\": \"Godkend\",\n    \"Settings.permissions.conditions.can\": \"Kan\",\n    \"Settings.permissions.conditions.conditions\": \"Definér betingelser\",\n    \"Settings.permissions.conditions.links\": \"Links\",\n    \"Settings.permissions.conditions.no-actions\": \"Der er ingen handling\",\n    \"Settings.permissions.conditions.none-selected\": \"Når som helst\",\n    \"Settings.permissions.conditions.or\": \"ELLER\",\n    \"Settings.permissions.conditions.when\": \"Når\",\n    \"Settings.permissions.select-all-by-permission\": \"Vælg alle {label} tilladelser\",\n    \"Settings.permissions.select-by-permission\": \"Vælg {label} tilladelse\",\n    \"Settings.permissions.users.create\": \"Opret ny bruger\",\n    \"Settings.permissions.users.email\": \"E-mail\",\n    \"Settings.permissions.users.firstname\": \"Fornavn\",\n    \"Settings.permissions.users.lastname\": \"Efternavn\",\n    \"Settings.permissions.users.form.sso\": \"Forbind med SSO\",\n    \"Settings.permissions.users.form.sso.description\": \"Når aktiveret (TIL), kan brugere logge ind med SSO\",\n    \"Settings.permissions.users.listview.header.subtitle\": \"Alle brugere som har adgnag til Strapi admin panelet\",\n    \"Settings.permissions.users.tabs.label\": \"Tabs Tilladelser\",\n    \"Settings.profile.form.notify.data.loaded\": \"Dine profildata er blevet hentet\",\n    \"Settings.profile.form.section.experience.clear.select\": \"Nulstil det valgte interface sprog\",\n    \"Settings.profile.form.section.experience.here\": \"dokumentation\",\n    \"Settings.profile.form.section.experience.interfaceLanguage\": \"Interface sprog\",\n    \"Settings.profile.form.section.experience.interfaceLanguage.hint\": \"Dette vil kun vise dit eget interface i det valgte sprog.\",\n    \"Settings.profile.form.section.experience.interfaceLanguageHelp\": \"Valget vil kun ændre sproget for dig. Referér venligst til dette {here} for at gøre andre sprog tilgængelige for dit hold.\",\n    \"Settings.profile.form.section.experience.title\": \"Oplevelse\",\n    \"Settings.profile.form.section.head.title\": \"Bruger profil\",\n    \"Settings.profile.form.section.profile.page.title\": \"Profil side\",\n    \"Settings.roles.create.description\": \"Definér rollens rettigheder\",\n    \"Settings.roles.create.title\": \"Opret en rolle\",\n    \"Settings.roles.created\": \"Rolle oprettet\",\n    \"Settings.roles.edit.title\": \"Redigér en rolle\",\n    \"Settings.roles.form.button.users-with-role\": \"Brugere med denne rolle\",\n    \"Settings.roles.form.created\": \"Oprettet\",\n    \"Settings.roles.form.description\": \"Navn og beskrivelse af rollen\",\n    \"Settings.roles.form.permission.property-label\": \"{label} tilladelser\",\n    \"Settings.roles.form.permissions.attributesPermissions\": \"Feltrettigheder\",\n    \"Settings.roles.form.permissions.create\": \"Opret\",\n    \"Settings.roles.form.permissions.delete\": \"Slet\",\n    \"Settings.roles.form.permissions.publish\": \"Offentliggør\",\n    \"Settings.roles.form.permissions.read\": \"Læs\",\n    \"Settings.roles.form.permissions.update\": \"Opdatér\",\n    \"Settings.roles.list.button.add\": \"Tilføj ny rolle\",\n    \"Settings.roles.list.description\": \"Liste over roller\",\n    \"Settings.roles.title.singular\": \"rolle\",\n    \"Settings.sso.description\": \"Ændre indstillingerne for Single Sign-On funktionen.\",\n    \"Settings.sso.form.defaultRole.description\": \"Det vil forbinde nye autentificerede brugere til den valgte rolle\",\n    \"Settings.sso.form.defaultRole.description-not-allowed\": \"Du skal have tilladelse til at læse admin roller\",\n    \"Settings.sso.form.defaultRole.label\": \"Standard rolle\",\n    \"Settings.sso.form.registration.description\": \"Opret ny bruger med SSO log ind hvis ingen bruger findes\",\n    \"Settings.sso.form.registration.label\": \"Auto-registrering\",\n    \"Settings.sso.title\": \"Single Sign-On\",\n    \"Settings.webhooks.create\": \"Opret en webhook\",\n    \"Settings.webhooks.create.header\": \"Opret en ny header\",\n    \"Settings.webhooks.created\": \"Webhook oprettet\",\n    \"Settings.webhooks.event.publish-tooltip\": \"Dette event findes kun for indhold med udkast/offentliggør system slået til\",\n    \"Settings.webhooks.events.create\": \"Opret\",\n    \"Settings.webhooks.events.update\": \"Opdater\",\n    \"Settings.webhooks.form.events\": \"Events\",\n    \"Settings.webhooks.form.headers\": \"Headers\",\n    \"Settings.webhooks.form.url\": \"Url\",\n    \"Settings.webhooks.headers.remove\": \"Fjern overskriftrække {number}\",\n    \"Settings.webhooks.key\": \"Key\",\n    \"Settings.webhooks.list.button.add\": \"Tilføj ny webhook\",\n    \"Settings.webhooks.list.description\": \"Modtag POST ændringsnotifikationer.\",\n    \"Settings.webhooks.list.empty.description\": \"Tilføj din første til denne liste.\",\n    \"Settings.webhooks.list.empty.link\": \"Se vores dokumentation\",\n    \"Settings.webhooks.list.empty.title\": \"Der er ingen webhooks endnu\",\n    \"Settings.webhooks.list.th.actions\": \"handlinger\",\n    \"Settings.webhooks.list.th.status\": \"status\",\n    \"Settings.webhooks.singular\": \"webhook\",\n    \"Settings.webhooks.title\": \"Webhooks\",\n    \"Settings.webhooks.to.delete\": \"{webhooksToDeleteLength, plural, one {# asset} other {# assets}} selected\",\n    \"Settings.webhooks.trigger\": \"Trigger\",\n    \"Settings.webhooks.trigger.cancel\": \"Annuller trigger\",\n    \"Settings.webhooks.trigger.pending\": \"Venter…\",\n    \"Settings.webhooks.trigger.save\": \"Gem venligst for at trigger\",\n    \"Settings.webhooks.trigger.success\": \"Succes!\",\n    \"Settings.webhooks.trigger.success.label\": \"Trigger succesfuld\",\n    \"Settings.webhooks.trigger.test\": \"Test-trigger\",\n    \"Settings.webhooks.trigger.title\": \"Gem inden trigger\",\n    \"Settings.webhooks.value\": \"Værdi\",\n    Username: Username,\n    Users: Users,\n    \"Users & Permissions\": \"Brugere & rettigheder\",\n    \"Users.components.List.empty\": \"Der er ingen brugere...\",\n    \"Users.components.List.empty.withFilters\": \"Der er ingen brugere med de valgte filtre...\",\n    \"Users.components.List.empty.withSearch\": \"Der er ingen brugere der matcher søgningen ({search})...\",\n    \"admin.pages.MarketPlacePage.head\": \"Marketplace - Plugins\",\n    \"admin.pages.MarketPlacePage.submit.plugin.link\": \"Indsend dit plugin\",\n    \"admin.pages.MarketPlacePage.subtitle\": \"Få mere ud af Strapi\",\n    anErrorOccurred: anErrorOccurred,\n    \"app.component.CopyToClipboard.label\": \"Kopiér til klippebord\",\n    \"app.component.search.label\": \"Søg efter {target}\",\n    \"app.component.table.duplicate\": \"Duplikér {target}\",\n    \"app.component.table.edit\": \"Redigér {target}\",\n    \"app.component.table.select.one-entry\": \"Vælg {target}\",\n    \"app.components.BlockLink.blog\": \"Blog\",\n    \"app.components.BlockLink.blog.content\": \"Læs de seneste nyheder omkring Strapi og økosystemet.\",\n    \"app.components.BlockLink.code\": \"Kode eksempler\",\n    \"app.components.BlockLink.code.content\": \"Lær ved at teste rigtige projekter udviklet af community.\",\n    \"app.components.BlockLink.documentation.content\": \"Opdag essentielle koncepter, guides og instruktioner.\",\n    \"app.components.BlockLink.tutorial\": \"Tutorials\",\n    \"app.components.BlockLink.tutorial.content\": \"Følg step-by-step instruktioner til at bruge og ændre Strapi.\",\n    \"app.components.Button.cancel\": \"Annuller\",\n    \"app.components.Button.confirm\": \"Bekræft\",\n    \"app.components.Button.reset\": \"Nulstil\",\n    \"app.components.ComingSoonPage.comingSoon\": \"Kommer snart\",\n    \"app.components.ConfirmDialog.title\": \"Bekræftelse\",\n    \"app.components.DownloadInfo.download\": \"Download er i gang...\",\n    \"app.components.DownloadInfo.text\": \"Dette kan tage et øjeblik, tak for din tålmodighed.\",\n    \"app.components.EmptyAttributes.title\": \"Der er ingen felter endnu\",\n    \"app.components.EmptyStateLayout.content-document\": \"Intet indhold fundet\",\n    \"app.components.EmptyStateLayout.content-permissions\": \"Du har ikke tilladelse til at tilgå indholdet\",\n    \"app.components.HomePage.button.blog\": \"SE MERE PÅ BLOGGEN\",\n    \"app.components.HomePage.community\": \"Find fællesskabet på nettet\",\n    \"app.components.HomePage.community.content\": \"Diskutér med team medlemmer, contributors og udviklere på forskellige kanaler.\",\n    \"app.components.HomePage.create\": \"Opret din første indholdstype\",\n    \"app.components.HomePage.roadmap\": \"Se vores roadmap\",\n    \"app.components.HomePage.welcome\": \"Velkommen ombord!\",\n    \"app.components.HomePage.welcome.again\": \"Velkommen \",\n    \"app.components.HomePage.welcomeBlock.content\": \"Vi er glade for at have dig som en dig af fællesskabet. Vi leder konstant efter feedback så send os gerne en besked på \",\n    \"app.components.HomePage.welcomeBlock.content.again\": \"Vi håber at det går fremad med dit projekt... Læs gerne de seneste nyheder omkring Strapi. Vi gør vores bedste for at forbedre produktet baseret på din feedback.\",\n    \"app.components.HomePage.welcomeBlock.content.issues\": \"problemer.\",\n    \"app.components.HomePage.welcomeBlock.content.raise\": \" eller fremhæv \",\n    \"app.components.ImgPreview.hint\": \"Drag & drop din fil til dette område eller {browse} efter en fil at uploade\",\n    \"app.components.ImgPreview.hint.browse\": \"browse\",\n    \"app.components.InputFile.newFile\": \"Tilføj ny fil\",\n    \"app.components.InputFileDetails.open\": \"Åben i en ny fane\",\n    \"app.components.InputFileDetails.originalName\": \"Originalt navn:\",\n    \"app.components.InputFileDetails.remove\": \"Fjern denne fil\",\n    \"app.components.InputFileDetails.size\": \"Størrelse:\",\n    \"app.components.InstallPluginPage.Download.description\": \"Det tager muligvis et øjeblik at downloade og installere dette plugin.\",\n    \"app.components.InstallPluginPage.Download.title\": \"Downloader...\",\n    \"app.components.InstallPluginPage.description\": \"Udvid din app problemfrit.\",\n    \"app.components.LeftMenu.collapse\": \"Indskrænk menu\",\n    \"app.components.LeftMenu.expand\": \"Udvid menu\",\n    \"app.components.LeftMenu.logout\": \"Log ud\",\n    \"app.components.LeftMenu.trialCountdown\": \"Din prøve slutter den {date}.\",\n    \"app.components.LeftMenuFooter.help\": \"Hjælp\",\n    \"app.components.LeftMenuFooter.poweredBy\": \"Powered by \",\n    \"app.components.LeftMenuLinkContainer.collectionTypes\": \"Dokument typer\",\n    \"app.components.LeftMenuLinkContainer.configuration\": \"Konfigurationer\",\n    \"app.components.LeftMenuLinkContainer.general\": \"Generelt\",\n    \"app.components.LeftMenuLinkContainer.noPluginsInstalled\": \"Ingen plugins installeret endnu\",\n    \"app.components.LeftMenuLinkContainer.plugins\": \"Plugins\",\n    \"app.components.LeftMenuLinkContainer.singleTypes\": \"Enkelt typer\",\n    \"app.components.ListPluginsPage.deletePlugin.description\": \"Det tager muligvis et øjeblik at afinstallere dette plugin.\",\n    \"app.components.ListPluginsPage.deletePlugin.title\": \"Afinstallerer\",\n    \"app.components.ListPluginsPage.description\": \"Liste over installerede plugins i dette projekt.\",\n    \"app.components.ListPluginsPage.head.title\": \"Vis plugins\",\n    \"app.components.Logout.logout\": \"Log ud\",\n    \"app.components.Logout.profile\": \"Profil\",\n    \"app.components.MarketplaceBanner\": \"Opdap plugins bygget af commnuity, samt mange andre awesome ting til at kickstarte dit projekt.\",\n    \"app.components.MarketplaceBanner.image.alt\": \"strapi raket logo\",\n    \"app.components.MarketplaceBanner.link\": \"Tjek det ud nu\",\n    \"app.components.NotFoundPage.back\": \"Tilbage til hjem\",\n    \"app.components.NotFoundPage.description\": \"Ikke fundet\",\n    \"app.components.Official\": \"Officielt\",\n    \"app.components.Onboarding.help.button\": \"Hjælp knap\",\n    \"app.components.Onboarding.label.completed\": \"% gennefært\",\n    \"app.components.Onboarding.title\": \"Kom i gang videoer\",\n    \"app.components.PluginCard.Button.label.download\": \"Download\",\n    \"app.components.PluginCard.Button.label.install\": \"Allerede installeret\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed\": \"AutoReload funktionen skal aktiveres. Start venligst din app med `yarn develop`.\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.confirm\": \"Jeg forstår!\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.environment\": \"Af sikkerhedsmæssige årsager kan et plugin kun downloades i et development miljø.\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.title\": \"Download er ikke muligt\",\n    \"app.components.PluginCard.compatible\": \"Kompatibel med din app\",\n    \"app.components.PluginCard.compatibleCommunity\": \"Kompatibel med fællesskabet\",\n    \"app.components.PluginCard.more-details\": \"Flere detaljer\",\n    \"app.components.ToggleCheckbox.off-label\": \"Fra\",\n    \"app.components.ToggleCheckbox.on-label\": \"Til\",\n    \"app.components.Users.MagicLink.connect\": \"Send dette link til brugeren for at de kan connecte.\",\n    \"app.components.Users.MagicLink.connect.sso\": \"Send dette link til brugeren. Det første log ind kan klares med en SSO provider\",\n    \"app.components.Users.ModalCreateBody.block-title.details\": \"Detaljer\",\n    \"app.components.Users.ModalCreateBody.block-title.roles\": \"Brugerens roller\",\n    \"app.components.Users.ModalCreateBody.block-title.roles.description\": \"En bruger kan have en eller flere roller\",\n    \"app.components.Users.SortPicker.button-label\": \"Sortér efter\",\n    \"app.components.Users.SortPicker.sortby.email_asc\": \"E-mail (A til Z)\",\n    \"app.components.Users.SortPicker.sortby.email_desc\": \"E-mail (Z til A)\",\n    \"app.components.Users.SortPicker.sortby.firstname_asc\": \"Fornavn (A til Z)\",\n    \"app.components.Users.SortPicker.sortby.firstname_desc\": \"Fornavn (Z til A)\",\n    \"app.components.Users.SortPicker.sortby.lastname_asc\": \"Efternavn (A til Z)\",\n    \"app.components.Users.SortPicker.sortby.lastname_desc\": \"Efternavn (Z til A)\",\n    \"app.components.Users.SortPicker.sortby.username_asc\": \"Brugernavn (A til Z)\",\n    \"app.components.Users.SortPicker.sortby.username_desc\": \"Brugernavn (Z til A)\",\n    \"app.components.listPlugins.button\": \"Tilføj nyt plugin\",\n    \"app.components.listPlugins.title.none\": \"Ingen plugins installeret\",\n    \"app.components.listPluginsPage.deletePlugin.error\": \"Der skete en fejl under afinstallering af dette plugin\",\n    \"app.containers.App.notification.error.init\": \"Der skete en fejl under forespørgelse af API\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin\": \"Hvis du ikke modtager dette link, kontakt venligst din administrator.\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.text.email\": \"Det tager muligvis et øjeblik at modtage dit kodeord link.\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.title\": \"E-mail sendt\",\n    \"app.containers.Users.EditPage.form.active.label\": \"Aktiv\",\n    \"app.containers.Users.EditPage.header.label\": \"Redigér {name}\",\n    \"app.containers.Users.EditPage.header.label-loading\": \"Redigér bruger\",\n    \"app.containers.Users.EditPage.roles-bloc-title\": \"Tildelte roller\",\n    \"app.containers.Users.ModalForm.footer.button-success\": \"Opret bruger\",\n    \"app.links.configure-view\": \"Konfigurér visning\",\n    \"app.static.links.cheatsheet\": \"CheatSheet\",\n    \"app.utils.SelectOption.defaultMessage\": \" \",\n    \"app.utils.add-filter\": \"Tilføj filter\",\n    \"app.utils.close-label\": \"Luk\",\n    \"app.utils.defaultMessage\": \" \",\n    \"app.utils.duplicate\": \"Duplikér\",\n    \"app.utils.edit\": \"Redigér\",\n    \"app.utils.errors.file-too-big.message\": \"Filen er for stor\",\n    \"app.utils.filter-value\": \"Filtrér værdi\",\n    \"app.utils.filters\": \"Filtre\",\n    \"app.utils.notify.data-loaded\": \"{target} er hentet\",\n    \"app.utils.placeholder.defaultMessage\": \" \",\n    \"app.utils.publish\": \"Offentliggør\",\n    \"app.utils.select-all\": \"Vælg alle\",\n    \"app.utils.select-field\": \"Vælg felt\",\n    \"app.utils.select-filter\": \"Vælg filter\",\n    \"app.utils.unpublish\": \"Udkast\",\n    clearLabel: clearLabel,\n    \"coming.soon\": \"Dette indhold er under konstruktion og er tilbage om et par uger!\",\n    \"component.Input.error.validation.integer\": \"Værdien skal være et helt tal\",\n    \"components.AutoReloadBlocker.description\": \"Kør Strapi med en af følgende kommandoer:\",\n    \"components.AutoReloadBlocker.header\": \"Reload funktion er påkrævet for dette plugin.\",\n    \"components.ErrorBoundary.title\": \"Noget gik galt...\",\n    \"components.FilterOptions.FILTER_TYPES.$contains\": \"indeholder\",\n    \"components.FilterOptions.FILTER_TYPES.$containsi\": \"indeholder (sag ufølsom)\",\n    \"components.FilterOptions.FILTER_TYPES.$endsWith\": \"slutter med\",\n    \"components.FilterOptions.FILTER_TYPES.$endsWithi\": \"slutter med (sag ufølsom)\",\n    \"components.FilterOptions.FILTER_TYPES.$eq\": \"er\",\n    \"components.FilterOptions.FILTER_TYPES.$eqi\": \"er (sag ufølsom)\",\n    \"components.FilterOptions.FILTER_TYPES.$gt\": \"er større end\",\n    \"components.FilterOptions.FILTER_TYPES.$gte\": \"er større end eller lig med\",\n    \"components.FilterOptions.FILTER_TYPES.$lt\": \"er mindre end\",\n    \"components.FilterOptions.FILTER_TYPES.$lte\": \"er mindre end eller lig med\",\n    \"components.FilterOptions.FILTER_TYPES.$ne\": \"er ikke\",\n    \"components.FilterOptions.FILTER_TYPES.$nei\": \"er ikke (sag ufølsom)\",\n    \"components.FilterOptions.FILTER_TYPES.$notContains\": \"indeholder ikke\",\n    \"components.FilterOptions.FILTER_TYPES.$notContainsi\": \"indeholder ikke (sag ufølsom)\",\n    \"components.FilterOptions.FILTER_TYPES.$notNull\": \"er ikke null\",\n    \"components.FilterOptions.FILTER_TYPES.$null\": \"er null\",\n    \"components.FilterOptions.FILTER_TYPES.$startsWith\": \"starter med\",\n    \"components.FilterOptions.FILTER_TYPES.$startsWithi\": \"starter med (sag ufølsom)\",\n    \"components.Input.error.attribute.key.taken\": \"Værdien findes allerede\",\n    \"components.Input.error.attribute.sameKeyAndName\": \"Kan ikke være lig\",\n    \"components.Input.error.attribute.taken\": \"Dette feltnavn findes allerede\",\n    \"components.Input.error.contain.lowercase\": \"Kodeord skal indeholde mindst et lille bogstav\",\n    \"components.Input.error.contain.number\": \"Kodeord skal indehold mindst et tal\",\n    \"components.Input.error.contain.uppercase\": \"Kodeord skal indholde mindst et stort bogstav\",\n    \"components.Input.error.contentTypeName.taken\": \"Dette navn er allerede taget\",\n    \"components.Input.error.custom-error\": \"{errorMessage} \",\n    \"components.Input.error.password.noMatch\": \"Kodeord er ikke ens\",\n    \"components.Input.error.validation.email\": \"Dette er ikke en e-mail\",\n    \"components.Input.error.validation.json\": \"Dette matcher ikke JSON formatet\",\n    \"components.Input.error.validation.max\": \"Værdien er for høj {max}.\",\n    \"components.Input.error.validation.maxLength\": \"Værdien er for lang {max}.\",\n    \"components.Input.error.validation.min\": \"Værdien er for lav {min}.\",\n    \"components.Input.error.validation.minLength\": \"Værdien er for kort {min}.\",\n    \"components.Input.error.validation.minSupMax\": \"Kan ikke være højere\",\n    \"components.Input.error.validation.regex\": \"Værdien matcher ikke regex.\",\n    \"components.Input.error.validation.required\": \"Værdien er påkrævet.\",\n    \"components.Input.error.validation.unique\": \"Værdien er allerede brugt.\",\n    \"components.InputSelect.option.placeholder\": \"Vælg her\",\n    \"components.ListRow.empty\": \"Der er ingen data at vise.\",\n    \"components.NotAllowedInput.text\": \"Ingen tilladelse til at se dette felt\",\n    \"components.OverlayBlocker.description\": \"Du bruger en feature der kræver genstart. Vent veligst til serveren er oppe.\",\n    \"components.OverlayBlocker.description.serverError\": \"Serveren skulle have restartet, tjek venligst dine logs i terminalen.\",\n    \"components.OverlayBlocker.title\": \"Venter på genstart...\",\n    \"components.OverlayBlocker.title.serverError\": \"Genstart tager længere end forventet\",\n    \"components.PageFooter.select\": \"elementer pr side\",\n    \"components.ProductionBlocker.description\": \"Af sikkerhedsmæssige årsager deaktiveres dette plugin i andre miljøer.\",\n    \"components.ProductionBlocker.header\": \"Dette plugin er kun tilgængeligt under udvikling.\",\n    \"components.Search.placeholder\": \"Søg...\",\n    \"components.TableHeader.sort\": \"Sortér efter {label}\",\n    \"components.Wysiwyg.ToggleMode.markdown-mode\": \"Markdown tilstand\",\n    \"components.Wysiwyg.ToggleMode.preview-mode\": \"Forhåndsvisningstilstand\",\n    \"components.Wysiwyg.collapse\": \"Kollaps\",\n    \"components.Wysiwyg.selectOptions.H1\": \"Titel H1\",\n    \"components.Wysiwyg.selectOptions.H2\": \"Titel H2\",\n    \"components.Wysiwyg.selectOptions.H3\": \"Titel H3\",\n    \"components.Wysiwyg.selectOptions.H4\": \"Titel H4\",\n    \"components.Wysiwyg.selectOptions.H5\": \"Titel H5\",\n    \"components.Wysiwyg.selectOptions.H6\": \"Titel H6\",\n    \"components.Wysiwyg.selectOptions.title\": \"Tilføj en titel\",\n    \"components.WysiwygBottomControls.charactersIndicators\": \"tegn\",\n    \"components.WysiwygBottomControls.fullscreen\": \"Udvid\",\n    \"components.WysiwygBottomControls.uploadFiles\": \"Drag & drop filer, indsæt fra udklipsholder eller {browse}.\",\n    \"components.WysiwygBottomControls.uploadFiles.browse\": \"vælg dem\",\n    \"components.pagination.go-to\": \"Gå til side {page}\",\n    \"components.pagination.go-to-next\": \"Gå til næste side\",\n    \"components.pagination.go-to-previous\": \"Gå til forrige side\",\n    \"components.pagination.remaining-links\": \"og {number} ander links\",\n    \"components.popUpWarning.button.cancel\": \"Annuller\",\n    \"components.popUpWarning.button.confirm\": \"Bekræft\",\n    \"components.popUpWarning.message\": \"Er du sikker på at du vil slette?\",\n    \"components.popUpWarning.title\": \"Bekræft venligst\",\n    \"form.button.done\": \"Færdig\",\n    \"global.prompt.unsaved\": \"Er du sikker på at du vil forlade denne side? Alle dine ændringer vil gå tabt\",\n    \"notification.contentType.relations.conflict\": \"Der er en eller flere konflikter med indholdstypens relationer\",\n    \"notification.default.title\": \"Information:\",\n    \"notification.error\": \"Der er sket en fejl\",\n    \"notification.error.layout\": \"Kunne ikke hente layout\",\n    \"notification.form.error.fields\": \"Formularen indeholder fejl\",\n    \"notification.form.success.fields\": \"Ændringer gemt\",\n    \"notification.link-copied\": \"Link kopieret til udklipsholder\",\n    \"notification.permission.not-allowed-read\": \"Du har ikke tilladelse til at se dette dokument\",\n    \"notification.success.delete\": \"Elementet er blevet slettet\",\n    \"notification.success.saved\": \"Gemt\",\n    \"notification.success.title\": \"Succes:\",\n    \"notification.version.update.message\": \"En ny version af Strapi er tilgængelig!\",\n    \"notification.warning.title\": \"Advarsel:\",\n    or: or,\n    \"request.error.model.unknown\": \"Denne model findes ikke\",\n    skipToContent: skipToContent,\n    submit: submit\n};\n\nexport { Analytics, Documentation, Email, Password, Provider, ResetPasswordToken, Role, Username, Users, anErrorOccurred, clearLabel, dk as default, or, skipToContent, submit };\n//# sourceMappingURL=dk.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,YAAY;AAChB,IAAI,gBAAgB;AACpB,IAAI,QAAQ;AACZ,IAAI,WAAW;AACf,IAAI,WAAW;AACf,IAAI,qBAAqB;AACzB,IAAI,OAAO;AACX,IAAI,WAAW;AACf,IAAI,QAAQ;AACZ,IAAI,kBAAkB;AACtB,IAAI,aAAa;AACjB,IAAI,KAAK;AACT,IAAI,gBAAgB;AACpB,IAAI,SAAS;AACb,IAAI,KAAK;AAAA,EACL;AAAA,EACA,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0CAA0C;AAAA,EAC1C,iCAAiC;AAAA,EACjC,sCAAsC;AAAA,EACtC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,iDAAiD;AAAA,EACjD,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,2BAA2B;AAAA,EAC3B,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA,EAChB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,6BAA6B;AAAA,EAC7B,qCAAqC;AAAA,EACrC,0CAA0C;AAAA,EAC1C,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,4BAA4B;AAAA,EAC5B,0CAA0C;AAAA,EAC1C,sBAAsB;AAAA,EACtB,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,4BAA4B;AAAA,EAC5B,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,oCAAoC;AAAA,EACpC,sCAAsC;AAAA,EACtC,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,sCAAsC;AAAA,EACtC,8BAA8B;AAAA,EAC9B,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,yDAAyD;AAAA,EACzD,iDAAiD;AAAA,EACjD,8DAA8D;AAAA,EAC9D,mEAAmE;AAAA,EACnE,kEAAkE;AAAA,EAClE,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,oDAAoD;AAAA,EACpD,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,mCAAmC;AAAA,EACnC,iDAAiD;AAAA,EACjD,yDAAyD;AAAA,EACzD,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,wCAAwC;AAAA,EACxC,0CAA0C;AAAA,EAC1C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,6CAA6C;AAAA,EAC7C,yDAAyD;AAAA,EACzD,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,wCAAwC;AAAA,EACxC,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,2CAA2C;AAAA,EAC3C,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,4CAA4C;AAAA,EAC5C,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,2BAA2B;AAAA,EAC3B;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,+BAA+B;AAAA,EAC/B,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,kDAAkD;AAAA,EAClD,wCAAwC;AAAA,EACxC;AAAA,EACA,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,wCAAwC;AAAA,EACxC,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,4CAA4C;AAAA,EAC5C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,0CAA0C;AAAA,EAC1C,sCAAsC;AAAA,EACtC,2CAA2C;AAAA,EAC3C,wDAAwD;AAAA,EACxD,sDAAsD;AAAA,EACtD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,2DAA2D;AAAA,EAC3D,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,oCAAoC;AAAA,EACpC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,2CAA2C;AAAA,EAC3C,2BAA2B;AAAA,EAC3B,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,+EAA+E;AAAA,EAC/E,qEAAqE;AAAA,EACrE,yEAAyE;AAAA,EACzE,mEAAmE;AAAA,EACnE,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,4DAA4D;AAAA,EAC5D,0DAA0D;AAAA,EAC1D,sEAAsE;AAAA,EACtE,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,qDAAqD;AAAA,EACrD,wDAAwD;AAAA,EACxD,yDAAyD;AAAA,EACzD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,qCAAqC;AAAA,EACrC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,oEAAoE;AAAA,EACpE,4DAA4D;AAAA,EAC5D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,wDAAwD;AAAA,EACxD,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,yCAAyC;AAAA,EACzC,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,4BAA4B;AAAA,EAC5B,uBAAuB;AAAA,EACvB,kBAAkB;AAAA,EAClB,yCAAyC;AAAA,EACzC,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,gCAAgC;AAAA,EAChC,wCAAwC;AAAA,EACxC,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB;AAAA,EACA,eAAe;AAAA,EACf,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,+CAA+C;AAAA,EAC/C,qDAAqD;AAAA,EACrD,sDAAsD;AAAA,EACtD,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,0CAA0C;AAAA,EAC1C,4CAA4C;AAAA,EAC5C,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,gDAAgD;AAAA,EAChD,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,yDAAyD;AAAA,EACzD,+CAA+C;AAAA,EAC/C,gDAAgD;AAAA,EAChD,uDAAuD;AAAA,EACvD,+BAA+B;AAAA,EAC/B,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,yCAAyC;AAAA,EACzC,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,oBAAoB;AAAA,EACpB,yBAAyB;AAAA,EACzB,+CAA+C;AAAA,EAC/C,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,4CAA4C;AAAA,EAC5C,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B;AAAA,EACA,+BAA+B;AAAA,EAC/B;AAAA,EACA;AACJ;", "names": []}