{"version": 3, "sources": ["../../../@strapi/email/dist/admin/translations/dk.json.mjs"], "sourcesContent": ["var dk = {\n    \"Settings.email.plugin.button.test-email\": \"Send test e-mail\",\n    \"Settings.email.plugin.label.defaultFrom\": \"Standard afsender e-mail\",\n    \"Settings.email.plugin.label.defaultReplyTo\": \"Standard svar e-mail\",\n    \"Settings.email.plugin.label.provider\": \"E-mail provider\",\n    \"Settings.email.plugin.label.testAddress\": \"Modtager e-mail\",\n    \"Settings.email.plugin.notification.config.error\": \"Fejlede i at hente e-mail konfiguration\",\n    \"Settings.email.plugin.notification.data.loaded\": \"E-mail indstillinger er blevet hentet\",\n    \"Settings.email.plugin.notification.test.error\": \"Fejlede ved udsendelse af test mail til {to}\",\n    \"Settings.email.plugin.notification.test.success\": \"E-mail test lykkedes, tjek indbakken hos {to}\",\n    \"Settings.email.plugin.placeholder.defaultFrom\": \"f.eks. Strapi No-Reply <<EMAIL>>\",\n    \"Settings.email.plugin.placeholder.defaultReplyTo\": \"f.eks. Strapi <<EMAIL>>\",\n    \"Settings.email.plugin.placeholder.testAddress\": \"f.eks. <EMAIL>\",\n    \"Settings.email.plugin.subTitle\": \"Test indstillingerne for Email plugin\",\n    \"Settings.email.plugin.text.configuration\": \"Pluginnet er konfigureret igennem {file} filen, tjek {link} for dokumentation.\",\n    \"Settings.email.plugin.title\": \"Konfiguration\",\n    \"Settings.email.plugin.title.config\": \"Konfiguration\",\n    \"Settings.email.plugin.title.test\": \"Test e-mail modtagelse\",\n    \"SettingsNav.link.settings\": \"Indstillinger\",\n    \"SettingsNav.section-label\": \"E-mail plugin\",\n    \"components.Input.error.validation.email\": \"Dette er en ugyldig e-mail\"\n};\n\nexport { dk as default };\n//# sourceMappingURL=dk.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,KAAK;AAAA,EACL,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,wCAAwC;AAAA,EACxC,2CAA2C;AAAA,EAC3C,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,iDAAiD;AAAA,EACjD,mDAAmD;AAAA,EACnD,iDAAiD;AAAA,EACjD,oDAAoD;AAAA,EACpD,iDAAiD;AAAA,EACjD,kCAAkC;AAAA,EAClC,4CAA4C;AAAA,EAC5C,+BAA+B;AAAA,EAC/B,sCAAsC;AAAA,EACtC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,2CAA2C;AAC/C;", "names": []}