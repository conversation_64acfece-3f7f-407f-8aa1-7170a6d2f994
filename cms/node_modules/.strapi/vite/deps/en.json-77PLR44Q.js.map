{"version": 3, "sources": ["../../../@strapi/content-releases/dist/admin/translations/en.json.mjs"], "sourcesContent": ["var en = {\n    \"content-manager-edit-view.add-to-release.select-label\": \"Select a release\",\n    \"content-manager-edit-view.add-to-release.select-placeholder\": \"Select\",\n    \"content-manager-edit-view.add-to-release.action-type-label\": \"What do you want to do with this entry?\",\n    \"content-manager-edit-view.add-to-release.cancel-button\": \"Cancel\",\n    \"content-manager-edit-view.add-to-release.continue-button\": \"Continue\",\n    \"content-manager-edit-view.add-to-release\": \"Add to release\",\n    \"content-manager-edit-view.add-to-release.notification.success\": \"Entry added to release\",\n    \"content-manager-edit-view.add-to-release.no-releases-message\": \"No available releases. Open the list of releases and create a new one from there.\",\n    \"content-manager-edit-view.add-to-release.redirect-button\": \"Open the list of releases\",\n    \"content-manager-edit-view.list-releases.title\": \"{isPublish, select, true {Will be published in} other {Will be unpublished in}}\",\n    \"content-manager-edit-view.remove-from-release\": \"Remove from release\",\n    \"content-manager-edit-view.scheduled.date\": \"{date} at {time} ({offset})\",\n    \"content-manager-edit-view.edit-release\": \"Edit release\",\n    \"content-releases.content-manager-edit-view.edit-entry\": \"Edit entry\",\n    \"content-manager-edit-view.remove-from-release.notification.success\": \"Entry removed from release\",\n    \"content-manager-edit-view.release-action-menu\": \"Release action options\",\n    \"content-manager.list-view.releases.header\": \"To be released in\",\n    \"content-manager-list-view.add-to-release\": \"Add to release\",\n    \"content-manager-list-view.add-to-release.cancel-button\": \"Cancel\",\n    \"content-manager-list-view.add-to-release.continue-button\": \"Continue\",\n    \"content-manager-list-view.add-to-release.select-label\": \"Select a release\",\n    \"content-manager-list-view.add-to-release.select-placeholder\": \"Select\",\n    \"content-manager-list-view.add-to-release.action-type-label\": \"What do you want to do with these entries?\",\n    \"content-manager-list-view.add-to-release.notification.success.title\": \"Successfully added to release.\",\n    \"content-manager-list-view.add-to-release.notification.success.message\": \"{entriesAlreadyInRelease} out of {totalEntries} entries were already in the release.\",\n    \"content-manager.notification.entry-error\": \"Failed to get entry data\",\n    \"content-manager.list-view.releases-number\": \"{number} {number, plural, one {release} other {releases}}\",\n    \"plugin.name\": \"Releases\",\n    \"pages.Releases.title\": \"Releases\",\n    \"pages.Releases.header-subtitle\": \"Create and manage content updates\",\n    \"pages.Releases.max-limit-reached.title\": \"You have reached the {number} pending {number, plural, one {release} other {releases}} limit.\",\n    \"pages.Releases.max-limit-reached.message\": \"Upgrade to manage an unlimited number of releases.\",\n    \"pages.Releases.max-limit-reached.action\": \"Explore plans\",\n    \"pages.PurchaseRelease.subTitle\": \"Manage content updates and releases.\",\n    \"pages.PurchaseRelease.not-available\": \"Releases is only available as part of a paid plan. Upgrade to create and manage releases.\",\n    \"pages.PurchaseRelease.description\": \"Group content and publish updates together\",\n    \"pages.PurchaseRelease.perks1\": \"Add many entries to releases\",\n    \"pages.PurchaseRelease.perks2\": \"Quickly identify entries containing errors\",\n    \"pages.PurchaseRelease.perks3\": \"Schedule their publication, or publish them manually\",\n    \"header.actions.add-release\": \"New Release\",\n    \"header.actions.refresh\": \"Refresh\",\n    \"header.actions.publish\": \"Publish\",\n    \"header.actions.open-release-actions\": \"Release edit and delete menu\",\n    \"header.actions.edit\": \"Edit\",\n    \"header.actions.delete\": \"Delete\",\n    \"header.actions.created\": \"Created\",\n    \"header.actions.created.description\": \"{hasCreatedByUser, select, true { by {createdBy}} other { by deleted user}}\",\n    \"modal.release-created-notification-success\": \"Release created\",\n    \"modal.release-updated-notification-success\": \"Release updated\",\n    \"modal.title\": \"{isCreatingRelease, select, true {New release} other {Edit release}}\",\n    \"modal.form.input.label.release-name\": \"Name\",\n    \"modal.form.input.label.schedule-release\": \"Schedule release\",\n    \"modal.form.input.label.date\": \"Date\",\n    \"modal.form.input.label.time\": \"Time\",\n    \"modal.form.input.label.timezone\": \"Timezone\",\n    \"modal.form.input.clearLabel\": \"Clear\",\n    \"modal.form.button.submit\": \"{isCreatingRelease, select, true {Continue} other {Save}}\",\n    \"modal.form.time.has-passed\": \"Selected time has already passed.\",\n    \"pages.Details.header-subtitle\": \"{number, plural, =0 {No entries} one {# entry} other {# entries}}\",\n    \"pages.Releases.tab-group.label\": \"Releases list\",\n    \"pages.Releases.tab.pending\": \"Pending ({count})\",\n    \"pages.Releases.tab.done\": \"Done\",\n    \"page.Releases.tab.emptyEntries\": \"No releases\",\n    \"pages.Details.tab.emptyEntries\": \"This release is empty. Open the Content Manager, select an entry and add it to the release.\",\n    \"page.ReleaseDetails.table.header.label.name\": \"name\",\n    \"page.ReleaseDetails.table.header.label.locale\": \"locale\",\n    \"page.ReleaseDetails.table.header.label.content-type\": \"content-type\",\n    \"page.ReleaseDetails.table.header.label.action\": \"action\",\n    \"content-releases.page.ReleaseDetails.table.header.label.status\": \"status\",\n    \"page.ReleaseDetails.table.action-published\": \"This entry was <b>{isPublish, select, true {published} other {unpublished}}</b>.\",\n    \"pages.ReleaseDetails.publish-notification-success\": \"Release was published successfully.\",\n    \"dialog.confirmation-message\": \"Are you sure you want to delete this release?\",\n    \"page.Details.button.openContentManager\": \"Open the Content Manager\",\n    \"pages.Releases.notification.error.title\": \"Your request could not be processed.\",\n    \"pages.Releases.notification.error.message\": \"Please try again or open another release.\",\n    \"pages.Releases.not-scheduled\": \"Not scheduled\",\n    \"pages.ReleaseDetails.groupBy.label\": \"Group by {groupBy}\",\n    \"pages.ReleaseDetails.groupBy.aria-label\": \"Group by\",\n    \"pages.ReleaseDetails.entry-validation.already-published\": \"Already published\",\n    \"pages.ReleaseDetails.entry-validation.ready-to-publish\": \"Ready to publish\",\n    \"pages.ReleaseDetails.entry-validation.modified\": \"Ready to publish changes\",\n    \"pages.ReleaseDetails.entry-validation.already-unpublished\": \"Already unpublished\",\n    \"pages.ReleaseDetails.entry-validation.ready-to-unpublish\": \"Ready to unpublish\",\n    \"pages.ReleaseDetails.entry-validation.not-ready\": \"Not ready to publish\",\n    \"pages.ReleaseDetails.groupBy.option.content-type\": \"Content-Types\",\n    \"pages.ReleaseDetails.groupBy.option.locales\": \"Locales\",\n    \"pages.ReleaseDetails.groupBy.option.actions\": \"Actions\",\n    \"pages.ReleaseDetails.header-subtitle.scheduled\": \"Scheduled for {date} at {time} ({offset})\",\n    \"pages.ReleaseDetails.entry-validation.fields\": \"Fields\",\n    \"pages.Settings.releases.description\": \"Create and manage content updates\",\n    \"pages.Settings.releases.preferences.title\": \"Preferences\",\n    \"pages.Settings.releases.timezone.label\": \"Default timezone\",\n    \"pages.Settings.releases.timezone.hint\": \"The timezone of every release can still be changed individually.\",\n    \"pages.Settings.releases.setting.default-timezone-notification-success\": \"Default timezone updated.\",\n    \"pages.ReleaseDetails.entry-validation.fields.error\": \"{errors} errors on fields.\",\n    \"pages.ReleaseDetails.entry-validation.fields.success\": \"All fields are filled correctly.\",\n    \"pages.ReleaseDetails.entry-validation.fields.see-errors\": \"See errors\",\n    \"pages.ReleaseDetails.entry-validation.review-stage.not-enabled\": \"This entry is not associated to any workflow.\",\n    \"pages.ReleaseDetails.entry-validation.review-stage.not-ready\": \"This entry is not at the required stage for publishing. ({stageName})\",\n    \"pages.ReleaseDetails.entry-validation.review-stage.ready\": \"This entry is at the required stage for publishing. ({stageName})\",\n    \"pages.ReleaseDetails.entry-validation.review-stage.stage-not-required\": \"No required stage for publication.\"\n};\n\nexport { en as default };\n//# sourceMappingURL=en.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,KAAK;AAAA,EACL,yDAAyD;AAAA,EACzD,+DAA+D;AAAA,EAC/D,8DAA8D;AAAA,EAC9D,0DAA0D;AAAA,EAC1D,4DAA4D;AAAA,EAC5D,4CAA4C;AAAA,EAC5C,iEAAiE;AAAA,EACjE,gEAAgE;AAAA,EAChE,4DAA4D;AAAA,EAC5D,iDAAiD;AAAA,EACjD,iDAAiD;AAAA,EACjD,4CAA4C;AAAA,EAC5C,0CAA0C;AAAA,EAC1C,yDAAyD;AAAA,EACzD,sEAAsE;AAAA,EACtE,iDAAiD;AAAA,EACjD,6CAA6C;AAAA,EAC7C,4CAA4C;AAAA,EAC5C,0DAA0D;AAAA,EAC1D,4DAA4D;AAAA,EAC5D,yDAAyD;AAAA,EACzD,+DAA+D;AAAA,EAC/D,8DAA8D;AAAA,EAC9D,uEAAuE;AAAA,EACvE,yEAAyE;AAAA,EACzE,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,eAAe;AAAA,EACf,wBAAwB;AAAA,EACxB,kCAAkC;AAAA,EAClC,0CAA0C;AAAA,EAC1C,4CAA4C;AAAA,EAC5C,2CAA2C;AAAA,EAC3C,kCAAkC;AAAA,EAClC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,gCAAgC;AAAA,EAChC,gCAAgC;AAAA,EAChC,gCAAgC;AAAA,EAChC,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,uCAAuC;AAAA,EACvC,uBAAuB;AAAA,EACvB,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,8CAA8C;AAAA,EAC9C,8CAA8C;AAAA,EAC9C,eAAe;AAAA,EACf,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,+BAA+B;AAAA,EAC/B,+BAA+B;AAAA,EAC/B,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA,EAC9B,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,+CAA+C;AAAA,EAC/C,iDAAiD;AAAA,EACjD,uDAAuD;AAAA,EACvD,iDAAiD;AAAA,EACjD,kEAAkE;AAAA,EAClE,8CAA8C;AAAA,EAC9C,qDAAqD;AAAA,EACrD,+BAA+B;AAAA,EAC/B,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,sCAAsC;AAAA,EACtC,2CAA2C;AAAA,EAC3C,2DAA2D;AAAA,EAC3D,0DAA0D;AAAA,EAC1D,kDAAkD;AAAA,EAClD,6DAA6D;AAAA,EAC7D,4DAA4D;AAAA,EAC5D,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,kDAAkD;AAAA,EAClD,gDAAgD;AAAA,EAChD,uCAAuC;AAAA,EACvC,6CAA6C;AAAA,EAC7C,0CAA0C;AAAA,EAC1C,yCAAyC;AAAA,EACzC,yEAAyE;AAAA,EACzE,sDAAsD;AAAA,EACtD,wDAAwD;AAAA,EACxD,2DAA2D;AAAA,EAC3D,kEAAkE;AAAA,EAClE,gEAAgE;AAAA,EAChE,4DAA4D;AAAA,EAC5D,yEAAyE;AAC7E;", "names": []}