{"version": 3, "sources": ["../../../@strapi/upload/dist/admin/translations/en.json.mjs"], "sourcesContent": ["var en = {\n    \"apiError.FileTooBig\": \"The uploaded file exceeds the maximum allowed asset size.\",\n    \"upload.generic-error\": \"An error occurred while uploading the file.\",\n    \"bulk.select.label\": \"Select all assets\",\n    \"button.next\": \"Next\",\n    \"checkControl.crop-duplicate\": \"Duplicate & crop the asset\",\n    \"checkControl.crop-original\": \"Crop the original asset\",\n    \"content.isLoading\": \"Content is loading.\",\n    \"control-card.add\": \"Add\",\n    \"control-card.cancel\": \"Cancel\",\n    \"control-card.copy-link\": \"Copy link\",\n    \"control-card.crop\": \"Crop\",\n    \"control-card.download\": \"Download\",\n    \"control-card.edit\": \"Edit\",\n    \"control-card.replace-media\": \"Replace Media\",\n    \"control-card.save\": \"Save\",\n    \"control-card.stop-crop\": \"Stop cropping\",\n    \"filter.add\": \"Add filter\",\n    \"form.button.replace-media\": \"Replace media\",\n    \"form.input.description.file-alt\": \"This text will be displayed if the asset can’t be shown.\",\n    \"form.input.label.file-alt\": \"Alternative text\",\n    \"form.input.label.file-caption\": \"Caption\",\n    \"form.input.label.file-name\": \"File name\",\n    \"form.upload-url.error.url.invalid\": \"One URL is invalid\",\n    \"form.upload-url.error.url.invalids\": \"{number} URLs are invalids\",\n    \"header.actions.add-assets\": \"Add new assets\",\n    \"header.actions.add-folder\": \"Add new folder\",\n    \"header.actions.add-assets.folder\": \"folder\",\n    \"header.actions.upload-assets\": \"Upload assets\",\n    \"header.actions.upload-new-asset\": \"Upload new asset\",\n    \"header.content.assets-empty\": \"No assets\",\n    \"header.content.assets\": \"{numberFolders, plural, one {1 folder} other {# folders}} - {numberAssets, plural, one {1 asset} other {# assets}}\",\n    \"input.button.label\": \"Browse files\",\n    \"input.label\": \"Drag & Drop here or\",\n    \"input.label-bold\": \"Drag & drop\",\n    \"input.label-normal\": \"to upload or\",\n    \"input.placeholder\": \"Click to add an asset or drag & drop a file in this area\",\n    \"input.placeholder.icon\": \"Drop the asset in this zone\",\n    \"input.url.description\": \"Separate your URL links by a carriage return.\",\n    \"input.url.label\": \"URL\",\n    \"input.notification.not-supported\": \"You can't upload this type of file, only the following types are accepted – {fileTypes}\",\n    \"list.assets.title\": \"Assets ({count})\",\n    \"list.asset.at.finished\": \"The assets have finished loading.\",\n    \"list.assets-empty.search\": \"No result found\",\n    \"list.assets-empty.subtitle\": \"Add one to the list.\",\n    \"list.assets-empty.title\": \"There are no assets yet\",\n    \"list.assets-empty.title-withSearch\": \"There are no elements with the applied filters\",\n    \"list.assets.empty\": \"Media Library is empty\",\n    \"list.assets.empty-upload\": \"Upload your first assets...\",\n    \"list.assets.empty.no-permissions\": \"No permissions to view\",\n    \"list.assets.loading-asset\": \"Loading the preview for the media: {path}\",\n    \"list.assets.not-supported-content\": \"No preview available\",\n    \"list.assets.preview-asset\": \"Preview for the video at path {path}\",\n    \"list.assets.selected\": \"{numberFolders, plural, one {1 folder} other {# folders}} - {numberAssets, plural, one {1 asset} other {# assets}}\",\n    \"list-assets-select\": \"Select {name} asset\",\n    \"list.assets.type-not-allowed\": \"This type of file is not allowed.\",\n    \"list.assets.to-upload\": \"{number, plural, =0 {No asset} one {1 asset} other {# assets}} ready to upload\",\n    \"list.folder.edit\": \"Edit folder\",\n    \"list.folder.select\": \"Select {name} folder\",\n    \"list.folder.subtitle\": \"{folderCount, plural, =0 {# folder} one {# folder} other {# folders}}, {filesCount, plural, =0 {# asset} one {# asset} other {# assets}}\",\n    \"list.folders.title\": \"Folders ({count})\",\n    \"list.folders.link-label\": \"Access folder\",\n    \"mediaLibraryInput.actions.nextSlide\": \"Next slide\",\n    \"mediaLibraryInput.actions.previousSlide\": \"Previous slide\",\n    \"mediaLibraryInput.placeholder\": \"Click to add an asset or drag and drop one in this area\",\n    \"mediaLibraryInput.slideCount\": \"{n} of {m} slides\",\n    \"modal.file-details.date\": \"Date\",\n    \"modal.file-details.dimensions\": \"Dimensions\",\n    \"modal.file-details.extension\": \"Extension\",\n    \"modal.file-details.size\": \"Size\",\n    \"modal.file-details.id\": \"Asset ID\",\n    \"modal.folder.elements.count\": \"{folderCount} folders, {assetCount} assets\",\n    \"modal.header.browse\": \"Upload assets\",\n    \"modal.header.file-detail\": \"Details\",\n    \"modal.header.pending-assets\": \"Pending assets\",\n    \"modal.header.select-files\": \"Selected files\",\n    \"modal.header.go-back\": \"Go back\",\n    \"modal.folder.move.title\": \"Move elements to\",\n    \"modal.nav.browse\": \"browse\",\n    \"modal.nav.computer\": \"From computer\",\n    \"modal.nav.selected\": \"selected\",\n    \"modal.nav.url\": \"From url\",\n    \"modal.remove.success-label\": \"Elements have been successfully deleted.\",\n    \"modal.move.success-label\": \"Elements have been moved successfully\",\n    \"modal.selected-list.sub-header-subtitle\": \"Drag & drop to reorder the assets in the field\",\n    \"modal.upload-list.footer.button\": \"Upload {number, plural, one {# asset} other {# assets}} to the library\",\n    \"modal.upload-list.sub-header-subtitle\": \"Manage the assets before adding them to the Media Library\",\n    \"modal.upload-list.sub-header.button\": \"Add more assets\",\n    \"modal.upload.cancelled\": \"Upload manually aborted.\",\n    \"page.title\": \"Settings - Media Library\",\n    \"permissions.not-allowed.update\": \"You are not allowed to edit this file.\",\n    \"plugin.description.long\": \"Media file management.\",\n    \"plugin.description.short\": \"Media file management.\",\n    \"plugin.name\": \"Media Library\",\n    \"search.clear.label\": \"Clear the search\",\n    \"search.label\": \"Search for an asset\",\n    \"search.placeholder\": \"e.g: the first dog on the moon\",\n    \"settings.blockTitle\": \"Asset Management\",\n    \"settings.form.autoOrientation.description\": \"Enabling this option will automatically rotate the image according to EXIF orientation tag.\",\n    \"settings.form.autoOrientation.label\": \"Auto orientation\",\n    \"settings.form.responsiveDimensions.description\": \"Enabling this option will generate multiple formats (small, medium and large) of the uploaded asset.\",\n    \"settings.form.responsiveDimensions.label\": \"Responsive friendly upload\",\n    \"settings.form.sizeOptimization.description\": \"Enabling this option will reduce the image size and slightly reduce its quality.\",\n    \"settings.form.sizeOptimization.label\": \"Size optimization\",\n    \"settings.form.videoPreview.description\": \"It will generate a six-second preview of the video (GIF)\",\n    \"settings.form.videoPreview.label\": \"Preview\",\n    \"settings.header.label\": \"Media Library\",\n    \"settings.section.doc.label\": \"Doc\",\n    \"settings.section.image.label\": \"Image\",\n    \"settings.section.video.label\": \"Video\",\n    \"settings.sub-header.label\": \"Configure the settings for the Media Library\",\n    \"sort.created_at_asc\": \"Oldest uploads\",\n    \"sort.created_at_desc\": \"Most recent uploads\",\n    \"sort.label\": \"Sort by\",\n    \"sort.name_asc\": \"Alphabetical order (A to Z)\",\n    \"sort.name_desc\": \"Reverse alphabetical order (Z to A)\",\n    \"sort.updated_at_asc\": \"Oldest updates\",\n    \"sort.updated_at_desc\": \"Most recent updates\",\n    \"list.table.header.actions\": \"actions\",\n    \"list.table.header.preview\": \"preview\",\n    \"list.table.header.name\": \"name\",\n    \"list.table.header.ext\": \"extension\",\n    \"list.table.header.size\": \"size\",\n    \"list.table.header.createdAt\": \"created\",\n    \"list.table.header.updatedAt\": \"last update\",\n    \"list.table.header.sort\": \"Sort on {label}\",\n    \"list.table.content.empty-label\": \"This field is empty\",\n    \"tabs.title\": \"How do you want to upload your assets?\",\n    \"window.confirm.close-modal.file\": \"Are you sure? Your changes will be lost.\",\n    \"window.confirm.close-modal.files\": \"Are you sure? You have some files that have not been uploaded yet.\",\n    \"config.back\": \"Back\",\n    \"config.subtitle\": \"Define the view settings of the media library.\",\n    \"config.entries.title\": \"Entries per page\",\n    \"config.sort.title\": \"Default sort order\",\n    \"config.entries.note\": \"Number of assets displayed by default in the Media Library\",\n    \"config.note\": \"Note: You can override this value in the media library.\",\n    \"config.popUpWarning.warning.updateAllSettings\": \"This will modify all your settings\",\n    \"view-switch.list\": \"List View\",\n    \"view-switch.grid\": \"Grid View\"\n};\n\nexport { en as default };\n//# sourceMappingURL=en.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,KAAK;AAAA,EACL,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,qBAAqB;AAAA,EACrB,eAAe;AAAA,EACf,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,qBAAqB;AAAA,EACrB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,qBAAqB;AAAA,EACrB,8BAA8B;AAAA,EAC9B,qBAAqB;AAAA,EACrB,0BAA0B;AAAA,EAC1B,cAAc;AAAA,EACd,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,gCAAgC;AAAA,EAChC,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,qBAAqB;AAAA,EACrB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,mBAAmB;AAAA,EACnB,oCAAoC;AAAA,EACpC,qBAAqB;AAAA,EACrB,0BAA0B;AAAA,EAC1B,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,sCAAsC;AAAA,EACtC,qBAAqB;AAAA,EACrB,4BAA4B;AAAA,EAC5B,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,qCAAqC;AAAA,EACrC,6BAA6B;AAAA,EAC7B,wBAAwB;AAAA,EACxB,sBAAsB;AAAA,EACtB,gCAAgC;AAAA,EAChC,yBAAyB;AAAA,EACzB,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,wBAAwB;AAAA,EACxB,sBAAsB;AAAA,EACtB,2BAA2B;AAAA,EAC3B,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,iCAAiC;AAAA,EACjC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,6BAA6B;AAAA,EAC7B,wBAAwB;AAAA,EACxB,2BAA2B;AAAA,EAC3B,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,iBAAiB;AAAA,EACjB,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,2CAA2C;AAAA,EAC3C,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,0BAA0B;AAAA,EAC1B,cAAc;AAAA,EACd,kCAAkC;AAAA,EAClC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,eAAe;AAAA,EACf,sBAAsB;AAAA,EACtB,gBAAgB;AAAA,EAChB,sBAAsB;AAAA,EACtB,uBAAuB;AAAA,EACvB,6CAA6C;AAAA,EAC7C,uCAAuC;AAAA,EACvC,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,8CAA8C;AAAA,EAC9C,wCAAwC;AAAA,EACxC,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,yBAAyB;AAAA,EACzB,8BAA8B;AAAA,EAC9B,gCAAgC;AAAA,EAChC,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,cAAc;AAAA,EACd,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,+BAA+B;AAAA,EAC/B,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,kCAAkC;AAAA,EAClC,cAAc;AAAA,EACd,mCAAmC;AAAA,EACnC,oCAAoC;AAAA,EACpC,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,eAAe;AAAA,EACf,iDAAiD;AAAA,EACjD,oBAAoB;AAAA,EACpB,oBAAoB;AACxB;", "names": []}