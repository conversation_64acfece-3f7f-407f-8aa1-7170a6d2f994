{"version": 3, "sources": ["../../../@strapi/review-workflows/dist/admin/translations/en.json.mjs"], "sourcesContent": ["var en = {\n    \"settings.page.title\": \"Review Workflows\",\n    \"settings.page.subtitle\": \"{count, plural, one {# stage} other {# stages}}\",\n    \"settings.page.isLoading\": \"Workflow is loading\",\n    \"settings.page.delete.confirm.body\": \"All entries assigned to deleted stages will be moved to the previous stage. Are you sure you want to save?\",\n    \"settings.stage.name.label\": \"Stage name\",\n    \"settings.not-available\": \"Review Workflows is only available as part of the Enterprise Edition. Upgrade to create and manage workflows.\",\n    \"settings.review-workflows.workflow.stageRequiredToPublish.label\": \"Required stage for publishing\",\n    \"settings.review-workflows.workflow.stageRequiredToPublish.any\": \"Any stage\",\n    \"settings.review-workflows.workflow.stageRequiredToPublish.hint\": \"Prevents entries from being published if they are not at the required stage.\",\n    \"settings.page.purchase.description\": \"Manage your content review process\",\n    \"settings.page.purchase.perks1\": \"Customizable review stages\",\n    \"settings.page.purchase.perks2\": \"Manage user permissions\",\n    \"settings.page.purchase.perks3\": \"Support for webhooks\"\n};\n\nexport { en as default };\n//# sourceMappingURL=en.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,KAAK;AAAA,EACL,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,qCAAqC;AAAA,EACrC,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,mEAAmE;AAAA,EACnE,iEAAiE;AAAA,EACjE,kEAAkE;AAAA,EAClE,sCAAsC;AAAA,EACtC,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,iCAAiC;AACrC;", "names": []}