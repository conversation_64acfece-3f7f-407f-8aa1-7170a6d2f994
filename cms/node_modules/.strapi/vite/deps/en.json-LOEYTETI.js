import "./chunk-PLDDJCW6.js";

// node_modules/@strapi/plugin-cloud/dist/admin/translations/en.json.mjs
var en = {
  "Plugin.name": "Deploy",
  "Homepage.title": "Fully-managed Cloud Hosting for your Strapi Project",
  "Homepage.subTitle": "Follow this 2 steps process to get Everything You Need to Run Strapi in Production.",
  "Homepage.githubBox.title.versioned": "Project pushed to GitHub",
  "Homepage.githubBox.title.not-versioned": "Push your project on GitHub",
  "Homepage.githubBox.subTitle.versioned": "You did it! You're just one step ahead of having your project hosted online.",
  "Homepage.githubBox.subTitle.not-versioned": "Your project has to be versioned on GitHub before deploying on Strapi Cloud.",
  "Homepage.githubBox.buttonText": "Upload to GitHub",
  "Homepage.cloudBox.title": "Deploy to Strapi Cloud",
  "Homepage.cloudBox.subTitle": "Enjoy a Strapi-optimized stack including database, email provider, and CDN.",
  "Homepage.cloudBox.buttonText": "Deploy to Strapi Cloud",
  "Homepage.textBox.label.versioned": "Try Strapi Cloud for Free!",
  "Homepage.textBox.label.not-versioned": "Why uploading my project to GitHub?",
  "Homepage.textBox.text.versioned": "Strapi Cloud offers a 14 days free trial for you to experiment with your project on the cloud including all features.",
  "Homepage.textBox.text.not-versioned": "Strapi Cloud will fetch and deploy your project from your GitHub repository. This is the best way to version, manage and deploy your project. Follow the steps on GitHub to successfully upload it."
};
export {
  en as default
};
//# sourceMappingURL=en.json-LOEYTETI.js.map
