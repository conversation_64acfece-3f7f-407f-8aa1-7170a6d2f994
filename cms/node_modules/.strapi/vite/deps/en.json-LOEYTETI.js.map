{"version": 3, "sources": ["../../../@strapi/plugin-cloud/dist/admin/translations/en.json.mjs"], "sourcesContent": ["var en = {\n    \"Plugin.name\": \"Deploy\",\n    \"Homepage.title\": \"Fully-managed Cloud Hosting for your Strapi Project\",\n    \"Homepage.subTitle\": \"Follow this 2 steps process to get Everything You Need to Run Strapi in Production.\",\n    \"Homepage.githubBox.title.versioned\": \"Project pushed to GitHub\",\n    \"Homepage.githubBox.title.not-versioned\": \"Push your project on GitHub\",\n    \"Homepage.githubBox.subTitle.versioned\": \"You did it! You're just one step ahead of having your project hosted online.\",\n    \"Homepage.githubBox.subTitle.not-versioned\": \"Your project has to be versioned on GitHub before deploying on Strapi Cloud.\",\n    \"Homepage.githubBox.buttonText\": \"Upload to GitHub\",\n    \"Homepage.cloudBox.title\": \"Deploy to Strapi Cloud\",\n    \"Homepage.cloudBox.subTitle\": \"Enjoy a Strapi-optimized stack including database, email provider, and CDN.\",\n    \"Homepage.cloudBox.buttonText\": \"Deploy to Strapi Cloud\",\n    \"Homepage.textBox.label.versioned\": \"Try Strapi Cloud for Free!\",\n    \"Homepage.textBox.label.not-versioned\": \"Why uploading my project to GitHub?\",\n    \"Homepage.textBox.text.versioned\": \"Strapi Cloud offers a 14 days free trial for you to experiment with your project on the cloud including all features.\",\n    \"Homepage.textBox.text.not-versioned\": \"Strapi Cloud will fetch and deploy your project from your GitHub repository. This is the best way to version, manage and deploy your project. Follow the steps on GitHub to successfully upload it.\"\n};\n\nexport { en as default };\n//# sourceMappingURL=en.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,KAAK;AAAA,EACL,eAAe;AAAA,EACf,kBAAkB;AAAA,EAClB,qBAAqB;AAAA,EACrB,sCAAsC;AAAA,EACtC,0CAA0C;AAAA,EAC1C,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,iCAAiC;AAAA,EACjC,2BAA2B;AAAA,EAC3B,8BAA8B;AAAA,EAC9B,gCAAgC;AAAA,EAChC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,mCAAmC;AAAA,EACnC,uCAAuC;AAC3C;", "names": []}