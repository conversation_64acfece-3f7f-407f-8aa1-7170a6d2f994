{"version": 3, "sources": ["../../../@strapi/i18n/dist/admin/translations/en.json.mjs"], "sourcesContent": ["var en = {\n    \"actions.delete.label\": \"Delete entry ({locale})\",\n    \"actions.delete.dialog.title\": \"Confirmation\",\n    \"actions.delete.dialog.body\": \"Are you sure you want to delete this locale?\",\n    \"actions.delete.error\": \"An error occurred while trying to delete the document locale.\",\n    \"CMEditViewCopyLocale.copy-failure\": \"Failed to copy locale\",\n    \"CMEditViewCopyLocale.copy-success\": \"Locale copied\",\n    \"CMEditViewCopyLocale.copy-text\": \"Fill in from another locale\",\n    \"CMEditViewCopyLocale.cancel-text\": \"No, cancel\",\n    \"CMEditViewCopyLocale.submit-text\": \"Yes, fill in\",\n    \"CMEditViewCopyLocale.dialog.title\": \"Confirmation\",\n    \"CMEditViewCopyLocale.dialog.body\": \"Your current content will be erased and filled by the content of the selected locale:\",\n    \"CMEditViewCopyLocale.dialog.field.label\": \"Locale\",\n    \"CMEditViewCopyLocale.dialog.field.placeholder\": \"Select one locale...\",\n    \"CMEditViewBulkLocale.publish-title\": \"Publish multiple locales\",\n    \"CMEditViewBulkLocale.unpublish-title\": \"Unpublish multiple locales\",\n    \"CMEditViewBulkLocale.status\": \"Status\",\n    \"CMEditViewBulkLocale.publication-status\": \"Publication Status\",\n    \"CMEditViewBulkLocale.draft-relation-warning\": \"Some locales are related to draft entries. Publishing them could leave broken links in your app.\",\n    \"CMEditViewBulkLocale.continue-confirmation\": \"Are you sure you want to continue?\",\n    \"CMEditViewLocalePicker.locale.create\": \"Create <bold>{locale}</bold> locale\",\n    \"CMListView.popover.display-locales.label\": \"Display translated locales\",\n    \"CheckboxConfirmation.Modal.body\": \"Do you want to disable it?\",\n    \"CheckboxConfirmation.Modal.button-confirm\": \"Yes, disable\",\n    \"CheckboxConfirmation.Modal.content\": \"Disabling localization will engender the deletion of all your content but the one associated to your default locale (if existing).\",\n    \"Field.localized\": \"This value is unique for the selected locale\",\n    \"Field.not-localized\": \"This value is common to all locales\",\n    \"Settings.list.actions.add\": \"Add new locale\",\n    \"Settings.list.actions.delete\": \"Delete {name} locale\",\n    \"Settings.list.actions.deleteAdditionalInfos\": \"This will delete the active locale versions <em>(from Internationalization)</em>\",\n    \"Settings.list.actions.publishAdditionalInfos\": \"This will publish the active locale versions <em>(from Internationalization)</em>\",\n    \"Settings.list.actions.unpublishAdditionalInfos\": \"This will unpublish the active locale versions <em>(from Internationalization)</em>\",\n    \"Settings.list.actions.edit\": \"Edit {name} locale\",\n    \"Settings.list.description\": \"Configure the settings for the Internationalization plugin\",\n    \"Settings.list.empty.description\": \"This is not a usual behavior, meaning that you have eventually modified the database manually. Make sure to have at least one locale saved in your database in order to be able to use Strapi correctly.\",\n    \"Settings.list.empty.title\": \"There are no locales.\",\n    \"Settings.locales.default\": \"Default\",\n    \"Settings.locales.list.sort.default\": \"Sort by the default locale\",\n    \"Settings.locales.list.sort.displayName\": \"Sort by display name\",\n    \"Settings.locales.list.sort.id\": \"Sort by ID\",\n    \"Settings.locales.modal.advanced\": \"Advanced settings\",\n    \"Settings.locales.modal.advanced.setAsDefault\": \"Set as default locale\",\n    \"Settings.locales.modal.advanced.setAsDefault.hint\": \"One default locale is required, change it by selecting another one\",\n    \"Settings.locales.modal.advanced.settings\": \"Settings\",\n    \"Settings.locales.modal.base\": \"Basic settings\",\n    \"Settings.locales.modal.create.success\": \"Locale successfully added\",\n    \"Settings.locales.modal.create.code.label\": \"Locales\",\n    \"Settings.locales.modal.create.code.error\": \"Please select a locale\",\n    \"Settings.locales.modal.create.name.description\": \"Locale will be displayed under that name in the administration panel\",\n    \"Settings.locales.modal.create.name.label\": \"Locale display name\",\n    \"Settings.locales.modal.create.name.error.min\": \"The locale display name can only be less than 50 characters.\",\n    \"Settings.locales.modal.create.name.error.required\": \"Please give the locale a display name\",\n    \"Settings.locales.modal.delete.confirm\": \"Yes, delete\",\n    \"Settings.locales.modal.delete.message\": \"Deleting this locale will delete all associated content. If you want to keep some content, make sure to reallocate it to another locale first.\",\n    \"Settings.locales.modal.delete.secondMessage\": \"Do you want to delete this locale?\",\n    \"Settings.locales.modal.delete.success\": \"Locale successfully deleted\",\n    \"Settings.locales.modal.edit.confirmation\": \"Finish\",\n    \"Settings.locales.modal.edit.success\": \"Locale successfully edited\",\n    \"Settings.locales.modal.edit.tab.label\": \"Navigating between the I18N basic settings and advanced settings\",\n    \"Settings.locales.modal.title\": \"Configuration\",\n    \"Settings.locales.row.default-locale\": \"Default locale\",\n    \"Settings.locales.row.displayName\": \"Display name\",\n    \"Settings.locales.row.id\": \"ID\",\n    \"Settings.permissions.loading\": \"Loading permissions\",\n    \"Settings.permissions.read.denied.description\": \"In order to be able to read this, make sure to get in touch with the administrator of your system.\",\n    \"Settings.permissions.read.denied.title\": \"You don't have the permissions to access this content.\",\n    \"actions.select-locale\": \"Select a locale\",\n    \"components.Select.locales.not-available\": \"No content available\",\n    \"plugin.description.long\": \"This plugin enables to create, to read and to update content in different languages, both from the Admin Panel and from the API.\",\n    \"plugin.description.short\": \"This plugin enables to create, to read and to update content in different languages, both from the Admin Panel and from the API.\",\n    \"plugin.name\": \"Internationalization\",\n    \"plugin.schema.i18n.localized.description-content-type\": \"Allows translating an entry into different languages\",\n    \"plugin.schema.i18n.localized.description-field\": \"The field can have different values in each language\",\n    \"plugin.schema.i18n.localized.label-content-type\": \"Internationalization\",\n    \"plugin.schema.i18n.localized.label-field\": \"Enable localization for this field\"\n};\n\nexport { en as default };\n//# sourceMappingURL=en.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,KAAK;AAAA,EACL,wBAAwB;AAAA,EACxB,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,2CAA2C;AAAA,EAC3C,iDAAiD;AAAA,EACjD,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,+BAA+B;AAAA,EAC/B,2CAA2C;AAAA,EAC3C,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,wCAAwC;AAAA,EACxC,4CAA4C;AAAA,EAC5C,mCAAmC;AAAA,EACnC,6CAA6C;AAAA,EAC7C,sCAAsC;AAAA,EACtC,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,+CAA+C;AAAA,EAC/C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,8BAA8B;AAAA,EAC9B,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,4BAA4B;AAAA,EAC5B,sCAAsC;AAAA,EACtC,0CAA0C;AAAA,EAC1C,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,gDAAgD;AAAA,EAChD,qDAAqD;AAAA,EACrD,4CAA4C;AAAA,EAC5C,+BAA+B;AAAA,EAC/B,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,gDAAgD;AAAA,EAChD,qDAAqD;AAAA,EACrD,yCAAyC;AAAA,EACzC,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,yCAAyC;AAAA,EACzC,gCAAgC;AAAA,EAChC,uCAAuC;AAAA,EACvC,oCAAoC;AAAA,EACpC,2BAA2B;AAAA,EAC3B,gCAAgC;AAAA,EAChC,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,yBAAyB;AAAA,EACzB,2CAA2C;AAAA,EAC3C,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,eAAe;AAAA,EACf,yDAAyD;AAAA,EACzD,kDAAkD;AAAA,EAClD,mDAAmD;AAAA,EACnD,4CAA4C;AAChD;", "names": []}