{"version": 3, "sources": ["../../../@strapi/email/dist/admin/translations/en.json.mjs"], "sourcesContent": ["var link = \"Link\";\nvar en = {\n    link: link,\n    \"Settings.email.plugin.button.test-email\": \"Send test email\",\n    \"Settings.email.plugin.label.defaultFrom\": \"Default sender email\",\n    \"Settings.email.plugin.label.defaultReplyTo\": \"Default response email\",\n    \"Settings.email.plugin.label.provider\": \"Email provider\",\n    \"Settings.email.plugin.label.testAddress\": \"Recipient email\",\n    \"Settings.email.plugin.notification.config.error\": \"Failed to retrieve the email config\",\n    \"Settings.email.plugin.notification.data.loaded\": \"Email settings data has been loaded\",\n    \"Settings.email.plugin.notification.test.error\": \"Failed to send a test mail to {to}\",\n    \"Settings.email.plugin.notification.test.success\": \"Email test succeeded, check the {to} mailbox\",\n    \"Settings.email.plugin.placeholder.defaultFrom\": \"ex: Strapi No-Reply <<EMAIL>>\",\n    \"Settings.email.plugin.placeholder.defaultReplyTo\": \"ex: Strapi <<EMAIL>>\",\n    \"Settings.email.plugin.placeholder.testAddress\": \"ex: <EMAIL>\",\n    \"Settings.email.plugin.subTitle\": \"Test the settings for the Email plugin\",\n    \"Settings.email.plugin.text.configuration\": \"The plugin is configured through the {file} file, checkout this {link} for the documentation.\",\n    \"Settings.email.plugin.title\": \"Configuration\",\n    \"Settings.email.plugin.title.config\": \"Configuration\",\n    \"Settings.email.plugin.title.test\": \"Test email delivery\",\n    \"SettingsNav.link.settings\": \"Settings\",\n    \"SettingsNav.section-label\": \"Email plugin\",\n    \"components.Input.error.validation.email\": \"This is not a valid email\"\n};\n\nexport { en as default, link };\n//# sourceMappingURL=en.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,OAAO;AACX,IAAI,KAAK;AAAA,EACL;AAAA,EACA,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,wCAAwC;AAAA,EACxC,2CAA2C;AAAA,EAC3C,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,iDAAiD;AAAA,EACjD,mDAAmD;AAAA,EACnD,iDAAiD;AAAA,EACjD,oDAAoD;AAAA,EACpD,iDAAiD;AAAA,EACjD,kCAAkC;AAAA,EAClC,4CAA4C;AAAA,EAC5C,+BAA+B;AAAA,EAC/B,sCAAsC;AAAA,EACtC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,2CAA2C;AAC/C;", "names": []}