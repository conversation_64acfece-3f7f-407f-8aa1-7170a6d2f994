import "./chunk-PLDDJCW6.js";

// node_modules/@strapi/content-manager/dist/admin/translations/en.json.mjs
var groups = "Groups";
var models = "Collection Types";
var pageNotFound = "Page not found";
var en = {
  "App.schemas.data-loaded": "The schemas have been successfully loaded",
  "actions.clone.error": "An error occurred while trying to clone the document.",
  "actions.clone.label": "Duplicate",
  "actions.delete.dialog.body": "Are you sure you want to delete this document? This action is irreversible.",
  "actions.delete.error": "An error occurred while trying to delete the document.",
  "actions.delete.label": "Delete entry{isLocalized, select, true { (all locales)} other {}}",
  "actions.discard.label": "Discard changes",
  "actions.discard.dialog.body": "Are you sure you want to discard the changes? This action is irreversible.",
  "actions.edit.error": "An error occurred while trying to edit the document.",
  "actions.edit.label": "Edit",
  "actions.unpublish.error": "An error occurred while trying to unpublish the document.",
  "actions.unpublish.dialog.body": "Are you sure you want to unpublish this?",
  "actions.unpublish.dialog.option.keep-draft": "Unpublish and keep last draft",
  "actions.unpublish.dialog.option.replace-draft": "Unpublish and replace last draft",
  "ListViewTable.relation-loaded": "Relations have been loaded",
  "ListViewTable.relation-loading": "Relations are loading",
  "ListViewTable.relation-more": "This relation contains more entities than displayed",
  "EditRelations.title": "Relational data",
  "HeaderLayout.button.label-add-entry": "Create new entry",
  "api.id": "API ID",
  "apiError.This attribute must be unique": "{field} must be unique",
  "components.AddFilterCTA.add": "Filters",
  "components.AddFilterCTA.hide": "Filters",
  "components.DragHandle-label": "Drag",
  "components.DraggableAttr.edit": "Click to edit",
  "components.DraggableCard.delete.field": "Delete {item}",
  "components.DraggableCard.edit.field": "Edit {item}",
  "components.DraggableCard.move.field": "Move {item}",
  "components.ListViewTable.row-line": "item line {number}",
  "components.DynamicZone.ComponentPicker-label": "Pick one component",
  "components.DynamicZone.add-component": "Add a component to {componentName}",
  "components.DynamicZone.delete-label": "Delete {name}",
  "components.DynamicZone.error-message": "The component contains error(s)",
  "components.DynamicZone.missing-components": "There {number, plural, =0 {are # missing components} one {is # missing component} other {are # missing components}}",
  "components.DynamicZone.extra-components": "There {number, plural, =0 {are # extra components} one {is # extra component} other {are # extra components}}",
  "components.DynamicZone.move-down-label": "Move component down",
  "components.DynamicZone.move-up-label": "Move component up",
  "components.DynamicZone.pick-compo": "Pick one component",
  "components.DynamicZone.required": "Component is required",
  "components.EmptyAttributesBlock.button": "Go to settings page",
  "components.EmptyAttributesBlock.description": "You can change your settings",
  "components.FieldItem.linkToComponentLayout": "Set the component's layout",
  "components.FieldSelect.label": "Add a field",
  "components.FilterOptions.button.apply": "Apply",
  "components.FiltersPickWrapper.PluginHeader.actions.apply": "Apply",
  "components.FiltersPickWrapper.PluginHeader.actions.clearAll": "Clear all",
  "components.FiltersPickWrapper.PluginHeader.description": "Set the conditions to apply to filter the entries",
  "components.FiltersPickWrapper.PluginHeader.title.filter": "Filters",
  "components.FiltersPickWrapper.hide": "Hide",
  "components.Filters.usersSelect.label": "Search and select a user to filter by",
  "components.LeftMenu.Search.label": "Search for a content type",
  "components.LeftMenu.collection-types": "Collection Types",
  "components.LeftMenu.single-types": "Single Types",
  "components.LimitSelect.itemsPerPage": "Items per page",
  "components.NotAllowedInput.text": "No permissions to see this field",
  "components.RelationInput.icon-button-aria-label": "Drag",
  "components.RelationInputModal.modal-title": "Edit a relation",
  "components.RelationInputModal.button-fullpage": "Go to entry",
  "components.RelationInputModal.confirmation-message": "Some changes were not saved. Are you sure you want to close this relation? All changes that were not saved will be lost.",
  "components.RepeatableComponent.error-message": "The component(s) contain error(s)",
  "components.Search.placeholder": "Search for an entry...",
  "components.Select.draft-info-title": "Draft",
  "components.Select.publish-info-title": "Published",
  "components.SettingsViewWrapper.pluginHeader.description.edit-settings": "Customize how the edit view will look like.",
  "components.SettingsViewWrapper.pluginHeader.description.list-settings": "Define the settings of the list view.",
  "components.SettingsViewWrapper.pluginHeader.title": "Configure the view — {name}",
  "bulk-publish.already-published": "Already Published",
  "bulk-unpublish.already-unpublished": "Already Unpublished",
  "bulk-publish.modified": "Ready to publish changes",
  "bulk-publish.waiting-for-action": "Waiting for action",
  "components.TableDelete.delete": "Delete all",
  "components.TableDelete.deleteSelected": "Delete selected",
  "components.TableDelete.label": "{number, plural, one {# entry} other {# entries}} selected",
  "components.TableEmpty.withFilters": "There are no {contentType} with the applied filters...",
  "components.TableEmpty.withSearch": "There are no {contentType} corresponding to the search ({search})...",
  "components.TableEmpty.withoutFilter": "There are no {contentType}...",
  "components.empty-repeatable": "No entry yet. Click to add one.",
  "components.notification.info.maximum-requirement": "You have already reached the maximum number of fields",
  "components.notification.info.minimum-requirement": "A field has been added to match the minimum requirement",
  "components.repeatable.reorder.error": "An error occurred while reordering your component's field, please try again",
  "components.reset-entry": "Reset entry",
  "components.uid.apply": "apply",
  "components.uid.available": "Available",
  "components.uid.regenerate": "Regenerate",
  "components.uid.suggested": "suggested",
  "components.uid.unavailable": "Unavailable",
  "containers.edit.tabs.label": "Document status",
  "containers.edit.tabs.draft": "draft",
  "containers.edit.tabs.published": "published",
  "containers.edit.panels.default.title": "Entry",
  "containers.edit.panels.default.more-actions": "More document actions",
  "containers.Edit.delete": "Delete",
  "containers.edit.title.new": "Create an entry",
  "containers.edit.header.more-actions": "More actions",
  "containers.edit.information.last-published.label": "Published",
  "containers.edit.information.last-published.value": "{time}{isAnonymous, select, true {} other { by {author}}}",
  "containers.edit.information.last-draft.label": "Updated",
  "containers.edit.information.last-draft.value": "{time}{isAnonymous, select, true {} other { by {author}}}",
  "containers.edit.information.document.label": "Created",
  "containers.edit.information.document.value": "{time}{isAnonymous, select, true {} other { by {author}}}",
  "containers.EditSettingsView.modal-form.edit-field": "Edit the field",
  "containers.EditView.add.new-entry": "Add an entry",
  "containers.EditView.notification.errors": "The form contains some errors",
  "containers.Home.introduction": "To edit your entries go to the specific link in the left menu. This plugin doesn't have a proper way to edit settings and it's still under active development.",
  "containers.Home.pluginHeaderDescription": "Manage your entries through a powerful and beautiful interface.",
  "containers.Home.pluginHeaderTitle": "Content Manager",
  "containers.List.draft": "Draft",
  "containers.List.published": "Published",
  "containers.List.modified": "Modified",
  "containers.list.displayedFields": "Displayed Fields",
  "containers.list.items": "{number} {number, plural, =0 {items} one {item} other {items}}",
  "containers.list.table.row-actions": "Row actions",
  "containers.list.selectedEntriesModal.title": "Publish entries",
  "containers.list.selectedEntriesModal.selectedCount.publish": "<b>{publishedCount}</b> {publishedCount, plural, =0 {entries} one {entry} other {entries}} already published. <b>{draftCount}</b> {draftCount, plural, =0 {entries} one {entry} other {entries}} ready to publish. <b>{withErrorsCount}</b> {withErrorsCount, plural, =0 {entries} one {entry} other {entries}} waiting for action.",
  "containers.list.selectedEntriesModal.selectedCount.unpublish": "<b>{draftCount}</b> {draftCount, plural, =0 {entries} one {entry} other {entries}} already unpublished. <b>{publishedCount}</b> {publishedCount, plural, =0 {entries} one {entry} other {entries}} ready to unpublish.",
  "containers.list.autoCloneModal.header": "Duplicate",
  "containers.list.autoCloneModal.title": "This entry can't be duplicated directly.",
  "containers.list.autoCloneModal.description": "A new entry will be created with the same content, but you'll have to change the following fields to save it.",
  "containers.list.autoCloneModal.create": "Create",
  "containers.list.autoCloneModal.error.unique": "Identical values in a unique field are not allowed.",
  "containers.list.autoCloneModal.error.relation": "Duplicating the relation could remove it from the original entry.",
  "containers.list-settings.modal-form.label": "Edit {fieldName}",
  "containers.list-settings.modal-form.error": "An error occurred while trying to open the form.",
  "containers.edit-settings.modal-form.error": "An error occurred while trying to open the form.",
  "containers.edit-settings.modal-form.label": "Label",
  "containers.edit-settings.modal-form.description": "Description",
  "containers.edit-settings.modal-form.placeholder": "Placeholder",
  "containers.edit-settings.modal-form.mainField": "Entry title",
  "containers.edit-settings.modal-form.mainField.hint": "Set the displayed field in both the edit and list views",
  "containers.edit-settings.modal-form.editable": "Editable field",
  "containers.edit-settings.modal-form.size": "Size",
  "containers.SettingPage.add.field": "Insert another field",
  "containers.SettingPage.add.relational-field": "Insert another related field",
  "containers.SettingPage.attributes": "Attributes fields",
  "containers.SettingPage.attributes.description": "Define the order of the attributes",
  "containers.SettingPage.editSettings.description": "Drag & drop the fields to build the layout",
  "containers.SettingPage.editSettings.entry.title": "Entry title",
  "containers.SettingPage.editSettings.entry.title.description": "Set the displayed field of your entry",
  "containers.SettingPage.editSettings.relation-field.description": "Set the displayed field in both the edit and list views",
  "containers.SettingPage.editSettings.title": "Edit view (settings)",
  "containers.SettingPage.layout": "Layout",
  "containers.SettingPage.listSettings.description": "Configure the options for this Collection Type",
  "containers.SettingPage.listSettings.title": "List view (settings)",
  "containers.SettingPage.pluginHeaderDescription": "Configure the specific settings for this Collection Type",
  "containers.SettingPage.relations": "Related fields",
  "containers.SettingPage.settings": "Settings",
  "containers.SettingPage.view": "View",
  "containers.SettingViewModel.pluginHeader.title": "Content Manager — {name}",
  "containers.SettingsPage.Block.contentType.description": "Configure the specific settings",
  "containers.SettingsPage.Block.contentType.title": "Collection Types",
  "containers.SettingsPage.Block.generalSettings.description": "Configure the default options for your Collection Types",
  "containers.SettingsPage.Block.generalSettings.title": "General",
  "containers.SettingsPage.pluginHeaderDescription": "Configure the settings for all your Collection Types and Groups",
  "containers.SettingsView.list.subtitle": "Configure the layout and display of your Collection Types and Groups",
  "containers.SettingsView.list.title": "Display configurations",
  "containers.untitled": "Untitled",
  "dnd.cancel-item": "{item}, dropped. Re-order cancelled.",
  "dnd.drop-item": "{item}, dropped. Final position in list: {position}.",
  "dnd.grab-item": "{item}, grabbed. Current position in list: {position}. Press up and down arrow to change position, Spacebar to drop, Escape to cancel.",
  "dnd.instructions": "Press spacebar to grab and re-order",
  "dnd.reorder": "{item}, moved. New position in list: {position}.",
  "edit-settings-view.link-to-ctb.components": "Edit the component",
  "edit-settings-view.link-to-ctb.content-types": "Edit the content type",
  "emptyAttributes.button": "Go to Collection Type builder",
  "emptyAttributes.description": "Add your first field to your Collection Type",
  "emptyAttributes.title": "There are no fields yet",
  "error.attribute.key.taken": "This value already exists",
  "error.attribute.sameKeyAndName": "Can't be equals",
  "error.attribute.taken": "This field name already exists",
  "error.contentTypeName.taken": "This name already exists",
  "error.model.fetch": "An error occurred during models config fetch.",
  "error.record.create": "An error occurred during record creation.",
  "error.record.delete": "An error occurred during record deletion.",
  "error.record.fetch": "An error occurred during record fetch.",
  "error.record.update": "An error occurred during record update.",
  "error.records.count": "An error occurred during count records fetch.",
  "error.records.fetch": "An error occurred during records fetch.",
  "error.records.fetch-draft-relatons": "An error occurred while fetching draft relations on this document.",
  "error.schema.generation": "An error occurred during schema generation.",
  "error.validation.json": "This is not a JSON",
  "error.validation.max": "The value is too high (max: {max}).",
  "error.validation.maxLength": "The value is too long (max: {max}).",
  "error.validation.min": "The value is too low (min: {min}).",
  "error.validation.minLength": "The value is too short (min: {min}).",
  "error.validation.minSupMax": "Can't be superior",
  "error.validation.regex": "The value does not match the regex.",
  "error.validation.required": "This value input is required.",
  "form.Input.bulkActions": "Enable bulk actions",
  "form.Input.defaultSort": "Default sort attribute",
  "form.Input.description": "Description",
  "form.Input.description.placeholder": "Display name in the profile",
  "form.Input.editable": "Editable field",
  "form.Input.filters": "Enable filters",
  "form.Input.hint.character.unit": "{maxValue, plural, one { character} other { characters}}",
  "form.Input.hint.minMaxDivider": " / ",
  "form.Input.hint.text": "{min, select, undefined {} other {min. {min}}}{divider}{max, select, undefined {} other {max. {max}}}{unit}{br}{description}",
  "form.Input.label": "Label",
  "form.Input.label.inputDescription": "This value overrides the label displayed in the table's head",
  "form.Input.pageEntries": "Entries per page",
  "form.Input.pageEntries.inputDescription": "Note: You can override this value in the Collection Type settings page.",
  "form.Input.placeholder": "Placeholder",
  "form.Input.placeholder.placeholder": "My awesome value",
  "form.Input.search": "Enable search",
  "form.Input.search.field": "Enable search on this field",
  "form.Input.sort.field": "Enable sort on this field",
  "form.Input.sort.order": "Default sort order",
  "form.Input.wysiwyg": "Display as WYSIWYG",
  "global.displayedFields": "Displayed Fields",
  groups,
  "groups.numbered": "Groups ({number})",
  "header.name": "Content Manager",
  "link-to-ctb": "Edit the model",
  models,
  "models.numbered": "Collection Types ({number})",
  "notification.error.displayedFields": "You need at least one displayed field",
  "notification.error.relationship.fetch": "An error occurred during relationship fetch.",
  "notification.info.SettingPage.disableSort": "You need to have one attribute with the sorting allowed",
  "notification.info.minimumFields": "You need to have at least one field displayed",
  "notification.upload.error": "An error has occurred while uploading your files",
  pageNotFound,
  "pages.ListView.header-subtitle": "{number, plural, =0 {# entries} one {# entry} other {# entries}} found",
  "pages.NoContentType.button": "Create your first Content-Type",
  "pages.NoContentType.text": "You don't have any content yet, we recommend you to create your first Content-Type.",
  "permissions.not-allowed.create": "You are not allowed to create a document",
  "permissions.not-allowed.update": "You are not allowed to see this document",
  "plugin.description.long": "Quick way to see, edit and delete the data in your database.",
  "plugin.description.short": "Quick way to see, edit and delete the data in your database.",
  "popUpWarning.bodyMessage.contentType.delete": "Are you sure to delete Content-Type?",
  "popUpWarning.bodyMessage.contentType.delete.all": "Are you sure you want to delete these entries?",
  "popUpWarning.bodyMessage.contentType.publish.all": "Are you sure you want to publish these entries?",
  "popUpWarning.bodyMessage.contentType.unpublish.all": "Are you sure you want to unpublish these entries?",
  "popUpWarning.warning.has-draft-relations.title": "Confirmation",
  "popUpWarning.warning.has-draft-relations.message": "This entry is related to {count, plural, one {# draft entry} other {# draft entries}}. Publishing it could leave broken links in your app.",
  "popUpwarning.warning.has-draft-relations.button-confirm": "Yes, publish",
  "popUpwarning.warning.bulk-has-draft-relations.message": "<b>{count} {count, plural, one { relation } other { relations } } out of {entities} { entities, plural, one { entry } other { entries } } {count, plural, one { is } other { are } }</b> not published yet and might lead to unexpected behavior. ",
  "popUpWarning.warning.publish-question": "Do you still want to publish?",
  "popUpWarning.warning.unpublish": "If you don't publish this content, it will automatically turn into a Draft.",
  "popUpWarning.warning.unpublish-question": "Are you sure you don't want to publish it?",
  "popUpWarning.warning.updateAllSettings": "This will modify all your settings",
  "popover.display-relations.label": "Display relations",
  "preview.panel.title": "Preview",
  "preview.panel.button": "Open preview",
  "preview.panel.button-disabled-tooltip": "Please save to open the preview",
  "preview.page-title": "{contentType} preview",
  "preview.header.close": "Close preview",
  "preview.copy.label": "Copy preview link",
  "preview.copy.success": "Copied preview link",
  "preview.tabs.label": "Preview status",
  "preview.content.close-editor": "Close editor",
  "preview.content.open-editor": "Open editor",
  "relation.create": "Create a relation",
  "relation.add": "Add or create a relation",
  "relation.disconnect": "Remove",
  "relation.error-adding-relation": "An error occurred while trying to add the relation.",
  "relation.isLoading": "Relations are loading",
  "relation.loadMore": "Load More",
  "relation.notAvailable": "No relations available",
  "relation.publicationState.draft": "Draft",
  "relation.publicationState.published": "Published",
  "reviewWorkflows.stage.label": "Review stage",
  "select.currently.selected": "{count} currently selected",
  "success.record.clone": "Cloned document",
  "success.record.discard": "Changes discarded",
  "success.record.delete": "Deleted document",
  "success.record.publish": "Published document",
  "success.record.publishing": "Publishing...",
  "success.record.save": "Saved document",
  "success.record.unpublish": "Unpublished document",
  "success.records.delete": "Successfully deleted.",
  "success.records.unpublish": "Successfully unpublished.",
  "success.records.publish": "Successfully published.",
  "utils.data-loaded": "The {number, plural, =1 {entry has} other {entries have}} successfully been loaded",
  "listView.validation.errors.title": "Action required",
  "listView.validation.errors.message": "Please make sure all fields are valid before publishing (required field, min/max character limit, etc.)",
  "history.document-action": "Content History",
  "history.page-title": "{contentType} history",
  "history.sidebar.title": "Versions",
  "history.sidebar.version-card.aria-label": "Version card",
  "history.sidebar.versionDescription": "{distanceToNow}{isAnonymous, select, true {} other { by {author}}}{isCurrent, select, true { <b>(current)</b>} other {}}",
  "history.sidebar.show-newer": "Show newer versions",
  "history.sidebar.show-older": "Show older versions",
  "history.version.subtitle": "{hasLocale, select, true {{subtitle}, in {locale}} other {{subtitle}}}",
  "history.content.new-field.title": "New field",
  "history.content.new-field.message": "This field didn't exist when this version was saved. If you restore this version, it will be empty.",
  "history.content.unknown-fields.title": "Unknown fields",
  "history.content.unknown-fields.message": "These fields have been deleted or renamed in the Content-Type Builder. <b>These fields will not be restored.</b>",
  "history.content.missing-assets.title": "{number, plural, =1 {Missing asset} other {{number} missing assets}}",
  "history.content.missing-assets.message": "{number, plural, =1 {It has} other {They have}} been deleted in the Media Library and can't be restored.",
  "history.content.missing-relations.title": "{number, plural, =1 {Missing relation} other {{number} missing relations}}",
  "history.content.missing-relations.message": "{number, plural, =1 {It has} other {They have}} been deleted and can't be restored.",
  "history.content.no-relations": "No relations.",
  "history.content.localized": "This value is specific to this locale. If you restore this version, the content will not be replaced for other locales.",
  "history.content.not-localized": "This value is common to all locales. If you restore this version, the content will be replaced for all locales.",
  "history.restore.confirm.button": "Restore",
  "history.restore.confirm.title": "Are you sure you want to restore this version?",
  "history.restore.confirm.message": "{isDraft, select, true {The restored content will override your draft.} other {The restored content won't be published, it will override the draft and be saved as pending changes. You'll be able to publish the changes at anytime.}}",
  "history.restore.success.title": "Version restored.",
  "history.restore.success.message": "A past version of the content was restored.",
  "history.restore.error.message": "Could not restore version.",
  "validation.error": "There are validation errors in your document. Please fix them before saving.",
  "validation.error.unreadable-required-field": "Your current permissions prevent access to certain required fields. Please request access from an administrator to proceed.",
  "bulk-publish.edit": "Edit",
  "widget.last-edited.title": "Last edited entries",
  "widget.last-edited.single-type": "Single-Type",
  "widget.last-edited.no-data": "No edited entries",
  "widget.last-published.title": "Last published entries",
  "widget.last-published.no-data": "No published entries"
};
export {
  en as default,
  groups,
  models,
  pageNotFound
};
//# sourceMappingURL=en.json-SJX4EWDG.js.map
