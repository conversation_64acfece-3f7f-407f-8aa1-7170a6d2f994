{"version": 3, "sources": ["../../../@strapi/content-manager/dist/admin/translations/en.json.mjs"], "sourcesContent": ["var groups = \"Groups\";\nvar models = \"Collection Types\";\nvar pageNotFound = \"Page not found\";\nvar en = {\n    \"App.schemas.data-loaded\": \"The schemas have been successfully loaded\",\n    \"actions.clone.error\": \"An error occurred while trying to clone the document.\",\n    \"actions.clone.label\": \"Duplicate\",\n    \"actions.delete.dialog.body\": \"Are you sure you want to delete this document? This action is irreversible.\",\n    \"actions.delete.error\": \"An error occurred while trying to delete the document.\",\n    \"actions.delete.label\": \"Delete entry{isLocalized, select, true { (all locales)} other {}}\",\n    \"actions.discard.label\": \"Discard changes\",\n    \"actions.discard.dialog.body\": \"Are you sure you want to discard the changes? This action is irreversible.\",\n    \"actions.edit.error\": \"An error occurred while trying to edit the document.\",\n    \"actions.edit.label\": \"Edit\",\n    \"actions.unpublish.error\": \"An error occurred while trying to unpublish the document.\",\n    \"actions.unpublish.dialog.body\": \"Are you sure you want to unpublish this?\",\n    \"actions.unpublish.dialog.option.keep-draft\": \"Unpublish and keep last draft\",\n    \"actions.unpublish.dialog.option.replace-draft\": \"Unpublish and replace last draft\",\n    \"ListViewTable.relation-loaded\": \"Relations have been loaded\",\n    \"ListViewTable.relation-loading\": \"Relations are loading\",\n    \"ListViewTable.relation-more\": \"This relation contains more entities than displayed\",\n    \"EditRelations.title\": \"Relational data\",\n    \"HeaderLayout.button.label-add-entry\": \"Create new entry\",\n    \"api.id\": \"API ID\",\n    \"apiError.This attribute must be unique\": \"{field} must be unique\",\n    \"components.AddFilterCTA.add\": \"Filters\",\n    \"components.AddFilterCTA.hide\": \"Filters\",\n    \"components.DragHandle-label\": \"Drag\",\n    \"components.DraggableAttr.edit\": \"Click to edit\",\n    \"components.DraggableCard.delete.field\": \"Delete {item}\",\n    \"components.DraggableCard.edit.field\": \"Edit {item}\",\n    \"components.DraggableCard.move.field\": \"Move {item}\",\n    \"components.ListViewTable.row-line\": \"item line {number}\",\n    \"components.DynamicZone.ComponentPicker-label\": \"Pick one component\",\n    \"components.DynamicZone.add-component\": \"Add a component to {componentName}\",\n    \"components.DynamicZone.delete-label\": \"Delete {name}\",\n    \"components.DynamicZone.error-message\": \"The component contains error(s)\",\n    \"components.DynamicZone.missing-components\": \"There {number, plural, =0 {are # missing components} one {is # missing component} other {are # missing components}}\",\n    \"components.DynamicZone.extra-components\": \"There {number, plural, =0 {are # extra components} one {is # extra component} other {are # extra components}}\",\n    \"components.DynamicZone.move-down-label\": \"Move component down\",\n    \"components.DynamicZone.move-up-label\": \"Move component up\",\n    \"components.DynamicZone.pick-compo\": \"Pick one component\",\n    \"components.DynamicZone.required\": \"Component is required\",\n    \"components.EmptyAttributesBlock.button\": \"Go to settings page\",\n    \"components.EmptyAttributesBlock.description\": \"You can change your settings\",\n    \"components.FieldItem.linkToComponentLayout\": \"Set the component's layout\",\n    \"components.FieldSelect.label\": \"Add a field\",\n    \"components.FilterOptions.button.apply\": \"Apply\",\n    \"components.FiltersPickWrapper.PluginHeader.actions.apply\": \"Apply\",\n    \"components.FiltersPickWrapper.PluginHeader.actions.clearAll\": \"Clear all\",\n    \"components.FiltersPickWrapper.PluginHeader.description\": \"Set the conditions to apply to filter the entries\",\n    \"components.FiltersPickWrapper.PluginHeader.title.filter\": \"Filters\",\n    \"components.FiltersPickWrapper.hide\": \"Hide\",\n    \"components.Filters.usersSelect.label\": \"Search and select a user to filter by\",\n    \"components.LeftMenu.Search.label\": \"Search for a content type\",\n    \"components.LeftMenu.collection-types\": \"Collection Types\",\n    \"components.LeftMenu.single-types\": \"Single Types\",\n    \"components.LimitSelect.itemsPerPage\": \"Items per page\",\n    \"components.NotAllowedInput.text\": \"No permissions to see this field\",\n    \"components.RelationInput.icon-button-aria-label\": \"Drag\",\n    \"components.RelationInputModal.modal-title\": \"Edit a relation\",\n    \"components.RelationInputModal.button-fullpage\": \"Go to entry\",\n    \"components.RelationInputModal.confirmation-message\": \"Some changes were not saved. Are you sure you want to close this relation? All changes that were not saved will be lost.\",\n    \"components.RepeatableComponent.error-message\": \"The component(s) contain error(s)\",\n    \"components.Search.placeholder\": \"Search for an entry...\",\n    \"components.Select.draft-info-title\": \"Draft\",\n    \"components.Select.publish-info-title\": \"Published\",\n    \"components.SettingsViewWrapper.pluginHeader.description.edit-settings\": \"Customize how the edit view will look like.\",\n    \"components.SettingsViewWrapper.pluginHeader.description.list-settings\": \"Define the settings of the list view.\",\n    \"components.SettingsViewWrapper.pluginHeader.title\": \"Configure the view — {name}\",\n    \"bulk-publish.already-published\": \"Already Published\",\n    \"bulk-unpublish.already-unpublished\": \"Already Unpublished\",\n    \"bulk-publish.modified\": \"Ready to publish changes\",\n    \"bulk-publish.waiting-for-action\": \"Waiting for action\",\n    \"components.TableDelete.delete\": \"Delete all\",\n    \"components.TableDelete.deleteSelected\": \"Delete selected\",\n    \"components.TableDelete.label\": \"{number, plural, one {# entry} other {# entries}} selected\",\n    \"components.TableEmpty.withFilters\": \"There are no {contentType} with the applied filters...\",\n    \"components.TableEmpty.withSearch\": \"There are no {contentType} corresponding to the search ({search})...\",\n    \"components.TableEmpty.withoutFilter\": \"There are no {contentType}...\",\n    \"components.empty-repeatable\": \"No entry yet. Click to add one.\",\n    \"components.notification.info.maximum-requirement\": \"You have already reached the maximum number of fields\",\n    \"components.notification.info.minimum-requirement\": \"A field has been added to match the minimum requirement\",\n    \"components.repeatable.reorder.error\": \"An error occurred while reordering your component's field, please try again\",\n    \"components.reset-entry\": \"Reset entry\",\n    \"components.uid.apply\": \"apply\",\n    \"components.uid.available\": \"Available\",\n    \"components.uid.regenerate\": \"Regenerate\",\n    \"components.uid.suggested\": \"suggested\",\n    \"components.uid.unavailable\": \"Unavailable\",\n    \"containers.edit.tabs.label\": \"Document status\",\n    \"containers.edit.tabs.draft\": \"draft\",\n    \"containers.edit.tabs.published\": \"published\",\n    \"containers.edit.panels.default.title\": \"Entry\",\n    \"containers.edit.panels.default.more-actions\": \"More document actions\",\n    \"containers.Edit.delete\": \"Delete\",\n    \"containers.edit.title.new\": \"Create an entry\",\n    \"containers.edit.header.more-actions\": \"More actions\",\n    \"containers.edit.information.last-published.label\": \"Published\",\n    \"containers.edit.information.last-published.value\": \"{time}{isAnonymous, select, true {} other { by {author}}}\",\n    \"containers.edit.information.last-draft.label\": \"Updated\",\n    \"containers.edit.information.last-draft.value\": \"{time}{isAnonymous, select, true {} other { by {author}}}\",\n    \"containers.edit.information.document.label\": \"Created\",\n    \"containers.edit.information.document.value\": \"{time}{isAnonymous, select, true {} other { by {author}}}\",\n    \"containers.EditSettingsView.modal-form.edit-field\": \"Edit the field\",\n    \"containers.EditView.add.new-entry\": \"Add an entry\",\n    \"containers.EditView.notification.errors\": \"The form contains some errors\",\n    \"containers.Home.introduction\": \"To edit your entries go to the specific link in the left menu. This plugin doesn't have a proper way to edit settings and it's still under active development.\",\n    \"containers.Home.pluginHeaderDescription\": \"Manage your entries through a powerful and beautiful interface.\",\n    \"containers.Home.pluginHeaderTitle\": \"Content Manager\",\n    \"containers.List.draft\": \"Draft\",\n    \"containers.List.published\": \"Published\",\n    \"containers.List.modified\": \"Modified\",\n    \"containers.list.displayedFields\": \"Displayed Fields\",\n    \"containers.list.items\": \"{number} {number, plural, =0 {items} one {item} other {items}}\",\n    \"containers.list.table.row-actions\": \"Row actions\",\n    \"containers.list.selectedEntriesModal.title\": \"Publish entries\",\n    \"containers.list.selectedEntriesModal.selectedCount.publish\": \"<b>{publishedCount}</b> {publishedCount, plural, =0 {entries} one {entry} other {entries}} already published. <b>{draftCount}</b> {draftCount, plural, =0 {entries} one {entry} other {entries}} ready to publish. <b>{withErrorsCount}</b> {withErrorsCount, plural, =0 {entries} one {entry} other {entries}} waiting for action.\",\n    \"containers.list.selectedEntriesModal.selectedCount.unpublish\": \"<b>{draftCount}</b> {draftCount, plural, =0 {entries} one {entry} other {entries}} already unpublished. <b>{publishedCount}</b> {publishedCount, plural, =0 {entries} one {entry} other {entries}} ready to unpublish.\",\n    \"containers.list.autoCloneModal.header\": \"Duplicate\",\n    \"containers.list.autoCloneModal.title\": \"This entry can't be duplicated directly.\",\n    \"containers.list.autoCloneModal.description\": \"A new entry will be created with the same content, but you'll have to change the following fields to save it.\",\n    \"containers.list.autoCloneModal.create\": \"Create\",\n    \"containers.list.autoCloneModal.error.unique\": \"Identical values in a unique field are not allowed.\",\n    \"containers.list.autoCloneModal.error.relation\": \"Duplicating the relation could remove it from the original entry.\",\n    \"containers.list-settings.modal-form.label\": \"Edit {fieldName}\",\n    \"containers.list-settings.modal-form.error\": \"An error occurred while trying to open the form.\",\n    \"containers.edit-settings.modal-form.error\": \"An error occurred while trying to open the form.\",\n    \"containers.edit-settings.modal-form.label\": \"Label\",\n    \"containers.edit-settings.modal-form.description\": \"Description\",\n    \"containers.edit-settings.modal-form.placeholder\": \"Placeholder\",\n    \"containers.edit-settings.modal-form.mainField\": \"Entry title\",\n    \"containers.edit-settings.modal-form.mainField.hint\": \"Set the displayed field in both the edit and list views\",\n    \"containers.edit-settings.modal-form.editable\": \"Editable field\",\n    \"containers.edit-settings.modal-form.size\": \"Size\",\n    \"containers.SettingPage.add.field\": \"Insert another field\",\n    \"containers.SettingPage.add.relational-field\": \"Insert another related field\",\n    \"containers.SettingPage.attributes\": \"Attributes fields\",\n    \"containers.SettingPage.attributes.description\": \"Define the order of the attributes\",\n    \"containers.SettingPage.editSettings.description\": \"Drag & drop the fields to build the layout\",\n    \"containers.SettingPage.editSettings.entry.title\": \"Entry title\",\n    \"containers.SettingPage.editSettings.entry.title.description\": \"Set the displayed field of your entry\",\n    \"containers.SettingPage.editSettings.relation-field.description\": \"Set the displayed field in both the edit and list views\",\n    \"containers.SettingPage.editSettings.title\": \"Edit view (settings)\",\n    \"containers.SettingPage.layout\": \"Layout\",\n    \"containers.SettingPage.listSettings.description\": \"Configure the options for this Collection Type\",\n    \"containers.SettingPage.listSettings.title\": \"List view (settings)\",\n    \"containers.SettingPage.pluginHeaderDescription\": \"Configure the specific settings for this Collection Type\",\n    \"containers.SettingPage.relations\": \"Related fields\",\n    \"containers.SettingPage.settings\": \"Settings\",\n    \"containers.SettingPage.view\": \"View\",\n    \"containers.SettingViewModel.pluginHeader.title\": \"Content Manager — {name}\",\n    \"containers.SettingsPage.Block.contentType.description\": \"Configure the specific settings\",\n    \"containers.SettingsPage.Block.contentType.title\": \"Collection Types\",\n    \"containers.SettingsPage.Block.generalSettings.description\": \"Configure the default options for your Collection Types\",\n    \"containers.SettingsPage.Block.generalSettings.title\": \"General\",\n    \"containers.SettingsPage.pluginHeaderDescription\": \"Configure the settings for all your Collection Types and Groups\",\n    \"containers.SettingsView.list.subtitle\": \"Configure the layout and display of your Collection Types and Groups\",\n    \"containers.SettingsView.list.title\": \"Display configurations\",\n    \"containers.untitled\": \"Untitled\",\n    \"dnd.cancel-item\": \"{item}, dropped. Re-order cancelled.\",\n    \"dnd.drop-item\": \"{item}, dropped. Final position in list: {position}.\",\n    \"dnd.grab-item\": \"{item}, grabbed. Current position in list: {position}. Press up and down arrow to change position, Spacebar to drop, Escape to cancel.\",\n    \"dnd.instructions\": \"Press spacebar to grab and re-order\",\n    \"dnd.reorder\": \"{item}, moved. New position in list: {position}.\",\n    \"edit-settings-view.link-to-ctb.components\": \"Edit the component\",\n    \"edit-settings-view.link-to-ctb.content-types\": \"Edit the content type\",\n    \"emptyAttributes.button\": \"Go to Collection Type builder\",\n    \"emptyAttributes.description\": \"Add your first field to your Collection Type\",\n    \"emptyAttributes.title\": \"There are no fields yet\",\n    \"error.attribute.key.taken\": \"This value already exists\",\n    \"error.attribute.sameKeyAndName\": \"Can't be equals\",\n    \"error.attribute.taken\": \"This field name already exists\",\n    \"error.contentTypeName.taken\": \"This name already exists\",\n    \"error.model.fetch\": \"An error occurred during models config fetch.\",\n    \"error.record.create\": \"An error occurred during record creation.\",\n    \"error.record.delete\": \"An error occurred during record deletion.\",\n    \"error.record.fetch\": \"An error occurred during record fetch.\",\n    \"error.record.update\": \"An error occurred during record update.\",\n    \"error.records.count\": \"An error occurred during count records fetch.\",\n    \"error.records.fetch\": \"An error occurred during records fetch.\",\n    \"error.records.fetch-draft-relatons\": \"An error occurred while fetching draft relations on this document.\",\n    \"error.schema.generation\": \"An error occurred during schema generation.\",\n    \"error.validation.json\": \"This is not a JSON\",\n    \"error.validation.max\": \"The value is too high (max: {max}).\",\n    \"error.validation.maxLength\": \"The value is too long (max: {max}).\",\n    \"error.validation.min\": \"The value is too low (min: {min}).\",\n    \"error.validation.minLength\": \"The value is too short (min: {min}).\",\n    \"error.validation.minSupMax\": \"Can't be superior\",\n    \"error.validation.regex\": \"The value does not match the regex.\",\n    \"error.validation.required\": \"This value input is required.\",\n    \"form.Input.bulkActions\": \"Enable bulk actions\",\n    \"form.Input.defaultSort\": \"Default sort attribute\",\n    \"form.Input.description\": \"Description\",\n    \"form.Input.description.placeholder\": \"Display name in the profile\",\n    \"form.Input.editable\": \"Editable field\",\n    \"form.Input.filters\": \"Enable filters\",\n    \"form.Input.hint.character.unit\": \"{maxValue, plural, one { character} other { characters}}\",\n    \"form.Input.hint.minMaxDivider\": \" / \",\n    \"form.Input.hint.text\": \"{min, select, undefined {} other {min. {min}}}{divider}{max, select, undefined {} other {max. {max}}}{unit}{br}{description}\",\n    \"form.Input.label\": \"Label\",\n    \"form.Input.label.inputDescription\": \"This value overrides the label displayed in the table's head\",\n    \"form.Input.pageEntries\": \"Entries per page\",\n    \"form.Input.pageEntries.inputDescription\": \"Note: You can override this value in the Collection Type settings page.\",\n    \"form.Input.placeholder\": \"Placeholder\",\n    \"form.Input.placeholder.placeholder\": \"My awesome value\",\n    \"form.Input.search\": \"Enable search\",\n    \"form.Input.search.field\": \"Enable search on this field\",\n    \"form.Input.sort.field\": \"Enable sort on this field\",\n    \"form.Input.sort.order\": \"Default sort order\",\n    \"form.Input.wysiwyg\": \"Display as WYSIWYG\",\n    \"global.displayedFields\": \"Displayed Fields\",\n    groups: groups,\n    \"groups.numbered\": \"Groups ({number})\",\n    \"header.name\": \"Content Manager\",\n    \"link-to-ctb\": \"Edit the model\",\n    models: models,\n    \"models.numbered\": \"Collection Types ({number})\",\n    \"notification.error.displayedFields\": \"You need at least one displayed field\",\n    \"notification.error.relationship.fetch\": \"An error occurred during relationship fetch.\",\n    \"notification.info.SettingPage.disableSort\": \"You need to have one attribute with the sorting allowed\",\n    \"notification.info.minimumFields\": \"You need to have at least one field displayed\",\n    \"notification.upload.error\": \"An error has occurred while uploading your files\",\n    pageNotFound: pageNotFound,\n    \"pages.ListView.header-subtitle\": \"{number, plural, =0 {# entries} one {# entry} other {# entries}} found\",\n    \"pages.NoContentType.button\": \"Create your first Content-Type\",\n    \"pages.NoContentType.text\": \"You don't have any content yet, we recommend you to create your first Content-Type.\",\n    \"permissions.not-allowed.create\": \"You are not allowed to create a document\",\n    \"permissions.not-allowed.update\": \"You are not allowed to see this document\",\n    \"plugin.description.long\": \"Quick way to see, edit and delete the data in your database.\",\n    \"plugin.description.short\": \"Quick way to see, edit and delete the data in your database.\",\n    \"popUpWarning.bodyMessage.contentType.delete\": \"Are you sure to delete Content-Type?\",\n    \"popUpWarning.bodyMessage.contentType.delete.all\": \"Are you sure you want to delete these entries?\",\n    \"popUpWarning.bodyMessage.contentType.publish.all\": \"Are you sure you want to publish these entries?\",\n    \"popUpWarning.bodyMessage.contentType.unpublish.all\": \"Are you sure you want to unpublish these entries?\",\n    \"popUpWarning.warning.has-draft-relations.title\": \"Confirmation\",\n    \"popUpWarning.warning.has-draft-relations.message\": \"This entry is related to {count, plural, one {# draft entry} other {# draft entries}}. Publishing it could leave broken links in your app.\",\n    \"popUpwarning.warning.has-draft-relations.button-confirm\": \"Yes, publish\",\n    \"popUpwarning.warning.bulk-has-draft-relations.message\": \"<b>{count} {count, plural, one { relation } other { relations } } out of {entities} { entities, plural, one { entry } other { entries } } {count, plural, one { is } other { are } }</b> not published yet and might lead to unexpected behavior. \",\n    \"popUpWarning.warning.publish-question\": \"Do you still want to publish?\",\n    \"popUpWarning.warning.unpublish\": \"If you don't publish this content, it will automatically turn into a Draft.\",\n    \"popUpWarning.warning.unpublish-question\": \"Are you sure you don't want to publish it?\",\n    \"popUpWarning.warning.updateAllSettings\": \"This will modify all your settings\",\n    \"popover.display-relations.label\": \"Display relations\",\n    \"preview.panel.title\": \"Preview\",\n    \"preview.panel.button\": \"Open preview\",\n    \"preview.panel.button-disabled-tooltip\": \"Please save to open the preview\",\n    \"preview.page-title\": \"{contentType} preview\",\n    \"preview.header.close\": \"Close preview\",\n    \"preview.copy.label\": \"Copy preview link\",\n    \"preview.copy.success\": \"Copied preview link\",\n    \"preview.tabs.label\": \"Preview status\",\n    \"preview.content.close-editor\": \"Close editor\",\n    \"preview.content.open-editor\": \"Open editor\",\n    \"relation.create\": \"Create a relation\",\n    \"relation.add\": \"Add or create a relation\",\n    \"relation.disconnect\": \"Remove\",\n    \"relation.error-adding-relation\": \"An error occurred while trying to add the relation.\",\n    \"relation.isLoading\": \"Relations are loading\",\n    \"relation.loadMore\": \"Load More\",\n    \"relation.notAvailable\": \"No relations available\",\n    \"relation.publicationState.draft\": \"Draft\",\n    \"relation.publicationState.published\": \"Published\",\n    \"reviewWorkflows.stage.label\": \"Review stage\",\n    \"select.currently.selected\": \"{count} currently selected\",\n    \"success.record.clone\": \"Cloned document\",\n    \"success.record.discard\": \"Changes discarded\",\n    \"success.record.delete\": \"Deleted document\",\n    \"success.record.publish\": \"Published document\",\n    \"success.record.publishing\": \"Publishing...\",\n    \"success.record.save\": \"Saved document\",\n    \"success.record.unpublish\": \"Unpublished document\",\n    \"success.records.delete\": \"Successfully deleted.\",\n    \"success.records.unpublish\": \"Successfully unpublished.\",\n    \"success.records.publish\": \"Successfully published.\",\n    \"utils.data-loaded\": \"The {number, plural, =1 {entry has} other {entries have}} successfully been loaded\",\n    \"listView.validation.errors.title\": \"Action required\",\n    \"listView.validation.errors.message\": \"Please make sure all fields are valid before publishing (required field, min/max character limit, etc.)\",\n    \"history.document-action\": \"Content History\",\n    \"history.page-title\": \"{contentType} history\",\n    \"history.sidebar.title\": \"Versions\",\n    \"history.sidebar.version-card.aria-label\": \"Version card\",\n    \"history.sidebar.versionDescription\": \"{distanceToNow}{isAnonymous, select, true {} other { by {author}}}{isCurrent, select, true { <b>(current)</b>} other {}}\",\n    \"history.sidebar.show-newer\": \"Show newer versions\",\n    \"history.sidebar.show-older\": \"Show older versions\",\n    \"history.version.subtitle\": \"{hasLocale, select, true {{subtitle}, in {locale}} other {{subtitle}}}\",\n    \"history.content.new-field.title\": \"New field\",\n    \"history.content.new-field.message\": \"This field didn't exist when this version was saved. If you restore this version, it will be empty.\",\n    \"history.content.unknown-fields.title\": \"Unknown fields\",\n    \"history.content.unknown-fields.message\": \"These fields have been deleted or renamed in the Content-Type Builder. <b>These fields will not be restored.</b>\",\n    \"history.content.missing-assets.title\": \"{number, plural, =1 {Missing asset} other {{number} missing assets}}\",\n    \"history.content.missing-assets.message\": \"{number, plural, =1 {It has} other {They have}} been deleted in the Media Library and can't be restored.\",\n    \"history.content.missing-relations.title\": \"{number, plural, =1 {Missing relation} other {{number} missing relations}}\",\n    \"history.content.missing-relations.message\": \"{number, plural, =1 {It has} other {They have}} been deleted and can't be restored.\",\n    \"history.content.no-relations\": \"No relations.\",\n    \"history.content.localized\": \"This value is specific to this locale. If you restore this version, the content will not be replaced for other locales.\",\n    \"history.content.not-localized\": \"This value is common to all locales. If you restore this version, the content will be replaced for all locales.\",\n    \"history.restore.confirm.button\": \"Restore\",\n    \"history.restore.confirm.title\": \"Are you sure you want to restore this version?\",\n    \"history.restore.confirm.message\": \"{isDraft, select, true {The restored content will override your draft.} other {The restored content won't be published, it will override the draft and be saved as pending changes. You'll be able to publish the changes at anytime.}}\",\n    \"history.restore.success.title\": \"Version restored.\",\n    \"history.restore.success.message\": \"A past version of the content was restored.\",\n    \"history.restore.error.message\": \"Could not restore version.\",\n    \"validation.error\": \"There are validation errors in your document. Please fix them before saving.\",\n    \"validation.error.unreadable-required-field\": \"Your current permissions prevent access to certain required fields. Please request access from an administrator to proceed.\",\n    \"bulk-publish.edit\": \"Edit\",\n    \"widget.last-edited.title\": \"Last edited entries\",\n    \"widget.last-edited.single-type\": \"Single-Type\",\n    \"widget.last-edited.no-data\": \"No edited entries\",\n    \"widget.last-published.title\": \"Last published entries\",\n    \"widget.last-published.no-data\": \"No published entries\"\n};\n\nexport { en as default, groups, models, pageNotFound };\n//# sourceMappingURL=en.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,SAAS;AACb,IAAI,SAAS;AACb,IAAI,eAAe;AACnB,IAAI,KAAK;AAAA,EACL,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,uCAAuC;AAAA,EACvC,UAAU;AAAA,EACV,0CAA0C;AAAA,EAC1C,+BAA+B;AAAA,EAC/B,gCAAgC;AAAA,EAChC,+BAA+B;AAAA,EAC/B,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,gDAAgD;AAAA,EAChD,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,wCAAwC;AAAA,EACxC,6CAA6C;AAAA,EAC7C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,gCAAgC;AAAA,EAChC,yCAAyC;AAAA,EACzC,4DAA4D;AAAA,EAC5D,+DAA+D;AAAA,EAC/D,0DAA0D;AAAA,EAC1D,2DAA2D;AAAA,EAC3D,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,uCAAuC;AAAA,EACvC,mCAAmC;AAAA,EACnC,mDAAmD;AAAA,EACnD,6CAA6C;AAAA,EAC7C,iDAAiD;AAAA,EACjD,sDAAsD;AAAA,EACtD,gDAAgD;AAAA,EAChD,iCAAiC;AAAA,EACjC,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,yEAAyE;AAAA,EACzE,yEAAyE;AAAA,EACzE,qDAAqD;AAAA,EACrD,kCAAkC;AAAA,EAClC,sCAAsC;AAAA,EACtC,yBAAyB;AAAA,EACzB,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,gCAAgC;AAAA,EAChC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,uCAAuC;AAAA,EACvC,+BAA+B;AAAA,EAC/B,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,uCAAuC;AAAA,EACvC,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,6BAA6B;AAAA,EAC7B,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,kCAAkC;AAAA,EAClC,wCAAwC;AAAA,EACxC,+CAA+C;AAAA,EAC/C,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,uCAAuC;AAAA,EACvC,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,gDAAgD;AAAA,EAChD,gDAAgD;AAAA,EAChD,8CAA8C;AAAA,EAC9C,8CAA8C;AAAA,EAC9C,qDAAqD;AAAA,EACrD,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,gCAAgC;AAAA,EAChC,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,yBAAyB;AAAA,EACzB,6BAA6B;AAAA,EAC7B,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,8CAA8C;AAAA,EAC9C,8DAA8D;AAAA,EAC9D,gEAAgE;AAAA,EAChE,yCAAyC;AAAA,EACzC,wCAAwC;AAAA,EACxC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,iDAAiD;AAAA,EACjD,6CAA6C;AAAA,EAC7C,6CAA6C;AAAA,EAC7C,6CAA6C;AAAA,EAC7C,6CAA6C;AAAA,EAC7C,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,iDAAiD;AAAA,EACjD,sDAAsD;AAAA,EACtD,gDAAgD;AAAA,EAChD,4CAA4C;AAAA,EAC5C,oCAAoC;AAAA,EACpC,+CAA+C;AAAA,EAC/C,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,+DAA+D;AAAA,EAC/D,kEAAkE;AAAA,EAClE,6CAA6C;AAAA,EAC7C,iCAAiC;AAAA,EACjC,mDAAmD;AAAA,EACnD,6CAA6C;AAAA,EAC7C,kDAAkD;AAAA,EAClD,oCAAoC;AAAA,EACpC,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,kDAAkD;AAAA,EAClD,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,6DAA6D;AAAA,EAC7D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,yCAAyC;AAAA,EACzC,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,oBAAoB;AAAA,EACpB,eAAe;AAAA,EACf,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,0BAA0B;AAAA,EAC1B,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,sCAAsC;AAAA,EACtC,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,iCAAiC;AAAA,EACjC,wBAAwB;AAAA,EACxB,oBAAoB;AAAA,EACpB,qCAAqC;AAAA,EACrC,0BAA0B;AAAA,EAC1B,2CAA2C;AAAA,EAC3C,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B;AAAA,EACA,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,eAAe;AAAA,EACf;AAAA,EACA,mBAAmB;AAAA,EACnB,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B;AAAA,EACA,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,+CAA+C;AAAA,EAC/C,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,oDAAoD;AAAA,EACpD,2DAA2D;AAAA,EAC3D,yDAAyD;AAAA,EACzD,yCAAyC;AAAA,EACzC,kCAAkC;AAAA,EAClC,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,mCAAmC;AAAA,EACnC,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,yCAAyC;AAAA,EACzC,sBAAsB;AAAA,EACtB,wBAAwB;AAAA,EACxB,sBAAsB;AAAA,EACtB,wBAAwB;AAAA,EACxB,sBAAsB;AAAA,EACtB,gCAAgC;AAAA,EAChC,+BAA+B;AAAA,EAC/B,mBAAmB;AAAA,EACnB,gBAAgB;AAAA,EAChB,uBAAuB;AAAA,EACvB,kCAAkC;AAAA,EAClC,sBAAsB;AAAA,EACtB,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,mCAAmC;AAAA,EACnC,uCAAuC;AAAA,EACvC,+BAA+B;AAAA,EAC/B,6BAA6B;AAAA,EAC7B,wBAAwB;AAAA,EACxB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,uBAAuB;AAAA,EACvB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,2BAA2B;AAAA,EAC3B,qBAAqB;AAAA,EACrB,oCAAoC;AAAA,EACpC,sCAAsC;AAAA,EACtC,2BAA2B;AAAA,EAC3B,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,2CAA2C;AAAA,EAC3C,sCAAsC;AAAA,EACtC,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,qCAAqC;AAAA,EACrC,wCAAwC;AAAA,EACxC,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,oBAAoB;AAAA,EACpB,8CAA8C;AAAA,EAC9C,qBAAqB;AAAA,EACrB,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,+BAA+B;AAAA,EAC/B,iCAAiC;AACrC;", "names": []}