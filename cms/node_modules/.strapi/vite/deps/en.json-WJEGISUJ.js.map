{"version": 3, "sources": ["../../../@strapi/plugin-users-permissions/dist/admin/translations/en.json.mjs"], "sourcesContent": ["var en = {\n    \"BoundRoute.title\": \"Bound route to\",\n    \"EditForm.inputSelect.description.role\": \"It will attach the new authenticated user to the selected role.\",\n    \"EditForm.inputSelect.label.role\": \"Default role for authenticated users\",\n    \"EditForm.inputToggle.description.email\": \"Disallow the user to create multiple accounts using the same email address with different authentication providers.\",\n    \"EditForm.inputToggle.description.email-confirmation\": \"When enabled (ON), new registered users receive a confirmation email.\",\n    \"EditForm.inputToggle.description.email-confirmation-redirection\": \"After you confirmed your email, choose where you will be redirected.\",\n    \"EditForm.inputToggle.description.email-reset-password\": \"URL of your application's reset password page\",\n    \"EditForm.inputToggle.description.sign-up\": \"When disabled (OFF), the registration process is forbidden. No one can subscribe anymore no matter the used provider.\",\n    \"EditForm.inputToggle.label.email\": \"One account per email address\",\n    \"EditForm.inputToggle.label.email-confirmation\": \"Enable email confirmation\",\n    \"EditForm.inputToggle.label.email-confirmation-redirection\": \"Redirection url\",\n    \"EditForm.inputToggle.label.email-reset-password\": \"Reset password page\",\n    \"EditForm.inputToggle.label.sign-up\": \"Enable sign-ups\",\n    \"EditForm.inputToggle.placeholder.email-confirmation-redirection\": \"ex: https://yourfrontend.com/email-confirmation-redirection\",\n    \"EditForm.inputToggle.placeholder.email-reset-password\": \"ex: https://yourfrontend.com/reset-password\",\n    \"EditPage.form.roles\": \"Role details\",\n    \"Email.template.data.loaded\": \"Email templates has been loaded\",\n    \"Email.template.email_confirmation\": \"Email address confirmation\",\n    \"Email.template.form.edit.label\": \"Edit a template\",\n    \"Email.template.table.action.label\": \"action\",\n    \"Email.template.table.icon.label\": \"icon\",\n    \"Email.template.table.name.label\": \"name\",\n    \"Form.advancedSettings.data.loaded\": \"Advanced settings data has been loaded\",\n    \"HeaderNav.link.advancedSettings\": \"Advanced settings\",\n    \"HeaderNav.link.emailTemplates\": \"Email templates\",\n    \"HeaderNav.link.providers\": \"Providers\",\n    \"Plugin.permissions.plugins.description\": \"Define all allowed actions for the {name} plugin.\",\n    \"Plugins.header.description\": \"Only actions bound by a route are listed below.\",\n    \"Plugins.header.title\": \"Permissions\",\n    \"Policies.header.hint\": \"Select the application's actions or the plugin's actions and click on the cog icon to display the bound route\",\n    \"Policies.header.title\": \"Advanced settings\",\n    \"PopUpForm.Email.email_templates.inputDescription\": \"If you're unsure how to use variables, {link}\",\n    \"PopUpForm.Email.link.documentation\": \"check out our documentation.\",\n    \"PopUpForm.Email.options.from.email.label\": \"Shipper email\",\n    \"PopUpForm.Email.options.from.email.placeholder\": \"<EMAIL>\",\n    \"PopUpForm.Email.options.from.name.label\": \"Shipper name\",\n    \"PopUpForm.Email.options.from.name.placeholder\": \"Kai Doe\",\n    \"PopUpForm.Email.options.message.label\": \"Message\",\n    \"PopUpForm.Email.options.object.label\": \"Subject\",\n    \"PopUpForm.Email.options.object.placeholder\": \"Please confirm your email address for %APP_NAME%\",\n    \"PopUpForm.Email.options.response_email.label\": \"Response email\",\n    \"PopUpForm.Email.options.response_email.placeholder\": \"<EMAIL>\",\n    \"PopUpForm.Providers.enabled.description\": \"If disabled, users won't be able to use this provider.\",\n    \"PopUpForm.Providers.enabled.label\": \"Enable\",\n    \"PopUpForm.Providers.key.label\": \"Client ID\",\n    \"PopUpForm.Providers.key.placeholder\": \"TEXT\",\n    \"PopUpForm.Providers.redirectURL.front-end.label\": \"The redirect URL to your front-end app\",\n    \"PopUpForm.Providers.redirectURL.label\": \"The redirect URL to add in your {provider} application configurations\",\n    \"PopUpForm.Providers.secret.label\": \"Client Secret\",\n    \"PopUpForm.Providers.secret.placeholder\": \"TEXT\",\n    \"PopUpForm.Providers.subdomain.label\": \"Host URI (Subdomain)\",\n    \"PopUpForm.Providers.subdomain.placeholder\": \"my.subdomain.com\",\n    \"PopUpForm.header.edit.email-templates\": \"Edit email template\",\n    \"PopUpForm.header.edit.providers\": \"Edit Provider\",\n    \"Providers.data.loaded\": \"Providers have been loaded\",\n    \"Providers.status\": \"Status\",\n    \"Roles.empty\": \"You don't have any roles yet.\",\n    \"Roles.empty.search\": \"No roles match the search.\",\n    \"Settings.roles.deleted\": \"Role deleted\",\n    \"Settings.roles.edited\": \"Role edited\",\n    \"Settings.section-label\": \"Users & Permissions plugin\",\n    \"components.Input.error.validation.email\": \"This is not a valid email\",\n    \"components.Input.error.validation.json\": \"This doesn't match the JSON format\",\n    \"components.Input.error.validation.max\": \"The value is too high.\",\n    \"components.Input.error.validation.maxLength\": \"The value is too long.\",\n    \"components.Input.error.validation.min\": \"The value is too low.\",\n    \"components.Input.error.validation.minLength\": \"The value is too short.\",\n    \"components.Input.error.validation.minSupMax\": \"Can't be superior\",\n    \"components.Input.error.validation.regex\": \"The value does not match the regex.\",\n    \"components.Input.error.validation.required\": \"This value is required.\",\n    \"components.Input.error.validation.unique\": \"This value is already used.\",\n    \"notification.success.submit\": \"Settings have been updated\",\n    \"page.title\": \"Settings - Roles\",\n    \"plugin.description.long\": \"Protect your API with a full authentication process based on JWT. This plugin comes also with an ACL strategy that allows you to manage the permissions between the groups of users.\",\n    \"plugin.description.short\": \"Protect your API with a full authentication process based on JWT.\",\n    \"plugin.name\": \"Users & Permissions Plugin\",\n    \"popUpWarning.button.cancel\": \"Cancel\",\n    \"popUpWarning.button.confirm\": \"Confirm\",\n    \"popUpWarning.title\": \"Please confirm\",\n    \"popUpWarning.warning.cancel\": \"Are you sure you want to cancel your modifications?\"\n};\n\nexport { en as default };\n//# sourceMappingURL=en.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,KAAK;AAAA,EACL,oBAAoB;AAAA,EACpB,yCAAyC;AAAA,EACzC,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,uDAAuD;AAAA,EACvD,mEAAmE;AAAA,EACnE,yDAAyD;AAAA,EACzD,4CAA4C;AAAA,EAC5C,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,6DAA6D;AAAA,EAC7D,mDAAmD;AAAA,EACnD,sCAAsC;AAAA,EACtC,mEAAmE;AAAA,EACnE,yDAAyD;AAAA,EACzD,uBAAuB;AAAA,EACvB,8BAA8B;AAAA,EAC9B,qCAAqC;AAAA,EACrC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,0CAA0C;AAAA,EAC1C,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,oDAAoD;AAAA,EACpD,sCAAsC;AAAA,EACtC,4CAA4C;AAAA,EAC5C,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,iDAAiD;AAAA,EACjD,yCAAyC;AAAA,EACzC,wCAAwC;AAAA,EACxC,8CAA8C;AAAA,EAC9C,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,iCAAiC;AAAA,EACjC,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,0CAA0C;AAAA,EAC1C,uCAAuC;AAAA,EACvC,6CAA6C;AAAA,EAC7C,yCAAyC;AAAA,EACzC,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,oBAAoB;AAAA,EACpB,eAAe;AAAA,EACf,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,4CAA4C;AAAA,EAC5C,+BAA+B;AAAA,EAC/B,cAAc;AAAA,EACd,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,eAAe;AAAA,EACf,8BAA8B;AAAA,EAC9B,+BAA+B;AAAA,EAC/B,sBAAsB;AAAA,EACtB,+BAA+B;AACnC;", "names": []}