import "./chunk-PLDDJCW6.js";

// node_modules/@strapi/plugin-users-permissions/dist/admin/translations/es.json.mjs
var es = {
  "BoundRoute.title": "Ruta enlazada a",
  "EditForm.inputSelect.description.role": "Adjuntará el nuevo usuario autenticado al rol seleccionado.",
  "EditForm.inputSelect.label.role": "Rol predeterminado para usuarios autenticados",
  "EditForm.inputToggle.description.email": "No permita que el usuario cree varias cuentas utilizando la misma dirección de correo electrónico con distintos proveedores de autenticación.",
  "EditForm.inputToggle.description.email-confirmation": "Estando habilitado (ON), nuevos usuarios registrados reciben un correo de confirmación.",
  "EditForm.inputToggle.description.email-confirmation-redirection": "After confirmed your email, chose where you will be redirected.",
  "EditForm.inputToggle.description.email-reset-password": "URL de la página de restablecimiento de contraseña de su aplicación",
  "EditForm.inputToggle.description.sign-up": "Cuando está desactivado (OFF), el proceso de registro está prohibido. Nadie puede suscribirse sin importar el proveedor utilizado.",
  "EditForm.inputToggle.label.email": "Una cuenta por dirección de correo electrónico",
  "EditForm.inputToggle.label.email-confirmation": "Habilitar confirmación de correo",
  "EditForm.inputToggle.label.email-confirmation-redirection": "URL de redirección",
  "EditForm.inputToggle.label.email-reset-password": "Página de reestablecer la contraseña",
  "EditForm.inputToggle.label.sign-up": "Habilitar inscripciones",
  "EditForm.inputToggle.placeholder.email-confirmation-redirection": "ej: https://tufrontend.com/restablecer-contrasena",
  "EditForm.inputToggle.placeholder.email-reset-password": "ej: https://tufrontend.com/restablecer-contrasena",
  "EditPage.form.roles": "Detalles del rol",
  "Email.template.data.loaded": "Se han cargado las plantillas de correo electrónico",
  "Email.template.email_confirmation": "Confirmación de dirección de correo electrónico",
  "Email.template.form.edit.label": "Editar una plantilla",
  "Email.template.table.action.label": "acción",
  "Email.template.table.icon.label": "icono",
  "Email.template.table.name.label": "nombre",
  "Form.advancedSettings.data.loaded": "Se han cargado los datos de configuración avanzada",
  "HeaderNav.link.advancedSettings": "Ajustes avanzados",
  "HeaderNav.link.emailTemplates": "Plantillas de email",
  "HeaderNav.link.providers": "Proveedores",
  "Plugin.permissions.plugins.description": "Defina todas las acciones permitidas para el plugin {name}.",
  "Plugins.header.description": "Sólo las acciones vinculadas a una ruta se enumeran a continuación.",
  "Plugins.header.title": "Permisos",
  "Policies.header.hint": "Seleccione las acciones de la aplicación o las acciones del plugin y haga clic en el icono del engranaje para ver la ruta vinculada",
  "Policies.header.title": "Ajustes avanzados",
  "PopUpForm.Email.email_templates.inputDescription": "Si no estás seguro de cómo usar las variables, {link}",
  "PopUpForm.Email.link.documentation": "consulte nuestra documentación.",
  "PopUpForm.Email.options.from.email.label": "Email del remitente",
  "PopUpForm.Email.options.from.email.placeholder": "<EMAIL>",
  "PopUpForm.Email.options.from.name.label": "Nombre del remitente",
  "PopUpForm.Email.options.from.name.placeholder": "Kai Doe",
  "PopUpForm.Email.options.message.label": "Mensaje",
  "PopUpForm.Email.options.object.label": "Tema",
  "PopUpForm.Email.options.object.placeholder": "Confirma tu dirección de correo electrónico para %APP_NAME%",
  "PopUpForm.Email.options.response_email.label": "Email de respuesta",
  "PopUpForm.Email.options.response_email.placeholder": "<EMAIL>",
  "PopUpForm.Providers.enabled.description": "Si está desactivado, los usuarios no podrán utilizar este proveedor.",
  "PopUpForm.Providers.enabled.label": "Habilitar",
  "PopUpForm.Providers.key.label": "ID de cliente",
  "PopUpForm.Providers.key.placeholder": "TEXTO",
  "PopUpForm.Providers.redirectURL.front-end.label": "La URL de redireccionamiento a su aplicación front-end",
  "PopUpForm.Providers.redirectURL.label": "La URL de redireccionamiento para agregar en las configuraciones de su aplicación de {proveedor}",
  "PopUpForm.Providers.secret.label": "Secreto Cliente",
  "PopUpForm.Providers.secret.placeholder": "TEXTO",
  "PopUpForm.Providers.subdomain.label": "URI de host (subdominio)",
  "PopUpForm.Providers.subdomain.placeholder": "mi.subdominio.com",
  "PopUpForm.header.edit.email-templates": "Editar Plantillas de Email",
  "PopUpForm.header.edit.providers": "Editar proveedor",
  "Providers.data.loaded": "Los proveedores se han cargado",
  "Providers.status": "Estado",
  "Roles.empty": "Aún no tienes ningún rol.",
  "Roles.empty.search": "Ningún rol coincide con la búsqueda.",
  "Settings.roles.deleted": "Rol eliminado",
  "Settings.roles.edited": "Rol editado",
  "Settings.section-label": "Plugin de Usuarios y Permisos",
  "components.Input.error.validation.email": "El correo electrónico inválido",
  "components.Input.error.validation.json": "No coincide con el formato JSON",
  "components.Input.error.validation.max": "El valor es demasiado alto.",
  "components.Input.error.validation.maxLength": "El valor es demasiado largo.",
  "components.Input.error.validation.min": "El valor es demasiado bajo.",
  "components.Input.error.validation.minLength": "El valor es demasiado corto.",
  "components.Input.error.validation.minSupMax": "No puede ser superior",
  "components.Input.error.validation.regex": "El valor no coincide con la expresión regular.",
  "components.Input.error.validation.required": "Este valor es obligatorio.",
  "components.Input.error.validation.unique": "Este valor ya se utiliza.",
  "notification.success.submit": "Los ajustes se han actualizado",
  "page.title": "Configuración - Roles",
  "plugin.description.long": "Proteja su API con un proceso de autenticación completo basado en JWT. Este plugin viene también con una estrategia ACL que le permite administrar los permisos entre los grupos de usuarios.",
  "plugin.description.short": "Proteja su API con un proceso de autenticación completo basado en JWT",
  "plugin.name": "Roles y Permisos",
  "popUpWarning.button.cancel": "Cancelar",
  "popUpWarning.button.confirm": "Confirmar",
  "popUpWarning.title": "Por favor confirme",
  "popUpWarning.warning.cancel": "¿Está seguro de que desea cancelar sus modificaciones?"
};
export {
  es as default
};
//# sourceMappingURL=es.json-HNSRZYML.js.map
