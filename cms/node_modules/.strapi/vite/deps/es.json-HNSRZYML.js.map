{"version": 3, "sources": ["../../../@strapi/plugin-users-permissions/dist/admin/translations/es.json.mjs"], "sourcesContent": ["var es = {\n    \"BoundRoute.title\": \"Ruta enlazada a\",\n    \"EditForm.inputSelect.description.role\": \"Adjuntará el nuevo usuario autenticado al rol seleccionado.\",\n    \"EditForm.inputSelect.label.role\": \"Rol predeterminado para usuarios autenticados\",\n    \"EditForm.inputToggle.description.email\": \"No permita que el usuario cree varias cuentas utilizando la misma dirección de correo electrónico con distintos proveedores de autenticación.\",\n    \"EditForm.inputToggle.description.email-confirmation\": \"Estando habilitado (ON), nuevos usuarios registrados reciben un correo de confirmación.\",\n    \"EditForm.inputToggle.description.email-confirmation-redirection\": \"After confirmed your email, chose where you will be redirected.\",\n    \"EditForm.inputToggle.description.email-reset-password\": \"URL de la página de restablecimiento de contraseña de su aplicación\",\n    \"EditForm.inputToggle.description.sign-up\": \"<PERSON><PERSON><PERSON> est<PERSON> desactivado (OFF), el proceso de registro está prohibido. Nadie puede suscribirse sin importar el proveedor utilizado.\",\n    \"EditForm.inputToggle.label.email\": \"Una cuenta por dirección de correo electrónico\",\n    \"EditForm.inputToggle.label.email-confirmation\": \"Habilitar confirmación de correo\",\n    \"EditForm.inputToggle.label.email-confirmation-redirection\": \"URL de redirección\",\n    \"EditForm.inputToggle.label.email-reset-password\": \"Página de reestablecer la contraseña\",\n    \"EditForm.inputToggle.label.sign-up\": \"Habilitar inscripciones\",\n    \"EditForm.inputToggle.placeholder.email-confirmation-redirection\": \"ej: https://tufrontend.com/restablecer-contrasena\",\n    \"EditForm.inputToggle.placeholder.email-reset-password\": \"ej: https://tufrontend.com/restablecer-contrasena\",\n    \"EditPage.form.roles\": \"Detalles del rol\",\n    \"Email.template.data.loaded\": \"Se han cargado las plantillas de correo electrónico\",\n    \"Email.template.email_confirmation\": \"Confirmación de dirección de correo electrónico\",\n    \"Email.template.form.edit.label\": \"Editar una plantilla\",\n    \"Email.template.table.action.label\": \"acción\",\n    \"Email.template.table.icon.label\": \"icono\",\n    \"Email.template.table.name.label\": \"nombre\",\n    \"Form.advancedSettings.data.loaded\": \"Se han cargado los datos de configuración avanzada\",\n    \"HeaderNav.link.advancedSettings\": \"Ajustes avanzados\",\n    \"HeaderNav.link.emailTemplates\": \"Plantillas de email\",\n    \"HeaderNav.link.providers\": \"Proveedores\",\n    \"Plugin.permissions.plugins.description\": \"Defina todas las acciones permitidas para el plugin {name}.\",\n    \"Plugins.header.description\": \"Sólo las acciones vinculadas a una ruta se enumeran a continuación.\",\n    \"Plugins.header.title\": \"Permisos\",\n    \"Policies.header.hint\": \"Seleccione las acciones de la aplicación o las acciones del plugin y haga clic en el icono del engranaje para ver la ruta vinculada\",\n    \"Policies.header.title\": \"Ajustes avanzados\",\n    \"PopUpForm.Email.email_templates.inputDescription\": \"Si no estás seguro de cómo usar las variables, {link}\",\n    \"PopUpForm.Email.link.documentation\": \"consulte nuestra documentación.\",\n    \"PopUpForm.Email.options.from.email.label\": \"Email del remitente\",\n    \"PopUpForm.Email.options.from.email.placeholder\": \"<EMAIL>\",\n    \"PopUpForm.Email.options.from.name.label\": \"Nombre del remitente\",\n    \"PopUpForm.Email.options.from.name.placeholder\": \"Kai Doe\",\n    \"PopUpForm.Email.options.message.label\": \"Mensaje\",\n    \"PopUpForm.Email.options.object.label\": \"Tema\",\n    \"PopUpForm.Email.options.object.placeholder\": \"Confirma tu dirección de correo electrónico para %APP_NAME%\",\n    \"PopUpForm.Email.options.response_email.label\": \"Email de respuesta\",\n    \"PopUpForm.Email.options.response_email.placeholder\": \"<EMAIL>\",\n    \"PopUpForm.Providers.enabled.description\": \"Si está desactivado, los usuarios no podrán utilizar este proveedor.\",\n    \"PopUpForm.Providers.enabled.label\": \"Habilitar\",\n    \"PopUpForm.Providers.key.label\": \"ID de cliente\",\n    \"PopUpForm.Providers.key.placeholder\": \"TEXTO\",\n    \"PopUpForm.Providers.redirectURL.front-end.label\": \"La URL de redireccionamiento a su aplicación front-end\",\n    \"PopUpForm.Providers.redirectURL.label\": \"La URL de redireccionamiento para agregar en las configuraciones de su aplicación de {proveedor}\",\n    \"PopUpForm.Providers.secret.label\": \"Secreto Cliente\",\n    \"PopUpForm.Providers.secret.placeholder\": \"TEXTO\",\n    \"PopUpForm.Providers.subdomain.label\": \"URI de host (subdominio)\",\n    \"PopUpForm.Providers.subdomain.placeholder\": \"mi.subdominio.com\",\n    \"PopUpForm.header.edit.email-templates\": \"Editar Plantillas de Email\",\n    \"PopUpForm.header.edit.providers\": \"Editar proveedor\",\n    \"Providers.data.loaded\": \"Los proveedores se han cargado\",\n    \"Providers.status\": \"Estado\",\n    \"Roles.empty\": \"Aún no tienes ningún rol.\",\n    \"Roles.empty.search\": \"Ningún rol coincide con la búsqueda.\",\n    \"Settings.roles.deleted\": \"Rol eliminado\",\n    \"Settings.roles.edited\": \"Rol editado\",\n    \"Settings.section-label\": \"Plugin de Usuarios y Permisos\",\n    \"components.Input.error.validation.email\": \"El correo electrónico inválido\",\n    \"components.Input.error.validation.json\": \"No coincide con el formato JSON\",\n    \"components.Input.error.validation.max\": \"El valor es demasiado alto.\",\n    \"components.Input.error.validation.maxLength\": \"El valor es demasiado largo.\",\n    \"components.Input.error.validation.min\": \"El valor es demasiado bajo.\",\n    \"components.Input.error.validation.minLength\": \"El valor es demasiado corto.\",\n    \"components.Input.error.validation.minSupMax\": \"No puede ser superior\",\n    \"components.Input.error.validation.regex\": \"El valor no coincide con la expresión regular.\",\n    \"components.Input.error.validation.required\": \"Este valor es obligatorio.\",\n    \"components.Input.error.validation.unique\": \"Este valor ya se utiliza.\",\n    \"notification.success.submit\": \"Los ajustes se han actualizado\",\n    \"page.title\": \"Configuración - Roles\",\n    \"plugin.description.long\": \"Proteja su API con un proceso de autenticación completo basado en JWT. Este plugin viene también con una estrategia ACL que le permite administrar los permisos entre los grupos de usuarios.\",\n    \"plugin.description.short\": \"Proteja su API con un proceso de autenticación completo basado en JWT\",\n    \"plugin.name\": \"Roles y Permisos\",\n    \"popUpWarning.button.cancel\": \"Cancelar\",\n    \"popUpWarning.button.confirm\": \"Confirmar\",\n    \"popUpWarning.title\": \"Por favor confirme\",\n    \"popUpWarning.warning.cancel\": \"¿Está seguro de que desea cancelar sus modificaciones?\"\n};\n\nexport { es as default };\n//# sourceMappingURL=es.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,KAAK;AAAA,EACL,oBAAoB;AAAA,EACpB,yCAAyC;AAAA,EACzC,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,uDAAuD;AAAA,EACvD,mEAAmE;AAAA,EACnE,yDAAyD;AAAA,EACzD,4CAA4C;AAAA,EAC5C,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,6DAA6D;AAAA,EAC7D,mDAAmD;AAAA,EACnD,sCAAsC;AAAA,EACtC,mEAAmE;AAAA,EACnE,yDAAyD;AAAA,EACzD,uBAAuB;AAAA,EACvB,8BAA8B;AAAA,EAC9B,qCAAqC;AAAA,EACrC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,0CAA0C;AAAA,EAC1C,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,oDAAoD;AAAA,EACpD,sCAAsC;AAAA,EACtC,4CAA4C;AAAA,EAC5C,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,iDAAiD;AAAA,EACjD,yCAAyC;AAAA,EACzC,wCAAwC;AAAA,EACxC,8CAA8C;AAAA,EAC9C,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,iCAAiC;AAAA,EACjC,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,0CAA0C;AAAA,EAC1C,uCAAuC;AAAA,EACvC,6CAA6C;AAAA,EAC7C,yCAAyC;AAAA,EACzC,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,oBAAoB;AAAA,EACpB,eAAe;AAAA,EACf,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,4CAA4C;AAAA,EAC5C,+BAA+B;AAAA,EAC/B,cAAc;AAAA,EACd,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,eAAe;AAAA,EACf,8BAA8B;AAAA,EAC9B,+BAA+B;AAAA,EAC/B,sBAAsB;AAAA,EACtB,+BAA+B;AACnC;", "names": []}