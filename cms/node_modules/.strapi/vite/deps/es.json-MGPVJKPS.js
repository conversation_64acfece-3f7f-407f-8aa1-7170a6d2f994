import "./chunk-PLDDJCW6.js";

// node_modules/@strapi/content-manager/dist/admin/translations/es.json.mjs
var groups = "Grupos";
var models = "Tipos de Colección";
var pageNotFound = "Página no encontrada";
var es = {
  "App.schemas.data-loaded": "Los esquemas se han cargado correctamente.",
  "ListViewTable.relation-loaded": "Las relaciones se han cargado",
  "EditRelations.title": "Datos relacionados",
  "HeaderLayout.button.label-add-entry": "Crear nueva entrada",
  "api.id": "ID de API",
  "components.AddFilterCTA.add": "Filtros",
  "components.AddFilterCTA.hide": "Filtros",
  "components.DragHandle-label": "Arrastrar",
  "components.DraggableAttr.edit": "Click para editar",
  "components.DraggableCard.delete.field": "Borrar {item}",
  "components.DraggableCard.edit.field": "Editar {item}",
  "components.DraggableCard.move.field": "Mover {item}",
  "components.ListViewTable.row-line": "número de elemento {number}",
  "components.DynamicZone.ComponentPicker-label": "Elija un componente",
  "components.DynamicZone.add-component": "Agregue un componente a {componentName}",
  "components.DynamicZone.delete-label": "Eliminar {name}",
  "components.DynamicZone.error-message": "El componente contiene errore(s)",
  "components.DynamicZone.missing-components": "Hay {number, plural, =0 {# componentes faltantes} one {# componente faltante} other {# componentes faltantes}}",
  "components.DynamicZone.move-down-label": "Mover componente hacia abajo",
  "components.DynamicZone.move-up-label": "Mover componente hacia arriba",
  "components.DynamicZone.pick-compo": "Elija un componente",
  "components.DynamicZone.required": "Se requiere un componente",
  "components.EmptyAttributesBlock.button": "Ir a la página de configuraciones",
  "components.EmptyAttributesBlock.description": "Usted puede cambiar sus configuraciones",
  "components.FieldItem.linkToComponentLayout": "Establecer el diseño del componente",
  "components.FieldSelect.label": "Agregar un campo",
  "components.FilterOptions.button.apply": "Aplicar",
  "components.FiltersPickWrapper.PluginHeader.actions.apply": "Aplicar",
  "components.FiltersPickWrapper.PluginHeader.actions.clearAll": "Limpiar todo",
  "components.FiltersPickWrapper.PluginHeader.description": "Establece las condiciones a aplicar para filtrar registros",
  "components.FiltersPickWrapper.PluginHeader.title.filter": "Filtros",
  "components.FiltersPickWrapper.hide": "Ocultar",
  "components.LeftMenu.Search.label": "Buscar un tipo de contenido",
  "components.LeftMenu.collection-types": "Tipos de colección",
  "components.LeftMenu.single-types": "Tipos individuales",
  "components.LimitSelect.itemsPerPage": "registros por página",
  "components.NotAllowedInput.text": "Sin permisos para ver este campo",
  "components.RepeatableComponent.error-message": "Los componentes contienen errores",
  "components.Search.placeholder": "Buscar un registro...",
  "components.Select.draft-info-title": "Estado: Borrador",
  "components.Select.publish-info-title": "Estado: Publicado",
  "components.SettingsViewWrapper.pluginHeader.description.edit-settings": "Personaliza cómo se verá la vista de edición.",
  "components.SettingsViewWrapper.pluginHeader.description.list-settings": "Defina la configuración de la vista de lista.",
  "components.SettingsViewWrapper.pluginHeader.title": "Configurar la vista - {name}",
  "components.TableDelete.delete": "Eliminar todo",
  "components.TableDelete.deleteSelected": "Eliminar seleccionados",
  "components.TableDelete.label": "{number, plural, one {# entrada seleccionada} other {# entradas seleccionads}}",
  "components.TableEmpty.withFilters": "No hay {contentType} con los filtros aplicados...",
  "components.TableEmpty.withSearch": "No hay {contentType} que coincida con la búsqueda ({search})...",
  "components.TableEmpty.withoutFilter": "No hay {contentType}...",
  "components.empty-repeatable": "Aún no hay entrada. Haga clic para agregar una.",
  "components.notification.info.maximum-requirement": "Ya has alcanzado el número máximo de campos",
  "components.notification.info.minimum-requirement": "Se ha agregado un campo para cumplir con el requisito mínimo",
  "components.repeatable.reorder.error": "Se produjo un error al reordenar el campo de su componente. Vuelva a intentarlo.",
  "components.reset-entry": "Restablecer entrada",
  "components.uid.apply": "aplicar",
  "components.uid.available": "disponible",
  "components.uid.regenerate": "regenerado",
  "components.uid.suggested": "sugerido",
  "components.uid.unavailable": "no disponible",
  "containers.Edit.Link.Layout": "Configurar el layout",
  "containers.Edit.Link.Model": "Edita el Tipo de Colección",
  "containers.Edit.addAnItem": "Agregar un registro...",
  "containers.Edit.clickToJump": "Click para ir al registro",
  "containers.Edit.delete": "Eliminar",
  "containers.Edit.delete-entry": "Eliminar esta entrada",
  "containers.Edit.editing": "Editando...",
  "containers.Edit.information": "Información",
  "containers.Edit.information.by": "Por",
  "containers.Edit.information.created": "Creado",
  "containers.Edit.information.draftVersion": "versión preliminar",
  "containers.Edit.information.editing": "Edición",
  "containers.Edit.information.lastUpdate": "Última actualización",
  "containers.Edit.information.publishedVersion": "versión publicada",
  "containers.Edit.pluginHeader.title.new": "Crea una entrada",
  "containers.Edit.reset": "Reiniciar",
  "containers.Edit.returnList": "Regresar a la lista",
  "containers.Edit.seeDetails": "Detalles",
  "containers.Edit.submit": "Guardar",
  "containers.EditSettingsView.modal-form.edit-field": "Edita el campo",
  "containers.EditView.add.new-entry": "Agregar una entrada",
  "containers.EditView.notification.errors": "El formulario contiene algunos errores",
  "containers.Home.introduction": "Para editar sus registros vaya al link en específico en el menu de la izquierda. Este plugin no tiene una manera de editar configuraciones y aún esta en continuo desarrollo.",
  "containers.Home.pluginHeaderDescription": "Gestiona sus registros en una bella y poderoza interfaz.",
  "containers.Home.pluginHeaderTitle": "Gestor de Contenido",
  "containers.List.draft": "Borrador",
  "containers.List.errorFetchRecords": "Error",
  "containers.List.published": "Publicado",
  "containers.list.displayedFields": "Campos mostrados",
  "containers.list.items": "{number, plural, =0 {elementos} one {elemento} other {elementos}}",
  "containers.list.table-headers.publishedAt": "Estado",
  "containers.ListSettingsView.modal-form.edit-label": "Edita la etiqueta",
  "containers.SettingPage.add.field": "Insertar otro campo",
  "containers.SettingPage.attributes": "Campos de atributos",
  "containers.SettingPage.attributes.description": "Defina el orden de sus atributos",
  "containers.SettingPage.editSettings.description": "Arrastra y suelta los campos para construir el diseño",
  "containers.SettingPage.editSettings.entry.title": "Título de la entrada",
  "containers.SettingPage.editSettings.entry.title.description": "Establece el campo para mostar en tu entrada",
  "containers.SettingPage.editSettings.relation-field.description": "Establece el campo mostrado en las vistas de lista y edición",
  "containers.SettingPage.editSettings.title": "Editar (configuraciones)",
  "containers.SettingPage.layout": "Layout",
  "containers.SettingPage.listSettings.description": "Configure las opciones para este Tipo de Colección",
  "containers.SettingPage.listSettings.title": "Lista (configuraciones)",
  "containers.SettingPage.pluginHeaderDescription": "Configure los ajustes específicos para este Tipo de Colección",
  "containers.SettingPage.settings": "Ajustes",
  "containers.SettingPage.view": "Ver",
  "containers.SettingViewModel.pluginHeader.title": "Administrador de contenido - {name}",
  "containers.SettingsPage.Block.contentType.description": "Configuraciones específicas",
  "containers.SettingsPage.Block.contentType.title": "Tipos de Colección",
  "containers.SettingsPage.Block.generalSettings.description": "Configure las opciones predeterminadas para sus Tipos de Colección",
  "containers.SettingsPage.Block.generalSettings.title": "General",
  "containers.SettingsPage.pluginHeaderDescription": "Configure los ajustes para todos sus Tipos y Grupos de Colecciones",
  "containers.SettingsView.list.subtitle": "Configure el diseño y la visualización de sus Tipos y Grupos de Colecciones",
  "containers.SettingsView.list.title": "Configuraciones de pantalla",
  "edit-settings-view.link-to-ctb.components": "Edita el componente",
  "edit-settings-view.link-to-ctb.content-types": "Edita el tipo de contenido",
  "emptyAttributes.button": "Ir al constructor de Tipo de Colección",
  "emptyAttributes.description": "Agregue su primer campo a su Tipo de Colección",
  "emptyAttributes.title": "Aún no hay campos",
  "error.attribute.key.taken": "Este valor ya existe",
  "error.attribute.sameKeyAndName": "No pueden ser iguales",
  "error.attribute.taken": "Este campo ya existe",
  "error.contentTypeName.taken": "Este nombre ya existe",
  "error.model.fetch": "Ocurrió un error durante la consulta de configuración de modelos.",
  "error.record.create": "Ocurrió un error durante la creación del registro.",
  "error.record.delete": "Ocurrió un error durante la eliminación del registro.",
  "error.record.fetch": "Ocurrió un error durante la consulta del registro.",
  "error.record.update": "Ocurrió un error durante la actualización del registro.",
  "error.records.count": "Ocurrió un error durante la consulta del número de registros.",
  "error.records.fetch": "Ocurrió un error durante la consulta de registros.",
  "error.schema.generation": "Ocurrió un error durante la generación de esquema.",
  "error.validation.json": "Este no es un JSON",
  "error.validation.max": "El valor es muy alto.",
  "error.validation.maxLength": "El valor es muy largo.",
  "error.validation.min": "El valor es muy bajo.",
  "error.validation.minLength": "El valor es muy corto.",
  "error.validation.minSupMax": "No puede ser superior",
  "error.validation.regex": "El valor no cumple la expresión regular.",
  "error.validation.required": "Este dato es requerido.",
  "form.Input.bulkActions": "Habilitar acciones en bloque",
  "form.Input.defaultSort": "Atributo para ordenar por defecto",
  "form.Input.description": "Descripción",
  "form.Input.description.placeholder": "Mostrar nombre en el perfíl",
  "form.Input.editable": "Campo editable",
  "form.Input.filters": "Habilitar filtros",
  "form.Input.label": "Etiqueta",
  "form.Input.label.inputDescription": "Este valor sobrescribe la etiqueta mostrada en la cabecera de la tabla",
  "form.Input.pageEntries": "Entradas por página",
  "form.Input.pageEntries.inputDescription": "Nota: Puede anular este valor en la página de configuración de Tipo de Colección.",
  "form.Input.placeholder": "Placeholder",
  "form.Input.placeholder.placeholder": "Mi valor maravilloso",
  "form.Input.search": "Habilitar la búsqueda",
  "form.Input.search.field": "Habilitar la búsqueda para este campo",
  "form.Input.sort.field": "Habilitar ordenado para este campo",
  "form.Input.sort.order": "Orden por defecto",
  "form.Input.wysiwyg": "Mostrar como WYSIWYG",
  "global.displayedFields": "Campos mostrados",
  groups,
  "groups.numbered": "Grupos ({number})",
  "header.name": "Contenido",
  "link-to-ctb": "Edita el modelo",
  models,
  "models.numbered": "Tipos de Colección ({number})",
  "notification.error.displayedFields": "Usted necesita al menos un campo mostrado",
  "notification.error.relationship.fetch": "Ocurrió un error durante la consulta de la relación.",
  "notification.info.SettingPage.disableSort": "Necesita tener un habilidato el ordenado en un atributo",
  "notification.info.minimumFields": "Debe tener al menos un campo mostrado",
  "notification.upload.error": "Se produjo un error al subir sus archivos",
  pageNotFound,
  "pages.ListView.header-subtitle": "{number, plural, =0 {# entradas encontradas} one {# entrada encontrada} other {# entradas encontradas}}",
  "pages.NoContentType.button": "Crea tu primer tipo de contenido",
  "pages.NoContentType.text": "Aún no tiene ningún contenido, le recomendamos que cree su primer tipo de contenido.",
  "permissions.not-allowed.create": "No se le permite crear un documento",
  "permissions.not-allowed.update": "No se le permite ver este documento",
  "plugin.description.long": "Ver, editar y eliminar información de su base de datos de manera rápida.",
  "plugin.description.short": "Ver, editar y eliminar información de su base de datos de manera rápida.",
  "popover.display-relations.label": "Display relations",
  "success.record.delete": "Eliminado",
  "success.record.publish": "Publicado",
  "success.record.save": "Guardado",
  "success.record.unpublish": "Sin publicar",
  "utils.data-loaded": "{number, plural, =1 {La entrada se ha cargado correctamente} other {Las entradas se han cargado correctamente}}",
  "popUpWarning.warning.publish-question": "¿Aún quieres publicarlo?",
  "popUpwarning.warning.has-draft-relations.button-confirm": "Si, publicar"
};
export {
  es as default,
  groups,
  models,
  pageNotFound
};
//# sourceMappingURL=es.json-MGPVJKPS.js.map
