{"version": 3, "sources": ["../../../@strapi/content-manager/dist/admin/translations/es.json.mjs"], "sourcesContent": ["var groups = \"Grupos\";\nvar models = \"Tipos de Colección\";\nvar pageNotFound = \"Página no encontrada\";\nvar es = {\n    \"App.schemas.data-loaded\": \"Los esquemas se han cargado correctamente.\",\n    \"ListViewTable.relation-loaded\": \"Las relaciones se han cargado\",\n    \"EditRelations.title\": \"Datos relacionados\",\n    \"HeaderLayout.button.label-add-entry\": \"Crear nueva entrada\",\n    \"api.id\": \"ID de API\",\n    \"components.AddFilterCTA.add\": \"Filtros\",\n    \"components.AddFilterCTA.hide\": \"Filtros\",\n    \"components.DragHandle-label\": \"Arrastrar\",\n    \"components.DraggableAttr.edit\": \"Click para editar\",\n    \"components.DraggableCard.delete.field\": \"Borrar {item}\",\n    \"components.DraggableCard.edit.field\": \"Editar {item}\",\n    \"components.DraggableCard.move.field\": \"Mover {item}\",\n    \"components.ListViewTable.row-line\": \"número de elemento {number}\",\n    \"components.DynamicZone.ComponentPicker-label\": \"Elija un componente\",\n    \"components.DynamicZone.add-component\": \"Agregue un componente a {componentName}\",\n    \"components.DynamicZone.delete-label\": \"Eliminar {name}\",\n    \"components.DynamicZone.error-message\": \"El componente contiene errore(s)\",\n    \"components.DynamicZone.missing-components\": \"Hay {number, plural, =0 {# componentes faltantes} one {# componente faltante} other {# componentes faltantes}}\",\n    \"components.DynamicZone.move-down-label\": \"Mover componente hacia abajo\",\n    \"components.DynamicZone.move-up-label\": \"Mover componente hacia arriba\",\n    \"components.DynamicZone.pick-compo\": \"Elija un componente\",\n    \"components.DynamicZone.required\": \"Se requiere un componente\",\n    \"components.EmptyAttributesBlock.button\": \"Ir a la página de configuraciones\",\n    \"components.EmptyAttributesBlock.description\": \"Usted puede cambiar sus configuraciones\",\n    \"components.FieldItem.linkToComponentLayout\": \"Establecer el diseño del componente\",\n    \"components.FieldSelect.label\": \"Agregar un campo\",\n    \"components.FilterOptions.button.apply\": \"Aplicar\",\n    \"components.FiltersPickWrapper.PluginHeader.actions.apply\": \"Aplicar\",\n    \"components.FiltersPickWrapper.PluginHeader.actions.clearAll\": \"Limpiar todo\",\n    \"components.FiltersPickWrapper.PluginHeader.description\": \"Establece las condiciones a aplicar para filtrar registros\",\n    \"components.FiltersPickWrapper.PluginHeader.title.filter\": \"Filtros\",\n    \"components.FiltersPickWrapper.hide\": \"Ocultar\",\n    \"components.LeftMenu.Search.label\": \"Buscar un tipo de contenido\",\n    \"components.LeftMenu.collection-types\": \"Tipos de colección\",\n    \"components.LeftMenu.single-types\": \"Tipos individuales\",\n    \"components.LimitSelect.itemsPerPage\": \"registros por página\",\n    \"components.NotAllowedInput.text\": \"Sin permisos para ver este campo\",\n    \"components.RepeatableComponent.error-message\": \"Los componentes contienen errores\",\n    \"components.Search.placeholder\": \"Buscar un registro...\",\n    \"components.Select.draft-info-title\": \"Estado: Borrador\",\n    \"components.Select.publish-info-title\": \"Estado: Publicado\",\n    \"components.SettingsViewWrapper.pluginHeader.description.edit-settings\": \"Personaliza cómo se verá la vista de edición.\",\n    \"components.SettingsViewWrapper.pluginHeader.description.list-settings\": \"Defina la configuración de la vista de lista.\",\n    \"components.SettingsViewWrapper.pluginHeader.title\": \"Configurar la vista - {name}\",\n    \"components.TableDelete.delete\": \"Eliminar todo\",\n    \"components.TableDelete.deleteSelected\": \"Eliminar seleccionados\",\n    \"components.TableDelete.label\": \"{number, plural, one {# entrada seleccionada} other {# entradas seleccionads}}\",\n    \"components.TableEmpty.withFilters\": \"No hay {contentType} con los filtros aplicados...\",\n    \"components.TableEmpty.withSearch\": \"No hay {contentType} que coincida con la búsqueda ({search})...\",\n    \"components.TableEmpty.withoutFilter\": \"No hay {contentType}...\",\n    \"components.empty-repeatable\": \"Aún no hay entrada. Haga clic para agregar una.\",\n    \"components.notification.info.maximum-requirement\": \"Ya has alcanzado el número máximo de campos\",\n    \"components.notification.info.minimum-requirement\": \"Se ha agregado un campo para cumplir con el requisito mínimo\",\n    \"components.repeatable.reorder.error\": \"Se produjo un error al reordenar el campo de su componente. Vuelva a intentarlo.\",\n    \"components.reset-entry\": \"Restablecer entrada\",\n    \"components.uid.apply\": \"aplicar\",\n    \"components.uid.available\": \"disponible\",\n    \"components.uid.regenerate\": \"regenerado\",\n    \"components.uid.suggested\": \"sugerido\",\n    \"components.uid.unavailable\": \"no disponible\",\n    \"containers.Edit.Link.Layout\": \"Configurar el layout\",\n    \"containers.Edit.Link.Model\": \"Edita el Tipo de Colección\",\n    \"containers.Edit.addAnItem\": \"Agregar un registro...\",\n    \"containers.Edit.clickToJump\": \"Click para ir al registro\",\n    \"containers.Edit.delete\": \"Eliminar\",\n    \"containers.Edit.delete-entry\": \"Eliminar esta entrada\",\n    \"containers.Edit.editing\": \"Editando...\",\n    \"containers.Edit.information\": \"Información\",\n    \"containers.Edit.information.by\": \"Por\",\n    \"containers.Edit.information.created\": \"Creado\",\n    \"containers.Edit.information.draftVersion\": \"versión preliminar\",\n    \"containers.Edit.information.editing\": \"Edición\",\n    \"containers.Edit.information.lastUpdate\": \"Última actualización\",\n    \"containers.Edit.information.publishedVersion\": \"versión publicada\",\n    \"containers.Edit.pluginHeader.title.new\": \"Crea una entrada\",\n    \"containers.Edit.reset\": \"Reiniciar\",\n    \"containers.Edit.returnList\": \"Regresar a la lista\",\n    \"containers.Edit.seeDetails\": \"Detalles\",\n    \"containers.Edit.submit\": \"Guardar\",\n    \"containers.EditSettingsView.modal-form.edit-field\": \"Edita el campo\",\n    \"containers.EditView.add.new-entry\": \"Agregar una entrada\",\n    \"containers.EditView.notification.errors\": \"El formulario contiene algunos errores\",\n    \"containers.Home.introduction\": \"Para editar sus registros vaya al link en específico en el menu de la izquierda. Este plugin no tiene una manera de editar configuraciones y aún esta en continuo desarrollo.\",\n    \"containers.Home.pluginHeaderDescription\": \"Gestiona sus registros en una bella y poderoza interfaz.\",\n    \"containers.Home.pluginHeaderTitle\": \"Gestor de Contenido\",\n    \"containers.List.draft\": \"Borrador\",\n    \"containers.List.errorFetchRecords\": \"Error\",\n    \"containers.List.published\": \"Publicado\",\n    \"containers.list.displayedFields\": \"Campos mostrados\",\n    \"containers.list.items\": \"{number, plural, =0 {elementos} one {elemento} other {elementos}}\",\n    \"containers.list.table-headers.publishedAt\": \"Estado\",\n    \"containers.ListSettingsView.modal-form.edit-label\": \"Edita la etiqueta\",\n    \"containers.SettingPage.add.field\": \"Insertar otro campo\",\n    \"containers.SettingPage.attributes\": \"Campos de atributos\",\n    \"containers.SettingPage.attributes.description\": \"Defina el orden de sus atributos\",\n    \"containers.SettingPage.editSettings.description\": \"Arrastra y suelta los campos para construir el diseño\",\n    \"containers.SettingPage.editSettings.entry.title\": \"Título de la entrada\",\n    \"containers.SettingPage.editSettings.entry.title.description\": \"Establece el campo para mostar en tu entrada\",\n    \"containers.SettingPage.editSettings.relation-field.description\": \"Establece el campo mostrado en las vistas de lista y edición\",\n    \"containers.SettingPage.editSettings.title\": \"Editar (configuraciones)\",\n    \"containers.SettingPage.layout\": \"Layout\",\n    \"containers.SettingPage.listSettings.description\": \"Configure las opciones para este Tipo de Colección\",\n    \"containers.SettingPage.listSettings.title\": \"Lista (configuraciones)\",\n    \"containers.SettingPage.pluginHeaderDescription\": \"Configure los ajustes específicos para este Tipo de Colección\",\n    \"containers.SettingPage.settings\": \"Ajustes\",\n    \"containers.SettingPage.view\": \"Ver\",\n    \"containers.SettingViewModel.pluginHeader.title\": \"Administrador de contenido - {name}\",\n    \"containers.SettingsPage.Block.contentType.description\": \"Configuraciones específicas\",\n    \"containers.SettingsPage.Block.contentType.title\": \"Tipos de Colección\",\n    \"containers.SettingsPage.Block.generalSettings.description\": \"Configure las opciones predeterminadas para sus Tipos de Colección\",\n    \"containers.SettingsPage.Block.generalSettings.title\": \"General\",\n    \"containers.SettingsPage.pluginHeaderDescription\": \"Configure los ajustes para todos sus Tipos y Grupos de Colecciones\",\n    \"containers.SettingsView.list.subtitle\": \"Configure el diseño y la visualización de sus Tipos y Grupos de Colecciones\",\n    \"containers.SettingsView.list.title\": \"Configuraciones de pantalla\",\n    \"edit-settings-view.link-to-ctb.components\": \"Edita el componente\",\n    \"edit-settings-view.link-to-ctb.content-types\": \"Edita el tipo de contenido\",\n    \"emptyAttributes.button\": \"Ir al constructor de Tipo de Colección\",\n    \"emptyAttributes.description\": \"Agregue su primer campo a su Tipo de Colección\",\n    \"emptyAttributes.title\": \"Aún no hay campos\",\n    \"error.attribute.key.taken\": \"Este valor ya existe\",\n    \"error.attribute.sameKeyAndName\": \"No pueden ser iguales\",\n    \"error.attribute.taken\": \"Este campo ya existe\",\n    \"error.contentTypeName.taken\": \"Este nombre ya existe\",\n    \"error.model.fetch\": \"Ocurrió un error durante la consulta de configuración de modelos.\",\n    \"error.record.create\": \"Ocurrió un error durante la creación del registro.\",\n    \"error.record.delete\": \"Ocurrió un error durante la eliminación del registro.\",\n    \"error.record.fetch\": \"Ocurrió un error durante la consulta del registro.\",\n    \"error.record.update\": \"Ocurrió un error durante la actualización del registro.\",\n    \"error.records.count\": \"Ocurrió un error durante la consulta del número de registros.\",\n    \"error.records.fetch\": \"Ocurrió un error durante la consulta de registros.\",\n    \"error.schema.generation\": \"Ocurrió un error durante la generación de esquema.\",\n    \"error.validation.json\": \"Este no es un JSON\",\n    \"error.validation.max\": \"El valor es muy alto.\",\n    \"error.validation.maxLength\": \"El valor es muy largo.\",\n    \"error.validation.min\": \"El valor es muy bajo.\",\n    \"error.validation.minLength\": \"El valor es muy corto.\",\n    \"error.validation.minSupMax\": \"No puede ser superior\",\n    \"error.validation.regex\": \"El valor no cumple la expresión regular.\",\n    \"error.validation.required\": \"Este dato es requerido.\",\n    \"form.Input.bulkActions\": \"Habilitar acciones en bloque\",\n    \"form.Input.defaultSort\": \"Atributo para ordenar por defecto\",\n    \"form.Input.description\": \"Descripción\",\n    \"form.Input.description.placeholder\": \"Mostrar nombre en el perfíl\",\n    \"form.Input.editable\": \"Campo editable\",\n    \"form.Input.filters\": \"Habilitar filtros\",\n    \"form.Input.label\": \"Etiqueta\",\n    \"form.Input.label.inputDescription\": \"Este valor sobrescribe la etiqueta mostrada en la cabecera de la tabla\",\n    \"form.Input.pageEntries\": \"Entradas por página\",\n    \"form.Input.pageEntries.inputDescription\": \"Nota: Puede anular este valor en la página de configuración de Tipo de Colección.\",\n    \"form.Input.placeholder\": \"Placeholder\",\n    \"form.Input.placeholder.placeholder\": \"Mi valor maravilloso\",\n    \"form.Input.search\": \"Habilitar la búsqueda\",\n    \"form.Input.search.field\": \"Habilitar la búsqueda para este campo\",\n    \"form.Input.sort.field\": \"Habilitar ordenado para este campo\",\n    \"form.Input.sort.order\": \"Orden por defecto\",\n    \"form.Input.wysiwyg\": \"Mostrar como WYSIWYG\",\n    \"global.displayedFields\": \"Campos mostrados\",\n    groups: groups,\n    \"groups.numbered\": \"Grupos ({number})\",\n    \"header.name\": \"Contenido\",\n    \"link-to-ctb\": \"Edita el modelo\",\n    models: models,\n    \"models.numbered\": \"Tipos de Colección ({number})\",\n    \"notification.error.displayedFields\": \"Usted necesita al menos un campo mostrado\",\n    \"notification.error.relationship.fetch\": \"Ocurrió un error durante la consulta de la relación.\",\n    \"notification.info.SettingPage.disableSort\": \"Necesita tener un habilidato el ordenado en un atributo\",\n    \"notification.info.minimumFields\": \"Debe tener al menos un campo mostrado\",\n    \"notification.upload.error\": \"Se produjo un error al subir sus archivos\",\n    pageNotFound: pageNotFound,\n    \"pages.ListView.header-subtitle\": \"{number, plural, =0 {# entradas encontradas} one {# entrada encontrada} other {# entradas encontradas}}\",\n    \"pages.NoContentType.button\": \"Crea tu primer tipo de contenido\",\n    \"pages.NoContentType.text\": \"Aún no tiene ningún contenido, le recomendamos que cree su primer tipo de contenido.\",\n    \"permissions.not-allowed.create\": \"No se le permite crear un documento\",\n    \"permissions.not-allowed.update\": \"No se le permite ver este documento\",\n    \"plugin.description.long\": \"Ver, editar y eliminar información de su base de datos de manera rápida.\",\n    \"plugin.description.short\": \"Ver, editar y eliminar información de su base de datos de manera rápida.\",\n    \"popover.display-relations.label\": \"Display relations\",\n    \"success.record.delete\": \"Eliminado\",\n    \"success.record.publish\": \"Publicado\",\n    \"success.record.save\": \"Guardado\",\n    \"success.record.unpublish\": \"Sin publicar\",\n    \"utils.data-loaded\": \"{number, plural, =1 {La entrada se ha cargado correctamente} other {Las entradas se han cargado correctamente}}\",\n    \"popUpWarning.warning.publish-question\": \"¿Aún quieres publicarlo?\",\n    \"popUpwarning.warning.has-draft-relations.button-confirm\": \"Si, publicar\"\n};\n\nexport { es as default, groups, models, pageNotFound };\n//# sourceMappingURL=es.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,SAAS;AACb,IAAI,SAAS;AACb,IAAI,eAAe;AACnB,IAAI,KAAK;AAAA,EACL,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,uBAAuB;AAAA,EACvB,uCAAuC;AAAA,EACvC,UAAU;AAAA,EACV,+BAA+B;AAAA,EAC/B,gCAAgC;AAAA,EAChC,+BAA+B;AAAA,EAC/B,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,gDAAgD;AAAA,EAChD,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,wCAAwC;AAAA,EACxC,6CAA6C;AAAA,EAC7C,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,gCAAgC;AAAA,EAChC,yCAAyC;AAAA,EACzC,4DAA4D;AAAA,EAC5D,+DAA+D;AAAA,EAC/D,0DAA0D;AAAA,EAC1D,2DAA2D;AAAA,EAC3D,sCAAsC;AAAA,EACtC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,uCAAuC;AAAA,EACvC,mCAAmC;AAAA,EACnC,gDAAgD;AAAA,EAChD,iCAAiC;AAAA,EACjC,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,yEAAyE;AAAA,EACzE,yEAAyE;AAAA,EACzE,qDAAqD;AAAA,EACrD,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,gCAAgC;AAAA,EAChC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,uCAAuC;AAAA,EACvC,+BAA+B;AAAA,EAC/B,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,uCAAuC;AAAA,EACvC,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,6BAA6B;AAAA,EAC7B,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA,EAC9B,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,6BAA6B;AAAA,EAC7B,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,kCAAkC;AAAA,EAClC,uCAAuC;AAAA,EACvC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,yBAAyB;AAAA,EACzB,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,qDAAqD;AAAA,EACrD,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,gCAAgC;AAAA,EAChC,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,6CAA6C;AAAA,EAC7C,qDAAqD;AAAA,EACrD,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,+DAA+D;AAAA,EAC/D,kEAAkE;AAAA,EAClE,6CAA6C;AAAA,EAC7C,iCAAiC;AAAA,EACjC,mDAAmD;AAAA,EACnD,6CAA6C;AAAA,EAC7C,kDAAkD;AAAA,EAClD,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,kDAAkD;AAAA,EAClD,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,6DAA6D;AAAA,EAC7D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,yCAAyC;AAAA,EACzC,sCAAsC;AAAA,EACtC,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,0BAA0B;AAAA,EAC1B,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,qCAAqC;AAAA,EACrC,0BAA0B;AAAA,EAC1B,2CAA2C;AAAA,EAC3C,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B;AAAA,EACA,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,eAAe;AAAA,EACf;AAAA,EACA,mBAAmB;AAAA,EACnB,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B;AAAA,EACA,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,4BAA4B;AAAA,EAC5B,qBAAqB;AAAA,EACrB,yCAAyC;AAAA,EACzC,2DAA2D;AAC/D;", "names": []}