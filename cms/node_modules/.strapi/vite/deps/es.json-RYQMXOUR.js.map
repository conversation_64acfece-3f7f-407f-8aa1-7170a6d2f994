{"version": 3, "sources": ["../../../@strapi/admin/dist/admin/admin/src/translations/es.json.mjs"], "sourcesContent": ["var Analytics = \"Analytics\";\nvar Documentation = \"Documentación\";\nvar Email = \"Email\";\nvar Password = \"Contraseña\";\nvar Provider = \"Proveedor\";\nvar ResetPasswordToken = \"Restablecer Token de Contraseña\";\nvar Role = \"Rol\";\nvar Username = \"Nombre de usuario\";\nvar Users = \"Usuarios\";\nvar anErrorOccurred = \"¡Ups! Algo salió mal. Inténtalo de nuevo.\";\nvar clearLabel = \"Limpiar\";\nvar or = \"O\";\nvar skipToContent = \"Saltar al contenido\";\nvar submit = \"Enviar\";\nvar light = \"Claro\";\nvar dark = \"Oscuro\";\nvar es = {\n    Analytics: Analytics,\n    \"Auth.components.Oops.text\": \"Tu cuenta ha sido suspendida\",\n    \"Auth.components.Oops.text.admin\": \"Si se trata de un error, comuníquese con su administrador.\",\n    \"Auth.components.Oops.title\": \"Ups...\",\n    \"Auth.form.button.forgot-password\": \"Enviar Email\",\n    \"Auth.form.button.go-home\": \"REGRESAR A CASA\",\n    \"Auth.form.button.login\": \"Iniciar sesión\",\n    \"Auth.form.button.login.providers.error\": \"No podemos conectarlo a través del proveedor seleccionado.\",\n    \"Auth.form.button.login.strapi\": \"Iniciar sesión a través de Strapi\",\n    \"Auth.form.button.password-recovery\": \"Recuperación de Contraseña\",\n    \"Auth.form.button.register\": \"Listo para comenzar\",\n    \"Auth.form.confirmPassword.label\": \"Confirmación de contraseña\",\n    \"Auth.form.currentPassword.label\": \"Contraseña actual\",\n    \"Auth.form.email.label\": \"Correo electrónico\",\n    \"Auth.form.email.placeholder\": \"<EMAIL>\",\n    \"Auth.form.error.blocked\": \"Su cuenta ha sido bloqueada por el administrador.\",\n    \"Auth.form.error.code.provide\": \"Código incorrecto proporcionado.\",\n    \"Auth.form.error.confirmed\": \"Su cuenta de correo no ha sido confirmada.\",\n    \"Auth.form.error.email.invalid\": \"Este email es inválido.\",\n    \"Auth.form.error.email.provide\": \"Por favor proporcione su nombre de usuario o su correo electrónico.\",\n    \"Auth.form.error.email.taken\": \"El email ya está registrado\",\n    \"Auth.form.error.invalid\": \"Identificador o contraseña inválidos.\",\n    \"Auth.form.error.params.provide\": \"Parametros incorrectos proporcionados.\",\n    \"Auth.form.error.password.format\": \"Su contraseña no puede contener el símbolo `$` más de tres veces.\",\n    \"Auth.form.error.password.local\": \"Este usuario nunca estableció una contraseña local, por favor ingrese a través de su proveedor utilizado durante la creación de la cuenta.\",\n    \"Auth.form.error.password.matching\": \"Las contraseñas no coinciden.\",\n    \"Auth.form.error.password.provide\": \"Por favor, introduzca su contraseña.\",\n    \"Auth.form.error.ratelimit\": \"Demasiados intentos. Por favor vuelva a intentarlo dentro de un minuto.\",\n    \"Auth.form.error.user.not-exist\": \"Este email no existe.\",\n    \"Auth.form.error.username.taken\": \"El nombre de usuario ya está registrado\",\n    \"Auth.form.firstname.label\": \"Nombre\",\n    \"Auth.form.firstname.placeholder\": \"Juan\",\n    \"Auth.form.forgot-password.email.label\": \"Introduce tu email\",\n    \"Auth.form.forgot-password.email.label.success\": \"Email enviado con éxito a\",\n    \"Auth.form.lastname.label\": \"Apellido\",\n    \"Auth.form.lastname.placeholder\": \"Pérez\",\n    \"Auth.form.password.hide-password\": \"Ocultar contraseña\",\n    \"Auth.form.password.hint\": \"La contraseña debe contener al menos 8 caracteres, 1 mayúscula, 1 minúscula y 1 número\",\n    \"Auth.form.password.show-password\": \"Mostrar contraseña\",\n    \"Auth.form.register.news.label\": \"Mantenerme informado sobre las nuevas funciones y las próximas mejoras (al hacer esto, acepta las {terms} y la {policy}).\",\n    \"Auth.form.register.subtitle\": \"Sus credenciales solo se utilizan para autenticarse en el panel de administración. Todos los datos guardados se almacenarán en su propia base de datos.\",\n    \"Auth.form.rememberMe.label\": \"Recuérdame\",\n    \"Auth.form.username.label\": \"Usuario\",\n    \"Auth.form.username.placeholder\": \"Kai Doe\",\n    \"Auth.form.welcome.subtitle\": \"Inicie sesión en su cuenta de Strapi\",\n    \"Auth.form.welcome.title\": \"Bienvenido!\",\n    \"Auth.link.forgot-password\": \"¿Olvidó su contraseña?\",\n    \"Auth.link.ready\": \"¿Listo para iniciar sesión?\",\n    \"Auth.link.signin\": \"Registrarse\",\n    \"Auth.link.signin.account\": \"¿Ya tienes una cuenta?\",\n    \"Auth.login.sso.divider\": \"O inicia sesión con\",\n    \"Auth.login.sso.loading\": \"Cargando proveedores...\",\n    \"Auth.login.sso.subtitle\": \"Inicie sesión en su cuenta a través de SSO\",\n    \"Auth.privacy-policy-agreement.policy\": \"política de privacidad\",\n    \"Auth.privacy-policy-agreement.terms\": \"condiciones\",\n    \"Content Manager\": \"Gestor de Contenidos\",\n    \"Content Type Builder\": \"Constructor de Tipos de Contenido\",\n    Documentation: Documentation,\n    Email: Email,\n    \"Files Upload\": \"Subida de archivos\",\n    \"HomePage.head.title\": \"Página principal\",\n    \"HomePage.roadmap\": \"Vea nuestra hoja de ruta\",\n    \"HomePage.welcome.congrats\": \"¡Felicidades!\",\n    \"HomePage.welcome.congrats.content\": \"Está registrado como el primer administrador. \\nPara descubrir las potentes funciones que ofrece Strapi,\",\n    \"HomePage.welcome.congrats.content.bold\": \"le recomendamos que cree su primer Tipo de Colección.\",\n    \"Media Library\": \"Biblioteca de Multimedia\",\n    \"New entry\": \"Entrada nueva\",\n    Password: Password,\n    Provider: Provider,\n    ResetPasswordToken: ResetPasswordToken,\n    Role: Role,\n    \"Roles & Permissions\": \"Roles y Permisos\",\n    \"Roles.ListPage.notification.delete-all-not-allowed\": \"Algunos roles no se pudieron eliminar porque están asociados a usuarios\",\n    \"Roles.ListPage.notification.delete-not-allowed\": \"No se puede eliminar un rol si está asociado a usuarios\",\n    \"Roles.RoleRow.select-all\": \"Seleccione {name} para acciones en bloque\",\n    \"Roles.components.List.empty.withSearch\": \"No hay rol correspondiente a la búsqueda ({search})...\",\n    \"Settings.PageTitle\": \"Configuración - {name}\",\n    \"Settings.apiTokens.addFirstToken\": \"Agrega tu primer token de API\",\n    \"Settings.apiTokens.addNewToken\": \"Agregar nuevo token de API\",\n    \"Settings.tokens.copy.editMessage\": \"Por razones de seguridad, solo puede ver su token una vez.\",\n    \"Settings.tokens.copy.editTitle\": \"Este token ya no es accesible.\",\n    \"Settings.tokens.copy.lastWarning\": \"¡Asegúrate de copiar este token, no podrás volver a verlo!\",\n    \"Settings.apiTokens.create\": \"Añadir entrada\",\n    \"Settings.apiTokens.description\": \"Lista de tokens generados para consumir la API\",\n    \"Settings.apiTokens.emptyStateLayout\": \"Aún no tienes ningún contenido ...\",\n    \"Settings.tokens.notification.copied\": \"Token copiado al portapapeles.\",\n    \"Settings.apiTokens.title\": \"Tokens de API\",\n    \"Settings.apiTokens.lastHour\": \"última hora\",\n    \"Settings.tokens.ListView.headers.createdAt\": \"Creado en\",\n    \"Settings.tokens.ListView.headers.description\": \"Descripción\",\n    \"Settings.tokens.ListView.headers.lastUsedAt\": \"Último uso\",\n    \"Settings.tokens.ListView.headers.name\": \"Nombre\",\n    \"Settings.tokens.types.full-access\": \"Acceso completo\",\n    \"Settings.tokens.types.read-only\": \"Solo lectura\",\n    \"Settings.application.description\": \"Información global del panel de administración\",\n    \"Settings.application.edition-title\": \"Edición actual\",\n    \"Settings.application.get-help\": \"Consigue ayuda\",\n    \"Settings.application.link-pricing\": \"Ver todos los planes\",\n    \"Settings.application.link-upgrade\": \"Actualiza tu panel de administración\",\n    \"Settings.application.node-version\": \"versión de node\",\n    \"Settings.application.strapi-version\": \"versión de strapi\",\n    \"Settings.application.strapiVersion\": \"versión de strapi\",\n    \"Settings.application.title\": \"Descripción general\",\n    \"Settings.error\": \"Error\",\n    \"Settings.global\": \"Configuración global\",\n    \"Settings.permissions\": \"Panel de administración\",\n    \"Settings.permissions.category\": \"Configuración de permisos para {category}\",\n    \"Settings.permissions.category.plugins\": \"Configuración de permisos para el plugin de {category}\",\n    \"Settings.permissions.conditions.anytime\": \"En cualquier momento\",\n    \"Settings.permissions.conditions.apply\": \"Aplicar\",\n    \"Settings.permissions.conditions.can\": \"Poder\",\n    \"Settings.permissions.conditions.conditions\": \"Definir condiciones\",\n    \"Settings.permissions.conditions.links\": \"Enlaces\",\n    \"Settings.permissions.conditions.no-actions\": \"No hay acción\",\n    \"Settings.permissions.conditions.none-selected\": \"En cualquier momento\",\n    \"Settings.permissions.conditions.or\": \"O\",\n    \"Settings.permissions.conditions.when\": \"Cuando\",\n    \"Settings.permissions.select-all-by-permission\": \"Seleccionar todos los permisos de {label}\",\n    \"Settings.permissions.select-by-permission\": \"Seleccionar permiso de {label}\",\n    \"Settings.permissions.users.create\": \"Crear nuevo usuario\",\n    \"Settings.permissions.users.email\": \"Correo electrónico\",\n    \"Settings.permissions.users.firstname\": \"Nombre\",\n    \"Settings.permissions.users.lastname\": \"Apellido\",\n    \"Settings.permissions.users.form.sso\": \"connect with sso\",\n    \"Settings.permissions.users.form.sso.description\": \"when enabled (on), users can login via sso\",\n    \"Settings.permissions.users.listview.header.subtitle\": \"Todos los usuarios que tienen acceso al panel de administración de strapi\",\n    \"Settings.permissions.users.tabs.label\": \"Permisos de pestañas\",\n    \"Settings.profile.form.notify.data.loaded\": \"Se han cargado los datos de tu perfil\",\n    \"Settings.profile.form.section.experience.clear.select\": \"Borrar el idioma de interfaz seleccionado\",\n    \"Settings.profile.form.section.experience.here\": \"documentación\",\n    \"Settings.profile.form.section.experience.interfaceLanguage\": \"Idioma de interfaz\",\n    \"Settings.profile.form.section.experience.interfaceLanguage.hint\": \"Esto solo mostrará su propia interfaz en el idioma elegido.\",\n    \"Settings.profile.form.section.experience.interfaceLanguageHelp\": \"La selección cambiará el idioma de la interfaz solo para usted. Consulte esta {here} para que otros idiomas estén disponibles para su equipo.\",\n    \"Settings.profile.form.section.experience.title\": \"Experiencia\",\n    \"Settings.profile.form.section.head.title\": \"Perfil de usuario\",\n    \"Settings.profile.form.section.profile.page.title\": \"Página de perfil\",\n    \"Settings.roles.create.description\": \"Definir los derechos otorgados al rol\",\n    \"Settings.roles.create.title\": \"Crea un rol\",\n    \"Settings.roles.created\": \"Rol creado\",\n    \"Settings.roles.edit.title\": \"Editar un rol\",\n    \"Settings.roles.form.button.users-with-role\": \"Usuarios con este rol\",\n    \"Settings.roles.form.created\": \"Creado\",\n    \"Settings.roles.form.description\": \"Nombre y descripción del rol\",\n    \"Settings.roles.form.permission.property-label\": \"permisos de {label}\",\n    \"Settings.roles.form.permissions.attributesPermissions\": \"Permisos de los campos\",\n    \"Settings.roles.form.permissions.create\": \"Crear\",\n    \"Settings.roles.form.permissions.delete\": \"Eliminar\",\n    \"Settings.roles.form.permissions.publish\": \"Publicar\",\n    \"Settings.roles.form.permissions.read\": \"Leer\",\n    \"Settings.roles.form.permissions.update\": \"Actualizar\",\n    \"Settings.roles.list.button.add\": \"Agregar nuevo rol\",\n    \"Settings.roles.list.description\": \"Lista de roles\",\n    \"Settings.roles.title.singular\": \"rol\",\n    \"Settings.sso.description\": \"Configure los ajustes para la función de inicio de sesión único (SSO).\",\n    \"Settings.sso.form.defaultRole.description\": \"Asociará al nuevo usuario autenticado al rol seleccionado\",\n    \"Settings.sso.form.defaultRole.description-not-allowed\": \"Debes tener permiso para leer los roles de administrador.\",\n    \"Settings.sso.form.defaultRole.label\": \"Rol predeterminado\",\n    \"Settings.sso.form.registration.description\": \"Crear un nuevo usuario en el inicio de sesión (SSO) si no existe una cuenta\",\n    \"Settings.sso.form.registration.label\": \"Auto-registro\",\n    \"Settings.sso.title\": \"Inicio de sesión único (SSO)\",\n    \"Settings.webhooks.create\": \"Crea un webhook\",\n    \"Settings.webhooks.create.header\": \"Crea un nuevo encabezado\",\n    \"Settings.webhooks.created\": \"Webhook creado\",\n    \"Settings.webhooks.event.publish-tooltip\": \"Este evento solo existe para contenidos con el sistema Borrador/Publicación habilitado\",\n    \"Settings.webhooks.events.create\": \"Crear\",\n    \"Settings.webhooks.events.update\": \"Actualizar\",\n    \"Settings.webhooks.form.events\": \"Eventos\",\n    \"Settings.webhooks.form.headers\": \"Encabezados\",\n    \"Settings.webhooks.form.url\": \"Url\",\n    \"Settings.webhooks.headers.remove\": \"eliminar fila de encabezado {number}\",\n    \"Settings.webhooks.key\": \"Clave\",\n    \"Settings.webhooks.list.button.add\": \"Agregar nuevo webhook\",\n    \"Settings.webhooks.list.description\": \"Recibe notificaciones de cambios POST.\",\n    \"Settings.webhooks.list.empty.description\": \"Agregue el primero a esta lista.\",\n    \"Settings.webhooks.list.empty.link\": \"Ver nuestra documentación\",\n    \"Settings.webhooks.list.empty.title\": \"Todavía no hay webhooks\",\n    \"Settings.webhooks.list.th.actions\": \"acciones\",\n    \"Settings.webhooks.list.th.status\": \"estado\",\n    \"Settings.webhooks.singular\": \"webhook\",\n    \"Settings.webhooks.title\": \"Webhooks\",\n    \"Settings.webhooks.to.delete\": \"{webhooksToDeleteLength, plural, one {# recurso seleccionado} other {# recursos seleccionados}}\",\n    \"Settings.webhooks.trigger\": \"Desencadenante\",\n    \"Settings.webhooks.trigger.cancel\": \"Cancelar disparador\",\n    \"Settings.webhooks.trigger.pending\": \"Pendiente…\",\n    \"Settings.webhooks.trigger.save\": \"Guarde para desencadenar\",\n    \"Settings.webhooks.trigger.success\": \"¡Éxito!\",\n    \"Settings.webhooks.trigger.success.label\": \"Desencadenante éxitoso\",\n    \"Settings.webhooks.trigger.test\": \"Probar desencadenante\",\n    \"Settings.webhooks.trigger.title\": \"Guardar antes de desencadenar\",\n    \"Settings.webhooks.value\": \"Valor\",\n    Username: Username,\n    Users: Users,\n    \"Users & Permissions\": \"Usuarios y permisos\",\n    \"Users.components.List.empty\": \"No hay usuarios...\",\n    \"Users.components.List.empty.withFilters\": \"No hay usuarios con los filtros aplicados...\",\n    \"Users.components.List.empty.withSearch\": \"No hay usuarios correspondientes a la búsqueda ({search})...\",\n    \"admin.pages.MarketPlacePage.head\": \"Marketplace - Plugins\",\n    \"admin.pages.MarketPlacePage.submit.plugin.link\": \"Envíe su plugin\",\n    \"admin.pages.MarketPlacePage.subtitle\": \"Saca más partido a Strapi\",\n    anErrorOccurred: anErrorOccurred,\n    \"app.component.CopyToClipboard.label\": \"Copiar al portapapeles\",\n    \"app.component.search.label\": \"Buscar {target}\",\n    \"app.component.table.duplicate\": \"Copiar {target}\",\n    \"app.component.table.edit\": \"Editar {target}\",\n    \"app.component.table.select.one-entry\": \"Seleccionar {target}\",\n    \"app.components.BlockLink.blog\": \"Blog\",\n    \"app.components.BlockLink.blog.content\": \"Lea las últimas noticias sobre Strapi y el ecosistema.\",\n    \"app.components.BlockLink.code\": \"Ejemplos de código\",\n    \"app.components.BlockLink.code.content\": \"Aprenda probando proyectos reales desarrollados por la comunidad.\",\n    \"app.components.BlockLink.documentation.content\": \"Descubra los conceptos esenciales, guías e instrucciones.\",\n    \"app.components.BlockLink.tutorial\": \"Tutoriales\",\n    \"app.components.BlockLink.tutorial.content\": \"Siga las instrucciones paso a paso para usar y personalizar Strapi.\",\n    \"app.components.Button.cancel\": \"Cancelar\",\n    \"app.components.Button.confirm\": \"Confirmar\",\n    \"app.components.Button.reset\": \"Reiniciar\",\n    \"app.components.ComingSoonPage.comingSoon\": \"Próximamente\",\n    \"app.components.ConfirmDialog.title\": \"Confirmación\",\n    \"app.components.DownloadInfo.download\": \"Descarga en curso...\",\n    \"app.components.DownloadInfo.text\": \"Esto puede tardar un minuto. Gracias por su paciencia.\",\n    \"app.components.EmptyAttributes.title\": \"Aún no hay campos\",\n    \"app.components.EmptyStateLayout.content-document\": \"No se encontró contenido\",\n    \"app.components.EmptyStateLayout.content-permissions\": \"No tienes los permisos para acceder a ese contenido.\",\n    \"app.components.HomePage.button.blog\": \"VER MÁS EN EL BLOG\",\n    \"app.components.HomePage.community\": \"Encuentre la comunidad en la web\",\n    \"app.components.HomePage.community.content\": \"Hable con los miembros del equipo, colaboradores y desarrolladores en diferentes canales.\",\n    \"app.components.HomePage.create\": \"Crea tu primer Tipo de Contenido\",\n    \"app.components.HomePage.roadmap\": \"Vea nuestros próximos objetivos\",\n    \"app.components.HomePage.welcome\": \"¡Bienvenido a bordo!\",\n    \"app.components.HomePage.welcome.again\": \"¡Bienvenido \",\n    \"app.components.HomePage.welcomeBlock.content\": \"Estamos felices de tenerlo como miembro de la comunidad. Estamos constantemente en busca de comentarios así que no dude en enviarnos un DM en \",\n    \"app.components.HomePage.welcomeBlock.content.again\": \"Esperamos que estés progresando en tu proyecto.... Siéntase libre de leer las últimas novedades sobre Strapi. Estamos dando lo mejor de nosotros mismos para mejorar el producto basándonos en sus comentarios.\",\n    \"app.components.HomePage.welcomeBlock.content.issues\": \"problema.\",\n    \"app.components.HomePage.welcomeBlock.content.raise\": \" o reportar cualquier \",\n    \"app.components.ImgPreview.hint\": \"Arrastre y suelte el archivo en esta área o {browse} para subir un archivo.\",\n    \"app.components.ImgPreview.hint.browse\": \"buscar\",\n    \"app.components.InputFile.newFile\": \"Añadir nuevo archivo\",\n    \"app.components.InputFileDetails.open\": \"Abrir en una nueva pestaña\",\n    \"app.components.InputFileDetails.originalName\": \"Nombre original:\",\n    \"app.components.InputFileDetails.remove\": \"Eliminar este archivo\",\n    \"app.components.InputFileDetails.size\": \"Tamaño:\",\n    \"app.components.InstallPluginPage.Download.description\": \"La descarga e instalación del plugin podría llevar unos segundos.\",\n    \"app.components.InstallPluginPage.Download.title\": \"Descargando...\",\n    \"app.components.InstallPluginPage.description\": \"Extienda su aplicación sin esfuerzo.\",\n    \"app.components.LeftMenu.collapse\": \"Contraer la barra de navegación\",\n    \"app.components.LeftMenu.expand\": \"Expandir la barra de navegación\",\n    \"app.components.LeftMenu.logout\": \"Cerrar sesión\",\n    \"app.components.LeftMenu.trialCountdown\": \"Tu prueba termina el {date}.\",\n    \"app.components.LeftMenuFooter.help\": \"Ayuda\",\n    \"app.components.LeftMenuFooter.poweredBy\": \"Potenciado por \",\n    \"app.components.LeftMenuLinkContainer.collectionTypes\": \"Tipos de Colección\",\n    \"app.components.LeftMenuLinkContainer.configuration\": \"Configuraciones\",\n    \"app.components.LeftMenuLinkContainer.general\": \"General\",\n    \"app.components.LeftMenuLinkContainer.noPluginsInstalled\": \"No hay plugins instalados todavía\",\n    \"app.components.LeftMenuLinkContainer.plugins\": \"Plugins\",\n    \"app.components.LeftMenuLinkContainer.singleTypes\": \"Tipos Únicos\",\n    \"app.components.ListPluginsPage.deletePlugin.description\": \"Es posible que la desinstalación del plugin tarde unos segundos.\",\n    \"app.components.ListPluginsPage.deletePlugin.title\": \"Desinstalar\",\n    \"app.components.ListPluginsPage.description\": \"Lista de los plugins instalados en el proyecto.\",\n    \"app.components.ListPluginsPage.head.title\": \"Lista de plugins\",\n    \"app.components.Logout.logout\": \"Cerrar sesión\",\n    \"app.components.Logout.profile\": \"Perfil\",\n    \"app.components.MarketplaceBanner\": \"Descubra los plugins creados por la comunidad y muchas más cosas increíbles para impulsar su proyecto, en Strapi Awesome.\",\n    \"app.components.MarketplaceBanner.image.alt\": \"un logo de cohete strapi\",\n    \"app.components.MarketplaceBanner.link\": \"Échale un vistazo ahora\",\n    \"app.components.NotFoundPage.back\": \"Volver a la página de inicio\",\n    \"app.components.NotFoundPage.description\": \"No encontrado\",\n    \"app.components.Official\": \"Oficial\",\n    \"app.components.Onboarding.help.button\": \"Boton de ayuda\",\n    \"app.components.Onboarding.label.completed\": \"% completado\",\n    \"app.components.Onboarding.title\": \"Vídeos introductorios\",\n    \"app.components.PluginCard.Button.label.download\": \"Descargar\",\n    \"app.components.PluginCard.Button.label.install\": \"Ya instalado\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed\": \"La función de recarga automática debe estar desactivada. Por favor, inicie su aplicación con `yarn develop`.\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.confirm\": \"¡Entendido!\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.environment\": \"Por motivos de seguridad, el plugin sólo puede descargarse en entorno de desarrollo.\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.title\": \"Imposible descargar\",\n    \"app.components.PluginCard.compatible\": \"Compatible con su aplicación\",\n    \"app.components.PluginCard.compatibleCommunity\": \"Compatible con la comunidad\",\n    \"app.components.PluginCard.more-details\": \"Más detalles\",\n    \"app.components.ToggleCheckbox.off-label\": \"Apagado\",\n    \"app.components.ToggleCheckbox.on-label\": \"Encendido\",\n    \"app.components.Users.MagicLink.connect\": \"Envíe este enlace al usuario para que se conecte.\",\n    \"app.components.Users.MagicLink.connect.sso\": \"Envíe este enlace al usuario, el primer inicio de sesión se puede realizar a través de un proveedor de SSO\",\n    \"app.components.Users.ModalCreateBody.block-title.details\": \"Detalles\",\n    \"app.components.Users.ModalCreateBody.block-title.roles\": \"Roles del usuario\",\n    \"app.components.Users.ModalCreateBody.block-title.roles.description\": \"Un usuario puede tener uno o varios roles.\",\n    \"app.components.Users.SortPicker.button-label\": \"Ordenar por\",\n    \"app.components.Users.SortPicker.sortby.email_asc\": \"Correo electrónico (de la A a la Z)\",\n    \"app.components.Users.SortPicker.sortby.email_desc\": \"Correo electrónico (de la Z a la A)\",\n    \"app.components.Users.SortPicker.sortby.firstname_asc\": \"Nombre (de la A a la Z)\",\n    \"app.components.Users.SortPicker.sortby.firstname_desc\": \"Nombre (de la Z a la A)\",\n    \"app.components.Users.SortPicker.sortby.lastname_asc\": \"Apellido (de la A a la Z)\",\n    \"app.components.Users.SortPicker.sortby.lastname_desc\": \"Apellido (de la Z a la A)\",\n    \"app.components.Users.SortPicker.sortby.username_asc\": \"Nombre de usuario (de la A a la Z)\",\n    \"app.components.Users.SortPicker.sortby.username_desc\": \"Nombre de usuario (de la Z a la A)\",\n    \"app.components.listPlugins.button\": \"Añadir nuevo plugin\",\n    \"app.components.listPlugins.title.none\": \"No hay plugins instalados\",\n    \"app.components.listPluginsPage.deletePlugin.error\": \"Se ha producido un error al desinstalar el plugin\",\n    \"app.containers.App.notification.error.init\": \"Se produjo un error al solicitar la API\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin\": \"Si no recibe este enlace, comuníquese con su administrador.\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.text.email\": \"Es posible que tarde unos minutos en recibir el enlace de recuperación de contraseña.\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.title\": \"Correo electrónico enviado\",\n    \"app.containers.Users.EditPage.form.active.label\": \"Activo\",\n    \"app.containers.Users.EditPage.header.label\": \"Editar {name}\",\n    \"app.containers.Users.EditPage.header.label-loading\": \"Editar usuario\",\n    \"app.containers.Users.EditPage.roles-bloc-title\": \"Roles atribuidos\",\n    \"app.containers.Users.ModalForm.footer.button-success\": \"Crear usuario\",\n    \"app.links.configure-view\": \"Configurar la vista\",\n    \"app.static.links.cheatsheet\": \"CheatSheet\",\n    \"app.utils.SelectOption.defaultMessage\": \" \",\n    \"app.utils.add-filter\": \"Añadir filtro\",\n    \"app.utils.close-label\": \"Cerrar\",\n    \"app.utils.defaultMessage\": \" \",\n    \"app.utils.duplicate\": \"Duplicar\",\n    \"app.utils.edit\": \"Editar\",\n    \"app.utils.errors.file-too-big.message\": \"El archivo es demasiado grande\",\n    \"app.utils.filter-value\": \"Filtro\",\n    \"app.utils.filters\": \"Filtros\",\n    \"app.utils.notify.data-loaded\": \"{target} se ha cargado\",\n    \"app.utils.placeholder.defaultMessage\": \" \",\n    \"app.utils.publish\": \"Publicar\",\n    \"app.utils.select-all\": \"Seleccionar todo\",\n    \"app.utils.select-field\": \"Seleccionar campo\",\n    \"app.utils.select-filter\": \"Seleccionar filtro\",\n    \"app.utils.unpublish\": \"Anular publicación\",\n    clearLabel: clearLabel,\n    \"coming.soon\": \"¡Este contenido está actualmente en construcción y estará de regreso en unas semanas!\",\n    \"component.Input.error.validation.integer\": \"El valor debe ser un número entero\",\n    \"components.AutoReloadBlocker.description\": \"Inicia Strapi con uno de los siguientes comandos:\",\n    \"components.AutoReloadBlocker.header\": \"Es necesario recargar para este plugin.\",\n    \"components.ErrorBoundary.title\": \"Algo salió mal...\",\n    \"components.FilterOptions.FILTER_TYPES.$contains\": \"contiene\",\n    \"components.FilterOptions.FILTER_TYPES.$containsi\": \"contiene (insensible a mayúsculas y minúsculas)\",\n    \"components.FilterOptions.FILTER_TYPES.$endsWith\": \"termina con\",\n    \"components.FilterOptions.FILTER_TYPES.$endsWithi\": \"termina con (insensible a mayúsculas y minúsculas)\",\n    \"components.FilterOptions.FILTER_TYPES.$eq\": \"es\",\n    \"components.FilterOptions.FILTER_TYPES.$eqi\": \"es (insensible a mayúsculas y minúsculas)\",\n    \"components.FilterOptions.FILTER_TYPES.$gt\": \"es mayor que\",\n    \"components.FilterOptions.FILTER_TYPES.$gte\": \"es mayor o igual a\",\n    \"components.FilterOptions.FILTER_TYPES.$lt\": \"es menor que\",\n    \"components.FilterOptions.FILTER_TYPES.$lte\": \"es menor o igual a\",\n    \"components.FilterOptions.FILTER_TYPES.$ne\": \"no es\",\n    \"components.FilterOptions.FILTER_TYPES.$nei\": \"no es (insensible a mayúsculas y minúsculas)\",\n    \"components.FilterOptions.FILTER_TYPES.$notContains\": \"no contiene\",\n    \"components.FilterOptions.FILTER_TYPES.$notContainsi\": \"no contiene (insensible a mayúsculas y minúsculas)\",\n    \"components.FilterOptions.FILTER_TYPES.$notNull\": \"is not null\",\n    \"components.FilterOptions.FILTER_TYPES.$null\": \"is null\",\n    \"components.FilterOptions.FILTER_TYPES.$startsWith\": \"comienza con\",\n    \"components.FilterOptions.FILTER_TYPES.$startsWithi\": \"comienza con (insensible a mayúsculas y minúsculas)\",\n    \"components.Input.error.attribute.key.taken\": \"Este valor ya existe\",\n    \"components.Input.error.attribute.sameKeyAndName\": \"No puede ser igual\",\n    \"components.Input.error.attribute.taken\": \"Este nombre de campo ya existe\",\n    \"components.Input.error.contain.lowercase\": \"La contraseña debe contener al menos un carácter en minúscula\",\n    \"components.Input.error.contain.number\": \"La contraseña debe contener al menos un número\",\n    \"components.Input.error.contain.uppercase\": \"La contraseña debe contener al menos un carácter en mayúscula\",\n    \"components.Input.error.contentTypeName.taken\": \"Este nombre ya existe\",\n    \"components.Input.error.custom-error\": \"{errorMessage} \",\n    \"components.Input.error.password.noMatch\": \"Las contraseñas no coinciden\",\n    \"components.Input.error.validation.email\": \"Esto no es un email\",\n    \"components.Input.error.validation.json\": \"Esto no coincide con el formato JSON\",\n    \"components.Input.error.validation.max\": \"El valor es demasiado alto {max}.\",\n    \"components.Input.error.validation.maxLength\": \"El valor es demasiado largo {max}.\",\n    \"components.Input.error.validation.min\": \"El valor es demasiado bajo {min}.\",\n    \"components.Input.error.validation.minLength\": \"El valor es demasiado corto {min}.\",\n    \"components.Input.error.validation.minSupMax\": \"No puede ser superior\",\n    \"components.Input.error.validation.regex\": \"El valor no coincide con el de regex.\",\n    \"components.Input.error.validation.required\": \"Este valor es obligatorio.\",\n    \"components.Input.error.validation.unique\": \"Este valor ya se utiliza.\",\n    \"components.InputSelect.option.placeholder\": \"Elige aquí\",\n    \"components.ListRow.empty\": \"No hay datos que mostrar.\",\n    \"components.NotAllowedInput.text\": \"Sin permisos para ver este campo.\",\n    \"components.OverlayBlocker.description\": \"Está utilizando una función que necesita que el servidor se reinicie. Por favor, espere hasta que el servidor esté listo..\",\n    \"components.OverlayBlocker.description.serverError\": \"El servidor debería haberse reiniciado, compruebe sus logs en el terminal.\",\n    \"components.OverlayBlocker.title\": \"Esperando el reinicio...\",\n    \"components.OverlayBlocker.title.serverError\": \"El reinicio está llevando más tiempo de lo esperado\",\n    \"components.PageFooter.select\": \"entradas por página\",\n    \"components.ProductionBlocker.description\": \"Por razones de seguridad tenemos que desactivar este plugin en otros entornos.\",\n    \"components.ProductionBlocker.header\": \"Este plugin sólo está disponible en entornos de desarrollo.\",\n    \"components.Search.placeholder\": \"Buscar...\",\n    \"components.TableHeader.sort\": \"Ordenar por {label}\",\n    \"components.Wysiwyg.ToggleMode.markdown-mode\": \"Modo de Markdown\",\n    \"components.Wysiwyg.ToggleMode.preview-mode\": \"Modo de vista previa\",\n    \"components.Wysiwyg.collapse\": \"Contraer menú\",\n    \"components.Wysiwyg.selectOptions.H1\": \"Título H1\",\n    \"components.Wysiwyg.selectOptions.H2\": \"Título H2\",\n    \"components.Wysiwyg.selectOptions.H3\": \"Título H3\",\n    \"components.Wysiwyg.selectOptions.H4\": \"Título H4\",\n    \"components.Wysiwyg.selectOptions.H5\": \"Título H5\",\n    \"components.Wysiwyg.selectOptions.H6\": \"Título H6\",\n    \"components.Wysiwyg.selectOptions.title\": \"Añadir un título\",\n    \"components.WysiwygBottomControls.charactersIndicators\": \"caracteres\",\n    \"components.WysiwygBottomControls.fullscreen\": \"Expandir\",\n    \"components.WysiwygBottomControls.uploadFiles\": \"Arrastrar y soltar archivos, pegar desde el portapapeles o {browse}.\",\n    \"components.WysiwygBottomControls.uploadFiles.browse\": \"seleccionarlos\",\n    \"components.pagination.go-to\": \"Ir a la página {page}\",\n    \"components.pagination.go-to-next\": \"Ir a la página siguiente\",\n    \"components.pagination.go-to-previous\": \"Regresar a la página anterior\",\n    \"components.pagination.remaining-links\": \"Y {number} enlaces más\",\n    \"components.popUpWarning.button.cancel\": \"No, cancelar\",\n    \"components.popUpWarning.button.confirm\": \"Sí, confirmar\",\n    \"components.popUpWarning.message\": \"¿Estás seguro de que quieres borrar esto?\",\n    \"components.popUpWarning.title\": \"Por favor, confirme\",\n    \"form.button.done\": \"Hecho\",\n    \"global.prompt.unsaved\": \"¿Está seguro de que quiere salir de esta página? Todas sus modificaciones se perderán\",\n    \"notification.contentType.relations.conflict\": \"El Tipo de Contenido tiene relaciones conflictivas\",\n    \"notification.default.title\": \"Información:\",\n    \"notification.error\": \"Se ha producido un error\",\n    \"notification.error.layout\": \"No se pudo recuperar el esquema\",\n    \"notification.form.error.fields\": \"El formulario contiene algunos errores\",\n    \"notification.form.success.fields\": \"Cambios guardados\",\n    \"notification.link-copied\": \"Enlace copiado en el portapapeles\",\n    \"notification.permission.not-allowed-read\": \"No tienes permiso para ver este documento\",\n    \"notification.success.delete\": \"El elemento ha sido eliminado\",\n    \"notification.success.saved\": \"Guardado\",\n    \"notification.success.title\": \"Éxito:\",\n    \"notification.version.update.message\": \"¡Hay una nueva versión de Strapi disponible!\",\n    \"notification.warning.title\": \"Advertencia:\",\n    or: or,\n    \"request.error.model.unknown\": \"Este modelo no existe\",\n    skipToContent: skipToContent,\n    submit: submit,\n    \"Auth.form.active.label\": \"Activo\",\n    \"Auth.reset-password.title\": \"Resetear contraseña\",\n    \"Roles.RoleRow.user-count\": \"{number, plural, =0 {#  usuario} one {#  usuario} other {# usuarios}}\",\n    \"Settings.application.customization\": \"Personalización\",\n    \"Settings.application.customization.carousel.title\": \"Logo\",\n    \"Settings.application.customization.carousel.change-action\": \"Cambiarlogo\",\n    \"Settings.application.customization.carousel.reset-action\": \"Resetear logo\",\n    \"Settings.application.customization.carousel-slide.label\": \"Logo slider\",\n    \"Settings.application.customization.carousel-hint\": \"Cambiar el logo del panel de administración (Dimensión máxima: {dimension}x{dimension}, Tamaño máximo del archivo: {size}KB)\",\n    \"Settings.application.customization.modal.cancel\": \"Cancelar\",\n    \"Settings.application.customization.modal.upload\": \"Subir logo\",\n    \"Settings.application.customization.modal.tab.label\": \"¿Cómo te gustaría subir tus archivos?\",\n    \"Settings.application.customization.modal.upload.from-computer\": \"Desde el ordenador\",\n    \"Settings.application.customization.modal.upload.file-validation\": \"Dimensión máxima: {dimension}x{dimension}, Tamaño máximo: {size}KB\",\n    \"Settings.application.customization.modal.upload.error-format\": \"Se cargó un formato incorrecto (formatos aceptados: jpeg, jpg, png, svg).\",\n    \"Settings.application.customization.modal.upload.error-size\": \"El archivo es muy grande (Dimensión máxima: {dimension}x{dimension},Tamaño máximo del archivo: {size}KB)\",\n    \"Settings.application.customization.modal.upload.error-network\": \"Error de red\",\n    \"Settings.application.customization.modal.upload.cta.browse\": \"Buscar archivos\",\n    \"Settings.application.customization.modal.upload.drag-drop\": \"Arrastrar aquí o\",\n    \"Settings.application.customization.modal.upload.from-url\": \"Desde una url\",\n    \"Settings.application.customization.modal.upload.from-url.input-label\": \"URL\",\n    \"Settings.application.customization.modal.upload.next\": \"Siguiente\",\n    \"Settings.application.customization.modal.pending\": \"Logo pendiente\",\n    \"Settings.application.customization.modal.pending.choose-another\": \"Elegir otro logo\",\n    \"Settings.application.customization.modal.pending.title\": \"Logo ya subido\",\n    \"Settings.application.customization.modal.pending.subtitle\": \"Gestiona el logo elegido antes de subirlo\",\n    \"Settings.application.customization.modal.pending.upload\": \"Subir logo\",\n    \"Settings.application.customization.modal.pending.card-badge\": \"imagen\",\n    \"Settings.profile.form.section.experience.mode.label\": \"Modo de la interfaz\",\n    \"Settings.profile.form.section.experience.mode.hint\": \"Mostrar la interfaz en el modo seleccionado.\",\n    \"Settings.profile.form.section.experience.mode.option-label\": \"Mode {name} \",\n    light: light,\n    dark: dark,\n    \"Usecase.back-end\": \"Desarrollador Back-end\",\n    \"Usecase.button.skip\": \"Saltar esta pregunta\",\n    \"Usecase.content-creator\": \"Creador de Contenido\",\n    \"Usecase.front-end\": \"Desarrollador Front-end\",\n    \"Usecase.full-stack\": \"Desarrollador Full-stack\",\n    \"Usecase.input.work-type\": \"¿Qué tipo de trabajo realizas?\",\n    \"Usecase.notification.success.project-created\": \"El proyecto ha sido creado con éxito\",\n    \"Usecase.other\": \"Otro\",\n    \"Usecase.title\": \"Dinos algo de ti\",\n    \"admin.pages.MarketPlacePage.offline.title\": \"Estás offline\",\n    \"admin.pages.MarketPlacePage.offline.subtitle\": \"Necesita estar conectado a Internet para acceder a Strapi Market.\",\n    \"admin.pages.MarketPlacePage.plugins\": \"Plugins\",\n    \"admin.pages.MarketPlacePage.plugin.copy\": \"Copiar comando de instalación\",\n    \"admin.pages.MarketPlacePage.plugin.copy.success\": \"Comando de instalación listo para ser pegado en su terminal\",\n    \"admin.pages.MarketPlacePage.plugin.info\": \"Aprender más\",\n    \"admin.pages.MarketPlacePage.plugin.info.label\": \"Aprender más about {pluginName}\",\n    \"admin.pages.MarketPlacePage.plugin.info.text\": \"Aprender más\",\n    \"admin.pages.MarketPlacePage.plugin.installed\": \"Instalado\",\n    \"admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi\": \"Hecho por Strapi\",\n    \"admin.pages.MarketPlacePage.plugin.tooltip.verified\": \"Plugin verificado por Strapi\",\n    \"admin.pages.MarketPlacePage.providers\": \"Proovedores\",\n    \"admin.pages.MarketPlacePage.search.clear\": \"Limpiar la búsqueda\",\n    \"admin.pages.MarketPlacePage.search.empty\": \"No hay resultados para \\\"{target}\\\"\",\n    \"admin.pages.MarketPlacePage.search.placeholder\": \"Búscar\",\n    \"admin.pages.MarketPlacePage.submit.provider.link\": \"Enviar proveedor\",\n    \"admin.pages.MarketPlacePage.tab-group.label\": \"Plugins y proveedores para Strapi\",\n    \"admin.pages.MarketPlacePage.missingPlugin.title\": \"¿Necesitas un plugin?\",\n    \"admin.pages.MarketPlacePage.missingPlugin.description\": \"¡Díganos qué plugin está buscando y le informaremos a los desarrolladores de plugin de nuestra comunidad en caso de que estén buscando inspiración!\",\n    \"app.components.GuidedTour.CM.create.content\": \"<p>Cree y administre todo el contenido aquí en el Administrador de contenido.</p><p>Ej: tomando el ejemplo del sitio web del blog más allá, uno puede escribir un artículo, guardarlo y publicarlo como desee.</p>< p>💡 Consejo rápido: no olvides presionar publicar en el contenido que crees.</p>\",\n    \"app.components.GuidedTour.CM.create.title\": \"⚡️ Crear contenido\",\n    \"app.components.GuidedTour.CM.success.content\": \"<p>¡Asombroso, un paso más!</p><b>🚀  Mirar el contenido en acción</b>\",\n    \"app.components.GuidedTour.CM.success.cta.title\": \"Testear el API\",\n    \"app.components.GuidedTour.CM.success.title\": \"Paso 2: Completado ✅\",\n    \"app.components.GuidedTour.CTB.create.content\": \"<p>Los tipos de colección lo ayudan a administrar varias entradas, los tipos únicos son adecuados para administrar solo una entrada.</p> <p>Ej: para un sitio web de blog, Los artículos serían del tipo Colección, mientras que la Página de inicio sería del tipo Único.</p>\",\n    \"app.components.GuidedTour.CTB.create.cta.title\": \"Crear un tipo de colección\",\n    \"app.components.GuidedTour.CTB.create.title\": \"🧠 Crear un primer tipo de colección\",\n    \"app.components.GuidedTour.CTB.success.content\": \"<p>¡Bien hecho!</p><b>⚡️ ¿Qué te gustaría compartir con el mundo?</b>\",\n    \"app.components.GuidedTour.CTB.success.title\": \"Paso 1: Completado ✅\",\n    \"app.components.GuidedTour.apiTokens.create.content\": \"<p>Genera un token de autenticación aquí y recupera el contenido que acabas de crear.</p>\",\n    \"app.components.GuidedTour.apiTokens.create.cta.title\": \"Generar un token de API\",\n    \"app.components.GuidedTour.apiTokens.create.title\": \"🚀 Ver contenido en acción\",\n    \"app.components.GuidedTour.apiTokens.success.content\": \"<p>Vea el contenido en acción haciendo una solicitud HTTP:</p><ul><li><p>A esta URL: <light>https: //'<'TU_DOMINIO'>'/api/'<'TU_CT'>'</light></p></li><li><p>Con el encabezado: <light>Autorización: portador '<' TU_API_TOKEN'>'</light></p></li></ul><p>Para obtener más formas de interactuar con el contenido, consulte la <documentationLink>documentación</documentationLink>.</p>\",\n    \"app.components.GuidedTour.apiTokens.success.cta.title\": \"Volver a la página de inicio\",\n    \"app.components.GuidedTour.apiTokens.success.title\": \"Paso 3: Completado ✅\",\n    \"app.components.GuidedTour.create-content\": \"Crear contenido\",\n    \"app.components.GuidedTour.home.CM.title\": \"⚡️ ¿Qué te gustaría compartir con el mundo?\",\n    \"app.components.GuidedTour.home.CTB.cta.title\": \"Ir al Creador de tipo de contenido\",\n    \"app.components.GuidedTour.home.CTB.title\": \"🧠 Construye la estructura del contenido\",\n    \"app.components.GuidedTour.home.apiTokens.cta.title\": \"Probar la API\",\n    \"app.components.GuidedTour.skip\": \"Omitir el recorrido\",\n    \"app.components.GuidedTour.title\": \"3 pasos para comenzar\",\n    \"app.components.LeftMenu.logo.alt\": \"Logotipo de la aplicación\",\n    \"app.components.LeftMenu.plugins\": \"Complementos\",\n    \"app.components.LeftMenu.navbrand.title\": \"Panel de control de Strapi\",\n    \"app.components.LeftMenu.navbrand.workplace\": \"Lugar de trabajo\",\n    \"app.page.not.found\": \"¡Vaya! Parece que no podemos encontrar la página que estás buscando...\",\n    \"components.Input.error.validation.lowercase\": \"El valor debe ser una cadena en minúsculas\",\n    \"content-manager.ListViewTable.relation-loading\": \"Las relaciones se están cargando\",\n    \"content-manager.ListViewTable.relation-more\": \"Esta relación contiene más entidades de las que se muestran\",\n    \"content-manager.apiError.Este atributo debe ser único\": \"{field} debe ser único\",\n    \"form.button.continue\": \"Continue\",\n    \"global.search\": \"Buscar\",\n    \"global.actions\": \"Acciones\",\n    \"global.active\": \"Activo\",\n    \"global.inactive\": \"Inactivo\",\n    \"global.back\": \"Volver\",\n    \"global.cancel\": \"Cancelar\",\n    \"global.change-password\": \"Cambiar contraseña\",\n    \"global.content-manager\": \"Administrador de Contenido\",\n    \"global.continue\": \"Continuar\",\n    \"global.delete\": \"Borrar\",\n    \"global.delete-target\": \"Borrar {target}\",\n    \"global.description\": \"Descripción\",\n    \"global.details\": \"Detalles\",\n    \"global.disabled\": \"Desactivado\",\n    \"global.documentation\": \"Documentación\",\n    \"global.enabled\": \"Habilitado\",\n    \"global.finish\": \"Terminar\",\n    \"global.marketplace\": \"Marketplace\",\n    \"global.name\": \"Nombre\",\n    \"global.none\": \"Ninguno\",\n    \"global.password\": \"Contraseña\",\n    \"global.plugins\": \"Plugins\",\n    \"global.plugins.content-manager\": \"Administrador de Contenido\",\n    \"global.plugins.content-manager.description\": \"Forma rápida de ver, editar y eliminar los datos en tu base de datos.\",\n    \"global.plugins.content-type-builder\": \"Generador de tipo de contenido\",\n    \"global.plugins.content-type-builder.description\": \"Modeliza la estructura de datos de tu API. Crea nuevos campos y relaciones en solo un minuto. Los archivos se crean y actualizan automáticamente en tu proyecto.\",\n    \"global.plugins.email\": \"Correo electrónico\",\n    \"global.plugins.email.description\": \"Configura tu aplicación para enviar correos electrónicos.\",\n    \"global.plugins.upload\": \"Biblioteca Multimedia\",\n    \"global.plugins.upload.description\": \"Gestión de archivos multimedia.\",\n    \"global.plugins.graphql\": \"GraphQL\",\n    \"global.plugins.graphql.description\": \"Agrega un punto final de GraphQL con métodos API predeterminados.\",\n    \"global.plugins.documentation\": \"Documentación\",\n    \"global.plugins.documentation.description\": \"Cree un documento OpenAPI y visualice su API con SWAGGER UI.\",\n    \"global.plugins.i18n\": \"Internacionalización\",\n    \"global.plugins.i18n.description\": \"Este complemento permite crear, leer y actualizar contenido en diferentes idiomas, tanto desde el Panel de administración como desde la API.\",\n    \"global.plugins.sentry\": \"Sentry\",\n    \"global.plugins.sentry.description\": \"Enviar errores de Strapi a Sentry.\",\n    \"global.plugins.users-permissions\": \"Roles & Permisos\",\n    \"global.plugins.users-permissions.description\": \"Proteja su API con un proceso de autenticación completo basado en JWT. Este complemento también viene con una estrategia ACL que le permite administrar los permisos entre los grupos de usuarios.\",\n    \"global.profile\": \"Perfil\",\n    \"global.reset-password\": \"Resetear contraseña\",\n    \"global.roles\": \"Roles\",\n    \"global.save\": \"Guardar\",\n    \"global.see-more\": \"Ver más\",\n    \"global.select\": \"Seleccionar\",\n    \"global.select-all-entries\": \"Selecionar todas las entradas\",\n    \"global.settings\": \"Configuraciones\",\n    \"global.strapi-super-admin\": \"Super Admin\",\n    \"global.strapi-editor\": \"Editor\",\n    \"global.strapi-author\": \"Autor\",\n    \"global.table.header.email\": \"Email\",\n    \"global.table.header.firstname\": \"Nombre\",\n    \"global.table.header.isActive\": \"Estado del usuario\",\n    \"global.table.header.lastname\": \"Apellido\",\n    \"global.table.header.roles\": \"Roles\",\n    \"global.table.header.username\": \"Nombre de usuario\",\n    \"global.type\": \"Tipo\",\n    \"global.users\": \"Usuarios\",\n    \"notification.warning.404\": \"404 - No Encontrado\",\n    \"components.Blocks.blocks.heading1\": \"Título 1\",\n    \"components.Blocks.blocks.heading2\": \"Título 2\",\n    \"components.Blocks.blocks.heading3\": \"Título 3\",\n    \"components.Blocks.blocks.heading4\": \"Título 4\",\n    \"components.Blocks.blocks.heading5\": \"Título 5\",\n    \"components.Blocks.blocks.heading6\": \"Título 6\",\n    \"components.Blocks.blocks.image\": \"Imagen\"\n};\n\nexport { Analytics, Documentation, Email, Password, Provider, ResetPasswordToken, Role, Username, Users, anErrorOccurred, clearLabel, dark, es as default, light, or, skipToContent, submit };\n//# sourceMappingURL=es.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,YAAY;AAChB,IAAI,gBAAgB;AACpB,IAAI,QAAQ;AACZ,IAAI,WAAW;AACf,IAAI,WAAW;AACf,IAAI,qBAAqB;AACzB,IAAI,OAAO;AACX,IAAI,WAAW;AACf,IAAI,QAAQ;AACZ,IAAI,kBAAkB;AACtB,IAAI,aAAa;AACjB,IAAI,KAAK;AACT,IAAI,gBAAgB;AACpB,IAAI,SAAS;AACb,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,KAAK;AAAA,EACL;AAAA,EACA,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0CAA0C;AAAA,EAC1C,iCAAiC;AAAA,EACjC,sCAAsC;AAAA,EACtC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,iDAAiD;AAAA,EACjD,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,2BAA2B;AAAA,EAC3B,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA,EAChB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,6BAA6B;AAAA,EAC7B,qCAAqC;AAAA,EACrC,0CAA0C;AAAA,EAC1C,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,4BAA4B;AAAA,EAC5B,0CAA0C;AAAA,EAC1C,sBAAsB;AAAA,EACtB,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,8CAA8C;AAAA,EAC9C,gDAAgD;AAAA,EAChD,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,oCAAoC;AAAA,EACpC,sCAAsC;AAAA,EACtC,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,sCAAsC;AAAA,EACtC,8BAA8B;AAAA,EAC9B,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,yDAAyD;AAAA,EACzD,iDAAiD;AAAA,EACjD,8DAA8D;AAAA,EAC9D,mEAAmE;AAAA,EACnE,kEAAkE;AAAA,EAClE,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,oDAAoD;AAAA,EACpD,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,mCAAmC;AAAA,EACnC,iDAAiD;AAAA,EACjD,yDAAyD;AAAA,EACzD,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,wCAAwC;AAAA,EACxC,0CAA0C;AAAA,EAC1C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,6CAA6C;AAAA,EAC7C,yDAAyD;AAAA,EACzD,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,wCAAwC;AAAA,EACxC,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,2CAA2C;AAAA,EAC3C,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,4CAA4C;AAAA,EAC5C,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,2BAA2B;AAAA,EAC3B;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,+BAA+B;AAAA,EAC/B,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,kDAAkD;AAAA,EAClD,wCAAwC;AAAA,EACxC;AAAA,EACA,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,wCAAwC;AAAA,EACxC,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,4CAA4C;AAAA,EAC5C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,0CAA0C;AAAA,EAC1C,sCAAsC;AAAA,EACtC,2CAA2C;AAAA,EAC3C,wDAAwD;AAAA,EACxD,sDAAsD;AAAA,EACtD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,2DAA2D;AAAA,EAC3D,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,oCAAoC;AAAA,EACpC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,2CAA2C;AAAA,EAC3C,2BAA2B;AAAA,EAC3B,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,+EAA+E;AAAA,EAC/E,qEAAqE;AAAA,EACrE,yEAAyE;AAAA,EACzE,mEAAmE;AAAA,EACnE,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,4DAA4D;AAAA,EAC5D,0DAA0D;AAAA,EAC1D,sEAAsE;AAAA,EACtE,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,qDAAqD;AAAA,EACrD,wDAAwD;AAAA,EACxD,yDAAyD;AAAA,EACzD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,qCAAqC;AAAA,EACrC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,oEAAoE;AAAA,EACpE,4DAA4D;AAAA,EAC5D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,wDAAwD;AAAA,EACxD,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,yCAAyC;AAAA,EACzC,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,4BAA4B;AAAA,EAC5B,uBAAuB;AAAA,EACvB,kBAAkB;AAAA,EAClB,yCAAyC;AAAA,EACzC,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,gCAAgC;AAAA,EAChC,wCAAwC;AAAA,EACxC,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB;AAAA,EACA,eAAe;AAAA,EACf,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,+CAA+C;AAAA,EAC/C,qDAAqD;AAAA,EACrD,sDAAsD;AAAA,EACtD,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,0CAA0C;AAAA,EAC1C,4CAA4C;AAAA,EAC5C,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,gDAAgD;AAAA,EAChD,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,yDAAyD;AAAA,EACzD,+CAA+C;AAAA,EAC/C,gDAAgD;AAAA,EAChD,uDAAuD;AAAA,EACvD,+BAA+B;AAAA,EAC/B,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,yCAAyC;AAAA,EACzC,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,oBAAoB;AAAA,EACpB,yBAAyB;AAAA,EACzB,+CAA+C;AAAA,EAC/C,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,4CAA4C;AAAA,EAC5C,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B;AAAA,EACA,+BAA+B;AAAA,EAC/B;AAAA,EACA;AAAA,EACA,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,4BAA4B;AAAA,EAC5B,sCAAsC;AAAA,EACtC,qDAAqD;AAAA,EACrD,6DAA6D;AAAA,EAC7D,4DAA4D;AAAA,EAC5D,2DAA2D;AAAA,EAC3D,oDAAoD;AAAA,EACpD,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,sDAAsD;AAAA,EACtD,iEAAiE;AAAA,EACjE,mEAAmE;AAAA,EACnE,gEAAgE;AAAA,EAChE,8DAA8D;AAAA,EAC9D,iEAAiE;AAAA,EACjE,8DAA8D;AAAA,EAC9D,6DAA6D;AAAA,EAC7D,4DAA4D;AAAA,EAC5D,wEAAwE;AAAA,EACxE,wDAAwD;AAAA,EACxD,oDAAoD;AAAA,EACpD,mEAAmE;AAAA,EACnE,0DAA0D;AAAA,EAC1D,6DAA6D;AAAA,EAC7D,2DAA2D;AAAA,EAC3D,+DAA+D;AAAA,EAC/D,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,8DAA8D;AAAA,EAC9D;AAAA,EACA;AAAA,EACA,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,qBAAqB;AAAA,EACrB,sBAAsB;AAAA,EACtB,2BAA2B;AAAA,EAC3B,gDAAgD;AAAA,EAChD,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,mDAAmD;AAAA,EACnD,2CAA2C;AAAA,EAC3C,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,kDAAkD;AAAA,EAClD,oDAAoD;AAAA,EACpD,+CAA+C;AAAA,EAC/C,mDAAmD;AAAA,EACnD,yDAAyD;AAAA,EACzD,+CAA+C;AAAA,EAC/C,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,+CAA+C;AAAA,EAC/C,sDAAsD;AAAA,EACtD,wDAAwD;AAAA,EACxD,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,yDAAyD;AAAA,EACzD,qDAAqD;AAAA,EACrD,4CAA4C;AAAA,EAC5C,2CAA2C;AAAA,EAC3C,gDAAgD;AAAA,EAChD,4CAA4C;AAAA,EAC5C,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,oCAAoC;AAAA,EACpC,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,sBAAsB;AAAA,EACtB,+CAA+C;AAAA,EAC/C,kDAAkD;AAAA,EAClD,+CAA+C;AAAA,EAC/C,yDAAyD;AAAA,EACzD,wBAAwB;AAAA,EACxB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,wBAAwB;AAAA,EACxB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,sBAAsB;AAAA,EACtB,eAAe;AAAA,EACf,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,kCAAkC;AAAA,EAClC,8CAA8C;AAAA,EAC9C,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,wBAAwB;AAAA,EACxB,oCAAoC;AAAA,EACpC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,uBAAuB;AAAA,EACvB,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,gDAAgD;AAAA,EAChD,kBAAkB;AAAA,EAClB,yBAAyB;AAAA,EACzB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,6BAA6B;AAAA,EAC7B,wBAAwB;AAAA,EACxB,wBAAwB;AAAA,EACxB,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,gCAAgC;AAAA,EAChC,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,4BAA4B;AAAA,EAC5B,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,kCAAkC;AACtC;", "names": []}