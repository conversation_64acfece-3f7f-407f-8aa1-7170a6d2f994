{"version": 3, "sources": ["../../../@strapi/content-type-builder/dist/admin/translations/es.json.mjs"], "sourcesContent": ["var configurations = \"configuraciones\";\nvar from = \"de\";\nvar es = {\n    \"attribute.boolean\": \"Booleano\",\n    \"attribute.boolean.description\": \"Si o no, 1 o 0, verdadero o falso\",\n    \"attribute.component\": \"Componente\",\n    \"attribute.component.description\": \"Grupo de campos que puedes repetir o reutilizar\",\n    \"attribute.date\": \"Fecha\",\n    \"attribute.date.description\": \"Un selector de fechas con horas, minutos y segundos\",\n    \"attribute.datetime\": \"Fecha y hora\",\n    \"attribute.dynamiczone\": \"Zona dinámica\",\n    \"attribute.dynamiczone.description\": \"Elija componentes dinámicamente al editar contenido\",\n    \"attribute.email\": \"Correo electrónico\",\n    \"attribute.email.description\": \"Campo de correo electrónico con formato de validaciones\",\n    \"attribute.enumeration\": \"Enumeración\",\n    \"attribute.enumeration.description\": \"Lista de valores, luego elija uno\",\n    \"attribute.json\": \"JSON\",\n    \"attribute.json.description\": \"Datos en formato JSON\",\n    \"attribute.media\": \"Media\",\n    \"attribute.media.description\": \"Archivos como imágenes, videos, etc.\",\n    \"attribute.null\": \" \",\n    \"attribute.number\": \"Número\",\n    \"attribute.number.description\": \"Números (entero, flotante, decimal)\",\n    \"attribute.password\": \"Contraseña\",\n    \"attribute.password.description\": \"Campo de contraseña con cifrado\",\n    \"attribute.relation\": \"Relación\",\n    \"attribute.relation.description\": \"Se refiere a un Tipo de Colección\",\n    \"attribute.richtext\": \"Texto enriquecido\",\n    \"attribute.richtext.description\": \"Un editor de texto enriquecido con opciones de formato.\",\n    \"attribute.text\": \"Texto\",\n    \"attribute.text.description\": \"Texto corto o largo como título o descripción\",\n    \"attribute.time\": \"Hora\",\n    \"attribute.timestamp\": \"Marca de tiempo\",\n    \"attribute.uid\": \"UID\",\n    \"attribute.uid.description\": \"Identificador único\",\n    \"button.attributes.add.another\": \"Agregar otro campo\",\n    \"button.component.add\": \"Agregar un componente\",\n    \"button.component.create\": \"Crear nuevo componente\",\n    \"button.model.create\": \"Crear nuevo Tipo de Colección\",\n    \"button.single-types.create\": \"Crear nuevo tipo único\",\n    \"component.repeatable\": \"(repetible)\",\n    \"components.SelectComponents.displayed-value\": \"{number, plural, =0 {ningún componente seleccionado} one {1 componente seleccionado} other {# componentes seleccionados}}\",\n    \"components.componentSelect.no-component-available\": \"Ya ha agregado todos sus componentes\",\n    \"components.componentSelect.no-component-available.with-search\": \"No hay ningún componente que coincida con su búsqueda\",\n    \"components.componentSelect.value-component\": \"{number} componente seleccionado (escriba para buscar un componente)\",\n    \"components.componentSelect.value-components\": \"{number} componentes seleccionados\",\n    configurations: configurations,\n    \"contentType.apiId-plural.description\": \"ID de API pluralizado\",\n    \"contentType.apiId-plural.label\": \"ID de API (Plural)\",\n    \"contentType.apiId-singular.description\": \"El UID se utiliza para generar las rutas de la API y las tablas/colecciones de la base de datos\",\n    \"contentType.apiId-singular.label\": \"ID de API (Singular)\",\n    \"contentType.collectionName.description\": \"Útil cuando el nombre de su Tipo de Contenido y el nombre de su tabla difieren\",\n    \"contentType.collectionName.label\": \"Nombre de la colección\",\n    \"contentType.displayName.label\": \"Nombre para mostrar\",\n    \"contentType.kind.change.warning\": \"Acaba de cambiar el Tipo de Contenido: la API se restablecerá (las rutas, los controladores y los servicios se sobrescribirán).\",\n    \"error.attributeName.reserved-name\": \"Este nombre no se puede utilizar en su Tipo de Contenido, ya que podría romper otras funcionalidades.\",\n    \"error.contentType.pluralName-used\": \"Este valor no puede ser igual al valor singular\",\n    \"error.contentType.singularName-used\": \"Este valor no puede ser igual al valor plural\",\n    \"error.contentTypeName.reserved-name\": \"Este nombre no se puede utilizar en su proyecto, ya que podría romper otras funcionalidades.\",\n    \"error.validation.enum-duplicate\": \"No se permiten valores duplicados\",\n    \"error.validation.enum-empty-string\": \"No se permiten cadenas de caracteres vacías\",\n    \"error.validation.minSupMax\": \"No puede ser superior\",\n    \"error.validation.positive\": \"Debe ser un número positivo\",\n    \"error.validation.regex\": \"El patrón de expresión regular no es válido\",\n    \"error.validation.relation.targetAttribute-taken\": \"Este nombre existe en el destino\",\n    \"form.attribute.component.option.add\": \"Agregar un componente\",\n    \"form.attribute.component.option.create\": \"Crea un nuevo componente\",\n    \"form.attribute.component.option.create.description\": \"Un componente se comparte entre tipos y componentes, estará disponible y accesible en todas partes.\",\n    \"form.attribute.component.option.repeatable\": \"Componente repetible\",\n    \"form.attribute.component.option.repeatable.description\": \"Lo mejor para múltiples instancias (matriz) de ingredientes, meta etiquetas, etc.\",\n    \"form.attribute.component.option.reuse-existing\": \"Utilice un componente existente\",\n    \"form.attribute.component.option.reuse-existing.description\": \"Reutilice un componente ya creado para mantener la coherencia de sus datos en todos los tipos de contenido.\",\n    \"form.attribute.component.option.single\": \"Componente único\",\n    \"form.attribute.component.option.single.description\": \"Lo mejor para agrupar campos como dirección completa, información principal...\",\n    \"form.attribute.item.customColumnName\": \"Nombres de columna personalizados\",\n    \"form.attribute.item.customColumnName.description\": \"Esto es útil para renombrar los nombres de las columnas de la base de datos en un formato más completo para las respuestas de la API.\",\n    \"form.attribute.item.defineRelation.fieldName\": \"Nombre del campo\",\n    \"form.attribute.item.enumeration.graphql\": \"Sobreescritura de nombre para GraphQL\",\n    \"form.attribute.item.enumeration.graphql.description\": \"Le permite redefinir el nombre generado por defecto para GraphQL\",\n    \"form.attribute.item.enumeration.placeholder\": \"Ej:\\nmañana\\nmediodía\\nnoche\",\n    \"form.attribute.item.enumeration.rules\": \"Valores (una línea por valor)\",\n    \"form.attribute.item.maximum\": \"Valor máximo\",\n    \"form.attribute.item.maximumLength\": \"Longitud máxima\",\n    \"form.attribute.item.minimum\": \"Valor mínimo\",\n    \"form.attribute.item.minimumLength\": \"Longitud mínima\",\n    \"form.attribute.item.number.type\": \"Tipo de número\",\n    \"form.attribute.item.number.type.biginteger\": \"entero grande (ej: 123456789)\",\n    \"form.attribute.item.number.type.decimal\": \"decimal (ej: 2.22)\",\n    \"form.attribute.item.number.type.float\": \"flotante (ej: 3.3333333333)\",\n    \"form.attribute.item.number.type.integer\": \"entero (ej: 10)\",\n    \"form.attribute.item.privateField\": \"Campo privado\",\n    \"form.attribute.item.privateField.description\": \"Este campo no aparecerá en la respuesta de la API\",\n    \"form.attribute.item.requiredField\": \"Campo obligatorio\",\n    \"form.attribute.item.requiredField.description\": \"No podrá crear un registro si este campo está vacío\",\n    \"form.attribute.item.text.regex\": \"Patrón de expresión regular\",\n    \"form.attribute.item.text.regex.description\": \"El texto de la expresión regular\",\n    \"form.attribute.item.uniqueField\": \"Campo único\",\n    \"form.attribute.item.uniqueField.description\": \"No podrá crear un registro si ya existe otro registro con el mismo contenido\",\n    \"form.attribute.media.allowed-types\": \"Seleccionar tipos de multimedia permitidos\",\n    \"form.attribute.media.allowed-types.option-files\": \"Archivos\",\n    \"form.attribute.media.allowed-types.option-images\": \"Imágenes\",\n    \"form.attribute.media.allowed-types.option-videos\": \"Videos\",\n    \"form.attribute.media.option.multiple\": \"Múltiples multimedia\",\n    \"form.attribute.media.option.multiple.description\": \"Ideal para controles deslizantes, carruseles o descarga de varios archivos\",\n    \"form.attribute.media.option.single\": \"Multimedia único\",\n    \"form.attribute.media.option.single.description\": \"Lo mejor para avatar, foto de perfil o portada\",\n    \"form.attribute.settings.default\": \"Valor por defecto\",\n    \"form.attribute.text.option.long-text\": \"Texto largo\",\n    \"form.attribute.text.option.long-text.description\": \"Mejor para descripciones o biografía. La búsqueda exacta está inhabilitada.\",\n    \"form.attribute.text.option.short-text\": \"Texto corto\",\n    \"form.attribute.text.option.short-text.description\": \"Mejor para títulos, nombres, enlaces (URL). También permite una búsqueda exacta en el campo.\",\n    \"form.button.add-components-to-dynamiczone\": \"Agregar componentes a la zona\",\n    \"form.button.add-field\": \"Agregar otro campo\",\n    \"form.button.add-first-field-to-created-component\": \"Agregue el primer campo al componente\",\n    \"form.button.add.field.to.collectionType\": \"Agrega otro campo a este tipo de colección\",\n    \"form.button.add.field.to.component\": \"Agregar otro campo a este componente\",\n    \"form.button.add.field.to.contentType\": \"Agregar campo al Tipo de Contenido\",\n    \"form.button.add.field.to.singleType\": \"Agregue otro campo a este tipo único\",\n    \"form.button.cancel\": \"Cancelar\",\n    \"form.button.collection-type.description\": \"Lo mejor para múltiples instancias como artículos, productos, comentarios, etc.\",\n    \"form.button.configure-component\": \"Configurar el componente\",\n    \"form.button.configure-view\": \"Configurar la vista\",\n    \"form.button.select-component\": \"Seleccione un componente\",\n    \"form.button.single-type.description\": \"Lo mejor para una sola instancia como acerca de nosotros, página de inicio, etc.\",\n    from: from,\n    \"modalForm.attribute.form.base.name.description\": \"No se permiten espacios para el nombre del atributo\",\n    \"modalForm.attribute.form.base.name.placeholder\": \"p. ej. slug, urlSeo, urlCanónica\",\n    \"modalForm.attribute.target-field\": \"Campo adjunto\",\n    \"modalForm.attributes.select-component\": \"Seleccione un componente\",\n    \"modalForm.attributes.select-components\": \"Seleccionar los componentes\",\n    \"modalForm.collectionType.header-create\": \"Crea un tipo de colección\",\n    \"modalForm.component.header-create\": \"Crea un componente\",\n    \"modalForm.components.create-component.category.label\": \"Seleccione una categoría o ingrese un nombre para crear una nueva\",\n    \"modalForm.components.icon.label\": \"Icono\",\n    \"modalForm.editCategory.base.name.description\": \"No se permiten espacios para el nombre de la categoría.\",\n    \"modalForm.header-edit\": \"Editar {name}\",\n    \"modalForm.header.categories\": \"Categorías\",\n    \"modalForm.header.back\": \"Atrás\",\n    \"modalForm.singleType.header-create\": \"Crea un tipo único\",\n    \"modalForm.sub-header.addComponentToDynamicZone\": \"Agregar nuevo componente a la zona dinámica\",\n    \"modalForm.sub-header.attribute.create\": \"Agregar nuevo campo {type}\",\n    \"modalForm.sub-header.attribute.create.step\": \"Agregar nuevo componente ({step}/2)\",\n    \"modalForm.sub-header.attribute.edit\": \"Editar {name}\",\n    \"modalForm.sub-header.chooseAttribute.collectionType\": \"Seleccione un campo para su Tipo de Colección\",\n    \"modalForm.sub-header.chooseAttribute.component\": \"Seleccione un campo para su componente\",\n    \"modalForm.sub-header.chooseAttribute.singleType\": \"Seleccione un campo para su tipo único\",\n    \"modelPage.attribute.relation-polymorphic\": \"Relación (polimórfica)\",\n    \"modelPage.attribute.relationWith\": \"Vinculación con\",\n    \"notification.info.autoreaload-disable\": \"Se requiere la función autoReload para usar este plugin. Inicie su servidor con `strapi develop`\",\n    \"notification.info.creating.notSaved\": \"Guarde su trabajo antes de crear un nuevo tipo de colección o componente\",\n    \"plugin.description.long\": \"Modelice la estructura de datos de su API. Cree nuevos campos y relaciones en sólo un minuto. Los archivos se crean y actualizan automáticamente en el proyecto.\",\n    \"plugin.description.short\": \"Modelice la estructura de datos de su API.\",\n    \"plugin.name\": \"Generador de Tipo de Contenido\",\n    \"popUpForm.navContainer.advanced\": \"Configuración avanzada\",\n    \"popUpForm.navContainer.base\": \"Ajustes básicos\",\n    \"popUpWarning.bodyMessage.cancel-modifications\": \"¿Estás seguro de que deseas cancelar tus modificaciones?\",\n    \"popUpWarning.bodyMessage.cancel-modifications.with-components\": \"¿Está seguro de que desea cancelar sus modificaciones? Algunos componentes han sido creados o modificados...\",\n    \"popUpWarning.bodyMessage.category.delete\": \"¿Está seguro de que desea eliminar esta categoría? También se eliminarán todos los componentes.\",\n    \"popUpWarning.bodyMessage.component.delete\": \"¿Está seguro de que desea eliminar este componente?\",\n    \"popUpWarning.bodyMessage.contentType.delete\": \"¿Está seguro de que desea eliminar este Tipo de Contenido?\",\n    \"popUpWarning.draft-publish.button.confirm\": \"Sí, deshabilitar\",\n    \"popUpWarning.draft-publish.message\": \"Si desactiva el sistema Borrador/Publicación, se eliminarán sus borradores.\",\n    \"popUpWarning.draft-publish.second-message\": \"¿Estás seguro de que quieres desactivarlo?\",\n    \"prompt.unsaved\": \"¿Estás seguro que quieres irte? Todas sus modificaciones se perderán.\",\n    \"relation.attributeName.placeholder\": \"Ej: autor, categoría, etiqueta\",\n    \"relation.manyToMany\": \"tiene y pertenece a muchos\",\n    \"relation.manyToOne\": \"tiene muchos\",\n    \"relation.manyWay\": \"tiene muchas\",\n    \"relation.oneToMany\": \"pertenece a muchos\",\n    \"relation.oneToOne\": \"tiene y pertenece a una\",\n    \"relation.oneWay\": \"tiene uno\",\n    \"table.button.no-fields\": \"Agregar campo\",\n    \"table.content.create-first-content-type\": \"Crea tu primer Tipo de Colección\",\n    \"table.content.no-fields.collection-type\": \"Agrega tu primer campo a este Tipo de Colección\",\n    \"table.content.no-fields.component\": \"Agregar tu primer campo a este componente\"\n};\n\nexport { configurations, es as default, from };\n//# sourceMappingURL=es.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,iBAAiB;AACrB,IAAI,OAAO;AACX,IAAI,KAAK;AAAA,EACL,qBAAqB;AAAA,EACrB,iCAAiC;AAAA,EACjC,uBAAuB;AAAA,EACvB,mCAAmC;AAAA,EACnC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,mBAAmB;AAAA,EACnB,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,mBAAmB;AAAA,EACnB,+BAA+B;AAAA,EAC/B,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,gCAAgC;AAAA,EAChC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,wBAAwB;AAAA,EACxB,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,+CAA+C;AAAA,EAC/C,qDAAqD;AAAA,EACrD,iEAAiE;AAAA,EACjE,8CAA8C;AAAA,EAC9C,+CAA+C;AAAA,EAC/C;AAAA,EACA,wCAAwC;AAAA,EACxC,kCAAkC;AAAA,EAClC,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,8BAA8B;AAAA,EAC9B,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,mDAAmD;AAAA,EACnD,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,sDAAsD;AAAA,EACtD,8CAA8C;AAAA,EAC9C,0DAA0D;AAAA,EAC1D,kDAAkD;AAAA,EAClD,8DAA8D;AAAA,EAC9D,0CAA0C;AAAA,EAC1C,sDAAsD;AAAA,EACtD,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,gDAAgD;AAAA,EAChD,2CAA2C;AAAA,EAC3C,uDAAuD;AAAA,EACvD,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+BAA+B;AAAA,EAC/B,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,8CAA8C;AAAA,EAC9C,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,oCAAoC;AAAA,EACpC,gDAAgD;AAAA,EAChD,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,kCAAkC;AAAA,EAClC,8CAA8C;AAAA,EAC9C,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,sCAAsC;AAAA,EACtC,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,sCAAsC;AAAA,EACtC,kDAAkD;AAAA,EAClD,mCAAmC;AAAA,EACnC,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,6CAA6C;AAAA,EAC7C,yBAAyB;AAAA,EACzB,oDAAoD;AAAA,EACpD,2CAA2C;AAAA,EAC3C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,sBAAsB;AAAA,EACtB,2CAA2C;AAAA,EAC3C,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,gCAAgC;AAAA,EAChC,uCAAuC;AAAA,EACvC;AAAA,EACA,kDAAkD;AAAA,EAClD,kDAAkD;AAAA,EAClD,oCAAoC;AAAA,EACpC,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,qCAAqC;AAAA,EACrC,wDAAwD;AAAA,EACxD,mCAAmC;AAAA,EACnC,gDAAgD;AAAA,EAChD,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,sCAAsC;AAAA,EACtC,kDAAkD;AAAA,EAClD,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,mDAAmD;AAAA,EACnD,4CAA4C;AAAA,EAC5C,oCAAoC;AAAA,EACpC,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,eAAe;AAAA,EACf,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,iDAAiD;AAAA,EACjD,iEAAiE;AAAA,EACjE,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,+CAA+C;AAAA,EAC/C,6CAA6C;AAAA,EAC7C,sCAAsC;AAAA,EACtC,6CAA6C;AAAA,EAC7C,kBAAkB;AAAA,EAClB,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,0BAA0B;AAAA,EAC1B,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,qCAAqC;AACzC;", "names": []}