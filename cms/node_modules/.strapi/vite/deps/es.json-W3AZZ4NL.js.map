{"version": 3, "sources": ["../../../@strapi/upload/dist/admin/translations/es.json.mjs"], "sourcesContent": ["var es = {\n    \"bulk.select.label\": \"Seleccionar todos los recursos\",\n    \"button.next\": \"<PERSON><PERSON><PERSON><PERSON>\",\n    \"checkControl.crop-duplicate\": \"Duplicar y cortar el recurso\",\n    \"checkControl.crop-original\": \"Recortar el recurso original\",\n    \"content.isLoading\": \"Cargando la lista de recursos.\",\n    \"control-card.add\": \"Añadir\",\n    \"control-card.cancel\": \"Cancelar\",\n    \"control-card.copy-link\": \"Copiar link\",\n    \"control-card.crop\": \"Cortar\",\n    \"control-card.download\": \"Descargar\",\n    \"control-card.edit\": \"Editar\",\n    \"control-card.replace-media\": \"Reemplazar multimedia\",\n    \"control-card.save\": \"Guardar\",\n    \"control-card.stop-crop\": \"Deja de recortar\",\n    \"filter.add\": \"Añadir filtro\",\n    \"form.button.replace-media\": \"Reemplazar multimedia\",\n    \"form.input.description.file-alt\": \"Este texto se mostrará si el recurso no se puede mostrar.\",\n    \"form.input.label.file-alt\": \"Texto alternativo\",\n    \"form.input.label.file-caption\": \"Subtítulo\",\n    \"form.input.label.file-name\": \"Nombre del archivo\",\n    \"form.upload-url.error.url.invalid\": \"Una URL no es válida\",\n    \"form.upload-url.error.url.invalids\": \"{number} URL no son válidas\",\n    \"header.actions.upload-assets\": \"Subir recursos\",\n    \"header.actions.upload-new-asset\": \"Subir nuevo recurso\",\n    \"header.actions.add-assets\": \"Agregar Archivos\",\n    \"header.actions.add-folder\": \"Agregar nueva carpeta\",\n    \"header.actions.add-assets.folder\": \"carpeta\",\n    \"header.content.assets-empty\": \"No hay archivos\",\n    \"header.content.assets\": \"{numberFolders, plural, one {1 folder} other {# folders}} - {numberAssets, plural, one {1 recurso} other {# recursos}}\",\n    \"input.button.label\": \"Buscar archivos\",\n    \"input.label\": \"Arrastra y suelta aquí o\",\n    \"input.label-bold\": \"Arrastrar\",\n    \"input.label-normal\": \"para subir o\",\n    \"input.placeholder\": \"Haga clic para seleccionar un recurso o arrastre\",\n    \"input.placeholder.icon\": \"Suelta el recurso en esta zona\",\n    \"input.url.description\": \"Separe sus enlaces URL con un salto de línea.\",\n    \"input.url.label\": \"URL\",\n    \"list.asset.at.finished\": \"Los recursos han terminado de cargarse.\",\n    \"list.assets-empty.search\": \"No se han encontrado resultados\",\n    \"list.assets-empty.subtitle\": \"Agrega uno a la lista.\",\n    \"list.assets-empty.title\": \"Todavía no hay recursos\",\n    \"list.assets-empty.title-withSearch\": \"No hay recursos con los filtros aplicados\",\n    \"list.assets.empty\": \"Sube tus primeros recursos...\",\n    \"list.assets.empty-upload\": \"Subir Archivos\",\n    \"list.assets.empty.no-permissions\": \"La lista de recursos está vacía.\",\n    \"list.assets.loading-asset\": \"Cargando la vista previa de los recursos: {path}\",\n    \"list.assets.not-supported-content\": \"No hay vista previa disponible\",\n    \"list.assets.preview-asset\": \"Vista previa del video en la ruta {path}\",\n    \"list.assets.selected\": \"{number, plural, =0 {Ningún recurso seleccionado} one {1 recurso seleccionado} other {# recursos seleccionados}}\",\n    \"list.assets.type-not-allowed\": \"Este tipo de archivo no está permitido.\",\n    \"mediaLibraryInput.actions.nextSlide\": \"Siguiente\",\n    \"mediaLibraryInput.actions.previousSlide\": \"Anterior\",\n    \"mediaLibraryInput.placeholder\": \"Haga clic para seleccionar un activo o arrastre y suelte uno en esta área\",\n    \"mediaLibraryInput.slideCount\": \"{n} de {m}\",\n    \"modal.file-details.date\": \"Fecha\",\n    \"modal.file-details.dimensions\": \"Dimensiones\",\n    \"modal.file-details.extension\": \"Extensión\",\n    \"modal.file-details.size\": \"Tamaño\",\n    \"modal.header.browse\": \"Subir recursos\",\n    \"modal.header.file-detail\": \"Detalles\",\n    \"modal.header.pending-assets\": \"Recursos pendientes\",\n    \"modal.header.select-files\": \"Archivos seleccionados\",\n    \"modal.nav.browse\": \"ver\",\n    \"modal.nav.computer\": \"desde el ordenador\",\n    \"modal.nav.selected\": \"seleccionado\",\n    \"modal.nav.url\": \"desde url\",\n    \"modal.remove.success-label\": \"El recurso se ha eliminado correctamente.\",\n    \"modal.selected-list.sub-header-subtitle\": \"Arrastra y suelta para reordenar los recursos en el campo\",\n    \"modal.upload-list.sub-header-subtitle\": \"Administre los activos antes de agregarlos a la Biblioteca de Medios\",\n    \"modal.upload-list.sub-header.button\": \"Agregar más recursos\",\n    \"modal.upload.cancelled\": \"Subida anulada manualmente.\",\n    \"page.title\": \"Configuración: biblioteca multimedia\",\n    \"permissions.not-allowed.update\": \"No se le permite editar este archivo.\",\n    \"plugin.description.long\": \"Gestión de archivos multimedia.\",\n    \"plugin.description.short\": \"Gestión de archivos multimedia.\",\n    \"plugin.name\": \"Biblioteca de Multimedia\",\n    \"search.clear.label\": \"Borrar la busqueda\",\n    \"search.label\": \"Buscar un recurso\",\n    \"search.placeholder\": \"Buscar un recurso...\",\n    \"settings.blockTitle\": \"Gestión de recursos\",\n    \"settings.form.autoOrientation.description\": \"Gire la imagen automáticamente según la etiqueta de orientación EXIF\",\n    \"settings.form.autoOrientation.label\": \"Habilitar la orientación automática\",\n    \"settings.form.responsiveDimensions.description\": \"Genera automáticamente múltiples formatos (grande, mediano, pequeño) del recurso subido\",\n    \"settings.form.responsiveDimensions.label\": \"Habilita la subida amigable\",\n    \"settings.form.sizeOptimization.description\": \"Habilitar esta opción optimizará el tamaño del archivo sin comprometer la calidad.\",\n    \"settings.form.sizeOptimization.label\": \"Habilite la optimización de tamaño (sin pérdida de calidad)\",\n    \"settings.form.videoPreview.description\": \"Generará una vista previa de seis segundos del video (GIF)\",\n    \"settings.form.videoPreview.label\": \"Vista previa\",\n    \"settings.header.label\": \"Biblioteca de Multimedia - Configuración\",\n    \"settings.section.doc.label\": \"Documento\",\n    \"settings.section.image.label\": \"IMAGEN\",\n    \"settings.section.video.label\": \"VÍDEO\",\n    \"settings.sub-header.label\": \"Configure los ajustes para la biblioteca de multimedia\",\n    \"sort.created_at_asc\": \"Subidas más antiguas\",\n    \"sort.created_at_desc\": \"Subidas más recientes\",\n    \"sort.label\": \"Ordenar por\",\n    \"sort.name_asc\": \"Orden alfabético (de la A a la Z)\",\n    \"sort.name_desc\": \"Orden alfabético inverso (de la Z a la A)\",\n    \"sort.updated_at_asc\": \"Actualizaciones más antiguas\",\n    \"sort.updated_at_desc\": \"Actualizaciones mas recientes\",\n    \"tabs.title\": \"¿Cómo desea cargar sus recursos?\",\n    \"window.confirm.close-modal.file\": \"¿Estás seguro? Tus cambios se perderán.\",\n    \"window.confirm.close-modal.files\": \"¿Estás seguro? Tiene algunos archivos que aún no se han subido.\",\n    \"upload.generic-error\": \"Ha ocurrido un error al subir el archivo. Por favor, inténtelo de nuevo.\",\n    \"list.assets.title\": \"Archivos\",\n    \"list.assets.to-upload\": \"{number, plural, =0 {No asset} one {1 archivo} other {# archivos}} listo para subir\",\n    \"list.folder.edit\": \"Editar carpeta\",\n    \"list.folder.subtitle\": \"{folderCount, plural, =0 {# folder} one {# folder} other {# folders}}, {filesCount, plural, =0 {# asset} uno {# asset} otro {# assets}}\",\n    \"list.folders.title\": \"Carpetas\",\n    \"modal.folder.elements.count\": \"{folderCount} carpetas, {assetCount} archivos\",\n    \"modal.header.go-back\": \"Volver\",\n    \"modal.folder.move.title\": \"Mover elementos a\",\n    \"modal.move.success-label\": \"Los elementos han sido movidos con éxito\",\n    \"modal.upload-list.footer.button\": \"Subir {number, plural, one {# archivo} other {# archivo}} a la biblioteca\"\n};\n\nexport { es as default };\n//# sourceMappingURL=es.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,KAAK;AAAA,EACL,qBAAqB;AAAA,EACrB,eAAe;AAAA,EACf,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,qBAAqB;AAAA,EACrB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,qBAAqB;AAAA,EACrB,8BAA8B;AAAA,EAC9B,qBAAqB;AAAA,EACrB,0BAA0B;AAAA,EAC1B,cAAc;AAAA,EACd,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,gCAAgC;AAAA,EAChC,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,qBAAqB;AAAA,EACrB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,mBAAmB;AAAA,EACnB,0BAA0B;AAAA,EAC1B,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,sCAAsC;AAAA,EACtC,qBAAqB;AAAA,EACrB,4BAA4B;AAAA,EAC5B,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,qCAAqC;AAAA,EACrC,6BAA6B;AAAA,EAC7B,wBAAwB;AAAA,EACxB,gCAAgC;AAAA,EAChC,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,iCAAiC;AAAA,EACjC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,6BAA6B;AAAA,EAC7B,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,iBAAiB;AAAA,EACjB,8BAA8B;AAAA,EAC9B,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,0BAA0B;AAAA,EAC1B,cAAc;AAAA,EACd,kCAAkC;AAAA,EAClC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,eAAe;AAAA,EACf,sBAAsB;AAAA,EACtB,gBAAgB;AAAA,EAChB,sBAAsB;AAAA,EACtB,uBAAuB;AAAA,EACvB,6CAA6C;AAAA,EAC7C,uCAAuC;AAAA,EACvC,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,8CAA8C;AAAA,EAC9C,wCAAwC;AAAA,EACxC,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,yBAAyB;AAAA,EACzB,8BAA8B;AAAA,EAC9B,gCAAgC;AAAA,EAChC,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,cAAc;AAAA,EACd,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,cAAc;AAAA,EACd,mCAAmC;AAAA,EACnC,oCAAoC;AAAA,EACpC,wBAAwB;AAAA,EACxB,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,oBAAoB;AAAA,EACpB,wBAAwB;AAAA,EACxB,sBAAsB;AAAA,EACtB,+BAA+B;AAAA,EAC/B,wBAAwB;AAAA,EACxB,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,mCAAmC;AACvC;", "names": []}