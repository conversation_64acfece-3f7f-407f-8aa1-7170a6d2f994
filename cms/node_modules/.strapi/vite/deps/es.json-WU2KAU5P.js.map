{"version": 3, "sources": ["../../../@strapi/email/dist/admin/translations/es.json.mjs"], "sourcesContent": ["var es = {\n    \"Settings.email.plugin.button.test-email\": \"Enviar email de prueba\",\n    \"Settings.email.plugin.label.defaultFrom\": \"Email del remitente predeterminado\",\n    \"Settings.email.plugin.label.defaultReplyTo\": \"Email de respuesta predeterminado\",\n    \"Settings.email.plugin.label.provider\": \"Proveedor de email\",\n    \"Settings.email.plugin.label.testAddress\": \"Receptor de email\",\n    \"Settings.email.plugin.notification.config.error\": \"No se pudo recuperar la configuración del email\",\n    \"Settings.email.plugin.notification.data.loaded\": \"Se han cargado los datos de configuración de email\",\n    \"Settings.email.plugin.notification.test.error\": \"No se pudo enviar un correo de prueba a {to}\",\n    \"Settings.email.plugin.notification.test.success\": \"La prueba de correo electrónico se realizó correctamente, verifique el buzón de {to}\",\n    \"Settings.email.plugin.placeholder.defaultFrom\": \"ej: Strapi No-Reply <<EMAIL>>\",\n    \"Settings.email.plugin.placeholder.defaultReplyTo\": \"ej: Strapi <<EMAIL>>\",\n    \"Settings.email.plugin.placeholder.testAddress\": \"ej: <EMAIL>\",\n    \"Settings.email.plugin.subTitle\": \"Pruebe la configuración del complemento de email\",\n    \"Settings.email.plugin.text.configuration\": \"El complemento se configura a través del archivo {file}, consulte este {link} para ver la documentación.\",\n    \"Settings.email.plugin.title\": \"Configuración\",\n    \"Settings.email.plugin.title.config\": \"Configuración\",\n    \"Settings.email.plugin.title.test\": \"Prueba el envío de email\",\n    \"SettingsNav.link.settings\": \"Ajustes\",\n    \"SettingsNav.section-label\": \"Plugin de email\",\n    \"components.Input.error.validation.email\": \"Este es un correo electrónico inválido\"\n};\n\nexport { es as default };\n//# sourceMappingURL=es.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,KAAK;AAAA,EACL,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,wCAAwC;AAAA,EACxC,2CAA2C;AAAA,EAC3C,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,iDAAiD;AAAA,EACjD,mDAAmD;AAAA,EACnD,iDAAiD;AAAA,EACjD,oDAAoD;AAAA,EACpD,iDAAiD;AAAA,EACjD,kCAAkC;AAAA,EAClC,4CAA4C;AAAA,EAC5C,+BAA+B;AAAA,EAC/B,sCAAsC;AAAA,EACtC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,2CAA2C;AAC/C;", "names": []}