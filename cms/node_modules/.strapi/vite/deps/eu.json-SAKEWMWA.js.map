{"version": 3, "sources": ["../../../@strapi/admin/dist/admin/admin/src/translations/eu.json.mjs"], "sourcesContent": ["var Analytics = \"Analitika\";\nvar Documentation = \"Dokumentazioa\";\nvar Email = \"Posta elektronikoa\";\nvar Password = \"Pasahitza\";\nvar Provider = \"Hornitzailea\";\nvar ResetPasswordToken = \"Berrezarri pasahitz tokena\";\nvar Role = \"Rola\";\nvar light = \"Argia\";\nvar dark = \"Iluna\";\nvar Username = \"Erabiltzaile izena\";\nvar Users = \"Erabiltzaileak\";\nvar anErrorOccurred = \"Arraioa! Zerbait gaizki joan da. Saiatu berriro mesedez\";\nvar clearLabel = \"Garbitu\";\nvar or = \"EDO\";\nvar skipToContent = \"Saltatu edukira\";\nvar submit = \"Bidali\";\nvar eu = {\n    Analytics: Analytics,\n    \"Auth.components.Oops.text\": \"Zure kontua etenda dago\",\n    \"Auth.components.Oops.text.admin\": \"Akats bat bada, jar zaitez harremanetan zure administratzailearekin\",\n    \"Auth.components.Oops.title\": \"Oops...\",\n    \"Auth.form.active.label\": \"Aktibo\",\n    \"Auth.form.button.forgot-password\": \"Bidali posta elektronikoa\",\n    \"Auth.form.button.go-home\": \"ITZULI ETXERA\",\n    \"Auth.form.button.login\": \"Login\",\n    \"Auth.form.button.login.providers.error\": \"Ezin zaitugu konektatu aukeratutako hornitzailearen bidez\",\n    \"Auth.form.button.login.strapi\": \"Saioa hasi Strapi-n\",\n    \"Auth.form.button.password-recovery\": \"Pasahitza berreskuratzea\",\n    \"Auth.form.button.register\": \"Has gaitezen\",\n    \"Auth.form.confirmPassword.label\": \"Konfirmatzeko pasahitza\",\n    \"Auth.form.currentPassword.label\": \"Egungo pasahitza\",\n    \"Auth.form.email.label\": \"Posta elektronikoa\",\n    \"Auth.form.email.placeholder\": \"adibidez, <EMAIL>\",\n    \"Auth.form.error.blocked\": \"Administratzaileak zure kontua blokeatu du\",\n    \"Auth.form.error.code.provide\": \"Emandako kodea ez da zuzena\",\n    \"Auth.form.error.confirmed\": \"Zure kontuko posta elektronikoa ez dago baieztatuta\",\n    \"Auth.form.error.email.invalid\": \"Posta elektroniko honek ez du balio\",\n    \"Auth.form.error.email.provide\": \"Mesedez, eman zure erabiltzaile izena edo posta elektronikoa\",\n    \"Auth.form.error.email.taken\": \"Posta elektronikoa hartuta dago\",\n    \"Auth.form.error.invalid\": \"Identifikatzailea edo pasahitza ez da baliozkoa\",\n    \"Auth.form.error.params.provide\": \"Emandako parametroak ez dira zuzenak\",\n    \"Auth.form.error.password.format\": \"Zure pasahitzak ezin du hiru aldiz baino gehiagotan izan `$` ikurra\",\n    \"Auth.form.error.password.local\": \"Erabiltzaile honek ez du sekula tokiko pasahitzik ezarri, mesedez hasi saioa kontua sortzean erabilitako hornitzailearen bidez\",\n    \"Auth.form.error.password.matching\": \"Pasahitzak ez datoz bat\",\n    \"Auth.form.error.password.provide\": \"Mesedez, eman pasahitza\",\n    \"Auth.form.error.ratelimit\": \"Saio gehiegi, mesedez, saiatu berriro minutu batean\",\n    \"Auth.form.error.user.not-exist\": \"Email hau ez da existitzen\",\n    \"Auth.form.error.username.taken\": \"Erabiltzaile izena hartuta dago\",\n    \"Auth.form.firstname.label\": \"Izena\",\n    \"Auth.form.firstname.placeholder\": \"adibidez, Iratxe\",\n    \"Auth.form.forgot-password.email.label\": \"Sartu zure posta elektronikoa\",\n    \"Auth.form.forgot-password.email.label.success\": \"Posta elektronikora ondo bidalia\",\n    \"Auth.form.lastname.label\": \"Abizena\",\n    \"Auth.form.lastname.placeholder\": \"adibidez, Etxeberria\",\n    \"Auth.form.password.hide-password\": \"Ezkutatu pasahitza\",\n    \"Auth.form.password.hint\": \"Gutxienez 8 karaktere izan behar ditu, 1 maiuskula, 1 minuskula eta zenbaki 1\",\n    \"Auth.form.password.show-password\": \"Erakutsi pasahitza\",\n    \"Auth.form.register.news.label\": \"Eduki nazazu eguneratuta funtzionalitate berriei eta hurrengo hobekuntzei buruz (hori eginez onartzen dituzu {terms} eta {policy})\",\n    \"Auth.form.register.subtitle\": \"Kredentzialak Strapin autentifikatzeko baino ez dira erabiltzen. Gordetako datu guztiak zure datu-basean gordeko dira\",\n    \"Auth.form.rememberMe.label\": \"Gogora nazazu\",\n    \"Auth.form.username.label\": \"Erabiltzaile izena\",\n    \"Auth.form.username.placeholder\": \"adibidez, Iratxe_Etxeberria\",\n    \"Auth.form.welcome.subtitle\": \"Hasi saioa zure Strapi kontuan\",\n    \"Auth.form.welcome.title\": \"Ongi etorri Strapira!\",\n    \"Auth.link.forgot-password\": \"Pasahitza ahaztu zaizu?\",\n    \"Auth.link.ready\": \"Saioa hasteko prest?\",\n    \"Auth.link.signin\": \"Hasi saioa\",\n    \"Auth.link.signin.account\": \"Baduzu konturik?\",\n    \"Auth.login.sso.divider\": \"Edo hasi saioa honekin\",\n    \"Auth.login.sso.loading\": \"Hornitzaileak kargatzen...\",\n    \"Auth.login.sso.subtitle\": \"Hasi saioa zure kontuan SSO bidez\",\n    \"Auth.privacy-policy-agreement.policy\": \"pribatutasun politika\",\n    \"Auth.privacy-policy-agreement.terms\": \"terminoak\",\n    \"Auth.reset-password.title\": \"Berrezarri pasahitza\",\n    \"Content Manager\": \"Eduki-kudeatzailea\",\n    \"Content Type Builder\": \"Edukiontzi-moten eraikitzailea\",\n    Documentation: Documentation,\n    Email: Email,\n    \"Files Upload\": \"Fitxategiak Kargatu\",\n    \"HomePage.head.title\": \"Hasierako orria\",\n    \"HomePage.roadmap\": \"Ikusi gure bide orria\",\n    \"HomePage.welcome.congrats\": \"Zorionak!\",\n    \"HomePage.welcome.congrats.content\": \"Lehen administratzaile bezala hasi duzu saioa. Strapi-k eskaintzen dituen funtzio indartsuak ezagutzeko,\",\n    \"HomePage.welcome.congrats.content.bold\": \"zure lehen Bilduma Mota sortzea gomendatzen dizugu\",\n    \"Media Library\": \"Mediateka\",\n    \"New entry\": \"Sarrera berria\",\n    Password: Password,\n    Provider: Provider,\n    ResetPasswordToken: ResetPasswordToken,\n    Role: Role,\n    \"Roles & Permissions\": \"Rolak eta Baimenak\",\n    \"Roles.ListPage.notification.delete-all-not-allowed\": \"Rol batzuk ezin izan dira ezabatu, erabiltzaileekin lotuta baitaude\",\n    \"Roles.ListPage.notification.delete-not-allowed\": \"Ezin da rol bat ezabatu erabiltzaileekin lotuta badago\",\n    \"Roles.RoleRow.select-all\": \"Hautatu {name} ekintza masiboetarako\",\n    \"Roles.RoleRow.user-count\": \"{number, plural, =0 {#  user} one {#  user} other {# users}}\",\n    \"Roles.components.List.empty.withSearch\": \"Ez dago bilaketari dagokion paperik ({search})...\",\n    \"Settings.PageTitle\": \"Konfigurazioa - {name}}\",\n    \"Settings.apiTokens.addFirstToken\": \"Gehitu zure lehen API Token\",\n    \"Settings.apiTokens.addNewToken\": \"Gehitu API Token berri bat\",\n    \"Settings.tokens.copy.editMessage\": \"Segurtasun arrazoiengatik, behin bakarrik ikus dezakezu zure tokena\",\n    \"Settings.tokens.copy.editTitle\": \"Token hau jada ez dago eskuragarri\",\n    \"Settings.tokens.copy.lastWarning\": \"Ziurtatu token hau kopiatzen duzula, ezin izango duzu berriro ikusi!\",\n    \"Settings.apiTokens.create\": \"Sortu API Token berria\",\n    \"Settings.apiTokens.description\": \"APIa kontsumitzeko sortutako token zerrenda\",\n    \"Settings.apiTokens.emptyStateLayout\": \"Oraindik ez duzu edukirik...\",\n    \"Settings.apiTokens.ListView.headers.name\": \"Izena\",\n    \"Settings.apiTokens.ListView.headers.description\": \"Deskribapena\",\n    \"Settings.apiTokens.ListView.headers.type\": \"Token mota\",\n    \"Settings.apiTokens.ListView.headers.createdAt\": \"Hemen sortua\",\n    \"Settings.apiTokens.ListView.headers.lastUsedAt\": \"Azken erabilia\",\n    \"Settings.tokens.notification.copied\": \"Fitxa arbelean kopiatuta\",\n    \"Settings.apiTokens.title\": \"API Token-ak\",\n    \"Settings.tokens.types.full-access\": \"Sarrera osoa\",\n    \"Settings.tokens.types.read-only\": \"Irakurri bakarrik\",\n    \"Settings.tokens.duration.7-days\": \"7 egun\",\n    \"Settings.tokens.duration.30-days\": \"30 egun\",\n    \"Settings.tokens.duration.90-days\": \"90 egun\",\n    \"Settings.tokens.duration.unlimited\": \"Mugagabea\",\n    \"Settings.tokens.form.duration\": \"Token iraupena\",\n    \"Settings.tokens.form.type\": \"Token mota\",\n    \"Settings.tokens.duration.expiration-date\": \"Iraungitze data\",\n    \"Settings.apiTokens.createPage.permissions.title\": \"Baimenak\",\n    \"Settings.apiTokens.createPage.permissions.description\": \"Behean, bide bati lotutako ekintzak baino ez dira zerrendatzen\",\n    \"Settings.tokens.RegenerateDialog.title\": \"Birsortu tokena\",\n    \"Settings.tokens.popUpWarning.message\": \"Ziur zaude token hau birsortu nahi duzula?\",\n    \"Settings.tokens.Button.cancel\": \"Utzi\",\n    \"Settings.tokens.Button.regenerate\": \"Birsortu\",\n    \"Settings.application.description\": \"Administrazioa panelaren informazio orokorra\",\n    \"Settings.application.edition-title\": \"egungo edizioa\",\n    \"Settings.application.get-help\": \"Eskuratu laguntza\",\n    \"Settings.application.link-pricing\": \"Ikusi prezio plan guztiak\",\n    \"Settings.application.link-upgrade\": \"Eguneratu zure adminiztrazio panela\",\n    \"Settings.application.node-version\": \"node bertsioa\",\n    \"Settings.application.strapi-version\": \"strapi bertsioa\",\n    \"Settings.application.strapiVersion\": \"strapi bertsioa\",\n    \"Settings.application.title\": \"Ikuspegi orokorra\",\n    \"Settings.application.customization\": \"Pertsonalizazioa\",\n    \"Settings.application.customization.carousel.title\": \"Logoa\",\n    \"Settings.application.customization.carousel.change-action\": \"Logoa aldatu\",\n    \"Settings.application.customization.carousel.reset-action\": \"Logoa berrezarri\",\n    \"Settings.application.customization.carousel-slide.label\": \"Logoaren diapositiba\",\n    \"Settings.application.customization.carousel-hint\": \"Aldatu administrazio panelaren logotipoa (gehienezko dimentsioa: {dimension}x{dimension}, fitxategiaren gehienezko tamaina: {size}KB)\",\n    \"Settings.application.customization.modal.cancel\": \"Utzi\",\n    \"Settings.application.customization.modal.upload\": \"Igo logotipoa\",\n    \"Settings.application.customization.modal.tab.label\": \"Nola igo nahi dituzu zure fitxategiak?\",\n    \"Settings.application.customization.modal.upload.from-computer\": \"Ordenagailutik\",\n    \"Settings.application.customization.modal.upload.file-validation\": \"Gehienezko dimentsioa: {dimension}x{dimension}, Gehienezko tamaina: {size}KB\",\n    \"Settings.application.customization.modal.upload.error-format\": \"Formatu okerra igo da (onartutako formatuak soilik: jpeg, jpg, png, svg)\",\n    \"Settings.application.customization.modal.upload.error-size\": \"Kargatutako fitxategia handiegia da (gehienezko dimentsioa: {dimension}x{dimension}, gehienezko fitxategiaren tamaina: {size}KB)\",\n    \"Settings.application.customization.modal.upload.error-network\": \"Sareko errorea\",\n    \"Settings.application.customization.modal.upload.cta.browse\": \"Arakatu fitxategiak\",\n    \"Settings.application.customization.modal.upload.drag-drop\": \"Arrastatu eta bota hemen edo\",\n    \"Settings.application.customization.modal.upload.from-url\": \"URL-tik\",\n    \"Settings.application.customization.modal.upload.from-url.input-label\": \"URL\",\n    \"Settings.application.customization.modal.upload.next\": \"Hurrengoa\",\n    \"Settings.application.customization.modal.pending\": \"Logoaren zain\",\n    \"Settings.application.customization.modal.pending.choose-another\": \"Aukeratu beste logo bat\",\n    \"Settings.application.customization.modal.pending.title\": \"Logoa igotzeko prest\",\n    \"Settings.application.customization.modal.pending.subtitle\": \"Kudeatu aukeratutako logotipoa igo aurretik\",\n    \"Settings.application.customization.modal.pending.upload\": \"Igo logotipoa\",\n    \"Settings.application.customization.modal.pending.card-badge\": \"irudia\",\n    \"Settings.error\": \"Errorea\",\n    \"Settings.global\": \"Ezarpen orokorrak\",\n    \"Settings.permissions\": \"Administrazio panela\",\n    \"Settings.permissions.category\": \"{category}-ren baimen-ezarpenak\",\n    \"Settings.permissions.category.plugins\": \"{category} pluginaren baimenen ezarpenak\",\n    \"Settings.permissions.conditions.anytime\": \"Edonoiz\",\n    \"Settings.permissions.conditions.apply\": \"Aplikatu\",\n    \"Settings.permissions.conditions.can\": \"Ahal\",\n    \"Settings.permissions.conditions.conditions\": \"Baldintzak\",\n    \"Settings.permissions.conditions.links\": \"Estekak\",\n    \"Settings.permissions.conditions.no-actions\": \"Lehenik eta behin ekintzak hautatu behar dituzu (sortu, irakurri, eguneratu, ...) horien gaineko baldintzak zehaztu aurretik\",\n    \"Settings.permissions.conditions.none-selected\": \"Edonoiz\",\n    \"Settings.permissions.conditions.or\": \"EDO\",\n    \"Settings.permissions.conditions.when\": \"Noiz\",\n    \"Settings.permissions.select-all-by-permission\": \"Hautatu {label} baimen guztiak\",\n    \"Settings.permissions.select-by-permission\": \"Aukeratu {label} baimena\",\n    \"Settings.permissions.users.create\": \"Gonbidatu erabiltzaile berria\",\n    \"Settings.permissions.users.email\": \"Posta elektronikoa\",\n    \"Settings.permissions.users.firstname\": \"Izena\",\n    \"Settings.permissions.users.lastname\": \"Abizena\",\n    \"Settings.permissions.users.user-status\": \"Erabiltzailearen egoera\",\n    \"Settings.permissions.users.roles\": \"Rolak\",\n    \"Settings.permissions.users.username\": \"Erabiltzaile izena\",\n    \"Settings.permissions.users.active\": \"Aktibo\",\n    \"Settings.permissions.users.inactive\": \"Inaktibo\",\n    \"Settings.permissions.users.form.sso\": \"Konektatu SSOrekin\",\n    \"Settings.permissions.users.form.sso.description\": \"Gaituta dagoenean (AKTIBATUA), erabiltzaileek SSO bidez hasi dezakete saioa\",\n    \"Settings.permissions.users.listview.header.subtitle\": \"Strapi administrazio panelerako sarbidea duten erabiltzaile guztiak\",\n    \"Settings.permissions.users.tabs.label\": \"Fitxen baimenak\",\n    \"Settings.permissions.users.strapi-super-admin\": \"Super Administratzailea\",\n    \"Settings.permissions.users.strapi-editor\": \"Editorea\",\n    \"Settings.permissions.users.strapi-author\": \"Egilea\",\n    \"Settings.profile.form.notify.data.loaded\": \"Zure profileko datuak kargatu dira\",\n    \"Settings.profile.form.section.experience.clear.select\": \"Garbitu hautatutako interfazearen hizkuntza\",\n    \"Settings.profile.form.section.experience.here\": \"hemen\",\n    \"Settings.profile.form.section.experience.interfaceLanguage\": \"Interfaze hizkuntza\",\n    \"Settings.profile.form.section.experience.interfaceLanguage.hint\": \"Honek zure interfazea aukeratutako hizkuntzan bakarrik erakutsiko du\",\n    \"Settings.profile.form.section.experience.interfaceLanguageHelp\": \"Hobespen-aldaketak zuri bakarrik aplikatuko dira. Informazio gehiago eskuragarri dago {here}\",\n    \"Settings.profile.form.section.experience.mode.label\": \"Interfaze modua\",\n    \"Settings.profile.form.section.experience.mode.hint\": \"Zure interfazea erakusten du aukeratutako moduan\",\n    \"Settings.profile.form.section.experience.mode.option-label\": \"{name} modua\",\n    light: light,\n    dark: dark,\n    \"Settings.profile.form.section.experience.title\": \"Esperientzia\",\n    \"Settings.profile.form.section.head.title\": \"Erabiltzailearen profila\",\n    \"Settings.profile.form.section.profile.page.title\": \"Profil-orria\",\n    \"Settings.roles.create.description\": \"Rolari emandako eskubideak definitu\",\n    \"Settings.roles.create.title\": \"Rol bat sortu\",\n    \"Settings.roles.created\": \"Sortutako rola\",\n    \"Settings.roles.edit.title\": \"Rol bat editatu\",\n    \"Settings.roles.form.button.users-with-role\": \"{number, plural, =0 {# users} one {# user} other {# users}} rol honekin\",\n    \"Settings.roles.form.created\": \"Sortuak\",\n    \"Settings.roles.form.description\": \"Rolaren izena eta deskribapena\",\n    \"Settings.roles.form.permission.property-label\": \"{label} baimenak\",\n    \"Settings.roles.form.permissions.attributesPermissions\": \"Eremuen baimenak\",\n    \"Settings.roles.form.permissions.create\": \"Sortu\",\n    \"Settings.roles.form.permissions.delete\": \"Ezabatu\",\n    \"Settings.roles.form.permissions.publish\": \"Argitaratu\",\n    \"Settings.roles.form.permissions.read\": \"Irakurri\",\n    \"Settings.roles.form.permissions.update\": \"Eguneratu\",\n    \"Settings.roles.list.button.add\": \"Rol berria gehitu\",\n    \"Settings.roles.list.description\": \"Rolen zerrenda\",\n    \"Settings.roles.title.singular\": \"rol\",\n    \"Settings.sso.description\": \"Konfiguratu Single Sign-On ezaugarrirako ezarpenak\",\n    \"Settings.sso.form.defaultRole.description\": \"Hautatutako rolari autentifikatutako erabiltzaile berria erantsiko dio\",\n    \"Settings.sso.form.defaultRole.description-not-allowed\": \"Admin rolak irakurtzeko baimena izan behar duzu\",\n    \"Settings.sso.form.defaultRole.label\": \"Lehenetsitako rola\",\n    \"Settings.sso.form.registration.description\": \"Sortu erabiltzaile berria SSO saioan, konturik ez badago\",\n    \"Settings.sso.form.registration.label\": \"Erregistro automatikoa\",\n    \"Settings.sso.title\": \"Single Sign-On\",\n    \"Settings.webhooks.create\": \"Sortu webhook bat\",\n    \"Settings.webhooks.create.header\": \"Sortu goiburu berria\",\n    \"Settings.webhooks.created\": \"Webhooka sortu da\",\n    \"Settings.webhooks.event.publish-tooltip\": \"Gertaera hau Zirriborroa/Argitaratu sistema gaituta duten edukietarako soilik dago\",\n    \"Settings.webhooks.events.create\": \"Sortu\",\n    \"Settings.webhooks.events.update\": \"Eguneratu\",\n    \"Settings.webhooks.form.events\": \"Gertaerak\",\n    \"Settings.webhooks.form.headers\": \"Goiburuak\",\n    \"Settings.webhooks.form.url\": \"Url\",\n    \"Settings.webhooks.headers.remove\": \"Kendu goiburuko errenkada {number}\",\n    \"Settings.webhooks.key\": \"Giltza\",\n    \"Settings.webhooks.list.button.add\": \"Sortu webhook berria\",\n    \"Settings.webhooks.list.description\": \"Jaso POST aldaketen jakinarazpenak\",\n    \"Settings.webhooks.list.empty.description\": \"Ez da webhook-ik aurkitu\",\n    \"Settings.webhooks.list.empty.link\": \"Ikusi gure dokumentazioa\",\n    \"Settings.webhooks.list.empty.title\": \"Oraindik ez dago webhookik\",\n    \"Settings.webhooks.list.th.actions\": \"ekintzak\",\n    \"Settings.webhooks.list.th.status\": \"egoera\",\n    \"Settings.webhooks.singular\": \"webhooka\",\n    \"Settings.webhooks.title\": \"Webhookak\",\n    \"Settings.webhooks.to.delete\": \"{webhooksToDeleteLength, plural, one {# asset} other {# assets}} selected\",\n    \"Settings.webhooks.trigger\": \"Kakoa\",\n    \"Settings.webhooks.trigger.cancel\": \"Kakoa ezeztatu\",\n    \"Settings.webhooks.trigger.pending\": \"Zain…\",\n    \"Settings.webhooks.trigger.save\": \"Mesedez, gorde kakoa abiarazteko\",\n    \"Settings.webhooks.trigger.success\": \"Ondo!\",\n    \"Settings.webhooks.trigger.success.label\": \"Kakoa ondo abiarazi da\",\n    \"Settings.webhooks.trigger.test\": \"Test-abiarazlea\",\n    \"Settings.webhooks.trigger.title\": \"Gorde Abiarazi aurretik\",\n    \"Settings.webhooks.value\": \"Balioa\",\n    \"Usecase.back-end\": \"Back-end garatzailea\",\n    \"Usecase.button.skip\": \"Saltatu galdera hau\",\n    \"Usecase.content-creator\": \"Eduki sortzailea\",\n    \"Usecase.front-end\": \"Frontend garatzailea\",\n    \"Usecase.full-stack\": \"Full-stack garatzailea\",\n    \"Usecase.input.work-type\": \"Zer lan mota egiten duzu?\",\n    \"Usecase.notification.success.project-created\": \"Proiektua ondo sortu da\",\n    \"Usecase.other\": \"Beste bat\",\n    \"Usecase.title\": \"Esaiguzu zeure buruari buruz pixka bat gehiago\",\n    Username: Username,\n    Users: Users,\n    \"Users & Permissions\": \"Erabiltzaileak eta baimenak\",\n    \"Users.components.List.empty\": \"Ez dago erabiltzailerik...\",\n    \"Users.components.List.empty.withFilters\": \"Ez dago erabiltzailerik aplikatutako iragazkiekin...\",\n    \"Users.components.List.empty.withSearch\": \"Ez dago ({search}) bilaketari dagokion erabiltzailerik ...\",\n    \"admin.pages.MarketPlacePage.head\": \"Marketplace - Pluginak\",\n    \"admin.pages.MarketPlacePage.offline.title\": \"Lineaz kanpo zaude\",\n    \"admin.pages.MarketPlacePage.offline.subtitle\": \"Internetera konektatuta egon behar duzu Strapi merkatuan sartzeko\",\n    \"admin.pages.MarketPlacePage.plugins\": \"Pluginak\",\n    \"admin.pages.MarketPlacePage.plugin.copy\": \"Kopiatu instalatzeko komandoa\",\n    \"admin.pages.MarketPlacePage.plugin.copy.success\": \"Instalatzeko komandoa prest zure terminalean itsasteko\",\n    \"admin.pages.MarketPlacePage.plugin.info\": \"Gehiago ikasi\",\n    \"admin.pages.MarketPlacePage.plugin.info.label\": \"Informazio gehiago {pluginName} -i buruz\",\n    \"admin.pages.MarketPlacePage.plugin.info.text\": \"Gehiago\",\n    \"admin.pages.MarketPlacePage.plugin.installed\": \"Instalatuta\",\n    \"admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi\": \"Strapik egina\",\n    \"admin.pages.MarketPlacePage.plugin.tooltip.verified\": \"Strapi-k egiaztatutako plugina\",\n    \"admin.pages.MarketPlacePage.plugin.version\": \"Eguneratu zure Strapi bertsioa: \\\"{strapiAppVersion}\\\" -tik \\\"{versionRange}\\\"-ra\",\n    \"admin.pages.MarketPlacePage.plugin.version.null\": \"Ezin da egiaztatu zure Strapi bertsioarekin bateragarritasuna: \\\"{strapiAppVersion}\\\"\",\n    \"admin.pages.MarketPlacePage.plugin.githubStars\": \"Plugin honek {starsCount} izar dauzka GitHub-en\",\n    \"admin.pages.MarketPlacePage.plugin.downloads\": \"Plugin honek astero {downloadsCount} deskarga ditu\",\n    \"admin.pages.MarketPlacePage.providers\": \"Hornitzaileak\",\n    \"admin.pages.MarketPlacePage.provider.githubStars\": \"Hornitzaile honek {starsCount} izar dauzka GitHub-en\",\n    \"admin.pages.MarketPlacePage.provider.downloads\": \"Hornitzaile honek astero {downloadsCount} deskarga ditu\",\n    \"admin.pages.MarketPlacePage.search.clear\": \"Garbitu bilaketa\",\n    \"admin.pages.MarketPlacePage.search.empty\": \"Ez dago emaitzarik \\\"{target}\\\" -rako\",\n    \"admin.pages.MarketPlacePage.search.placeholder\": \"Bilatu\",\n    \"admin.pages.MarketPlacePage.submit.plugin.link\": \"Bidali plugina\",\n    \"admin.pages.MarketPlacePage.submit.provider.link\": \"Bidali hornitzailea\",\n    \"admin.pages.MarketPlacePage.subtitle\": \"Atera gehiago Strapi-ri\",\n    \"admin.pages.MarketPlacePage.tab-group.label\": \"Strapi-rako pluginak eta hornitzaileak\",\n    \"admin.pages.MarketPlacePage.missingPlugin.title\": \"Plugin bat falta zaizu?\",\n    \"admin.pages.MarketPlacePage.missingPlugin.description\": \"Esaiguzu zer plugin bilatzen ari zaren eta gure komunitateko plugin-en garatzaileei jakinaraziko diegu inspirazio bila badabiltza!\",\n    \"admin.pages.MarketPlacePage.sort.alphabetical\": \"Ordena alfabetikoa\",\n    \"admin.pages.MarketPlacePage.sort.newest\": \"Berriena\",\n    \"admin.pages.MarketPlacePage.sort.alphabetical.selected\": \"Ordenatu ordena alfabetikoaren arabera\",\n    \"admin.pages.MarketPlacePage.sort.newest.selected\": \"Ordenatu berrienaren arabera\",\n    \"admin.pages.MarketPlacePage.sort.githubStars\": \"GitHub izar kopurua\",\n    \"admin.pages.MarketPlacePage.sort.githubStars.selected\": \"Ordenatu GitHub izarren arabera\",\n    \"admin.pages.MarketPlacePage.sort.npmDownloads\": \"Deskarga kopurua\",\n    \"admin.pages.MarketPlacePage.sort.npmDownloads.selected\": \"Ordenatu npm deskargaren arabera\",\n    \"admin.pages.MarketPlacePage.filters.collections\": \"Bildumak\",\n    \"admin.pages.MarketPlacePage.filters.collectionsSelected\": \"{count, plural, =0 {Ez dago bildumarik} {# collection} bat beste {# collections}} hautatuta\",\n    \"admin.pages.MarketPlacePage.filters.categories\": \"Kategoriak\",\n    \"admin.pages.MarketPlacePage.filters.categoriesSelected\": \"{count, plural, =0 {Ez dago kategoriarik} {# category} bat beste {# categories}} hautatuta\",\n    anErrorOccurred: anErrorOccurred,\n    \"app.component.CopyToClipboard.label\": \"Kopiatu arbelean\",\n    \"app.component.search.label\": \"Bilatu {target}\",\n    \"app.component.table.duplicate\": \"Bikoiztu {target}\",\n    \"app.component.table.edit\": \"Editatu {target}\",\n    \"app.component.table.select.one-entry\": \"Aukeratu {target}\",\n    \"app.components.BlockLink.blog\": \"Bloga\",\n    \"app.components.BlockLink.blog.content\": \"Irakurri Strapi eta ekosistemari buruzko azken berriak\",\n    \"app.components.BlockLink.code\": \"Kode adibideak\",\n    \"app.components.BlockLink.code.content\": \"Ikasi komunitateak garatutako benetako proiektuak probatuz\",\n    \"app.components.BlockLink.documentation.content\": \"Ezagutu funtsezko kontzeptuak, gidak eta argibideak\",\n    \"app.components.BlockLink.tutorial\": \"Tutorialak\",\n    \"app.components.BlockLink.tutorial.content\": \"Jarraitu urratsez urrats Strapi erabiltzeko eta pertsonalizatzeko argibideak\",\n    \"app.components.Button.cancel\": \"Utzi\",\n    \"app.components.Button.confirm\": \"Berretsi\",\n    \"app.components.Button.reset\": \"Berrezarri\",\n    \"app.components.ComingSoonPage.comingSoon\": \"Laister\",\n    \"app.components.ConfirmDialog.title\": \"Berrespena\",\n    \"app.components.DownloadInfo.download\": \"Deskarga abian...\",\n    \"app.components.DownloadInfo.text\": \"Minutu bat iraun dezake. Eskerrik asko zure pazientziagatik\",\n    \"app.components.EmptyAttributes.title\": \"Oraindik ez dago eremurik\",\n    \"app.components.EmptyStateLayout.content-document\": \"Ez da edukirik aurkitu\",\n    \"app.components.EmptyStateLayout.content-permissions\": \"Ez duzu eduki hori eskuratzeko baimenik\",\n    \"app.components.GuidedTour.CM.create.content\": \"<p>Sortu eta kudeatu eduki guztia hemen Eduki-kudeatzailean.</p><p>Adib.: Blogaren webgunearen adibidea harago hartuta, Artikulu bat idatzi, gorde eta nahi duen moduan argitaratu.</p><p> p>💡 Aholku azkarra - Ez ahaztu argitaratzea sakatzea sortzen duzun edukia.</p>\",\n    \"app.components.GuidedTour.CM.create.title\": \"⚡️ Sortu edukia\",\n    \"app.components.GuidedTour.CM.success.content\": \"<p>Ikaragarria, azken urratsa egiteko!</p><b>🚀 Ikusi edukia martxan</b>\",\n    \"app.components.GuidedTour.CM.success.cta.title\": \"Probatu APIa\",\n    \"app.components.GuidedTour.CM.success.title\": \"2. urratsa: amaituta ✅\",\n    \"app.components.GuidedTour.CTB.create.content\": \"<p>Bilduma motak hainbat sarrera kudeatzen laguntzen dizute, Mota bakarrak sarrera bakarra kudeatzeko egokiak dira.</p> <p>Adib.: Blogaren webgunerako, Artikuluak Bilduma mota izango lirateke eta Hasierako orria mota bakarra izango litzateke. </p>\",\n    \"app.components.GuidedTour.CTB.create.cta.title\": \"Eraiki bilduma mota\",\n    \"app.components.GuidedTour.CTB.create.title\": \"🧠 Sortu lehen bilduma mota bat\",\n    \"app.components.GuidedTour.CTB.success.content\": \"<p>Ondo izan!</p><b>⚡️ Zer gustatuko litzaizuke partekatu munduarekin?</b>\",\n    \"app.components.GuidedTour.CTB.success.title\": \"1. urratsa: amaituta ✅\",\n    \"app.components.GuidedTour.apiTokens.create.content\": \"<p>Sortu hemen autentifikazio-token bat eta berreskuratu sortu berri duzun edukia.</p>\",\n    \"app.components.GuidedTour.apiTokens.create.cta.title\": \"Sortu API Token bat\",\n    \"app.components.GuidedTour.apiTokens.create.title\": \"🚀 Ikusi edukia martxan\",\n    \"app.components.GuidedTour.apiTokens.success.content\": \"<p>Ikusi edukia martxan dagoen HTTP eskaera eginez:</p><ul><li><p>URL honetara: <light>https://'<'YOUR_DOMAIN'>'/api/'<' YOUR_CT'>'</light></p></li><li><p>Goiburuarekin: <light>Baimena: '<'YOUR_API_TOKEN'>'</light></p></li eramailea ></ul><p>Edukiarekin elkarreragiteko modu gehiago lortzeko, ikusi <documentationLink>dokumentazioa</documentationLink>.</p>\",\n    \"app.components.GuidedTour.apiTokens.success.cta.title\": \"Itzuli hasierako orrira\",\n    \"app.components.GuidedTour.apiTokens.success.title\": \"3. urratsa: amaituta ✅\",\n    \"app.components.GuidedTour.create-content\": \"Sortu edukia\",\n    \"app.components.GuidedTour.home.CM.title\": \"⚡️ Zer gustatuko litzaizuke partekatzea munduarekin?\",\n    \"app.components.GuidedTour.home.CTB.cta.title\": \"Joan Eduki motaren Eraikitzailera\",\n    \"app.components.GuidedTour.home.CTB.title\": \"🧠 Eraiki edukiaren egitura\",\n    \"app.components.GuidedTour.home.apiTokens.cta.title\": \"Proba ezazu APIa\",\n    \"app.components.GuidedTour.skip\": \"Saltatu bira\",\n    \"app.components.GuidedTour.title\": \"Hasteko 3 urrats\",\n    \"app.components.HomePage.button.blog\": \"Ikusi gehiago blogean\",\n    \"app.components.HomePage.community\": \"Sartu komunitatean\",\n    \"app.components.HomePage.community.content\": \"Eztabaidatu taldekideekin, laguntzaileekin eta garatzaileekin kanal ezberdinetan\",\n    \"app.components.HomePage.create\": \"Sortu zure lehen eduki mota\",\n    \"app.components.HomePage.roadmap\": \"Ikusi gure bide orria\",\n    \"app.components.HomePage.welcome\": \"Ongi etorri ontzira 👋\",\n    \"app.components.HomePage.welcome.again\": \"Ongi etorri 👋\",\n    \"app.components.HomePage.welcomeBlock.content\": \"Zorionak! Lehen administratzaile gisa erregistratu zara. Strapi-k eskaintzen dituen funtzio indartsuak ezagutzeko, zure lehen Eduki mota sortzea gomendatzen dizugu!\",\n    \"app.components.HomePage.welcomeBlock.content.again\": \"Zure proiektuan aurrera egitea espero dugu! Strapiri buruzko azken berriak irakurtzeko libre sentitu. Onena ematen ari gara zure feedbackean oinarritutako produktua hobetzeko\",\n    \"app.components.HomePage.welcomeBlock.content.issues\": \"gaiak\",\n    \"app.components.HomePage.welcomeBlock.content.raise\": \" edo igo \",\n    \"app.components.ImgPreview.hint\": \"Arrastatu eta jaregin zure fitxategia eremu honetara edo {browse} fitxategi bat kargatzeko\",\n    \"app.components.ImgPreview.hint.browse\": \"arakatu\",\n    \"app.components.InputFile.newFile\": \"Gehitu fitxategi berria\",\n    \"app.components.InputFileDetails.open\": \"Ireki fitxa berri batean\",\n    \"app.components.InputFileDetails.originalName\": \"Jatorrizko izena:\",\n    \"app.components.InputFileDetails.remove\": \"Ezabatu fitxategi hau\",\n    \"app.components.InputFileDetails.size\": \"Tamaina:\",\n    \"app.components.InstallPluginPage.Download.description\": \"Baliteke segundo batzuk behar izatea plugina deskargatu eta instalatzeko\",\n    \"app.components.InstallPluginPage.Download.title\": \"Deskargatzen...\",\n    \"app.components.InstallPluginPage.description\": \"Luzatu zure aplikazioa ahalegin handirik egin gabe\",\n    \"app.components.LeftMenu.collapse\": \"Nafarra kolapsatu\",\n    \"app.components.LeftMenu.expand\": \"Tolestu nabigazio-barra\",\n    \"app.components.LeftMenu.general\": \"Orokorra\",\n    \"app.components.LeftMenu.logout\": \"Saioa amaitu\",\n    \"app.components.LeftMenu.logo.alt\": \"Aplikazioaren logotipoa\",\n    \"app.components.LeftMenu.plugins\": \"Pluginak\",\n    \"app.components.LeftMenu.trialCountdown\": \"Zure proba {date} egunera amaituko du.\",\n    \"app.components.LeftMenu.navbrand.title\": \"Strapi Arbela\",\n    \"app.components.LeftMenu.navbrand.workplace\": \"Lantokia\",\n    \"app.components.LeftMenuFooter.help\": \"Laguntza\",\n    \"app.components.LeftMenuFooter.poweredBy\": \"Powered by\",\n    \"app.components.LeftMenuLinkContainer.collectionTypes\": \"Bilduma motak\",\n    \"app.components.LeftMenuLinkContainer.configuration\": \"Konfigurazioak\",\n    \"app.components.LeftMenuLinkContainer.general\": \"Orokorra\",\n    \"app.components.LeftMenuLinkContainer.noPluginsInstalled\": \"Oraindik ez dago pluginik instalatuta\",\n    \"app.components.LeftMenuLinkContainer.plugins\": \"Pluginak\",\n    \"app.components.LeftMenuLinkContainer.singleTypes\": \"Mota bakarrak\",\n    \"app.components.ListPluginsPage.deletePlugin.description\": \"Baliteke segundo batzuk behar izatea plugina desinstalatzeko\",\n    \"app.components.ListPluginsPage.deletePlugin.title\": \"Desinstalatzen\",\n    \"app.components.ListPluginsPage.description\": \"Proiektuan instalatutako pluginen zerrenda\",\n    \"app.components.ListPluginsPage.head.title\": \"Zerrendatu pluginak\",\n    \"app.components.Logout.logout\": \"Saioa amaitu\",\n    \"app.components.Logout.profile\": \"Profila\",\n    \"app.components.MarketplaceBanner\": \"Ezagutu komunitateak eraikitako pluginak eta zure proiektua abiarazteko gauza ikaragarri gehiago, Strapi Awesome-n\",\n    \"app.components.MarketplaceBanner.image.alt\": \"strapi suziriaren logotipoa\",\n    \"app.components.MarketplaceBanner.link\": \"Begiratu orain\",\n    \"app.components.NotFoundPage.back\": \"Itzuli hasierako orrira\",\n    \"app.components.NotFoundPage.description\": \"Ez da aurkitu\",\n    \"app.components.Official\": \"Ofiziala\",\n    \"app.components.Onboarding.help.button\": \"Laguntza botoia\",\n    \"app.components.Onboarding.label.completed\": \"% osatua\",\n    \"app.components.Onboarding.title\": \"Hasteko bideoak\",\n    \"app.components.PluginCard.Button.label.download\": \"Deskargatu\",\n    \"app.components.PluginCard.Button.label.install\": \"Dagoeneko instalatuta\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed\": \"AutoReload funtzioa gaitu behar da. Hasi zure aplikazioa `yarn develop`-rekin\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.confirm\": \"Ulertzen dut!\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.environment\": \"Segurtasun arrazoiengatik, plugin bat garapen-ingurunean soilik deskargatu daiteke\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.title\": \"Deskargatzea ezinezkoa da\",\n    \"app.components.PluginCard.compatible\": \"Zure aplikazioarekin bateragarria\",\n    \"app.components.PluginCard.compatibleCommunity\": \"Komunitatearekin bateragarria\",\n    \"app.components.PluginCard.more-details\": \"Xehetasun gehiago\",\n    \"app.components.ToggleCheckbox.off-label\": \"Gezurra\",\n    \"app.components.ToggleCheckbox.on-label\": \"Egia\",\n    \"app.components.Users.MagicLink.connect\": \"Kopiatu eta partekatu esteka hau erabiltzaile honi sarbidea emateko\",\n    \"app.components.Users.MagicLink.connect.sso\": \"Bidali esteka hau erabiltzaileari, lehen saioa SSO hornitzaile baten bidez egiteko\",\n    \"app.components.Users.ModalCreateBody.block-title.details\": \"Erabiltzailearen datuak\",\n    \"app.components.Users.ModalCreateBody.block-title.roles\": \"Erabiltzailearen rolak\",\n    \"app.components.Users.ModalCreateBody.block-title.roles.description\": \"Erabiltzaile batek rol bat edo batzuk izan ditzake\",\n    \"app.components.Users.SortPicker.button-label\": \"Ordenatu\",\n    \"app.components.Users.SortPicker.sortby.email_asc\": \"Posta elektronikoa (A-tik Z-ra)\",\n    \"app.components.Users.SortPicker.sortby.email_desc\": \"Posta elektronikoa (Z-tik A-ra)\",\n    \"app.components.Users.SortPicker.sortby.firstname_asc\": \"Izena (A-tik Z-ra)\",\n    \"app.components.Users.SortPicker.sortby.firstname_desc\": \"Izena (Z-tik A-ra)\",\n    \"app.components.Users.SortPicker.sortby.lastname_asc\": \"Abizena (A-tik Z-ra)\",\n    \"app.components.Users.SortPicker.sortby.lastname_desc\": \"Abizena (Z-tik A-ra)\",\n    \"app.components.Users.SortPicker.sortby.username_asc\": \"Erabiltzaile izena (A-tik Z-ra)\",\n    \"app.components.Users.SortPicker.sortby.username_desc\": \"Erabiltzaile izena (Z-tik A-ra)\",\n    \"app.components.listPlugins.button\": \"Gehitu plugin berria\",\n    \"app.components.listPlugins.title.none\": \"Ez dago pluginik instalatuta\",\n    \"app.components.listPluginsPage.deletePlugin.error\": \"Errorea gertatu da plugina desinstalatzean\",\n    \"app.containers.App.notification.error.init\": \"Errore bat gertatu da APIa eskatzean\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin\": \"Ez baduzu esteka hau jasotzen, jar zaitez harremanetan zure administratzailearekin\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.text.email\": \"Minutu batzuk pasatu daitezke pasahitza berreskuratzeko esteka jasotzeko\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.title\": \"Bidali da mezu elektronikoa\",\n    \"app.containers.Users.EditPage.form.active.label\": \"Aktiboa\",\n    \"app.containers.Users.EditPage.header.label\": \"Editatu {name}\",\n    \"app.containers.Users.EditPage.header.label-loading\": \"Editatu erabiltzailea\",\n    \"app.containers.Users.EditPage.roles-bloc-title\": \"Esleitutako rolak\",\n    \"app.containers.Users.ModalForm.footer.button-success\": \"Gonbidatu erabiltzailea\",\n    \"app.links.configure-view\": \"Konfiguratu ikuspegia\",\n    \"app.page.not.found\": \"Arraioa! Badirudi ez dugula bilatzen ari zaren orria aurkitu...\",\n    \"app.static.links.cheatsheet\": \"CheatSheet\",\n    \"app.utils.SelectOption.defaultMessage\": \" \",\n    \"app.utils.add-filter\": \"Gehitu iragazkia\",\n    \"app.utils.close-label\": \"Itxi\",\n    \"app.utils.defaultMessage\": \" \",\n    \"app.utils.duplicate\": \"Bikoiztu\",\n    \"app.utils.edit\": \"Editatu\",\n    \"app.utils.delete\": \"Ezabatu\",\n    \"app.utils.errors.file-too-big.message\": \"Fitxategia handiegia da\",\n    \"app.utils.filter-value\": \"Iragazkiaren balioa\",\n    \"app.utils.filters\": \"Iragazkiak\",\n    \"app.utils.notify.data-loaded\": \"{target} kargatu da\",\n    \"app.utils.placeholder.defaultMessage\": \" \",\n    \"app.utils.publish\": \"Argitaratu\",\n    \"app.utils.select-all\": \"Hautatu guztiak\",\n    \"app.utils.select-field\": \"Hautatu eremua\",\n    \"app.utils.select-filter\": \"Hautatu iragazkia\",\n    \"app.utils.unpublish\": \"Argitaratu gabe\",\n    clearLabel: clearLabel,\n    \"coming.soon\": \"Eduki hau eraikitzen ari da eta aste batzuk barru itzuliko da!\",\n    \"component.Input.error.validation.integer\": \"Balioak zenbaki oso bat izan behar du\",\n    \"components.AutoReloadBlocker.description\": \"Exekutatu Strapi komando hauetako batekin:\",\n    \"components.AutoReloadBlocker.header\": \"Plugin honetarako birkargatu eginbidea beharrezkoa da\",\n    \"components.ErrorBoundary.title\": \"Zerbait gaizki atera da...\",\n    \"components.FilterOptions.FILTER_TYPES.$contains\": \"da\",\n    \"components.FilterOptions.FILTER_TYPES.$containsi\": \"da (ez ditu letra larriak eta txikiak bereizten)\",\n    \"components.FilterOptions.FILTER_TYPES.$endsWith\": \"honekin bukatzen da\",\n    \"components.FilterOptions.FILTER_TYPES.$endsWithi\": \"honekin bukatzen da (ez ditu letra larriak eta txikiak bereizten)\",\n    \"components.FilterOptions.FILTER_TYPES.$eq\": \"da\",\n    \"components.FilterOptions.FILTER_TYPES.$eqi\": \"da (ez ditu letra larriak eta txikiak bereizten)\",\n    \"components.FilterOptions.FILTER_TYPES.$gt\": \"baino handiagoa da\",\n    \"components.FilterOptions.FILTER_TYPES.$gte\": \"baino handiagoa edo berdina da\",\n    \"components.FilterOptions.FILTER_TYPES.$lt\": \"baino baxuagoa da\",\n    \"components.FilterOptions.FILTER_TYPES.$lte\": \"baino txikiagoa edo berdina da\",\n    \"components.FilterOptions.FILTER_TYPES.$ne\": \"ez da\",\n    \"components.FilterOptions.FILTER_TYPES.$nei\": \"ez da (ez ditu letra larriak eta txikiak bereizten)\",\n    \"components.FilterOptions.FILTER_TYPES.$notContains\": \"ez dauka\",\n    \"components.FilterOptions.FILTER_TYPES.$notContainsi\": \"ez dauka (ez ditu letra larriak eta txikiak bereizten)\",\n    \"components.FilterOptions.FILTER_TYPES.$notNull\": \"ez da nulua\",\n    \"components.FilterOptions.FILTER_TYPES.$null\": \"nulua da\",\n    \"components.FilterOptions.FILTER_TYPES.$startsWith\": \"hasten da\",\n    \"components.FilterOptions.FILTER_TYPES.$startsWithi\": \"hasten da  (ez ditu letra larriak eta txikiak bereizten)\",\n    \"components.Input.error.attribute.key.taken\": \"Balio hori existitzen da\",\n    \"components.Input.error.attribute.sameKeyAndName\": \"Ezin da berdina izan\",\n    \"components.Input.error.attribute.taken\": \"Eremu-izen hau dagoeneko badago\",\n    \"components.Input.error.contain.lowercase\": \"Pasahitzak karaktere xehe bat izan behar du gutxienez\",\n    \"components.Input.error.contain.number\": \"Pasahitzak zenbaki bat izan behar du gutxienez\",\n    \"components.Input.error.contain.uppercase\": \"Pasahitzak karaktere maiuskula bat izan behar du gutxienez\",\n    \"components.Input.error.contentTypeName.taken\": \"Izen hau dagoeneko existitzen da\",\n    \"components.Input.error.custom-error\": \"{errorMessage}\",\n    \"components.Input.error.password.noMatch\": \"Pasahitzak ez datoz bat\",\n    \"components.Input.error.validation.email\": \"Posta elektroniko hau ez da baliozkoa\",\n    \"components.Input.error.validation.json\": \"Hau ez dator bat JSON formatuarekin\",\n    \"components.Input.error.validation.lowercase\": \"Balioa minuskulazko kate bat izan behar da\",\n    \"components.Input.error.validation.max\": \"Balioa handiegia da {max}\",\n    \"components.Input.error.validation.maxLength\": \"Balioa luzeegia da {max}\",\n    \"components.Input.error.validation.min\": \"Balioa txikiegia da {min}\",\n    \"components.Input.error.validation.minLength\": \"Balioa laburregia da {min}\",\n    \"components.Input.error.validation.minSupMax\": \"Ezin da handiagoa izan\",\n    \"components.Input.error.validation.regex\": \"Balioa ez dator bat regex-arekin\",\n    \"components.Input.error.validation.required\": \"Balio hau beharrezkoa da\",\n    \"components.Input.error.validation.unique\": \"Balio hau jada erabilita dago\",\n    \"components.InputSelect.option.placeholder\": \"Aukeratu hemen\",\n    \"components.ListRow.empty\": \"Ez dago daturik erakusteko\",\n    \"components.NotAllowedInput.text\": \"Ez dago baimenik eremu hau ikusteko\",\n    \"components.OverlayBlocker.description\": \"Zerbitzaria berrabiarazi behar duen funtzio bat erabiltzen ari zara. Mesedez, itxaron zerbitzaria abiarazi arte\",\n    \"components.OverlayBlocker.description.serverError\": \"Zerbitzariak berrabiarazi behar zuen, mesedez egiaztatu zure erregistroak terminalean\",\n    \"components.OverlayBlocker.title\": \"Berrabiarazteko zain...\",\n    \"components.OverlayBlocker.title.serverError\": \"Berrabiaraztea espero baino denbora gehiago hartzen ari da\",\n    \"components.PageFooter.select\": \"Sarrerak orrialde bakoitzeko\",\n    \"components.ProductionBlocker.description\": \"Segurtasun helburuetarako plugin hau beste ingurune batzuetan desgaitu behar dugu\",\n    \"components.ProductionBlocker.header\": \"Plugin hau garapenean bakarrik dago eskuragarri\",\n    \"components.Search.placeholder\": \"Bilatu...\",\n    \"components.TableHeader.sort\": \"Ordenatu {label}\",\n    \"components.Wysiwyg.ToggleMode.markdown-mode\": \"Markdown modua\",\n    \"components.Wysiwyg.ToggleMode.preview-mode\": \"Aurrebista modua\",\n    \"components.Wysiwyg.collapse\": \"Tolestu\",\n    \"components.Wysiwyg.selectOptions.H1\": \"H1 titulua\",\n    \"components.Wysiwyg.selectOptions.H2\": \"H2 titulua\",\n    \"components.Wysiwyg.selectOptions.H3\": \"H3 titulua\",\n    \"components.Wysiwyg.selectOptions.H4\": \"H4 titulua\",\n    \"components.Wysiwyg.selectOptions.H5\": \"H5 titulua\",\n    \"components.Wysiwyg.selectOptions.H6\": \"H6 titulua\",\n    \"components.Wysiwyg.selectOptions.title\": \"Gehitu izenburu bat\",\n    \"components.WysiwygBottomControls.charactersIndicators\": \"karaktereak\",\n    \"components.WysiwygBottomControls.fullscreen\": \"Zabaldu\",\n    \"components.WysiwygBottomControls.uploadFiles\": \"Arrastatu eta jaregin fitxategiak, itsatsi arbeletik edo {browse}\",\n    \"components.WysiwygBottomControls.uploadFiles.browse\": \"hautatu itzazu\",\n    \"components.pagination.go-to\": \"Joan {page} orrialdera\",\n    \"components.pagination.go-to-next\": \"Joan hurrengo orrialdera\",\n    \"components.pagination.go-to-previous\": \"Joan aurreko orrialdera\",\n    \"components.pagination.remaining-links\": \"Eta beste {number} esteka\",\n    \"components.popUpWarning.button.cancel\": \"Ez, bertan behera\",\n    \"components.popUpWarning.button.confirm\": \"Bai, berretsi\",\n    \"components.popUpWarning.message\": \"Ziur zaude hau ezabatu nahi duzula?\",\n    \"components.popUpWarning.title\": \"Mesedez, berretsi\",\n    \"form.button.continue\": \"Jarraitu\",\n    \"form.button.done\": \"Eginda\",\n    \"global.search\": \"Bilatu\",\n    \"global.actions\": \"Ekintzak\",\n    \"global.back\": \"Itzuli\",\n    \"global.cancel\": \"Utzi\",\n    \"global.change-password\": \"Pasahitza aldatu\",\n    \"global.content-manager\": \"Edukien kudeatzailea\",\n    \"global.continue\": \"Jarraitu\",\n    \"global.delete\": \"Ezabatu\",\n    \"global.delete-target\": \"Ezabatu {target}\",\n    \"global.description\": \"Deskribapena\",\n    \"global.details\": \"Xehetasunak\",\n    \"global.disabled\": \"Desgaituta\",\n    \"global.documentation\": \"Dokumentazioa\",\n    \"global.enabled\": \"Gaituta\",\n    \"global.finish\": \"Amaitu\",\n    \"global.marketplace\": \"Marketplace\",\n    \"global.name\": \"Izena\",\n    \"global.none\": \"Bat ere ez\",\n    \"global.password\": \"Pasahitza\",\n    \"global.plugins\": \"Pluginak\",\n    \"global.plugins.content-manager\": \"Edukien kudeatzailea\",\n    \"global.plugins.content-manager.description\": \"Datu-baseko datuak ikusteko, editatzeko eta ezabatzeko modu azkarra\",\n    \"global.plugins.content-type-builder\": \"Eduki Motaren Eraikitzailea\",\n    \"global.plugins.content-type-builder.description\": \"Modelatu zure APIaren datu-egitura. Sortu eremu eta harreman berriak minutu batean. Fitxategiak automatikoki sortu eta eguneratzen dira zure proiektuan\",\n    \"global.plugins.email\": \"Posta elektronikoa\",\n    \"global.plugins.email.description\": \"Konfiguratu zure aplikazioa mezu elektronikoak bidaltzeko\",\n    \"global.plugins.upload\": \"Mediateka\",\n    \"global.plugins.upload.description\": \"Multimedia fitxategien kudeaketa\",\n    \"global.plugins.graphql\": \"GraphQL\",\n    \"global.plugins.graphql.description\": \"GraphQL amaierako puntua gehitzen du API metodo lehenetsiekin\",\n    \"global.plugins.documentation\": \"Dokumentazioa\",\n    \"global.plugins.documentation.description\": \"Sortu OpenAPI dokumentu bat eta ikusi zure APIa SWAGGER UI-rekin\",\n    \"global.plugins.i18n\": \"Internazionalizazioa\",\n    \"global.plugins.i18n.description\": \"Plugin honek hainbat hizkuntzatako edukiak sortu, irakurri eta eguneratzeko aukera ematen du, bai Admin Paneletik, bai APItik\",\n    \"global.plugins.sentry\": \"Sentry\",\n    \"global.plugins.sentry.description\": \"Bidali Strapi errore-gertaerak Sentry-ri\",\n    \"global.plugins.users-permissions\": \"Rolak eta Baimenak\",\n    \"global.plugins.users-permissions.description\": \"Babestu zure APIa JWTn oinarritutako autentifikazio prozesu oso batekin. Plugin honek erabiltzaile taldeen arteko baimenak kudeatzeko aukera ematen duen ACL estrategia batekin dator\",\n    \"global.profile\": \"Profila\",\n    \"global.prompt.unsaved\": \"Ziur orrialde hau utzi nahi duzula? Zure aldaketa guztiak galduko dira\",\n    \"global.reset-password\": \"Berrezarri pasahitza\",\n    \"global.roles\": \"Rolak\",\n    \"global.save\": \"Gorde\",\n    \"global.see-more\": \"Gehiago ikusi\",\n    \"global.select\": \"Aukeratu\",\n    \"global.select-all-entries\": \"Aukeratu sarrera guztiak\",\n    \"global.settings\": \"Ezarpenak\",\n    \"global.type\": \"Mota\",\n    \"global.users\": \"Erabiltzaileak\",\n    \"notification.contentType.relations.conflict\": \"Eduki motak erlazio kontrajarriak ditu\",\n    \"notification.default.title\": \"Informazioa:\",\n    \"notification.error\": \"Errore bat gertatu da\",\n    \"notification.error.layout\": \"Ezin izan da diseinua berreskuratu\",\n    \"notification.form.error.fields\": \"Formularioak akats batzuk ditu\",\n    \"notification.form.success.fields\": \"Aldaketak gorde dira\",\n    \"notification.link-copied\": \"Esteka arbelean kopiatu da\",\n    \"notification.permission.not-allowed-read\": \"Ez duzu dokumentu hau ikusteko baimenik\",\n    \"notification.success.delete\": \"Elementua ezabatu egin da\",\n    \"notification.success.saved\": \"Gordeta\",\n    \"notification.success.title\": \"Arrakasta:\",\n    \"notification.success.apitokencreated\": \"API Tokena behar bezala sortu da\",\n    \"notification.success.apitokenedited\": \"API Tokena behar bezala editatu da\",\n    \"notification.error.tokennamenotunique\": \"Beste token bati esleitutako izena\",\n    \"notification.version.update.message\": \"Strapi-ren bertsio berri bat dago eskuragarri!\",\n    \"notification.warning.title\": \"Abisua:\",\n    \"notification.warning.404\": \"404 - Ez da aurkitu\",\n    or: or,\n    \"request.error.model.unknown\": \"Eredu hau ez da existitzen\",\n    skipToContent: skipToContent,\n    submit: submit\n};\n\nexport { Analytics, Documentation, Email, Password, Provider, ResetPasswordToken, Role, Username, Users, anErrorOccurred, clearLabel, dark, eu as default, light, or, skipToContent, submit };\n//# sourceMappingURL=eu.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,YAAY;AAChB,IAAI,gBAAgB;AACpB,IAAI,QAAQ;AACZ,IAAI,WAAW;AACf,IAAI,WAAW;AACf,IAAI,qBAAqB;AACzB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,WAAW;AACf,IAAI,QAAQ;AACZ,IAAI,kBAAkB;AACtB,IAAI,aAAa;AACjB,IAAI,KAAK;AACT,IAAI,gBAAgB;AACpB,IAAI,SAAS;AACb,IAAI,KAAK;AAAA,EACL;AAAA,EACA,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0CAA0C;AAAA,EAC1C,iCAAiC;AAAA,EACjC,sCAAsC;AAAA,EACtC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,iDAAiD;AAAA,EACjD,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,2BAA2B;AAAA,EAC3B,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA,EAChB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,6BAA6B;AAAA,EAC7B,qCAAqC;AAAA,EACrC,0CAA0C;AAAA,EAC1C,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,4BAA4B;AAAA,EAC5B,4BAA4B;AAAA,EAC5B,0CAA0C;AAAA,EAC1C,sBAAsB;AAAA,EACtB,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,uCAAuC;AAAA,EACvC,4CAA4C;AAAA,EAC5C,mDAAmD;AAAA,EACnD,4CAA4C;AAAA,EAC5C,iDAAiD;AAAA,EACjD,kDAAkD;AAAA,EAClD,uCAAuC;AAAA,EACvC,4BAA4B;AAAA,EAC5B,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,oCAAoC;AAAA,EACpC,oCAAoC;AAAA,EACpC,sCAAsC;AAAA,EACtC,iCAAiC;AAAA,EACjC,6BAA6B;AAAA,EAC7B,4CAA4C;AAAA,EAC5C,mDAAmD;AAAA,EACnD,yDAAyD;AAAA,EACzD,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,sCAAsC;AAAA,EACtC,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,sCAAsC;AAAA,EACtC,8BAA8B;AAAA,EAC9B,sCAAsC;AAAA,EACtC,qDAAqD;AAAA,EACrD,6DAA6D;AAAA,EAC7D,4DAA4D;AAAA,EAC5D,2DAA2D;AAAA,EAC3D,oDAAoD;AAAA,EACpD,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,sDAAsD;AAAA,EACtD,iEAAiE;AAAA,EACjE,mEAAmE;AAAA,EACnE,gEAAgE;AAAA,EAChE,8DAA8D;AAAA,EAC9D,iEAAiE;AAAA,EACjE,8DAA8D;AAAA,EAC9D,6DAA6D;AAAA,EAC7D,4DAA4D;AAAA,EAC5D,wEAAwE;AAAA,EACxE,wDAAwD;AAAA,EACxD,oDAAoD;AAAA,EACpD,mEAAmE;AAAA,EACnE,0DAA0D;AAAA,EAC1D,6DAA6D;AAAA,EAC7D,2DAA2D;AAAA,EAC3D,+DAA+D;AAAA,EAC/D,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,iDAAiD;AAAA,EACjD,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,yDAAyD;AAAA,EACzD,iDAAiD;AAAA,EACjD,8DAA8D;AAAA,EAC9D,mEAAmE;AAAA,EACnE,kEAAkE;AAAA,EAClE,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,8DAA8D;AAAA,EAC9D;AAAA,EACA;AAAA,EACA,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,oDAAoD;AAAA,EACpD,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,mCAAmC;AAAA,EACnC,iDAAiD;AAAA,EACjD,yDAAyD;AAAA,EACzD,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,wCAAwC;AAAA,EACxC,0CAA0C;AAAA,EAC1C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,6CAA6C;AAAA,EAC7C,yDAAyD;AAAA,EACzD,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,wCAAwC;AAAA,EACxC,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,2CAA2C;AAAA,EAC3C,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,4CAA4C;AAAA,EAC5C,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,2BAA2B;AAAA,EAC3B,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,qBAAqB;AAAA,EACrB,sBAAsB;AAAA,EACtB,2BAA2B;AAAA,EAC3B,gDAAgD;AAAA,EAChD,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,+BAA+B;AAAA,EAC/B,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,mDAAmD;AAAA,EACnD,2CAA2C;AAAA,EAC3C,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,uDAAuD;AAAA,EACvD,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,gDAAgD;AAAA,EAChD,yCAAyC;AAAA,EACzC,oDAAoD;AAAA,EACpD,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,kDAAkD;AAAA,EAClD,kDAAkD;AAAA,EAClD,oDAAoD;AAAA,EACpD,wCAAwC;AAAA,EACxC,+CAA+C;AAAA,EAC/C,mDAAmD;AAAA,EACnD,yDAAyD;AAAA,EACzD,iDAAiD;AAAA,EACjD,2CAA2C;AAAA,EAC3C,0DAA0D;AAAA,EAC1D,oDAAoD;AAAA,EACpD,gDAAgD;AAAA,EAChD,yDAAyD;AAAA,EACzD,iDAAiD;AAAA,EACjD,0DAA0D;AAAA,EAC1D,mDAAmD;AAAA,EACnD,2DAA2D;AAAA,EAC3D,kDAAkD;AAAA,EAClD,0DAA0D;AAAA,EAC1D;AAAA,EACA,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,wCAAwC;AAAA,EACxC,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,4CAA4C;AAAA,EAC5C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,+CAA+C;AAAA,EAC/C,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,+CAA+C;AAAA,EAC/C,sDAAsD;AAAA,EACtD,wDAAwD;AAAA,EACxD,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,yDAAyD;AAAA,EACzD,qDAAqD;AAAA,EACrD,4CAA4C;AAAA,EAC5C,2CAA2C;AAAA,EAC3C,gDAAgD;AAAA,EAChD,4CAA4C;AAAA,EAC5C,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,sCAAsC;AAAA,EACtC,2CAA2C;AAAA,EAC3C,wDAAwD;AAAA,EACxD,sDAAsD;AAAA,EACtD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,2DAA2D;AAAA,EAC3D,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,oCAAoC;AAAA,EACpC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,2CAA2C;AAAA,EAC3C,2BAA2B;AAAA,EAC3B,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,+EAA+E;AAAA,EAC/E,qEAAqE;AAAA,EACrE,yEAAyE;AAAA,EACzE,mEAAmE;AAAA,EACnE,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,4DAA4D;AAAA,EAC5D,0DAA0D;AAAA,EAC1D,sEAAsE;AAAA,EACtE,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,qDAAqD;AAAA,EACrD,wDAAwD;AAAA,EACxD,yDAAyD;AAAA,EACzD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,qCAAqC;AAAA,EACrC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,oEAAoE;AAAA,EACpE,4DAA4D;AAAA,EAC5D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,wDAAwD;AAAA,EACxD,4BAA4B;AAAA,EAC5B,sBAAsB;AAAA,EACtB,+BAA+B;AAAA,EAC/B,yCAAyC;AAAA,EACzC,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,4BAA4B;AAAA,EAC5B,uBAAuB;AAAA,EACvB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,yCAAyC;AAAA,EACzC,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,gCAAgC;AAAA,EAChC,wCAAwC;AAAA,EACxC,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB;AAAA,EACA,eAAe;AAAA,EACf,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,+CAA+C;AAAA,EAC/C,qDAAqD;AAAA,EACrD,sDAAsD;AAAA,EACtD,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,0CAA0C;AAAA,EAC1C,4CAA4C;AAAA,EAC5C,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,gDAAgD;AAAA,EAChD,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,yDAAyD;AAAA,EACzD,+CAA+C;AAAA,EAC/C,gDAAgD;AAAA,EAChD,uDAAuD;AAAA,EACvD,+BAA+B;AAAA,EAC/B,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,yCAAyC;AAAA,EACzC,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,wBAAwB;AAAA,EACxB,oBAAoB;AAAA,EACpB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,wBAAwB;AAAA,EACxB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,sBAAsB;AAAA,EACtB,eAAe;AAAA,EACf,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,kCAAkC;AAAA,EAClC,8CAA8C;AAAA,EAC9C,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,wBAAwB;AAAA,EACxB,oCAAoC;AAAA,EACpC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,uBAAuB;AAAA,EACvB,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,gDAAgD;AAAA,EAChD,kBAAkB;AAAA,EAClB,yBAAyB;AAAA,EACzB,yBAAyB;AAAA,EACzB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,+CAA+C;AAAA,EAC/C,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,4CAA4C;AAAA,EAC5C,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B;AAAA,EACA,+BAA+B;AAAA,EAC/B;AAAA,EACA;AACJ;", "names": []}