{"version": 3, "sources": ["../../../@strapi/upload/dist/admin/translations/fr.json.mjs"], "sourcesContent": ["var fr = {\n    \"apiError.FileTooBig\": \"Le fichier importé dépasse la taille maximale autorisée pour les médias.\",\n    \"upload.generic-error\": \"Un erreur est survenu durant l'importation du fichier.\",\n    \"bulk.select.label\": \"Selectionner tous les médias\",\n    \"button.next\": \"Suivant\",\n    \"checkControl.crop-duplicate\": \"Dupliquer & recadrer le média\",\n    \"checkControl.crop-original\": \"Recadrer le média d'origine\",\n    \"content.isLoading\": \"Chargement du contenu en cours.\",\n    \"control-card.add\": \"Ajouter\",\n    \"control-card.cancel\": \"Annuler\",\n    \"control-card.copy-link\": \"Copier le lien\",\n    \"control-card.crop\": \"Recadrer\",\n    \"control-card.download\": \"Télécharger\",\n    \"control-card.edit\": \"Éditer\",\n    \"control-card.replace-media\": \"Remplacer le média\",\n    \"control-card.save\": \"<PERSON>uvegarder\",\n    \"control-card.stop-crop\": \"Arrêter le recadrage\",\n    \"filter.add\": \"Ajouter un filtre\",\n    \"form.button.replace-media\": \"Remplacer le média\",\n    \"form.input.description.file-alt\": \"Ce texte sera affiché si le média ne peut pas être affiché.\",\n    \"form.input.label.file-alt\": \"Texte alternatif\",\n    \"form.input.label.file-caption\": \"Légende\",\n    \"form.input.label.file-name\": \"Nom du fichier\",\n    \"form.upload-url.error.url.invalid\": \"Une URL n'est pas valide\",\n    \"form.upload-url.error.url.invalids\": \"{number} URLs ne sont pas valides\",\n    \"header.actions.add-assets\": \"Ajouter des médias\",\n    \"header.actions.add-folder\": \"Ajouter un dossier\",\n    \"header.actions.add-assets.folder\": \"dossier\",\n    \"header.actions.upload-assets\": \"Importer des médias\",\n    \"header.actions.upload-new-asset\": \"Importer un média\",\n    \"header.content.assets-empty\": \"Aucun média\",\n    \"header.content.assets\": \"{numberFolders, plural, one {1 dossier} other {# dossiers}} - {numberAssets, plural, one {1 média} other {# médias}}\",\n    \"input.button.label\": \"Parcourir les fichiers\",\n    \"input.label\": \"Glisser & déposer ici ou\",\n    \"input.label-bold\": \"Glissez & déposez\",\n    \"input.label-normal\": \"pour importer ou\",\n    \"input.placeholder\": \"Cliquez pour sélectionner un média ou glissez & déposez un fichier dans cette zone\",\n    \"input.placeholder.icon\": \"Lacher le fichier dans cette zone\",\n    \"input.url.description\": \"Séparez vos URLs par un retour à la ligne.\",\n    \"input.url.label\": \"URL\",\n    \"input.notification.not-supported\": \"Vous ne pouvez pas importer ce type de fichier, seuls ces types de fichiers sont acceptés – {fileTypes}\",\n    \"list.assets.title\": \"Médias ({count})\",\n    \"list.asset.at.finished\": \"Les médias sont chargés.\",\n    \"list.assets-empty.search\": \"No result found\",\n    \"list.assets-empty.subtitle\": \"Ajoutez-en un à la liste.\",\n    \"list.assets-empty.title\": \"Il n'y a pas encore de média\",\n    \"list.assets-empty.title-withSearch\": \"Il n'y a aucun média avec les filtres appliqués\",\n    \"list.assets.empty\": \"La médiathèque est vide\",\n    \"list.assets.empty-upload\": \"Importez vos premiers médias...\",\n    \"list.assets.empty.no-permissions\": \"Aucune permissions à voir\",\n    \"list.assets.loading-asset\": \"Chargement du contenu pour le media {path}\",\n    \"list.assets.not-supported-content\": \"Aperçu non disponible\",\n    \"list.assets.preview-asset\": \"Aperçu de la vidéo {path}\",\n    \"list.assets.selected\": \"{numberFolders, plural, one {1 dossier} other {# dossiers}} - {numberAssets, plural, one {1 média} other {# médias}}\",\n    \"list-assets-select\": \"Selectionner le média {name}\",\n    \"list.assets.type-not-allowed\": \"Ce type de fichier n'est pas autorisé.\",\n    \"list.assets.to-upload\": \"{number, plural, =0 {Aucun média} one {1 média} other {# médias}} à importer\",\n    \"list.folder.edit\": \"Éditer le dossier\",\n    \"list.folder.select\": \"Selectionner le dossier {name}\",\n    \"list.folder.subtitle\": \"{folderCount, plural, =0 {aucun dossier} one {1 dossier} other {# dossiers}}, {filesCount, plural, =0 {aucun média} one {1 média} other {# médias}}\",\n    \"list.folders.title\": \"Dossiers ({count})\",\n    \"list.folders.link-label\": \"Accéder au dossier\",\n    \"mediaLibraryInput.actions.nextSlide\": \"Diapo suivante\",\n    \"mediaLibraryInput.actions.previousSlide\": \"Diapo précédente\",\n    \"mediaLibraryInput.placeholder\": \"Cliquer pour ajouter un média ou glisser-déposer dans cette zone\",\n    \"mediaLibraryInput.slideCount\": \"{n} de {m} diapo\",\n    \"modal.file-details.date\": \"Date\",\n    \"modal.file-details.dimensions\": \"Dimensions\",\n    \"modal.file-details.extension\": \"Extension\",\n    \"modal.file-details.size\": \"Taille\",\n    \"modal.file-details.id\": \"ID du média\",\n    \"modal.folder.elements.count\": \"{folderCount} dossiers, {assetCount} médias\",\n    \"modal.header.browse\": \"Importer des médias\",\n    \"modal.header.file-detail\": \"Détails\",\n    \"modal.header.pending-assets\": \"Médias en attente\",\n    \"modal.header.select-files\": \"Fichiers sélectionnés\",\n    \"modal.header.go-back\": \"Revenir\",\n    \"modal.folder.move.title\": \"Déplacer l'élément vers\",\n    \"modal.nav.browse\": \"parcourir\",\n    \"modal.nav.computer\": \"depuis l'ordinateur\",\n    \"modal.nav.selected\": \"sélectionné(s)\",\n    \"modal.nav.url\": \"depuis une url\",\n    \"modal.remove.success-label\": \"Les éléments sélectionnés ont bien été supprimés.\",\n    \"modal.move.success-label\": \"Les éléments sélectionnés ont bien été déplacés.\",\n    \"modal.selected-list.sub-header-subtitle\": \"Glissez-déposez pour réorganiser les médias dans le champ\",\n    \"modal.upload-list.footer.button\": \"Importer {number, plural, one {1 média} other {# médias}} dans la médiathèque\",\n    \"modal.upload-list.sub-header-subtitle\": \"Gérez les médias avant de les ajouter à la médiathèque\",\n    \"modal.upload-list.sub-header.button\": \"Ajouter plus de médias\",\n    \"modal.upload.cancelled\": \"Importation interrompue manuellement.\",\n    \"page.title\": \"Paramètres - Médiathèque\",\n    \"permissions.not-allowed.update\": \"Vous n'êtes pas autorisé à éditer ce fichier.\",\n    \"plugin.description.long\": \"Gestion des fichiers multimédias.\",\n    \"plugin.description.short\": \"Gestion des fichiers multimédias.\",\n    \"plugin.name\": \"Médiathèque\",\n    \"search.clear.label\": \"Effacer\",\n    \"search.label\": \"Rechercher un média\",\n    \"search.placeholder\": \"Rechercher un média...\",\n    \"settings.blockTitle\": \"Gestion des fichiers\",\n    \"settings.form.autoOrientation.description\": \"Faire pivoter automatiquement l'image selon les EXIF d'orientation\",\n    \"settings.form.autoOrientation.label\": \"Activer l'orientation automatique\",\n    \"settings.form.responsiveDimensions.description\": \"Génère automatiquement plusieurs formats (grand, moyen, petit) du média importé\",\n    \"settings.form.responsiveDimensions.label\": \"Activer le téléchargement \\\"responsive friendly\\\"\",\n    \"settings.form.sizeOptimization.description\": \"Activer cette option réduira la taille des images en baissant légèrement leur qualité\",\n    \"settings.form.sizeOptimization.label\": \"Optimisation de la taille\",\n    \"settings.form.videoPreview.description\": \"Génère un aperçu de six secondes de la vidéo (GIF)\",\n    \"settings.form.videoPreview.label\": \"Aperçu\",\n    \"settings.header.label\": \"Médiathèque - Réglages\",\n    \"settings.section.doc.label\": \"DOC\",\n    \"settings.section.image.label\": \"IMAGE\",\n    \"settings.section.video.label\": \"VIDÉO\",\n    \"settings.sub-header.label\": \"Configurer les paramètres de la médiathèque\",\n    \"sort.created_at_asc\": \"Importations les plus anciennes\",\n    \"sort.created_at_desc\": \"Importations les plus récentes\",\n    \"sort.label\": \"Trier par\",\n    \"sort.name_asc\": \"Ordre alphabétique (A à Z)\",\n    \"sort.name_desc\": \"Ordre alphabétique inversé (Z à A)\",\n    \"sort.updated_at_asc\": \"Modifications les plus anciennes\",\n    \"sort.updated_at_desc\": \"Modifications les plus récentes\",\n    \"list.table.header.actions\": \"actions\",\n    \"list.table.header.preview\": \"aperçu\",\n    \"list.table.header.name\": \"nom\",\n    \"list.table.header.ext\": \"extension\",\n    \"list.table.header.size\": \"taille\",\n    \"list.table.header.createdAt\": \"créé le\",\n    \"list.table.header.updatedAt\": \"modifé le\",\n    \"list.table.header.sort\": \"Trier par {label}\",\n    \"list.table.content.empty-label\": \"Ce champ est vide\",\n    \"tabs.title\": \"Comment souhaitez-vous importer vos médias ?\",\n    \"window.confirm.close-modal.file\": \"Êtes-vous sûr ? Vos modifications seront perdues.\",\n    \"window.confirm.close-modal.files\": \"Êtes-vous sûr ? Certains fichiers n'ont pas encore été téléchargés.\",\n    \"config.back\": \"Retour\",\n    \"config.subtitle\": \"Définissez les paramètres d'affichage de la médiathèque.\",\n    \"config.entries.title\": \"Entrées par page\",\n    \"config.sort.title\": \"Tri par défaut\",\n    \"config.entries.note\": \"Nombre de médias affichés par défaut dans la médiathèque\",\n    \"config.note\": \"Remarque : Vous pouvez redéfinir cette valeur dans la médiathèque.\",\n    \"config.popUpWarning.warning.updateAllSettings\": \"Ceci changera tous vos paramètres\",\n    \"view-switch.list\": \"Vue en liste\",\n    \"view-switch.grid\": \"Vue en grille\"\n};\n\nexport { fr as default };\n//# sourceMappingURL=fr.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,KAAK;AAAA,EACL,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,qBAAqB;AAAA,EACrB,eAAe;AAAA,EACf,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,qBAAqB;AAAA,EACrB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,qBAAqB;AAAA,EACrB,8BAA8B;AAAA,EAC9B,qBAAqB;AAAA,EACrB,0BAA0B;AAAA,EAC1B,cAAc;AAAA,EACd,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,gCAAgC;AAAA,EAChC,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,qBAAqB;AAAA,EACrB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,mBAAmB;AAAA,EACnB,oCAAoC;AAAA,EACpC,qBAAqB;AAAA,EACrB,0BAA0B;AAAA,EAC1B,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,sCAAsC;AAAA,EACtC,qBAAqB;AAAA,EACrB,4BAA4B;AAAA,EAC5B,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,qCAAqC;AAAA,EACrC,6BAA6B;AAAA,EAC7B,wBAAwB;AAAA,EACxB,sBAAsB;AAAA,EACtB,gCAAgC;AAAA,EAChC,yBAAyB;AAAA,EACzB,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,wBAAwB;AAAA,EACxB,sBAAsB;AAAA,EACtB,2BAA2B;AAAA,EAC3B,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,iCAAiC;AAAA,EACjC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,6BAA6B;AAAA,EAC7B,wBAAwB;AAAA,EACxB,2BAA2B;AAAA,EAC3B,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,iBAAiB;AAAA,EACjB,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,2CAA2C;AAAA,EAC3C,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,0BAA0B;AAAA,EAC1B,cAAc;AAAA,EACd,kCAAkC;AAAA,EAClC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,eAAe;AAAA,EACf,sBAAsB;AAAA,EACtB,gBAAgB;AAAA,EAChB,sBAAsB;AAAA,EACtB,uBAAuB;AAAA,EACvB,6CAA6C;AAAA,EAC7C,uCAAuC;AAAA,EACvC,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,8CAA8C;AAAA,EAC9C,wCAAwC;AAAA,EACxC,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,yBAAyB;AAAA,EACzB,8BAA8B;AAAA,EAC9B,gCAAgC;AAAA,EAChC,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,cAAc;AAAA,EACd,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,+BAA+B;AAAA,EAC/B,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,kCAAkC;AAAA,EAClC,cAAc;AAAA,EACd,mCAAmC;AAAA,EACnC,oCAAoC;AAAA,EACpC,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,eAAe;AAAA,EACf,iDAAiD;AAAA,EACjD,oBAAoB;AAAA,EACpB,oBAAoB;AACxB;", "names": []}