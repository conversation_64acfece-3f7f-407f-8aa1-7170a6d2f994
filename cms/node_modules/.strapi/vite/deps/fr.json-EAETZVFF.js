import "./chunk-PLDDJCW6.js";

// node_modules/@strapi/plugin-users-permissions/dist/admin/translations/fr.json.mjs
var fr = {
  "BoundRoute.title": "Route associée à",
  "EditForm.inputSelect.description.role": "Choisissez le rôle qui sera lié aux utilisateurs lors de leur enregistrement.",
  "EditForm.inputSelect.label.role": "Rôle par defaut pour les nouveaux utilisateurs",
  "EditForm.inputToggle.description.email": "Interdire l'utilisateur de créer de multiple comptes avec la même adresse e-mail avec des providers différents",
  "EditForm.inputToggle.description.email-confirmation": "Quand cette option est activée (ON), les nouveaux utilisateurs enregistrés reçoivent un e-mail de confirmation.",
  "EditForm.inputToggle.description.email-confirmation-redirection": "Après confirmation de votre e-mail, choisissez où vous allez être redirigé.",
  "EditForm.inputToggle.description.email-reset-password": "URL de la page de réinitialisation de mot de passe.",
  "EditForm.inputToggle.description.sign-up": "Quand l'inscription est désactivée (OFF), aucun utilisateur ne peut s'inscrire qu'importe le provider",
  "EditForm.inputToggle.label.email": "Un compte par adresse e-mail",
  "EditForm.inputToggle.label.email-confirmation": "Activer l'e-mail de confirmation",
  "EditForm.inputToggle.label.email-confirmation-redirection": "Redirection de l'URL",
  "EditForm.inputToggle.label.email-reset-password": "Page de réinitialisation de mot de passe",
  "EditForm.inputToggle.label.sign-up": "Activer l'inscription",
  "HeaderNav.link.advancedSettings": "Paramètres avancés",
  "HeaderNav.link.emailTemplates": "Templates d'e-mail",
  "HeaderNav.link.providers": "Fournisseurs",
  "Plugin.permissions.plugins.description": "Définissez les actions autorisées dans le {name} plugin.",
  "Plugins.header.description": "Sont listés uniquement les actions associées à une route.",
  "Plugins.header.title": "Permissions",
  "Policies.header.hint": "Sélectionnez les actions de l'application ou d'un plugin et cliquer sur l'icon de paramètres pour voir les routes associées à cette action",
  "Policies.header.title": "Paramètres avancés",
  "PopUpForm.Email.email_templates.inputDescription": "Regardez la documentation des variables, {link}",
  "PopUpForm.Email.options.from.email.label": "E-mail de l'envoyeur",
  "PopUpForm.Email.options.from.email.placeholder": "<EMAIL>",
  "PopUpForm.Email.options.from.name.label": "Nom de l'envoyeur",
  "PopUpForm.Email.options.from.name.placeholder": "Arthur Dupont",
  "PopUpForm.Email.options.message.label": "Message",
  "PopUpForm.Email.options.object.label": "Objet",
  "PopUpForm.Email.options.response_email.label": "E-mail de réponse",
  "PopUpForm.Email.options.response_email.placeholder": "<EMAIL>",
  "PopUpForm.Providers.enabled.description": "S'il est désactivé les utilisateurs ne pourront pas utiliser ce provider.",
  "PopUpForm.Providers.enabled.label": "Activer",
  "PopUpForm.Providers.key.label": "Client ID",
  "PopUpForm.Providers.key.placeholder": "TEXT",
  "PopUpForm.Providers.redirectURL.front-end.label": "L'URL de redirection de votre app front-end",
  "PopUpForm.Providers.secret.label": "Client Secret",
  "PopUpForm.Providers.secret.placeholder": "TEXT",
  "PopUpForm.Providers.subdomain.label": "Host URI (Subdomain)",
  "PopUpForm.Providers.subdomain.placeholder": "my.subdomain.com",
  "PopUpForm.header.edit.email-templates": "Editer E-mail Templates",
  "notification.success.submit": "Les configurations ont bien été sauvegardés",
  "plugin.description.long": "Protégez votre API avec un système d'authentification complet basé sur JWT (JSON Web Token). Ce plugin ajoute aussi une stratégie ACL (Access Control Layer) qui vous permet de gérer les permissions entre les groupes d'utilisateurs.",
  "plugin.description.short": "Protégez votre API avec un système d'authentification complet basé sur JWT.",
  "plugin.name": "Rôles et autorisations"
};
export {
  fr as default
};
//# sourceMappingURL=fr.json-EAETZVFF.js.map
