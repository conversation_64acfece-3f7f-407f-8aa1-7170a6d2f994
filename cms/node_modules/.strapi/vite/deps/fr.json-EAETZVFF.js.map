{"version": 3, "sources": ["../../../@strapi/plugin-users-permissions/dist/admin/translations/fr.json.mjs"], "sourcesContent": ["var fr = {\n    \"BoundRoute.title\": \"Route associée à\",\n    \"EditForm.inputSelect.description.role\": \"Choi<PERSON><PERSON><PERSON> le rôle qui sera lié aux utilisateurs lors de leur enregistrement.\",\n    \"EditForm.inputSelect.label.role\": \"Rôle par defaut pour les nouveaux utilisateurs\",\n    \"EditForm.inputToggle.description.email\": \"Interdire l'utilisateur de créer de multiple comptes avec la même adresse e-mail avec des providers différents\",\n    \"EditForm.inputToggle.description.email-confirmation\": \"Quand cette option est activée (ON), les nouveaux utilisateurs enregistrés reçoivent un e-mail de confirmation.\",\n    \"EditForm.inputToggle.description.email-confirmation-redirection\": \"Après confirmation de votre e-mail, choisissez où vous allez être redirigé.\",\n    \"EditForm.inputToggle.description.email-reset-password\": \"URL de la page de réinitialisation de mot de passe.\",\n    \"EditForm.inputToggle.description.sign-up\": \"Quand l'inscription est désactivée (OFF), aucun utilisateur ne peut s'inscrire qu'importe le provider\",\n    \"EditForm.inputToggle.label.email\": \"Un compte par adresse e-mail\",\n    \"EditForm.inputToggle.label.email-confirmation\": \"Activer l'e-mail de confirmation\",\n    \"EditForm.inputToggle.label.email-confirmation-redirection\": \"Redirection de l'URL\",\n    \"EditForm.inputToggle.label.email-reset-password\": \"Page de réinitialisation de mot de passe\",\n    \"EditForm.inputToggle.label.sign-up\": \"Activer l'inscription\",\n    \"HeaderNav.link.advancedSettings\": \"Paramètres avancés\",\n    \"HeaderNav.link.emailTemplates\": \"Templates d'e-mail\",\n    \"HeaderNav.link.providers\": \"Fournisseurs\",\n    \"Plugin.permissions.plugins.description\": \"Définissez les actions autorisées dans le {name} plugin.\",\n    \"Plugins.header.description\": \"Sont listés uniquement les actions associées à une route.\",\n    \"Plugins.header.title\": \"Permissions\",\n    \"Policies.header.hint\": \"Sélectionnez les actions de l'application ou d'un plugin et cliquer sur l'icon de paramètres pour voir les routes associées à cette action\",\n    \"Policies.header.title\": \"Paramètres avancés\",\n    \"PopUpForm.Email.email_templates.inputDescription\": \"Regardez la documentation des variables, {link}\",\n    \"PopUpForm.Email.options.from.email.label\": \"E-mail de l'envoyeur\",\n    \"PopUpForm.Email.options.from.email.placeholder\": \"<EMAIL>\",\n    \"PopUpForm.Email.options.from.name.label\": \"Nom de l'envoyeur\",\n    \"PopUpForm.Email.options.from.name.placeholder\": \"Arthur Dupont\",\n    \"PopUpForm.Email.options.message.label\": \"Message\",\n    \"PopUpForm.Email.options.object.label\": \"Objet\",\n    \"PopUpForm.Email.options.response_email.label\": \"E-mail de réponse\",\n    \"PopUpForm.Email.options.response_email.placeholder\": \"<EMAIL>\",\n    \"PopUpForm.Providers.enabled.description\": \"S'il est désactivé les utilisateurs ne pourront pas utiliser ce provider.\",\n    \"PopUpForm.Providers.enabled.label\": \"Activer\",\n    \"PopUpForm.Providers.key.label\": \"Client ID\",\n    \"PopUpForm.Providers.key.placeholder\": \"TEXT\",\n    \"PopUpForm.Providers.redirectURL.front-end.label\": \"L'URL de redirection de votre app front-end\",\n    \"PopUpForm.Providers.secret.label\": \"Client Secret\",\n    \"PopUpForm.Providers.secret.placeholder\": \"TEXT\",\n    \"PopUpForm.Providers.subdomain.label\": \"Host URI (Subdomain)\",\n    \"PopUpForm.Providers.subdomain.placeholder\": \"my.subdomain.com\",\n    \"PopUpForm.header.edit.email-templates\": \"Editer E-mail Templates\",\n    \"notification.success.submit\": \"Les configurations ont bien été sauvegardés\",\n    \"plugin.description.long\": \"Protégez votre API avec un système d'authentification complet basé sur JWT (JSON Web Token). Ce plugin ajoute aussi une stratégie ACL (Access Control Layer) qui vous permet de gérer les permissions entre les groupes d'utilisateurs.\",\n    \"plugin.description.short\": \"Protégez votre API avec un système d'authentification complet basé sur JWT.\",\n    \"plugin.name\": \"Rôles et autorisations\"\n};\n\nexport { fr as default };\n//# sourceMappingURL=fr.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,KAAK;AAAA,EACL,oBAAoB;AAAA,EACpB,yCAAyC;AAAA,EACzC,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,uDAAuD;AAAA,EACvD,mEAAmE;AAAA,EACnE,yDAAyD;AAAA,EACzD,4CAA4C;AAAA,EAC5C,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,6DAA6D;AAAA,EAC7D,mDAAmD;AAAA,EACnD,sCAAsC;AAAA,EACtC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,0CAA0C;AAAA,EAC1C,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,oDAAoD;AAAA,EACpD,4CAA4C;AAAA,EAC5C,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,iDAAiD;AAAA,EACjD,yCAAyC;AAAA,EACzC,wCAAwC;AAAA,EACxC,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,iCAAiC;AAAA,EACjC,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,oCAAoC;AAAA,EACpC,0CAA0C;AAAA,EAC1C,uCAAuC;AAAA,EACvC,6CAA6C;AAAA,EAC7C,yCAAyC;AAAA,EACzC,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,eAAe;AACnB;", "names": []}