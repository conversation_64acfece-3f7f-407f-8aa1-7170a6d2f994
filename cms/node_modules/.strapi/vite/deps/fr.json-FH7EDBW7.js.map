{"version": 3, "sources": ["../../../@strapi/plugin-cloud/dist/admin/translations/fr.json.mjs"], "sourcesContent": ["var fr = {\n    \"Plugin.name\": \"Déploiement\",\n    \"Homepage.title\": \"Hébergement cloud entièrement géré pour votre projet Strapi\",\n    \"Homepage.subTitle\": \"Suivez ce processus en 2 étapes pour obtenir tout ce dont vous avez besoin pour exécuter Strapi en production.\",\n    \"Homepage.githubBox.title.versioned\": \"Projet uploadé sur GitHub\",\n    \"Homepage.githubBox.title.not-versioned\": \"Upload ton projet sur GitHub\",\n    \"Homepage.githubBox.subTitle.versioned\": \"Bien joué ! Il ne manque plus qu'une étape pour déployer ton projet sur Strapi Cloud\",\n    \"Homepage.githubBox.subTitle.not-versioned\": \"Ton projet doit être versionné sur GitHub avant d'être déployé sur Strapi Cloud.\",\n    \"Homepage.githubBox.buttonText\": \"Upload sur GitHub\",\n    \"Homepage.cloudBox.title\": \"Deploie sur Strapi Cloud\",\n    \"Homepage.cloudBox.subTitle\": \"Profitez d'une stack opptimisé pour Strapi comprenant une base de données, un email provider et un CDN.\",\n    \"Homepage.cloudBox.buttonText\": \"Deploie sur Strapi Cloud\",\n    \"Homepage.textBox.label.versioned\": \"Essaies Strapi Cloud gratuitement !\",\n    \"Homepage.textBox.label.not-versioned\": \"Pourquoi uploader mon projet sur GitHub ?\",\n    \"Homepage.textBox.text.versioned\": \"Strapi Cloud propose un essai gratuit de 14 jours pour te permettre d'expérimenter ton projet sur le cloud !\",\n    \"Homepage.textBox.text.not-versioned\": \"Strapi Cloud récupérera et déploiera ton projet à partir de ton repo GitHub. C’est la meilleure façon de versionner, gérer et déployer votre projet. Suivez les étapes sur GitHub pour l'uploader avec succès\"\n};\n\nexport { fr as default };\n//# sourceMappingURL=fr.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,KAAK;AAAA,EACL,eAAe;AAAA,EACf,kBAAkB;AAAA,EAClB,qBAAqB;AAAA,EACrB,sCAAsC;AAAA,EACtC,0CAA0C;AAAA,EAC1C,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,iCAAiC;AAAA,EACjC,2BAA2B;AAAA,EAC3B,8BAA8B;AAAA,EAC9B,gCAAgC;AAAA,EAChC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,mCAAmC;AAAA,EACnC,uCAAuC;AAC3C;", "names": []}