{"version": 3, "sources": ["../../../@strapi/admin/dist/admin/admin/src/translations/fr.json.mjs"], "sourcesContent": ["var Analytics = \"Statistiques\";\nvar Documentation = \"Documentation\";\nvar Email = \"E-mail\";\nvar Password = \"Mot de passe\";\nvar Provider = \"Provider\";\nvar ResetPasswordToken = \"ResetPasswordToken\";\nvar Role = \"Rôle\";\nvar Username = \"Nom d'utilisateur\";\nvar Users = \"Utilisateurs\";\nvar anErrorOccurred = \"Oups ! Une erreur s'est produite. Veuillez réessayer.\";\nvar noPreview = \"Aucun aperçu disponible\";\nvar clearLabel = \"Vider\";\nvar dark = \"Sombre\";\nvar light = \"Clair\";\nvar or = \"OU\";\nvar selectButtonTitle = \"Sélectionner\";\nvar skipToContent = \"Aller au contenu\";\nvar submit = \"Soumettre\";\nvar fr = {\n    Analytics: Analytics,\n    \"Auth.components.Oops.text\": \"Votre compte a été suspendu.\",\n    \"Auth.components.Oops.text.admin\": \"Si c'est une erreur, veuillez contacter votre administrateur.\",\n    \"Auth.components.Oops.title\": \"Oups !\",\n    \"Auth.form.active.label\": \"Actif\",\n    \"Auth.form.button.forgot-password\": \"Envoyer à nouveau\",\n    \"Auth.form.button.go-home\": \"Retour à l'accueil\",\n    \"Auth.form.button.login\": \"Se connecter\",\n    \"Auth.form.button.login.providers.error\": \"Nous ne pouvons pas vous connecter via le fournisseur sélectionné\",\n    \"Auth.form.button.login.strapi\": \"Se connecter avec Strapi\",\n    \"Auth.form.button.password-recovery\": \"Récupération de mot de passe\",\n    \"Auth.form.button.register\": \"Prêt à commencer\",\n    \"Auth.form.confirmPassword.label\": \"Confirmation du mot de passe\",\n    \"Auth.form.currentPassword.label\": \"Mot de passe actuel\",\n    \"Auth.form.email.label\": \"Email\",\n    \"Auth.form.email.placeholder\": \"<EMAIL>\",\n    \"Auth.form.error.blocked\": \"Votre compte a été bloqué par l'administrateur.\",\n    \"Auth.form.error.code.provide\": \"Le code est incorrect.\",\n    \"Auth.form.error.confirmed\": \"L'e-mail de votre compte n'est pas confirmé.\",\n    \"Auth.form.error.email.invalid\": \"Cette e-mail n'est pas valide.\",\n    \"Auth.form.error.email.provide\": \"Votre identifiant est manquant.\",\n    \"Auth.form.error.email.taken\": \"Cet e-mail est déjà utilisé\",\n    \"Auth.form.error.invalid\": \"Votre identifiant ou mot de passe est incorrect.\",\n    \"Auth.form.error.params.provide\": \"Les informations sont incorrectes.\",\n    \"Auth.form.error.password.format\": \"Votre mot de passe ne peut pas contenir trois fois le symbole `$`.\",\n    \"Auth.form.error.password.local\": \"Ce compte n'a pas de mot de passe.\",\n    \"Auth.form.error.password.matching\": \"Les mots de passe ne sont pas identique.\",\n    \"Auth.form.error.password.provide\": \"Votre mot de passe est manquant.\",\n    \"Auth.form.error.ratelimit\": \"Trop de tentatives, veuillez réessayer dans une minute.\",\n    \"Auth.form.error.user.not-exist\": \"Cette e-mail n'existe pas.\",\n    \"Auth.form.error.username.taken\": \"Ce nom est déjà utilisé\",\n    \"Auth.form.firstname.label\": \"Prénom\",\n    \"Auth.form.firstname.placeholder\": \"John\",\n    \"Auth.form.forgot-password.email.label\": \"Entrez votre e-mail\",\n    \"Auth.form.forgot-password.email.label.success\": \"E-mail envoyé avec succès à l'adresse suivante\",\n    \"Auth.form.lastname.label\": \"Nom\",\n    \"Auth.form.lastname.placeholder\": \"Doe\",\n    \"Auth.form.password.hide-password\": \"Cacher le mot de passe\",\n    \"Auth.form.password.hint\": \"Le mot de passe doit contenir au moins 8 caractères, 1 majuscule, 1 minuscule et 1 chiffre.\",\n    \"Auth.form.password.show-password\": \"Afficher le mot de passe\",\n    \"Auth.form.register.news.label\": \"Me tenir au courant des nouvelles fonctionnalités et améliorations à venir (en faisant cela vous acceptez les {terms} et {policy}).\",\n    \"Auth.form.register.subtitle\": \"Vos identifiants sont utilisé uniquement pour vous authentifier sur l'interface d'administration. Toutes les données sauvegardées seront stockées dans votre propre base de données.\",\n    \"Auth.form.rememberMe.label\": \"Se souvenir de moi\",\n    \"Auth.form.username.label\": \"Nom d'utilisateur\",\n    \"Auth.form.username.placeholder\": \"Kai Doe\",\n    \"Auth.form.welcome.subtitle\": \"Connectez-vous à votre compte Strapi\",\n    \"Auth.form.welcome.title\": \"Bienvenue !\",\n    \"Auth.link.forgot-password\": \"Mot de passe oublié ?\",\n    \"Auth.link.ready\": \"Prêt à vous connecter ?\",\n    \"Auth.link.signin\": \"Connexion\",\n    \"Auth.link.signin.account\": \"Vous avez déjà un compte ?\",\n    \"Auth.login.sso.divider\": \"Ou connectez-vous avec\",\n    \"Auth.login.sso.loading\": \"Chargement des fournisseurs\",\n    \"Auth.login.sso.subtitle\": \"Vous connecter via SSO\",\n    \"Auth.privacy-policy-agreement.policy\": \"la politique de confidentialité\",\n    \"Auth.privacy-policy-agreement.terms\": \"termes\",\n    \"Auth.reset-password.title\": \"Réinitialiser le mot de passe\",\n    \"Content Manager\": \"Content Manager\",\n    \"Content Type Builder\": \"Content Types Builder\",\n    Documentation: Documentation,\n    Email: Email,\n    \"Files Upload\": \"Téléversement de fichiers\",\n    \"HomePage.head.title\": \"Accueil\",\n    \"HomePage.roadmap\": \"Voir la roadmap\",\n    \"HomePage.welcome.congrats\": \"Bravo !\",\n    \"HomePage.welcome.congrats.content\": \"Vous êtes connecté en tant que premier Administrateur. Afin de découvrir les fonctionnalités proposées par Strapi,\",\n    \"HomePage.welcome.congrats.content.bold\": \"nous vous conseillons de créer votre première Collection.\",\n    \"Media Library\": \"Mediathèque\",\n    \"New entry\": \"Nouvelle entrée\",\n    Password: Password,\n    Provider: Provider,\n    ResetPasswordToken: ResetPasswordToken,\n    Role: Role,\n    \"Roles & Permissions\": \"Rôles & Permissions\",\n    \"Roles.ListPage.notification.delete-all-not-allowed\": \"Certains rôles n'ont pas pu être supprimés car ils sont associés à des utilisateurs.\",\n    \"Roles.ListPage.notification.delete-not-allowed\": \"Un rôle ne peu pas être supprimé s'il est associé à des utilisateurs.\",\n    \"Roles.RoleRow.select-all\": \"Sélectionner {name} pour action groupée\",\n    \"Roles.RoleRow.user-count\": \"{number, plural, =0 {# utilisateur} one {# utilisateur} other {# utilisateurs}}\",\n    \"Roles.components.List.empty.withSearch\": \"Il n'y a pas de rôles correspondant à la recherche ({search})...\",\n    \"Settings.PageTitle\": \"Réglages - {name}\",\n    \"Settings.apiTokens.ListView.headers.createdAt\": \"Créé le\",\n    \"Settings.apiTokens.ListView.headers.description\": \"Description\",\n    \"Settings.apiTokens.ListView.headers.lastUsedAt\": \"Dernière utilisation le\",\n    \"Settings.apiTokens.ListView.headers.name\": \"Nom\",\n    \"Settings.apiTokens.ListView.headers.type\": \"Type de jeton\",\n    \"Settings.apiTokens.regenerate\": \"Régénérer\",\n    \"Settings.apiTokens.createPage.title\": \"Créer un jeton d'API\",\n    \"Settings.transferTokens.createPage.title\": \"Créer un jeton de transfert\",\n    \"Settings.tokens.RegenerateDialog.title\": \"Régénérer le jeton\",\n    \"Settings.apiTokens.addFirstToken\": \"Ajouter votre premier jeton d'API\",\n    \"Settings.apiTokens.addNewToken\": \"Ajouter un nouveau jeton d'API\",\n    \"Settings.tokens.copy.editMessage\": \"Pour des raisons de sécurité, vous ne pouvoir voir votre jeton qu'une seule fois\",\n    \"Settings.tokens.copy.editTitle\": \"Ce jeton n'est désormais plus accessible\",\n    \"Settings.tokens.copy.lastWarning\": \"Assurez-vous de copier ce jeton, vous ne pourrez plus le revoir par la suite !\",\n    \"Settings.apiTokens.create\": \"Ajouter une entrée\",\n    \"Settings.apiTokens.createPage.permissions.description\": \"Seules les actions rattachées à une route sont listées ci-dessous.\",\n    \"Settings.apiTokens.createPage.permissions.title\": \"Permissions\",\n    \"Settings.apiTokens.description\": \"Liste des jetons générés pour consommer l'API\",\n    \"Settings.apiTokens.createPage.BoundRoute.title\": \"Route rattachée à\",\n    \"Settings.apiTokens.createPage.permissions.header.title\": \"Paramètres avancés\",\n    \"Settings.apiTokens.createPage.permissions.header.hint\": \"Sélectionner les actions de l'application ou du plugin et sur l'icône de la roue crantée pour afficher la route rattachée\",\n    \"Settings.apiTokens.lastHour\": \"dernière heure\",\n    \"Settings.tokens.duration.30-days\": \"30 jours\",\n    \"Settings.tokens.duration.7-days\": \"7 jours\",\n    \"Settings.tokens.duration.90-days\": \"90 jours\",\n    \"Settings.tokens.duration.expiration-date\": \"Date d'expiration\",\n    \"Settings.tokens.duration.unlimited\": \"Illimité\",\n    \"Settings.apiTokens.emptyStateLayout\": \"Vous n'avez pas encore de contenu...\",\n    \"Settings.tokens.form.duration\": \"Durée de vie du jeton\",\n    \"Settings.tokens.form.type\": \"Type de jeton\",\n    \"Settings.tokens.form.name\": \"Nom\",\n    \"Settings.tokens.form.description\": \"Description\",\n    \"Settings.tokens.notification.copied\": \"Jeton copié dans le press-papiers.\",\n    \"Settings.tokens.popUpWarning.message\": \"Êtes-vous sûr(e) de vouloir régénérer ce jeton ?\",\n    \"Settings.tokens.Button.cancel\": \"Annuler\",\n    \"Settings.tokens.Button.regenerate\": \"Régénérer\",\n    \"Settings.tokens.types.full-access\": \"Accès total\",\n    \"Settings.tokens.types.read-only\": \"Lecture seule\",\n    \"Settings.tokens.types.custom\": \"Custom\",\n    \"Settings.tokens.regenerate\": \"Régénérer\",\n    \"Settings.transferTokens.title\": \"Jetons de transfert\",\n    \"Settings.transferTokens.description\": \"Liste des jetons de transfert générés\",\n    \"Settings.transferTokens.create\": \"Créer un nouveau jeton de transfert\",\n    \"Settings.transferTokens.addFirstToken\": \"Ajouter votre premier jeton de transfert\",\n    \"Settings.transferTokens.addNewToken\": \"Ajouter un nouveau jeton de transfert\",\n    \"Settings.transferTokens.emptyStateLayout\": \"Vous n'avez aucun contenu pour le moment...\",\n    \"Settings.tokens.ListView.headers.name\": \"Nom\",\n    \"Settings.tokens.ListView.headers.description\": \"Description\",\n    \"Settings.transferTokens.ListView.headers.type\": \"Type de jeton\",\n    \"Settings.tokens.ListView.headers.createdAt\": \"Créé le\",\n    \"Settings.tokens.ListView.headers.lastUsedAt\": \"Dernière utilisation\",\n    \"Settings.application.ee.admin-seats.count\": \"<text>{enforcementUserCount}</text>/{permittedSeats}\",\n    \"Settings.application.ee.admin-seats.at-limit-tooltip\": \"Limite atteinte : ajouter des places pour inviter d'autres utilisateurs\",\n    \"Settings.application.ee.admin-seats.add-seats\": \"Gérer les places\",\n    \"Settings.application.ee.admin-seats.support\": \"Contacter le service clients\",\n    \"Settings.application.customization\": \"Customisation\",\n    \"Settings.application.customization.auth-logo.carousel-hint\": \"Remplacer le logo dans la page de connexion\",\n    \"Settings.application.customization.carousel-hint\": \"Changer le logo dans l'interface d'administration (dimensions maximales: {dimension}x{dimension}, poids maximal du fichier : {size}KB)\",\n    \"Settings.application.customization.carousel-slide.label\": \"Logo slide\",\n    \"Settings.application.customization.carousel.auth-logo.title\": \"Logo de connexion\",\n    \"Settings.application.customization.carousel.change-action\": \"Changer le logo\",\n    \"Settings.application.customization.carousel.menu-logo.title\": \"Logo du menu\",\n    \"Settings.application.customization.carousel.reset-action\": \"Réinitialiser le logo\",\n    \"Settings.application.customization.carousel.title\": \"Logo\",\n    \"Settings.application.customization.menu-logo.carousel-hint\": \"Remplacer le logo dans la navigation principale\",\n    \"Settings.application.customization.modal.cancel\": \"Annuler\",\n    \"Settings.application.customization.modal.pending\": \"Téléchargement du logo\",\n    \"Settings.application.customization.modal.pending.card-badge\": \"image\",\n    \"Settings.application.customization.modal.pending.choose-another\": \"Choisir un autre logo\",\n    \"Settings.application.customization.modal.pending.subtitle\": \"Gérer le logo choisi avant de le télécharger\",\n    \"Settings.application.customization.modal.pending.title\": \"Logo prêt pour le téléchargement\",\n    \"Settings.application.customization.modal.pending.upload\": \"Téléchargement du logo\",\n    \"Settings.application.customization.modal.tab.label\": \"Comment voulez-vous télécharger vos medias ?\",\n    \"Settings.application.customization.modal.upload\": \"Télécharger le logo\",\n    \"Settings.application.customization.modal.upload.cta.browse\": \"Explorer les fichiers\",\n    \"Settings.application.customization.modal.upload.drag-drop\": \"Glisser-déposer ici ou\",\n    \"Settings.application.customization.modal.upload.error-format\": \"Mauvais format chargé (formats acceptés : jpeg, jpg, png, svg).\",\n    \"Settings.application.customization.modal.upload.error-network\": \"Erreur réseau\",\n    \"Settings.application.customization.modal.upload.error-size\": \"Le fichier téléchargé est trop grand (dimensions max : {dimension}x{dimension}, poids max: {size}KB)\",\n    \"Settings.application.customization.modal.upload.file-validation\": \"Dimensions maximales : {dimension}x{dimension}, poids maximal : {size}KB\",\n    \"Settings.application.customization.modal.upload.from-computer\": \"Depuis l'ordinateur\",\n    \"Settings.application.customization.modal.upload.from-url\": \"Depuis une URL\",\n    \"Settings.application.customization.modal.upload.from-url.input-label\": \"URL\",\n    \"Settings.application.customization.modal.upload.next\": \"Suivant\",\n    \"Settings.application.customization.size-details\": \"Dimensions maximales : {dimension}×{dimension}, poids maximal : {size}KB\",\n    \"Settings.application.description\": \"Informations globales du panneau d'administration\",\n    \"Settings.application.edition-title\": \"édition actuel\",\n    \"Settings.application.ee-or-ce\": \"{communityEdition, select, true {Édition Communauté} other {Édition Entreprise}}\",\n    \"Settings.application.get-help\": \"Obtenir de l'aide\",\n    \"Settings.application.link-pricing\": \"Voir tous les tarifs\",\n    \"Settings.application.link-upgrade\": \"Mettez à niveau votre panneau d'administration\",\n    \"Settings.application.node-version\": \"version de node\",\n    \"Settings.application.strapi-version\": \"version de strapi\",\n    \"Settings.application.strapiVersion\": \"version de strapi\",\n    \"Settings.application.title\": \"Aperçu\",\n    \"Settings.error\": \"Erreur\",\n    \"Settings.global\": \"Paramètre Globaux\",\n    \"Settings.permissions\": \"Panneau d'aministration\",\n    \"Settings.permissions.category\": \"Paramètres de permissions pour la catégorie {category}\",\n    \"Settings.permissions.category.plugins\": \"Paramètres de permissions pour le plugin {plugin}\",\n    \"Settings.permissions.conditions.anytime\": \"N'importe quand\",\n    \"Settings.permissions.conditions.apply\": \"Appliquer\",\n    \"Settings.permissions.conditions.can\": \"Peut\",\n    \"Settings.permissions.conditions.conditions\": \"Définir les conditions\",\n    \"Settings.permissions.conditions.links\": \"Liens\",\n    \"Settings.permissions.conditions.no-actions\": \"Vous devez d'abord sélectionner des actions (créer, lire, mettre à jour, ...) avant de définir des conditions sur celles-ci.\",\n    \"Settings.permissions.conditions.none-selected\": \"N'importe quand\",\n    \"Settings.permissions.conditions.or\": \"OU\",\n    \"Settings.permissions.conditions.when\": \"Quand\",\n    \"Settings.permissions.select-all-by-permission\": \"Sélectionner toutes les permissions de {label}\",\n    \"Settings.permissions.select-by-permission\": \"Sélectionner la permission de {label}\",\n    \"Settings.permissions.users.create\": \"Créer un nouvel Utilisateur\",\n    \"Settings.permissions.users.email\": \"Email\",\n    \"Settings.permissions.users.firstname\": \"Prénom\",\n    \"Settings.permissions.users.lastname\": \"Nom\",\n    \"Settings.permissions.users.form.sso\": \"Se connecter via SSO\",\n    \"Settings.permissions.users.form.sso.description\": \"Quand activé, les  utilisateurs peuvent se connecter via SSO\",\n    \"Settings.permissions.users.listview.header.subtitle\": \"Tous les utilisateurs ayant accès au panneau d'administration de Strapi\",\n    \"Settings.permissions.users.tabs.label\": \"Onglet Autorisations\",\n    \"Settings.profile.form.notify.data.loaded\": \"Les données de votre profil ont été chargées\",\n    \"Settings.profile.form.section.experience.clear.select\": \"Vider la langue de l'interface sélectionnée\",\n    \"Settings.profile.form.section.experience.here\": \"documentation\",\n    \"Settings.profile.form.section.experience.interfaceLanguage\": \"Langue de l'interface\",\n    \"Settings.profile.form.section.experience.interfaceLanguage.hint\": \"Cela affichera seulement votre propre interface dans la langue sélectionnée\",\n    \"Settings.profile.form.section.experience.interfaceLanguageHelp\": \"La sélection changera la langue de l'interface uniquement pour vous. Veuillez vous référer à cette {here} pour rendre d'autres langues disponibles pour votre équipe.\",\n    \"Settings.profile.form.section.experience.title\": \"Expérience\",\n    \"Settings.profile.form.section.head.title\": \"Profil utilisateur\",\n    \"Settings.profile.form.section.profile.page.title\": \"Page de profil\",\n    \"Settings.roles.create.description\": \"Définir les droits attribués au rôle\",\n    \"Settings.roles.create.title\": \"Créer un rôle\",\n    \"Settings.roles.created\": \"Rôle créé\",\n    \"Settings.roles.edit.title\": \"Editer un rôle\",\n    \"Settings.roles.form.button.users-with-role\": \"{number, plural, =0 {# utilisateurs} one {# utilisateur} other {# utilisateurs}} possédant ce rôle\",\n    \"Settings.roles.form.created\": \"Créé\",\n    \"Settings.roles.form.description\": \"Nom et description du rôle\",\n    \"Settings.roles.form.permission.property-label\": \"permissions de {label}\",\n    \"Settings.roles.form.permissions.attributesPermissions\": \"Permissions de champs\",\n    \"Settings.roles.form.permissions.create\": \"Créer\",\n    \"Settings.roles.form.permissions.delete\": \"Supprimer\",\n    \"Settings.roles.form.permissions.publish\": \"Publier\",\n    \"Settings.roles.form.permissions.read\": \"Lire\",\n    \"Settings.roles.form.permissions.update\": \"Mettre à jour\",\n    \"Settings.roles.list.button.add\": \"Ajouter un rôle\",\n    \"Settings.roles.list.description\": \"Liste des rôles\",\n    \"Settings.roles.title.singular\": \"rôle\",\n    \"Settings.sso.description\": \"Configurer les paramètres de la fonctionnalité Single Sign-On.\",\n    \"Settings.sso.form.defaultRole.description\": \"Cela attribuera le nouvel utilisateur authentifié au rôle sélectionné\",\n    \"Settings.sso.form.defaultRole.description-not-allowed\": \"Vous devez avec la permission de lire les rôles administateurs\",\n    \"Settings.sso.form.defaultRole.label\": \"Rôle par défaut\",\n    \"Settings.sso.form.registration.description\": \"Créer un nouvel utilisateur lors de la connexion via SSO si aucun compte n'existe\",\n    \"Settings.sso.form.registration.label\": \"Enregistrement automatique\",\n    \"Settings.sso.title\": \"Single Sign-On\",\n    \"Settings.webhooks.create\": \"Créer un webhook\",\n    \"Settings.webhooks.create.header\": \"Créer un nouvel en-tête\",\n    \"Settings.webhooks.created\": \"Webhook créé\",\n    \"Settings.webhooks.event.publish-tooltip\": \"Cet événement n'existe que pour les contenus avec le système Brouillon/Publier activé\",\n    \"Settings.webhooks.events.create\": \"Créer\",\n    \"Settings.webhooks.events.update\": \"Mettre à jour\",\n    \"Settings.webhooks.form.events\": \"Evénements\",\n    \"Settings.webhooks.form.headers\": \"En-têtes\",\n    \"Settings.webhooks.form.url\": \"Url\",\n    \"Settings.webhooks.headers.remove\": \"Supprimer l'en-tête ligne {number}\",\n    \"Settings.webhooks.key\": \"Clé\",\n    \"Settings.webhooks.list.button.add\": \"Créer un nouveau webhook\",\n    \"Settings.webhooks.list.description\": \"Recevoir des notifications de modifications en POST\",\n    \"Settings.webhooks.list.empty.description\": \"Aucun webhook trouvé\",\n    \"Settings.webhooks.list.empty.link\": \"Voir notre documentation\",\n    \"Settings.webhooks.list.empty.title\": \"Il n'y a pas encore de webhooks\",\n    \"Settings.webhooks.list.th.actions\": \"actions\",\n    \"Settings.webhooks.list.th.status\": \"statut\",\n    \"Settings.webhooks.singular\": \"webhook\",\n    \"Settings.webhooks.title\": \"Webhooks\",\n    \"Settings.webhooks.to.delete\": \"{webhooksToDeleteLength, plural, one {# élément} other {# éléments}} sélectionné\",\n    \"Settings.webhooks.trigger\": \"Déclencheur\",\n    \"Settings.webhooks.trigger.cancel\": \"Annuler le déclencheur\",\n    \"Settings.webhooks.trigger.pending\": \"En attente...\",\n    \"Settings.webhooks.trigger.save\": \"Veuillez sauvegarder pour déclencher\",\n    \"Settings.webhooks.trigger.success\": \"Succès !\",\n    \"Settings.webhooks.trigger.success.label\": \"Déclenchement réussi\",\n    \"Settings.webhooks.trigger.test\": \"Déclencheur de test\",\n    \"Settings.webhooks.trigger.title\": \"Sauvegarder avant de déclencher\",\n    \"Settings.webhooks.value\": \"Valeur\",\n    Username: Username,\n    Users: Users,\n    \"Users & Permissions\": \"Utilisateurs et autorisations\",\n    \"Users.components.List.empty\": \"Aucun utilisateur...\",\n    \"Users.components.List.empty.withFilters\": \"Aucun utilisateur avec les filtres appliqués...\",\n    \"Users.components.List.empty.withSearch\": \"Aucun utilisateur correspondant à la recherche ({search})...\",\n    \"admin.pages.MarketPlacePage.head\": \"Marketplace - Plugins\",\n    \"admin.pages.MarketPlacePage.submit.plugin.link\": \"Soumettez votre plugin\",\n    \"admin.pages.MarketPlacePage.subtitle\": \"Tirez le meilleur de Strapi\",\n    anErrorOccurred: anErrorOccurred,\n    \"app.component.CopyToClipboard.label\": \"Copier dans le presse-papier\",\n    noPreview: noPreview,\n    \"app.component.search.label\": \"Rechercher {target}\",\n    \"app.component.table.duplicate\": \"Dupliquer {target}\",\n    \"app.component.table.edit\": \"Modifier {target}\",\n    \"app.component.table.select.one-entry\": \"Sélectionner {target}\",\n    \"app.components.BlockLink.blog\": \"Blog\",\n    \"app.components.BlockLink.blog.content\": \"Lire les dernières actualités à propos de Strapi et de son écosystème\",\n    \"app.components.BlockLink.code\": \"Apps d'exemple\",\n    \"app.components.BlockLink.code.content\": \"Apprenez en testant des projets réels développés par la communauté.\",\n    \"app.components.BlockLink.documentation.content\": \"Découvrir les concepts essentials, guides et instructions.\",\n    \"app.components.BlockLink.tutorial\": \"Tutoriels\",\n    \"app.components.BlockLink.tutorial.content\": \"Suivre les instructions étapes par étapes pour utiliser et personnaliser Strapi.\",\n    \"app.components.Button.cancel\": \"Annuler\",\n    \"app.components.Button.confirm\": \"Confirmer\",\n    \"app.components.Button.reset\": \"Annuler\",\n    \"app.components.ComingSoonPage.comingSoon\": \"Bientôt disponible\",\n    \"app.components.ConfirmDialog.title\": \"Confirmation\",\n    \"app.components.DownloadInfo.download\": \"Téléchargement en cours...\",\n    \"app.components.DownloadInfo.text\": \"Cela peut prendre une minute. Merci de patienter.\",\n    \"app.components.EmptyAttributes.title\": \"Il n'y a pas encore de champ\",\n    \"app.components.EmptyStateLayout.content-document\": \"Vous n'avez pas encore de contenu...\",\n    \"app.components.EmptyStateLayout.content-permissions\": \"Vous n'avez pas les permissions pour accéder à ce contenu\",\n    \"app.components.HomePage.button.blog\": \"Voir plus sur le blog\",\n    \"app.components.HomePage.community\": \"Rejoignez la communauté\",\n    \"app.components.HomePage.community.content\": \"Discutez avec les membres de l'équipe, contributeurs et développeurs sur différent supports.\",\n    \"app.components.HomePage.create\": \"Créez votre première Collection\",\n    \"app.components.HomePage.roadmap\": \"Voir notre roadmap\",\n    \"app.components.HomePage.welcome\": \"Bienvenue à bord !\",\n    \"app.components.HomePage.welcome.again\": \"Bienvenue \",\n    \"app.components.HomePage.welcomeBlock.content\": \"Félicitations ! Vous êtes connecté en tant que tout premier administrateur. Pour découvrir les puissantes fonctionnalités fournies par Strapi, nous vous recommandons de créer votre premier Type de Contenu !\",\n    \"app.components.HomePage.welcomeBlock.content.again\": \"Nous espérons que votre projet avance bien... Découvrez les derniers articles à propos de Strapi. Nous faisons de notre mieux pour améliorer le produit selon vos retours.\",\n    \"app.components.HomePage.welcomeBlock.content.issues\": \"issues\",\n    \"app.components.HomePage.welcomeBlock.content.raise\": \" ou soumettez des \",\n    \"app.components.ImgPreview.hint\": \"Glissez-déposez dans cette zone ou {browse} un fichier à télécharger\",\n    \"app.components.ImgPreview.hint.browse\": \"recherchez\",\n    \"app.components.InputFile.newFile\": \"Ajouter un nouveau fichier\",\n    \"app.components.InputFileDetails.open\": \"Ouvrir dans une nouvelle fenêtre\",\n    \"app.components.InputFileDetails.originalName\": \"Nom d'origine :\",\n    \"app.components.InputFileDetails.remove\": \"Supprimer ce fichier\",\n    \"app.components.InputFileDetails.size\": \"Taille:\",\n    \"app.components.InstallPluginPage.Download.description\": \"L'installation d'un plugin peut prendre quelques secondes.\",\n    \"app.components.InstallPluginPage.Download.title\": \"Téléchargement en cours...\",\n    \"app.components.InstallPluginPage.description\": \"Améliorez votre app sans efforts\",\n    \"app.components.LeftMenu.collapse\": \"Réduire la barre de navigation\",\n    \"app.components.LeftMenu.expand\": \"Développer la barre de navigation\",\n    \"app.components.LeftMenu.logout\": \"Déconnexion\",\n    \"app.components.LeftMenu.trialCountdown\": \"Votre essai se termine le {date}.\",\n    \"app.components.LeftMenuFooter.help\": \"Aide\",\n    \"app.components.LeftMenuFooter.poweredBy\": \"Propulsé par \",\n    \"app.components.LeftMenuLinkContainer.collectionTypes\": \"Types de collection\",\n    \"app.components.LeftMenuLinkContainer.configuration\": \"Configurations\",\n    \"app.components.LeftMenuLinkContainer.general\": \"Général\",\n    \"app.components.LeftMenuLinkContainer.noPluginsInstalled\": \"Aucun plugin installé\",\n    \"app.components.LeftMenuLinkContainer.plugins\": \"Plugins\",\n    \"app.components.LeftMenuLinkContainer.singleTypes\": \"Types uniques\",\n    \"app.components.ListPluginsPage.deletePlugin.description\": \"La désinstallation du plugin peut prendre quelques secondes.\",\n    \"app.components.ListPluginsPage.deletePlugin.title\": \"Désinstallation\",\n    \"app.components.ListPluginsPage.description\": \"Liste des plugins installés dans le projet.\",\n    \"app.components.ListPluginsPage.head.title\": \"List plugins\",\n    \"app.components.Logout.logout\": \"Se déconnecter\",\n    \"app.components.Logout.profile\": \"Profil\",\n    \"app.components.MarketplaceBanner\": \"Découvrez les plugins construits par la communauté, et bien d'autres choses géniales pour démarrer votre projet, sur Strapi Awesome.\",\n    \"app.components.MarketplaceBanner.image.alt\": \"un logo fusée de strapi\",\n    \"app.components.MarketplaceBanner.link\": \"Aller voir ça maintenant\",\n    \"app.components.NotFoundPage.back\": \"Retourner à la page d'accueil\",\n    \"app.components.NotFoundPage.description\": \"Page introuvable\",\n    \"app.components.Official\": \"Officiel\",\n    \"app.components.Onboarding.help.button\": \"Bouton d'aide\",\n    \"app.components.Onboarding.label.completed\": \"% complétées\",\n    \"app.components.Onboarding.title\": \"Démarrons ensemble\",\n    \"app.components.PluginCard.Button.label.download\": \"Télécharger\",\n    \"app.components.PluginCard.Button.label.install\": \"Déjà installé\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed\": \"La configuration d'autoReload a besoin d'être activée pour télécharger un plugin. Veuillez démarrer votre application avec `yarn develop`.\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.confirm\": \"J'ai compris !\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.environment\": \"Pour des raisoins de sécurité, un plugin ne peut être installé qu'en dévelopment.\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.title\": \"Le téléchargement est impossible\",\n    \"app.components.PluginCard.compatible\": \"Compatible avec votre app\",\n    \"app.components.PluginCard.compatibleCommunity\": \"Compatible avec la communauté\",\n    \"app.components.PluginCard.more-details\": \"Plus de détails\",\n    \"app.components.ToggleCheckbox.off-label\": \"Désactivé\",\n    \"app.components.ToggleCheckbox.on-label\": \"Activé\",\n    \"app.components.Users.MagicLink.connect\": \"Envoyez ce lien à l'utilisateur pour qu'il se connecte.\",\n    \"app.components.Users.MagicLink.connect.sso\": \"Envoyez ce lien à l'utilisateur, la première connexion peut être effectué via un fournisseur SSO\",\n    \"app.components.Users.ModalCreateBody.block-title.details\": \"Détails\",\n    \"app.components.Users.ModalCreateBody.block-title.roles\": \"Rôles de l'utilisateur\",\n    \"app.components.Users.ModalCreateBody.block-title.roles.description\": \"Un utilisateur peut avoir un ou plusieurs rôles\",\n    \"app.components.Users.SortPicker.button-label\": \"Trier par\",\n    \"app.components.Users.SortPicker.sortby.email_asc\": \"Email (A à Z)\",\n    \"app.components.Users.SortPicker.sortby.email_desc\": \"Email (Z à A)\",\n    \"app.components.Users.SortPicker.sortby.firstname_asc\": \"Prénom (A à Z)\",\n    \"app.components.Users.SortPicker.sortby.firstname_desc\": \"Prénom (Z à A)\",\n    \"app.components.Users.SortPicker.sortby.lastname_asc\": \"Nom (A à Z)\",\n    \"app.components.Users.SortPicker.sortby.lastname_desc\": \"Nom (Z à A)\",\n    \"app.components.Users.SortPicker.sortby.username_asc\": \"Nom d'utilisateur (A à Z)\",\n    \"app.components.Users.SortPicker.sortby.username_desc\": \"Nom d'utilisateur (Z à A)\",\n    \"app.components.listPlugins.button\": \"Ajouter un Nouveau Plugin\",\n    \"app.components.listPlugins.title.none\": \"Aucun plugin n'est installé\",\n    \"app.components.listPluginsPage.deletePlugin.error\": \"Une erreur est survenue pendant la désintallation\",\n    \"app.containers.App.notification.error.init\": \"Une erreur est survenue en requêtant l'API\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin\": \"Si vous ne recevez pas ce lien, veuillez contacter votre administrateur.\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.text.email\": \"La réception de votre lien de récupération de mot de passe peut prendre quelques minutes.\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.title\": \"Email envoyé\",\n    \"app.containers.Users.EditPage.form.active.label\": \"Actif\",\n    \"app.containers.Users.EditPage.header.label\": \"Modifier {name}\",\n    \"app.containers.Users.EditPage.header.label-loading\": \"Modifier l'utilisateur\",\n    \"app.containers.Users.EditPage.roles-bloc-title\": \"Rôles attribués\",\n    \"app.containers.Users.ModalForm.footer.button-success\": \"Créer l'utilisateur\",\n    \"app.links.configure-view\": \"Configurez la vue\",\n    \"app.static.links.cheatsheet\": \"Aide-mémoire\",\n    \"app.utils.SelectOption.defaultMessage\": \" \",\n    \"app.utils.add-filter\": \"Ajouter un filtre\",\n    \"app.utils.close-label\": \"Fermer\",\n    \"app.utils.defaultMessage\": \" \",\n    \"app.utils.duplicate\": \"Dupliquer\",\n    \"app.utils.edit\": \"Modifier\",\n    \"app.utils.delete\": \"Supprimer\",\n    \"app.utils.errors.file-too-big.message\": \"Le fichier est trop lourd\",\n    \"app.utils.filter-value\": \"Valeur du filtre\",\n    \"app.utils.filters\": \"Filtres\",\n    \"app.utils.notify.data-loaded\": \"{target} est chargée\",\n    \"app.utils.placeholder.defaultMessage\": \" \",\n    \"app.utils.publish\": \"Publier\",\n    \"app.utils.select-all\": \"Tout sélectionner\",\n    \"app.utils.select-field\": \"Sélectionner un champ\",\n    \"app.utils.select-filter\": \"Sélectionner un filtre\",\n    \"app.utils.unpublish\": \"Annuler la publication\",\n    \"app.utils.ready-to-publish\": \"Prêt à publier\",\n    \"app.utils.already-published\": \"Déjà publié\",\n    clearLabel: clearLabel,\n    \"coming.soon\": \"Ce contenu est actuellement en construction et sera de retour dans quelques semaines !\",\n    \"component.Input.error.validation.integer\": \"La valeur doit être un nombre entier\",\n    \"components.AutoReloadBlocker.description\": \"Démarrez Strapi avec l'une des commandes suivantes:\",\n    \"components.AutoReloadBlocker.header\": \"L'autoReload doit être activé pour ce plugin.\",\n    \"components.ErrorBoundary.title\": \"Une erreur est survenue...\",\n    \"components.FilterOptions.FILTER_TYPES.$contains\": \"contient\",\n    \"components.FilterOptions.FILTER_TYPES.$containsi\": \"contient (insensible à la casse)\",\n    \"components.FilterOptions.FILTER_TYPES.$endsWith\": \"termine par\",\n    \"components.FilterOptions.FILTER_TYPES.$endsWithi\": \"termine par (insensible à la casse)\",\n    \"components.FilterOptions.FILTER_TYPES.$eq\": \"est\",\n    \"components.FilterOptions.FILTER_TYPES.$eqi\": \"est (insensible à la casse)\",\n    \"components.FilterOptions.FILTER_TYPES.$gt\": \"est plus grand que\",\n    \"components.FilterOptions.FILTER_TYPES.$gte\": \"est plus grand ou égal à\",\n    \"components.FilterOptions.FILTER_TYPES.$lt\": \"est plus petit que\",\n    \"components.FilterOptions.FILTER_TYPES.$lte\": \"est plus petit ou égal à\",\n    \"components.FilterOptions.FILTER_TYPES.$ne\": \"n'est pas\",\n    \"components.FilterOptions.FILTER_TYPES.$nei\": \"n'est pas (insensible à la casse)\",\n    \"components.FilterOptions.FILTER_TYPES.$notContains\": \"ne contient pas\",\n    \"components.FilterOptions.FILTER_TYPES.$notContainsi\": \"ne contient pas (insensible à la casse)\",\n    \"components.FilterOptions.FILTER_TYPES.$notNull\": \"n'est pas nul\",\n    \"components.FilterOptions.FILTER_TYPES.$null\": \"est nul\",\n    \"components.FilterOptions.FILTER_TYPES.$startsWith\": \"commence par\",\n    \"components.FilterOptions.FILTER_TYPES.$startsWithi\": \"commence par (insensible à la casse)\",\n    \"components.Input.error.attribute.key.taken\": \"Cette valeur existe déjà\",\n    \"components.Input.error.attribute.sameKeyAndName\": \"Ne peuvent pas être égaux\",\n    \"components.Input.error.attribute.taken\": \"Ce champ existe déjà\",\n    \"components.Input.error.contain.lowercase\": \"Le mot de passe doit contenir au moins une lettre minuscule\",\n    \"components.Input.error.contain.number\": \"Le mot de passe doit contenir au moins un chiffre\",\n    \"components.Input.error.contain.uppercase\": \"Le mot de passe doit contenir au moins une lettre majuscule\",\n    \"components.Input.error.contentTypeName.taken\": \"Ce nom existe déjà\",\n    \"components.Input.error.custom-error\": \"{errorMessage} \",\n    \"components.Input.error.password.noMatch\": \"Le mot de passe ne correspond pas\",\n    \"components.Input.error.validation.email\": \"Le format n'est pas de type e-mail\",\n    \"components.Input.error.validation.json\": \"Le format JSON n'est pas respecté\",\n    \"components.Input.error.validation.max\": \"La valeur est trop grande {max}.\",\n    \"components.Input.error.validation.maxLength\": \"La valeur est trop longue {max}.\",\n    \"components.Input.error.validation.min\": \"La valeur est trop basse {min}.\",\n    \"components.Input.error.validation.minLength\": \"La valeur est trop courte {min}.\",\n    \"components.Input.error.validation.minSupMax\": \"Ne peut pas être plus grand.\",\n    \"components.Input.error.validation.regex\": \"La valeur ne correspond pas au format attendu.\",\n    \"components.Input.error.validation.required\": \"Ce champ est obligatoire.\",\n    \"components.Input.error.validation.unique\": \"Cette valeur est déjà prise\",\n    \"components.InputSelect.option.placeholder\": \"Choisissez ici\",\n    \"components.ListRow.empty\": \"Il n'y a pas de données à afficher.\",\n    \"components.NotAllowedInput.text\": \"Vous n'êtes pas autorisé à voir ce champ\",\n    \"components.OverlayBlocker.description\": \"Vous utilisez une fonctionnalité qui nécessite le redémarrage du server. Merci d'attendre que celui-ci ait redémarré.\",\n    \"components.OverlayBlocker.description.serverError\": \"Le serveur aurait déjà du redémarrer, vous devriez regarder les messages dans le terminal.\",\n    \"components.OverlayBlocker.title\": \"Le serveur est en train de redémarrer\",\n    \"components.OverlayBlocker.title.serverError\": \"Le serveur aurait déjà du redémarrer\",\n    \"components.PageFooter.select\": \"entrées par page\",\n    \"components.ProductionBlocker.description\": \"Pour des raisons de sécurité il est désactivé dans les autres environnements.\",\n    \"components.ProductionBlocker.header\": \"Ce plugin est disponible uniquement en développement.\",\n    \"components.Search.placeholder\": \"Rechercher...\",\n    \"components.TableHeader.sort\": \"Trier par {label}\",\n    \"components.Wysiwyg.ToggleMode.markdown-mode\": \"Mode Markdown\",\n    \"components.Wysiwyg.ToggleMode.preview-mode\": \"Mode Aperçu\",\n    \"components.Wysiwyg.collapse\": \"Fermer\",\n    \"components.Wysiwyg.selectOptions.H1\": \"Titre H1\",\n    \"components.Wysiwyg.selectOptions.H2\": \"Titre H2\",\n    \"components.Wysiwyg.selectOptions.H3\": \"Titre H3\",\n    \"components.Wysiwyg.selectOptions.H4\": \"Titre H4\",\n    \"components.Wysiwyg.selectOptions.H5\": \"Titre H5\",\n    \"components.Wysiwyg.selectOptions.H6\": \"Titre H6\",\n    \"components.Wysiwyg.selectOptions.title\": \"Ajouter un titre\",\n    \"components.WysiwygBottomControls.charactersIndicators\": \"caractères\",\n    \"components.WysiwygBottomControls.fullscreen\": \"Plein écran\",\n    \"components.WysiwygBottomControls.uploadFiles\": \"Ajouter des fichiers en les 'glissant-déposant', {browse}, ou en les collant depuis le presse-papier\",\n    \"components.WysiwygBottomControls.uploadFiles.browse\": \"en les selectionnant\",\n    \"components.pagination.go-to\": \"Aller à la page {page}\",\n    \"components.pagination.go-to-next\": \"Aller à la page suivante\",\n    \"components.pagination.go-to-previous\": \"Aller à la page précédente\",\n    \"components.pagination.remaining-links\": \"Et {number} autres liens\",\n    \"components.popUpWarning.button.cancel\": \"Non, annuler\",\n    \"components.popUpWarning.button.confirm\": \"Oui, confirmer\",\n    \"components.popUpWarning.message\": \"Etes-vous sure de vouloir le supprimer ?\",\n    \"components.popUpWarning.title\": \"Merci de confirmer\",\n    dark: dark,\n    \"form.button.continue\": \"Continuer\",\n    \"global.search\": \"Rechercher\",\n    \"global.actions\": \"Actions\",\n    \"global.auditLogs\": \"Journaux d'audit\",\n    \"global.back\": \"Retour\",\n    \"global.cancel\": \"Annuler\",\n    \"global.change-password\": \"Modifier le mot de passe\",\n    \"global.content-manager\": \"Gestion du contenu\",\n    \"global.continue\": \"Continuer\",\n    \"global.delete\": \"Supprimer\",\n    \"global.delete-target\": \"Supprimer {target}\",\n    \"global.description\": \"Description\",\n    \"global.details\": \"Détails\",\n    \"global.disabled\": \"Désactivé\",\n    \"global.documentation\": \"Documentation\",\n    \"global.enabled\": \"Activé\",\n    \"global.finish\": \"Terminer\",\n    \"global.last-change.redo\": \"Rétablir dernier changement\",\n    \"global.last-change.undo\": \"Annuler dernier changement\",\n    \"global.last-changes.discard\": \"Annuler derniers changements\",\n    \"global.marketplace\": \"Marketplace\",\n    \"global.name\": \"Nom\",\n    \"global.none\": \"Aucun\",\n    \"global.password\": \"Mot de passe\",\n    \"global.plugins\": \"Plugins\",\n    \"global.profile\": \"Profil\",\n    \"global.reset-password\": \"Réinitialiser le mot de passe\",\n    \"global.roles\": \"Rôles\",\n    \"global.save\": \"Enregistrer\",\n    \"global.see-more\": \"Voir plus\",\n    \"global.select\": \"Sélectionner\",\n    \"global.select-all-entries\": \"Sélectionner toutes les entrées\",\n    \"global.settings\": \"Paramètres\",\n    \"global.type\": \"Type\",\n    \"global.users\": \"Utilisateurs\",\n    light: light,\n    \"form.button.done\": \"Terminer\",\n    \"global.prompt.unsaved\": \"Êtes-vous sûr de vouloir quitter cette page? Toutes vos modifications seront perdues\",\n    \"notification.contentType.relations.conflict\": \"Le Type de Contenu à des relations qui rentrent en conflit\",\n    \"notification.default.title\": \"Information:\",\n    \"notification.error\": \"Une erreur est survenue\",\n    \"notification.error.layout\": \"Impossible de récupérer le layout de l'admin\",\n    \"notification.form.error.fields\": \"Le formulaire contient des erreurs\",\n    \"notification.form.success.fields\": \"Modifications enregistrées\",\n    \"notification.link-copied\": \"Lien copié dans le presse-papier\",\n    \"notification.permission.not-allowed-read\": \"Vous n'êtes pas autorisé à voir ce document\",\n    \"notification.success.delete\": \"Cet élément a été supprimé\",\n    \"notification.success.saved\": \"Sauvegardé\",\n    \"notification.success.title\": \"Succès :\",\n    \"notification.version.update.message\": \"Une nouvelle version de Strapi est disponible !\",\n    \"notification.warning.title\": \"Attention :\",\n    \"notification.warning.404\": \"404 - Introuvable\",\n    or: or,\n    \"request.error.model.unknown\": \"Le model n'existe pas\",\n    selectButtonTitle: selectButtonTitle,\n    skipToContent: skipToContent,\n    submit: submit,\n    \"components.Blocks.modifiers.bold\": \"Gras\",\n    \"components.Blocks.modifiers.italic\": \"Italique\",\n    \"components.Blocks.modifiers.underline\": \"Souligné\",\n    \"components.Blocks.modifiers.strikethrough\": \"Barré\",\n    \"components.Blocks.modifiers.code\": \"Code\",\n    \"components.Blocks.link\": \"Lien\",\n    \"components.Blocks.expand\": \"Agrandir\",\n    \"components.Blocks.collapse\": \"Rétrécir\",\n    \"components.Blocks.popover.text\": \"Texte\",\n    \"components.Blocks.popover.text.placeholder\": \"Entrez le texte du lien\",\n    \"components.Blocks.popover.link\": \"Lien\",\n    \"components.Blocks.popover.link.placeholder\": \"Coller un lien\",\n    \"components.Blocks.popover.link.error\": \"Merci d'entrer un lien valide\",\n    \"components.Blocks.popover.remove\": \"Supprimer\",\n    \"components.Blocks.popover.edit\": \"Modifier\",\n    \"components.Blocks.blocks.text\": \"Texte\",\n    \"components.Blocks.blocks.heading1\": \"Titre 1\",\n    \"components.Blocks.blocks.heading2\": \"Titre 2\",\n    \"components.Blocks.blocks.heading3\": \"Titre 3\",\n    \"components.Blocks.blocks.heading4\": \"Titre 4\",\n    \"components.Blocks.blocks.heading5\": \"Titre 5\",\n    \"components.Blocks.blocks.heading6\": \"Titre 6\",\n    \"components.Blocks.blocks.code\": \"Bloc de code\",\n    \"components.Blocks.blocks.quote\": \"Citation\",\n    \"components.Blocks.blocks.image\": \"Image\",\n    \"components.Blocks.blocks.unorderedList\": \"Liste à puces\",\n    \"components.Blocks.blocks.orderedList\": \"Liste numérotée\"\n};\n\nexport { Analytics, Documentation, Email, Password, Provider, ResetPasswordToken, Role, Username, Users, anErrorOccurred, clearLabel, dark, fr as default, light, noPreview, or, selectButtonTitle, skipToContent, submit };\n//# sourceMappingURL=fr.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,YAAY;AAChB,IAAI,gBAAgB;AACpB,IAAI,QAAQ;AACZ,IAAI,WAAW;AACf,IAAI,WAAW;AACf,IAAI,qBAAqB;AACzB,IAAI,OAAO;AACX,IAAI,WAAW;AACf,IAAI,QAAQ;AACZ,IAAI,kBAAkB;AACtB,IAAI,YAAY;AAChB,IAAI,aAAa;AACjB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,KAAK;AACT,IAAI,oBAAoB;AACxB,IAAI,gBAAgB;AACpB,IAAI,SAAS;AACb,IAAI,KAAK;AAAA,EACL;AAAA,EACA,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0CAA0C;AAAA,EAC1C,iCAAiC;AAAA,EACjC,sCAAsC;AAAA,EACtC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,iDAAiD;AAAA,EACjD,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,2BAA2B;AAAA,EAC3B,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA,EAChB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,6BAA6B;AAAA,EAC7B,qCAAqC;AAAA,EACrC,0CAA0C;AAAA,EAC1C,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,4BAA4B;AAAA,EAC5B,4BAA4B;AAAA,EAC5B,0CAA0C;AAAA,EAC1C,sBAAsB;AAAA,EACtB,iDAAiD;AAAA,EACjD,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,iCAAiC;AAAA,EACjC,uCAAuC;AAAA,EACvC,4CAA4C;AAAA,EAC5C,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,kCAAkC;AAAA,EAClC,kDAAkD;AAAA,EAClD,0DAA0D;AAAA,EAC1D,yDAAyD;AAAA,EACzD,+BAA+B;AAAA,EAC/B,oCAAoC;AAAA,EACpC,mCAAmC;AAAA,EACnC,oCAAoC;AAAA,EACpC,4CAA4C;AAAA,EAC5C,sCAAsC;AAAA,EACtC,uCAAuC;AAAA,EACvC,iCAAiC;AAAA,EACjC,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,uCAAuC;AAAA,EACvC,wCAAwC;AAAA,EACxC,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,gCAAgC;AAAA,EAChC,8BAA8B;AAAA,EAC9B,iCAAiC;AAAA,EACjC,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,4CAA4C;AAAA,EAC5C,yCAAyC;AAAA,EACzC,gDAAgD;AAAA,EAChD,iDAAiD;AAAA,EACjD,8CAA8C;AAAA,EAC9C,+CAA+C;AAAA,EAC/C,6CAA6C;AAAA,EAC7C,wDAAwD;AAAA,EACxD,iDAAiD;AAAA,EACjD,+CAA+C;AAAA,EAC/C,sCAAsC;AAAA,EACtC,8DAA8D;AAAA,EAC9D,oDAAoD;AAAA,EACpD,2DAA2D;AAAA,EAC3D,+DAA+D;AAAA,EAC/D,6DAA6D;AAAA,EAC7D,+DAA+D;AAAA,EAC/D,4DAA4D;AAAA,EAC5D,qDAAqD;AAAA,EACrD,8DAA8D;AAAA,EAC9D,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,+DAA+D;AAAA,EAC/D,mEAAmE;AAAA,EACnE,6DAA6D;AAAA,EAC7D,0DAA0D;AAAA,EAC1D,2DAA2D;AAAA,EAC3D,sDAAsD;AAAA,EACtD,mDAAmD;AAAA,EACnD,8DAA8D;AAAA,EAC9D,6DAA6D;AAAA,EAC7D,gEAAgE;AAAA,EAChE,iEAAiE;AAAA,EACjE,8DAA8D;AAAA,EAC9D,mEAAmE;AAAA,EACnE,iEAAiE;AAAA,EACjE,4DAA4D;AAAA,EAC5D,wEAAwE;AAAA,EACxE,wDAAwD;AAAA,EACxD,mDAAmD;AAAA,EACnD,oCAAoC;AAAA,EACpC,sCAAsC;AAAA,EACtC,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,sCAAsC;AAAA,EACtC,8BAA8B;AAAA,EAC9B,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,yDAAyD;AAAA,EACzD,iDAAiD;AAAA,EACjD,8DAA8D;AAAA,EAC9D,mEAAmE;AAAA,EACnE,kEAAkE;AAAA,EAClE,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,oDAAoD;AAAA,EACpD,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,mCAAmC;AAAA,EACnC,iDAAiD;AAAA,EACjD,yDAAyD;AAAA,EACzD,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,wCAAwC;AAAA,EACxC,0CAA0C;AAAA,EAC1C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,6CAA6C;AAAA,EAC7C,yDAAyD;AAAA,EACzD,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,wCAAwC;AAAA,EACxC,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,2CAA2C;AAAA,EAC3C,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,4CAA4C;AAAA,EAC5C,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,2BAA2B;AAAA,EAC3B;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,+BAA+B;AAAA,EAC/B,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,kDAAkD;AAAA,EAClD,wCAAwC;AAAA,EACxC;AAAA,EACA,uCAAuC;AAAA,EACvC;AAAA,EACA,8BAA8B;AAAA,EAC9B,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,wCAAwC;AAAA,EACxC,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,4CAA4C;AAAA,EAC5C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,0CAA0C;AAAA,EAC1C,sCAAsC;AAAA,EACtC,2CAA2C;AAAA,EAC3C,wDAAwD;AAAA,EACxD,sDAAsD;AAAA,EACtD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,2DAA2D;AAAA,EAC3D,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,oCAAoC;AAAA,EACpC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,2CAA2C;AAAA,EAC3C,2BAA2B;AAAA,EAC3B,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,+EAA+E;AAAA,EAC/E,qEAAqE;AAAA,EACrE,yEAAyE;AAAA,EACzE,mEAAmE;AAAA,EACnE,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,4DAA4D;AAAA,EAC5D,0DAA0D;AAAA,EAC1D,sEAAsE;AAAA,EACtE,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,qDAAqD;AAAA,EACrD,wDAAwD;AAAA,EACxD,yDAAyD;AAAA,EACzD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,qCAAqC;AAAA,EACrC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,oEAAoE;AAAA,EACpE,4DAA4D;AAAA,EAC5D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,wDAAwD;AAAA,EACxD,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,yCAAyC;AAAA,EACzC,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,4BAA4B;AAAA,EAC5B,uBAAuB;AAAA,EACvB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,yCAAyC;AAAA,EACzC,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,gCAAgC;AAAA,EAChC,wCAAwC;AAAA,EACxC,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB,8BAA8B;AAAA,EAC9B,+BAA+B;AAAA,EAC/B;AAAA,EACA,eAAe;AAAA,EACf,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,+CAA+C;AAAA,EAC/C,qDAAqD;AAAA,EACrD,sDAAsD;AAAA,EACtD,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,0CAA0C;AAAA,EAC1C,4CAA4C;AAAA,EAC5C,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,gDAAgD;AAAA,EAChD,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,yDAAyD;AAAA,EACzD,+CAA+C;AAAA,EAC/C,gDAAgD;AAAA,EAChD,uDAAuD;AAAA,EACvD,+BAA+B;AAAA,EAC/B,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,yCAAyC;AAAA,EACzC,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC;AAAA,EACA,wBAAwB;AAAA,EACxB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,wBAAwB;AAAA,EACxB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,2BAA2B;AAAA,EAC3B,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,sBAAsB;AAAA,EACtB,eAAe;AAAA,EACf,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,yBAAyB;AAAA,EACzB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB;AAAA,EACA,oBAAoB;AAAA,EACpB,yBAAyB;AAAA,EACzB,+CAA+C;AAAA,EAC/C,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,4CAA4C;AAAA,EAC5C,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B;AAAA,EACA,+BAA+B;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA,oCAAoC;AAAA,EACpC,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,oCAAoC;AAAA,EACpC,0BAA0B;AAAA,EAC1B,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA,EAC9B,kCAAkC;AAAA,EAClC,8CAA8C;AAAA,EAC9C,kCAAkC;AAAA,EAClC,8CAA8C;AAAA,EAC9C,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,0CAA0C;AAAA,EAC1C,wCAAwC;AAC5C;", "names": []}