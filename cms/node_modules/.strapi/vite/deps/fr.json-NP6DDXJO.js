import "./chunk-PLDDJCW6.js";

// node_modules/@strapi/content-manager/dist/admin/translations/fr.json.mjs
var groups = "Groupes";
var models = "Types de Collection";
var pageNotFound = "Page non trouvée";
var fr = {
  "App.schemas.data-loaded": "Les schéma ont été chargés avec succès",
  "ListViewTable.relation-loaded": "Les relations on été chargées",
  "EditRelations.title": "Données associées",
  "HeaderLayout.button.label-add-entry": "Créer une nouvelle entrée",
  "api.id": "API ID",
  "bulk-publish.already-published": "Déjà publié",
  "bulk-publish.modified": "Prêt à publier les changements",
  "bulk-publish.waiting-for-action": "En attente d'une action",
  "components.AddFilterCTA.add": "Filtres",
  "components.AddFilterCTA.hide": "Filtres",
  "components.DragHandle-label": "Glisser",
  "components.DraggableAttr.edit": "Cliquez pour modifier",
  "components.DraggableCard.delete.field": "Supprimer {item}",
  "components.DraggableCard.edit.field": "Modifier {item}",
  "components.DraggableCard.move.field": "Déplacer {item}",
  "components.ListViewTable.row-line": "ligne {number}",
  "components.DynamicZone.ComponentPicker-label": "Choisir un composant",
  "components.DynamicZone.add-component": "Ajouter un composant à {componentName}",
  "components.DynamicZone.delete-label": "Supprimer {name}",
  "components.DynamicZone.error-message": "Le composant contient une ou des erreurs",
  "components.DynamicZone.missing-components": "Il y a {number, plural, =0 {# composants manquants} one {# composant manquant} other {# composants manquants}}",
  "components.DynamicZone.move-down-label": "Déplacer le composant vers le bas",
  "components.DynamicZone.move-up-label": "Déplacer le composant vers le haut",
  "components.DynamicZone.pick-compo": "Choisir un composant",
  "components.DynamicZone.required": "Composant requis",
  "components.EmptyAttributesBlock.button": "Voir la page des configurations",
  "components.EmptyAttributesBlock.description": "Vous pouvez modifiez vos paramètres",
  "components.FieldItem.linkToComponentLayout": "Modifier le layout du composant",
  "components.FieldSelect.label": "Ajouter un champ",
  "components.FilterOptions.button.apply": "Appliquer",
  "components.FiltersPickWrapper.PluginHeader.actions.apply": "Appliquer",
  "components.FiltersPickWrapper.PluginHeader.actions.clearAll": "Tout supprimer",
  "components.FiltersPickWrapper.PluginHeader.description": "Définissez les conditions des filtres à appliquer",
  "components.FiltersPickWrapper.PluginHeader.title.filter": "Filtres",
  "components.FiltersPickWrapper.hide": "Fermer",
  "components.LeftMenu.Search.label": "Chercher un type de contenu",
  "components.LeftMenu.collection-types": "Types de Collections",
  "components.LeftMenu.single-types": "Types uniques",
  "components.LimitSelect.itemsPerPage": "Éléments par page",
  "components.NotAllowedInput.text": "Vous n'avez pas la permission de voir ce champ",
  "components.RepeatableComponent.error-message": "Le composant contient une ou des erreurs",
  "components.Search.placeholder": "Rechercher une entrée...",
  "components.Select.draft-info-title": "Statut: Brouillon",
  "components.Select.publish-info-title": "Statut: Publié",
  "components.SettingsViewWrapper.pluginHeader.description.edit-settings": "Définissez l'apparence de la vue edit.",
  "components.SettingsViewWrapper.pluginHeader.description.list-settings": "Définir les paramètres de la vue liste.",
  "components.SettingsViewWrapper.pluginHeader.title": "Configurer la vue - {name}",
  "components.TableDelete.delete": "Tout supprimer",
  "components.TableDelete.deleteSelected": "Supprimer les éléments sélectionnés",
  "components.TableDelete.label": "{number, plural, one {# entrée sélectionnée} other {# entrées sélectionnées}}",
  "components.TableEmpty.withFilters": "Aucun {contentType} n'a été trouvé avec ces filtres...",
  "components.TableEmpty.withSearch": "Aucun {contentType} n'a été trouvé avec cette recherche ({search})...",
  "components.TableEmpty.withoutFilter": "Aucun {contentType} n'a été trouvé...",
  "components.empty-repeatable": "Il n'a pas encore d'entrée. Cliquez pour en ajouter une.",
  "components.notification.info.maximum-requirement": "Le nombre maximal de champs est atteint",
  "components.notification.info.minimum-requirement": "Un champ a été rajouté pour remplir les conditions minimales",
  "components.repeatable.reorder.error": "Une erreur s'est produite lors de la réorganisation du champ de votre composant, veuillez réessayer",
  "components.reset-entry": "Supprimer l'entrée",
  "components.uid.apply": "appliquer",
  "components.uid.available": "disponible",
  "components.uid.regenerate": "regénérer",
  "components.uid.suggested": "suggéré",
  "components.uid.unavailable": "indisponible",
  "containers.Edit.Link.Layout": "Paramétrer la vue",
  "containers.Edit.Link.Model": "Éditer le modèle",
  "containers.Edit.addAnItem": "Ajouter un élément...",
  "containers.Edit.clickToJump": "Cliquer pour voir l'entrée",
  "containers.Edit.delete": "Supprimer",
  "containers.Edit.delete-entry": "Supprimer cette entrée",
  "containers.Edit.editing": "Édition en cours...",
  "containers.Edit.information": "Informations",
  "containers.Edit.information.by": "Par",
  "containers.Edit.information.created": "Créé",
  "containers.Edit.information.draftVersion": "version brouillon",
  "containers.Edit.information.editing": "Édition :",
  "containers.Edit.information.lastUpdate": "Dernière modification",
  "containers.Edit.information.publishedVersion": "version publiée",
  "containers.Edit.pluginHeader.title.new": "Créer un document",
  "containers.Edit.reset": "Annuler",
  "containers.Edit.returnList": "Retourner à la liste",
  "containers.Edit.seeDetails": "Détails",
  "containers.Edit.submit": "Valider",
  "containers.EditSettingsView.modal-form.edit-field": "Editer le champ",
  "containers.EditView.add.new-entry": "Ajouter une nouvelle entrée",
  "containers.EditView.notification.errors": "Le formulaire contient des erreurs",
  "containers.Home.introduction": "Pour éditer du contenu, choisissez un type de données dans le menu de gauche.",
  "containers.Home.pluginHeaderDescription": "Créer et modifier votre type de contenu",
  "containers.Home.pluginHeaderTitle": "Type de contenu",
  "containers.List.draft": "Brouillon",
  "containers.List.errorFetchRecords": "Erreur",
  "containers.List.published": "Publié",
  "containers.list.displayedFields": "Champs affichés",
  "containers.list.items": "{number, plural, =0 {élements} one {élement} other {élements}}",
  "containers.list.table-headers.publishedAt": "Statut",
  "containers.ListSettingsView.modal-form.edit-label": "Editer le label",
  "containers.SettingPage.add.field": "Insérer un autre champ",
  "containers.SettingPage.attributes": "Attributs",
  "containers.SettingPage.attributes.description": "Organisez les attributs du modèle",
  "containers.SettingPage.editSettings.description": "Glissez & déposez les champs pour construire le layout",
  "containers.SettingPage.editSettings.entry.title": "Nom de l'entrée",
  "containers.SettingPage.editSettings.entry.title.description": "Définissez quel champ sera affiché",
  "containers.SettingPage.editSettings.relation-field.description": "Définir le champ affiché dans les vues d'édition et de liste",
  "containers.SettingPage.editSettings.title": "Vue edit (paramètres)",
  "containers.SettingPage.layout": "Layout",
  "containers.SettingPage.listSettings.description": "Configurez les options de ce modèle",
  "containers.SettingPage.listSettings.title": "Vue liste (paramètres)",
  "containers.SettingPage.pluginHeaderDescription": "Configurez les paramètres de ce modèle",
  "containers.SettingPage.settings": "Paramètres",
  "containers.SettingPage.view": "Vue",
  "containers.SettingViewModel.pluginHeader.title": "Gestion du contenu - {name}",
  "containers.SettingsPage.Block.contentType.description": "Configurez les paramètres spécifiques",
  "containers.SettingsPage.Block.contentType.title": "Types de collection",
  "containers.SettingsPage.Block.generalSettings.description": "Configurez les options par défault de vos modèles",
  "containers.SettingsPage.Block.generalSettings.title": "Général",
  "containers.SettingsPage.pluginHeaderDescription": "Configurez les paramètres de vos modèles et groupes",
  "containers.SettingsView.list.subtitle": "Configurez le layout et l'affichage de vos types de collection et groupes",
  "containers.SettingsView.list.title": "Paramètres d'affichage",
  "edit-settings-view.link-to-ctb.components": "Modifier le composant",
  "edit-settings-view.link-to-ctb.content-types": "Modifier le type de contenu",
  "emptyAttributes.button": "Ouvrir le constructeur de types de contenu",
  "emptyAttributes.description": "Ajoutez votre premier champ a votre modèle",
  "emptyAttributes.title": "Il n'y a pas encore de champs",
  "error.attribute.key.taken": "Cette valeur existe déjà",
  "error.attribute.sameKeyAndName": "Ne peuvent pas être égaux",
  "error.attribute.taken": "Ce champ existe déjà",
  "error.contentTypeName.taken": "Ce nom existe déjà",
  "error.model.fetch": "Une erreur est survenue lors de la réception des modèles.",
  "error.record.create": "Une erreur est survenue lors de la création de l'entrée.",
  "error.record.delete": "Une erreur est survenue lors de la suppression de l'entrée.",
  "error.record.fetch": "Une erreur est survenue lors de la réception de l'entrée.",
  "error.record.update": "Une erreur est survenue lors de la modification de l'entrée.",
  "error.records.count": "Une erreur est survenue lors de la réception du nombre d'entrées.",
  "error.records.fetch": "Une erreur est survenue lors de la réception des entrées.",
  "error.schema.generation": "Une erreur est survenue lors de la génération du schéma.",
  "error.validation.json": "Le format JSON n'est pas respecté",
  "error.validation.max": "La valeur est trop grande.",
  "error.validation.maxLength": "La valeur est trop longue.",
  "error.validation.min": "La valeur est trop basse.",
  "error.validation.minLength": "La valeur est trop courte.",
  "error.validation.minSupMax": "Ne peut pas être plus grand",
  "error.validation.regex": "La valeur ne correspond pas au format attendu.",
  "error.validation.required": "Ce champ est obligatoire.",
  "form.Input.bulkActions": "Autoriser les actions groupées",
  "form.Input.defaultSort": "Attribut de tri par défault",
  "form.Input.description": "Description",
  "form.Input.description.placeholder": "Afficher le nom dans le profil",
  "form.Input.editable": "Champ editable",
  "form.Input.filters": "Autoriser les filtres",
  "form.Input.label": "Label",
  "form.Input.label.inputDescription": "Cette valeur modifie celle du champs de la table",
  "form.Input.pageEntries": "Nombre d'entrées par page",
  "form.Input.pageEntries.inputDescription": "Note : Vous pouvez modifier ces valeurs par modèle",
  "form.Input.placeholder": "Placeholder",
  "form.Input.placeholder.placeholder": "Mon super placeholder",
  "form.Input.search": "Autoriser la recherche",
  "form.Input.search.field": "Autoriser la recherche sur ce champs",
  "form.Input.sort.field": "Autoriser le tri sur ce champs",
  "form.Input.sort.order": "Ordre de tri par défaut",
  "form.Input.wysiwyg": "Afficher comme WYSIWYG",
  "global.displayedFields": "Champs affichés",
  groups,
  "groups.numbered": "Groupes ({number})",
  "header.name": "Contenu",
  "link-to-ctb": "Editer le modèle",
  models,
  "models.numbered": "Types de Collection ({number})",
  "notification.error.displayedFields": "Vous devez avoir au moins un champ d'affiché",
  "notification.error.relationship.fetch": "Une erreur est survenue en récupérant les relations.",
  "notification.info.SettingPage.disableSort": "Vous devez avoir au moins un attribut de tri par défaut",
  "notification.info.minimumFields": "Vous devez avoir au moins un champ d'affiché",
  "notification.upload.error": "Une erreur est survenues en téléchargeant vos fichiers",
  pageNotFound,
  "pages.ListView.header-subtitle": "{number, plural, =0 {# entrées trouvée} one {# entrée trouvée} other {# entrées trouvées}}",
  "pages.NoContentType.button": "Créer votre premier Type de Contenu",
  "pages.NoContentType.text": "Vous n'avez encore aucun contenu, nous vous recommandons de créer votre premier Type de Contenu",
  "permissions.not-allowed.create": "Vous n'êtes pas autorisé à créer un document",
  "permissions.not-allowed.update": "Vous n'êtes pas autorisé à voir ce document",
  "plugin.description.long": "Visualisez, modifiez et supprimez les données de votre base de données.",
  "plugin.description.short": "Visualisez, modifiez et supprimez les données de votre base de données.",
  "popover.display-relations.label": "Afficher les relations",
  "relation.add": "Ajouter une relation",
  "relation.disconnect": "Supprimer",
  "relation.isLoading": "Chargement des relations en cours",
  "relation.loadMore": "Charger davantage",
  "relation.notAvailable": "Aucune relation disponible",
  "relation.publicationState.draft": "Brouillon",
  "relation.publicationState.published": "Publiée",
  "select.currently.selected": "{count} actuellement sélectionnées",
  "success.record.delete": "Supprimé",
  "success.record.publish": "Publié",
  "success.record.save": "Sauvegardé",
  "success.record.unpublish": "Publication annulée",
  "utils.data-loaded": "{number, plural, =1 {L'entrée a été chargée} other {Les entrées on été chargées} avec  succès",
  "apiError.This attribute must be unique": "Le champ {field} doit être unique",
  "popUpWarning.warning.publish-question": "Êtes-vous sûr de vouloir le publier ?",
  "popUpwarning.warning.has-draft-relations.button-confirm": "Oui, publier",
  "popUpwarning.warning.has-draft-relations.message": "<b>{count, plural, =0 { des relations de votre contenu n'est} one { des relations de votre contenu n'est} other { des relations de votre contenu ne sont}}</b> pas publié actuellement.<br></br>Cela peut engendrer des liens cassés ou des erreurs dans votre projet.",
  "history.sidebar.show-newer": "Voir les versions récentes",
  "history.sidebar.show-older": "Voir les anciennes versions",
  "history.content.new-field.message": "Ce champ n'existait pas lorsque cette version a été sauvegardée. Si vous restaurez cette version, il sera vide.",
  "history.content.unknown-fields.message": "Ces champs ont été supprimés ou renommés dans le Content-Type Builder. <b>Ces champs ne seront pas restaurés.</b>",
  "history.content.no-relations": "Aucune relation.",
  "history.restore.confirm.button": "Restaurer",
  "history.restore.confirm.title": "Êtes-vous sûr de vouloir restaurer cette version ?",
  "history.restore.confirm.message": "{isDraft, select, true {Le contenu restauré écrasera votre brouillon.} other {Le contenu restauré ne sera pas publié, il écrasera le brouillon et sera sauvegardé en tant que changement en attente de publication. Vous pourrez publier les changements à tout moment.}}",
  "history.restore.success.title": "Version restaurée.",
  "history.restore.success.message": "Le contenu de la version restaurée n'a pas encore été publié."
};
export {
  fr as default,
  groups,
  models,
  pageNotFound
};
//# sourceMappingURL=fr.json-NP6DDXJO.js.map
