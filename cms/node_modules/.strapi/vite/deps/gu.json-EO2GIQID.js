import "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/admin/src/translations/gu.json.mjs
var Analytics = "એનાલિટિક્સ";
var anErrorOccurred = "ઉફ્ફ! કંઈક ખોટું થયું. કૃપા કરીને, ફરી પ્રયાસ કરો.";
var clearLabel = "સાફ કરો";
var skipToContent = "સામગ્રી પર જાઓ";
var submit = "સબમિટ કરો";
var gu = {
  Analytics,
  "Auth.components.Oops.text": "તમારું એકાઉન્ટ સસ્પેન્ડ કરવામાં આવ્યું છે.",
  "Auth.components.Oops.text.admin": "જો આ ભૂલ છે, તો કૃપા કરીને તમારા વ્યવસ્થાપકનો સંપર્ક કરો",
  "Auth.components.Oops.title": "અરે...",
  "Auth.form.button.forgot-password": "ઈ - મેલ મોકલો",
  "Auth.form.button.go-home": "ઘરે પાછા જાવ",
  "Auth.form.button.login": "રવેશ કરો",
  "Auth.form.button.login.providers.error": "અમે તમને પસંદ કરેલ પ્રદાતા દ્વારા કનેક્ટ કરી શકતા નથી",
  "Auth.form.button.login.strapi": "સ્ટ્રેપી દ્વારા લૉગ ઇન કરો",
  "Auth.form.button.password-recovery": "પાસવર્ડ પુનઃપ્રાપ્તિ",
  "Auth.form.button.register": "ચાલો શરૂ કરીએ",
  "Auth.form.confirmPassword.label": "પુષ્ટિકરણ પાસવર્ડ",
  "Auth.form.currentPassword.label": "અત્યારનો પાસવર્ડ",
  "Auth.form.email.label": "ઈમેલ",
  "Auth.form.email.placeholder": "દા.ત. кай@дое.цум",
  "Auth.form.error.blocked": "તમારું એકાઉન્ટ એડમિનિસ્ટ્રેટર દ્વારા અવરોધિત કરવામાં આવ્યું છે",
  "Auth.form.error.code.provide": "ખોટો કોડ આપેલ છે",
  "Auth.form.error.confirmed": "તમારા એકાઉન્ટ ઈમેલની પુષ્ટિ થઈ નથી",
  "Auth.form.error.email.invalid": "આ ઈમેલ અમાન્ય છે",
  "Auth.form.error.email.provide": "કૃપા કરીને તમારું વપરાશકર્તા નામ અથવા તમારું ઇમેઇલ પ્રદાન કરો",
  "Auth.form.error.email.taken": "ઇમેલ અગાઉ લેવાઇ ચુક્યું છે",
  "Auth.form.error.invalid": "ઓળખકર્તા અથવા પાસવર્ડ અમાન્ય",
  "Auth.form.error.params.provide": "અયોગ્ય પરિમાણો પ્રદાન કરવામાં આવ્યા છે",
  "Auth.form.error.password.format": "તમારા પાસવર્ડમાં ત્રણ કરતા વધુ વખત `$` ચિહ્ન ન હોઈ શકે",
  "Auth.form.error.password.local": "આ વપરાશકર્તાએ ક્યારેય સ્થાનિક પાસવર્ડ સેટ કર્યો નથી, કૃપા કરીને એકાઉન્ટ બનાવતી વખતે ઉપયોગમાં લેવાતા પ્રદાતા દ્વારા લૉગિન કરો",
  "Auth.form.error.password.matching": "પાસવર્ડ મેળ ખાતા નથી",
  "Auth.form.error.password.provide": "કૃપા કરીને તમારો પાસવર્ડ આપો",
  "Settings.permissions.conditions.conditions": "શરતો વ્યાખ્યાયિત કરો",
  "Settings.permissions.conditions.links": "લિંક્સ",
  "Settings.permissions.conditions.no-actions": "તમારે તેના પર શરતો વ્યાખ્યાયિત કરતા પહેલા ક્રિયાઓ (બનાવો, વાંચો, અપડેટ કરો, ...) પસંદ કરો.",
  "Settings.permissions.conditions.none-selected": "કોઈપણ સમયે",
  "Settings.permissions.conditions.or": "અથવા",
  "Settings.permissions.conditions.when": "ક્યારે",
  "settings.permissions.select-all-by-permission": "બધી {label} પરવાનગીઓ પસંદ કરો",
  "settings.permissions.select-by-permission": "{label} પરવાનગી પસંદ કરો",
  "Settings.permissions.users.create": "નવા વપરાશકર્તાને આમંત્રિત કરો",
  "Settings.permissions.users.email": "ઈમેલ",
  "Settings.permissions.users.firstname": "પ્રથમ નામ",
  "Settings.permissions.users.lastname": "છેલ્લું નામ",
  "Settings.permissions.users.form.sso": "SSO સાથે કનેક્ટ કરો",
  "Settings.permissions.users.form.sso.description": "જ્યારે સક્ષમ (ચાલુ) હોય, ત્યારે વપરાશકર્તાઓ SSO દ્વારા લૉગિન કરી શકે છે",
  "Settings.permissions.users.listview.header.subtitle": "સ્ટ્રેપી એડમિન પેનલની ઍક્સેસ ધરાવતા તમામ વપરાશકર્તાઓ",
  "Settings.permissions.users.tabs.label": "ટેબ પરવાનગીઓ",
  "Settings.profile.form.notify.data.loaded": "તમારો પ્રોફાઇલ ડેટા લોડ કરવામાં આવ્યો છે",
  "Settings.profile.form.section.experience.clear.select": "પસંદ કરેલ ઈન્ટરફેસ ભાષા સાફ કરો",
  "Settings.profile.form.section.experience.here": "અહીં",
  "Settings.profile.form.section.experience.interfaceLanguage": "ઇન્ટરફેસ ભાષા",
  "Settings.profile.form.section.experience.interfaceLanguage.hint": "આ ફક્ત પસંદ કરેલી ભાષામાં તમારું પોતાનું ઈન્ટરફેસ પ્રદર્શિત કરશે.",
  "Settings.profile.form.section.experience.interfaceLanguageHelp": "પસંદગીના ફેરફારો ફક્ત તમને જ લાગુ થશે. વધુ માહિતી {અહીં} ઉપલબ્ધ છે.",
  "Settings.profile.form.section.experience.mode.label": "ઇન્ટરફેસ મોડ",
  "Settings.profile.form.section.experience.mode.hint": "તમારું ઇન્ટરફેસ પસંદ કરેલ મોડમાં દર્શાવે છે.",
  "Settings.profile.form.section.experience.mode.option-label": "{name} મોડ",
  "Settings.profile.form.section.experience.title": "અનુભવ",
  "Settings.profile.form.section.head.title": "વપરાશકર્તા પ્રોફાઇલ",
  "Settings.profile.form.section.profile.page.title": "પ્રોફાઇલ પૃષ્ઠ",
  "Settings.roles.create.description": "ભૂમિકાને આપવામાં આવેલા અધિકારોને વ્યાખ્યાયિત કરો",
  "Settings.roles.create.title": "કોઈ ભૂમિકા બનાવો",
  "Settings.roles.created": "ભૂમિકા બનાવી",
  "Settings.roles.edit.title": "કોઈ ભૂમિકા સંપાદિત કરો",
  "Settings.roles.form.button.users-with-role": "{સંખ્યા, બહુવચન, =0 {# વપરાશકર્તાઓ} એક {# વપરાશકર્તા} અન્ય {# વપરાશકર્તાઓ}} આ ભૂમિકા સાથે",
  "Settings.roles.form.created": "બનાવ્યું",
  "Settings.roles.form.description": "ભૂમિકાનું નામ અને વર્ણન",
  "Settings.roles.form.permission.property-label": "{label} પરવાનગીઓ",
  "Settings.roles.form.permissions.attributesPermissions": "ક્ષેત્ર પરવાનગીઓ",
  "Settings.roles.form.permissions.create": "બનાવો",
  "Settings.roles.form.permissions.delete": "કાઢી નાખો",
  "Settings.roles.form.permissions.publish": "પ્રકાશિત કરો",
  "Settings.roles.form.permissions.read": "વાંચો",
  "Settings.roles.form.permissions.update": "અપડેટ",
  "Settings.roles.list.button.add": "નવી ભૂમિકા ઉમેરો",
  "Settings.roles.list.description": "ભૂમિકાઓની યાદી",
  "Settings.roles.title.singular": "ભૂમિકા",
  "Settings.sso.description": "સિંગલ સાઇન-ઓન સુવિધા માટે સેટિંગ્સને ગોઠવો.",
  "Settings.sso.form.defaultRole.description": "તે નવા પ્રમાણિત વપરાશકર્તાને પસંદ કરેલ ભૂમિકા સાથે જોડશે",
  "Settings.sso.form.defaultRole.description-not-allowed": "તમારી પાસે એડમિન ભૂમિકાઓ વાંચવા માટે પરવાનગી હોવી જરૂરી છે",
  "Settings.sso.form.defaultRole.label": "મૂળભૂત ભૂમિકા",
  "Settings.sso.form.registration.description": "જો કોઈ એકાઉન્ટ અસ્તિત્વમાં ન હોય તો SSO લૉગિન પર નવો વપરાશકર્તા બનાવો",
  "Settings.sso.form.registration.label": "ઓટો-નોંધણી",
  "Settings.sso.title": "સિંગલ સાઇન-ઓન",
  "Settings.webhooks.create": "વેબહુક બનાવો",
  "Settings.webhooks.create.header": "નવું હેડર બનાવો",
  "Settings.webhooks.created": "વેબહુક બનાવ્યું",
  "Settings.webhooks.event.publish-tooltip": "આ ઇવેન્ટ ફક્ત ડ્રાફ્ટ/પ્રકાશિત સિસ્ટમ સક્ષમ કરેલ સામગ્રીઓ માટે જ અસ્તિત્વમાં છે",
  "Settings.webhooks.events.create": "બનાવો",
  "Settings.webhooks.events.update": "અપડેટ",
  "Settings.webhooks.form.events": "ઇવેન્ટ્સ",
  "Settings.webhooks.form.headers": "હેડર",
  "Settings.webhooks.form.url": "Url",
  "Settings.webhooks.headers.remove": "હેડર પંક્તિ {નંબર} દૂર કરો",
  "Settings.webhooks.key": "કી",
  "Settings.webhooks.list.button.add": "નવું વેબહુક બનાવો",
  "Settings.webhooks.list.description": "POST ફેરફારોની સૂચનાઓ મેળવો",
  "Settings.webhooks.list.empty.description": "કોઈ વેબહુક્સ મળ્યા નથી",
  "Settings.webhooks.list.empty.link": "અમારા દસ્તાવેજો જુઓ",
  "Settings.webhooks.list.empty.title": "હજી સુધી કોઈ વેબહુક્સ નથી",
  "Settings.webhooks.list.th.actions": "ક્રિયાઓ",
  "Settings.webhooks.list.th.status": "સ્ટેટસ",
  "Settings.webhooks.singular": "webhook",
  "Settings.webhooks.title": "વેબહુક્સ",
  "Settings.webhooks.to.delete": "{webhooksToDeleteLength, બહુવચન, એક {# સંપત્તિ} અન્ય {# સંપત્તિ}} પસંદ કરેલ",
  "Settings.webhooks.trigger": "ટ્રિગર",
  "Settings.webhooks.trigger.cancel": "ટ્રિગર રદ કરો",
  "Settings.webhooks.trigger.pending": "બાકી…",
  "Settings.webhooks.trigger.save": "કૃપા કરીને ટ્રિગર કરવા માટે સાચવો",
  "Settings.webhooks.trigger.success": "સફળતા!",
  "Settings.webhooks.trigger.success.label": "ટ્રિગર સફળ થયું",
  "Settings.webhooks.trigger.test": "ટેસ્ટ-ટ્રિગર",
  "Settings.webhooks.trigger.title": "ટ્રિગર પહેલા સાચવો",
  "Settings.webhooks.value": "મૂલ્ય",
  "Usecase.back-end": "બેક-એન્ડ ડેવલપર",
  "Usecase.button.skip": "આ પ્રશ્ન છોડો",
  "Usecase.content-creator": "સામગ્રી નિર્માતા",
  "Usecase.front-end": "ફ્રન્ટ-એન્ડ ડેવલપર",
  "Usecase.full-stack": "ફુલ-સ્ટેક ડેવલપર",
  "usecase.input.work-type": "તમે કયા પ્રકારનું કામ કરો છો?",
  "usecase.notification.success.project-created": "પ્રોજેક્ટ સફળતાપૂર્વક બનાવવામાં આવ્યો છે",
  "Usecase.other": "અન્ય",
  "Usecase.title": "તમારા વિશે અમને થોડું વધુ કહો",
  "વપરાશકર્તા નામ": "વપરાશકર્તા નામ",
  "વપરાશકર્તાઓ": "વપરાશકર્તાઓ",
  "વપરાશકર્તાઓ અને પરવાનગીઓ": "વપરાશકર્તાઓ અને પરવાનગીઓ",
  "Users.components.List.empty": "ત્યાં કોઈ વપરાશકર્તાઓ નથી...",
  "Users.components.List.empty.withFilters": "લાગુ કરેલ ફિલ્ટર્સ સાથે કોઈ વપરાશકર્તા નથી...",
  "Users.components.List.empty.withSearch": "શોધને અનુરૂપ કોઈ વપરાશકર્તાઓ નથી ({search})...",
  "admin.pages.MarketPlacePage.head": "માર્કેટપ્લેસ - પ્લગઇન્સ",
  "admin.pages.MarketPlacePage.offline.title": "તમે ઑફલાઇન છો",
  "admin.pages.MarketPlacePage.offline.subtitle": "સ્ટ્રેપી માર્કેટને ઍક્સેસ કરવા માટે તમારે ઇન્ટરનેટ સાથે કનેક્ટેડ હોવું જરૂરી છે.",
  "admin.pages.MarketPlacePage.plugin.copy": "ઇન્સ્ટોલ આદેશની નકલ કરો",
  "admin.pages.MarketPlacePage.plugin.copy.success": "તમારા ટર્મિનલમાં પેસ્ટ કરવા માટે તૈયાર આદેશ ઇન્સ્ટોલ કરો",
  "admin.pages.MarketPlacePage.plugin.info": "વધુ જાણો",
  "admin.pages.MarketPlacePage.plugin.info.label": "{pluginName} વિશે વધુ જાણો",
  "admin.pages.MarketPlacePage.plugin.info.text": "વધુ જાણો",
  "admin.pages.MarketPlacePage.plugin.installed": "ઇન્સ્ટોલ કરેલ",
  "admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi": "સ્ટ્રેપી દ્વારા બનાવેલ",
  "admin.pages.MarketPlacePage.plugin.tooltip.verified": "સ્ટ્રેપી દ્વારા ચકાસાયેલ પ્લગઇન",
  "admin.pages.MarketPlacePage.search.clear": "પ્લગઇન શોધ સાફ કરો",
  "admin.pages.MarketPlacePage.search.empty": '"{target}" માટે કોઈ પરિણામ નથી',
  "admin.pages.MarketPlacePage.search.placeholder": "પ્લગઇન માટે શોધો",
  "admin.pages.MarketPlacePage.submit.plugin.link": "તમારું પ્લગઇન સબમિટ કરો",
  "admin.pages.MarketPlacePage.subtitle": "સ્ટ્રેપીમાંથી વધુ મેળવો",
  "admin.pages.MarketPlacePage.missingPlugin.title": "પ્લગઇન ખૂટે છે?",
  "admin.pages.MarketPlacePage.missingPlugin.description": "તમે કયું પ્લગઇન શોધી રહ્યાં છો તે અમને કહો અને અમે અમારા સમુદાય પ્લગઇન ડેવલપર્સને જણાવીશું કે તેઓ પ્રેરણાની શોધમાં હોય તો!",
  anErrorOccurred,
  "app.component.CopyToClipboard.label": "ક્લિપબોર્ડ પર કૉપિ કરો",
  "app.component.search.label": "{target} માટે શોધો",
  "app.component.table.duplicate": "ડુપ્લિકેટ {target}",
  "app.component.table.edit": "સંપાદિત કરો {target}",
  "app.component.table.select.one-entry": "{target} પસંદ કરો",
  "app.components.BlockLink.blog": "બ્લોગ",
  "app.components.BlockLink.blog.content": "સ્ટ્રેપી અને ઇકોસિસ્ટમ વિશે નવીનતમ સમાચાર વાંચો.",
  "app.components.BlockLink.code": "કોડ ઉદાહરણો",
  "app.components.BlockLink.code.content": "સમુદાય દ્વારા વિકસિત વાસ્તવિક પ્રોજેક્ટ્સનું પરીક્ષણ કરીને શીખો.",
  "app.components.BlockLink.documentation.content": "આવશ્યક ખ્યાલો, માર્ગદર્શિકાઓ અને સૂચનાઓ શોધો.",
  "app.components.BlockLink.tutorial": "ટ્યુટોરિયલ્સ",
  "app.components.BlockLink.tutorial.content": "સ્ટ્રેપીનો ઉપયોગ કરવા અને કસ્ટમાઇઝ કરવા માટે પગલું-દર-પગલાં સૂચનોને અનુસરો.",
  "app.components.Button.cancel": "રદ કરો",
  "app.components.Button.confirm": "પુષ્ટિ કરો",
  "app.components.Button.reset": "રીસેટ કરો",
  "app.components.ComingSoonPage.comingSoon": "ટૂંક સમયમાં આવી રહ્યું છે",
  "app.components.ConfirmDialog.title": "પુષ્ટિ",
  "app.components.DownloadInfo.download": "ડાઉનલોડ ચાલુ છે...",
  "app.components.DownloadInfo.text": "આમાં એક મિનિટ લાગી શકે છે. તમારી ધીરજ બદલ આભાર.",
  "app.components.EmptyAttributes.title": "હજી સુધી કોઈ ફીલ્ડ નથી",
  "app.components.EmptyStateLayout.content-document": "કોઈ સામગ્રી મળી નથી",
  "app.components.EmptyStateLayout.content-permissions": "તમારી પાસે તે સામગ્રીને ઍક્સેસ કરવાની પરવાનગીઓ નથી",
  "app.components.GuidedTour.CM.create.content": "<p>અહીં કન્ટેન્ટ મેનેજરમાં તમામ કન્ટેન્ટ બનાવો અને મેનેજ કરો.</p><p>ઉદા.: બ્લોગ વેબસાઈટના ઉદાહરણને આગળ લઈએ તો, કોઈ એક લખી શકે છે. તેમને ગમે તે રીતે લેખ, સાચવો અને પ્રકાશિત કરો.</p><p>💡 ઝડપી ટીપ - તમે જે સામગ્રી બનાવો છો તેના પર પ્રકાશિત કરવાનું ભૂલશો નહીં.</p>",
  "app.components.GuidedTour.CM.create.title": "⚡️ સામગ્રી બનાવો",
  "app.components.GuidedTour.CM.success.content": "<p>અદ્ભુત, જવા માટેનું એક છેલ્લું પગલું!</p><b>🚀 ક્રિયામાં સામગ્રી જુઓ</b>",
  "app.components.GuidedTour.CM.success.cta.title": "API નું પરીક્ષણ કરો",
  "app.components.GuidedTour.CM.success.title": "પગલું 2: પૂર્ણ ✅",
  "app.components.GuidedTour.CTB.create.content": "<p>સંગ્રહના પ્રકારો તમને ઘણી એન્ટ્રીઓનું સંચાલન કરવામાં મદદ કરે છે, સિંગલ પ્રકારો માત્ર એક જ એન્ટ્રીને મેનેજ કરવા માટે યોગ્ય છે.</p> <p>ઉદા.: બ્લોગ વેબસાઇટ માટે, લેખો એક સંગ્રહ પ્રકાર હશે જ્યારે હોમપેજ એક પ્રકારનું હશે.</p>",
  "app.components.GuidedTour.CTB.create.cta.title": "સંગ્રહ પ્રકાર બનાવો",
  "app.components.GuidedTour.CTB.create.title": "🧠 પ્રથમ કલેક્શન પ્રકાર બનાવો",
  "app.components.GuidedTour.CTB.success.content": "<p>સારું ચાલી રહ્યું છે!</p><b>⚡️ તમે વિશ્વ સાથે શું શેર કરવા માંગો છો?</b>",
  "app.components.GuidedTour.CTB.success.title": "પગલું 1: પૂર્ણ ✅",
  "app.components.GuidedTour.apiTokens.create.content": "<p>અહીં એક પ્રમાણીકરણ ટોકન જનરેટ કરો અને તમે હમણાં જ બનાવેલ સામગ્રી પુનઃપ્રાપ્ત કરો.</p>",
  "app.components.GuidedTour.apiTokens.create.cta.title": "એક API ટોકન બનાવો",
  "app.components.GuidedTour.apiTokens.create.title": "🚀 ક્રિયામાં સામગ્રી જુઓ",
  "app.components.GuidedTour.apiTokens.success.content": "<p>એક HTTP વિનંતી કરીને સામગ્રીને ક્રિયામાં જુઓ:</p><ul><li><p>આ URL પર: <light>https: //'<'YOUR_DOMAIN'>'/api/'<'YOUR_CT'>'</light></p></li><li><p>હેડર સાથે: <light>અધિકૃતતા: વાહક '<' YOUR_API_TOKEN'>'</light></p></li></ul><p>સામગ્રી સાથે ક્રિયાપ્રતિક્રિયા કરવાની વધુ રીતો માટે, <documentationLink>દસ્તાવેજીકરણ</documentationLink> જુઓ.</p>",
  "app.components.GuidedTour.apiTokens.success.cta.title": "હોમપેજ પર પાછા જાઓ",
  "app.components.GuidedTour.apiTokens.success.title": "પગલું 3: પૂર્ણ ✅",
  "app.components.GuidedTour.create-content": "સામગ્રી બનાવો",
  "app.components.GuidedTour.home.CM.title": "⚡️ તમે વિશ્વ સાથે શું શેર કરવા માંગો છો?",
  "app.components.GuidedTour.home.CTB.cta.title": "સામગ્રી પ્રકાર બિલ્ડર પર જાઓ",
  "app.components.GuidedTour.home.CTB.title": "🧠 સામગ્રી માળખું બનાવો",
  "app.components.GuidedTour.home.apiTokens.cta.title": "API નું પરીક્ષણ કરો",
  "app.components.GuidedTour.skip": "ટૂર છોડો",
  "app.components.GuidedTour.title": "પ્રારંભ કરવા માટે 3 પગલાં",
  "app.components.HomePage.button.blog": "બ્લોગ પર વધુ જુઓ",
  "app.components.HomePage.community": "સમુદાયમાં જોડાઓ",
  "app.components.HomePage.community.content": "વિવિધ ચેનલો પર ટીમના સભ્યો, યોગદાનકર્તાઓ અને વિકાસકર્તાઓ સાથે ચર્ચા કરો.",
  "app.components.HomePage.create": "તમારો પ્રથમ સામગ્રી પ્રકાર બનાવો",
  "app.components.HomePage.roadmap": "અમારો રોડમેપ જુઓ",
  "app.components.HomePage.welcome": "બોર્ડ પર આપનું સ્વાગત છે 👋",
  "app.components.HomePage.welcome.again": "સ્વાગત છે 👋",
  "app.components.HomePage.welcomeBlock.content": "અભિનંદન! તમે પ્રથમ એડમિનિસ્ટ્રેટર તરીકે લૉગ ઇન થયા છો. સ્ટ્રેપી દ્વારા પ્રદાન કરવામાં આવેલ શક્તિશાળી સુવિધાઓ શોધવા માટે, અમે તમને તમારો પ્રથમ સામગ્રી પ્રકાર બનાવવાની ભલામણ કરીએ છીએ!",
  "app.components.HomePage.welcomeBlock.content.again": "અમને આશા છે કે તમે તમારા પ્રોજેક્ટમાં પ્રગતિ કરી રહ્યાં છો! સ્ટ્રેપી વિશેના નવીનતમ સમાચાર વાંચવા માટે નિઃસંકોચ. અમે તમારા પ્રતિસાદના આધારે ઉત્પાદનને સુધારવા માટે અમારા શ્રેષ્ઠ પ્રયાસો આપી રહ્યા છીએ.",
  "app.components.HomePage.welcomeBlock.content.issues": "સમસ્યાઓ.",
  "app.components.HomePage.welcomeBlock.content.raise": "અથવા વધારો",
  "app.components.ImgPreview.hint": "તમારી ફાઇલને આ વિસ્તારમાં ખેંચો અને છોડો અથવા ફાઇલ અપલોડ કરવા માટે {બ્રાઉઝ કરો",
  "app.components.ImgPreview.hint.browse": "બ્રાઉઝ કરો",
  "app.components.InputFile.newFile": "નવી ફાઇલ ઉમેરો",
  "app.components.InputFileDetails.open": "નવી ટેબમાં ખોલો",
  "app.components.InputFileDetails.originalName": "મૂળ નામ:",
  "app.components.InputFileDetails.remove": "આ ફાઇલને દૂર કરો",
  "app.components.InputFileDetails.size": "કદ:",
  "app.components.InstallPluginPage.Download.description": "પ્લગઇનને ડાઉનલોડ અને ઇન્સ્ટોલ કરવામાં થોડીક સેકન્ડ લાગી શકે છે.",
  "app.components.InstallPluginPage.Download.title": "ડાઉનલોડ કરી રહ્યું છે...",
  "app.components.InstallPluginPage.description": "તમારી એપ્લિકેશનને વિના પ્રયાસે વિસ્તૃત કરો.",
  "app.components.LeftMenu.collapse": "નવબારને સંકુચિત કરો",
  "app.components.LeftMenu.expand": "નવબાર વિસ્તૃત કરો",
  "app.components.LeftMenu.logout": "લોગઆઉટ",
  "app.components.LeftMenu.trialCountdown": "તમારી પ્રયાસ અંતમાં {date} પર થાય છે.",
  "app.components.LeftMenu.navbrand.title": "સ્ટ્રેપી ડેશબોર્ડ",
  "app.components.LeftMenu.navbrand.workplace": "કાર્યસ્થળ",
  "app.components.LeftMenuFooter.help": "સહાય",
  "app.components.LeftMenuFooter.poweredBy": "દ્વારા સંચાલિત",
  "app.components.LeftMenuLinkContainer.collectionTypes": "સંગ્રહના પ્રકાર",
  "app.components.LeftMenuLinkContainer.configuration": "રૂપરેખાંકનો",
  "app.components.LeftMenuLinkContainer.general": "સામાન્ય",
  "app.components.LeftMenuLinkContainer.noPluginsInstalled": "હજુ સુધી કોઈ પ્લગઈન્સ ઇન્સ્ટોલ કરેલ નથી",
  "app.components.LeftMenuLinkContainer.plugins": "પ્લગઇન્સ",
  "app.components.LeftMenuLinkContainer.singleTypes": "એક પ્રકાર",
  "app.components.ListPluginsPage.deletePlugin.description": "પ્લગઇનને અનઇન્સ્ટોલ કરવામાં થોડીક સેકન્ડ લાગી શકે છે.",
  "app.components.ListPluginsPage.deletePlugin.title": "અનઇન્સ્ટોલ કરી રહ્યું છે",
  "app.components.ListPluginsPage.description": "પ્રોજેક્ટમાં ઇન્સ્ટોલ કરેલ પ્લગિન્સની સૂચિ.",
  "app.components.ListPluginsPage.head.title": "પ્લગઇન્સની સૂચિ બનાવો",
  "app.components.Logout.logout": "લોગઆઉટ",
  "app.components.Logout.profile": "પ્રોફાઇલ",
  "app.components.MarketplaceBanner": "સમુદાય દ્વારા બનાવવામાં આવેલ પ્લગઇન્સ અને તમારા પ્રોજેક્ટને કિકસ્ટાર્ટ કરવા માટે ઘણી વધુ અદ્ભુત વસ્તુઓ, Strapi Awesome પર શોધો.",
  "app.components.MarketplaceBanner.image.alt": "એક સ્ટ્રેપી રોકેટ લોગો",
  "app.components.MarketplaceBanner.link": "હમણાં જ તપાસો",
  "app.components.NotFoundPage.back": "હોમપેજ પર પાછા",
  "app.components.NotFoundPage.description": "મળ્યું નથી",
  "app.components.Official": "સત્તાવાર",
  "app.components.Onboarding.help.button": "સહાય બટન",
  "app.components.Onboarding.label.completed": "% પૂર્ણ",
  "app.components.Onboarding.title": "પ્રારંભ કરો વિડિઓઝ",
  "app.components.PluginCard.Button.label.download": "ડાઉનલોડ કરો",
  "app.components.PluginCard.Button.label.install": "પહેલેથી જ ઇન્સ્ટોલ કરેલ છે",
  "app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed": "ઓટોરીલોડ સુવિધાને સક્ષમ કરવાની જરૂર છે. કૃપા કરીને તમારી એપ્લિકેશનને `યાર્ન ડેવલપ` સાથે પ્રારંભ કરો.",
  "app.components.PluginCard.PopUpWarning.install.impossible.confirm": "હું સમજું છું!",
  "app.components.PluginCard.PopUpWarning.install.impossible.environment": "સુરક્ષા કારણોસર, પ્લગઇન ફક્ત વિકાસ વાતાવરણમાં જ ડાઉનલોડ કરી શકાય છે.",
  "app.components.PluginCard.PopUpWarning.install.impossible.title": "ડાઉનલોડ કરવું અશક્ય છે",
  "app.components.PluginCard.compatible": "તમારી એપ્લિકેશન સાથે સુસંગત",
  "app.components.PluginCard.compatibleCommunity": "સમુદાય સાથે સુસંગત",
  "app.components.PluginCard.more-details": "વધુ વિગતો",
  "app.components.ToggleCheckbox.off-label": "ખોટું",
  "app.components.ToggleCheckbox.on-label": "True",
  "app.components.Users.MagicLink.connect": "આ વપરાશકર્તાને ઍક્સેસ આપવા માટે આ લિંક કૉપિ કરો અને શેર કરો",
  "app.components.Users.MagicLink.connect.sso": "વપરાશકર્તાને આ લિંક મોકલો, પ્રથમ લૉગિન SSO પ્રદાતા દ્વારા કરી શકાય છે",
  "app.components.Users.ModalCreateBody.block-title.details": "વપરાશકર્તા વિગતો",
  "app.components.Users.ModalCreateBody.block-title.roles": "વપરાશકર્તાની ભૂમિકા",
  "app.components.Users.ModalCreateBody.block-title.roles.description": "વપરાશકર્તા એક અથવા અનેક ભૂમિકાઓ ધરાવી શકે છે",
  "app.components.Users.SortPicker.button-label": "આ પ્રમાણે સૉર્ટ કરો",
  "app.components.Users.SortPicker.sortby.email_asc": "ઇમેઇલ (A થી Z)",
  "app.components.Users.SortPicker.sortby.email_desc": "ઈમેલ (Z થી A)",
  "app.components.Users.SortPicker.sortby.firstname_asc": "પ્રથમ નામ (A થી Z)",
  "app.components.Users.SortPicker.sortby.firstname_desc": "પ્રથમ નામ (Z થી A)",
  "app.components.Users.SortPicker.sortby.lastname_asc": "છેલ્લું નામ (A થી Z)",
  "app.components.Users.SortPicker.sortby.lastname_desc": "છેલ્લું નામ (Z થી A)",
  "app.components.Users.SortPicker.sortby.username_asc": "વપરાશકર્તા નામ (A થી Z)",
  "app.components.Users.SortPicker.sortby.username_desc": "વપરાશકર્તા નામ (Z થી A)",
  "app.components.listPlugins.button": "નવું પ્લગઇન ઉમેરો",
  "app.components.listPlugins.title.none": "કોઈ પ્લગઈન્સ ઇન્સ્ટોલ કરેલ નથી",
  "app.components.listPluginsPage.deletePlugin.error": "પ્લગઇનને અનઇન્સ્ટોલ કરતી વખતે ભૂલ આવી",
  "app.containers.App.notification.error.init": "એપીઆઈની વિનંતી કરતી વખતે ભૂલ આવી",
  "app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin": "જો તમને આ લિંક પ્રાપ્ત ન થાય, તો કૃપા કરીને તમારા વ્યવસ્થાપકનો સંપર્ક કરો.",
  "app.containers.AuthPage.ForgotPasswordSuccess.text.email": "તમારી પાસવર્ડ પુનઃપ્રાપ્તિ લિંક પ્રાપ્ત કરવામાં થોડી મિનિટો લાગી શકે છે.",
  "app.containers.AuthPage.ForgotPasswordSuccess.title": "ઈમેલ મોકલ્યો",
  "app.containers.Users.EditPage.form.active.label": "સક્રિય",
  "app.containers.Users.EditPage.header.label": "{name} સંપાદિત કરો",
  "app.containers.Users.EditPage.header.label-loading": "વપરાશકર્તા સંપાદિત કરો",
  "app.containers.Users.EditPage.roles-bloc-title": "એટ્રિબ્યુટેડ ભૂમિકાઓ",
  "app.containers.Users.ModalForm.footer.button-success": "વપરાશકર્તાને આમંત્રિત કરો",
  "app.links.configure-view": "દૃશ્યને ગોઠવો",
  "app.page.not.found": "અરેરે! તમે જે પૃષ્ઠ શોધી રહ્યાં છો તે અમે શોધી શકતા નથી...",
  "app.static.links.cheatsheet": "ચીટશીટ",
  "app.utils.SelectOption.defaultMessage": " ",
  "app.utils.add-filter": "ફિલ્ટર ઉમેરો",
  "app.utils.close-label": "બંધ કરો",
  "app.utils.defaultMessage": " ",
  "app.utils.duplicate": "ડુપ્લિકેટ",
  "app.utils.edit": "સંપાદિત કરો",
  "app.utils.errors.file-too-big.message": "ફાઇલ ખૂબ મોટી છે",
  "app.utils.filter-value": "ફિલ્ટર મૂલ્ય",
  "app.utils.filters": "ફિલ્ટર્સ",
  "app.utils.notify.data-loaded": "{target} લોડ થઈ ગયું છે",
  "app.utils.placeholder.defaultMessage": " ",
  "app.utils.publish": "પ્રકાશિત કરો",
  "app.utils.select-all": "બધા પસંદ કરો",
  "app.utils.select-field": "ક્ષેત્ર પસંદ કરો",
  "app.utils.select-filter": "ફિલ્ટર પસંદ કરો",
  "app.utils.unpublish": "અપ્રકાશિત કરો",
  clearLabel,
  "coming.soon": "આ સામગ્રી હાલમાં નિર્માણાધીન છે અને થોડા અઠવાડિયામાં પાછી આવશે!",
  "component.Input.error.validation.integer": "મૂલ્ય પૂર્ણાંક હોવું આવશ્યક છે",
  "components.AutoReloadBlocker.description": "નીચેના આદેશોમાંથી એક સાથે સ્ટ્રેપી ચલાવો:",
  "components.AutoReloadBlocker.header": "આ પ્લગઇન માટે રીલોડ સુવિધા જરૂરી છે.",
  "components.ErrorBoundary.title": "કંઈક ખોટું થયું...",
  "components.FilterOptions.FILTER_TYPES.$contains": "સમાવે છે",
  "components.FilterOptions.FILTER_TYPES.$containsi": "સમાવે છે (case insensitive)",
  "components.FilterOptions.FILTER_TYPES.$endsWith": "આની સાથે સમાપ્ત થાય છે",
  "components.FilterOptions.FILTER_TYPES.$endsWithi": "આની સાથે સમાપ્ત થાય છે (case insensitive)",
  "components.FilterOptions.FILTER_TYPES.$eq": "છે",
  "components.FilterOptions.FILTER_TYPES.$eqi": "છે (case insensitive)",
  "components.FilterOptions.FILTER_TYPES.$gt": "આના કરતા વધારે છે",
  "components.FilterOptions.FILTER_TYPES.$gte": "તેના કરતા વધારે અથવા બરાબર છે",
  "components.FilterOptions.FILTER_TYPES.$lt": "આના કરતા ઓછું છે",
  "components.FilterOptions.FILTER_TYPES.$lte": "તેના કરતા ઓછું અથવા બરાબર છે",
  "components.FilterOptions.FILTER_TYPES.$ne": "નથી",
  "components.FilterOptions.FILTER_TYPES.$nei": "નથી (case insensitive)",
  "components.FilterOptions.FILTER_TYPES.$notContains": "સમાવતું નથી",
  "components.FilterOptions.FILTER_TYPES.$notContainsi": "સમાવતું નથી (case insensitive)",
  "components.FilterOptions.FILTER_TYPES.$notNull": "નલ નથી",
  "components.FilterOptions.FILTER_TYPES.$null": "નલ છે",
  "components.FilterOptions.FILTER_TYPES.$startsWith": "આનાથી શરૂ થાય છે",
  "components.FilterOptions.FILTER_TYPES.$startsWithi": "આનાથી શરૂ થાય છે (case insensitive)",
  "components.Input.error.attribute.key.taken": "આ મૂલ્ય પહેલાથી જ અસ્તિત્વમાં છે",
  "components.Input.error.attribute.sameKeyAndName": "સમાન ન હોઈ શકે",
  "components.Input.error.attribute.taken": "આ ક્ષેત્રનું નામ પહેલેથી જ અસ્તિત્વમાં છે",
  "components.Input.error.contain.lowercase": "પાસવર્ડમાં ઓછામાં ઓછો એક લોઅરકેસ અક્ષર હોવો જોઈએ",
  "components.Input.error.contain.number": "પાસવર્ડમાં ઓછામાં ઓછો એક નંબર હોવો જોઈએ",
  "components.Input.error.contain.uppercase": "પાસવર્ડમાં ઓછામાં ઓછો એક અપરકેસ અક્ષર હોવો જોઈએ",
  "components.Input.error.contentTypeName.taken": "આ નામ પહેલેથી જ અસ્તિત્વમાં છે",
  "components.Input.error.custom-error": "{errorMessage}",
  "components.Input.error.password.noMatch": "પાસવર્ડ મેળ ખાતા નથી",
  "components.Input.error.validation.email": "આ અમાન્ય ઈમેલ છે",
  "components.Input.error.validation.json": "આ JSON ફોર્મેટ સાથે મેળ ખાતું નથી",
  "components.Input.error.validation.lowercase": "મૂલ્ય લોઅરકેસ સ્ટ્રિંગ હોવી જોઈએ",
  "components.Input.error.validation.max": "મૂલ્ય ખૂબ વધારે છે {max}.",
  "components.Input.error.validation.maxLength": "મૂલ્ય ખૂબ લાંબુ છે {max}.",
  "components.Input.error.validation.min": "મૂલ્ય ખૂબ ઓછું છે {min}.",
  "components.Input.error.validation.minLength": "મૂલ્ય ખૂબ ટૂંકું છે {min}.",
  "components.Input.error.validation.minSupMax": "બહેતર ન હોઈ શકે",
  "components.Input.error.validation.regex": "મૂલ્ય રેગેક્સ સાથે મેળ ખાતું નથી.",
  "components.Input.error.validation.required": "આ મૂલ્ય જરૂરી છે.",
  "components.Input.error.validation.unique": "આ મૂલ્ય પહેલેથી જ વપરાયેલ છે.",
  "components.InputSelect.option.placeholder": "અહીં પસંદ કરો",
  "components.ListRow.empty": "બતાવવા માટે કોઈ ડેટા નથી.",
  "components.NotAllowedInput.text": "આ ક્ષેત્ર જોવા માટે કોઈ પરવાનગી નથી",
  "components.OverlayBlocker.description": "તમે એવી સુવિધાનો ઉપયોગ કરી રહ્યાં છો કે જેને સર્વર પુનઃપ્રારંભ કરવાની જરૂર છે. કૃપા કરીને સર્વર ચાલુ થાય ત્યાં સુધી રાહ જુઓ.",
  "components.OverlayBlocker.description.serverError": "સર્વર પુનઃપ્રારંભ થયેલ હોવું જોઈએ, કૃપા કરીને ટર્મિનલમાં તમારા લોગ તપાસો.",
  "components.OverlayBlocker.title": "પુનઃશરૂ થવાની રાહ જોઈ રહ્યાં છીએ...",
  "components.OverlayBlocker.title.serverError": "પુનઃપ્રારંભમાં અપેક્ષા કરતા વધુ સમય લાગી રહ્યો છે",
  "components.PageFooter.select": "પ્રતિ પાનાની એન્ટ્રી",
  "components.ProductionBlocker.description": "સુરક્ષા હેતુઓ માટે અમારે અન્ય વાતાવરણમાં આ પ્લગઈનને અક્ષમ કરવું પડશે.",
  "components.ProductionBlocker.header": "આ પ્લગઇન ફક્ત વિકાસમાં જ ઉપલબ્ધ છે.",
  "components.Search.placeholder": "શોધો...",
  "components.TableHeader.sort": "{label} પર સૉર્ટ કરો",
  "components.Wysiwyg.ToggleMode.markdown-mode": "માર્કડાઉન મોડ",
  "components.Wysiwyg.ToggleMode.preview-mode": "પૂર્વાવલોકન મોડ",
  "components.Wysiwyg.collapse": "સંકુચિત કરો",
  "components.Wysiwyg.selectOptions.H1": "શીર્ષક H1",
  "components.Wysiwyg.selectOptions.H2": "શીર્ષક H2",
  "components.Wysiwyg.selectOptions.H3": "શીર્ષક H3",
  "components.Wysiwyg.selectOptions.H4": "શીર્ષક H4",
  "components.Wysiwyg.selectOptions.H5": "શીર્ષક H5",
  "components.Wysiwyg.selectOptions.H6": "શીર્ષક H6",
  "components.Wysiwyg.selectOptions.title": "શીર્ષક ઉમેરો",
  "components.WysiwygBottomControls.charactersIndicators": "અક્ષરો",
  "components.WysiwygBottomControls.fullscreen": "વિસ્તૃત કરો",
  "components.WysiwygBottomControls.uploadFiles": "ફાઈલોને ખેંચો અને છોડો, ક્લિપબોર્ડમાંથી પેસ્ટ કરો અથવા {બ્રાઉઝ કરો}.",
  "components.WysiwygBottomControls.uploadFiles.browse": "તેમને પસંદ કરો",
  "components.pagination.go-to": "પૃષ્ઠ {page} પર જાઓ",
  "components.pagination.go-to-next": "આગલા પૃષ્ઠ પર જાઓ",
  "components.pagination.go-to-previous": "પાછલા પૃષ્ઠ પર જાઓ",
  "components.pagination.remaining-links": "અને {નંબર} અન્ય લિંક્સ",
  "components.popUpWarning.button.cancel": "ના, રદ કરો",
  "components.popUpWarning.button.confirm": "હા, પુષ્ટિ કરો",
  "components.popUpWarning.message": "શું તમે ખરેખર આને કાઢી નાખવા માંગો છો?",
  "components.popUpWarning.title": "કૃપા કરીને પુષ્ટિ કરો",
  "form.button.continue": "ચાલુ રાખો",
  "form.button.done": "થઈ ગયું",
  "global.actions": "ક્રિયાઓ",
  "global.back": "પાછળ",
  "global.change-password": "પાસવર્ડ બદલો",
  "global.content-manager": "કન્ટેન્ટ મેનેજર",
  "global.continue": "ચાલુ રાખો",
  "global.delete": "કાઢી નાખો",
  "global.delete-target": "{target} કાઢી નાખો",
  "global.description": "વર્ણન",
  "global.details": "વિગતો",
  "global.disabled": "અક્ષમ",
  "global.documentation": "દસ્તાવેજીકરણ",
  "global.enabled": "સક્ષમ",
  "global.finish": "સમાપ્ત",
  "global.marketplace": "માર્કેટપ્લેસ",
  "global.name": "નામ",
  "global.none": "કોઈ નહિ",
  "global.password": "પાસવર્ડ",
  "global.plugins": "પ્લગઇન્સ",
  "global.profile": "પ્રોફાઇલ",
  "global.prompt.unsaved": "શું તમે ખરેખર આ પૃષ્ઠ છોડવા માંગો છો? તમારા બધા ફેરફારો ખોવાઈ જશે",
  "global.reset-password": "પાસવર્ડ રીસેટ કરો",
  "global.roles": "ભૂમિકાઓ",
  "global.save": "સાચવો",
  "global.see-more": "વધુ જુઓ",
  "global.select": "પસંદ કરો",
  "global.select-all-entries": "બધી એન્ટ્રી પસંદ કરો",
  "global.settings": "સેટિંગ્સ",
  "global.type": "પ્રકાર",
  "global.users": "વપરાશકર્તાઓ",
  "notification.contentType.relations.conflict": "સામગ્રીના પ્રકારમાં વિરોધાભાસી સંબંધો છે",
  "notification.default.title": "માહિતી:",
  "notification.error": "એક ભૂલ આવી છે",
  "notification.error.layout": "લેઆઉટ પુનઃપ્રાપ્ત કરી શકાયું નથી",
  "notification.form.error.fields": "ફોર્મમાં કેટલીક ભૂલો છે",
  "notification.form.success.fields": "ફેરફારો સાચવેલ",
  "notification.link-copied": "લિંક ક્લિપબોર્ડમાં કોપી કરી છે",
  "notification.permission.not-allowed-read": "તમને આ દસ્તાવેજ જોવાની મંજૂરી નથી",
  "notification.success.delete": "આઇટમ કાઢી નાખવામાં આવી છે",
  "notification.success.saved": "સાચવેલ",
  "notification.success.title": "સફળતા:",
  "notification.version.update.message": "સ્ટ્રેપીનું નવું સંસ્કરણ ઉપલબ્ધ છે!",
  "notification.warning.title": "ચેતવણી:",
  "અથવા": "અથવા",
  "request.error.model.unknown": "આ મોડલ અસ્તિત્વમાં નથી",
  skipToContent,
  submit
};
export {
  Analytics,
  anErrorOccurred,
  clearLabel,
  gu as default,
  skipToContent,
  submit
};
//# sourceMappingURL=gu.json-EO2GIQID.js.map
