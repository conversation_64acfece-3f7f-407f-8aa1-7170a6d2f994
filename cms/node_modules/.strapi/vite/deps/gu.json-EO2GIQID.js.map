{"version": 3, "sources": ["../../../@strapi/admin/dist/admin/admin/src/translations/gu.json.mjs"], "sourcesContent": ["var Analytics = \"એનાલિટિક્સ\";\nvar anErrorOccurred = \"ઉફ્ફ! કંઈક ખોટું થયું. કૃપા કરીને, ફરી પ્રયાસ કરો.\";\nvar clearLabel = \"સાફ કરો\";\nvar skipToContent = \"સામગ્રી પર જાઓ\";\nvar submit = \"સબમિટ કરો\";\nvar gu = {\n    Analytics: Analytics,\n    \"Auth.components.Oops.text\": \"તમારું એકાઉન્ટ સસ્પેન્ડ કરવામાં આવ્યું છે.\",\n    \"Auth.components.Oops.text.admin\": \"જો આ ભૂલ છે, તો કૃપા કરીને તમારા વ્યવસ્થાપકનો સંપર્ક કરો\",\n    \"Auth.components.Oops.title\": \"અરે...\",\n    \"Auth.form.button.forgot-password\": \"ઈ - મેલ મોકલો\",\n    \"Auth.form.button.go-home\": \"ઘરે પાછા જાવ\",\n    \"Auth.form.button.login\": \"રવેશ કરો\",\n    \"Auth.form.button.login.providers.error\": \"અમે તમને પસંદ કરેલ પ્રદાતા દ્વારા કનેક્ટ કરી શકતા નથી\",\n    \"Auth.form.button.login.strapi\": \"સ્ટ્રેપી દ્વારા લૉગ ઇન કરો\",\n    \"Auth.form.button.password-recovery\": \"પાસવર્ડ પુનઃપ્રાપ્તિ\",\n    \"Auth.form.button.register\": \"ચાલો શરૂ કરીએ\",\n    \"Auth.form.confirmPassword.label\": \"પુષ્ટિકરણ પાસવર્ડ\",\n    \"Auth.form.currentPassword.label\": \"અત્યારનો પાસવર્ડ\",\n    \"Auth.form.email.label\": \"ઈમેલ\",\n    \"Auth.form.email.placeholder\": \"દા.ત. кай@дое.цум\",\n    \"Auth.form.error.blocked\": \"તમારું એકાઉન્ટ એડમિનિસ્ટ્રેટર દ્વારા અવરોધિત કરવામાં આવ્યું છે\",\n    \"Auth.form.error.code.provide\": \"ખોટો કોડ આપેલ છે\",\n    \"Auth.form.error.confirmed\": \"તમારા એકાઉન્ટ ઈમેલની પુષ્ટિ થઈ નથી\",\n    \"Auth.form.error.email.invalid\": \"આ ઈમેલ અમાન્ય છે\",\n    \"Auth.form.error.email.provide\": \"કૃપા કરીને તમારું વપરાશકર્તા નામ અથવા તમારું ઇમેઇલ પ્રદાન કરો\",\n    \"Auth.form.error.email.taken\": \"ઇમેલ અગાઉ લેવાઇ ચુક્યું છે\",\n    \"Auth.form.error.invalid\": \"ઓળખકર્તા અથવા પાસવર્ડ અમાન્ય\",\n    \"Auth.form.error.params.provide\": \"અયોગ્ય પરિમાણો પ્રદાન કરવામાં આવ્યા છે\",\n    \"Auth.form.error.password.format\": \"તમારા પાસવર્ડમાં ત્રણ કરતા વધુ વખત `$` ચિહ્ન ન હોઈ શકે\",\n    \"Auth.form.error.password.local\": \"આ વપરાશકર્તાએ ક્યારેય સ્થાનિક પાસવર્ડ સેટ કર્યો નથી, કૃપા કરીને એકાઉન્ટ બનાવતી વખતે ઉપયોગમાં લેવાતા પ્રદાતા દ્વારા લૉગિન કરો\",\n    \"Auth.form.error.password.matching\": \"પાસવર્ડ મેળ ખાતા નથી\",\n    \"Auth.form.error.password.provide\": \"કૃપા કરીને તમારો પાસવર્ડ આપો\",\n    \"Settings.permissions.conditions.conditions\": \"શરતો વ્યાખ્યાયિત કરો\",\n    \"Settings.permissions.conditions.links\": \"લિંક્સ\",\n    \"Settings.permissions.conditions.no-actions\": \"તમારે તેના પર શરતો વ્યાખ્યાયિત કરતા પહેલા ક્રિયાઓ (બનાવો, વાંચો, અપડેટ કરો, ...) પસંદ કરો.\",\n    \"Settings.permissions.conditions.none-selected\": \"કોઈપણ સમયે\",\n    \"Settings.permissions.conditions.or\": \"અથવા\",\n    \"Settings.permissions.conditions.when\": \"ક્યારે\",\n    \"settings.permissions.select-all-by-permission\": \"બધી {label} પરવાનગીઓ પસંદ કરો\",\n    \"settings.permissions.select-by-permission\": \"{label} પરવાનગી પસંદ કરો\",\n    \"Settings.permissions.users.create\": \"નવા વપરાશકર્તાને આમંત્રિત કરો\",\n    \"Settings.permissions.users.email\": \"ઈમેલ\",\n    \"Settings.permissions.users.firstname\": \"પ્રથમ નામ\",\n    \"Settings.permissions.users.lastname\": \"છેલ્લું નામ\",\n    \"Settings.permissions.users.form.sso\": \"SSO સાથે કનેક્ટ કરો\",\n    \"Settings.permissions.users.form.sso.description\": \"જ્યારે સક્ષમ (ચાલુ) હોય, ત્યારે વપરાશકર્તાઓ SSO દ્વારા લૉગિન કરી શકે છે\",\n    \"Settings.permissions.users.listview.header.subtitle\": \"સ્ટ્રેપી એડમિન પેનલની ઍક્સેસ ધરાવતા તમામ વપરાશકર્તાઓ\",\n    \"Settings.permissions.users.tabs.label\": \"ટેબ પરવાનગીઓ\",\n    \"Settings.profile.form.notify.data.loaded\": \"તમારો પ્રોફાઇલ ડેટા લોડ કરવામાં આવ્યો છે\",\n    \"Settings.profile.form.section.experience.clear.select\": \"પસંદ કરેલ ઈન્ટરફેસ ભાષા સાફ કરો\",\n    \"Settings.profile.form.section.experience.here\": \"અહીં\",\n    \"Settings.profile.form.section.experience.interfaceLanguage\": \"ઇન્ટરફેસ ભાષા\",\n    \"Settings.profile.form.section.experience.interfaceLanguage.hint\": \"આ ફક્ત પસંદ કરેલી ભાષામાં તમારું પોતાનું ઈન્ટરફેસ પ્રદર્શિત કરશે.\",\n    \"Settings.profile.form.section.experience.interfaceLanguageHelp\": \"પસંદગીના ફેરફારો ફક્ત તમને જ લાગુ થશે. વધુ માહિતી {અહીં} ઉપલબ્ધ છે.\",\n    \"Settings.profile.form.section.experience.mode.label\": \"ઇન્ટરફેસ મોડ\",\n    \"Settings.profile.form.section.experience.mode.hint\": \"તમારું ઇન્ટરફેસ પસંદ કરેલ મોડમાં દર્શાવે છે.\",\n    \"Settings.profile.form.section.experience.mode.option-label\": \"{name} મોડ\",\n    \"Settings.profile.form.section.experience.title\": \"અનુભવ\",\n    \"Settings.profile.form.section.head.title\": \"વપરાશકર્તા પ્રોફાઇલ\",\n    \"Settings.profile.form.section.profile.page.title\": \"પ્રોફાઇલ પૃષ્ઠ\",\n    \"Settings.roles.create.description\": \"ભૂમિકાને આપવામાં આવેલા અધિકારોને વ્યાખ્યાયિત કરો\",\n    \"Settings.roles.create.title\": \"કોઈ ભૂમિકા બનાવો\",\n    \"Settings.roles.created\": \"ભૂમિકા બનાવી\",\n    \"Settings.roles.edit.title\": \"કોઈ ભૂમિકા સંપાદિત કરો\",\n    \"Settings.roles.form.button.users-with-role\": \"{સંખ્યા, બહુવચન, =0 {# વપરાશકર્તાઓ} એક {# વપરાશકર્તા} અન્ય {# વપરાશકર્તાઓ}} આ ભૂમિકા સાથે\",\n    \"Settings.roles.form.created\": \"બનાવ્યું\",\n    \"Settings.roles.form.description\": \"ભૂમિકાનું નામ અને વર્ણન\",\n    \"Settings.roles.form.permission.property-label\": \"{label} પરવાનગીઓ\",\n    \"Settings.roles.form.permissions.attributesPermissions\": \"ક્ષેત્ર પરવાનગીઓ\",\n    \"Settings.roles.form.permissions.create\": \"બનાવો\",\n    \"Settings.roles.form.permissions.delete\": \"કાઢી નાખો\",\n    \"Settings.roles.form.permissions.publish\": \"પ્રકાશિત કરો\",\n    \"Settings.roles.form.permissions.read\": \"વાંચો\",\n    \"Settings.roles.form.permissions.update\": \"અપડેટ\",\n    \"Settings.roles.list.button.add\": \"નવી ભૂમિકા ઉમેરો\",\n    \"Settings.roles.list.description\": \"ભૂમિકાઓની યાદી\",\n    \"Settings.roles.title.singular\": \"ભૂમિકા\",\n    \"Settings.sso.description\": \"સિંગલ સાઇન-ઓન સુવિધા માટે સેટિંગ્સને ગોઠવો.\",\n    \"Settings.sso.form.defaultRole.description\": \"તે નવા પ્રમાણિત વપરાશકર્તાને પસંદ કરેલ ભૂમિકા સાથે જોડશે\",\n    \"Settings.sso.form.defaultRole.description-not-allowed\": \"તમારી પાસે એડમિન ભૂમિકાઓ વાંચવા માટે પરવાનગી હોવી જરૂરી છે\",\n    \"Settings.sso.form.defaultRole.label\": \"મૂળભૂત ભૂમિકા\",\n    \"Settings.sso.form.registration.description\": \"જો કોઈ એકાઉન્ટ અસ્તિત્વમાં ન હોય તો SSO લૉગિન પર નવો વપરાશકર્તા બનાવો\",\n    \"Settings.sso.form.registration.label\": \"ઓટો-નોંધણી\",\n    \"Settings.sso.title\": \"સિંગલ સાઇન-ઓન\",\n    \"Settings.webhooks.create\": \"વેબહુક બનાવો\",\n    \"Settings.webhooks.create.header\": \"નવું હેડર બનાવો\",\n    \"Settings.webhooks.created\": \"વેબહુક બનાવ્યું\",\n    \"Settings.webhooks.event.publish-tooltip\": \"આ ઇવેન્ટ ફક્ત ડ્રાફ્ટ/પ્રકાશિત સિસ્ટમ સક્ષમ કરેલ સામગ્રીઓ માટે જ અસ્તિત્વમાં છે\",\n    \"Settings.webhooks.events.create\": \"બનાવો\",\n    \"Settings.webhooks.events.update\": \"અપડેટ\",\n    \"Settings.webhooks.form.events\": \"ઇવેન્ટ્સ\",\n    \"Settings.webhooks.form.headers\": \"હેડર\",\n    \"Settings.webhooks.form.url\": \"Url\",\n    \"Settings.webhooks.headers.remove\": \"હેડર પંક્તિ {નંબર} દૂર કરો\",\n    \"Settings.webhooks.key\": \"કી\",\n    \"Settings.webhooks.list.button.add\": \"નવું વેબહુક બનાવો\",\n    \"Settings.webhooks.list.description\": \"POST ફેરફારોની સૂચનાઓ મેળવો\",\n    \"Settings.webhooks.list.empty.description\": \"કોઈ વેબહુક્સ મળ્યા નથી\",\n    \"Settings.webhooks.list.empty.link\": \"અમારા દસ્તાવેજો જુઓ\",\n    \"Settings.webhooks.list.empty.title\": \"હજી સુધી કોઈ વેબહુક્સ નથી\",\n    \"Settings.webhooks.list.th.actions\": \"ક્રિયાઓ\",\n    \"Settings.webhooks.list.th.status\": \"સ્ટેટસ\",\n    \"Settings.webhooks.singular\": \"webhook\",\n    \"Settings.webhooks.title\": \"વેબહુક્સ\",\n    \"Settings.webhooks.to.delete\": \"{webhooksToDeleteLength, બહુવચન, એક {# સંપત્તિ} અન્ય {# સંપત્તિ}} પસંદ કરેલ\",\n    \"Settings.webhooks.trigger\": \"ટ્રિગર\",\n    \"Settings.webhooks.trigger.cancel\": \"ટ્રિગર રદ કરો\",\n    \"Settings.webhooks.trigger.pending\": \"બાકી…\",\n    \"Settings.webhooks.trigger.save\": \"કૃપા કરીને ટ્રિગર કરવા માટે સાચવો\",\n    \"Settings.webhooks.trigger.success\": \"સફળતા!\",\n    \"Settings.webhooks.trigger.success.label\": \"ટ્રિગર સફળ થયું\",\n    \"Settings.webhooks.trigger.test\": \"ટેસ્ટ-ટ્રિગર\",\n    \"Settings.webhooks.trigger.title\": \"ટ્રિગર પહેલા સાચવો\",\n    \"Settings.webhooks.value\": \"મૂલ્ય\",\n    \"Usecase.back-end\": \"બેક-એન્ડ ડેવલપર\",\n    \"Usecase.button.skip\": \"આ પ્રશ્ન છોડો\",\n    \"Usecase.content-creator\": \"સામગ્રી નિર્માતા\",\n    \"Usecase.front-end\": \"ફ્રન્ટ-એન્ડ ડેવલપર\",\n    \"Usecase.full-stack\": \"ફુલ-સ્ટેક ડેવલપર\",\n    \"usecase.input.work-type\": \"તમે કયા પ્રકારનું કામ કરો છો?\",\n    \"usecase.notification.success.project-created\": \"પ્રોજેક્ટ સફળતાપૂર્વક બનાવવામાં આવ્યો છે\",\n    \"Usecase.other\": \"અન્ય\",\n    \"Usecase.title\": \"તમારા વિશે અમને થોડું વધુ કહો\",\n    \"વપરાશકર્તા નામ\": \"વપરાશકર્તા નામ\",\n    \"વપરાશકર્તાઓ\": \"વપરાશકર્તાઓ\",\n    \"વપરાશકર્તાઓ અને પરવાનગીઓ\": \"વપરાશકર્તાઓ અને પરવાનગીઓ\",\n    \"Users.components.List.empty\": \"ત્યાં કોઈ વપરાશકર્તાઓ નથી...\",\n    \"Users.components.List.empty.withFilters\": \"લાગુ કરેલ ફિલ્ટર્સ સાથે કોઈ વપરાશકર્તા નથી...\",\n    \"Users.components.List.empty.withSearch\": \"શોધને અનુરૂપ કોઈ વપરાશકર્તાઓ નથી ({search})...\",\n    \"admin.pages.MarketPlacePage.head\": \"માર્કેટપ્લેસ - પ્લગઇન્સ\",\n    \"admin.pages.MarketPlacePage.offline.title\": \"તમે ઑફલાઇન છો\",\n    \"admin.pages.MarketPlacePage.offline.subtitle\": \"સ્ટ્રેપી માર્કેટને ઍક્સેસ કરવા માટે તમારે ઇન્ટરનેટ સાથે કનેક્ટેડ હોવું જરૂરી છે.\",\n    \"admin.pages.MarketPlacePage.plugin.copy\": \"ઇન્સ્ટોલ આદેશની નકલ કરો\",\n    \"admin.pages.MarketPlacePage.plugin.copy.success\": \"તમારા ટર્મિનલમાં પેસ્ટ કરવા માટે તૈયાર આદેશ ઇન્સ્ટોલ કરો\",\n    \"admin.pages.MarketPlacePage.plugin.info\": \"વધુ જાણો\",\n    \"admin.pages.MarketPlacePage.plugin.info.label\": \"{pluginName} વિશે વધુ જાણો\",\n    \"admin.pages.MarketPlacePage.plugin.info.text\": \"વધુ જાણો\",\n    \"admin.pages.MarketPlacePage.plugin.installed\": \"ઇન્સ્ટોલ કરેલ\",\n    \"admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi\": \"સ્ટ્રેપી દ્વારા બનાવેલ\",\n    \"admin.pages.MarketPlacePage.plugin.tooltip.verified\": \"સ્ટ્રેપી દ્વારા ચકાસાયેલ પ્લગઇન\",\n    \"admin.pages.MarketPlacePage.search.clear\": \"પ્લગઇન શોધ સાફ કરો\",\n    \"admin.pages.MarketPlacePage.search.empty\": \"\\\"{target}\\\" માટે કોઈ પરિણામ નથી\",\n    \"admin.pages.MarketPlacePage.search.placeholder\": \"પ્લગઇન માટે શોધો\",\n    \"admin.pages.MarketPlacePage.submit.plugin.link\": \"તમારું પ્લગઇન સબમિટ કરો\",\n    \"admin.pages.MarketPlacePage.subtitle\": \"સ્ટ્રેપીમાંથી વધુ મેળવો\",\n    \"admin.pages.MarketPlacePage.missingPlugin.title\": \"પ્લગઇન ખૂટે છે?\",\n    \"admin.pages.MarketPlacePage.missingPlugin.description\": \"તમે કયું પ્લગઇન શોધી રહ્યાં છો તે અમને કહો અને અમે અમારા સમુદાય પ્લગઇન ડેવલપર્સને જણાવીશું કે તેઓ પ્રેરણાની શોધમાં હોય તો!\",\n    anErrorOccurred: anErrorOccurred,\n    \"app.component.CopyToClipboard.label\": \"ક્લિપબોર્ડ પર કૉપિ કરો\",\n    \"app.component.search.label\": \"{target} માટે શોધો\",\n    \"app.component.table.duplicate\": \"ડુપ્લિકેટ {target}\",\n    \"app.component.table.edit\": \"સંપાદિત કરો {target}\",\n    \"app.component.table.select.one-entry\": \"{target} પસંદ કરો\",\n    \"app.components.BlockLink.blog\": \"બ્લોગ\",\n    \"app.components.BlockLink.blog.content\": \"સ્ટ્રેપી અને ઇકોસિસ્ટમ વિશે નવીનતમ સમાચાર વાંચો.\",\n    \"app.components.BlockLink.code\": \"કોડ ઉદાહરણો\",\n    \"app.components.BlockLink.code.content\": \"સમુદાય દ્વારા વિકસિત વાસ્તવિક પ્રોજેક્ટ્સનું પરીક્ષણ કરીને શીખો.\",\n    \"app.components.BlockLink.documentation.content\": \"આવશ્યક ખ્યાલો, માર્ગદર્શિકાઓ અને સૂચનાઓ શોધો.\",\n    \"app.components.BlockLink.tutorial\": \"ટ્યુટોરિયલ્સ\",\n    \"app.components.BlockLink.tutorial.content\": \"સ્ટ્રેપીનો ઉપયોગ કરવા અને કસ્ટમાઇઝ કરવા માટે પગલું-દર-પગલાં સૂચનોને અનુસરો.\",\n    \"app.components.Button.cancel\": \"રદ કરો\",\n    \"app.components.Button.confirm\": \"પુષ્ટિ કરો\",\n    \"app.components.Button.reset\": \"રીસેટ કરો\",\n    \"app.components.ComingSoonPage.comingSoon\": \"ટૂંક સમયમાં આવી રહ્યું છે\",\n    \"app.components.ConfirmDialog.title\": \"પુષ્ટિ\",\n    \"app.components.DownloadInfo.download\": \"ડાઉનલોડ ચાલુ છે...\",\n    \"app.components.DownloadInfo.text\": \"આમાં એક મિનિટ લાગી શકે છે. તમારી ધીરજ બદલ આભાર.\",\n    \"app.components.EmptyAttributes.title\": \"હજી સુધી કોઈ ફીલ્ડ નથી\",\n    \"app.components.EmptyStateLayout.content-document\": \"કોઈ સામગ્રી મળી નથી\",\n    \"app.components.EmptyStateLayout.content-permissions\": \"તમારી પાસે તે સામગ્રીને ઍક્સેસ કરવાની પરવાનગીઓ નથી\",\n    \"app.components.GuidedTour.CM.create.content\": \"<p>અહીં કન્ટેન્ટ મેનેજરમાં તમામ કન્ટેન્ટ બનાવો અને મેનેજ કરો.</p><p>ઉદા.: બ્લોગ વેબસાઈટના ઉદાહરણને આગળ લઈએ તો, કોઈ એક લખી શકે છે. તેમને ગમે તે રીતે લેખ, સાચવો અને પ્રકાશિત કરો.</p><p>💡 ઝડપી ટીપ - તમે જે સામગ્રી બનાવો છો તેના પર પ્રકાશિત કરવાનું ભૂલશો નહીં.</p>\",\n    \"app.components.GuidedTour.CM.create.title\": \"⚡️ સામગ્રી બનાવો\",\n    \"app.components.GuidedTour.CM.success.content\": \"<p>અદ્ભુત, જવા માટેનું એક છેલ્લું પગલું!</p><b>🚀 ક્રિયામાં સામગ્રી જુઓ</b>\",\n    \"app.components.GuidedTour.CM.success.cta.title\": \"API નું પરીક્ષણ કરો\",\n    \"app.components.GuidedTour.CM.success.title\": \"પગલું 2: પૂર્ણ ✅\",\n    \"app.components.GuidedTour.CTB.create.content\": \"<p>સંગ્રહના પ્રકારો તમને ઘણી એન્ટ્રીઓનું સંચાલન કરવામાં મદદ કરે છે, સિંગલ પ્રકારો માત્ર એક જ એન્ટ્રીને મેનેજ કરવા માટે યોગ્ય છે.</p> <p>ઉદા.: બ્લોગ વેબસાઇટ માટે, લેખો એક સંગ્રહ પ્રકાર હશે જ્યારે હોમપેજ એક પ્રકારનું હશે.</p>\",\n    \"app.components.GuidedTour.CTB.create.cta.title\": \"સંગ્રહ પ્રકાર બનાવો\",\n    \"app.components.GuidedTour.CTB.create.title\": \"🧠 પ્રથમ કલેક્શન પ્રકાર બનાવો\",\n    \"app.components.GuidedTour.CTB.success.content\": \"<p>સારું ચાલી રહ્યું છે!</p><b>⚡️ તમે વિશ્વ સાથે શું શેર કરવા માંગો છો?</b>\",\n    \"app.components.GuidedTour.CTB.success.title\": \"પગલું 1: પૂર્ણ ✅\",\n    \"app.components.GuidedTour.apiTokens.create.content\": \"<p>અહીં એક પ્રમાણીકરણ ટોકન જનરેટ કરો અને તમે હમણાં જ બનાવેલ સામગ્રી પુનઃપ્રાપ્ત કરો.</p>\",\n    \"app.components.GuidedTour.apiTokens.create.cta.title\": \"એક API ટોકન બનાવો\",\n    \"app.components.GuidedTour.apiTokens.create.title\": \"🚀 ક્રિયામાં સામગ્રી જુઓ\",\n    \"app.components.GuidedTour.apiTokens.success.content\": \"<p>એક HTTP વિનંતી કરીને સામગ્રીને ક્રિયામાં જુઓ:</p><ul><li><p>આ URL પર: <light>https: //'<'YOUR_DOMAIN'>'/api/'<'YOUR_CT'>'</light></p></li><li><p>હેડર સાથે: <light>અધિકૃતતા: વાહક '<' YOUR_API_TOKEN'>'</light></p></li></ul><p>સામગ્રી સાથે ક્રિયાપ્રતિક્રિયા કરવાની વધુ રીતો માટે, <documentationLink>દસ્તાવેજીકરણ</documentationLink> જુઓ.</p>\",\n    \"app.components.GuidedTour.apiTokens.success.cta.title\": \"હોમપેજ પર પાછા જાઓ\",\n    \"app.components.GuidedTour.apiTokens.success.title\": \"પગલું 3: પૂર્ણ ✅\",\n    \"app.components.GuidedTour.create-content\": \"સામગ્રી બનાવો\",\n    \"app.components.GuidedTour.home.CM.title\": \"⚡️ તમે વિશ્વ સાથે શું શેર કરવા માંગો છો?\",\n    \"app.components.GuidedTour.home.CTB.cta.title\": \"સામગ્રી પ્રકાર બિલ્ડર પર જાઓ\",\n    \"app.components.GuidedTour.home.CTB.title\": \"🧠 સામગ્રી માળખું બનાવો\",\n    \"app.components.GuidedTour.home.apiTokens.cta.title\": \"API નું પરીક્ષણ કરો\",\n    \"app.components.GuidedTour.skip\": \"ટૂર છોડો\",\n    \"app.components.GuidedTour.title\": \"પ્રારંભ કરવા માટે 3 પગલાં\",\n    \"app.components.HomePage.button.blog\": \"બ્લોગ પર વધુ જુઓ\",\n    \"app.components.HomePage.community\": \"સમુદાયમાં જોડાઓ\",\n    \"app.components.HomePage.community.content\": \"વિવિધ ચેનલો પર ટીમના સભ્યો, યોગદાનકર્તાઓ અને વિકાસકર્તાઓ સાથે ચર્ચા કરો.\",\n    \"app.components.HomePage.create\": \"તમારો પ્રથમ સામગ્રી પ્રકાર બનાવો\",\n    \"app.components.HomePage.roadmap\": \"અમારો રોડમેપ જુઓ\",\n    \"app.components.HomePage.welcome\": \"બોર્ડ પર આપનું સ્વાગત છે 👋\",\n    \"app.components.HomePage.welcome.again\": \"સ્વાગત છે 👋\",\n    \"app.components.HomePage.welcomeBlock.content\": \"અભિનંદન! તમે પ્રથમ એડમિનિસ્ટ્રેટર તરીકે લૉગ ઇન થયા છો. સ્ટ્રેપી દ્વારા પ્રદાન કરવામાં આવેલ શક્તિશાળી સુવિધાઓ શોધવા માટે, અમે તમને તમારો પ્રથમ સામગ્રી પ્રકાર બનાવવાની ભલામણ કરીએ છીએ!\",\n    \"app.components.HomePage.welcomeBlock.content.again\": \"અમને આશા છે કે તમે તમારા પ્રોજેક્ટમાં પ્રગતિ કરી રહ્યાં છો! સ્ટ્રેપી વિશેના નવીનતમ સમાચાર વાંચવા માટે નિઃસંકોચ. અમે તમારા પ્રતિસાદના આધારે ઉત્પાદનને સુધારવા માટે અમારા શ્રેષ્ઠ પ્રયાસો આપી રહ્યા છીએ.\",\n    \"app.components.HomePage.welcomeBlock.content.issues\": \"સમસ્યાઓ.\",\n    \"app.components.HomePage.welcomeBlock.content.raise\": \"અથવા વધારો\",\n    \"app.components.ImgPreview.hint\": \"તમારી ફાઇલને આ વિસ્તારમાં ખેંચો અને છોડો અથવા ફાઇલ અપલોડ કરવા માટે {બ્રાઉઝ કરો\",\n    \"app.components.ImgPreview.hint.browse\": \"બ્રાઉઝ કરો\",\n    \"app.components.InputFile.newFile\": \"નવી ફાઇલ ઉમેરો\",\n    \"app.components.InputFileDetails.open\": \"નવી ટેબમાં ખોલો\",\n    \"app.components.InputFileDetails.originalName\": \"મૂળ નામ:\",\n    \"app.components.InputFileDetails.remove\": \"આ ફાઇલને દૂર કરો\",\n    \"app.components.InputFileDetails.size\": \"કદ:\",\n    \"app.components.InstallPluginPage.Download.description\": \"પ્લગઇનને ડાઉનલોડ અને ઇન્સ્ટોલ કરવામાં થોડીક સેકન્ડ લાગી શકે છે.\",\n    \"app.components.InstallPluginPage.Download.title\": \"ડાઉનલોડ કરી રહ્યું છે...\",\n    \"app.components.InstallPluginPage.description\": \"તમારી એપ્લિકેશનને વિના પ્રયાસે વિસ્તૃત કરો.\",\n    \"app.components.LeftMenu.collapse\": \"નવબારને સંકુચિત કરો\",\n    \"app.components.LeftMenu.expand\": \"નવબાર વિસ્તૃત કરો\",\n    \"app.components.LeftMenu.logout\": \"લોગઆઉટ\",\n    \"app.components.LeftMenu.trialCountdown\": \"તમારી પ્રયાસ અંતમાં {date} પર થાય છે.\",\n    \"app.components.LeftMenu.navbrand.title\": \"સ્ટ્રેપી ડેશબોર્ડ\",\n    \"app.components.LeftMenu.navbrand.workplace\": \"કાર્યસ્થળ\",\n    \"app.components.LeftMenuFooter.help\": \"સહાય\",\n    \"app.components.LeftMenuFooter.poweredBy\": \"દ્વારા સંચાલિત\",\n    \"app.components.LeftMenuLinkContainer.collectionTypes\": \"સંગ્રહના પ્રકાર\",\n    \"app.components.LeftMenuLinkContainer.configuration\": \"રૂપરેખાંકનો\",\n    \"app.components.LeftMenuLinkContainer.general\": \"સામાન્ય\",\n    \"app.components.LeftMenuLinkContainer.noPluginsInstalled\": \"હજુ સુધી કોઈ પ્લગઈન્સ ઇન્સ્ટોલ કરેલ નથી\",\n    \"app.components.LeftMenuLinkContainer.plugins\": \"પ્લગઇન્સ\",\n    \"app.components.LeftMenuLinkContainer.singleTypes\": \"એક પ્રકાર\",\n    \"app.components.ListPluginsPage.deletePlugin.description\": \"પ્લગઇનને અનઇન્સ્ટોલ કરવામાં થોડીક સેકન્ડ લાગી શકે છે.\",\n    \"app.components.ListPluginsPage.deletePlugin.title\": \"અનઇન્સ્ટોલ કરી રહ્યું છે\",\n    \"app.components.ListPluginsPage.description\": \"પ્રોજેક્ટમાં ઇન્સ્ટોલ કરેલ પ્લગિન્સની સૂચિ.\",\n    \"app.components.ListPluginsPage.head.title\": \"પ્લગઇન્સની સૂચિ બનાવો\",\n    \"app.components.Logout.logout\": \"લોગઆઉટ\",\n    \"app.components.Logout.profile\": \"પ્રોફાઇલ\",\n    \"app.components.MarketplaceBanner\": \"સમુદાય દ્વારા બનાવવામાં આવેલ પ્લગઇન્સ અને તમારા પ્રોજેક્ટને કિકસ્ટાર્ટ કરવા માટે ઘણી વધુ અદ્ભુત વસ્તુઓ, Strapi Awesome પર શોધો.\",\n    \"app.components.MarketplaceBanner.image.alt\": \"એક સ્ટ્રેપી રોકેટ લોગો\",\n    \"app.components.MarketplaceBanner.link\": \"હમણાં જ તપાસો\",\n    \"app.components.NotFoundPage.back\": \"હોમપેજ પર પાછા\",\n    \"app.components.NotFoundPage.description\": \"મળ્યું નથી\",\n    \"app.components.Official\": \"સત્તાવાર\",\n    \"app.components.Onboarding.help.button\": \"સહાય બટન\",\n    \"app.components.Onboarding.label.completed\": \"% પૂર્ણ\",\n    \"app.components.Onboarding.title\": \"પ્રારંભ કરો વિડિઓઝ\",\n    \"app.components.PluginCard.Button.label.download\": \"ડાઉનલોડ કરો\",\n    \"app.components.PluginCard.Button.label.install\": \"પહેલેથી જ ઇન્સ્ટોલ કરેલ છે\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed\": \"ઓટોરીલોડ સુવિધાને સક્ષમ કરવાની જરૂર છે. કૃપા કરીને તમારી એપ્લિકેશનને `યાર્ન ડેવલપ` સાથે પ્રારંભ કરો.\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.confirm\": \"હું સમજું છું!\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.environment\": \"સુરક્ષા કારણોસર, પ્લગઇન ફક્ત વિકાસ વાતાવરણમાં જ ડાઉનલોડ કરી શકાય છે.\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.title\": \"ડાઉનલોડ કરવું અશક્ય છે\",\n    \"app.components.PluginCard.compatible\": \"તમારી એપ્લિકેશન સાથે સુસંગત\",\n    \"app.components.PluginCard.compatibleCommunity\": \"સમુદાય સાથે સુસંગત\",\n    \"app.components.PluginCard.more-details\": \"વધુ વિગતો\",\n    \"app.components.ToggleCheckbox.off-label\": \"ખોટું\",\n    \"app.components.ToggleCheckbox.on-label\": \"True\",\n    \"app.components.Users.MagicLink.connect\": \"આ વપરાશકર્તાને ઍક્સેસ આપવા માટે આ લિંક કૉપિ કરો અને શેર કરો\",\n    \"app.components.Users.MagicLink.connect.sso\": \"વપરાશકર્તાને આ લિંક મોકલો, પ્રથમ લૉગિન SSO પ્રદાતા દ્વારા કરી શકાય છે\",\n    \"app.components.Users.ModalCreateBody.block-title.details\": \"વપરાશકર્તા વિગતો\",\n    \"app.components.Users.ModalCreateBody.block-title.roles\": \"વપરાશકર્તાની ભૂમિકા\",\n    \"app.components.Users.ModalCreateBody.block-title.roles.description\": \"વપરાશકર્તા એક અથવા અનેક ભૂમિકાઓ ધરાવી શકે છે\",\n    \"app.components.Users.SortPicker.button-label\": \"આ પ્રમાણે સૉર્ટ કરો\",\n    \"app.components.Users.SortPicker.sortby.email_asc\": \"ઇમેઇલ (A થી Z)\",\n    \"app.components.Users.SortPicker.sortby.email_desc\": \"ઈમેલ (Z થી A)\",\n    \"app.components.Users.SortPicker.sortby.firstname_asc\": \"પ્રથમ નામ (A થી Z)\",\n    \"app.components.Users.SortPicker.sortby.firstname_desc\": \"પ્રથમ નામ (Z થી A)\",\n    \"app.components.Users.SortPicker.sortby.lastname_asc\": \"છેલ્લું નામ (A થી Z)\",\n    \"app.components.Users.SortPicker.sortby.lastname_desc\": \"છેલ્લું નામ (Z થી A)\",\n    \"app.components.Users.SortPicker.sortby.username_asc\": \"વપરાશકર્તા નામ (A થી Z)\",\n    \"app.components.Users.SortPicker.sortby.username_desc\": \"વપરાશકર્તા નામ (Z થી A)\",\n    \"app.components.listPlugins.button\": \"નવું પ્લગઇન ઉમેરો\",\n    \"app.components.listPlugins.title.none\": \"કોઈ પ્લગઈન્સ ઇન્સ્ટોલ કરેલ નથી\",\n    \"app.components.listPluginsPage.deletePlugin.error\": \"પ્લગઇનને અનઇન્સ્ટોલ કરતી વખતે ભૂલ આવી\",\n    \"app.containers.App.notification.error.init\": \"એપીઆઈની વિનંતી કરતી વખતે ભૂલ આવી\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin\": \"જો તમને આ લિંક પ્રાપ્ત ન થાય, તો કૃપા કરીને તમારા વ્યવસ્થાપકનો સંપર્ક કરો.\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.text.email\": \"તમારી પાસવર્ડ પુનઃપ્રાપ્તિ લિંક પ્રાપ્ત કરવામાં થોડી મિનિટો લાગી શકે છે.\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.title\": \"ઈમેલ મોકલ્યો\",\n    \"app.containers.Users.EditPage.form.active.label\": \"સક્રિય\",\n    \"app.containers.Users.EditPage.header.label\": \"{name} સંપાદિત કરો\",\n    \"app.containers.Users.EditPage.header.label-loading\": \"વપરાશકર્તા સંપાદિત કરો\",\n    \"app.containers.Users.EditPage.roles-bloc-title\": \"એટ્રિબ્યુટેડ ભૂમિકાઓ\",\n    \"app.containers.Users.ModalForm.footer.button-success\": \"વપરાશકર્તાને આમંત્રિત કરો\",\n    \"app.links.configure-view\": \"દૃશ્યને ગોઠવો\",\n    \"app.page.not.found\": \"અરેરે! તમે જે પૃષ્ઠ શોધી રહ્યાં છો તે અમે શોધી શકતા નથી...\",\n    \"app.static.links.cheatsheet\": \"ચીટશીટ\",\n    \"app.utils.SelectOption.defaultMessage\": \" \",\n    \"app.utils.add-filter\": \"ફિલ્ટર ઉમેરો\",\n    \"app.utils.close-label\": \"બંધ કરો\",\n    \"app.utils.defaultMessage\": \" \",\n    \"app.utils.duplicate\": \"ડુપ્લિકેટ\",\n    \"app.utils.edit\": \"સંપાદિત કરો\",\n    \"app.utils.errors.file-too-big.message\": \"ફાઇલ ખૂબ મોટી છે\",\n    \"app.utils.filter-value\": \"ફિલ્ટર મૂલ્ય\",\n    \"app.utils.filters\": \"ફિલ્ટર્સ\",\n    \"app.utils.notify.data-loaded\": \"{target} લોડ થઈ ગયું છે\",\n    \"app.utils.placeholder.defaultMessage\": \" \",\n    \"app.utils.publish\": \"પ્રકાશિત કરો\",\n    \"app.utils.select-all\": \"બધા પસંદ કરો\",\n    \"app.utils.select-field\": \"ક્ષેત્ર પસંદ કરો\",\n    \"app.utils.select-filter\": \"ફિલ્ટર પસંદ કરો\",\n    \"app.utils.unpublish\": \"અપ્રકાશિત કરો\",\n    clearLabel: clearLabel,\n    \"coming.soon\": \"આ સામગ્રી હાલમાં નિર્માણાધીન છે અને થોડા અઠવાડિયામાં પાછી આવશે!\",\n    \"component.Input.error.validation.integer\": \"મૂલ્ય પૂર્ણાંક હોવું આવશ્યક છે\",\n    \"components.AutoReloadBlocker.description\": \"નીચેના આદેશોમાંથી એક સાથે સ્ટ્રેપી ચલાવો:\",\n    \"components.AutoReloadBlocker.header\": \"આ પ્લગઇન માટે રીલોડ સુવિધા જરૂરી છે.\",\n    \"components.ErrorBoundary.title\": \"કંઈક ખોટું થયું...\",\n    \"components.FilterOptions.FILTER_TYPES.$contains\": \"સમાવે છે\",\n    \"components.FilterOptions.FILTER_TYPES.$containsi\": \"સમાવે છે (case insensitive)\",\n    \"components.FilterOptions.FILTER_TYPES.$endsWith\": \"આની સાથે સમાપ્ત થાય છે\",\n    \"components.FilterOptions.FILTER_TYPES.$endsWithi\": \"આની સાથે સમાપ્ત થાય છે (case insensitive)\",\n    \"components.FilterOptions.FILTER_TYPES.$eq\": \"છે\",\n    \"components.FilterOptions.FILTER_TYPES.$eqi\": \"છે (case insensitive)\",\n    \"components.FilterOptions.FILTER_TYPES.$gt\": \"આના કરતા વધારે છે\",\n    \"components.FilterOptions.FILTER_TYPES.$gte\": \"તેના કરતા વધારે અથવા બરાબર છે\",\n    \"components.FilterOptions.FILTER_TYPES.$lt\": \"આના કરતા ઓછું છે\",\n    \"components.FilterOptions.FILTER_TYPES.$lte\": \"તેના કરતા ઓછું અથવા બરાબર છે\",\n    \"components.FilterOptions.FILTER_TYPES.$ne\": \"નથી\",\n    \"components.FilterOptions.FILTER_TYPES.$nei\": \"નથી (case insensitive)\",\n    \"components.FilterOptions.FILTER_TYPES.$notContains\": \"સમાવતું નથી\",\n    \"components.FilterOptions.FILTER_TYPES.$notContainsi\": \"સમાવતું નથી (case insensitive)\",\n    \"components.FilterOptions.FILTER_TYPES.$notNull\": \"નલ નથી\",\n    \"components.FilterOptions.FILTER_TYPES.$null\": \"નલ છે\",\n    \"components.FilterOptions.FILTER_TYPES.$startsWith\": \"આનાથી શરૂ થાય છે\",\n    \"components.FilterOptions.FILTER_TYPES.$startsWithi\": \"આનાથી શરૂ થાય છે (case insensitive)\",\n    \"components.Input.error.attribute.key.taken\": \"આ મૂલ્ય પહેલાથી જ અસ્તિત્વમાં છે\",\n    \"components.Input.error.attribute.sameKeyAndName\": \"સમાન ન હોઈ શકે\",\n    \"components.Input.error.attribute.taken\": \"આ ક્ષેત્રનું નામ પહેલેથી જ અસ્તિત્વમાં છે\",\n    \"components.Input.error.contain.lowercase\": \"પાસવર્ડમાં ઓછામાં ઓછો એક લોઅરકેસ અક્ષર હોવો જોઈએ\",\n    \"components.Input.error.contain.number\": \"પાસવર્ડમાં ઓછામાં ઓછો એક નંબર હોવો જોઈએ\",\n    \"components.Input.error.contain.uppercase\": \"પાસવર્ડમાં ઓછામાં ઓછો એક અપરકેસ અક્ષર હોવો જોઈએ\",\n    \"components.Input.error.contentTypeName.taken\": \"આ નામ પહેલેથી જ અસ્તિત્વમાં છે\",\n    \"components.Input.error.custom-error\": \"{errorMessage}\",\n    \"components.Input.error.password.noMatch\": \"પાસવર્ડ મેળ ખાતા નથી\",\n    \"components.Input.error.validation.email\": \"આ અમાન્ય ઈમેલ છે\",\n    \"components.Input.error.validation.json\": \"આ JSON ફોર્મેટ સાથે મેળ ખાતું નથી\",\n    \"components.Input.error.validation.lowercase\": \"મૂલ્ય લોઅરકેસ સ્ટ્રિંગ હોવી જોઈએ\",\n    \"components.Input.error.validation.max\": \"મૂલ્ય ખૂબ વધારે છે {max}.\",\n    \"components.Input.error.validation.maxLength\": \"મૂલ્ય ખૂબ લાંબુ છે {max}.\",\n    \"components.Input.error.validation.min\": \"મૂલ્ય ખૂબ ઓછું છે {min}.\",\n    \"components.Input.error.validation.minLength\": \"મૂલ્ય ખૂબ ટૂંકું છે {min}.\",\n    \"components.Input.error.validation.minSupMax\": \"બહેતર ન હોઈ શકે\",\n    \"components.Input.error.validation.regex\": \"મૂલ્ય રેગેક્સ સાથે મેળ ખાતું નથી.\",\n    \"components.Input.error.validation.required\": \"આ મૂલ્ય જરૂરી છે.\",\n    \"components.Input.error.validation.unique\": \"આ મૂલ્ય પહેલેથી જ વપરાયેલ છે.\",\n    \"components.InputSelect.option.placeholder\": \"અહીં પસંદ કરો\",\n    \"components.ListRow.empty\": \"બતાવવા માટે કોઈ ડેટા નથી.\",\n    \"components.NotAllowedInput.text\": \"આ ક્ષેત્ર જોવા માટે કોઈ પરવાનગી નથી\",\n    \"components.OverlayBlocker.description\": \"તમે એવી સુવિધાનો ઉપયોગ કરી રહ્યાં છો કે જેને સર્વર પુનઃપ્રારંભ કરવાની જરૂર છે. કૃપા કરીને સર્વર ચાલુ થાય ત્યાં સુધી રાહ જુઓ.\",\n    \"components.OverlayBlocker.description.serverError\": \"સર્વર પુનઃપ્રારંભ થયેલ હોવું જોઈએ, કૃપા કરીને ટર્મિનલમાં તમારા લોગ તપાસો.\",\n    \"components.OverlayBlocker.title\": \"પુનઃશરૂ થવાની રાહ જોઈ રહ્યાં છીએ...\",\n    \"components.OverlayBlocker.title.serverError\": \"પુનઃપ્રારંભમાં અપેક્ષા કરતા વધુ સમય લાગી રહ્યો છે\",\n    \"components.PageFooter.select\": \"પ્રતિ પાનાની એન્ટ્રી\",\n    \"components.ProductionBlocker.description\": \"સુરક્ષા હેતુઓ માટે અમારે અન્ય વાતાવરણમાં આ પ્લગઈનને અક્ષમ કરવું પડશે.\",\n    \"components.ProductionBlocker.header\": \"આ પ્લગઇન ફક્ત વિકાસમાં જ ઉપલબ્ધ છે.\",\n    \"components.Search.placeholder\": \"શોધો...\",\n    \"components.TableHeader.sort\": \"{label} પર સૉર્ટ કરો\",\n    \"components.Wysiwyg.ToggleMode.markdown-mode\": \"માર્કડાઉન મોડ\",\n    \"components.Wysiwyg.ToggleMode.preview-mode\": \"પૂર્વાવલોકન મોડ\",\n    \"components.Wysiwyg.collapse\": \"સંકુચિત કરો\",\n    \"components.Wysiwyg.selectOptions.H1\": \"શીર્ષક H1\",\n    \"components.Wysiwyg.selectOptions.H2\": \"શીર્ષક H2\",\n    \"components.Wysiwyg.selectOptions.H3\": \"શીર્ષક H3\",\n    \"components.Wysiwyg.selectOptions.H4\": \"શીર્ષક H4\",\n    \"components.Wysiwyg.selectOptions.H5\": \"શીર્ષક H5\",\n    \"components.Wysiwyg.selectOptions.H6\": \"શીર્ષક H6\",\n    \"components.Wysiwyg.selectOptions.title\": \"શીર્ષક ઉમેરો\",\n    \"components.WysiwygBottomControls.charactersIndicators\": \"અક્ષરો\",\n    \"components.WysiwygBottomControls.fullscreen\": \"વિસ્તૃત કરો\",\n    \"components.WysiwygBottomControls.uploadFiles\": \"ફાઈલોને ખેંચો અને છોડો, ક્લિપબોર્ડમાંથી પેસ્ટ કરો અથવા {બ્રાઉઝ કરો}.\",\n    \"components.WysiwygBottomControls.uploadFiles.browse\": \"તેમને પસંદ કરો\",\n    \"components.pagination.go-to\": \"પૃષ્ઠ {page} પર જાઓ\",\n    \"components.pagination.go-to-next\": \"આગલા પૃષ્ઠ પર જાઓ\",\n    \"components.pagination.go-to-previous\": \"પાછલા પૃષ્ઠ પર જાઓ\",\n    \"components.pagination.remaining-links\": \"અને {નંબર} અન્ય લિંક્સ\",\n    \"components.popUpWarning.button.cancel\": \"ના, રદ કરો\",\n    \"components.popUpWarning.button.confirm\": \"હા, પુષ્ટિ કરો\",\n    \"components.popUpWarning.message\": \"શું તમે ખરેખર આને કાઢી નાખવા માંગો છો?\",\n    \"components.popUpWarning.title\": \"કૃપા કરીને પુષ્ટિ કરો\",\n    \"form.button.continue\": \"ચાલુ રાખો\",\n    \"form.button.done\": \"થઈ ગયું\",\n    \"global.actions\": \"ક્રિયાઓ\",\n    \"global.back\": \"પાછળ\",\n    \"global.change-password\": \"પાસવર્ડ બદલો\",\n    \"global.content-manager\": \"કન્ટેન્ટ મેનેજર\",\n    \"global.continue\": \"ચાલુ રાખો\",\n    \"global.delete\": \"કાઢી નાખો\",\n    \"global.delete-target\": \"{target} કાઢી નાખો\",\n    \"global.description\": \"વર્ણન\",\n    \"global.details\": \"વિગતો\",\n    \"global.disabled\": \"અક્ષમ\",\n    \"global.documentation\": \"દસ્તાવેજીકરણ\",\n    \"global.enabled\": \"સક્ષમ\",\n    \"global.finish\": \"સમાપ્ત\",\n    \"global.marketplace\": \"માર્કેટપ્લેસ\",\n    \"global.name\": \"નામ\",\n    \"global.none\": \"કોઈ નહિ\",\n    \"global.password\": \"પાસવર્ડ\",\n    \"global.plugins\": \"પ્લગઇન્સ\",\n    \"global.profile\": \"પ્રોફાઇલ\",\n    \"global.prompt.unsaved\": \"શું તમે ખરેખર આ પૃષ્ઠ છોડવા માંગો છો? તમારા બધા ફેરફારો ખોવાઈ જશે\",\n    \"global.reset-password\": \"પાસવર્ડ રીસેટ કરો\",\n    \"global.roles\": \"ભૂમિકાઓ\",\n    \"global.save\": \"સાચવો\",\n    \"global.see-more\": \"વધુ જુઓ\",\n    \"global.select\": \"પસંદ કરો\",\n    \"global.select-all-entries\": \"બધી એન્ટ્રી પસંદ કરો\",\n    \"global.settings\": \"સેટિંગ્સ\",\n    \"global.type\": \"પ્રકાર\",\n    \"global.users\": \"વપરાશકર્તાઓ\",\n    \"notification.contentType.relations.conflict\": \"સામગ્રીના પ્રકારમાં વિરોધાભાસી સંબંધો છે\",\n    \"notification.default.title\": \"માહિતી:\",\n    \"notification.error\": \"એક ભૂલ આવી છે\",\n    \"notification.error.layout\": \"લેઆઉટ પુનઃપ્રાપ્ત કરી શકાયું નથી\",\n    \"notification.form.error.fields\": \"ફોર્મમાં કેટલીક ભૂલો છે\",\n    \"notification.form.success.fields\": \"ફેરફારો સાચવેલ\",\n    \"notification.link-copied\": \"લિંક ક્લિપબોર્ડમાં કોપી કરી છે\",\n    \"notification.permission.not-allowed-read\": \"તમને આ દસ્તાવેજ જોવાની મંજૂરી નથી\",\n    \"notification.success.delete\": \"આઇટમ કાઢી નાખવામાં આવી છે\",\n    \"notification.success.saved\": \"સાચવેલ\",\n    \"notification.success.title\": \"સફળતા:\",\n    \"notification.version.update.message\": \"સ્ટ્રેપીનું નવું સંસ્કરણ ઉપલબ્ધ છે!\",\n    \"notification.warning.title\": \"ચેતવણી:\",\n    \"અથવા\": \"અથવા\",\n    \"request.error.model.unknown\": \"આ મોડલ અસ્તિત્વમાં નથી\",\n    skipToContent: skipToContent,\n    submit: submit\n};\n\nexport { Analytics, anErrorOccurred, clearLabel, gu as default, skipToContent, submit };\n//# sourceMappingURL=gu.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,YAAY;AAChB,IAAI,kBAAkB;AACtB,IAAI,aAAa;AACjB,IAAI,gBAAgB;AACpB,IAAI,SAAS;AACb,IAAI,KAAK;AAAA,EACL;AAAA,EACA,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0CAA0C;AAAA,EAC1C,iCAAiC;AAAA,EACjC,sCAAsC;AAAA,EACtC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,yDAAyD;AAAA,EACzD,iDAAiD;AAAA,EACjD,8DAA8D;AAAA,EAC9D,mEAAmE;AAAA,EACnE,kEAAkE;AAAA,EAClE,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,8DAA8D;AAAA,EAC9D,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,oDAAoD;AAAA,EACpD,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,mCAAmC;AAAA,EACnC,iDAAiD;AAAA,EACjD,yDAAyD;AAAA,EACzD,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,wCAAwC;AAAA,EACxC,0CAA0C;AAAA,EAC1C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,6CAA6C;AAAA,EAC7C,yDAAyD;AAAA,EACzD,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,wCAAwC;AAAA,EACxC,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,2CAA2C;AAAA,EAC3C,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,4CAA4C;AAAA,EAC5C,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,2BAA2B;AAAA,EAC3B,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,qBAAqB;AAAA,EACrB,sBAAsB;AAAA,EACtB,2BAA2B;AAAA,EAC3B,gDAAgD;AAAA,EAChD,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,eAAe;AAAA,EACf,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,2CAA2C;AAAA,EAC3C,mDAAmD;AAAA,EACnD,2CAA2C;AAAA,EAC3C,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,uDAAuD;AAAA,EACvD,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,kDAAkD;AAAA,EAClD,kDAAkD;AAAA,EAClD,wCAAwC;AAAA,EACxC,mDAAmD;AAAA,EACnD,yDAAyD;AAAA,EACzD;AAAA,EACA,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,wCAAwC;AAAA,EACxC,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,4CAA4C;AAAA,EAC5C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,+CAA+C;AAAA,EAC/C,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,+CAA+C;AAAA,EAC/C,sDAAsD;AAAA,EACtD,wDAAwD;AAAA,EACxD,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,yDAAyD;AAAA,EACzD,qDAAqD;AAAA,EACrD,4CAA4C;AAAA,EAC5C,2CAA2C;AAAA,EAC3C,gDAAgD;AAAA,EAChD,4CAA4C;AAAA,EAC5C,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,sCAAsC;AAAA,EACtC,2CAA2C;AAAA,EAC3C,wDAAwD;AAAA,EACxD,sDAAsD;AAAA,EACtD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,2DAA2D;AAAA,EAC3D,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,oCAAoC;AAAA,EACpC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,2CAA2C;AAAA,EAC3C,2BAA2B;AAAA,EAC3B,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,+EAA+E;AAAA,EAC/E,qEAAqE;AAAA,EACrE,yEAAyE;AAAA,EACzE,mEAAmE;AAAA,EACnE,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,4DAA4D;AAAA,EAC5D,0DAA0D;AAAA,EAC1D,sEAAsE;AAAA,EACtE,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,qDAAqD;AAAA,EACrD,wDAAwD;AAAA,EACxD,yDAAyD;AAAA,EACzD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,qCAAqC;AAAA,EACrC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,oEAAoE;AAAA,EACpE,4DAA4D;AAAA,EAC5D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,wDAAwD;AAAA,EACxD,4BAA4B;AAAA,EAC5B,sBAAsB;AAAA,EACtB,+BAA+B;AAAA,EAC/B,yCAAyC;AAAA,EACzC,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,4BAA4B;AAAA,EAC5B,uBAAuB;AAAA,EACvB,kBAAkB;AAAA,EAClB,yCAAyC;AAAA,EACzC,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,gCAAgC;AAAA,EAChC,wCAAwC;AAAA,EACxC,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB;AAAA,EACA,eAAe;AAAA,EACf,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,+CAA+C;AAAA,EAC/C,qDAAqD;AAAA,EACrD,sDAAsD;AAAA,EACtD,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,0CAA0C;AAAA,EAC1C,4CAA4C;AAAA,EAC5C,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,gDAAgD;AAAA,EAChD,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,yDAAyD;AAAA,EACzD,+CAA+C;AAAA,EAC/C,gDAAgD;AAAA,EAChD,uDAAuD;AAAA,EACvD,+BAA+B;AAAA,EAC/B,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,yCAAyC;AAAA,EACzC,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,wBAAwB;AAAA,EACxB,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,eAAe;AAAA,EACf,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,wBAAwB;AAAA,EACxB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,sBAAsB;AAAA,EACtB,eAAe;AAAA,EACf,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,yBAAyB;AAAA,EACzB,yBAAyB;AAAA,EACzB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,+CAA+C;AAAA,EAC/C,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,4CAA4C;AAAA,EAC5C,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B,QAAQ;AAAA,EACR,+BAA+B;AAAA,EAC/B;AAAA,EACA;AACJ;", "names": []}