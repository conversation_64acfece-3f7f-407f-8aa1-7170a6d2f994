{"version": 3, "sources": ["../../../@strapi/content-manager/dist/admin/translations/id.json.mjs"], "sourcesContent": ["var groups = \"Grup\";\nvar models = \"Tipe Koleksi\";\nvar pageNotFound = \"Halaman tidak ditemukan\";\nvar id = {\n    \"EditRelations.title\": \"Data relasional\",\n    \"api.id\": \"API ID\",\n    \"components.AddFilterCTA.add\": \"Filter\",\n    \"components.AddFilterCTA.hide\": \"Filter\",\n    \"components.DraggableAttr.edit\": \"Klik untuk mengedit\",\n    \"components.DynamicZone.pick-compo\": \"Pilih satu komponen\",\n    \"components.DynamicZone.required\": \"Komponen diperlukan\",\n    \"components.EmptyAttributesBlock.button\": \"Masuk ke halaman pengaturan\",\n    \"components.EmptyAttributesBlock.description\": \"Anda dapat mengubah pengaturan Anda\",\n    \"components.FieldItem.linkToComponentLayout\": \"Mengatur tata letak komponen\",\n    \"components.FilterOptions.button.apply\": \"Terapkan\",\n    \"components.FiltersPickWrapper.PluginHeader.actions.apply\": \"Terapkan\",\n    \"components.FiltersPickWrapper.PluginHeader.actions.clearAll\": \"Bersihkan semua\",\n    \"components.FiltersPickWrapper.PluginHeader.description\": \"Tetapkan ketentuan yang akan diterapkan untuk memfilter entri\",\n    \"components.FiltersPickWrapper.PluginHeader.title.filter\": \"Filter\",\n    \"components.FiltersPickWrapper.hide\": \"Sembunyikan\",\n    \"components.LimitSelect.itemsPerPage\": \"Item per halaman\",\n    \"components.NotAllowedInput.text\": \"Tidak ada izin untuk melihat bidang ini\",\n    \"components.Search.placeholder\": \"Telusuri entri ...\",\n    \"components.Select.draft-info-title\": \"Status: Draf\",\n    \"components.Select.publish-info-title\": \"Status: Diterbitkan\",\n    \"components.SettingsViewWrapper.pluginHeader.description.edit-settings\": \"Sesuaikan bagaimana tampilan edit akan terlihat.\",\n    \"components.SettingsViewWrapper.pluginHeader.description.list-settings\": \"Tentukan pengaturan tampilan daftar.\",\n    \"components.SettingsViewWrapper.pluginHeader.title\": \"Konfigurasi tampilan - {name}\",\n    \"components.TableDelete.delete\": \"Hapus semua\",\n    \"components.TableDelete.deleteSelected\": \"Hapus terpilih\",\n    \"components.TableEmpty.withFilters\": \"Tidak ada {contentType} dengan filter yang diterapkan ...\",\n    \"components.TableEmpty.withSearch\": \"Tidak ada {contentType} yang sesuai dengan pencarian ({search}) ...\",\n    \"components.TableEmpty.withoutFilter\": \"Tidak ada {contentType} ...\",\n    \"components.empty-repeatable\": \"Belum ada entri. Klik tombol di bawah untuk menambahkannya.\",\n    \"components.notification.info.maximum-requirement\": \"Anda telah mencapai jumlah bidang maksimum\",\n    \"components.notification.info.minimum-requirement\": \"Bidang telah ditambahkan agar sesuai dengan persyaratan minimum\",\n    \"components.reset-entry\": \"Atur ulang entri\",\n    \"components.uid.apply\": \"menerapkan\",\n    \"components.uid.available\": \"tersedia\",\n    \"components.uid.regenerate\": \"diperbarui\",\n    \"components.uid.suggested\": \"disarankan\",\n    \"components.uid.unavailable\": \"tidak tersedia\",\n    \"containers.Edit.Link.Layout\": \"Konfigurasi tata letaknya\",\n    \"containers.Edit.Link.Model\": \"Edit collection-type\",\n    \"containers.Edit.addAnItem\": \"Tambahkan item...\",\n    \"containers.Edit.clickToJump\": \"Klik untuk melompat ke entri\",\n    \"containers.Edit.delete\": \"Hapus\",\n    \"containers.Edit.delete-entry\": \"Hapus entri ini\",\n    \"containers.Edit.editing\": \"Mengedit...\",\n    \"containers.Edit.information\": \"Informasi\",\n    \"containers.Edit.information.by\": \"Oleh\",\n    \"containers.Edit.information.draftVersion\": \"versi draf\",\n    \"containers.Edit.information.editing\": \"Mengedit\",\n    \"containers.Edit.information.lastUpdate\": \"Terakhir diperbarui\",\n    \"containers.Edit.information.publishedVersion\": \"versi publikasi\",\n    \"containers.Edit.pluginHeader.title.new\": \"Buat entri\",\n    \"containers.Edit.reset\": \"Atur ulang\",\n    \"containers.Edit.returnList\": \"Kembali ke list\",\n    \"containers.Edit.seeDetails\": \"Selengkapnya\",\n    \"containers.Edit.submit\": \"Simpan\",\n    \"containers.EditSettingsView.modal-form.edit-field\": \"Edit bidang\",\n    \"containers.EditView.notification.errors\": \"Form tersebut mengandung beberapa kesalahan\",\n    \"containers.Home.introduction\": \"Untuk mengedit entri Anda, buka tautan khusus di menu sebelah kiri. Plugin ini tidak memiliki cara yang tepat untuk mengedit pengaturan dan masih dalam pengembangan aktif.\",\n    \"containers.Home.pluginHeaderDescription\": \"Kelola entri Anda melalui antarmuka yang kuat dan indah.\",\n    \"containers.Home.pluginHeaderTitle\": \"Pengelola Konten\",\n    \"containers.List.draft\": \"Draf\",\n    \"containers.List.errorFetchRecords\": \"Eror\",\n    \"containers.List.published\": \"Dipublikasi\",\n    \"containers.list.displayedFields\": \"Bidang Ditampilkan\",\n    \"containers.list.table-headers.publishedAt\": \"Status\",\n    \"containers.ListSettingsView.modal-form.edit-label\": \"Edit label\",\n    \"containers.SettingPage.add.field\": \"Tambah bidang lain\",\n    \"containers.SettingPage.attributes\": \"Atribut bidang\",\n    \"containers.SettingPage.attributes.description\": \"Tentukan urutan bidang\",\n    \"containers.SettingPage.editSettings.description\": \"Tarik & lepas bidang untuk membentuk layout\",\n    \"containers.SettingPage.editSettings.entry.title\": \"Judul entri\",\n    \"containers.SettingPage.editSettings.entry.title.description\": \"Atur bidang yang ditampilkan di entri mu\",\n    \"containers.SettingPage.editSettings.title\": \"Edit tampilan (pengaturan)\",\n    \"containers.SettingPage.layout\": \"Layout\",\n    \"containers.SettingPage.listSettings.description\": \"Atur opsi untuk collection type ini\",\n    \"containers.SettingPage.listSettings.title\": \"Tampilan list (pengaturan)\",\n    \"containers.SettingPage.pluginHeaderDescription\": \"Atur pengaturan spesifik untuk Collection Type ini\",\n    \"containers.SettingPage.settings\": \"Pengaturan\",\n    \"containers.SettingPage.view\": \"Tampilan\",\n    \"containers.SettingViewModel.pluginHeader.title\": \"Pengelola Konten - {name}\",\n    \"containers.SettingsPage.Block.contentType.description\": \"Atur pengaturan spesifik\",\n    \"containers.SettingsPage.Block.contentType.title\": \"Collection Types\",\n    \"containers.SettingsPage.Block.generalSettings.description\": \"Atur opsi default Collection Types anda\",\n    \"containers.SettingsPage.Block.generalSettings.title\": \"UUmum\",\n    \"containers.SettingsPage.pluginHeaderDescription\": \"Konfigurasikan pengaturan untuk semua jenis dan Grup Koleksi Anda\",\n    \"containers.SettingsView.list.subtitle\": \"Konfigurasikan tata letak dan tampilan jenis dan grup Koleksi Anda\",\n    \"containers.SettingsView.list.title\": \"Konfigurasi tampilan\",\n    \"emptyAttributes.button\": \"Buka pembuat tipe koleksi\",\n    \"emptyAttributes.description\": \"Tambahkan bidang pertama Anda ke Jenis Koleksi Anda\",\n    \"emptyAttributes.title\": \"Belum ada bidang\",\n    \"error.attribute.key.taken\": \"Nilai ini sudah ada\",\n    \"error.attribute.sameKeyAndName\": \"Tidak boleh sama\",\n    \"error.attribute.taken\": \"Nama bidang ini sudah ada\",\n    \"error.contentTypeName.taken\": \"Nama ini sudah ada\",\n    \"error.model.fetch\": \"Terjadi kesalahan selama pengambilan konfigurasi model.\",\n    \"error.record.create\": \"Terjadi kesalahan selama pembuatan rekaman.\",\n    \"error.record.delete\": \"Terjadi kesalahan selama penghapusan catatan.\",\n    \"error.record.fetch\": \"Terjadi kesalahan selama pengambilan rekaman.\",\n    \"error.record.update\": \"Terjadi kesalahan selama pembaruan catatan.\",\n    \"error.records.count\": \"Terjadi kesalahan selama pengambilan catatan hitungan.\",\n    \"error.records.fetch\": \"Terjadi kesalahan selama pengambilan catatan.\",\n    \"error.schema.generation\": \"Terjadi kesalahan selama pembuatan skema.\",\n    \"error.validation.json\": \"Ini bukan JSON\",\n    \"error.validation.max\": \"Nilainya terlalu tinggi.\",\n    \"error.validation.maxLength\": \"Nilainya terlalu panjang.\",\n    \"error.validation.min\": \"Nilainya terlalu rendah.\",\n    \"error.validation.minLength\": \"The value is too short.\",\n    \"error.validation.minSupMax\": \"Tidak bisa lebih unggul.\",\n    \"error.validation.regex\": \"Nilainya tidak cocok dengan regex.\",\n    \"error.validation.required\": \"Input nilai ini diperlukan.\",\n    \"form.Input.bulkActions\": \"Aktifkan tindakan massal\",\n    \"form.Input.defaultSort\": \"Atribut sortir default\",\n    \"form.Input.description\": \"Deskripsi\",\n    \"form.Input.description.placeholder\": \"Nama tampilan di profil\",\n    \"form.Input.editable\": \"Bidang yang dapat diedit\",\n    \"form.Input.filters\": \"Aktifkan filter\",\n    \"form.Input.label\": \"Label\",\n    \"form.Input.label.inputDescription\": \"Nilai ini menggantikan label yang ditampilkan di kepala tabel\",\n    \"form.Input.pageEntries\": \"Entri per halaman\",\n    \"form.Input.pageEntries.inputDescription\": \"Catatan: Anda dapat mengganti nilai ini di halaman pengaturan Jenis Koleksi.\",\n    \"form.Input.placeholder\": \"Placeholder\",\n    \"form.Input.placeholder.placeholder\": \"My awesome value\",\n    \"form.Input.search\": \"Aktifkan pencarian\",\n    \"form.Input.search.field\": \"Aktifkan pencarian di bidang ini\",\n    \"form.Input.sort.field\": \"Aktifkan urutkan di bidang ini\",\n    \"form.Input.wysiwyg\": \"Tampil sebagai WYSIWYG\",\n    \"global.displayedFields\": \"Bidang yang Ditampilkan\",\n    groups: groups,\n    \"groups.numbered\": \"Grup ({number})\",\n    models: models,\n    \"models.numbered\": \"Tipe Koleksi ({number})\",\n    \"notification.error.displayedFields\": \"Anda membutuhkan setidaknya satu bidang yang ditampilkan\",\n    \"notification.error.relationship.fetch\": \"Terjadi kesalahan selama pengambilan hubungan.\",\n    \"notification.info.SettingPage.disableSort\": \"Anda harus memiliki satu atribut dengan pengurutan yang diperbolehkan\",\n    \"notification.info.minimumFields\": \"Anda harus memiliki setidaknya satu bidang yang ditampilkan\",\n    \"notification.upload.error\": \"Terjadi kesalahan saat mengupload file Anda\",\n    pageNotFound: pageNotFound,\n    \"permissions.not-allowed.create\": \"Anda tidak diizinkan membuat dokumen\",\n    \"permissions.not-allowed.update\": \"Anda tidak diizinkan untuk melihat dokumen ini\",\n    \"plugin.description.long\": \"Cara cepat untuk melihat, mengedit, dan menghapus data di database Anda.\",\n    \"plugin.description.short\": \"Cara cepat untuk melihat, mengedit, dan menghapus data di database Anda.\",\n    \"success.record.delete\": \"Dihapus\",\n    \"success.record.publish\": \"Diterbitkan\",\n    \"success.record.save\": \"Disimpan\",\n    \"success.record.unpublish\": \"Didraf\",\n    \"popUpWarning.warning.publish-question\": \"Apakah Anda masih ingin menerbitkannya?\",\n    \"popUpwarning.warning.has-draft-relations.button-confirm\": \"Ya, terbitkan\"\n};\n\nexport { id as default, groups, models, pageNotFound };\n//# sourceMappingURL=id.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,SAAS;AACb,IAAI,SAAS;AACb,IAAI,eAAe;AACnB,IAAI,KAAK;AAAA,EACL,uBAAuB;AAAA,EACvB,UAAU;AAAA,EACV,+BAA+B;AAAA,EAC/B,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,4DAA4D;AAAA,EAC5D,+DAA+D;AAAA,EAC/D,0DAA0D;AAAA,EAC1D,2DAA2D;AAAA,EAC3D,sCAAsC;AAAA,EACtC,uCAAuC;AAAA,EACvC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,yEAAyE;AAAA,EACzE,yEAAyE;AAAA,EACzE,qDAAqD;AAAA,EACrD,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,uCAAuC;AAAA,EACvC,+BAA+B;AAAA,EAC/B,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,6BAA6B;AAAA,EAC7B,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA,EAC9B,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,6BAA6B;AAAA,EAC7B,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,kCAAkC;AAAA,EAClC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,yBAAyB;AAAA,EACzB,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,qDAAqD;AAAA,EACrD,2CAA2C;AAAA,EAC3C,gCAAgC;AAAA,EAChC,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,6CAA6C;AAAA,EAC7C,qDAAqD;AAAA,EACrD,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,+DAA+D;AAAA,EAC/D,6CAA6C;AAAA,EAC7C,iCAAiC;AAAA,EACjC,mDAAmD;AAAA,EACnD,6CAA6C;AAAA,EAC7C,kDAAkD;AAAA,EAClD,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,kDAAkD;AAAA,EAClD,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,6DAA6D;AAAA,EAC7D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,yCAAyC;AAAA,EACzC,sCAAsC;AAAA,EACtC,0BAA0B;AAAA,EAC1B,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,qCAAqC;AAAA,EACrC,0BAA0B;AAAA,EAC1B,2CAA2C;AAAA,EAC3C,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B;AAAA,EACA,mBAAmB;AAAA,EACnB;AAAA,EACA,mBAAmB;AAAA,EACnB,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B;AAAA,EACA,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,4BAA4B;AAAA,EAC5B,yCAAyC;AAAA,EACzC,2DAA2D;AAC/D;", "names": []}