{"version": 3, "sources": ["../../../@strapi/content-type-builder/dist/admin/translations/ja.json.mjs"], "sourcesContent": ["var from = \"from\";\nvar ja = {\n    \"attribute.boolean\": \"Boolean\",\n    \"attribute.date\": \"Date\",\n    \"attribute.email\": \"Email\",\n    \"attribute.enumeration\": \"Enumeration\",\n    \"attribute.json\": \"JSON\",\n    \"attribute.media\": \"Media\",\n    \"attribute.password\": \"Password\",\n    \"attribute.relation\": \"Relation\",\n    \"attribute.text\": \"Text\",\n    \"form.attribute.item.customColumnName\": \"カスタム列名\",\n    \"form.attribute.item.customColumnName.description\": \"これは、データベースのカラム名をAPIのレスポンスのより包括的なフォーマットに変更する場合に便利です\",\n    \"form.attribute.item.defineRelation.fieldName\": \"フィールド名\",\n    \"form.attribute.item.enumeration.graphql\": \"GraphQLの名前の上書き\",\n    \"form.attribute.item.enumeration.graphql.description\": \"GraphQLの既定の生成名をオーバーライドできます\",\n    \"form.attribute.item.enumeration.rules\": \"Values (one line per value)\",\n    \"form.attribute.item.maximum\": \"最大値\",\n    \"form.attribute.item.maximumLength\": \"最大長\",\n    \"form.attribute.item.minimum\": \"最小値\",\n    \"form.attribute.item.minimumLength\": \"最小長\",\n    \"form.attribute.item.number.type\": \"数値形式\",\n    \"form.attribute.item.number.type.decimal\": \"decimal (ex: 2.22)\",\n    \"form.attribute.item.number.type.float\": \"float (ex: 3.33333333)\",\n    \"form.attribute.item.number.type.integer\": \"integer (ex: 10)\",\n    \"form.attribute.item.requiredField\": \"必須フィールド\",\n    \"form.attribute.item.requiredField.description\": \"このフィールドが空の場合、エントリを作成することはできません\",\n    \"form.attribute.item.uniqueField\": \"一意のフィールド\",\n    \"form.attribute.item.uniqueField.description\": \"同じ内容の既存のエントリがある場合、エントリを作成することはできません\",\n    \"form.attribute.settings.default\": \"デフォルト値\",\n    \"form.button.cancel\": \"キャンセル\",\n    from: from,\n    \"modelPage.attribute.relationWith\": \"関係\",\n    \"plugin.description.long\": \"APIのデータ構造をモデル化します。数分で新しいフィールドと関係を作成します。ファイルはプロジェクトで自動的に作成され、更新されます。\",\n    \"plugin.description.short\": \"APIのデータ構造をモデル化します。\",\n    \"popUpForm.navContainer.advanced\": \"高度な設定\",\n    \"popUpForm.navContainer.base\": \"基本設定\",\n    \"popUpWarning.bodyMessage.contentType.delete\": \"このコンテンツタイプを削除してもよろしいですか？\",\n    \"relation.attributeName.placeholder\": \"例：author、category、tag\",\n    \"relation.manyToMany\": \"has and belongs to many\",\n    \"relation.manyToOne\": \"has many\",\n    \"relation.oneToMany\": \"belongs to many\",\n    \"relation.oneToOne\": \"has and belongs to one\",\n    \"relation.oneWay\": \"has one\"\n};\n\nexport { ja as default, from };\n//# sourceMappingURL=ja.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,OAAO;AACX,IAAI,KAAK;AAAA,EACL,qBAAqB;AAAA,EACrB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,gDAAgD;AAAA,EAChD,2CAA2C;AAAA,EAC3C,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,+BAA+B;AAAA,EAC/B,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,mCAAmC;AAAA,EACnC,sBAAsB;AAAA,EACtB;AAAA,EACA,oCAAoC;AAAA,EACpC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,+CAA+C;AAAA,EAC/C,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,qBAAqB;AAAA,EACrB,mBAAmB;AACvB;", "names": []}