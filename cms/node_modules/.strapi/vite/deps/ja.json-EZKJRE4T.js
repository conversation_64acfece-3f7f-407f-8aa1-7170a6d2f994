import "./chunk-PLDDJCW6.js";

// node_modules/@strapi/email/dist/admin/translations/ja.json.mjs
var ja = {
  "Settings.email.plugin.button.test-email": "メール送信のテスト",
  "Settings.email.plugin.label.defaultFrom": "デフォルトの送信アドレス",
  "Settings.email.plugin.label.defaultReplyTo": "デフォルトの返信アドレス",
  "Settings.email.plugin.label.provider": "Eメール　プロバイダ",
  "Settings.email.plugin.label.testAddress": "メール送信のテストをする",
  "Settings.email.plugin.notification.config.error": "Eメールの設定の取得に失敗しました",
  "Settings.email.plugin.notification.data.loaded": "Eメールの設定データはすでに取得済みです",
  "Settings.email.plugin.notification.test.error": "{to}へのテストメースの送信に失敗しました",
  "Settings.email.plugin.notification.test.success": "テストメースの送信に成功しました、{to}のメールボックスを確認してください",
  "Settings.email.plugin.placeholder.defaultFrom": "例: Strapi No-Reply <<EMAIL>>",
  "Settings.email.plugin.placeholder.defaultReplyTo": "例: Strapi <<EMAIL>>",
  "Settings.email.plugin.placeholder.testAddress": "例: <EMAIL>",
  "Settings.email.plugin.subTitle": "Eメールプラグインの設定のテスト",
  "Settings.email.plugin.text.configuration": "このプラグインは{file}ファイルを用いて構成されました、このリンク {link}　のドキュメントを確認してください。",
  "Settings.email.plugin.title": "Eメールの設定",
  "Settings.email.plugin.title.config": "構成",
  "Settings.email.plugin.title.test": "テストメールの送信",
  "SettingsNav.link.settings": "設定",
  "SettingsNav.section-label": "Eメール　プラグイン",
  "components.Input.error.validation.email": "これは無効なEメールアドレスです"
};
export {
  ja as default
};
//# sourceMappingURL=ja.json-EZKJRE4T.js.map
