{"version": 3, "sources": ["../../../@strapi/content-manager/dist/admin/translations/ja.json.mjs"], "sourcesContent": ["var groups = \"Groups\";\nvar models = \"Collection Types\";\nvar pageNotFound = \"ページが見つかりません\";\nvar ja = {\n    \"App.schemas.data-loaded\": \"The schemas have been successfully loaded\",\n    \"ListViewTable.relation-loaded\": \"The relations have been loaded\",\n    \"EditRelations.title\": \"リレーショナルデータ\",\n    \"HeaderLayout.button.label-add-entry\": \"Create new entry\",\n    \"api.id\": \"API ID\",\n    \"components.AddFilterCTA.add\": \"フィルタ\",\n    \"components.AddFilterCTA.hide\": \"フィルタ\",\n    \"components.DragHandle-label\": \"Drag\",\n    \"components.DraggableAttr.edit\": \"クリックして編集\",\n    \"components.DraggableCard.delete.field\": \"{item}を削除\",\n    \"components.DraggableCard.edit.field\": \"{item}を編集\",\n    \"components.DraggableCard.move.field\": \"Move {item}\",\n    \"components.ListViewTable.row-line\": \"item line {number}\",\n    \"components.DynamicZone.ComponentPicker-label\": \"Pick one component\",\n    \"components.DynamicZone.add-component\": \"Add a component to {componentName}\",\n    \"components.DynamicZone.delete-label\": \"{name}を削除\",\n    \"components.DynamicZone.error-message\": \"The component contains error(s)\",\n    \"components.DynamicZone.missing-components\": \"There {number, plural, =0 {are # missing components} one {is # missing component} other {are # missing components}}\",\n    \"components.DynamicZone.move-down-label\": \"Move component down\",\n    \"components.DynamicZone.move-up-label\": \"Move component up\",\n    \"components.DynamicZone.pick-compo\": \"Pick one component\",\n    \"components.DynamicZone.required\": \"Component is required\",\n    \"components.EmptyAttributesBlock.button\": \"設定ページに移動\",\n    \"components.EmptyAttributesBlock.description\": \"設定を変更することができます\",\n    \"components.FieldItem.linkToComponentLayout\": \"Set the component's layout\",\n    \"components.FieldSelect.label\": \"Add a field\",\n    \"components.FilterOptions.button.apply\": \"適用\",\n    \"components.FiltersPickWrapper.PluginHeader.actions.apply\": \"適用\",\n    \"components.FiltersPickWrapper.PluginHeader.actions.clearAll\": \"すべてクリア\",\n    \"components.FiltersPickWrapper.PluginHeader.description\": \"エントリをフィルタリングするための条件を設定する\",\n    \"components.FiltersPickWrapper.PluginHeader.title.filter\": \"フィルタ\",\n    \"components.FiltersPickWrapper.hide\": \"隠す\",\n    \"components.LeftMenu.Search.label\": \"Search for a content type\",\n    \"components.LeftMenu.collection-types\": \"Collection Types\",\n    \"components.LeftMenu.single-types\": \"Single Types\",\n    \"components.LimitSelect.itemsPerPage\": \"ページあたりのアイテム数\",\n    \"components.NotAllowedInput.text\": \"No permissions to see this field\",\n    \"components.RepeatableComponent.error-message\": \"The component(s) contain error(s)\",\n    \"components.Search.placeholder\": \"エントリを検索する...\",\n    \"components.Select.draft-info-title\": \"State: Draft\",\n    \"components.Select.publish-info-title\": \"State: Published\",\n    \"components.SettingsViewWrapper.pluginHeader.description.edit-settings\": \"Customize how the edit view will look like.\",\n    \"components.SettingsViewWrapper.pluginHeader.description.list-settings\": \"Define the settings of the list view.\",\n    \"components.SettingsViewWrapper.pluginHeader.title\": \"Configure the view - {name}\",\n    \"components.TableDelete.delete\": \"すべて削除\",\n    \"components.TableDelete.deleteSelected\": \"選択を削除します\",\n    \"components.TableDelete.label\": \"{number, plural, one {# entry} other {# entries}} selected\",\n    \"components.TableEmpty.withFilters\": \"適用されたフィルタには{contentType}はありません...\",\n    \"components.TableEmpty.withSearch\": \"検索に対応する{contentType}はありません（{search}）...\",\n    \"components.TableEmpty.withoutFilter\": \"{contentType}はありません...\",\n    \"components.empty-repeatable\": \"No entry yet. Click to add one.\",\n    \"components.notification.info.maximum-requirement\": \"You have already reached the maximum number of fields\",\n    \"components.notification.info.minimum-requirement\": \"A field has been added to match the minimum requirement\",\n    \"components.repeatable.reorder.error\": \"An error occurred while reordering your component's field, please try again\",\n    \"components.reset-entry\": \"Reset entry\",\n    \"components.uid.apply\": \"apply\",\n    \"components.uid.available\": \"Available\",\n    \"components.uid.regenerate\": \"Regenerate\",\n    \"components.uid.suggested\": \"suggested\",\n    \"components.uid.unavailable\": \"Unavailable\",\n    \"containers.Edit.Link.Layout\": \"Configure the layout\",\n    \"containers.Edit.Link.Model\": \"Edit the collection-type\",\n    \"containers.Edit.addAnItem\": \"アイテムを追加する...\",\n    \"containers.Edit.clickToJump\": \"クリックするとエントリにジャンプします\",\n    \"containers.Edit.delete\": \"削除\",\n    \"containers.Edit.delete-entry\": \"Delete this entry\",\n    \"containers.Edit.editing\": \"編集...\",\n    \"containers.Edit.information\": \"Information\",\n    \"containers.Edit.information.by\": \"By\",\n    \"containers.Edit.information.created\": \"Created\",\n    \"containers.Edit.information.draftVersion\": \"draft version\",\n    \"containers.Edit.information.editing\": \"Editing\",\n    \"containers.Edit.information.lastUpdate\": \"Last update\",\n    \"containers.Edit.information.publishedVersion\": \"published version\",\n    \"containers.Edit.pluginHeader.title.new\": \"Create an entry\",\n    \"containers.Edit.reset\": \"リセット\",\n    \"containers.Edit.returnList\": \"リストに戻る\",\n    \"containers.Edit.seeDetails\": \"詳細\",\n    \"containers.Edit.submit\": \"保存\",\n    \"containers.EditSettingsView.modal-form.edit-field\": \"Edit the field\",\n    \"containers.EditView.add.new-entry\": \"Add an entry\",\n    \"containers.EditView.notification.errors\": \"The form contains some errors\",\n    \"containers.Home.introduction\": \"あなたのエントリーを編集するには、左側のメニューの特定のリンクに行きます。このプラグインは設定を編集する適切な方法がなく、まだアクティブな開発中です\",\n    \"containers.Home.pluginHeaderDescription\": \"パワフルで美しいインターフェイスでエントリを管理します。\",\n    \"containers.Home.pluginHeaderTitle\": \"コンテンツ マネージャ\",\n    \"containers.List.draft\": \"Draft\",\n    \"containers.List.errorFetchRecords\": \"エラー\",\n    \"containers.List.published\": \"Published\",\n    \"containers.list.displayedFields\": \"フィールドが表示されました\",\n    \"containers.list.items\": \"{number, plural, =0 {items} one {item} other {items}}\",\n    \"containers.list.table-headers.publishedAt\": \"State\",\n    \"containers.ListSettingsView.modal-form.edit-label\": \"Edit {fieldName}\",\n    \"containers.SettingPage.add.field\": \"Insert another field\",\n    \"containers.SettingPage.attributes\": \"属性フィールド\",\n    \"containers.SettingPage.attributes.description\": \"属性の順序を定義する\",\n    \"containers.SettingPage.editSettings.description\": \"フィールドをドラッグアンドドロップしてレイアウトを作成する\",\n    \"containers.SettingPage.editSettings.entry.title\": \"Entry title\",\n    \"containers.SettingPage.editSettings.entry.title.description\": \"Set the displayed field of your entry\",\n    \"containers.SettingPage.editSettings.relation-field.description\": \"Set the displayed field in both the edit and list views\",\n    \"containers.SettingPage.editSettings.title\": \"編集 (設定)\",\n    \"containers.SettingPage.layout\": \"Layout\",\n    \"containers.SettingPage.listSettings.description\": \"Configure the options for this collection type\",\n    \"containers.SettingPage.listSettings.title\": \"一覧 (設定)\",\n    \"containers.SettingPage.pluginHeaderDescription\": \"Configure the specific settings for this Collection Type\",\n    \"containers.SettingPage.settings\": \"設定\",\n    \"containers.SettingPage.view\": \"View\",\n    \"containers.SettingViewModel.pluginHeader.title\": \"コンテンツ管理 - {name}\",\n    \"containers.SettingsPage.Block.contentType.description\": \"特定の設定を構成する\",\n    \"containers.SettingsPage.Block.contentType.title\": \"Collection Types\",\n    \"containers.SettingsPage.Block.generalSettings.description\": \"Configure the default options for your Collection Types\",\n    \"containers.SettingsPage.Block.generalSettings.title\": \"一般\",\n    \"containers.SettingsPage.pluginHeaderDescription\": \"Configure the settings for all your Collection types and Groups\",\n    \"containers.SettingsView.list.subtitle\": \"Configure the layout and display of your Collection types and groups\",\n    \"containers.SettingsView.list.title\": \"Display configurations\",\n    \"edit-settings-view.link-to-ctb.components\": \"Edit the component\",\n    \"edit-settings-view.link-to-ctb.content-types\": \"Edit the content type\",\n    \"emptyAttributes.button\": \"Go to collection type builder\",\n    \"emptyAttributes.description\": \"Add your first field to your Collection Type\",\n    \"emptyAttributes.title\": \"フィールドはまだありません\",\n    \"error.attribute.key.taken\": \"この値は既に存在します\",\n    \"error.attribute.sameKeyAndName\": \"同じにすることはできません\",\n    \"error.attribute.taken\": \"このフィールド名は既に存在します\",\n    \"error.contentTypeName.taken\": \"この名前は既に存在します\",\n    \"error.model.fetch\": \"モデルの設定フェッチ中にエラーが発生しました\",\n    \"error.record.create\": \"レコードの作成中にエラーが発生しました\",\n    \"error.record.delete\": \"レコードの削除中にエラーが発生しました\",\n    \"error.record.fetch\": \"レコードの取得中にエラーが発生しました\",\n    \"error.record.update\": \"レコードの更新中にエラーが発生しました\",\n    \"error.records.count\": \"カウントレコードのフェッチ中にエラーが発生しました.\",\n    \"error.records.fetch\": \"レコードの取得中にエラーが発生しました\",\n    \"error.schema.generation\": \"スキーマの生成中にエラーが発生しました\",\n    \"error.validation.json\": \"これはJSONではありません\",\n    \"error.validation.max\": \"値が高すぎます\",\n    \"error.validation.maxLength\": \"値が長すぎます\",\n    \"error.validation.min\": \"値が低すぎます\",\n    \"error.validation.minLength\": \"値が小さすぎます\",\n    \"error.validation.minSupMax\": \"優れていることはできません\",\n    \"error.validation.regex\": \"値は正規表現と一致しません\",\n    \"error.validation.required\": \"この値の入力は必須です\",\n    \"form.Input.bulkActions\": \"一括処理を有効にする\",\n    \"form.Input.defaultSort\": \"デフォルトのソート属性\",\n    \"form.Input.description\": \"説明文\",\n    \"form.Input.description.placeholder\": \"プロフィールの表示名\",\n    \"form.Input.editable\": \"編集可能なフィールド\",\n    \"form.Input.filters\": \"フィルタを有効にする\",\n    \"form.Input.label\": \"Label\",\n    \"form.Input.label.inputDescription\": \"この値は、テーブルの先頭に表示されるラベル\",\n    \"form.Input.pageEntries\": \"1ページあたりのエントリ数\",\n    \"form.Input.pageEntries.inputDescription\": \"Note: You can override this value in the Collection Type settings page.\",\n    \"form.Input.placeholder\": \"プレースホルダー\",\n    \"form.Input.placeholder.placeholder\": \"My awesome value\",\n    \"form.Input.search\": \"検索を有効にする\",\n    \"form.Input.search.field\": \"このフィールドで検索を有効にする\",\n    \"form.Input.sort.field\": \"このフィールドでソートを有効にする\",\n    \"form.Input.sort.order\": \"Default sort order\",\n    \"form.Input.wysiwyg\": \"Display as WYSIWYG\",\n    \"global.displayedFields\": \"Displayed Fields\",\n    groups: groups,\n    \"groups.numbered\": \"Groups ({number})\",\n    \"header.name\": \"Content\",\n    \"link-to-ctb\": \"Edit the model\",\n    models: models,\n    \"models.numbered\": \"Collection Types ({number})\",\n    \"notification.error.displayedFields\": \"少なくとも1つの表示フィールドが必要です\",\n    \"notification.error.relationship.fetch\": \"リレーションシップフェッチ中にエラーが発生しました\",\n    \"notification.info.SettingPage.disableSort\": \"並べ替えを許可する属性が1つ必要です\",\n    \"notification.info.minimumFields\": \"You need to have at least one field displayed\",\n    \"notification.upload.error\": \"An error has occurred while uploading your files\",\n    pageNotFound: pageNotFound,\n    \"pages.ListView.header-subtitle\": \"{number, plural, =0 {# entries} one {# entry} other {# entries}} found\",\n    \"pages.NoContentType.button\": \"Create your first Content-Type\",\n    \"pages.NoContentType.text\": \"You don't have any content yet, we recommend you to create your first Content-Type.\",\n    \"permissions.not-allowed.create\": \"You are not allowed to create a document\",\n    \"permissions.not-allowed.update\": \"You are not allowed to see this document\",\n    \"plugin.description.long\": \"データベース内のデータを表示、編集、削除するための方法。\",\n    \"plugin.description.short\": \"データベース内のデータを表示、編集、削除するための方法。\",\n    \"popover.display-relations.label\": \"Display relations\",\n    \"success.record.delete\": \"削除\",\n    \"success.record.publish\": \"Published\",\n    \"success.record.save\": \"保存\",\n    \"success.record.unpublish\": \"Unpublished\",\n    \"utils.data-loaded\": \"The {number, plural, =1 {entry has} other {entries have}} successfully been loaded\",\n    \"popUpWarning.warning.publish-question\": \"Do you still want to publish?\",\n    \"popUpwarning.warning.has-draft-relations.button-confirm\": \"Yes, publish\"\n};\n\nexport { ja as default, groups, models, pageNotFound };\n//# sourceMappingURL=ja.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,SAAS;AACb,IAAI,SAAS;AACb,IAAI,eAAe;AACnB,IAAI,KAAK;AAAA,EACL,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,uBAAuB;AAAA,EACvB,uCAAuC;AAAA,EACvC,UAAU;AAAA,EACV,+BAA+B;AAAA,EAC/B,gCAAgC;AAAA,EAChC,+BAA+B;AAAA,EAC/B,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,gDAAgD;AAAA,EAChD,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,wCAAwC;AAAA,EACxC,6CAA6C;AAAA,EAC7C,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,gCAAgC;AAAA,EAChC,yCAAyC;AAAA,EACzC,4DAA4D;AAAA,EAC5D,+DAA+D;AAAA,EAC/D,0DAA0D;AAAA,EAC1D,2DAA2D;AAAA,EAC3D,sCAAsC;AAAA,EACtC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,uCAAuC;AAAA,EACvC,mCAAmC;AAAA,EACnC,gDAAgD;AAAA,EAChD,iCAAiC;AAAA,EACjC,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,yEAAyE;AAAA,EACzE,yEAAyE;AAAA,EACzE,qDAAqD;AAAA,EACrD,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,gCAAgC;AAAA,EAChC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,uCAAuC;AAAA,EACvC,+BAA+B;AAAA,EAC/B,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,uCAAuC;AAAA,EACvC,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,6BAA6B;AAAA,EAC7B,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA,EAC9B,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,6BAA6B;AAAA,EAC7B,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,kCAAkC;AAAA,EAClC,uCAAuC;AAAA,EACvC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,yBAAyB;AAAA,EACzB,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,qDAAqD;AAAA,EACrD,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,gCAAgC;AAAA,EAChC,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,6CAA6C;AAAA,EAC7C,qDAAqD;AAAA,EACrD,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,+DAA+D;AAAA,EAC/D,kEAAkE;AAAA,EAClE,6CAA6C;AAAA,EAC7C,iCAAiC;AAAA,EACjC,mDAAmD;AAAA,EACnD,6CAA6C;AAAA,EAC7C,kDAAkD;AAAA,EAClD,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,kDAAkD;AAAA,EAClD,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,6DAA6D;AAAA,EAC7D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,yCAAyC;AAAA,EACzC,sCAAsC;AAAA,EACtC,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,0BAA0B;AAAA,EAC1B,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,qCAAqC;AAAA,EACrC,0BAA0B;AAAA,EAC1B,2CAA2C;AAAA,EAC3C,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B;AAAA,EACA,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,eAAe;AAAA,EACf;AAAA,EACA,mBAAmB;AAAA,EACnB,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B;AAAA,EACA,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,4BAA4B;AAAA,EAC5B,qBAAqB;AAAA,EACrB,yCAAyC;AAAA,EACzC,2DAA2D;AAC/D;", "names": []}