import "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/admin/src/translations/ja.json.mjs
var Analytics = "分析";
var Documentation = "ドキュメンテーション";
var Email = "Eメール";
var Password = "パスワード";
var Provider = "プロバイダ";
var ResetPasswordToken = "パスワードトークンをリセット";
var Role = "Role";
var Username = "ユーザー名";
var Users = "ユーザー";
var anErrorOccurred = "Whoops! Something went wrong. Please, try again.";
var clearLabel = "Clear";
var or = "OR";
var skipToContent = "Skip to content";
var submit = "送信";
var ja = {
  Analytics,
  "Auth.components.Oops.text": "あなたのアカウントは凍結されました",
  "Auth.components.Oops.text.admin": "こちらが間違いであれば、管理者に連絡してください。",
  "Auth.components.Oops.title": "Oops...",
  "Auth.form.button.forgot-password": "メールを送信",
  "Auth.form.button.go-home": "ホームに戻る",
  "Auth.form.button.login": "ログイン",
  "Auth.form.button.login.providers.error": "選択したプロバイダを通して接続することはできません。",
  "Auth.form.button.login.strapi": "Strapiでログインします",
  "Auth.form.button.password-recovery": "パスワードの復元",
  "Auth.form.button.register": "はじめよう",
  "Auth.form.confirmPassword.label": "パスワードを確認",
  "Auth.form.currentPassword.label": "現在のパスワード",
  "Auth.form.email.label": "Eメール",
  "Auth.form.email.placeholder": "<EMAIL>",
  "Auth.form.error.blocked": "あなたのアカウントは管理者からブロックされています",
  "Auth.form.error.code.provide": "不正なコードが提供されました",
  "Auth.form.error.confirmed": "メールが確認できません",
  "Auth.form.error.email.invalid": "このメールは無効です",
  "Auth.form.error.email.provide": "あなたのユーザー名またはメールアドレスを入力してください。",
  "Auth.form.error.email.taken": "Eメールはすでに取得済みです",
  "Auth.form.error.invalid": "識別子またはパスワードが無効です。",
  "Auth.form.error.params.provide": "不適切なパラメータが指定されました。",
  "Auth.form.error.password.format": "あなたのパスワードには、`$`シンボルを3回以上含めることはできません。",
  "Auth.form.error.password.local": "このユーザーは決してローカルパスワードを設定しません。アカウント作成時に使用したプロバイダ経由でログインしてください。",
  "Auth.form.error.password.matching": "パスワードが一致していません。",
  "Auth.form.error.password.provide": "パスワードを入力してください。",
  "Auth.form.error.ratelimit": "試行回数が多すぎる場合は、もう一度試してください。",
  "Auth.form.error.user.not-exist": "このメールは存在しません。",
  "Auth.form.error.username.taken": "ユーザー名は既に使われています。",
  "Auth.form.firstname.label": "名",
  "Auth.form.firstname.placeholder": "カイ",
  "Auth.form.forgot-password.email.label": "メールアドレスを入力",
  "Auth.form.forgot-password.email.label.success": "Eメールが正常に送信されました",
  "Auth.form.lastname.label": "姓",
  "Auth.form.lastname.placeholder": "ドウ",
  "Auth.form.password.hide-password": "パスワードを非表示",
  "Auth.form.password.hint": "Password must contain at least 8 characters, 1 uppercase, 1 lowercase, and 1 number",
  "Auth.form.password.show-password": "パスワードを表示",
  "Auth.form.register.news.label": "新機能や今後の改善についての最新情報を受け取る(受け取る場合は{terms}と{policy}に同意したこととします)。",
  "Auth.form.register.subtitle": "認証情報は、管理パネルで自分を認証するためにのみ使用されます。保存されているすべてのデータは自分のデータベースに保存されます。",
  "Auth.form.rememberMe.label": "記憶する",
  "Auth.form.username.label": "ユーザー名",
  "Auth.form.username.placeholder": "カイ・ドウ",
  "Auth.form.welcome.subtitle": "Strapiアカウントにログイン",
  "Auth.form.welcome.title": "いらっしゃいませ！",
  "Auth.link.forgot-password": "パスワードをお忘れですか？",
  "Auth.link.ready": "サインインする準備ができましたか？",
  "Auth.link.signin": "サインイン",
  "Auth.link.signin.account": "すでにアカウントをお持ちですか？",
  "Auth.login.sso.divider": "または",
  "Auth.login.sso.loading": "認証プロバイダを読み込み中...",
  "Auth.login.sso.subtitle": "SSOを介してアカウントにログイン",
  "Auth.privacy-policy-agreement.policy": "プライバシーポリシー",
  "Auth.privacy-policy-agreement.terms": "利用規約",
  "Content Manager": "コンテンツ管理",
  "Content Type Builder": "コンテンツタイプビルダ",
  Documentation,
  Email,
  "Files Upload": "ファイルアップロード",
  "HomePage.head.title": "ホームページ",
  "HomePage.roadmap": "ロードマップを見る",
  "HomePage.welcome.congrats": "おめでとうございます！",
  "HomePage.welcome.congrats.content": "初期管理者としてログインしました。Strapiが提供する強力な機能を探すために、",
  "HomePage.welcome.congrats.content.bold": "まずはコンテンツタイプを作ることをオススメします。",
  "Media Library": "メディアライブラリ",
  "New entry": "新規投稿",
  Password,
  Provider,
  ResetPasswordToken,
  Role,
  "Roles & Permissions": "ロールと権限",
  "Roles.ListPage.notification.delete-all-not-allowed": "いくつかのロールはユーザーにひもづいているため削除できませんでした",
  "Roles.ListPage.notification.delete-not-allowed": "ユーザーにひもづいているロールは削除できません",
  "Roles.RoleRow.select-all": "Select {name} for bulk actions",
  "Roles.components.List.empty.withSearch": "検索（{}）に合致するロールはありません…",
  "Settings.PageTitle": "設定 - {name}",
  "Settings.apiTokens.addFirstToken": "最初のAPIトークンを追加",
  "Settings.apiTokens.addNewToken": "新しいAPIトークンを追加",
  "Settings.tokens.copy.editMessage": "セキュリティ上の理由から、トークンは一度にのみ見ることができます。",
  "Settings.tokens.copy.editTitle": "このトークンはもうアクセスできません。",
  "Settings.tokens.copy.lastWarning": "このトークンをコピーするようにしてください、一度のみ表示されます。",
  "Settings.apiTokens.create": "エントリを追加",
  "Settings.apiTokens.description": "List of generated tokens to consume the API",
  "Settings.apiTokens.emptyStateLayout": "You don’t have any content yet...",
  "Settings.tokens.notification.copied": "Token copied to clipboard.",
  "Settings.apiTokens.title": "APIトークン",
  "Settings.tokens.types.full-access": "Full access",
  "Settings.tokens.types.read-only": "Read-only",
  "Settings.application.description": "プロジェクトの詳細を見る",
  "Settings.application.edition-title": "現在の版",
  "Settings.application.get-help": "Get help",
  "Settings.application.link-pricing": "すべての価格を見る",
  "Settings.application.link-upgrade": "プロジェクトをアップグレードする",
  "Settings.application.node-version": "Node バージョン",
  "Settings.application.strapi-version": "Strapi バージョン",
  "Settings.application.strapiVersion": "Strapi バージョン",
  "Settings.application.title": "アプリケーション",
  "Settings.error": "エラー",
  "Settings.global": "グローバル設定",
  "Settings.permissions": "管理パネル",
  "Settings.permissions.category": "{category}の権限設定",
  "Settings.permissions.category.plugins": "{category}プラグインの権限設定",
  "Settings.permissions.conditions.anytime": "いつでも",
  "Settings.permissions.conditions.apply": "適用",
  "Settings.permissions.conditions.can": "可能",
  "Settings.permissions.conditions.conditions": "条件を定義",
  "Settings.permissions.conditions.links": "リンク",
  "Settings.permissions.conditions.no-actions": "条件を定義する前に最初に作業（作成・閲覧・更新等）を選択する必要があります。",
  "Settings.permissions.conditions.none-selected": "Anytime",
  "Settings.permissions.conditions.or": "OR",
  "Settings.permissions.conditions.when": "WHEN",
  "Settings.permissions.select-all-by-permission": "Select all {label} permissions",
  "Settings.permissions.select-by-permission": "Select {label} permission",
  "Settings.permissions.users.create": "新しいユーザーを作成",
  "Settings.permissions.users.email": "Eメール",
  "Settings.permissions.users.firstname": "名",
  "Settings.permissions.users.lastname": "姓",
  "Settings.permissions.users.form.sso": "Connect with SSO",
  "Settings.permissions.users.form.sso.description": "When enabled (ON), users can login via SSO",
  "Settings.permissions.users.listview.header.subtitle": "All the users who have access to the Strapi admin panel",
  "Settings.permissions.users.tabs.label": "Tabs Permissions",
  "Settings.profile.form.notify.data.loaded": "Your profile data has been loaded",
  "Settings.profile.form.section.experience.clear.select": "Clear the interface language selected",
  "Settings.profile.form.section.experience.here": "documentation",
  "Settings.profile.form.section.experience.interfaceLanguage": "Interface language",
  "Settings.profile.form.section.experience.interfaceLanguage.hint": "This will only display your own interface in the chosen language.",
  "Settings.profile.form.section.experience.interfaceLanguageHelp": "Selection will change the interface language only for you. Please refer to this {here} to make other languages available for your team.",
  "Settings.profile.form.section.experience.title": "Experience",
  "Settings.profile.form.section.head.title": "ユーザープロフィール",
  "Settings.profile.form.section.profile.page.title": "プロフィールページ",
  "Settings.roles.create.description": "ロールに付与された権利を定義する",
  "Settings.roles.create.title": "ロールを作成",
  "Settings.roles.created": "ロールが作成されました",
  "Settings.roles.edit.title": "ロールを編集",
  "Settings.roles.form.button.users-with-role": "このロールを持つユーザー",
  "Settings.roles.form.created": "作成されました",
  "Settings.roles.form.description": "ロールの名前と説明",
  "Settings.roles.form.permission.property-label": "{label}権限",
  "Settings.roles.form.permissions.attributesPermissions": "フィールド権限",
  "Settings.roles.form.permissions.create": "作成",
  "Settings.roles.form.permissions.delete": "削除",
  "Settings.roles.form.permissions.publish": "公開",
  "Settings.roles.form.permissions.read": "閲覧",
  "Settings.roles.form.permissions.update": "更新",
  "Settings.roles.list.button.add": "新しいロールを追加",
  "Settings.roles.list.description": "ロールの一覧",
  "Settings.roles.title.singular": "ロール",
  "Settings.sso.description": "Configure the settings for the Single Sign-On feature.",
  "Settings.sso.form.defaultRole.description": "It will attach the new authenticated user to the selected role",
  "Settings.sso.form.defaultRole.description-not-allowed": "You need to have the permission to read the admin roles",
  "Settings.sso.form.defaultRole.label": "Default role",
  "Settings.sso.form.registration.description": "Create new user on SSO login if no account exists",
  "Settings.sso.form.registration.label": "Auto-registration",
  "Settings.sso.title": "Single Sign-On",
  "Settings.webhooks.create": "webhookを作成",
  "Settings.webhooks.create.header": "ヘッダーの新規作成",
  "Settings.webhooks.created": "Webhookが作成されました",
  "Settings.webhooks.event.publish-tooltip": "このイベントは下書き・公開のシステムが有効になったコンテンツでのみ発生します",
  "Settings.webhooks.events.create": "作成",
  "Settings.webhooks.events.update": "更新",
  "Settings.webhooks.form.events": "イベント",
  "Settings.webhooks.form.headers": "ヘッダー",
  "Settings.webhooks.form.url": "URL",
  "Settings.webhooks.headers.remove": "Remove header row {number}",
  "Settings.webhooks.key": "キー",
  "Settings.webhooks.list.button.add": "webhookを追加",
  "Settings.webhooks.list.description": "POSTの変更通知を取得する",
  "Settings.webhooks.list.empty.description": "最初のwebhookをこのリストに追加してください。",
  "Settings.webhooks.list.empty.link": "ドキュメントを見る",
  "Settings.webhooks.list.empty.title": "まだwebhookはありません",
  "Settings.webhooks.list.th.actions": "actions",
  "Settings.webhooks.list.th.status": "status",
  "Settings.webhooks.singular": "webhook",
  "Settings.webhooks.title": "Webhooks",
  "Settings.webhooks.to.delete": "{webhooksToDeleteLength, plural, one {# asset} other {# assets}} selected",
  "Settings.webhooks.trigger": "トリガー",
  "Settings.webhooks.trigger.cancel": "トリガーをキャンセルする",
  "Settings.webhooks.trigger.pending": "ペンディング中…",
  "Settings.webhooks.trigger.save": "トリガーを保存してください",
  "Settings.webhooks.trigger.success": "成功!",
  "Settings.webhooks.trigger.success.label": "トリガー成功",
  "Settings.webhooks.trigger.test": "テストトリガー",
  "Settings.webhooks.trigger.title": "トリガー前に保存する",
  "Settings.webhooks.value": "値",
  Username,
  Users,
  "Users & Permissions": "ユーザーと権限",
  "Users.components.List.empty": "ユーザーがいません…",
  "Users.components.List.empty.withFilters": "適用されたフィルターにマッチするユーザーがいません…",
  "Users.components.List.empty.withSearch": "検索（{search}）に合致するユーザーがいません…",
  "admin.pages.MarketPlacePage.head": "マーケットプレイス - プラグイン",
  "admin.pages.MarketPlacePage.submit.plugin.link": "プラグインを送信",
  "admin.pages.MarketPlacePage.subtitle": "Get more out of Strapi",
  anErrorOccurred,
  "app.component.CopyToClipboard.label": "Copy to clipboard",
  "app.component.search.label": "{target}を検索",
  "app.component.table.duplicate": "{target}を復元",
  "app.component.table.edit": "{target}を編集",
  "app.component.table.select.one-entry": "{target}を選択",
  "app.components.BlockLink.blog": "Blog",
  "app.components.BlockLink.blog.content": "Read the latest news about Strapi and the ecosystem.",
  "app.components.BlockLink.code": "コード例",
  "app.components.BlockLink.code.content": "Learn by testing real projects developed the community.",
  "app.components.BlockLink.documentation.content": "Discover the essential concepts, guides and instructions.",
  "app.components.BlockLink.tutorial": "Tutorials",
  "app.components.BlockLink.tutorial.content": "Follow step-by-step instructions to use and customize Strapi.",
  "app.components.Button.cancel": "キャンセル",
  "app.components.Button.confirm": "確認",
  "app.components.Button.reset": "リセット",
  "app.components.ComingSoonPage.comingSoon": "近日公開",
  "app.components.ConfirmDialog.title": "確認",
  "app.components.DownloadInfo.download": "ダウンロード中...",
  "app.components.DownloadInfo.text": "これには数分かかることがあります。 お待ちください",
  "app.components.EmptyAttributes.title": "フィールドはまだありません",
  "app.components.EmptyStateLayout.content-document": "No content found",
  "app.components.EmptyStateLayout.content-permissions": "You don't have the permissions to access that content",
  "app.components.HomePage.button.blog": "ブログでもっと見る",
  "app.components.HomePage.community": "コミュニティで見つける",
  "app.components.HomePage.community.content": "異なるチャンネルでチームメンバー、コントリビューターやデベロッパーと議論する",
  "app.components.HomePage.create": "最初のコンテンツタイプを作成する",
  "app.components.HomePage.roadmap": "See our roadmap",
  "app.components.HomePage.welcome": "ボードにようこそ!",
  "app.components.HomePage.welcome.again": "ようこそ ",
  "app.components.HomePage.welcomeBlock.content": "私たちはコミュニティメンバーのひとりとしてあなたをお待ちしております。私たちは常にフィードバックを求めていますので、DMを送ってください",
  "app.components.HomePage.welcomeBlock.content.again": "あなたのプロジェクトを進歩させていただければ幸いです... Strapiに関する最新の新情報をお読みください。私たちはあなたのフィードバックに基づいて製品を改善するために最善を尽くしています。",
  "app.components.HomePage.welcomeBlock.content.issues": "問題",
  "app.components.HomePage.welcomeBlock.content.raise": "または上げる",
  "app.components.ImgPreview.hint": "ファイルをドラッグ&ドロップ、もしくは、アップロード{browse}",
  "app.components.ImgPreview.hint.browse": "ブラウズ",
  "app.components.InputFile.newFile": "ファイルを追加",
  "app.components.InputFileDetails.open": "別のタブを開く",
  "app.components.InputFileDetails.originalName": "オリジナル名:",
  "app.components.InputFileDetails.remove": "ファイルを取り除く",
  "app.components.InputFileDetails.size": "サイズ:",
  "app.components.InstallPluginPage.Download.description": "It might take a few seconds to download and install the plugin.",
  "app.components.InstallPluginPage.Download.title": "Downloading...",
  "app.components.InstallPluginPage.description": "簡単にアプリを拡張する",
  "app.components.LeftMenu.collapse": "Collapse the navbar",
  "app.components.LeftMenu.expand": "Expand the navbar",
  "app.components.LeftMenu.logout": "ログアウト",
  "app.components.LeftMenu.trialCountdown": "試用期間が終了するのは {date} です。",
  "app.components.LeftMenuFooter.help": "Help",
  "app.components.LeftMenuFooter.poweredBy": "Powered by ",
  "app.components.LeftMenuLinkContainer.collectionTypes": "コレクションタイプ",
  "app.components.LeftMenuLinkContainer.configuration": "構成",
  "app.components.LeftMenuLinkContainer.general": "一般",
  "app.components.LeftMenuLinkContainer.noPluginsInstalled": "プラグインがインストールされていません",
  "app.components.LeftMenuLinkContainer.plugins": "プラグイン",
  "app.components.LeftMenuLinkContainer.singleTypes": "シングルタイプ",
  "app.components.ListPluginsPage.deletePlugin.description": "It might take a few seconds to uninstall the plugin.",
  "app.components.ListPluginsPage.deletePlugin.title": "アンインストール中",
  "app.components.ListPluginsPage.description": "このプロジェクトでインストールされたプラグイン一覧",
  "app.components.ListPluginsPage.head.title": "プラグイン一覧",
  "app.components.Logout.logout": "ログアウト",
  "app.components.Logout.profile": "プロフィール",
  "app.components.MarketplaceBanner": "Discover plugins built by the community, and many more awesome things to kickstart your project, on Strapi Awesome.",
  "app.components.MarketplaceBanner.image.alt": "a strapi rocket logo",
  "app.components.MarketplaceBanner.link": "Check it out now",
  "app.components.NotFoundPage.back": "ホームページに戻る",
  "app.components.NotFoundPage.description": "見つかりません",
  "app.components.Official": "オフィシャル",
  "app.components.Onboarding.help.button": "Help button",
  "app.components.Onboarding.label.completed": "% completed",
  "app.components.Onboarding.title": "Get Started Videos",
  "app.components.PluginCard.Button.label.download": "ダウンロード",
  "app.components.PluginCard.Button.label.install": "インストール",
  "app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed": "The autoReload feature needs to be enabled. Please start your app with `yarn develop`.",
  "app.components.PluginCard.PopUpWarning.install.impossible.confirm": "I understand!",
  "app.components.PluginCard.PopUpWarning.install.impossible.environment": "For security reasons, a plugin can only be downloaded in a development environment.",
  "app.components.PluginCard.PopUpWarning.install.impossible.title": "Downloading is impossible",
  "app.components.PluginCard.compatible": "アプリとの互換性",
  "app.components.PluginCard.compatibleCommunity": "コミュニティとの互換性",
  "app.components.PluginCard.more-details": "詳細を見る",
  "app.components.ToggleCheckbox.off-label": "False",
  "app.components.ToggleCheckbox.on-label": "True",
  "app.components.Users.MagicLink.connect": "Copy and share this link to give access to this user",
  "app.components.Users.MagicLink.connect.sso": "Send this link to the user, the first login can be made via a SSO provider",
  "app.components.Users.ModalCreateBody.block-title.details": "User details",
  "app.components.Users.ModalCreateBody.block-title.roles": "User's roles",
  "app.components.Users.ModalCreateBody.block-title.roles.description": "A user can have one or several roles",
  "app.components.Users.SortPicker.button-label": "Sort by",
  "app.components.Users.SortPicker.sortby.email_asc": "Email (A to Z)",
  "app.components.Users.SortPicker.sortby.email_desc": "Email (Z to A)",
  "app.components.Users.SortPicker.sortby.firstname_asc": "First name (A to Z)",
  "app.components.Users.SortPicker.sortby.firstname_desc": "First name (Z to A)",
  "app.components.Users.SortPicker.sortby.lastname_asc": "Last name (A to Z)",
  "app.components.Users.SortPicker.sortby.lastname_desc": "Last name (Z to A)",
  "app.components.Users.SortPicker.sortby.username_asc": "Username (A to Z)",
  "app.components.Users.SortPicker.sortby.username_desc": "Username (Z to A)",
  "app.components.listPlugins.button": "プラグインを追加",
  "app.components.listPlugins.title.none": "インストール済みのプラグインはありません",
  "app.components.listPluginsPage.deletePlugin.error": "アンインストール中にエラーが発生しました",
  "app.containers.App.notification.error.init": "APIのリクエスト中にエラーが発生しました",
  "app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin": "If you do not receive this link, please contact your administrator.",
  "app.containers.AuthPage.ForgotPasswordSuccess.text.email": "It can take a few minutes to receive your password recovery link.",
  "app.containers.AuthPage.ForgotPasswordSuccess.title": "Email sent",
  "app.containers.Users.EditPage.form.active.label": "Active",
  "app.containers.Users.EditPage.header.label": "{name}を編集",
  "app.containers.Users.EditPage.header.label-loading": "ユーザーを編集",
  "app.containers.Users.EditPage.roles-bloc-title": "Attributed roles",
  "app.containers.Users.ModalForm.footer.button-success": "ユーザーを招待",
  "app.links.configure-view": "表示設定",
  "app.static.links.cheatsheet": "CheatSheet",
  "app.utils.SelectOption.defaultMessage": " ",
  "app.utils.add-filter": "Add filter",
  "app.utils.close-label": "閉じる",
  "app.utils.defaultMessage": " ",
  "app.utils.duplicate": "復元",
  "app.utils.edit": "編集",
  "app.utils.errors.file-too-big.message": "The file is too big",
  "app.utils.filter-value": "Filter value",
  "app.utils.filters": "フィルター",
  "app.utils.notify.data-loaded": "The {target} has loaded",
  "app.utils.placeholder.defaultMessage": " ",
  "app.utils.publish": "Publish",
  "app.utils.select-all": "すべてを選択",
  "app.utils.select-field": "Select field",
  "app.utils.select-filter": "Select filter",
  "app.utils.unpublish": "Unpublish",
  clearLabel,
  "coming.soon": "This content is currently under construction and will be back in a few weeks!",
  "component.Input.error.validation.integer": "The value must be an integer",
  "components.AutoReloadBlocker.description": "Run Strapi with one of the following commands:",
  "components.AutoReloadBlocker.header": "プラグインを有効化するにはリロードが必要です",
  "components.ErrorBoundary.title": "なにかが間違っています...",
  "components.FilterOptions.FILTER_TYPES.$contains": "を含む",
  "components.FilterOptions.FILTER_TYPES.$containsi": "を含む (case insensitive)",
  "components.FilterOptions.FILTER_TYPES.$endsWith": "で終わる",
  "components.FilterOptions.FILTER_TYPES.$endsWithi": "で終わる (case insensitive)",
  "components.FilterOptions.FILTER_TYPES.$eq": "同等",
  "components.FilterOptions.FILTER_TYPES.$eqi": "同等 (case insensitive)",
  "components.FilterOptions.FILTER_TYPES.$gt": "is greater than",
  "components.FilterOptions.FILTER_TYPES.$gte": "is greater than or equal to",
  "components.FilterOptions.FILTER_TYPES.$lt": "is lower than",
  "components.FilterOptions.FILTER_TYPES.$lte": "is lower than or equal to",
  "components.FilterOptions.FILTER_TYPES.$ne": "等しくありません",
  "components.FilterOptions.FILTER_TYPES.$nei": "等しくありません (case insensitive)",
  "components.FilterOptions.FILTER_TYPES.$notContains": "含まれていない",
  "components.FilterOptions.FILTER_TYPES.$notContainsi": "含まれていない (case insensitive)",
  "components.FilterOptions.FILTER_TYPES.$notNull": "is not null",
  "components.FilterOptions.FILTER_TYPES.$null": "is null",
  "components.FilterOptions.FILTER_TYPES.$startsWith": "皮切りに",
  "components.FilterOptions.FILTER_TYPES.$startsWithi": "皮切りに (case insensitive)",
  "components.Input.error.attribute.key.taken": "この値はすでに存在しています",
  "components.Input.error.attribute.sameKeyAndName": "等しくありません",
  "components.Input.error.attribute.taken": "このフィールド名はすでに存在します",
  "components.Input.error.contain.lowercase": "Password must contain at least one lowercase character",
  "components.Input.error.contain.number": "Password must contain at least one number",
  "components.Input.error.contain.uppercase": "Password must contain at least one uppercase character",
  "components.Input.error.contentTypeName.taken": "この名前はすでに存在します",
  "components.Input.error.custom-error": "{errorMessage} ",
  "components.Input.error.password.noMatch": "パスワードが一致しません",
  "components.Input.error.validation.email": "E-mailアドレスではありません",
  "components.Input.error.validation.json": "JSONフォーマットではありません",
  "components.Input.error.validation.max": "値が大きすぎます {max}",
  "components.Input.error.validation.maxLength": "値が長すぎます {max}",
  "components.Input.error.validation.min": "値が小さすぎます {min}",
  "components.Input.error.validation.minLength": "値が短すぎます {min}",
  "components.Input.error.validation.minSupMax": "値が超過しています",
  "components.Input.error.validation.regex": "値が正規表現と一致しません",
  "components.Input.error.validation.required": "この値は必須項目です",
  "components.Input.error.validation.unique": "この値はすでに存在します",
  "components.InputSelect.option.placeholder": "選択してください",
  "components.ListRow.empty": "表示するデータがありません",
  "components.NotAllowedInput.text": "No permissions to see this field",
  "components.OverlayBlocker.description": "サーバーのリスタートが必要な機能を使用しています。サーバーが起動するまでお待ち下さい",
  "components.OverlayBlocker.description.serverError": "The server should have restarted, please check your logs in the terminal.",
  "components.OverlayBlocker.title": "リスタートを待っています...",
  "components.OverlayBlocker.title.serverError": "The restart is taking longer than expected",
  "components.PageFooter.select": "ページ毎に表示する投稿数",
  "components.ProductionBlocker.description": "このプラグインは、安全のため、他の環境では無効する必要があります",
  "components.ProductionBlocker.header": "このプラグインはデベロップ環境でのみ利用できます",
  "components.Search.placeholder": "検索...",
  "components.TableHeader.sort": "Sort on {label}",
  "components.Wysiwyg.ToggleMode.markdown-mode": "Markdown mode",
  "components.Wysiwyg.ToggleMode.preview-mode": "Preview mode",
  "components.Wysiwyg.collapse": "Collapse",
  "components.Wysiwyg.selectOptions.H1": "タイトル H1",
  "components.Wysiwyg.selectOptions.H2": "タイトル H2",
  "components.Wysiwyg.selectOptions.H3": "タイトル H3",
  "components.Wysiwyg.selectOptions.H4": "タイトル H4",
  "components.Wysiwyg.selectOptions.H5": "タイトル H5",
  "components.Wysiwyg.selectOptions.H6": "タイトル H6",
  "components.Wysiwyg.selectOptions.title": "タイトルを追加する",
  "components.WysiwygBottomControls.charactersIndicators": "キャラクター",
  "components.WysiwygBottomControls.fullscreen": "広げる",
  "components.WysiwygBottomControls.uploadFiles": "ファイルをドラッグ＆ドロップ, クリップボードからペースト もしくは {browse}.",
  "components.WysiwygBottomControls.uploadFiles.browse": "選ぶ",
  "components.pagination.go-to": "Go to page {page}",
  "components.pagination.go-to-next": "Go to next page",
  "components.pagination.go-to-previous": "Go to previous page",
  "components.pagination.remaining-links": "And {number} other links",
  "components.popUpWarning.button.cancel": "No, cancel",
  "components.popUpWarning.button.confirm": "Yes, confirm",
  "components.popUpWarning.message": "本当に削除しますか?",
  "components.popUpWarning.title": "確認してください",
  "form.button.done": "完了",
  "global.prompt.unsaved": "ページから離れてもいいですか？編集中のものは全て失われます",
  "notification.contentType.relations.conflict": "コンテンツタイプがリレーションと競合しています",
  "notification.default.title": "Information:",
  "notification.error": "エラーが発生しました",
  "notification.error.layout": "レイアウトを復旧できませんでした",
  "notification.form.error.fields": "フォームに同じエラーがあります",
  "notification.form.success.fields": "保存されました",
  "notification.link-copied": "クリップボードにリンクをコピーしました",
  "notification.permission.not-allowed-read": "You are not allowed to see this document",
  "notification.success.delete": "アイテムは削除されました",
  "notification.success.saved": "Saved",
  "notification.success.title": "Success:",
  "notification.version.update.message": "WHEN",
  "notification.warning.title": "Warning:",
  or,
  "request.error.model.unknown": "モデルが存在しません",
  skipToContent,
  submit
};
export {
  Analytics,
  Documentation,
  Email,
  Password,
  Provider,
  ResetPasswordToken,
  Role,
  Username,
  Users,
  anErrorOccurred,
  clearLabel,
  ja as default,
  or,
  skipToContent,
  submit
};
//# sourceMappingURL=ja.json-W3KU6X2O.js.map
