{"version": 3, "sources": ["../../../@strapi/admin/dist/admin/admin/src/translations/ko.json.mjs"], "sourcesContent": ["var Analytics = \"통계\";\nvar Documentation = \"도큐멘테이션\";\nvar Email = \"이메일\";\nvar Password = \"비밀번호\";\nvar Provider = \"프로바이더(provider)\";\nvar ResetPasswordToken = \"비밀번호 토큰 재설정\";\nvar Role = \"역할\";\nvar Username = \"사용자 이름(Username)\";\nvar Users = \"사용자\";\nvar anErrorOccurred = \"에러가 발생했습니다. 잠시 후에 다시 시도해주세요.\";\nvar clearLabel = \"초기화\";\nvar or = \"또는\";\nvar skipToContent = \"콘텐츠로 건너뛰기\";\nvar submit = \"등록\";\nvar ko = {\n    Analytics: Analytics,\n    \"Auth.components.Oops.text\": \"계정이 정지되었습니다.\",\n    \"Auth.components.Oops.text.admin\": \"오류가 있는 경우 관리자에게 문의해주세요.\",\n    \"Auth.components.Oops.title\": \"앗...\",\n    \"Auth.form.button.forgot-password\": \"메일 보내기\",\n    \"Auth.form.button.go-home\": \"홈으로\",\n    \"Auth.form.button.login\": \"로그인\",\n    \"Auth.form.button.login.providers.error\": \"선택한 프로바이더로 로그인할 수 없습니다.\",\n    \"Auth.form.button.login.strapi\": \"Strapi 계정으로 로그인\",\n    \"Auth.form.button.password-recovery\": \"비밀번호 복원\",\n    \"Auth.form.button.register\": \"등록\",\n    \"Auth.form.confirmPassword.label\": \"비밀번호 확인\",\n    \"Auth.form.currentPassword.label\": \"기존 비밀번호\",\n    \"Auth.form.email.label\": \"이메일\",\n    \"Auth.form.email.placeholder\": \"<EMAIL>\",\n    \"Auth.form.error.blocked\": \"관리자에 의해 접근이 제한된 계정입니다.\",\n    \"Auth.form.error.code.provide\": \"유효하지 않은 코드입니다.\",\n    \"Auth.form.error.confirmed\": \"이메일 인증이 필요합니다.\",\n    \"Auth.form.error.email.invalid\": \"유효하지 않은 이메일입니다.\",\n    \"Auth.form.error.email.provide\": \"이메일을 입력해 주세요.\",\n    \"Auth.form.error.email.taken\": \"이미 사용 중인 이메일입니다.\",\n    \"Auth.form.error.invalid\": \"입력한 내용이 유효하지 않습니다.\",\n    \"Auth.form.error.params.provide\": \"유효하지 않은 파라미터입니다.\",\n    \"Auth.form.error.password.format\": \"비밀번호에 `$` 문자를 세 번 이상 포함 할 수 없습니다.\",\n    \"Auth.form.error.password.local\": \"비밀번호를 설정하지 않았습니다. 다른 방법으로 로그인 하세요.\",\n    \"Auth.form.error.password.matching\": \"비밀번호가 일치하지 않습니다.\",\n    \"Auth.form.error.password.provide\": \"비밀번호를 입력해 주세요.\",\n    \"Auth.form.error.ratelimit\": \"요청이 너무 많습니다. 잠시 후 다시 시도해주세요.\",\n    \"Auth.form.error.user.not-exist\": \"이메일이 없습니다.\",\n    \"Auth.form.error.username.taken\": \"이미 사용 중인 아이디입니다.\",\n    \"Auth.form.firstname.label\": \"이름\",\n    \"Auth.form.firstname.placeholder\": \"e.g. Kai\",\n    \"Auth.form.forgot-password.email.label\": \"메일 주소를 입력하세요.\",\n    \"Auth.form.forgot-password.email.label.success\": \"메일을 보냈습니다.\",\n    \"Auth.form.lastname.label\": \"성\",\n    \"Auth.form.lastname.placeholder\": \"e.g. Doe\",\n    \"Auth.form.password.hide-password\": \"비밀번호 숨기기\",\n    \"Auth.form.password.hint\": \"암호는 대문자 1자, 소문자 1자, 숫자 1자를 포함한 8자 이상의 문자를 입력해주세요.\",\n    \"Auth.form.password.show-password\": \"비밀번호 표시\",\n    \"Auth.form.register.news.label\": \"새 기능과 향후 개선 사항에 대한 최신 정보를 계속 제공해주세요 (선택시 {terms}과 {policy}에 동의하는 걸로 간주됩니다).\",\n    \"Auth.form.register.subtitle\": \"인증 정보는 관리자 패널에서 자신을 인증하는 데만 사용됩니다. 저장된 모든 데이터는 사용자의 데이터베이스에 저장됩니다.\",\n    \"Auth.form.rememberMe.label\": \"로그인 상태 저장\",\n    \"Auth.form.username.label\": \"아이디\",\n    \"Auth.form.username.placeholder\": \"KaiDoe\",\n    \"Auth.form.welcome.subtitle\": \"Strapi 계정으로 로그인하세요.\",\n    \"Auth.form.welcome.title\": \"안녕하세요!\",\n    \"Auth.link.forgot-password\": \"비밀번호 재설정\",\n    \"Auth.link.ready\": \"로그인 하시겠습니까?\",\n    \"Auth.link.signin\": \"로그인\",\n    \"Auth.link.signin.account\": \"이미 계정이 있으신가요?\",\n    \"Auth.login.sso.divider\": \"Or login with\",\n    \"Auth.login.sso.loading\": \"프로바이더를 불러오는 중...\",\n    \"Auth.login.sso.subtitle\": \"SSO를 통해 로그인합니다.\",\n    \"Auth.privacy-policy-agreement.policy\": \"개인정보 보호정책\",\n    \"Auth.privacy-policy-agreement.terms\": \"약관\",\n    \"Content Manager\": \"콘텐츠 관리\",\n    \"Content Type Builder\": \"콘텐츠 타입 빌더\",\n    Documentation: Documentation,\n    Email: Email,\n    \"Files Upload\": \"파일 업로드\",\n    \"HomePage.head.title\": \"홈페이지\",\n    \"HomePage.roadmap\": \"로드맵 보기\",\n    \"HomePage.welcome.congrats\": \"축하합니다!\",\n    \"HomePage.welcome.congrats.content\": \"첫번째 관리자로 로그인하셨습니다. Strapi의 강력한 기능을 확인하시려면,\",\n    \"HomePage.welcome.congrats.content.bold\": \"we recommend you to create your first Collection-Type.\",\n    \"Media Library\": \"미디어 라이브러리\",\n    \"New entry\": \"새 항목\",\n    Password: Password,\n    Provider: Provider,\n    ResetPasswordToken: ResetPasswordToken,\n    Role: Role,\n    \"Roles & Permissions\": \"역할(Roles) & 권한(Permissions)\",\n    \"Roles.ListPage.notification.delete-all-not-allowed\": \"Some roles could not be deleted since they are associated with users\",\n    \"Roles.ListPage.notification.delete-not-allowed\": \"A role cannot be deleted if associated with users\",\n    \"Roles.RoleRow.select-all\": \"Select {name} for bulk actions\",\n    \"Roles.components.List.empty.withSearch\": \"There is no role corresponding to the search ({search})...\",\n    \"Settings.PageTitle\": \"Settings - {name}\",\n    \"Settings.apiTokens.addFirstToken\": \"첫 API Token을 만들어보세요.\",\n    \"Settings.apiTokens.addNewToken\": \"새 API Token 만들기\",\n    \"Settings.tokens.copy.editMessage\": \"보안상의 이유로 토큰은 한 번만 볼 수 있습니다.\",\n    \"Settings.tokens.copy.editTitle\": \"이 토큰은 더 이상 액세스할 수 없습니다.\",\n    \"Settings.tokens.copy.lastWarning\": \"이 토큰을 복사해두세요. 다시 볼 수 없습니다!\",\n    \"Settings.apiTokens.create\": \"항목 추가\",\n    \"Settings.apiTokens.description\": \"API 사용을 위해 생성된 토큰 목록입니다.\",\n    \"Settings.apiTokens.emptyStateLayout\": \"아직 콘텐츠가 없습니다.\",\n    \"Settings.tokens.notification.copied\": \"토큰이 클립보드에 복사되었습니다.\",\n    \"Settings.apiTokens.title\": \"API 토큰\",\n    \"Settings.tokens.types.full-access\": \"전체 액세스\",\n    \"Settings.tokens.types.read-only\": \"읽기 전용\",\n    \"Settings.application.description\": \"프로젝트 세부 정보\",\n    \"Settings.application.edition-title\": \"현재 에디션\",\n    \"Settings.application.link-pricing\": \"모든 가격 정책 보기\",\n    \"Settings.application.link-upgrade\": \"어드민 패널 업그레이드\",\n    \"Settings.application.node-version\": \"node version\",\n    \"Settings.application.strapi-version\": \"strapi version\",\n    \"Settings.application.title\": \"어플리케이션\",\n    \"Settings.error\": \"에러\",\n    \"Settings.global\": \"글로벌 설정\",\n    \"Settings.permissions\": \"어드민 패널\",\n    \"Settings.permissions.category\": \"{category} 사용 권한 설정\",\n    \"Settings.permissions.category.plugins\": \"{category} 플러그인 사용 권한 설정\",\n    \"Settings.permissions.conditions.anytime\": \"Anytime\",\n    \"Settings.permissions.conditions.apply\": \"Apply\",\n    \"Settings.permissions.conditions.can\": \"Can\",\n    \"Settings.permissions.conditions.conditions\": \"Define conditions\",\n    \"Settings.permissions.conditions.links\": \"Links\",\n    \"Settings.permissions.conditions.no-actions\": \"You first need to select actions (create, read, update, ...) before defining conditions on them.\",\n    \"Settings.permissions.conditions.none-selected\": \"Anytime\",\n    \"Settings.permissions.conditions.or\": \"OR\",\n    \"Settings.permissions.conditions.when\": \"When\",\n    \"Settings.permissions.select-all-by-permission\": \"Select all {label} permissions\",\n    \"Settings.permissions.select-by-permission\": \"Select {label} permission\",\n    \"Settings.permissions.users.create\": \"새 사용자 추가\",\n    \"Settings.permissions.users.email\": \"이메일\",\n    \"Settings.permissions.users.firstname\": \"이름\",\n    \"Settings.permissions.users.lastname\": \"성\",\n    \"Settings.permissions.users.form.sso\": \"Connect with SSO\",\n    \"Settings.permissions.users.form.sso.description\": \"When enabled (ON), users can login via SSO\",\n    \"Settings.permissions.users.listview.header.subtitle\": \"{number, plural, =0 {# 명} one {# 명} other {# 명}}의 사용자를 찾았습니다.\",\n    \"Settings.permissions.users.tabs.label\": \"Tabs Permissions\",\n    \"Settings.profile.form.notify.data.loaded\": \"사용자 프로필 정보를 불러왔습니다.\",\n    \"Settings.profile.form.section.experience.clear.select\": \"Clear the interface language selected\",\n    \"Settings.profile.form.section.experience.interfaceLanguage\": \"인터페이스 언어\",\n    \"Settings.profile.form.section.experience.interfaceLanguage.hint\": \"선택한 언어로 인터페이스의 언어가 변경됩니다.\",\n    \"Settings.profile.form.section.experience.interfaceLanguageHelp\": \"선택하면 이 계정에서만 인터페이스 언어가 변경됩니다. 팀에서 다른 언어를 사용할 수 있도록 하려면 이 {documentation}를 참조해주세요.\",\n    \"Settings.profile.form.section.experience.title\": \"사용자 경험\",\n    \"Settings.profile.form.section.head.title\": \"사용자 프로필\",\n    \"Settings.profile.form.section.profile.page.title\": \"Profile page\",\n    \"Settings.roles.create.description\": \"역할에 부여된 권한을 정의합니다.\",\n    \"Settings.roles.create.title\": \"Create a role\",\n    \"Settings.roles.created\": \"Role created\",\n    \"Settings.roles.edit.title\": \"역할 수정\",\n    \"Settings.roles.form.button.users-with-role\": \"{number, plural, =0 {# 명} one {# 명} other {# 명}}의 사용자가 이 역할을 가지고 있습니다.\",\n    \"Settings.roles.form.created\": \"Created\",\n    \"Settings.roles.form.description\": \"역할에 대한 이름과 설명\",\n    \"Settings.roles.form.permission.property-label\": \"{label} permissions\",\n    \"Settings.roles.form.permissions.attributesPermissions\": \"Fields permissions\",\n    \"Settings.roles.form.permissions.create\": \"생성\",\n    \"Settings.roles.form.permissions.delete\": \"삭제\",\n    \"Settings.roles.form.permissions.publish\": \"발행\",\n    \"Settings.roles.form.permissions.read\": \"조회\",\n    \"Settings.roles.form.permissions.update\": \"수정\",\n    \"Settings.roles.list.button.add\": \"새 역할 추가\",\n    \"Settings.roles.list.description\": \"역할 목록\",\n    \"Settings.roles.title.singular\": \"역할\",\n    \"Settings.sso.description\": \"Single Sign-On 기능에 대한 설정을 구성합니다.\",\n    \"Settings.sso.form.defaultRole.description\": \"새 사용자는 선택한 역할에 연결됩니다.\",\n    \"Settings.sso.form.defaultRole.description-not-allowed\": \"어드민 역할을 보려면 권한이 필요합니다.\",\n    \"Settings.sso.form.defaultRole.label\": \"기본 역할\",\n    \"Settings.sso.form.registration.description\": \"계정이 없으면 SSO 로그인 시 새 사용자를 생성합니다.\",\n    \"Settings.sso.form.registration.label\": \"자동 회원가입\",\n    \"Settings.sso.title\": \"Single Sign-On\",\n    \"Settings.webhooks.create\": \"웹훅 만들기\",\n    \"Settings.webhooks.create.header\": \"새 헤더 만들기\",\n    \"Settings.webhooks.created\": \"웹훅이 생성되었습니다.\",\n    \"Settings.webhooks.event.publish-tooltip\": \"이 이벤트는 초안/발행 시스템이 활성화된 콘텐츠에 대해서만 존재합니다.\",\n    \"Settings.webhooks.events.create\": \"생성\",\n    \"Settings.webhooks.events.update\": \"수정\",\n    \"Settings.webhooks.form.events\": \"이벤트\",\n    \"Settings.webhooks.form.headers\": \"헤더\",\n    \"Settings.webhooks.form.url\": \"Url\",\n    \"Settings.webhooks.headers.remove\": \"Remove header row {number}\",\n    \"Settings.webhooks.key\": \"Key\",\n    \"Settings.webhooks.list.button.add\": \"새 웹훅 만들기\",\n    \"Settings.webhooks.list.description\": \"POST 변경 알림을 가져옵니다.\",\n    \"Settings.webhooks.list.empty.description\": \"첫 웹훅을 만들어보세요.\",\n    \"Settings.webhooks.list.empty.link\": \"설명서 보기\",\n    \"Settings.webhooks.list.empty.title\": \"아직 웹훅이 없습니다.\",\n    \"Settings.webhooks.list.th.actions\": \"actions\",\n    \"Settings.webhooks.list.th.status\": \"status\",\n    \"Settings.webhooks.singular\": \"웹훅\",\n    \"Settings.webhooks.title\": \"웹훅\",\n    \"Settings.webhooks.to.delete\": \"{webhooksToDeleteLength, plural, one {# 개의 에셋이} other {# 개의 에셋이}} 선택됨\",\n    \"Settings.webhooks.trigger\": \"Trigger\",\n    \"Settings.webhooks.trigger.cancel\": \"Cancel trigger\",\n    \"Settings.webhooks.trigger.pending\": \"Pending…\",\n    \"Settings.webhooks.trigger.save\": \"Please save to trigger\",\n    \"Settings.webhooks.trigger.success\": \"Success!\",\n    \"Settings.webhooks.trigger.success.label\": \"Trigger succeeded\",\n    \"Settings.webhooks.trigger.test\": \"Test-trigger\",\n    \"Settings.webhooks.trigger.title\": \"Save before Trigger\",\n    \"Settings.webhooks.value\": \"Value\",\n    Username: Username,\n    Users: Users,\n    \"Users & Permissions\": \"사용자 & 권한(Permissions)\",\n    \"Users.components.List.empty\": \"사용자가 없습니다.\",\n    \"Users.components.List.empty.withFilters\": \"적용된 필터와 일치하는 사용자가 없습니다.\",\n    \"Users.components.List.empty.withSearch\": \"({search}) 검색 결과와 일치하는 사용자가 없습니다.\",\n    \"admin.pages.MarketPlacePage.head\": \"마켓플레이스 - 플러그인\",\n    \"admin.pages.MarketPlacePage.submit.plugin.link\": \"플러그인 제출\",\n    \"admin.pages.MarketPlacePage.subtitle\": \"Strapi에서 더 많은 것을 해보세요.\",\n    anErrorOccurred: anErrorOccurred,\n    \"app.component.CopyToClipboard.label\": \"클립보드 복사\",\n    \"app.component.search.label\": \"Search for {target}\",\n    \"app.component.table.duplicate\": \"Duplicate {target}\",\n    \"app.component.table.edit\": \"Edit {target}\",\n    \"app.component.table.select.one-entry\": \"Select {target}\",\n    \"app.components.BlockLink.blog\": \"블로그\",\n    \"app.components.BlockLink.blog.content\": \"Strapi와 생태계에 대한 최신 뉴스를 읽어보세요.\",\n    \"app.components.BlockLink.code\": \"코드 샘플\",\n    \"app.components.BlockLink.code.content\": \"실제 프로젝트를 테스트하여 학습합니다.\",\n    \"app.components.BlockLink.documentation.content\": \"필수 개념, 가이드 및 지침을 살펴보세요.\",\n    \"app.components.BlockLink.tutorial\": \"튜토리얼\",\n    \"app.components.BlockLink.tutorial.content\": \"단계별 지침에 따라 Strapi를 사용하고 커스터마이징 해보세요.\",\n    \"app.components.Button.cancel\": \"취소\",\n    \"app.components.Button.confirm\": \"확인\",\n    \"app.components.Button.reset\": \"리셋\",\n    \"app.components.ComingSoonPage.comingSoon\": \"Coming soon\",\n    \"app.components.ConfirmDialog.title\": \"확인\",\n    \"app.components.DownloadInfo.download\": \"다운로드 중...\",\n    \"app.components.DownloadInfo.text\": \"조금만 기다려 주세요.\",\n    \"app.components.EmptyAttributes.title\": \"아직 생성된 필드가 없습니다.\",\n    \"app.components.EmptyStateLayout.content-document\": \"아직 콘텐츠가 없습니다.\",\n    \"app.components.EmptyStateLayout.content-permissions\": \"해당 콘텐츠에 액세스할 수 있는 권한이 없습니다\",\n    \"app.components.HomePage.button.blog\": \"블로그 보기\",\n    \"app.components.HomePage.community\": \"커뮤니티를 찾아보세요!\",\n    \"app.components.HomePage.community.content\": \"다양한 채널에서 Strapi 팀원, 콘트리뷰터 및 개발자들과 토론해보세요.\",\n    \"app.components.HomePage.create\": \"첫 콘텐츠 타입 만들기\",\n    \"app.components.HomePage.roadmap\": \"로드맵 보기\",\n    \"app.components.HomePage.welcome\": \"환영합니다 👋\",\n    \"app.components.HomePage.welcome.again\": \"반갑습니다 👋\",\n    \"app.components.HomePage.welcomeBlock.content\": \"축하드립니다! 첫 번째 관리자로 로그인하셨습니다. Strapi가 제공하는 강력한 기능을 알아보려면 첫 번째 콘텐츠 유형을 만들어보세요!\",\n    \"app.components.HomePage.welcomeBlock.content.again\": \"Strapi에 대한 최신 뉴스를 자유롭게 읽어보세요. 저희는 여러분의 피드백을 바탕으로 제품을 개선하기 위해 최선을 다하고 있습니다.\",\n    \"app.components.HomePage.welcomeBlock.content.issues\": \"이슈\",\n    \"app.components.HomePage.welcomeBlock.content.raise\": \", \",\n    \"app.components.ImgPreview.hint\": \"파일을 끌어 놓거나 {browse} 하세요.\",\n    \"app.components.ImgPreview.hint.browse\": \"선택\",\n    \"app.components.InputFile.newFile\": \"파일 추가\",\n    \"app.components.InputFileDetails.open\": \"새 탭으로 열기\",\n    \"app.components.InputFileDetails.originalName\": \"원래 파일 이름:\",\n    \"app.components.InputFileDetails.remove\": \"파일 삭제\",\n    \"app.components.InputFileDetails.size\": \"크기:\",\n    \"app.components.InstallPluginPage.Download.description\": \"플러그인을 다운로드하여 설치하는 데 몇 초 정도 걸릴 수 있습니다.\",\n    \"app.components.InstallPluginPage.Download.title\": \"다운로드 중...\",\n    \"app.components.InstallPluginPage.description\": \"빠르고 간단하게 기능을 확장해 보세요.\",\n    \"app.components.LeftMenu.collapse\": \"Collapse the navbar\",\n    \"app.components.LeftMenu.expand\": \"Expand the navbar\",\n    \"app.components.LeftMenu.logout\": \"로그아웃\",\n    \"app.components.LeftMenu.navbrand.title\": \"Strapi 대시보드\",\n    \"app.components.LeftMenu.navbrand.workplace\": \"작업 공간\",\n    \"app.components.LeftMenu.trialCountdown\": \"테스트 기간이 {date}에 종료됩니다.\",\n    \"app.components.LeftMenuFooter.help\": \"도움말\",\n    \"app.components.LeftMenuFooter.poweredBy\": \"Powered by \",\n    \"app.components.LeftMenuLinkContainer.collectionTypes\": \"콜렉션 타입\",\n    \"app.components.LeftMenuLinkContainer.configuration\": \"환경설정\",\n    \"app.components.LeftMenuLinkContainer.general\": \"일반\",\n    \"app.components.LeftMenuLinkContainer.noPluginsInstalled\": \"설치된 플러그인이 없습니다.\",\n    \"app.components.LeftMenuLinkContainer.plugins\": \"플러그인\",\n    \"app.components.LeftMenuLinkContainer.singleTypes\": \"싱글 타입\",\n    \"app.components.ListPluginsPage.deletePlugin.description\": \"플러그인을 제거하는 데 몇 초 정도 걸릴 수 있습니다.\",\n    \"app.components.ListPluginsPage.deletePlugin.title\": \"제거하는 중\",\n    \"app.components.ListPluginsPage.description\": \"이 프로젝트에 설치된 플러그인 목록입니다.\",\n    \"app.components.ListPluginsPage.head.title\": \"플러그인 목록\",\n    \"app.components.Logout.logout\": \"로그아웃\",\n    \"app.components.Logout.profile\": \"프로필\",\n    \"app.components.MarketplaceBanner\": \"Discover plugins built by the community, and many more awesome things to kickstart your project, on Strapi Awesome.\",\n    \"app.components.MarketplaceBanner.image.alt\": \"a strapi rocket logo\",\n    \"app.components.MarketplaceBanner.link\": \"지금 확인해보기\",\n    \"app.components.NotFoundPage.back\": \"홈으로 돌아가기\",\n    \"app.components.NotFoundPage.description\": \"찾을 수 없는 페이지입니다.\",\n    \"app.components.Official\": \"공식\",\n    \"app.components.Onboarding.help.button\": \"도움말\",\n    \"app.components.Onboarding.label.completed\": \"% 완료\",\n    \"app.components.Onboarding.title\": \"동영상 시청하기\",\n    \"app.components.PluginCard.Button.label.download\": \"다운로드\",\n    \"app.components.PluginCard.Button.label.install\": \"설치됨\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed\": \"autoReload 기능을 사용하지 않도록 설정해야 합니다. `yarn develop`로 앱을 시작하세요.\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.confirm\": \"알겠습니다!\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.environment\": \"보안상의 이유로 개발 환경에서만 플러그인을 다운로드할 수 있습니다.\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.title\": \"다운로드 불가\",\n    \"app.components.PluginCard.compatible\": \"이 애플리케이션에 호환됩니다.\",\n    \"app.components.PluginCard.compatibleCommunity\": \"션뮤니티에 호환됩니다.\",\n    \"app.components.PluginCard.more-details\": \"[더보기]\",\n    \"app.components.ToggleCheckbox.off-label\": \"False\",\n    \"app.components.ToggleCheckbox.on-label\": \"True\",\n    \"app.components.Users.MagicLink.connect\": \"이 링크를 가입할 사용자에게 보내주세요.\",\n    \"app.components.Users.MagicLink.connect.sso\": \"이 링크를 가입할 사용자에게 보내주세요. SSO 프로바이더를 통해 처음 로그인할 수 있습니다.\",\n    \"app.components.Users.ModalCreateBody.block-title.details\": \"상세 정보\",\n    \"app.components.Users.ModalCreateBody.block-title.roles\": \"사용자 역할\",\n    \"app.components.Users.ModalCreateBody.block-title.roles.description\": \"사용자는 하나 이상의 역할을 가질 수 있습니다.\",\n    \"app.components.Users.SortPicker.button-label\": \"정렬 기준\",\n    \"app.components.Users.SortPicker.sortby.email_asc\": \"이메일 (A to Z)\",\n    \"app.components.Users.SortPicker.sortby.email_desc\": \"이메일 (Z to A)\",\n    \"app.components.Users.SortPicker.sortby.firstname_asc\": \"이름 (A to Z)\",\n    \"app.components.Users.SortPicker.sortby.firstname_desc\": \"이름 (Z to A)\",\n    \"app.components.Users.SortPicker.sortby.lastname_asc\": \"성 (A to Z)\",\n    \"app.components.Users.SortPicker.sortby.lastname_desc\": \"성 (Z to A)\",\n    \"app.components.Users.SortPicker.sortby.username_asc\": \"Username (A to Z)\",\n    \"app.components.Users.SortPicker.sortby.username_desc\": \"Username (Z to A)\",\n    \"app.components.listPlugins.button\": \"새로운 플러그인 추가하기\",\n    \"app.components.listPlugins.title.none\": \"설치된 플러그인이 없습니다.\",\n    \"app.components.listPluginsPage.deletePlugin.error\": \"플러그인을 제거하는데 에러가 발생했습니다.\",\n    \"app.containers.App.notification.error.init\": \"API 요청 중에 에러가 발생했습니다.\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin\": \"링크를 받지 못했다면 관리자에게 문의해주세요.\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.text.email\": \"비밀번호 복구 링크를 받는 데 몇 분 정도 걸릴 수 있습니다.\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.title\": \"Email sent\",\n    \"app.containers.Users.EditPage.form.active.label\": \"활성\",\n    \"app.containers.Users.EditPage.header.label\": \"{name} 수정\",\n    \"app.containers.Users.EditPage.header.label-loading\": \"사용자 수정\",\n    \"app.containers.Users.EditPage.roles-bloc-title\": \"Attributed roles\",\n    \"app.containers.Users.ModalForm.footer.button-success\": \"사용자 생성\",\n    \"app.links.configure-view\": \"보기 설정\",\n    \"app.static.links.cheatsheet\": \"CheatSheet\",\n    \"app.utils.SelectOption.defaultMessage\": \" \",\n    \"app.utils.add-filter\": \"필터 추가\",\n    \"app.utils.close-label\": \"닫기\",\n    \"app.utils.defaultMessage\": \" \",\n    \"app.utils.duplicate\": \"복사\",\n    \"app.utils.edit\": \"수정\",\n    \"app.utils.errors.file-too-big.message\": \"파일이 너무 큽니다\",\n    \"app.utils.filter-value\": \"필터 값\",\n    \"app.utils.filters\": \"필터\",\n    \"app.utils.notify.data-loaded\": \"{target}을 불러왔습니다.\",\n    \"app.utils.placeholder.defaultMessage\": \" \",\n    \"app.utils.publish\": \"발행\",\n    \"app.utils.select-all\": \"전체 선택\",\n    \"app.utils.select-field\": \"필드 선택\",\n    \"app.utils.select-filter\": \"필터 선택\",\n    \"app.utils.unpublish\": \"발행 취소\",\n    clearLabel: clearLabel,\n    \"coming.soon\": \"주\",\n    \"component.Input.error.validation.integer\": \"값은 정수여야 합니다.\",\n    \"components.AutoReloadBlocker.description\": \"다음 명령어 중 하나를 사용하여 Strapi를 실행합니다:\",\n    \"components.AutoReloadBlocker.header\": \"이 플러그인은 리로드 기능이 필요합니다.\",\n    \"components.ErrorBoundary.title\": \"에러가 발생했습니다.\",\n    \"components.FilterOptions.FILTER_TYPES.$contains\": \"포함\",\n    \"components.FilterOptions.FILTER_TYPES.$containsi\": \"포함(대소문자 구분 안 함)\",\n    \"components.FilterOptions.FILTER_TYPES.$endsWith\": \"로 끝나다\",\n    \"components.FilterOptions.FILTER_TYPES.$endsWithi\": \"다음으로 끝남(case insensitive)\",\n    \"components.FilterOptions.FILTER_TYPES.$eq\": \"같음\",\n    \"components.FilterOptions.FILTER_TYPES.$eqi\": \"같음(대소문자 구분 안 함)\",\n    \"components.FilterOptions.FILTER_TYPES.$gt\": \"is greater than\",\n    \"components.FilterOptions.FILTER_TYPES.$gte\": \"is greater than or equal to\",\n    \"components.FilterOptions.FILTER_TYPES.$lt\": \"is lower than\",\n    \"components.FilterOptions.FILTER_TYPES.$lte\": \"is lower than or equal to\",\n    \"components.FilterOptions.FILTER_TYPES.$ne\": \"같지 않음\",\n    \"components.FilterOptions.FILTER_TYPES.$nei\": \"같지 않음(case insensitive)\",\n    \"components.FilterOptions.FILTER_TYPES.$notContains\": \"포함되어 있지 않다\",\n    \"components.FilterOptions.FILTER_TYPES.$notContainsi\": \"포함하지 않음(대소문자 구분 안 함)\",\n    \"components.FilterOptions.FILTER_TYPES.$notNull\": \"is not null\",\n    \"components.FilterOptions.FILTER_TYPES.$null\": \"is null\",\n    \"components.FilterOptions.FILTER_TYPES.$startsWith\": \"로 시작\",\n    \"components.FilterOptions.FILTER_TYPES.$startsWithi\": \"로 시작(대소문자 구분 안 함)\",\n    \"components.Input.error.attribute.key.taken\": \"이미 사용중인 키입니다.\",\n    \"components.Input.error.attribute.sameKeyAndName\": \"같은 값을 사용할 수 없습니다.\",\n    \"components.Input.error.attribute.taken\": \"이미 사용중인 이름입니다.\",\n    \"components.Input.error.contain.lowercase\": \"비밀번호는 소문자 하나를 반드시 포함해야 합니다.\",\n    \"components.Input.error.contain.number\": \"비밀번호는 숫자 하나를 반드시 포함해야 합니다.\",\n    \"components.Input.error.contain.uppercase\": \"비밀번호는 대문자 하나를 반드시 포함해야 합니다.\",\n    \"components.Input.error.contentTypeName.taken\": \"이미 사용중인 이름입니다.\",\n    \"components.Input.error.custom-error\": \"{errorMessage} \",\n    \"components.Input.error.password.noMatch\": \"비밀번호가 일치하지 않습니다.\",\n    \"components.Input.error.validation.email\": \"올바른 이메일 주소가 아닙니다.\",\n    \"components.Input.error.validation.json\": \"JSON 형식이 아닙니다.\",\n    \"components.Input.error.validation.max\": \"입력한 내용이 너무 큽니다 {max}.\",\n    \"components.Input.error.validation.maxLength\": \"입력한 내용이 너무 깁니다 {max}.\",\n    \"components.Input.error.validation.min\": \"입력한 내용이 너무 작습니다 {min}.\",\n    \"components.Input.error.validation.minLength\": \"입력한 내용이 너무 짧습니다 {min}.\",\n    \"components.Input.error.validation.minSupMax\": \"이 보다 더 클 수 없습니다.\",\n    \"components.Input.error.validation.regex\": \"입력한 내용이 형식에 맞지 않습니다.\",\n    \"components.Input.error.validation.required\": \"내용을 입력해 주세요.\",\n    \"components.Input.error.validation.unique\": \"이 값은 이미 사용중입니다.\",\n    \"components.InputSelect.option.placeholder\": \"선택해 주세요.\",\n    \"components.ListRow.empty\": \"데이터가 없습니다.\",\n    \"components.NotAllowedInput.text\": \"이 필드를 볼 수 있는 권한이 없습니다.\",\n    \"components.OverlayBlocker.description\": \"이 기능은 서버를 재시작해야 합니다. 서버가 시작될 때까지 잠시만 기다려주세요.\",\n    \"components.OverlayBlocker.description.serverError\": \"서버가 재시작되지 않았습니다. 터미널에서 로그를 확인하십시오.\",\n    \"components.OverlayBlocker.title\": \"재시작하고 있습니다...\",\n    \"components.OverlayBlocker.title.serverError\": \"재시작 시간이 예상보다 오래 걸리고 있습니다.\",\n    \"components.PageFooter.select\": \"항목 수 / 페이지\",\n    \"components.ProductionBlocker.description\": \"이 플러그인은 안전을 위해 다른 환경에서 사용할 수 없습니다.\",\n    \"components.ProductionBlocker.header\": \"이 플러그인은 개발 모드에서만 사용할 수 있습니다.\",\n    \"components.Search.placeholder\": \"검색...\",\n    \"components.TableHeader.sort\": \"Sort on {label}\",\n    \"components.Wysiwyg.ToggleMode.markdown-mode\": \"마크다운 모드\",\n    \"components.Wysiwyg.ToggleMode.preview-mode\": \"미리보기 모드\",\n    \"components.Wysiwyg.collapse\": \"병합\",\n    \"components.Wysiwyg.selectOptions.H1\": \"제목 H1\",\n    \"components.Wysiwyg.selectOptions.H2\": \"제목 H2\",\n    \"components.Wysiwyg.selectOptions.H3\": \"제목 H3\",\n    \"components.Wysiwyg.selectOptions.H4\": \"제목 H4\",\n    \"components.Wysiwyg.selectOptions.H5\": \"제목 H5\",\n    \"components.Wysiwyg.selectOptions.H6\": \"제목 H6\",\n    \"components.Wysiwyg.selectOptions.title\": \"제목\",\n    \"components.WysiwygBottomControls.charactersIndicators\": \"문자 표시기\",\n    \"components.WysiwygBottomControls.fullscreen\": \"전체화면\",\n    \"components.WysiwygBottomControls.uploadFiles\": \"파일을 끌어 놓으세요. 혹은 클립보드에서 붙혀넣거나 {browse} 하세요.\",\n    \"components.WysiwygBottomControls.uploadFiles.browse\": \"선택\",\n    \"components.pagination.go-to\": \"{page} 페이지로\",\n    \"components.pagination.go-to-next\": \"다음 페이지\",\n    \"components.pagination.go-to-previous\": \"이전 페이지\",\n    \"components.pagination.remaining-links\": \"And {number} other links\",\n    \"components.popUpWarning.button.cancel\": \"아니요, 취소합니다.\",\n    \"components.popUpWarning.button.confirm\": \"네, 확인했습니다.\",\n    \"components.popUpWarning.message\": \"삭제하시겠습니까?\",\n    \"components.popUpWarning.title\": \"확인\",\n    \"form.button.done\": \"확인\",\n    \"global.prompt.unsaved\": \"이 페이지를 떠나시겠습니까? 모든 변경 사항이 없어집니다.\",\n    \"notification.contentType.relations.conflict\": \"콘텐츠 타입에 충돌하는 릴레이션(conflict relation)이 있습니다.\",\n    \"notification.default.title\": \"정보 알림:\",\n    \"notification.error\": \"에러가 발생했습니다.\",\n    \"notification.error.layout\": \"레이아웃을 가져올 수 없습니다.\",\n    \"notification.form.error.fields\": \"잘못 입력된 필드가 존재합니다.\",\n    \"notification.form.success.fields\": \"변경 사항이 저장되었습니다.\",\n    \"notification.link-copied\": \"링크가 클립보드에 복사되었습니다.\",\n    \"notification.permission.not-allowed-read\": \"이 문서를 볼 수 있는 권한이 없습니다.\",\n    \"notification.success.delete\": \"항목이 삭제되었습니다.\",\n    \"notification.success.saved\": \"저장되었습니다.\",\n    \"notification.success.title\": \"성공 알림:\",\n    \"notification.version.update.message\": \"Strapi 새 버전이 출시되었습니다!\",\n    \"notification.warning.title\": \"경고 알림:\",\n    or: or,\n    \"request.error.model.unknown\": \"모델이 없습니다.\",\n    skipToContent: skipToContent,\n    submit: submit\n};\n\nexport { Analytics, Documentation, Email, Password, Provider, ResetPasswordToken, Role, Username, Users, anErrorOccurred, clearLabel, ko as default, or, skipToContent, submit };\n//# sourceMappingURL=ko.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,YAAY;AAChB,IAAI,gBAAgB;AACpB,IAAI,QAAQ;AACZ,IAAI,WAAW;AACf,IAAI,WAAW;AACf,IAAI,qBAAqB;AACzB,IAAI,OAAO;AACX,IAAI,WAAW;AACf,IAAI,QAAQ;AACZ,IAAI,kBAAkB;AACtB,IAAI,aAAa;AACjB,IAAI,KAAK;AACT,IAAI,gBAAgB;AACpB,IAAI,SAAS;AACb,IAAI,KAAK;AAAA,EACL;AAAA,EACA,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0CAA0C;AAAA,EAC1C,iCAAiC;AAAA,EACjC,sCAAsC;AAAA,EACtC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,iDAAiD;AAAA,EACjD,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,2BAA2B;AAAA,EAC3B,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA,EAChB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,6BAA6B;AAAA,EAC7B,qCAAqC;AAAA,EACrC,0CAA0C;AAAA,EAC1C,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,4BAA4B;AAAA,EAC5B,0CAA0C;AAAA,EAC1C,sBAAsB;AAAA,EACtB,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,4BAA4B;AAAA,EAC5B,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,oCAAoC;AAAA,EACpC,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,yDAAyD;AAAA,EACzD,8DAA8D;AAAA,EAC9D,mEAAmE;AAAA,EACnE,kEAAkE;AAAA,EAClE,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,oDAAoD;AAAA,EACpD,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,mCAAmC;AAAA,EACnC,iDAAiD;AAAA,EACjD,yDAAyD;AAAA,EACzD,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,wCAAwC;AAAA,EACxC,0CAA0C;AAAA,EAC1C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,6CAA6C;AAAA,EAC7C,yDAAyD;AAAA,EACzD,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,wCAAwC;AAAA,EACxC,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,2CAA2C;AAAA,EAC3C,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,4CAA4C;AAAA,EAC5C,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,2BAA2B;AAAA,EAC3B;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,+BAA+B;AAAA,EAC/B,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,kDAAkD;AAAA,EAClD,wCAAwC;AAAA,EACxC;AAAA,EACA,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,wCAAwC;AAAA,EACxC,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,4CAA4C;AAAA,EAC5C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,0CAA0C;AAAA,EAC1C,sCAAsC;AAAA,EACtC,2CAA2C;AAAA,EAC3C,wDAAwD;AAAA,EACxD,sDAAsD;AAAA,EACtD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,2DAA2D;AAAA,EAC3D,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,oCAAoC;AAAA,EACpC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,2CAA2C;AAAA,EAC3C,2BAA2B;AAAA,EAC3B,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,+EAA+E;AAAA,EAC/E,qEAAqE;AAAA,EACrE,yEAAyE;AAAA,EACzE,mEAAmE;AAAA,EACnE,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,4DAA4D;AAAA,EAC5D,0DAA0D;AAAA,EAC1D,sEAAsE;AAAA,EACtE,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,qDAAqD;AAAA,EACrD,wDAAwD;AAAA,EACxD,yDAAyD;AAAA,EACzD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,qCAAqC;AAAA,EACrC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,oEAAoE;AAAA,EACpE,4DAA4D;AAAA,EAC5D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,wDAAwD;AAAA,EACxD,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,yCAAyC;AAAA,EACzC,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,4BAA4B;AAAA,EAC5B,uBAAuB;AAAA,EACvB,kBAAkB;AAAA,EAClB,yCAAyC;AAAA,EACzC,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,gCAAgC;AAAA,EAChC,wCAAwC;AAAA,EACxC,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB;AAAA,EACA,eAAe;AAAA,EACf,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,+CAA+C;AAAA,EAC/C,qDAAqD;AAAA,EACrD,sDAAsD;AAAA,EACtD,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,0CAA0C;AAAA,EAC1C,4CAA4C;AAAA,EAC5C,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,gDAAgD;AAAA,EAChD,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,yDAAyD;AAAA,EACzD,+CAA+C;AAAA,EAC/C,gDAAgD;AAAA,EAChD,uDAAuD;AAAA,EACvD,+BAA+B;AAAA,EAC/B,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,yCAAyC;AAAA,EACzC,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,oBAAoB;AAAA,EACpB,yBAAyB;AAAA,EACzB,+CAA+C;AAAA,EAC/C,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,4CAA4C;AAAA,EAC5C,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B;AAAA,EACA,+BAA+B;AAAA,EAC/B;AAAA,EACA;AACJ;", "names": []}