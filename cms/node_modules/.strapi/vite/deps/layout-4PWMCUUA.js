import {
  CardDragPreview
} from "./chunk-ADBKZJUD.js";
import {
  useTypedDispatch,
  useTypedSelector
} from "./chunk-C3SQV6Q7.js";
import {
  setInitialData
} from "./chunk-OTW64HJY.js";
import {
  Disconnect<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>
} from "./chunk-AGGTEVCI.js";
import {
  DocumentStatus
} from "./chunk-2PKL3TZW.js";
import {
  ItemTypes
} from "./chunk-MEVWXSLO.js";
import "./chunk-2UKZ2UD6.js";
import {
  COLLECTION_TYPES,
  HOOKS,
  SINGLE_TYPES,
  require_lib,
  useContentTypeSchema,
  useGetAllContentTypeSettingsQuery,
  useGetInitialDataQuery
} from "./chunk-LIDWYKFI.js";
import "./chunk-CNQJ6H2W.js";
import {
  getTranslation
} from "./chunk-HIZVCZYI.js";
import "./chunk-6LY4MOO2.js";
import "./chunk-DDRVMFUU.js";
import "./chunk-SYWYLB7I.js";
import {
  useDragLayer
} from "./chunk-HN5HEQBI.js";
import "./chunk-YTYOKH3J.js";
import "./chunk-IHK57WMI.js";
import "./chunk-4QC3H4VA.js";
import "./chunk-BI5NNIZJ.js";
import {
  SubNav
} from "./chunk-NG2C3VVD.js";
import {
  useGuidedTour
} from "./chunk-HTIJXKV5.js";
import "./chunk-U2WQMOGI.js";
import "./chunk-Y7ATVFIR.js";
import "./chunk-YHXFGNIS.js";
import "./chunk-SWRLCKDU.js";
import "./chunk-QQYLVHWD.js";
import "./chunk-QU5YEK3U.js";
import "./chunk-G7IUBXYD.js";
import "./chunk-ZMVBHWHR.js";
import "./chunk-IAC3QHW4.js";
import "./chunk-3B6YWEPM.js";
import "./chunk-EP6CMRDH.js";
import "./chunk-MBK4V2X7.js";
import "./chunk-X26IT6UA.js";
import "./chunk-K65KIEAL.js";
import "./chunk-MYUJRQKL.js";
import "./chunk-BJKYWYYT.js";
import "./chunk-TJHDBNKZ.js";
import "./chunk-5K42SEBG.js";
import "./chunk-COX6EF2B.js";
import "./chunk-IFOFBKTA.js";
import "./chunk-4WX3TY7V.js";
import "./chunk-EGNP2T5O.js";
import "./chunk-USP7YHPW.js";
import "./chunk-LLJ4ZRUJ.js";
import "./chunk-YXDCVYVT.js";
import "./chunk-QIJGNK42.js";
import "./chunk-X6QBIBK4.js";
import "./chunk-YMKQOWME.js";
import "./chunk-IQMXEFLL.js";
import "./chunk-F2ZGELOG.js";
import "./chunk-6EESFY62.js";
import "./chunk-QQ4VIATE.js";
import "./chunk-VWIQ6S3Y.js";
import "./chunk-EYSFNMMY.js";
import "./chunk-D4WYVNVM.js";
import "./chunk-MMOBCIZG.js";
import "./chunk-TLFBIKP6.js";
import {
  Layouts
} from "./chunk-IL5LQ6CF.js";
import "./chunk-PQINNV4N.js";
import "./chunk-VYSYYPOB.js";
import {
  Page,
  useAPIErrorHandler
} from "./chunk-3UVBSCPH.js";
import {
  useAuth,
  useQueryParams,
  useStrapiApp
} from "./chunk-JXTOCMQW.js";
import "./chunk-B33LMLYV.js";
import "./chunk-XU64YSEO.js";
import "./chunk-IHIZNGTI.js";
import "./chunk-BHLYCXQ7.js";
import "./chunk-BLJUR3JO.js";
import "./chunk-CE4VABH2.js";
import "./chunk-QOUV5O5E.js";
import {
  useNotification
} from "./chunk-4EY5FDQG.js";
import {
  Box,
  Divider,
  Flex,
  IconButton,
  TextInput,
  Typography,
  useCollator,
  useFilter,
  useIntl,
  useNotifyAT
} from "./chunk-4YYUDJZ2.js";
import "./chunk-5ZC4PE57.js";
import {
  Navigate,
  Outlet,
  useLocation,
  useMatch
} from "./chunk-S65ZWNEO.js";
import "./chunk-FOD4ENRR.js";
import {
  ForwardRef$3T,
  ForwardRef$45,
  ForwardRef$4T,
  ForwardRef$X,
  ForwardRef$j
} from "./chunk-QW5ZDDTU.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  dt
} from "./chunk-3CQBCJ3G.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/content-manager/dist/admin/layout.mjs
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
var React2 = __toESM(require_react(), 1);

// node_modules/@strapi/content-manager/dist/admin/components/DragLayer.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);
function getStyle(initialOffset, currentOffset, mouseOffset) {
  if (!initialOffset || !currentOffset || !mouseOffset) {
    return {
      display: "none"
    };
  }
  const { x, y } = mouseOffset;
  return {
    transform: `translate(${x}px, ${y}px)`
  };
}
var DragLayer = ({ renderItem }) => {
  const { itemType, isDragging, item, initialOffset, currentOffset, mouseOffset } = useDragLayer((monitor) => ({
    item: monitor.getItem(),
    itemType: monitor.getItemType(),
    initialOffset: monitor.getInitialSourceClientOffset(),
    currentOffset: monitor.getSourceClientOffset(),
    isDragging: monitor.isDragging(),
    mouseOffset: monitor.getClientOffset()
  }));
  if (!isDragging) {
    return null;
  }
  return (0, import_jsx_runtime.jsx)(Box, {
    height: "100%",
    left: 0,
    position: "fixed",
    pointerEvents: "none",
    top: 0,
    zIndex: 100,
    width: "100%",
    children: (0, import_jsx_runtime.jsx)(Box, {
      style: getStyle(initialOffset, currentOffset, mouseOffset),
      children: renderItem({
        type: itemType,
        item
      })
    })
  });
};

// node_modules/@strapi/content-manager/dist/admin/components/DragPreviews/ComponentDragPreview.mjs
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var ComponentDragPreview = ({ displayedValue }) => {
  return (0, import_jsx_runtime2.jsxs)(Flex, {
    background: "neutral0",
    borderColor: "neutral200",
    justifyContent: "space-between",
    gap: 3,
    padding: 3,
    width: "30rem",
    children: [
      (0, import_jsx_runtime2.jsx)(ToggleButton, {
        type: "button",
        children: (0, import_jsx_runtime2.jsxs)(Flex, {
          gap: 6,
          children: [
            (0, import_jsx_runtime2.jsx)(DropdownIconWrapper, {
              alignItems: "center",
              justifyContent: "center",
              background: "neutral200",
              height: "3.2rem",
              width: "3.2rem",
              children: (0, import_jsx_runtime2.jsx)(ForwardRef$4T, {})
            }),
            (0, import_jsx_runtime2.jsx)(Flex, {
              maxWidth: "15rem",
              children: (0, import_jsx_runtime2.jsx)(Typography, {
                textColor: "neutral700",
                ellipsis: true,
                children: displayedValue
              })
            })
          ]
        })
      }),
      (0, import_jsx_runtime2.jsxs)(Flex, {
        gap: 2,
        children: [
          (0, import_jsx_runtime2.jsx)(IconButton, {
            withTooltip: false,
            label: "",
            variant: "ghost",
            children: (0, import_jsx_runtime2.jsx)(ForwardRef$j, {})
          }),
          (0, import_jsx_runtime2.jsx)(IconButton, {
            withTooltip: false,
            label: "",
            variant: "ghost",
            children: (0, import_jsx_runtime2.jsx)(ForwardRef$3T, {})
          })
        ]
      })
    ]
  });
};
var DropdownIconWrapper = dt(Flex)`
  border-radius: 50%;

  svg {
    height: 0.6rem;
    width: 1.1rem;
    > path {
      fill: ${({ theme }) => theme.colors.neutral600};
    }
  }
`;
var ToggleButton = dt.button`
  border: none;
  background: transparent;
  display: block;
  width: 100%;
  text-align: unset;
  padding: 0;
`;

// node_modules/@strapi/content-manager/dist/admin/components/DragPreviews/RelationDragPreview.mjs
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var RelationDragPreview = ({ status, displayedValue, width }) => {
  return (0, import_jsx_runtime3.jsx)(Box, {
    style: {
      width
    },
    children: (0, import_jsx_runtime3.jsxs)(Flex, {
      paddingTop: 2,
      paddingBottom: 2,
      paddingLeft: 2,
      paddingRight: 4,
      hasRadius: true,
      borderWidth: 1,
      background: "neutral0",
      borderColor: "neutral200",
      justifyContent: "space-between",
      gap: 4,
      children: [
        (0, import_jsx_runtime3.jsxs)(FlexWrapper, {
          gap: 1,
          children: [
            (0, import_jsx_runtime3.jsx)(IconButton, {
              withTooltip: false,
              label: "",
              variant: "ghost",
              children: (0, import_jsx_runtime3.jsx)(ForwardRef$3T, {})
            }),
            (0, import_jsx_runtime3.jsxs)(Flex, {
              width: "100%",
              minWidth: 0,
              justifyContent: "space-between",
              children: [
                (0, import_jsx_runtime3.jsx)(Box, {
                  minWidth: 0,
                  paddingTop: 1,
                  paddingBottom: 1,
                  paddingRight: 4,
                  children: (0, import_jsx_runtime3.jsx)(LinkEllipsis, {
                    href: "",
                    children: (0, import_jsx_runtime3.jsx)(Typography, {
                      textColor: "primary600",
                      ellipsis: true,
                      children: displayedValue
                    })
                  })
                }),
                status ? (0, import_jsx_runtime3.jsx)(DocumentStatus, {
                  status
                }) : null
              ]
            })
          ]
        }),
        (0, import_jsx_runtime3.jsx)(DisconnectButton, {
          type: "button",
          children: (0, import_jsx_runtime3.jsx)(ForwardRef$45, {
            width: "12px"
          })
        })
      ]
    })
  });
};

// node_modules/@strapi/content-manager/dist/admin/components/LeftMenu.mjs
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var import_qs = __toESM(require_lib(), 1);
var LeftMenu = () => {
  const [search, setSearch] = React.useState("");
  const [{ query }] = useQueryParams();
  const { formatMessage, locale } = useIntl();
  const collectionTypeLinks = useTypedSelector((state) => state["content-manager"].app.collectionTypeLinks);
  const singleTypeLinks = useTypedSelector((state) => state["content-manager"].app.singleTypeLinks);
  const { schemas } = useContentTypeSchema();
  const { startsWith } = useFilter(locale, {
    sensitivity: "base"
  });
  const formatter = useCollator(locale, {
    sensitivity: "base"
  });
  const menu = React.useMemo(() => [
    {
      id: "collectionTypes",
      title: formatMessage({
        id: getTranslation("components.LeftMenu.collection-types"),
        defaultMessage: "Collection Types"
      }),
      searchable: true,
      links: collectionTypeLinks
    },
    {
      id: "singleTypes",
      title: formatMessage({
        id: getTranslation("components.LeftMenu.single-types"),
        defaultMessage: "Single Types"
      }),
      searchable: true,
      links: singleTypeLinks
    }
  ].map((section) => ({
    ...section,
    links: section.links.filter((link) => startsWith(link.title, search)).sort((a, b) => formatter.compare(a.title, b.title)).map((link) => {
      return {
        ...link,
        title: formatMessage({
          id: link.title,
          defaultMessage: link.title
        })
      };
    })
  })), [
    collectionTypeLinks,
    search,
    singleTypeLinks,
    startsWith,
    formatMessage,
    formatter
  ]);
  const handleClear = () => {
    setSearch("");
  };
  const handleChangeSearch = ({ target: { value } }) => {
    setSearch(value);
  };
  const label = formatMessage({
    id: getTranslation("header.name"),
    defaultMessage: "Content Manager"
  });
  const getPluginsParamsForLink = (link) => {
    var _a, _b;
    const schema = schemas.find((schema2) => schema2.uid === link.uid);
    const isI18nEnabled = Boolean((_b = (_a = schema == null ? void 0 : schema.pluginOptions) == null ? void 0 : _a.i18n) == null ? void 0 : _b.localized);
    if (query.plugins && "i18n" in query.plugins) {
      const { i18n, ...restPlugins } = query.plugins;
      if (!isI18nEnabled) {
        return restPlugins;
      }
      return {
        i18n,
        ...restPlugins
      };
    }
    return query.plugins;
  };
  return (0, import_jsx_runtime4.jsxs)(SubNav.Main, {
    "aria-label": label,
    children: [
      (0, import_jsx_runtime4.jsx)(SubNav.Header, {
        label
      }),
      (0, import_jsx_runtime4.jsx)(Divider, {
        background: "neutral150"
      }),
      (0, import_jsx_runtime4.jsx)(Flex, {
        padding: 5,
        gap: 3,
        direction: "column",
        alignItems: "stretch",
        children: (0, import_jsx_runtime4.jsx)(TextInput, {
          startAction: (0, import_jsx_runtime4.jsx)(ForwardRef$X, {
            fill: "neutral500"
          }),
          value: search,
          onChange: handleChangeSearch,
          "aria-label": "Search",
          placeholder: formatMessage({
            id: "content-manager.components.LeftMenu.Search.label",
            defaultMessage: "Search for a content type"
          }),
          endAction: (0, import_jsx_runtime4.jsx)(ForwardRef$45, {
            onClick: handleClear,
            fill: "neutral500",
            cursor: "pointer"
          }),
          size: "S"
        })
      }),
      (0, import_jsx_runtime4.jsx)(SubNav.Sections, {
        children: menu.map((section) => {
          return (0, import_jsx_runtime4.jsx)(SubNav.Section, {
            label: section.title,
            children: section.links.map((link) => {
              return (0, import_jsx_runtime4.jsx)(SubNav.Link, {
                to: {
                  pathname: link.to,
                  search: (0, import_qs.stringify)({
                    ...(0, import_qs.parse)(link.search ?? ""),
                    plugins: getPluginsParamsForLink(link)
                  })
                },
                label: link.title
              }, link.uid);
            })
          }, section.id);
        })
      })
    ]
  });
};

// node_modules/@strapi/content-manager/dist/admin/hooks/useContentManagerInitData.mjs
var import_react2 = __toESM(require_react(), 1);
var import_qs2 = __toESM(require_lib(), 1);
var { MUTATE_COLLECTION_TYPES_LINKS, MUTATE_SINGLE_TYPES_LINKS } = HOOKS;
var useContentManagerInitData = () => {
  const { toggleNotification } = useNotification();
  const dispatch = useTypedDispatch();
  const runHookWaterfall = useStrapiApp("useContentManagerInitData", (state2) => state2.runHookWaterfall);
  const { notifyStatus } = useNotifyAT();
  const { formatMessage } = useIntl();
  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler(getTranslation);
  const checkUserHasPermissions = useAuth("useContentManagerInitData", (state2) => state2.checkUserHasPermissions);
  const state = useTypedSelector((state2) => state2["content-manager"].app);
  const initialDataQuery = useGetInitialDataQuery(void 0, {
    /**
    * TODO: remove this when the CTB has been refactored to use redux-toolkit-query
    * and it can invalidate the cache on mutation
    */
    refetchOnMountOrArgChange: true
  });
  (0, import_react2.useEffect)(() => {
    if (initialDataQuery.data) {
      notifyStatus(formatMessage({
        id: getTranslation("App.schemas.data-loaded"),
        defaultMessage: "The schemas have been successfully loaded."
      }));
    }
  }, [
    formatMessage,
    initialDataQuery.data,
    notifyStatus
  ]);
  (0, import_react2.useEffect)(() => {
    if (initialDataQuery.error) {
      toggleNotification({
        type: "danger",
        message: formatAPIError(initialDataQuery.error)
      });
    }
  }, [
    formatAPIError,
    initialDataQuery.error,
    toggleNotification
  ]);
  const contentTypeSettingsQuery = useGetAllContentTypeSettingsQuery();
  (0, import_react2.useEffect)(() => {
    if (contentTypeSettingsQuery.error) {
      toggleNotification({
        type: "danger",
        message: formatAPIError(contentTypeSettingsQuery.error)
      });
    }
  }, [
    formatAPIError,
    contentTypeSettingsQuery.error,
    toggleNotification
  ]);
  const formatData = async (components, contentTypes, fieldSizes, contentTypeConfigurations) => {
    const { collectionType: collectionTypeLinks, singleType: singleTypeLinks } = contentTypes.reduce((acc, model) => {
      acc[model.kind].push(model);
      return acc;
    }, {
      collectionType: [],
      singleType: []
    });
    const collectionTypeSectionLinks = generateLinks(collectionTypeLinks, "collectionTypes", contentTypeConfigurations);
    const singleTypeSectionLinks = generateLinks(singleTypeLinks, "singleTypes");
    const collectionTypeLinksPermissions = await Promise.all(collectionTypeSectionLinks.map(({ permissions }) => checkUserHasPermissions(permissions)));
    const authorizedCollectionTypeLinks = collectionTypeSectionLinks.filter((_, index) => collectionTypeLinksPermissions[index].length > 0);
    const singleTypeLinksPermissions = await Promise.all(singleTypeSectionLinks.map(({ permissions }) => checkUserHasPermissions(permissions)));
    const authorizedSingleTypeLinks = singleTypeSectionLinks.filter((_, index) => singleTypeLinksPermissions[index].length > 0);
    const { ctLinks } = runHookWaterfall(MUTATE_COLLECTION_TYPES_LINKS, {
      ctLinks: authorizedCollectionTypeLinks,
      models: contentTypes
    });
    const { stLinks } = runHookWaterfall(MUTATE_SINGLE_TYPES_LINKS, {
      stLinks: authorizedSingleTypeLinks,
      models: contentTypes
    });
    dispatch(setInitialData({
      authorizedCollectionTypeLinks: ctLinks,
      authorizedSingleTypeLinks: stLinks,
      components,
      contentTypeSchemas: contentTypes,
      fieldSizes
    }));
  };
  (0, import_react2.useEffect)(() => {
    if (initialDataQuery.data && contentTypeSettingsQuery.data) {
      formatData(initialDataQuery.data.components, initialDataQuery.data.contentTypes, initialDataQuery.data.fieldSizes, contentTypeSettingsQuery.data);
    }
  }, [
    initialDataQuery.data,
    contentTypeSettingsQuery.data
  ]);
  return {
    ...state
  };
};
var generateLinks = (links, type, configurations = []) => {
  return links.filter((link) => link.isDisplayed).map((link) => {
    const collectionTypesPermissions = [
      {
        action: "plugin::content-manager.explorer.create",
        subject: link.uid
      },
      {
        action: "plugin::content-manager.explorer.read",
        subject: link.uid
      }
    ];
    const singleTypesPermissions = [
      {
        action: "plugin::content-manager.explorer.read",
        subject: link.uid
      }
    ];
    const permissions = type === "collectionTypes" ? collectionTypesPermissions : singleTypesPermissions;
    const currentContentTypeConfig = configurations.find(({ uid }) => uid === link.uid);
    let search = null;
    if (currentContentTypeConfig) {
      const searchParams = {
        page: 1,
        pageSize: currentContentTypeConfig.settings.pageSize,
        sort: `${currentContentTypeConfig.settings.defaultSortBy}:${currentContentTypeConfig.settings.defaultSortOrder}`
      };
      search = (0, import_qs2.stringify)(searchParams, {
        encode: false
      });
    }
    return {
      permissions,
      search,
      kind: link.kind,
      title: link.info.displayName,
      to: `/content-manager/${link.kind === "collectionType" ? COLLECTION_TYPES : SINGLE_TYPES}/${link.uid}`,
      uid: link.uid,
      // Used for the list item key in the helper plugin
      name: link.uid,
      isDisplayed: link.isDisplayed
    };
  });
};

// node_modules/@strapi/content-manager/dist/admin/layout.mjs
var Layout = () => {
  const contentTypeMatch = useMatch("/content-manager/:kind/:uid/*");
  const { isLoading, collectionTypeLinks, models, singleTypeLinks } = useContentManagerInitData();
  const authorisedModels = [
    ...collectionTypeLinks,
    ...singleTypeLinks
  ].sort((a, b) => a.title.localeCompare(b.title));
  const { pathname } = useLocation();
  const { formatMessage } = useIntl();
  const startSection = useGuidedTour("Layout", (state) => state.startSection);
  const startSectionRef = React2.useRef(startSection);
  React2.useEffect(() => {
    if (startSectionRef.current) {
      startSectionRef.current("contentManager");
    }
  }, []);
  if (isLoading) {
    return (0, import_jsx_runtime5.jsxs)(import_jsx_runtime5.Fragment, {
      children: [
        (0, import_jsx_runtime5.jsx)(Page.Title, {
          children: formatMessage({
            id: getTranslation("plugin.name"),
            defaultMessage: "Content Manager"
          })
        }),
        (0, import_jsx_runtime5.jsx)(Page.Loading, {})
      ]
    });
  }
  const supportedModelsToDisplay = models.filter(({ isDisplayed }) => isDisplayed);
  if (authorisedModels.length === 0 && supportedModelsToDisplay.length > 0 && pathname !== "/content-manager/403") {
    return (0, import_jsx_runtime5.jsx)(Navigate, {
      to: "/403"
    });
  }
  if (supportedModelsToDisplay.length === 0 && pathname !== "/no-content-types") {
    return (0, import_jsx_runtime5.jsx)(Navigate, {
      to: "/no-content-types"
    });
  }
  if (!contentTypeMatch && authorisedModels.length > 0) {
    return (0, import_jsx_runtime5.jsx)(Navigate, {
      to: {
        pathname: authorisedModels[0].to,
        search: authorisedModels[0].search ?? ""
      },
      replace: true
    });
  }
  return (0, import_jsx_runtime5.jsxs)(import_jsx_runtime5.Fragment, {
    children: [
      (0, import_jsx_runtime5.jsx)(Page.Title, {
        children: formatMessage({
          id: getTranslation("plugin.name"),
          defaultMessage: "Content Manager"
        })
      }),
      (0, import_jsx_runtime5.jsxs)(Layouts.Root, {
        sideNav: (0, import_jsx_runtime5.jsx)(LeftMenu, {}),
        children: [
          (0, import_jsx_runtime5.jsx)(DragLayer, {
            renderItem: renderDraglayerItem
          }),
          (0, import_jsx_runtime5.jsx)(Outlet, {})
        ]
      })
    ]
  });
};
function renderDraglayerItem({ type, item }) {
  if (!type || type && typeof type !== "string") {
    return null;
  }
  const [actualType] = type.split("_");
  switch (actualType) {
    case ItemTypes.EDIT_FIELD:
    case ItemTypes.FIELD:
      return (0, import_jsx_runtime5.jsx)(CardDragPreview, {
        label: item.label
      });
    case ItemTypes.COMPONENT:
    case ItemTypes.DYNAMIC_ZONE:
      return (0, import_jsx_runtime5.jsx)(ComponentDragPreview, {
        displayedValue: item.displayedValue
      });
    case ItemTypes.RELATION:
      return (0, import_jsx_runtime5.jsx)(RelationDragPreview, {
        ...item
      });
    default:
      return null;
  }
}
export {
  Layout
};
//# sourceMappingURL=layout-4PWMCUUA.js.map
