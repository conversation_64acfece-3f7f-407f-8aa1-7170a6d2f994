import "./chunk-PLDDJCW6.js";

// node_modules/@strapi/content-manager/dist/admin/translations/ml.json.mjs
var groups = "ഗ്രൂപ്പുകൾ";
var models = "ശേഖരണ തരങ്ങൾ";
var pageNotFound = "പേജ് കണ്ടെത്തിയില്ല";
var ml = {
  "App.schemas.data-loaded": "സ്കീമകൾ വിജയകരമായി ലോഡ് ചെയ്തു",
  "ListViewTable.relation-loaded": "ബന്ധങ്ങൾ ലോഡ് ചെയ്തു",
  "ListViewTable.relation-loading": "ബന്ധങ്ങൾ ലോഡുചെയ്യുന്നു",
  "ListViewTable.relation-more": "പ്രദർശിപ്പിച്ചതിനേക്കാൾ കൂടുതൽ എന്റിറ്റികൾ ഈ ബന്ധത്തിൽ അടങ്ങിയിരിക്കുന്നു",
  "EditRelations.title": "റിലേഷണൽ ഡാറ്റ",
  "HeaderLayout.button.label-add-entry": "പുതിയ എൻട്രി സൃഷ്ടിക്കുക",
  "api.id": "API ഐഡി",
  "components.AddFilterCTA.add": "ഫിൽട്ടറുകൾ",
  "components.AddFilterCTA.hide": "ഫിൽട്ടറുകൾ",
  "components.DragHandle-label": "ഡ്രാഗ്",
  "components.DraggableAttr.edit": "എഡിറ്റ് ചെയ്യാൻ ക്ലിക്ക് ചെയ്യുക",
  "components.DraggableCard.delete.field": "{item} ഇല്ലാതാക്കുക",
  "components.DraggableCard.edit.field": "എഡിറ്റ് {ഇനം}",
  "components.DraggableCard.move.field": "നീക്കുക {ഇനം}",
  "components.ListViewTable.row-line": "ഇനത്തിന്റെ വരി {number}",
  "components.DynamicZone.ComponentPicker-label": "ഒരു ഘടകം തിരഞ്ഞെടുക്കുക",
  "components.DynamicZone.add-component": "{componentName} എന്നതിലേക്ക് ഒരു ഘടകം ചേർക്കുക",
  "components.DynamicZone.delete-label": "{name} ഇല്ലാതാക്കുക",
  "components.DynamicZone.error-message": "ഘടകത്തിൽ പിശക്(കൾ) അടങ്ങിയിരിക്കുന്നു",
  "components.DynamicZone.missing-components": "അവിടെ {സംഖ്യ, ബഹുവചനം, =0 {നഷ്‌ടമായ # ഘടകങ്ങളുണ്ട്} ഒന്ന് {# നഷ്‌ടമായ ഘടകമാണ്} മറ്റൊന്ന് {# നഷ്‌ടമായ ഘടകങ്ങളാണ്}}",
  "components.DynamicZone.move-down-label": "ഘടകം താഴേക്ക് നീക്കുക",
  "components.DynamicZone.move-up-label": "ഘടകം മുകളിലേക്ക് നീക്കുക",
  "components.DynamicZone.pick-compo": "ഒരു ഘടകം തിരഞ്ഞെടുക്കുക",
  "components.DynamicZone.required": "ഘടകം ആവശ്യമാണ്",
  "components.EmptyAttributesBlock.button": "ക്രമീകരണ പേജിലേക്ക് പോകുക",
  "components.EmptyAttributesBlock.description": "നിങ്ങളുടെ ക്രമീകരണങ്ങൾ നിങ്ങൾക്ക് മാറ്റാവുന്നതാണ്",
  "components.FieldItem.linkToComponentLayout": "ഘടകത്തിന്റെ ലേഔട്ട് സജ്ജമാക്കുക",
  "components.FieldSelect.label": "ഒരു ഫീൽഡ് ചേർക്കുക",
  "components.FilterOptions.button.apply": "പ്രയോഗിക്കുക",
  "components.FiltersPickWrapper.PluginHeader.actions.apply": "പ്രയോഗിക്കുക",
  "components.FiltersPickWrapper.PluginHeader.actions.clearAll": "എല്ലാം മായ്‌ക്കുക",
  "components.FiltersPickWrapper.PluginHeader.description": "എൻട്രികൾ ഫിൽട്ടർ ചെയ്യുന്നതിന് ബാധകമാക്കുന്നതിനുള്ള വ്യവസ്ഥകൾ സജ്ജമാക്കുക",
  "components.FiltersPickWrapper.PluginHeader.title.filter": "ഫിൽട്ടറുകൾ",
  "components.FiltersPickWrapper.hide": "മറയ്ക്കുക",
  "components.LeftMenu.Search.label": "ഒരു ഉള്ളടക്ക തരത്തിനായി തിരയുക",
  "components.LeftMenu.collection-types": "ശേഖരണ തരങ്ങൾ",
  "components.LeftMenu.single-types": "ഒറ്റ തരങ്ങൾ",
  "components.LimitSelect.itemsPerPage": "ഓരോ പേജിനും ഇനങ്ങൾ",
  "components.NotAllowedInput.text": "ഈ ഫീൽഡ് കാണുന്നതിന് അനുമതികളൊന്നുമില്ല",
  "components.RepeatableComponent.error-message": "ഘടക(ങ്ങളിൽ) പിശക്(കൾ) അടങ്ങിയിരിക്കുന്നു",
  "components.Search.placeholder": "ഒരു എൻട്രിക്കായി തിരയുക...",
  "components.Select.draft-info-title": "സംസ്ഥാനം: ഡ്രാഫ്റ്റ്",
  "components.Select.publish-info-title": "സംസ്ഥാനം: പ്രസിദ്ധീകരിച്ചത്",
  "components.SettingsViewWrapper.pluginHeader.description.edit-settings": "എഡിറ്റ് കാഴ്‌ച എങ്ങനെയായിരിക്കുമെന്ന് ഇഷ്ടാനുസൃതമാക്കുക.",
  "components.SettingsViewWrapper.pluginHeader.description.list-settings": "ലിസ്റ്റ് കാഴ്ചയുടെ ക്രമീകരണങ്ങൾ നിർവചിക്കുക.",
  "components.SettingsViewWrapper.pluginHeader.title": "കാഴ്ച കോൺഫിഗർ ചെയ്യുക - {name}",
  "components.TableDelete.delete": "എല്ലാം ഇല്ലാതാക്കുക",
  "components.TableDelete.deleteSelected": "തിരഞ്ഞെടുത്ത ഇല്ലാതാക്കുക",
  "components.TableDelete.label": "{സംഖ്യ, ബഹുവചനം, ഒരു {# എൻട്രി} മറ്റ് {# എൻട്രികൾ}} തിരഞ്ഞെടുത്തു",
  "components.TableEmpty.withFilters": "പ്രയോഗിച്ച ഫിൽട്ടറുകൾക്കൊപ്പം {contentType} ഒന്നുമില്ല...",
  "components.TableEmpty.withSearch": "തിരയലുമായി ബന്ധപ്പെട്ട {contentType} ഒന്നുമില്ല ({തിരയൽ})...",
  "components.TableEmpty.withoutFilter": "{contentType} ഒന്നുമില്ല...",
  "components.empty-repeatable": "ഇതുവരെ എൻട്രി ഒന്നുമില്ല. ഒരെണ്ണം ചേർക്കാൻ ചുവടെയുള്ള ബട്ടണിൽ ക്ലിക്കുചെയ്യുക.",
  "components.notification.info.maximum-requirement": "നിങ്ങൾ ഇതിനകം പരമാവധി എണ്ണം ഫീൽഡുകളിൽ എത്തിയിരിക്കുന്നു",
  "components.notification.info.minimum-requirement": "കുറഞ്ഞ ആവശ്യകതയുമായി പൊരുത്തപ്പെടുന്നതിന് ഒരു ഫീൽഡ് ചേർത്തു",
  "components.repeatable.reorder.error": "നിങ്ങളുടെ ഘടകത്തിന്റെ ഫീൽഡ് പുനഃക്രമീകരിക്കുമ്പോൾ ഒരു പിശക് സംഭവിച്ചു, ദയവായി വീണ്ടും ശ്രമിക്കുക",
  "components.reset-entry": "റീസെറ്റ് എൻട്രി",
  "components.uid.apply": "പ്രയോഗിക്കുക",
  "components.uid.available": "ലഭ്യം",
  "components.uid.regenerate": "പുനരുജ്ജീവിപ്പിക്കുക",
  "components.uid.suggested": "നിർദ്ദേശിച്ചത്",
  "components.uid.unavailable": "ലഭ്യമല്ല",
  "containers.Edit.Link.Layout": "ലേഔട്ട് കോൺഫിഗർ ചെയ്യുക",
  "containers.Edit.Link.Model": "ശേഖരം-തരം എഡിറ്റ് ചെയ്യുക",
  "containers.Edit.addAnItem": "ഒരു ഇനം ചേർക്കുക...",
  "containers.Edit.clickToJump": "എൻട്രിയിലേക്ക് പോകുന്നതിന് ക്ലിക്കുചെയ്യുക",
  "containers.Edit.delete": "ഇല്ലാതാക്കുക",
  "containers.Edit.delete-entry": "ഈ എൻട്രി ഇല്ലാതാക്കുക",
  "containers.Edit.editing": "എഡിറ്റിംഗ്...",
  "containers.Edit.information": "വിവരങ്ങൾ",
  "containers.Edit.information.by": "എഴുതിയത്",
  "containers.Edit.information.created": "സൃഷ്ടിച്ചു",
  "containers.Edit.information.draftVersion": "ഡ്രാഫ്റ്റ് പതിപ്പ്",
  "containers.Edit.information.editing": "എഡിറ്റിംഗ്",
  "containers.Edit.information.lastUpdate": "അവസാന അപ്ഡേറ്റ്",
  "containers.Edit.information.publishedVersion": "പ്രസിദ്ധീകരിച്ച പതിപ്പ്",
  "containers.Edit.pluginHeader.title.new": "ഒരു എൻട്രി സൃഷ്ടിക്കുക",
  "containers.Edit.reset": "പുനഃസജ്ജമാക്കുക",
  "containers.Edit.returnList": "ലിസ്റ്റിലേക്ക് മടങ്ങുക",
  "containers.Edit.seeDetails": "വിശദാംശങ്ങൾ",
  "containers.Edit.submit": "സംരക്ഷിക്കുക",
  "containers.EditSettingsView.modal-form.edit-field": "ഫീൽഡ് എഡിറ്റ് ചെയ്യുക",
  "containers.EditView.add.new-entry": "ഒരു എൻട്രി ചേർക്കുക",
  "containers.EditView.notification.errors": "ഫോമിൽ ചില പിശകുകൾ അടങ്ങിയിരിക്കുന്നു",
  "containers.Home.introduction": "നിങ്ങളുടെ എൻട്രികൾ എഡിറ്റ് ചെയ്യുന്നതിന് ഇടത് മെനുവിലെ നിർദ്ദിഷ്‌ട ലിങ്കിലേക്ക് പോകുക. ഈ പ്ലഗിന്നിന് ക്രമീകരണങ്ങൾ എഡിറ്റ് ചെയ്യാൻ ശരിയായ മാർഗമില്ല, അത് ഇപ്പോഴും സജീവമായ വികസനത്തിലാണ്.",
  "containers.Home.pluginHeaderDescription": "ശക്തവും മനോഹരവുമായ ഒരു ഇന്റർഫേസിലൂടെ നിങ്ങളുടെ എൻട്രികൾ കൈകാര്യം ചെയ്യുക.",
  "containers.Home.pluginHeaderTitle": "ഉള്ളടക്ക മാനേജർ",
  "containers.List.draft": "ഡ്രാഫ്റ്റ്",
  "containers.List.errorFetchRecords": "പിശക്",
  "containers.List.published": "പ്രസിദ്ധീകരിച്ചു",
  "containers.list.displayedFields": "പ്രദർശിപ്പിച്ച ഫീൽഡുകൾ",
  "containers.list.items": "{സംഖ്യ, ബഹുവചനം, =0 {ഇനങ്ങൾ} ഒന്ന് {ഇനം} മറ്റൊന്ന് {ഇനങ്ങൾ}}",
  "containers.list.table-headers.publishedAt": "സ്റ്റേറ്റ്",
  "containers.ListSettingsView.modal-form.edit-label": "എഡിറ്റ് {fieldName}",
  "containers.SettingPage.add.field": "മറ്റൊരു ഫീൽഡ് ചേർക്കുക",
  "containers.SettingPage.attributes": "ആട്രിബ്യൂട്ടുകളുടെ ഫീൽഡുകൾ",
  "containers.SettingPage.attributes.description": "ആട്രിബ്യൂട്ടുകളുടെ ക്രമം നിർവചിക്കുക",
  "containers.SettingPage.editSettings.description": "ലേഔട്ട് നിർമ്മിക്കുന്നതിന് ഫീൽഡുകൾ വലിച്ചിടുക",
  "containers.SettingPage.editSettings.entry.title": "എൻട്രി ശീർഷകം",
  "containers.SettingPage.editSettings.entry.title.description": "നിങ്ങളുടെ എൻട്രിയുടെ പ്രദർശിപ്പിച്ച ഫീൽഡ് സജ്ജമാക്കുക",
  "containers.SettingPage.editSettings.relation-field.description": "എഡിറ്റ്, ലിസ്റ്റ് കാഴ്‌ചകളിൽ പ്രദർശിപ്പിച്ച ഫീൽഡ് സജ്ജമാക്കുക",
  "containers.SettingPage.editSettings.title": "കാഴ്ച എഡിറ്റ് ചെയ്യുക (ക്രമീകരണങ്ങൾ)",
  "containers.SettingPage.layout": "ലേഔട്ട്",
  "containers.SettingPage.listSettings.description": "ഈ ശേഖരണ തരത്തിനായുള്ള ഓപ്ഷനുകൾ കോൺഫിഗർ ചെയ്യുക",
  "containers.SettingPage.listSettings.title": "ലിസ്റ്റ് കാഴ്ച (ക്രമീകരണങ്ങൾ)",
  "containers.SettingPage.pluginHeaderDescription": "ഈ ശേഖരണ തരത്തിനായുള്ള നിർദ്ദിഷ്ട ക്രമീകരണങ്ങൾ കോൺഫിഗർ ചെയ്യുക",
  "containers.SettingPage.settings": "ക്രമീകരണങ്ങൾ",
  "containers.SettingPage.view": "കാണുക",
  "containers.SettingViewModel.pluginHeader.title": "ഉള്ളടക്ക മാനേജർ - {name}",
  "containers.SettingsPage.Block.contentType.description": "നിർദ്ദിഷ്ട ക്രമീകരണങ്ങൾ കോൺഫിഗർ ചെയ്യുക",
  "containers.SettingsPage.Block.contentType.title": "ശേഖരണ തരങ്ങൾ",
  "containers.SettingsPage.Block.generalSettings.description": "നിങ്ങളുടെ ശേഖരണ തരങ്ങൾക്കായി സ്ഥിരസ്ഥിതി ഓപ്ഷനുകൾ കോൺഫിഗർ ചെയ്യുക",
  "containers.SettingsPage.Block.generalSettings.title": "പൊതുവായത്",
  "containers.SettingsPage.pluginHeaderDescription": "നിങ്ങളുടെ എല്ലാ ശേഖരണ തരങ്ങൾക്കും ഗ്രൂപ്പുകൾക്കുമായി ക്രമീകരണങ്ങൾ കോൺഫിഗർ ചെയ്യുക",
  "containers.SettingsView.list.subtitle": "നിങ്ങളുടെ ശേഖരണ തരങ്ങളുടെയും ഗ്രൂപ്പുകളുടെയും ലേഔട്ടും പ്രദർശനവും കോൺഫിഗർ ചെയ്യുക",
  "containers.SettingsView.list.title": "ഡിസ്‌പ്ലേ കോൺഫിഗറേഷനുകൾ",
  "edit-settings-view.link-to-ctb.components": "ഘടകം എഡിറ്റ് ചെയ്യുക",
  "edit-settings-view.link-to-ctb.content-types": "ഉള്ളടക്ക തരം എഡിറ്റ് ചെയ്യുക",
  "emptyAttributes.button": "ശേഖരണ തരം ബിൽഡറിലേക്ക് പോകുക",
  "emptyAttributes.description": "നിങ്ങളുടെ ശേഖരണ തരത്തിലേക്ക് നിങ്ങളുടെ ആദ്യ ഫീൽഡ് ചേർക്കുക",
  "emptyAttributes.title": "ഇതുവരെ ഫീൽഡുകളൊന്നുമില്ല",
  "error.attribute.key.taken": "ഈ മൂല്യം ഇതിനകം നിലവിലുണ്ട്",
  "error.attribute.sameKeyAndName": "തുല്യനാകാൻ കഴിയില്ല",
  "error.attribute.taken": "ഈ ഫീൽഡ് നാമം ഇതിനകം നിലവിലുണ്ട്",
  "error.contentTypeName.taken": "ഈ പേര് ഇതിനകം നിലവിലുണ്ട്",
  "error.model.fetch": "മോഡലുകൾ കോൺഫിഗർ ചെയ്യുന്നതിനിടയിൽ ഒരു പിശക് സംഭവിച്ചു.",
  "error.record.create": "റെക്കോർഡ് സൃഷ്ടിക്കുന്നതിനിടയിൽ ഒരു പിശക് സംഭവിച്ചു.",
  "error.record.delete": "റെക്കോർഡ് ഇല്ലാതാക്കുമ്പോൾ ഒരു പിശക് സംഭവിച്ചു.",
  "error.record.fetch": "റെക്കോർഡ് ലഭ്യമാക്കുമ്പോൾ ഒരു പിശക് സംഭവിച്ചു.",
  "error.record.update": "റെക്കോർഡ് അപ്ഡേറ്റ് സമയത്ത് ഒരു പിശക് സംഭവിച്ചു.",
  "error.records.count": "എണ്ണം രേഖപ്പെടുത്തുന്ന സമയത്ത് ഒരു പിശക് സംഭവിച്ചു.",
  "error.records.fetch": "രേഖകൾ ലഭ്യമാക്കുന്നതിനിടയിൽ ഒരു പിശക് സംഭവിച്ചു.",
  "error.schema.generation": "സ്‌കീമ ജനറേഷൻ സമയത്ത് ഒരു പിശക് സംഭവിച്ചു.",
  "error.validation.json": "ഇതൊരു JSON അല്ല",
  "error.validation.max": "മൂല്യം വളരെ കൂടുതലാണ്.",
  "error.validation.maxLength": "മൂല്യം വളരെ ദൈർഘ്യമേറിയതാണ്.",
  "error.validation.min": "മൂല്യം വളരെ കുറവാണ്.",
  "error.validation.minLength": "മൂല്യം വളരെ ചെറുതാണ്.",
  "error.validation.minSupMax": "ഉന്നതമാകാൻ കഴിയില്ല",
  "error.validation.regex": "മൂല്യം regex-മായി പൊരുത്തപ്പെടുന്നില്ല.",
  "error.validation.required": "ഈ മൂല്യ ഇൻപുട്ട് ആവശ്യമാണ്.",
  "form.Input.bulkActions": "ബൾക്ക് പ്രവർത്തനങ്ങൾ പ്രവർത്തനക്ഷമമാക്കുക",
  "form.Input.defaultSort": "ഡിഫോൾട്ട് സോർട്ട് ആട്രിബ്യൂട്ട്",
  "form.Input.description": "വിവരണം",
  "form.Input.description.placeholder": "പ്രൊഫൈലിൽ പേര് പ്രദർശിപ്പിക്കുക",
  "form.Input.editable": "എഡിറ്റബിൾ ഫീൽഡ്",
  "form.Input.filters": "ഫിൽട്ടറുകൾ പ്രവർത്തനക്ഷമമാക്കുക",
  "form.Input.label": "ലേബൽ",
  "form.Input.label.inputDescription": "ഈ മൂല്യം പട്ടികയുടെ തലയിൽ പ്രദർശിപ്പിച്ചിരിക്കുന്ന ലേബലിനെ മറികടക്കുന്നു",
  "form.Input.pageEntries": "ഓരോ പേജിനും എൻട്രികൾ",
  "form.Input.pageEntries.inputDescription": "ശ്രദ്ധിക്കുക: ശേഖരണ തരം ക്രമീകരണ പേജിൽ നിങ്ങൾക്ക് ഈ മൂല്യം അസാധുവാക്കാനാകും.",
  "form.Input.placeholder": "പ്ലെയ്‌സ്‌ഹോൾഡർ",
  "form.Input.placeholder.placeholder": "എന്റെ ആകർഷണീയമായ മൂല്യം",
  "form.Input.search": "തിരയൽ പ്രവർത്തനക്ഷമമാക്കുക",
  "form.Input.search.field": "ഈ ഫീൽഡിൽ തിരയൽ പ്രവർത്തനക്ഷമമാക്കുക",
  "form.Input.sort.field": "ഈ ഫീൽഡിൽ അടുക്കുന്നത് പ്രവർത്തനക്ഷമമാക്കുക",
  "form.Input.sort.order": "ഡിഫോൾട്ട് സോർട്ട് ഓർഡർ",
  "form.Input.wysiwyg": "WYSIWYG ആയി പ്രദർശിപ്പിക്കുക",
  "global.displayedFields": "പ്രദർശിപ്പിച്ച ഫീൽഡുകൾ",
  groups,
  "groups.numbered": "ഗ്രൂപ്പുകൾ ({നമ്പർ})",
  "header.name": "ഉള്ളടക്കം",
  "link-to-ctb": "മോഡൽ എഡിറ്റ് ചെയ്യുക",
  models,
  "models.numbered": "ശേഖരണ തരങ്ങൾ ({നമ്പർ})",
  "notification.error.displayedFields": "നിങ്ങൾക്ക് കുറഞ്ഞത് ഒരു പ്രദർശിപ്പിച്ച ഫീൽഡ് ആവശ്യമാണ്",
  "notification.error.relation.fetch": "ബന്ധം കണ്ടെത്തുന്നതിനിടയിൽ ഒരു പിശക് സംഭവിച്ചു.",
  "notification.info.SettingPage.disableSort": "അനുവദനീയമായ സോർട്ടിംഗിനൊപ്പം നിങ്ങൾക്ക് ഒരു ആട്രിബ്യൂട്ട് ഉണ്ടായിരിക്കണം",
  "notification.info.minimumFields": "നിങ്ങൾക്ക് കുറഞ്ഞത് ഒരു ഫീൽഡെങ്കിലും പ്രദർശിപ്പിക്കേണ്ടതുണ്ട്",
  "notification.upload.error": "നിങ്ങളുടെ ഫയലുകൾ അപ്‌ലോഡ് ചെയ്യുമ്പോൾ ഒരു പിശക് സംഭവിച്ചു",
  pageNotFound,
  "pages.ListView.header-subtitle": "{സംഖ്യ, ബഹുവചനം, =0 {# എൻട്രികൾ} ഒന്ന് {# എൻട്രി} മറ്റ് {# എൻട്രികൾ}} കണ്ടെത്തി",
  "pages.NoContentType.button": "നിങ്ങളുടെ ആദ്യ ഉള്ളടക്ക-തരം സൃഷ്ടിക്കുക",
  "pages.NoContentType.text": "നിങ്ങൾക്ക് ഇതുവരെ ഒരു ഉള്ളടക്കവും ഇല്ല, നിങ്ങളുടെ ആദ്യ ഉള്ളടക്ക-തരം സൃഷ്ടിക്കാൻ ഞങ്ങൾ ശുപാർശ ചെയ്യുന്നു.",
  "permissions.not-allowed.create": "ഒരു പ്രമാണം സൃഷ്ടിക്കാൻ നിങ്ങൾക്ക് അനുവാദമില്ല",
  "permissions.not-allowed.update": "നിങ്ങൾക്ക് ഈ പ്രമാണം കാണാൻ അനുവാദമില്ല",
  "plugin.description.long": "നിങ്ങളുടെ ഡാറ്റാബേസിലെ ഡാറ്റ കാണാനും എഡിറ്റ് ചെയ്യാനും ഇല്ലാതാക്കാനുമുള്ള ദ്രുത മാർഗം.",
  "plugin.description.short": "നിങ്ങളുടെ ഡാറ്റാബേസിലെ ഡാറ്റ കാണാനും എഡിറ്റ് ചെയ്യാനും ഇല്ലാതാക്കാനുമുള്ള ദ്രുത മാർഗം.",
  "popover.display-relations.label": "പ്രദർശന ബന്ധങ്ങൾ",
  "success.record.delete": "ഇല്ലാതാക്കപ്പെട്ടു",
  "success.record.publish": "പ്രസിദ്ധീകരിച്ചത്",
  "success.record.save": "സംരക്ഷിച്ചു",
  "success.record.unpublish": "പ്രസിദ്ധീകരിക്കാത്തത്",
  "utils.data-loaded": "{സംഖ്യ, ബഹുവചനം, =1 {എൻട്രി ഉണ്ട്} മറ്റ് {എൻട്രികൾ ഉണ്ട്}} വിജയകരമായി ലോഡ് ചെയ്തു",
  "apiError. ഈ ആട്രിബ്യൂട്ട് അദ്വിതീയമായിരിക്കണം": "{ഫീൽഡ്} അദ്വിതീയമായിരിക്കണം",
  "popUpWarning.warning.publish-question": "നിങ്ങൾക്കിത് ഇപ്പോഴും പ്രസിദ്ധീകരിക്കാൻ താൽപ്പര്യമുണ്ടോ?",
  "popUpwarning.warning.has-draft-relations.button-confirm": "അതെ, പ്രസിദ്ധീകരിക്കുക",
  "popUpwarning.warning.has-draft-relations.message": "<b>{count, plural, =0 {നിങ്ങളുടെ ഉള്ളടക്ക ബന്ധങ്ങളിൽ} ഒന്ന് {നിങ്ങളുടെ ഉള്ളടക്ക ബന്ധങ്ങളിൽ} മറ്റൊന്നാണ് {നിങ്ങളുടെ ഉള്ളടക്ക ബന്ധങ്ങളിൽ} are}}</b> ഇതുവരെ പ്രസിദ്ധീകരിച്ചിട്ടില്ല.<br></br>ഇത് നിങ്ങളുടെ പ്രോജക്റ്റിൽ തകർന്ന ലിങ്കുകളും പിശകുകളും സൃഷ്ടിച്ചേക്കാം."
};
export {
  ml as default,
  groups,
  models,
  pageNotFound
};
//# sourceMappingURL=ml.json-A4QAJBBK.js.map
