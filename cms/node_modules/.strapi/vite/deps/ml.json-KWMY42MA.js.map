{"version": 3, "sources": ["../../../@strapi/admin/dist/admin/admin/src/translations/ml.json.mjs"], "sourcesContent": ["var Analytics = \"അനലിറ്റിക്സ്\";\nvar Documentation = \"പ്രമാണീകരണം\";\nvar Email = \"ഇമെയിൽ\";\nvar Password = \"പാസ്വേഡ്\";\nvar Provider = \"ദാതാവ്\";\nvar ResetPasswordToken = \"പാസ്‌വേഡ് ടോക്കൺ റീസെറ്റ് ചെയ്യുക\";\nvar Role = \"പങ്ക്\";\nvar Username = \"ഉപയോക്തൃനാമം\";\nvar Users = \"ഉപയോക്താക്കൾ\";\nvar anErrorOccured = \"ശ്ശോ! എന്തോ കുഴപ്പം സംഭവിച്ചു. ദയവായി, വീണ്ടും ശ്രമിക്കുക.\";\nvar clearLabel = \"വ്യക്തം\";\nvar or = \"അഥവാ\";\nvar skipToContent = \"ഉള്ളടക്കത്തിലേക്ക് പോകുക\";\nvar submit = \"സമർപ്പിക്കുക\";\nvar ml = {\n    Analytics: Analytics,\n    \"Auth.components.Oops.text\": \"നിങ്ങളുടെ അക്കൗണ്ട് താൽക്കാലികമായി നിർത്തിവച്ചു.\",\n    \"Auth.components.Oops.text.admin\": \"ഇതൊരു തെറ്റാണെങ്കിൽ, നിങ്ങളുടെ അഡ്മിനിസ്ട്രേറ്ററെ ബന്ധപ്പെടുക.\",\n    \"Auth.components.Oops.title\": \"ശ്ശോ...\",\n    \"Auth.form.button.forgot-password\": \"ഇമെയിൽ അയയ്ക്കുക\",\n    \"Auth.form.button.go-home\": \"വീട്ടിലേക്ക് തിരിച്ച് പോവുക\",\n    \"Auth.form.button.login\": \"ലോഗിൻ\",\n    \"Auth.form.button.login.providers.error\": \"തിരഞ്ഞെടുത്ത ദാതാവിലൂടെ നിങ്ങളെ ബന്ധിപ്പിക്കാൻ ഞങ്ങൾക്ക് കഴിയില്ല.\",\n    \"Auth.form.button.login.strapi\": \"സ്ട്രാപ്പി വഴി ലോഗിൻ ചെയ്യുക\",\n    \"Auth.form.button.password-recovery\": \"പാസ്‌വേഡ് വീണ്ടെടുക്കൽ\",\n    \"Auth.form.button.register\": \"നമുക്ക് തുടങ്ങാം\",\n    \"Auth.form.confirmPassword.label\": \"സ്ഥിരീകരണ പാസ്‌വേഡ്\",\n    \"Auth.form.currentPassword.label\": \"ഇപ്പോഴത്തെ പാസ്സ്വേർഡ്\",\n    \"Auth.form.email.label\": \"ഇമെയിൽ\",\n    \"Auth.form.email.placeholder\": \"ഉദാ. <EMAIL>\",\n    \"Auth.form.error.blocked\": \"നിങ്ങളുടെ അക്കൗണ്ട് അഡ്‌മിനിസ്‌ട്രേറ്റർ ബ്ലോക്ക് ചെയ്‌തിരിക്കുന്നു.\",\n    \"Auth.form.error.code.provide\": \"തെറ്റായ കോഡ് നൽകിയിട്ടുണ്ട്.\",\n    \"Auth.form.error.confirmed\": \"നിങ്ങളുടെ അക്കൗണ്ട് ഇമെയിൽ സ്ഥിരീകരിച്ചിട്ടില്ല.\",\n    \"Auth.form.error.email.invalid\": \"ഈ ഇമെയിൽ അസാധുവാണ്.\",\n    \"Auth.form.error.email.provide\": \"ദയവായി നിങ്ങളുടെ ഉപയോക്തൃനാമം അല്ലെങ്കിൽ ഇമെയിൽ നൽകുക.\",\n    \"Auth.form.error.email.taken\": \"ഇമെയിൽ ഇതിനകം എടുത്തിട്ടുണ്ട്.\",\n    \"Auth.form.error.invalid\": \"ഐഡന്റിഫയർ അല്ലെങ്കിൽ പാസ്‌വേഡ് അസാധുവാണ്.\",\n    \"Auth.form.error.params.provide\": \"തെറ്റായ പാരാമുകൾ നൽകിയിരിക്കുന്നു.\",\n    \"Auth.form.error.password.format\": \"നിങ്ങളുടെ പാസ്‌വേഡിൽ മൂന്ന് തവണയിൽ കൂടുതൽ `$` ചിഹ്നം അടങ്ങിയിരിക്കരുത്.\",\n    \"Auth.form.error.password.local\": \"ഈ ഉപയോക്താവ് ഒരിക്കലും ഒരു പ്രാദേശിക പാസ്‌വേഡ് സജ്ജീകരിച്ചിട്ടില്ല, അക്കൗണ്ട് സൃഷ്‌ടിക്കുമ്പോൾ ഉപയോഗിക്കുന്ന ദാതാവ് വഴി ലോഗിൻ ചെയ്യുക.\",\n    \"Auth.form.error.password.matching\": \"പാസ്വേഡുകൾ പൊരുത്തപ്പെടുന്നില്ല.\",\n    \"Auth.form.error.password.provide\": \"ദയവായി നിങ്ങളുടെ പാസ്‌വേഡ് നൽകുക.\",\n    \"Auth.form.error.ratelimit\": \"വളരെയധികം ശ്രമങ്ങൾ, ഒരു മിനിറ്റിനുള്ളിൽ വീണ്ടും ശ്രമിക്കുക.\",\n    \"Auth.form.error.user.not-exist\": \"ഈ ഇമെയിൽ നിലവിലില്ല.\",\n    \"Auth.form.error.username.taken\": \"ഉപയോക്തൃനാമം ഇതിനകം എടുത്തതാണ്.\",\n    \"Auth.form.firstname.label\": \"പേരിന്റെ ആദ്യഭാഗം\",\n    \"Auth.form.firstname.placeholder\": \"ഉദാ. കൈ\",\n    \"Auth.form.forgot-password.email.label\": \"നിങ്ങളുടെ ഇമെയിൽ നൽകുക\",\n    \"Auth.form.forgot-password.email.label.success\": \"ഇമെയിൽ വിജയകരമായി അയച്ചു\",\n    \"Auth.form.lastname.label\": \"പേരിന്റെ അവസാന ഭാഗം\",\n    \"Auth.form.lastname.placeholder\": \"ഉദാ. ഡോ\",\n    \"Auth.form.password.hide-password\": \"രഹസ്യവാക്ക് മറയ്ക്കുക\",\n    \"Auth.form.password.hint\": \"കുറഞ്ഞത് 8 പ്രതീകങ്ങൾ, 1 വലിയക്ഷരം, 1 ചെറിയക്ഷരം, 1 നമ്പർ എന്നിവ ഉണ്ടായിരിക്കണം\",\n    \"Auth.form.password.show-password\": \"പാസ്‌വേഡ് കാണിക്കുക\",\n    \"Auth.form.register.news.label\": \"പുതിയ ഫീച്ചറുകളെക്കുറിച്ചും വരാനിരിക്കുന്ന മെച്ചപ്പെടുത്തലുകളെക്കുറിച്ചും എന്നെ അപ്ഡേറ്റ് ചെയ്യുക (ഇത് ചെയ്യുന്നതിലൂടെ നിങ്ങൾ {നിബന്ധനകളും} {നയവും} അംഗീകരിക്കുന്നു).\",\n    \"Auth.form.register.subtitle\": \"ക്രെഡൻഷ്യലുകൾ സ്ട്രാപിയിൽ ആധികാരികമാക്കാൻ മാത്രമേ ഉപയോഗിക്കൂ. സംരക്ഷിച്ച എല്ലാ ഡാറ്റയും നിങ്ങളുടെ ഡാറ്റാബേസിൽ സംഭരിക്കും.\",\n    \"Auth.form.rememberMe.label\": \"എന്നെ ഓർമ്മിക്കുക\",\n    \"Auth.form.username.label\": \"ഉപയോക്തൃനാമം\",\n    \"Auth.form.username.placeholder\": \"ഉദാ. കൈ_ഡോ\",\n    \"Auth.form.welcome.subtitle\": \"നിങ്ങളുടെ Strapi അക്കൗണ്ടിലേക്ക് ലോഗിൻ ചെയ്യുക\",\n    \"Auth.form.welcome.title\": \"സ്ട്രാപിയിലേക്ക് സ്വാഗതം!\",\n    \"Auth.link.forgot-password\": \"നിങ്ങളുടെ രഹസ്യ വാക്ക് മറന്നോ?\",\n    \"Auth.link.ready\": \"സൈൻ ഇൻ ചെയ്യാൻ തയ്യാറാണോ?\",\n    \"Auth.link.signin\": \"സൈൻ ഇൻ\",\n    \"Auth.link.signin.account\": \"ഇതിനകം ഒരു അക്കൗണ്ട് ഉണ്ടോ?\",\n    \"Auth.login.sso.divider\": \"അല്ലെങ്കിൽ ഇതിൽ ലോഗിൻ ചെയ്യുക\",\n    \"Auth.login.sso.loading\": \"ദാതാക്കളെ ലോഡുചെയ്യുന്നു...\",\n    \"Auth.login.sso.subtitle\": \"SSO വഴി നിങ്ങളുടെ അക്കൗണ്ടിലേക്ക് ലോഗിൻ ചെയ്യുക\",\n    \"Auth.privacy-policy-agreement.policy\": \"സ്വകാര്യതാ നയം\",\n    \"Auth.privacy-policy-agreement.terms\": \"നിബന്ധനകൾ\",\n    \"Auth.reset-password.title\": \"പാസ്‌വേഡ് പുനഃസജ്ജമാക്കുക\",\n    \"Content Manager\": \"ഉള്ളടക്ക മാനേജർ\",\n    \"Content Type Builder\": \"ഉള്ളടക്ക-തരം ബിൽഡർ\",\n    Documentation: Documentation,\n    Email: Email,\n    \"Files Upload\": \"ഫയലുകൾ അപ്ലോഡ്\",\n    \"HomePage.head.title\": \"ഹോംപേജ്\",\n    \"HomePage.roadmap\": \"ഞങ്ങളുടെ റോഡ്മാപ്പ് കാണുക\",\n    \"HomePage.welcome.congrats\": \"അഭിനന്ദനങ്ങൾ!\",\n    \"HomePage.welcome.congrats.content\": \"നിങ്ങൾ ആദ്യത്തെ അഡ്മിനിസ്ട്രേറ്ററായി ലോഗിൻ ചെയ്തു. സ്ട്രാപ്പി നൽകുന്ന ശക്തമായ സവിശേഷതകൾ കണ്ടെത്താൻ,\",\n    \"HomePage.welcome.congrats.content.bold\": \"നിങ്ങളുടെ ആദ്യ ശേഖരം-തരം സൃഷ്ടിക്കാൻ ഞങ്ങൾ ശുപാർശ ചെയ്യുന്നു.\",\n    \"Media Library\": \"മീഡിയ ലൈബ്രറി\",\n    \"New entry\": \"പുതിയ എൻട്രി\",\n    Password: Password,\n    Provider: Provider,\n    ResetPasswordToken: ResetPasswordToken,\n    Role: Role,\n    \"Roles & Permissions\": \"റോളുകളും അനുമതികളും\",\n    \"Roles.ListPage.notification.delete-all-not-allowed\": \"ചില റോളുകൾ ഉപയോക്താക്കളുമായി ബന്ധപ്പെട്ടിരിക്കുന്നതിനാൽ അവ ഇല്ലാതാക്കാൻ കഴിഞ്ഞില്ല\",\n    \"Roles.ListPage.notification.delete-not-allowed\": \"ഉപയോക്താക്കളുമായി ബന്ധപ്പെടുത്തിയാൽ ഒരു റോൾ ഇല്ലാതാക്കാൻ കഴിയില്ല\",\n    \"Roles.RoleRow.select-all\": \"ബൾക്ക് പ്രവർത്തനങ്ങൾക്കായി {name} തിരഞ്ഞെടുക്കുക\",\n    \"Roles.RoleRow.user-count\": \"{സംഖ്യ, ബഹുവചനം, =0 {# ഉപയോക്താവ്} ഒരാൾ {# ഉപയോക്താവ്} മറ്റ് {# ഉപയോക്താക്കൾ}}\",\n    \"Roles.components.List.empty.withSearch\": \"തിരയലുമായി ബന്ധപ്പെട്ട ഒരു റോളും ഇല്ല ({തിരയൽ})...\",\n    \"Settings.PageTitle\": \"ക്രമീകരണങ്ങൾ - {name}\",\n    \"Settings.apiTokens.addFirstToken\": \"നിങ്ങളുടെ ആദ്യ API ടോക്കൺ ചേർക്കുക\",\n    \"Settings.apiTokens.addNewToken\": \"പുതിയ API ടോക്കൺ ചേർക്കുക\",\n    \"Settings.tokens.copy.editMessage\": \"സുരക്ഷാ കാരണങ്ങളാൽ, നിങ്ങൾക്ക് ഒരു തവണ മാത്രമേ ടോക്കൺ കാണാനാകൂ.\",\n    \"Settings.tokens.copy.editTitle\": \"ഈ ടോക്കൺ ഇനി ആക്സസ് ചെയ്യാനാകില്ല.\",\n    \"Settings.tokens.copy.lastWarning\": \"ഈ ടോക്കൺ പകർത്തുന്നത് ഉറപ്പാക്കുക, നിങ്ങൾക്ക് ഇത് വീണ്ടും കാണാൻ കഴിയില്ല!\",\n    \"Settings.apiTokens.create\": \"പുതിയ API ടോക്കൺ സൃഷ്‌ടിക്കുക\",\n    \"Settings.apiTokens.description\": \"API ഉപയോഗിക്കുന്നതിന് ജനറേറ്റ് ചെയ്ത ടോക്കണുകളുടെ ലിസ്റ്റ്\",\n    \"Settings.apiTokens.emptyStateLayout\": \"നിങ്ങൾക്ക് ഇതുവരെ ഉള്ളടക്കമൊന്നുമില്ല...\",\n    \"Settings.tokens.notification.copied\": \"ടോക്കൺ ക്ലിപ്പ്ബോർഡിലേക്ക് പകർത്തി.\",\n    \"Settings.apiTokens.title\": \"API ടോക്കണുകൾ\",\n    \"Settings.tokens.types.full-access\": \"പൂർണ്ണമായ പ്രവേശനം\",\n    \"Settings.tokens.types.read-only\": \"വായിക്കാൻ മാത്രം\",\n    \"Settings.application.description\": \"അഡ്മിനിസ്ട്രേഷൻ പാനലിന്റെ ആഗോള വിവരങ്ങൾ\",\n    \"Settings.application.edition-title\": \"നിലവിലെ പതിപ്പ്\",\n    \"Settings.application.get-help\": \"സഹായം തേടു\",\n    \"Settings.application.link-pricing\": \"എല്ലാ വിലനിർണ്ണയ പ്ലാനുകളും കാണുക\",\n    \"Settings.application.link-upgrade\": \"നിങ്ങളുടെ അഡ്മിൻ പാനൽ നവീകരിക്കുക\",\n    \"Settings.application.node-version\": \"നോഡ് പതിപ്പ്\",\n    \"Settings.application.strapi-version\": \"സ്ട്രാപ്പി പതിപ്പ്\",\n    \"Settings.application.strapiVersion\": \"സ്ട്രാപ്പി പതിപ്പ്\",\n    \"Settings.application.title\": \"അവലോകനം\",\n    \"Settings.error\": \"പിശക്\",\n    \"Settings.global\": \"ആഗോള ക്രമീകരണങ്ങൾ\",\n    \"Settings.permissions\": \"അഡ്‌മിനിസ്‌ട്രേഷൻ പാനൽ\",\n    \"Settings.permissions.category\": \"{category} എന്നതിനായുള്ള അനുമതി ക്രമീകരണങ്ങൾ\",\n    \"Settings.permissions.category.plugins\": \"{category} പ്ലഗിനിനായുള്ള അനുമതി ക്രമീകരണങ്ങൾ\",\n    \"Settings.permissions.conditions.anytime\": \"എപ്പോൾ വേണമെങ്കിലും\",\n    \"Settings.permissions.conditions.apply\": \"അപേക്ഷിക്കുക\",\n    \"Settings.permissions.conditions.can\": \"കഴിയും\",\n    \"Settings.permissions.conditions.conditions\": \"നിബന്ധനകൾ നിർവചിക്കുക\",\n    \"Settings.permissions.conditions.links\": \"ലിങ്കുകൾ\",\n    \"Settings.permissions.conditions.no-actions\": \"അവയിലെ വ്യവസ്ഥകൾ നിർവചിക്കുന്നതിന് മുമ്പ് നിങ്ങൾ ആദ്യം പ്രവർത്തനങ്ങൾ (സൃഷ്ടിക്കുക, വായിക്കുക, അപ്ഡേറ്റ് ചെയ്യുക, ...) തിരഞ്ഞെടുക്കേണ്ടതുണ്ട്.\",\n    \"Settings.permissions.conditions.none-selected\": \"എപ്പോൾ വേണമെങ്കിലും\",\n    \"Settings.permissions.conditions.or\": \"അഥവാ\",\n    \"Settings.permissions.conditions.when\": \"എപ്പോൾ\",\n    \"Settings.permissions.select-all-by-permission\": \"എല്ലാ {label} അനുമതികളും തിരഞ്ഞെടുക്കുക\",\n    \"Settings.permissions.select-by-permission\": \"{label} അനുമതി തിരഞ്ഞെടുക്കുക\",\n    \"Settings.permissions.users.create\": \"പുതിയ ഉപയോക്താവിനെ ക്ഷണിക്കുക\",\n    \"Settings.permissions.users.email\": \"ഇമെയിൽ\",\n    \"Settings.permissions.users.firstname\": \"പേരിന്റെ ആദ്യഭാഗം\",\n    \"Settings.permissions.users.lastname\": \"പേരിന്റെ അവസാന ഭാഗം\",\n    \"Settings.permissions.users.form.sso\": \"SSO-മായി ബന്ധിപ്പിക്കുക\",\n    \"Settings.permissions.users.form.sso.description\": \"പ്രാപ്തമാക്കുമ്പോൾ (ഓൺ), ഉപയോക്താക്കൾക്ക് SSO വഴി ലോഗിൻ ചെയ്യാൻ കഴിയും\",\n    \"Settings.permissions.users.listview.header.subtitle\": \"Strapi അഡ്മിൻ പാനലിലേക്ക് ആക്‌സസ് ഉള്ള എല്ലാ ഉപയോക്താക്കളും\",\n    \"Settings.permissions.users.tabs.label\": \"ടാബ് അനുമതികൾ\",\n    \"Settings.profile.form.notify.data.loaded\": \"നിങ്ങളുടെ പ്രൊഫൈൽ ഡാറ്റ ലോഡ് ചെയ്തു\",\n    \"Settings.profile.form.section.experience.clear.select\": \"തിരഞ്ഞെടുത്ത ഇന്റർഫേസ് ഭാഷ മായ്ക്കുക\",\n    \"Settings.profile.form.section.experience.here\": \"ഇവിടെ\",\n    \"Settings.profile.form.section.experience.interfaceLanguage\": \"ഇന്റർഫേസ് ഭാഷ\",\n    \"Settings.profile.form.section.experience.interfaceLanguage.hint\": \"ഇത് തിരഞ്ഞെടുത്ത ഭാഷയിൽ നിങ്ങളുടെ സ്വന്തം ഇന്റർഫേസ് മാത്രമേ പ്രദർശിപ്പിക്കൂ.\",\n    \"Settings.profile.form.section.experience.interfaceLanguageHelp\": \"മുൻഗണന മാറ്റങ്ങൾ നിങ്ങൾക്ക് മാത്രമേ ബാധകമാകൂ. കൂടുതൽ വിവരങ്ങൾ {ഇവിടെ ലഭ്യമാണ്.\",\n    \"Settings.profile.form.section.experience.mode.label\": \"ഇന്റർഫേസ് മോഡ്\",\n    \"Settings.profile.form.section.experience.mode.hint\": \"തിരഞ്ഞെടുത്ത മോഡിൽ നിങ്ങളുടെ ഇന്റർഫേസ് പ്രദർശിപ്പിക്കുന്നു.\",\n    \"Settings.profile.form.section.experience.mode.option-label\": \"{name mode}\",\n    \"Settings.profile.form.section.experience.title\": \"അനുഭവം\",\n    \"Settings.profile.form.section.head.title\": \"ഉപയോക്തൃ പ്രൊഫൈൽ\",\n    \"Settings.profile.form.section.profile.page.title\": \"പ്രൊഫൈൽ പേജ്\",\n    \"Settings.roles.create.description\": \"റോളിന് നൽകിയിരിക്കുന്ന അവകാശങ്ങൾ നിർവ്വചിക്കുക\",\n    \"Settings.roles.create.title\": \"ഒരു റോൾ സൃഷ്ടിക്കുക\",\n    \"Settings.roles.created\": \"റോൾ സൃഷ്ടിച്ചു\",\n    \"Settings.roles.edit.title\": \"ഒരു റോൾ എഡിറ്റ് ചെയ്യുക\",\n    \"Settings.roles.form.button.users-with-role\": \"{സംഖ്യ, ബഹുവചനം, = 0 {# ഉപയോക്താക്കൾ} ഈ റോളുള്ള ഒരു {# ഉപയോക്താവ്} മറ്റൊരാൾ {# ഉപയോക്താക്കൾ}\",\n    \"Settings.roles.form.created\": \"സൃഷ്ടിച്ചു\",\n    \"Settings.roles.form.description\": \"റോളിന്റെ പേരും വിവരണവും\",\n    \"Settings.roles.form.permission.property-label\": \"ബെൽ ലേബൽ അനുമതികൾ\",\n    \"Settings.roles.form.permissions.attributesPermissions\": \"ഫീൽഡ് അനുമതികൾ\",\n    \"Settings.roles.form.permissions.create\": \"സൃഷ്ടിക്കുക\",\n    \"Settings.roles.form.permissions.delete\": \"ഇല്ലാതാക്കുക\",\n    \"Settings.roles.form.permissions.publish\": \"പ്രസിദ്ധീകരിക്കുക\",\n    \"Settings.roles.form.permissions.read\": \"വായിക്കുക\",\n    \"Settings.roles.form.permissions.update\": \"അപ്‌ഡേറ്റ്\",\n    \"Settings.roles.list.button.add\": \"പുതിയ റോൾ ചേർക്കുക\",\n    \"Settings.roles.list.description\": \"റോളുകളുടെ പട്ടിക\",\n    \"Settings.roles.title.singular\": \"പങ്ക്\",\n    \"Settings.sso.description\": \"സിംഗിൾ സൈൻ-ഓൺ ഫീച്ചറിനായുള്ള ക്രമീകരണങ്ങൾ കോൺഫിഗർ ചെയ്യുക.\",\n    \"Settings.sso.form.defaultRole.description\": \"ഇത് തിരഞ്ഞെടുത്ത റോളിലേക്ക് പുതിയ അംഗീകൃത ഉപയോക്താവിനെ അറ്റാച്ചുചെയ്യും\",\n    \"Settings.sso.form.defaultRole.description-not-allowed\": \"അഡ്മിൻ റോളുകൾ വായിക്കാൻ നിങ്ങൾക്ക് അനുമതി ആവശ്യമാണ്\",\n    \"Settings.sso.form.defaultRole.label\": \"ഡിഫോൾട്ട് റോൾ\",\n    \"Settings.sso.form.registration.description\": \"അക്കൗണ്ട് നിലവിലില്ലെങ്കിൽ SSO ലോഗിൻ ചെയ്യുമ്പോൾ പുതിയ ഉപയോക്താവിനെ സൃഷ്ടിക്കുക\",\n    \"Settings.sso.form.registration.label\": \"ഓട്ടോ-രജിസ്ട്രേഷൻ\",\n    \"Settings.sso.title\": \"ഒറ്റ സൈൻ-ഓൺ\",\n    \"Settings.webhooks.create\": \"ഒരു വെബ്ഹുക്ക് സൃഷ്ടിക്കുക\",\n    \"Settings.webhooks.create.header\": \"പുതിയ തലക്കെട്ട് സൃഷ്‌ടിക്കുക\",\n    \"Settings.webhooks.created\": \"Webhook സൃഷ്ടിച്ചു\",\n    \"Settings.webhooks.event.publish-tooltip\": \"ഡ്രാഫ്റ്റ്/പബ്ലിഷ് സിസ്റ്റം പ്രവർത്തനക്ഷമമാക്കിയിട്ടുള്ള ഉള്ളടക്കങ്ങൾക്ക് മാത്രമേ ഈ ഇവന്റ് നിലനിൽക്കൂ\",\n    \"Settings.webhooks.events.create\": \"സൃഷ്ടിക്കുക\",\n    \"Settings.webhooks.events.update\": \"അപ്‌ഡേറ്റ്\",\n    \"Settings.webhooks.form.events\": \"സംഭവങ്ങൾ\",\n    \"Settings.webhooks.form.headers\": \"ഹെഡറുകൾ\",\n    \"Settings.webhooks.form.url\": \"Url\",\n    \"Settings.webhooks.headers.remove\": \"തലക്കെട്ട് വരി {നമ്പർ} നീക്കം ചെയ്യുക\",\n    \"Settings.webhooks.key\": \"കീ\",\n    \"Settings.webhooks.list.button.add\": \"പുതിയ വെബ്ഹുക്ക് സൃഷ്‌ടിക്കുക\",\n    \"Settings.webhooks.list.description\": \"POST മാറ്റങ്ങളുടെ അറിയിപ്പുകൾ നേടുക\",\n    \"Settings.webhooks.list.empty.description\": \"വെബ്‌ഹുക്കുകളൊന്നും കണ്ടെത്തിയില്ല\",\n    \"Settings.webhooks.list.empty.link\": \"ഞങ്ങളുടെ ഡോക്യുമെന്റേഷൻ കാണുക\",\n    \"Settings.webhooks.list.empty.title\": \"ഇതുവരെ വെബ്‌ഹുക്കുകളൊന്നുമില്ല\",\n    \"Settings.webhooks.list.th.actions\": \"പ്രവർത്തനങ്ങൾ\",\n    \"Settings.webhooks.list.th.status\": \"പദവി\",\n    \"Settings.webhooks.singular\": \"വെബ്ഹുക്ക്\",\n    \"Settings.webhooks.title\": \"വെബ്ബുക്കുകൾ\",\n    \"Settings.webhooks.to.delete\": \"{webhooksToDeleteLength, ബഹുവചനം, ഒരു {# അസറ്റ്} മറ്റ് {# അസറ്റുകൾ}} തിരഞ്ഞെടുത്തു\",\n    \"Settings.webhooks.trigger\": \"ട്രിഗർ\",\n    \"Settings.webhooks.trigger.cancel\": \"ട്രിഗർ റദ്ദാക്കുക\",\n    \"Settings.webhooks.trigger.pending\": \"തീർച്ചപ്പെടുത്തിയിട്ടില്ല...\",\n    \"Settings.webhooks.trigger.save\": \"ട്രിഗർ ചെയ്യാൻ ദയവായി സംരക്ഷിക്കുക\",\n    \"Settings.webhooks.trigger.success\": \"വിജയം!\",\n    \"Settings.webhooks.trigger.success.label\": \"ട്രിഗർ വിജയിച്ചു\",\n    \"Settings.webhooks.trigger.test\": \"ടെസ്റ്റ്-ട്രിഗർ\",\n    \"Settings.webhooks.trigger.title\": \"ട്രിഗറിന് മുമ്പ് സംരക്ഷിക്കുക\",\n    \"Settings.webhooks.value\": \"മൂല്യം\",\n    \"Usecase.back-end\": \"ബാക്ക്-എൻഡ് ഡെവലപ്പർ\",\n    \"Usecase.button.skip\": \"ഈ ചോദ്യം ഒഴിവാക്കുക\",\n    \"Usecase.content-creator\": \"ഉള്ളടക്ക സ്രഷ്ടാവ്\",\n    \"Usecase.front-end\": \"ഫ്രണ്ട് എൻഡ് ഡെവലപ്പർ\",\n    \"Usecase.full-stack\": \"ഫുൾ-സ്റ്റാക്ക് ഡെവലപ്പർ\",\n    \"Usecase.input.work-type\": \"ഏത് തരത്തിലുള്ള ജോലിയാണ് നിങ്ങൾ ചെയ്യുന്നത്?\",\n    \"Usecase.notification.success.project-created\": \"പ്രോജക്റ്റ് വിജയകരമായി സൃഷ്ടിച്ചു\",\n    \"Usecase.other\": \"മറ്റുള്ളവ\",\n    \"Usecase.title\": \"നിങ്ങളെക്കുറിച്ച് കുറച്ചുകൂടി ഞങ്ങളോട് പറയുക\",\n    Username: Username,\n    Users: Users,\n    \"Users & Permissions\": \"ഉപയോക്താക്കളും അനുമതികളും\",\n    \"Users.components.List.empty\": \"ഉപയോക്താക്കൾ ഇല്ല...\",\n    \"Users.components.List.empty.withFilters\": \"പ്രയോഗിച്ച ഫിൽട്ടറുകൾ ഉള്ള ഉപയോക്താക്കളൊന്നും ഇല്ല...\",\n    \"Users.components.List.empty.withSearch\": \"തിരയൽ ({തിരയൽ}) മായി ബന്ധപ്പെട്ട ഉപയോക്താക്കളൊന്നും ഇല്ല...\",\n    \"admin.pages.MarketPlacePage.head\": \"മാർക്കറ്റ്പ്ലേസ് - പ്ലഗിനുകൾ\",\n    \"admin.pages.MarketPlacePage.offline.title\": \"നിങ്ങൾ ഓഫ്‌ലൈനാണ്\",\n    \"admin.pages.MarketPlacePage.offline.subtitle\": \"സ്ട്രാപ്പി മാർക്കറ്റ് ആക്സസ് ചെയ്യുന്നതിന് നിങ്ങൾ ഇന്റർനെറ്റിലേക്ക് കണക്റ്റുചെയ്യേണ്ടതുണ്ട്.\",\n    \"admin.pages.MarketPlacePage.plugin.copy\": \"ഇൻസ്റ്റാൾ കമാൻഡ് പകർത്തുക\",\n    \"admin.pages.MarketPlacePage.plugin.copy.success\": \"നിങ്ങളുടെ ടെർമിനലിൽ ഒട്ടിക്കാൻ തയ്യാറായ കമാൻഡ് ഇൻസ്റ്റാൾ ചെയ്യുക\",\n    \"admin.pages.MarketPlacePage.plugin.info\": \"കൂടുതലറിയുക\",\n    \"admin.pages.MarketPlacePage.plugin.info.label\": \"{pluginName}-നെ കുറിച്ച് കൂടുതലറിയുക\",\n    \"admin.pages.MarketPlacePage.plugin.info.text\": \"കൂടുതലറിയുക\",\n    \"admin.pages.MarketPlacePage.plugin.installed\": \"ഇൻസ്റ്റാൾ ചെയ്തു\",\n    \"admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi\": \"സ്ട്രാപ്പി നിർമ്മിച്ചത്\",\n    \"admin.pages.MarketPlacePage.plugin.tooltip.verified\": \"പ്ലഗിൻ സ്ഥിരീകരിച്ചത് Strapi\",\n    \"admin.pages.MarketPlacePage.search.clear\": \"പ്ലഗിൻ തിരയൽ മായ്ക്കുക\",\n    \"admin.pages.MarketPlacePage.search.empty\": \"\\\"{target}\\\" എന്നതിന് ഫലമില്ല\",\n    \"admin.pages.MarketPlacePage.search.placeholder\": \"ഒരു പ്ലഗിൻ തിരയുക\",\n    \"admin.pages.MarketPlacePage.submit.plugin.link\": \"നിങ്ങളുടെ പ്ലഗിൻ സമർപ്പിക്കുക\",\n    \"admin.pages.MarketPlacePage.subtitle\": \"സ്ട്രാപിയിൽ നിന്ന് കൂടുതൽ നേടൂ\",\n    \"admin.pages.MarketPlacePage.missingPlugin.title\": \"ഒരു പ്ലഗിൻ നഷ്ടമായോ?\",\n    \"admin.pages.MarketPlacePage.missingPlugin.description\": \"നിങ്ങൾ എന്ത് പ്ലഗിനാണ് തിരയുന്നതെന്ന് ഞങ്ങളോട് പറയൂ, ഞങ്ങളുടെ കമ്മ്യൂണിറ്റി പ്ലഗിൻ ഡെവലപ്പർമാർക്ക് പ്രചോദനം ലഭിക്കണമെങ്കിൽ ഞങ്ങൾ അവരെ അറിയിക്കും!\",\n    anErrorOccured: anErrorOccured,\n    \"app.component.CopyToClipboard.label\": \"ക്ലിപ്പ്ബോർഡിലേക്ക് പകർത്തുക\",\n    \"app.component.search.label\": \"{ലക്ഷ്യത്തിനായി} തിരയുക\",\n    \"app.component.table.duplicate\": \"ഡ്യൂപ്ലിക്കേറ്റ് {ലക്ഷ്യം}\",\n    \"app.component.table.edit\": \"എഡിറ്റ് {ലക്ഷ്യം}\",\n    \"app.component.table.select.one-entry\": \"{ലക്ഷ്യം} തിരഞ്ഞെടുക്കുക\",\n    \"app.components.BlockLink.blog\": \"ബ്ലോഗ്\",\n    \"app.components.BlockLink.blog.content\": \"സ്ട്രാപ്പിയെയും ആവാസവ്യവസ്ഥയെയും കുറിച്ചുള്ള ഏറ്റവും പുതിയ വാർത്തകൾ വായിക്കുക.\",\n    \"app.components.BlockLink.code\": \"കോഡ് ഉദാഹരണങ്ങൾ\",\n    \"app.components.BlockLink.code.content\": \"കമ്മ്യൂണിറ്റി വികസിപ്പിച്ച യഥാർത്ഥ പ്രോജക്റ്റുകൾ പരീക്ഷിച്ചുകൊണ്ട് പഠിക്കുക.\",\n    \"app.components.BlockLink.documentation.content\": \"അത്യാവശ്യ ആശയങ്ങളും ഗൈഡുകളും നിർദ്ദേശങ്ങളും കണ്ടെത്തുക.\",\n    \"app.components.BlockLink.tutorial\": \"ട്യൂട്ടോറിയലുകൾ\",\n    \"app.components.BlockLink.tutorial.content\": \"സ്ട്രാപ്പി ഉപയോഗിക്കാനും ഇഷ്ടാനുസൃതമാക്കാനും ഘട്ടം ഘട്ടമായുള്ള നിർദ്ദേശങ്ങൾ പാലിക്കുക.\",\n    \"app.components.Button.cancel\": \"റദ്ദാക്കുക\",\n    \"app.components.Button.confirm\": \"സ്ഥിരീകരിക്കുക\",\n    \"app.components.Button.reset\": \"റീസെറ്റ്\",\n    \"app.components.ComingSoonPage.comingSoon\": \"ഉടൻ വരുന്നു\",\n    \"app.components.ConfirmDialog.title\": \"സ്ഥിരീകരണം\",\n    \"app.components.DownloadInfo.download\": \"ഡൗൺലോഡ് പുരോഗതിയിലാണ്...\",\n    \"app.components.DownloadInfo.text\": \"ഇതിന് ഒരു മിനിറ്റ് എടുത്തേക്കാം. നിങ്ങളുടെ ക്ഷമയ്ക്ക് നന്ദി.\",\n    \"app.components.EmptyAttributes.title\": \"ഇതുവരെ ഫീൽഡുകളൊന്നുമില്ല\",\n    \"app.components.EmptyStateLayout.content-document\": \"ഉള്ളടക്കം ഒന്നും കണ്ടെത്തിയില്ല\",\n    \"app.components.EmptyStateLayout.content-permissions\": \"ആ ഉള്ളടക്കം ആക്‌സസ് ചെയ്യാൻ നിങ്ങൾക്ക് അനുമതിയില്ല\",\n    \"app.components.GuidedTour.CM.create.content\": \"<p>ഇവിടെയുള്ള ഉള്ളടക്ക മാനേജറിൽ എല്ലാ ഉള്ളടക്കവും സൃഷ്‌ടിക്കുകയും നിയന്ത്രിക്കുകയും ചെയ്യുക.</p><p>ഉദാ: ബ്ലോഗ് വെബ്‌സൈറ്റ് ഉദാഹരണം കൂടി എടുത്താൽ, ഒരാൾക്ക് എഴുതാം ലേഖനം, സംരക്ഷിച്ച് അവർക്ക് ഇഷ്ടമുള്ളത് പോലെ പ്രസിദ്ധീകരിക്കുക.</p><p>💡 ദ്രുത ടിപ്പ് - നിങ്ങൾ സൃഷ്ടിക്കുന്ന ഉള്ളടക്കത്തിൽ പ്രസിദ്ധീകരിക്കുക അമർത്താൻ മറക്കരുത്.</p>\",\n    \"app.components.GuidedTour.CM.create.title\": \"⚡️ ഉള്ളടക്കം സൃഷ്‌ടിക്കുക\",\n    \"app.components.GuidedTour.CM.success.content\": \"<p>അതിശയകരം, പോകാനുള്ള അവസാന ഘട്ടം!</p><b>🚀 പ്രവർത്തനത്തിലുള്ള ഉള്ളടക്കം കാണുക</b>\",\n    \"app.components.GuidedTour.CM.success.cta.title\": \"API പരീക്ഷിക്കുക\",\n    \"app.components.GuidedTour.CM.success.title\": \"ഘട്ടം 2: പൂർത്തിയായി ✅\",\n    \"app.components.GuidedTour.CTB.create.content\": \"<p>ശേഖര തരങ്ങൾ നിരവധി എൻട്രികൾ നിയന്ത്രിക്കാൻ നിങ്ങളെ സഹായിക്കുന്നു, ഒരു എൻട്രി മാത്രം നിയന്ത്രിക്കാൻ ഒറ്റ തരങ്ങൾ അനുയോജ്യമാണ്.</p> <p>ഉദാ: ഒരു ബ്ലോഗ് വെബ്‌സൈറ്റിന്, ലേഖനങ്ങൾ ഒരു ശേഖരണ തരമായിരിക്കും, അതേസമയം ഒരു ഹോം പേജ് ഒരൊറ്റ തരമായിരിക്കും.</p>\",\n    \"app.components.GuidedTour.CTB.create.cta.title\": \"ഒരു ശേഖരണ തരം നിർമ്മിക്കുക\",\n    \"app.components.GuidedTour.CTB.create.title\": \"🧠 ഒരു ആദ്യ ശേഖരണ തരം സൃഷ്ടിക്കുക\",\n    \"app.components.GuidedTour.CTB.success.content\": \"<p>നന്നായി പോകുന്നു!</p><b>⚡️ ലോകവുമായി എന്താണ് പങ്കിടാൻ നിങ്ങൾ ആഗ്രഹിക്കുന്നത്?</b>\",\n    \"app.components.GuidedTour.CTB.success.title\": \"ഘട്ടം 1: പൂർത്തിയായി ✅\",\n    \"app.components.GuidedTour.apiTokens.create.content\": \"<p>ഇവിടെ ഒരു പ്രാമാണീകരണ ടോക്കൺ സൃഷ്‌ടിക്കുകയും നിങ്ങൾ ഇപ്പോൾ സൃഷ്‌ടിച്ച ഉള്ളടക്കം വീണ്ടെടുക്കുകയും ചെയ്യുക.</p>\",\n    \"app.components.GuidedTour.apiTokens.create.cta.title\": \"ഒരു API ടോക്കൺ സൃഷ്ടിക്കുക\",\n    \"app.components.GuidedTour.apiTokens.create.title\": \"🚀 പ്രവർത്തനത്തിലുള്ള ഉള്ളടക്കം കാണുക\",\n    \"app.components.GuidedTour.apiTokens.success.content\": \"<p>ഒരു HTTP അഭ്യർത്ഥന നടത്തി പ്രവർത്തനത്തിലുള്ള ഉള്ളടക്കം കാണുക:</p><ul><li><p>ഈ URL-ലേക്ക്: <light>https: //'<'YOUR_DOMAIN'>'/api/'<'YOUR_CT'>'</p></p></li> <li><p>തലക്കെട്ടിനൊപ്പം: <light>അംഗീകാരം: ചുമക്കുന്നയാൾ '<' YOUR_API_TOKEN'>'</light></p></li></ul><p>ഉള്ളടക്കവുമായി സംവദിക്കാനുള്ള കൂടുതൽ വഴികൾക്കായി, <documentationLink>ഡോക്യുമെന്റേഷൻ</documentationLink> കാണുക.</p>\",\n    \"app.components.GuidedTour.apiTokens.success.cta.title\": \"ഹോംപേജിലേക്ക് മടങ്ങുക\",\n    \"app.components.GuidedTour.apiTokens.success.title\": \"ഘട്ടം 3: പൂർത്തിയായി ✅\",\n    \"app.components.GuidedTour.create-content\": \"ഉള്ളടക്കം സൃഷ്ടിക്കുക\",\n    \"app.components.GuidedTour.home.CM.title\": \"⚡️ ലോകവുമായി എന്താണ് പങ്കിടാൻ നിങ്ങൾ ആഗ്രഹിക്കുന്നത്?\",\n    \"app.components.GuidedTour.home.CTB.cta.title\": \"ഉള്ളടക്ക തരം ബിൽഡറിലേക്ക് പോകുക\",\n    \"app.components.GuidedTour.home.CTB.title\": \"🧠 ഉള്ളടക്ക ഘടന നിർമ്മിക്കുക\",\n    \"app.components.GuidedTour.home.apiTokens.cta.title\": \"API പരീക്ഷിക്കുക\",\n    \"app.components.GuidedTour.skip\": \"ടൂർ ഒഴിവാക്കുക\",\n    \"app.components.GuidedTour.title\": \"ആരംഭിക്കാനുള്ള 3 ഘട്ടങ്ങൾ\",\n    \"app.components.HomePage.button.blog\": \"ബ്ലോഗിൽ കൂടുതൽ കാണുക\",\n    \"app.components.HomePage.community\": \"കമ്മ്യൂണിറ്റിയിൽ ചേരുക\",\n    \"app.components.HomePage.community.content\": \"വ്യത്യസ്ത ചാനലുകളിലെ ടീം അംഗങ്ങൾ, സംഭാവകർ, ഡെവലപ്പർമാർ എന്നിവരുമായി ചർച്ച ചെയ്യുക.\",\n    \"app.components.HomePage.create\": \"നിങ്ങളുടെ ആദ്യ ഉള്ളടക്ക തരം സൃഷ്ടിക്കുക\",\n    \"app.components.HomePage.roadmap\": \"ഞങ്ങളുടെ റോഡ്മാപ്പ് കാണുക\",\n    \"app.components.HomePage.welcome\": \"ബോർഡിലേക്ക് സ്വാഗതം 👋\",\n    \"app.components.HomePage.welcome.again\": \"സ്വാഗതം 👋\",\n    \"app.components.HomePage.welcomeBlock.content\": \"അഭിനന്ദനങ്ങൾ! നിങ്ങൾ ആദ്യ അഡ്‌മിനിസ്‌ട്രേറ്ററായി ലോഗിൻ ചെയ്‌തിരിക്കുന്നു. സ്‌ട്രാപ്പി നൽകുന്ന ശക്തമായ സവിശേഷതകൾ കണ്ടെത്തുന്നതിന്, നിങ്ങളുടെ ആദ്യ ഉള്ളടക്ക തരം സൃഷ്‌ടിക്കാൻ ഞങ്ങൾ ശുപാർശ ചെയ്യുന്നു!\",\n    \"app.components.HomePage.welcomeBlock.content.again\": \"നിങ്ങളുടെ പ്രോജക്റ്റിൽ പുരോഗതിയുണ്ടെന്ന് ഞങ്ങൾ പ്രതീക്ഷിക്കുന്നു! സ്ട്രാപിയെക്കുറിച്ചുള്ള ഏറ്റവും പുതിയ വാർത്തകൾ വായിക്കാൻ മടിക്കേണ്ടതില്ല. നിങ്ങളുടെ ഫീഡ്‌ബാക്കിനെ അടിസ്ഥാനമാക്കി ഉൽപ്പന്നം മെച്ചപ്പെടുത്താൻ ഞങ്ങൾ പരമാവധി ശ്രമിക്കുന്നു.\",\n    \"app.components.HomePage.welcomeBlock.content.issues\": \"പ്രശ്നങ്ങൾ.\",\n    \"app.components.HomePage.welcomeBlock.content.raise\": \" അല്ലെങ്കിൽ ഉയർത്തുക \",\n    \"app.components.ImgPreview.hint\": \"നിങ്ങളുടെ ഫയൽ ഈ ഏരിയയിലേക്ക് വലിച്ചിടുക അല്ലെങ്കിൽ ഒരു ഫയൽ അപ്‌ലോഡ് ചെയ്യാൻ {ബ്രൗസ് ചെയ്യുക\",\n    \"app.components.ImgPreview.hint.browse\": \"browse\",\n    \"app.components.InputFile.newFile\": \"പുതിയ ഫയൽ ചേർക്കുക\",\n    \"app.components.InputFileDetails.open\": \"ഒരു പുതിയ ടാബിൽ തുറക്കുക\",\n    \"app.components.InputFileDetails.originalName\": \"യഥാർത്ഥ നാമം:\",\n    \"app.components.InputFileDetails.remove\": \"ഈ ഫയൽ നീക്കം ചെയ്യുക\",\n    \"app.components.InputFileDetails.size\": \"വലുപ്പം:\",\n    \"app.components.InstallPluginPage.Download.description\": \"പ്ലഗിൻ ഡൗൺലോഡ് ചെയ്ത് ഇൻസ്റ്റാൾ ചെയ്യാൻ കുറച്ച് സെക്കന്റുകൾ എടുത്തേക്കാം.\",\n    \"app.components.InstallPluginPage.Download.title\": \"ഡൗൺലോഡ് ചെയ്യുന്നു...\",\n    \"app.components.InstallPluginPage.description\": \"നിങ്ങളുടെ ആപ്പ് അനായാസമായി വിപുലീകരിക്കുക.\",\n    \"app.components.LeftMenu.collapse\": \"നാവ്ബാർ ചുരുക്കുക\",\n    \"app.components.LeftMenu.expand\": \"നാവ്ബാർ വികസിപ്പിക്കുക\",\n    \"app.components.LeftMenu.logout\": \"Logout\",\n    \"app.components.LeftMenu.navbrand.title\": \"സ്ട്രാപ്പി ഡാഷ്ബോർഡ്\",\n    \"app.components.LeftMenu.navbrand.workplace\": \"ജോലിസ്ഥലം\",\n    \"app.components.LeftMenu.trialCountdown\": \"നിങ്ങളുടെ പ്രയാസം {date} പ്രായം പ്രാപ്തമാകും.\",\n    \"app.components.LeftMenuFooter.help\": \"സഹായം\",\n    \"app.components.LeftMenuFooter.poweredBy\": \"പവർ ചെയ്യുന്നത് \",\n    \"app.components.LeftMenuLinkContainer.collectionTypes\": \"ശേഖരണ തരങ്ങൾ\",\n    \"app.components.LeftMenuLinkContainer.configuration\": \"കോൺഫിഗറേഷനുകൾ\",\n    \"app.components.LeftMenuLinkContainer.general\": \"പൊതുവായത്\",\n    \"app.components.LeftMenuLinkContainer.noPluginsInstalled\": \"ഇതുവരെ പ്ലഗിനുകളൊന്നും ഇൻസ്റ്റാൾ ചെയ്തിട്ടില്ല\",\n    \"app.components.LeftMenuLinkContainer.plugins\": \"പ്ലഗിനുകൾ\",\n    \"app.components.LeftMenuLinkContainer.singleTypes\": \"ഒറ്റ തരങ്ങൾ\",\n    \"app.components.ListPluginsPage.deletePlugin.description\": \"പ്ലഗിൻ അൺഇൻസ്റ്റാൾ ചെയ്യാൻ കുറച്ച് സെക്കന്റുകൾ എടുത്തേക്കാം.\",\n    \"app.components.ListPluginsPage.deletePlugin.title\": \"അൺഇൻസ്റ്റാൾ ചെയ്യുന്നു\",\n    \"app.components.ListPluginsPage.description\": \"പ്രോജക്ടിൽ ഇൻസ്റ്റാൾ ചെയ്ത പ്ലഗിന്നുകളുടെ ലിസ്റ്റ്.\",\n    \"app.components.ListPluginsPage.head.title\": \"ലിസ്റ്റ് പ്ലഗിനുകൾ\",\n    \"app.components.Logout.logout\": \"Logout\",\n    \"app.components.Logout.profile\": \"പ്രൊഫൈൽ\",\n    \"app.components.MarketplaceBanner\": \"കമ്മ്യൂണിറ്റി നിർമ്മിച്ച പ്ലഗിനുകൾ കണ്ടെത്തുക, കൂടാതെ നിങ്ങളുടെ പ്രോജക്‌റ്റ് കിക്ക്‌സ്റ്റാർട്ട് ചെയ്യുന്നതിനുള്ള നിരവധി ആകർഷണീയമായ കാര്യങ്ങൾ, Strapi Awesome-ൽ.\",\n    \"app.components.MarketplaceBanner.image.alt\": \"ഒരു സ്ട്രാപ്പി റോക്കറ്റ് ലോഗോ\",\n    \"app.components.MarketplaceBanner.link\": \"ഇപ്പോൾ തന്നെ പരിശോധിക്കുക\",\n    \"app.components.NotFoundPage.back\": \"ഹോംപേജിലേക്ക് മടങ്ങുക\",\n    \"app.components.NotFoundPage.description\": \"കണ്ടെത്തിയില്ല\",\n    \"app.components.Official\": \"ഔദ്യോഗികം\",\n    \"app.components.Onboarding.help.button\": \"സഹായ ബട്ടൺ\",\n    \"app.components.Onboarding.label.completed\": \"% പൂർത്തിയായി\",\n    \"app.components.Onboarding.title\": \"വീഡിയോകൾ ആരംഭിക്കുക\",\n    \"app.components.PluginCard.Button.label.download\": \"ഡൗൺലോഡ്\",\n    \"app.components.PluginCard.Button.label.install\": \"ഇതിനകം ഇൻസ്റ്റാൾ ചെയ്തു\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed\": \"സ്വയമേവ റീലോഡ് ഫീച്ചർ പ്രവർത്തനക്ഷമമാക്കേണ്ടതുണ്ട്. ദയവായി `നൂൽ വികസിപ്പിക്കുക` ഉപയോഗിച്ച് നിങ്ങളുടെ ആപ്പ് ആരംഭിക്കുക.\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.confirm\": \"എനിക്ക് മനസ്സിലായി!\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.environment\": \"സുരക്ഷാ കാരണങ്ങളാൽ, ഒരു വികസന പരിതസ്ഥിതിയിൽ മാത്രമേ ഒരു പ്ലഗിൻ ഡൗൺലോഡ് ചെയ്യാൻ കഴിയൂ.\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.title\": \"ഡൗൺലോഡ് ചെയ്യുന്നത് അസാധ്യമാണ്\",\n    \"app.components.PluginCard.compatible\": \"നിങ്ങളുടെ ആപ്പുമായി പൊരുത്തപ്പെടുന്നു\",\n    \"app.components.PluginCard.compatibleCommunity\": \"കമ്മ്യൂണിറ്റിയുമായി പൊരുത്തപ്പെടുന്നു\",\n    \"app.components.PluginCard.more-details\": \"കൂടുതൽ വിശദാംശങ്ങൾ\",\n    \"app.components.ToggleCheckbox.off-label\": \"തെറ്റായ\",\n    \"app.components.ToggleCheckbox.on-label\": \"സത്യം\",\n    \"app.components.Users.MagicLink.connect\": \"ഈ ഉപയോക്താവിന് ആക്‌സസ് നൽകുന്നതിന് ഈ ലിങ്ക് പകർത്തി പങ്കിടുക\",\n    \"app.components.Users.MagicLink.connect.sso\": \"ഈ ലിങ്ക് ഉപയോക്താവിന് അയയ്‌ക്കുക, ആദ്യ ലോഗിൻ ഒരു SSO ദാതാവ് വഴി നടത്താം\",\n    \"app.components.Users.ModalCreateBody.block-title.details\": \"ഉപയോക്തൃ വിശദാംശങ്ങൾ\",\n    \"app.components.Users.ModalCreateBody.block-title.roles\": \"ഉപയോക്താവിന്റെ റോളുകൾ\",\n    \"app.components.Users.ModalCreateBody.block-title.roles.description\": \"ഒരു ഉപയോക്താവിന് ഒന്നോ അതിലധികമോ റോളുകൾ ഉണ്ടായിരിക്കാം\",\n    \"app.components.Users.SortPicker.button-label\": \"അനുസരിച്ച് അടുക്കുക\",\n    \"app.components.Users.SortPicker.sortby.email_asc\": \"ഇമെയിൽ (A മുതൽ Z വരെ)\",\n    \"app.components.Users.SortPicker.sortby.email_desc\": \"ഇമെയിൽ (Z മുതൽ A)\",\n    \"app.components.Users.SortPicker.sortby.firstname_asc\": \"ആദ്യ നാമം (A മുതൽ Z വരെ)\",\n    \"app.components.Users.SortPicker.sortby.firstname_desc\": \"ആദ്യ നാമം (Z മുതൽ A)\",\n    \"app.components.Users.SortPicker.sortby.lastname_asc\": \"അവസാന നാമം (A മുതൽ Z വരെ)\",\n    \"app.components.Users.SortPicker.sortby.lastname_desc\": \"അവസാന നാമം (Z മുതൽ A)\",\n    \"app.components.Users.SortPicker.sortby.username_asc\": \"ഉപയോക്തൃനാമം (A മുതൽ Z വരെ)\",\n    \"app.components.Users.SortPicker.sortby.username_desc\": \"ഉപയോക്തൃനാമം (Z മുതൽ A)\",\n    \"app.components.listPlugins.button\": \"പുതിയ പ്ലഗിൻ ചേർക്കുക\",\n    \"app.components.listPlugins.title.none\": \"പ്ലഗിനുകളൊന്നും ഇൻസ്റ്റാൾ ചെയ്തിട്ടില്ല\",\n    \"app.components.listPluginsPage.deletePlugin.error\": \"പ്ലഗിൻ അൺഇൻസ്റ്റാൾ ചെയ്യുമ്പോൾ ഒരു പിശക് സംഭവിച്ചു\",\n    \"app.containers.App.notification.error.init\": \"API അഭ്യർത്ഥിക്കുമ്പോൾ ഒരു പിശക് സംഭവിച്ചു\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin\": \"നിങ്ങൾക്ക് ഈ ലിങ്ക് ലഭിച്ചില്ലെങ്കിൽ, ദയവായി നിങ്ങളുടെ അഡ്മിനിസ്ട്രേറ്ററെ ബന്ധപ്പെടുക.\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.text.email\": \"നിങ്ങളുടെ പാസ്‌വേഡ് വീണ്ടെടുക്കൽ ലിങ്ക് ലഭിക്കുന്നതിന് കുറച്ച് മിനിറ്റുകൾ എടുത്തേക്കാം.\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.title\": \"ഇമെയിൽ അയച്ചു\",\n    \"app.containers.Users.EditPage.form.active.label\": \"ആക്റ്റീവ്\",\n    \"app.containers.Users.EditPage.header.label\": \"എഡിറ്റ് {name}\",\n    \"app.containers.Users.EditPage.header.label-loading\": \"ഉപയോക്താവിനെ എഡിറ്റ് ചെയ്യുക\",\n    \"app.containers.Users.EditPage.roles-bloc-title\": \"ആട്രിബ്യൂട്ട് ചെയ്ത റോളുകൾ\",\n    \"app.containers.Users.ModalForm.footer.button-success\": \"ഉപയോക്താവിനെ ക്ഷണിക്കുക\",\n    \"app.links.configure-view\": \"കാഴ്ച കോൺഫിഗർ ചെയ്യുക\",\n    \"app.page.not.found\": \"ശ്ശോ! നിങ്ങൾ തിരയുന്ന പേജ് ഞങ്ങൾക്ക് കണ്ടെത്താൻ കഴിയുന്നില്ല...\",\n    \"app.static.links.cheatsheet\": \"ചീറ്റ് ഷീറ്റ്\",\n    \"app.utils.SelectOption.defaultMessage\": \" \",\n    \"app.utils.add-filter\": \"ഫിൽട്ടർ ചേർക്കുക\",\n    \"app.utils.close-label\": \"അടയ്ക്കുക\",\n    \"app.utils.defaultMessage\": \" \",\n    \"app.utils.duplicate\": \"ഡ്യൂപ്ലിക്കേറ്റ്\",\n    \"app.utils.edit\": \"എഡിറ്റ്\",\n    \"app.utils.errors.file-too-big.message\": \"ഫയൽ വളരെ വലുതാണ്\",\n    \"app.utils.filter-value\": \"ഫിൽട്ടർ മൂല്യം\",\n    \"app.utils.filters\": \"ഫിൽട്ടറുകൾ\",\n    \"app.utils.notify.data-loaded\": \"{ലക്ഷ്യം} ലോഡ് ചെയ്തു\",\n    \"app.utils.placeholder.defaultMessage\": \" \",\n    \"app.utils.publish\": \"പ്രസിദ്ധീകരിക്കുക\",\n    \"app.utils.select-all\": \"എല്ലാം തിരഞ്ഞെടുക്കുക\",\n    \"app.utils.select-field\": \"ഫീൽഡ് തിരഞ്ഞെടുക്കുക\",\n    \"app.utils.select-filter\": \"അരിപ്പ തിരഞ്ഞെടുക്കുക\",\n    \"app.utils.unpublish\": \"പ്രസിദ്ധീകരിക്കാതിരിക്കുക\",\n    clearLabel: clearLabel,\n    \"coming.soon\": \"ഈ ഉള്ളടക്കം നിലവിൽ നിർമ്മാണത്തിലാണ്, ഏതാനും ആഴ്ചകൾക്കുള്ളിൽ തിരിച്ചെത്തും!\",\n    \"component.Input.error.validation.integer\": \"മൂല്യം ഒരു പൂർണ്ണസംഖ്യ ആയിരിക്കണം\",\n    \"components.AutoReloadBlocker.description\": \"ഇനിപ്പറയുന്ന കമാൻഡുകളിലൊന്ന് ഉപയോഗിച്ച് Strapi പ്രവർത്തിപ്പിക്കുക:\",\n    \"components.AutoReloadBlocker.header\": \"ഈ പ്ലഗിന് റീലോഡ് സവിശേഷത ആവശ്യമാണ്.\",\n    \"components.ErrorBoundary.title\": \"എന്തോ കുഴപ്പം സംഭവിച്ചു...\",\n    \"components.FilterOptions.FILTER_TYPES.$contains\": \"അടങ്ങുന്നു\",\n    \"components.FilterOptions.FILTER_TYPES.$containsi\": \"അടങ്ങുന്നു (case insensitive)\",\n    \"components.FilterOptions.FILTER_TYPES.$endsWith\": \"അവസാനിക്കുന്നു\",\n    \"components.FilterOptions.FILTER_TYPES.$endsWithi\": \"അവസാനിക്കുന്നു (case insensitive)\",\n    \"components.FilterOptions.FILTER_TYPES.$eq\": \"ആണ്\",\n    \"components.FilterOptions.FILTER_TYPES.$eqi\": \"ആണ് (case insensitive)\",\n    \"components.FilterOptions.FILTER_TYPES.$gt\": \"ഇതിനേക്കാൾ വലുതാണ്\",\n    \"components.FilterOptions.FILTER_TYPES.$gte\": \"ഇതിനേക്കാൾ വലുതോ തുല്യമോ ആണ്\",\n    \"components.FilterOptions.FILTER_TYPES.$lt\": \"ഇതിനേക്കാൾ കുറവാണ്\",\n    \"components.FilterOptions.FILTER_TYPES.$lte\": \"ഇതിലും കുറവോ തുല്യമോ ആണ്\",\n    \"components.FilterOptions.FILTER_TYPES.$ne\": \"അല്ല\",\n    \"components.FilterOptions.FILTER_TYPES.$nei\": \"അല്ല (case insensitive)\",\n    \"components.FilterOptions.FILTER_TYPES.$notContains\": \"ഉൾക്കൊള്ളുന്നില്ല\",\n    \"components.FilterOptions.FILTER_TYPES.$notContainsi\": \"ഉൾക്കൊള്ളുന്നില്ല (case insensitive)\",\n    \"components.FilterOptions.FILTER_TYPES.$notNull\": \"ശൂന്യമല്ല\",\n    \"components.FilterOptions.FILTER_TYPES.$null\": \"ശൂന്യമാണ്\",\n    \"components.FilterOptions.FILTER_TYPES.$startsWith\": \"ആരംഭിക്കുന്നു\",\n    \"components.FilterOptions.FILTER_TYPES.$startsWithi\": \"ആരംഭിക്കുന്നു (case insensitive)\",\n    \"components.Input.error.attribute.key.taken\": \"ഈ മൂല്യം ഇതിനകം നിലവിലുണ്ട്\",\n    \"components.Input.error.attribute.sameKeyAndName\": \"തുല്യമാകാൻ കഴിയില്ല\",\n    \"components.Input.error.attribute.taken\": \"ഈ ഫീൽഡ് നാമം ഇതിനകം നിലവിലുണ്ട്\",\n    \"components.Input.error.contain.lowercase\": \"പാസ്‌വേഡിൽ കുറഞ്ഞത് ഒരു ചെറിയ അക്ഷരമെങ്കിലും ഉണ്ടായിരിക്കണം\",\n    \"components.Input.error.contain.number\": \"പാസ്‌വേഡിൽ കുറഞ്ഞത് ഒരു നമ്പറെങ്കിലും ഉണ്ടായിരിക്കണം\",\n    \"components.Input.error.contain.uppercase\": \"പാസ്‌വേഡിൽ കുറഞ്ഞത് ഒരു വലിയ അക്ഷരമെങ്കിലും ഉണ്ടായിരിക്കണം\",\n    \"components.Input.error.contentTypeName.taken\": \"ഈ പേര് ഇതിനകം നിലവിലുണ്ട്\",\n    \"components.Input.error.custom-error\": \"{പിശക് സന്ദേശം} \",\n    \"components.Input.error.password.noMatch\": \"പാസ്‌വേഡുകൾ പൊരുത്തപ്പെടുന്നില്ല\",\n    \"components.Input.error.validation.email\": \"ഇതൊരു അസാധുവായ ഇമെയിൽ ആണ്\",\n    \"components.Input.error.validation.json\": \"ഇത് JSON ഫോർമാറ്റുമായി പൊരുത്തപ്പെടുന്നില്ല\",\n    \"components.Input.error.validation.lowercase\": \"മൂല്യം ഒരു ചെറിയക്ഷര സ്ട്രിംഗ് ആയിരിക്കണം\",\n    \"components.Input.error.validation.max\": \"മൂല്യം വളരെ ഉയർന്നതാണ് {max}.\",\n    \"components.Input.error.validation.maxLength\": \"മൂല്യം വളരെ ദൈർഘ്യമേറിയതാണ് {max}.\",\n    \"components.Input.error.validation.min\": \"മൂല്യം വളരെ കുറവാണ് {min}.\",\n    \"components.Input.error.validation.minLength\": \"മൂല്യം വളരെ ചെറുതാണ് {min}.\",\n    \"components.Input.error.validation.minSupMax\": \"ഉന്നതമാകാൻ കഴിയില്ല\",\n    \"components.Input.error.validation.regex\": \"മൂല്യം regex-മായി പൊരുത്തപ്പെടുന്നില്ല.\",\n    \"components.Input.error.validation.required\": \"ഈ മൂല്യം ആവശ്യമാണ്.\",\n    \"components.Input.error.validation.unique\": \"ഈ മൂല്യം ഇതിനകം ഉപയോഗിച്ചു.\",\n    \"components.InputSelect.option.placeholder\": \"ഇവിടെ തിരഞ്ഞെടുക്കുക\",\n    \"components.ListRow.empty\": \"ഡാറ്റയൊന്നും കാണിക്കാനില്ല.\",\n    \"components.NotAllowedInput.text\": \"ഈ ഫീൽഡ് കാണുന്നതിന് അനുമതികളൊന്നുമില്ല\",\n    \"components.OverlayBlocker.description\": \"സെർവർ പുനരാരംഭിക്കുന്നതിന് ആവശ്യമായ ഒരു സവിശേഷതയാണ് നിങ്ങൾ ഉപയോഗിക്കുന്നത്. സെർവർ പ്രവർത്തനക്ഷമമാകുന്നതുവരെ കാത്തിരിക്കുക.\",\n    \"components.OverlayBlocker.description.serverError\": \"സെർവർ പുനരാരംഭിച്ചിരിക്കണം, ദയവായി ടെർമിനലിൽ നിങ്ങളുടെ ലോഗുകൾ പരിശോധിക്കുക.\",\n    \"components.OverlayBlocker.title\": \"പുനരാരംഭിക്കുന്നതിനായി കാത്തിരിക്കുന്നു...\",\n    \"components.OverlayBlocker.title.serverError\": \"പുനരാരംഭിക്കുന്നതിന് പ്രതീക്ഷിച്ചതിലും കൂടുതൽ സമയമെടുക്കുന്നു\",\n    \"components.PageFooter.select\": \"ഓരോ പേജിനും എൻട്രികൾ\",\n    \"components.ProductionBlocker.description\": \"സുരക്ഷാ ആവശ്യങ്ങൾക്കായി ഞങ്ങൾ മറ്റ് പരിതസ്ഥിതികളിൽ ഈ പ്ലഗിൻ പ്രവർത്തനരഹിതമാക്കേണ്ടതുണ്ട്.\",\n    \"components.ProductionBlocker.header\": \"ഈ പ്ലഗിൻ വികസനത്തിൽ മാത്രമേ ലഭ്യമാകൂ.\",\n    \"components.Search.placeholder\": \"തിരയുക...\",\n    \"components.TableHeader.sort\": \"{label}-ൽ അടുക്കുക\",\n    \"components.Wysiwyg.ToggleMode.markdown-mode\": \"മാർക്ക്ഡൗൺ മോഡ്\",\n    \"components.Wysiwyg.ToggleMode.preview-mode\": \"പ്രിവ്യൂ മോഡ്\",\n    \"components.Wysiwyg.collapse\": \"ചുരുക്കുക\",\n    \"components.Wysiwyg.selectOptions.H1\": \"ശീർഷകം H1\",\n    \"components.Wysiwyg.selectOptions.H2\": \"ശീർഷകം H2\",\n    \"components.Wysiwyg.selectOptions.H3\": \"ശീർഷകം H3\",\n    \"components.Wysiwyg.selectOptions.H4\": \"ശീർഷകം H4\",\n    \"components.Wysiwyg.selectOptions.H5\": \"ശീർഷകം H5\",\n    \"components.Wysiwyg.selectOptions.H6\": \"ശീർഷകം H6\",\n    \"components.Wysiwyg.selectOptions.title\": \"ഒരു ശീർഷകം ചേർക്കുക\",\n    \"components.WysiwygBottomControls.charactersIndicators\": \"കഥാപാത്രങ്ങൾ\",\n    \"components.WysiwygBottomControls.fullscreen\": \"വികസിപ്പിക്കുക\",\n    \"components.WysiwygBottomControls.uploadFiles\": \"ഫയലുകൾ വലിച്ചിടുക, ക്ലിപ്പ്ബോർഡിൽ നിന്ന് ഒട്ടിക്കുക അല്ലെങ്കിൽ {browse}.\",\n    \"components.WysiwygBottomControls.uploadFiles.browse\": \"അവ തിരഞ്ഞെടുക്കുക\",\n    \"components.pagination.go-to\": \"പേജിലേക്ക് പോകുക {page}\",\n    \"components.pagination.go-to-next\": \"അടുത്ത പേജിലേക്ക് പോകുക\",\n    \"components.pagination.go-to-previous\": \"മുമ്പത്തെ പേജിലേക്ക് പോകുക\",\n    \"components.pagination.remaining-links\": \"കൂടാതെ മറ്റ് {number} ലിങ്കുകളും\",\n    \"components.popUpWarning.button.cancel\": \"ഇല്ല, റദ്ദാക്കുക\",\n    \"components.popUpWarning.button.confirm\": \"അതെ, സ്ഥിരീകരിക്കുക\",\n    \"components.popUpWarning.message\": \"ഇത് ഇല്ലാതാക്കണമെന്ന് തീർച്ചയാണോ?\",\n    \"components.popUpWarning.title\": \"ദയവായി സ്ഥിരീകരിക്കുക\",\n    \"form.button.continue\": \"തുടരുക\",\n    \"form.button.done\": \"പൂർത്തിയായി\",\n    \"global.actions\": \"പ്രവർത്തനങ്ങൾ\",\n    \"global.back\": \"ബാക്ക്\",\n    \"global.change-password\": \"പാസ്‌വേഡ് മാറ്റുക\",\n    \"global.content-manager\": \"ഉള്ളടക്ക മാനേജർ\",\n    \"global.continue\": \"തുടരുക\",\n    \"global.delete\": \"ഇല്ലാതാക്കുക\",\n    \"global.delete-target\": \"{ലക്ഷ്യം} ഇല്ലാതാക്കുക\",\n    \"global.description\": \"വിവരണം\",\n    \"global.details\": \"വിശദാംശങ്ങൾ\",\n    \"global.disabled\": \"അപ്രാപ്തമാക്കി\",\n    \"global.documentation\": \"ഡോക്യുമെന്റേഷൻ\",\n    \"global.enabled\": \"പ്രാപ്തമാക്കി\",\n    \"global.finish\": \"ഫിനിഷ്\",\n    \"global.marketplace\": \"Marketplace\",\n    \"global.name\": \"പേര്\",\n    \"global.none\": \"ഒന്നുമില്ല\",\n    \"global.password\": \"പാസ്‌വേഡ്\",\n    \"global.plugins\": \"പ്ലഗിനുകൾ\",\n    \"global.profile\": \"പ്രൊഫൈൽ\",\n    \"global.prompt.unsaved\": \"ഈ പേജ് വിടണമെന്ന് തീർച്ചയാണോ? നിങ്ങളുടെ എല്ലാ പരിഷ്‌ക്കരണങ്ങളും നഷ്‌ടമാകും\",\n    \"global.reset-password\": \"പാസ്‌വേഡ് പുനഃസജ്ജമാക്കുക\",\n    \"global.roles\": \"റോളുകൾ\",\n    \"global.save\": \"സംരക്ഷിക്കുക\",\n    \"global.see-more\": \"കൂടുതൽ കാണുക\",\n    \"global.select\": \"തിരഞ്ഞെടുക്കുക\",\n    \"global.select-all-entries\": \"എല്ലാ എൻട്രികളും തിരഞ്ഞെടുക്കുക\",\n    \"global.settings\": \"ക്രമീകരണങ്ങൾ\",\n    \"global.type\": \"തരം\",\n    \"global.users\": \"ഉപയോക്താക്കൾ\",\n    \"notification.contentType.relations.conflict\": \"ഉള്ളടക്ക തരത്തിന് പരസ്പരവിരുദ്ധമായ ബന്ധങ്ങളുണ്ട്\",\n    \"notification.default.title\": \"വിവരങ്ങൾ:\",\n    \"notification.error\": \"ഒരു പിശക് സംഭവിച്ചു\",\n    \"notification.error.layout\": \"ലേഔട്ട് വീണ്ടെടുക്കാൻ കഴിഞ്ഞില്ല\",\n    \"notification.form.error.fields\": \"ഫോമിൽ ചില പിശകുകൾ അടങ്ങിയിരിക്കുന്നു\",\n    \"notification.form.success.fields\": \"മാറ്റങ്ങൾ സംരക്ഷിച്ചു\",\n    \"notification.link-copied\": \"ലിങ്ക് ക്ലിപ്പ്ബോർഡിലേക്ക് പകർത്തി\",\n    \"notification.permission.not-allowed-read\": \"നിങ്ങൾക്ക് ഈ പ്രമാണം കാണാൻ അനുവാദമില്ല\",\n    \"notification.success.delete\": \"ഇനം ഇല്ലാതാക്കി\",\n    \"notification.success.saved\": \"സംരക്ഷിച്ചു\",\n    \"notification.success.title\": \"വിജയം:\",\n    \"notification.version.update.message\": \"സ്ട്രാപിയുടെ ഒരു പുതിയ പതിപ്പ് ലഭ്യമാണ്!\",\n    \"notification.warning.title\": \"മുന്നറിയിപ്പ്:\",\n    or: or,\n    \"request.error.model.unknown\": \"ഈ മോഡൽ നിലവിലില്ല\",\n    skipToContent: skipToContent,\n    submit: submit\n};\n\nexport { Analytics, Documentation, Email, Password, Provider, ResetPasswordToken, Role, Username, Users, anErrorOccured, clearLabel, ml as default, or, skipToContent, submit };\n//# sourceMappingURL=ml.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,YAAY;AAChB,IAAI,gBAAgB;AACpB,IAAI,QAAQ;AACZ,IAAI,WAAW;AACf,IAAI,WAAW;AACf,IAAI,qBAAqB;AACzB,IAAI,OAAO;AACX,IAAI,WAAW;AACf,IAAI,QAAQ;AACZ,IAAI,iBAAiB;AACrB,IAAI,aAAa;AACjB,IAAI,KAAK;AACT,IAAI,gBAAgB;AACpB,IAAI,SAAS;AACb,IAAI,KAAK;AAAA,EACL;AAAA,EACA,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0CAA0C;AAAA,EAC1C,iCAAiC;AAAA,EACjC,sCAAsC;AAAA,EACtC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,iDAAiD;AAAA,EACjD,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,2BAA2B;AAAA,EAC3B,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA,EAChB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,6BAA6B;AAAA,EAC7B,qCAAqC;AAAA,EACrC,0CAA0C;AAAA,EAC1C,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,4BAA4B;AAAA,EAC5B,4BAA4B;AAAA,EAC5B,0CAA0C;AAAA,EAC1C,sBAAsB;AAAA,EACtB,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,4BAA4B;AAAA,EAC5B,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,oCAAoC;AAAA,EACpC,sCAAsC;AAAA,EACtC,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,sCAAsC;AAAA,EACtC,8BAA8B;AAAA,EAC9B,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,yDAAyD;AAAA,EACzD,iDAAiD;AAAA,EACjD,8DAA8D;AAAA,EAC9D,mEAAmE;AAAA,EACnE,kEAAkE;AAAA,EAClE,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,8DAA8D;AAAA,EAC9D,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,oDAAoD;AAAA,EACpD,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,mCAAmC;AAAA,EACnC,iDAAiD;AAAA,EACjD,yDAAyD;AAAA,EACzD,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,wCAAwC;AAAA,EACxC,0CAA0C;AAAA,EAC1C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,6CAA6C;AAAA,EAC7C,yDAAyD;AAAA,EACzD,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,wCAAwC;AAAA,EACxC,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,2CAA2C;AAAA,EAC3C,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,4CAA4C;AAAA,EAC5C,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,2BAA2B;AAAA,EAC3B,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,qBAAqB;AAAA,EACrB,sBAAsB;AAAA,EACtB,2BAA2B;AAAA,EAC3B,gDAAgD;AAAA,EAChD,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,+BAA+B;AAAA,EAC/B,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,2CAA2C;AAAA,EAC3C,mDAAmD;AAAA,EACnD,2CAA2C;AAAA,EAC3C,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,uDAAuD;AAAA,EACvD,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,kDAAkD;AAAA,EAClD,kDAAkD;AAAA,EAClD,wCAAwC;AAAA,EACxC,mDAAmD;AAAA,EACnD,yDAAyD;AAAA,EACzD;AAAA,EACA,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,wCAAwC;AAAA,EACxC,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,4CAA4C;AAAA,EAC5C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,+CAA+C;AAAA,EAC/C,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,+CAA+C;AAAA,EAC/C,sDAAsD;AAAA,EACtD,wDAAwD;AAAA,EACxD,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,yDAAyD;AAAA,EACzD,qDAAqD;AAAA,EACrD,4CAA4C;AAAA,EAC5C,2CAA2C;AAAA,EAC3C,gDAAgD;AAAA,EAChD,4CAA4C;AAAA,EAC5C,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,0CAA0C;AAAA,EAC1C,sCAAsC;AAAA,EACtC,2CAA2C;AAAA,EAC3C,wDAAwD;AAAA,EACxD,sDAAsD;AAAA,EACtD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,2DAA2D;AAAA,EAC3D,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,oCAAoC;AAAA,EACpC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,2CAA2C;AAAA,EAC3C,2BAA2B;AAAA,EAC3B,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,+EAA+E;AAAA,EAC/E,qEAAqE;AAAA,EACrE,yEAAyE;AAAA,EACzE,mEAAmE;AAAA,EACnE,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,4DAA4D;AAAA,EAC5D,0DAA0D;AAAA,EAC1D,sEAAsE;AAAA,EACtE,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,qDAAqD;AAAA,EACrD,wDAAwD;AAAA,EACxD,yDAAyD;AAAA,EACzD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,qCAAqC;AAAA,EACrC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,oEAAoE;AAAA,EACpE,4DAA4D;AAAA,EAC5D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,wDAAwD;AAAA,EACxD,4BAA4B;AAAA,EAC5B,sBAAsB;AAAA,EACtB,+BAA+B;AAAA,EAC/B,yCAAyC;AAAA,EACzC,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,4BAA4B;AAAA,EAC5B,uBAAuB;AAAA,EACvB,kBAAkB;AAAA,EAClB,yCAAyC;AAAA,EACzC,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,gCAAgC;AAAA,EAChC,wCAAwC;AAAA,EACxC,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB;AAAA,EACA,eAAe;AAAA,EACf,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,+CAA+C;AAAA,EAC/C,qDAAqD;AAAA,EACrD,sDAAsD;AAAA,EACtD,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,0CAA0C;AAAA,EAC1C,4CAA4C;AAAA,EAC5C,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,gDAAgD;AAAA,EAChD,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,yDAAyD;AAAA,EACzD,+CAA+C;AAAA,EAC/C,gDAAgD;AAAA,EAChD,uDAAuD;AAAA,EACvD,+BAA+B;AAAA,EAC/B,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,yCAAyC;AAAA,EACzC,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,wBAAwB;AAAA,EACxB,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,eAAe;AAAA,EACf,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,wBAAwB;AAAA,EACxB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,sBAAsB;AAAA,EACtB,eAAe;AAAA,EACf,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,yBAAyB;AAAA,EACzB,yBAAyB;AAAA,EACzB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,+CAA+C;AAAA,EAC/C,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,4CAA4C;AAAA,EAC5C,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B;AAAA,EACA,+BAA+B;AAAA,EAC/B;AAAA,EACA;AACJ;", "names": []}