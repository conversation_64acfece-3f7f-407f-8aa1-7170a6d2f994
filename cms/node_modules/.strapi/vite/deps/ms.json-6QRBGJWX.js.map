{"version": 3, "sources": ["../../../@strapi/content-manager/dist/admin/translations/ms.json.mjs"], "sourcesContent": ["var groups = \"Kumpulan\";\nvar models = \"<PERSON><PERSON>\";\nvar pageNotFound = \"Laman tidak dijumpai\";\nvar ms = {\n    \"EditRelations.title\": \"Data Terhubung(Relational data)\",\n    \"api.id\": \"ID API\",\n    \"components.AddFilterCTA.add\": \"Penapis\",\n    \"components.AddFilterCTA.hide\": \"Penapis\",\n    \"components.DraggableAttr.edit\": \"Klik untuk mengedit\",\n    \"components.DynamicZone.pick-compo\": \"Pilih satu komponen\",\n    \"components.EmptyAttributesBlock.button\": \"Pergi ke halaman tetapan\",\n    \"components.EmptyAttributesBlock.description\": \"Anda boleh ubah tetapan anda\",\n    \"components.FieldItem.linkToComponentLayout\": \"Tetapkan susun atur komponen\",\n    \"components.FilterOptions.button.apply\": \"Gunakan\",\n    \"components.FiltersPickWrapper.PluginHeader.actions.apply\": \"Gunakan\",\n    \"components.FiltersPickWrapper.PluginHeader.actions.clearAll\": \"Kosongkan semua\",\n    \"components.FiltersPickWrapper.PluginHeader.description\": \"Tetapkan syarat yang akan digunakan untuk tapis entri\",\n    \"components.FiltersPickWrapper.PluginHeader.title.filter\": \"Tapisan\",\n    \"components.FiltersPickWrapper.hide\": \"Sembunyikan\",\n    \"components.LimitSelect.itemsPerPage\": \"Item setiap halaman\",\n    \"components.Search.placeholder\": \"Cari entri..\",\n    \"components.SettingsViewWrapper.pluginHeader.description.edit-settings\": \"Ubah paparan untuk pengeditan.\",\n    \"components.SettingsViewWrapper.pluginHeader.description.list-settings\": \"Tentukan tetapan paparan senarai.\",\n    \"components.SettingsViewWrapper.pluginHeader.title\": \"Konfigurasikan paparan - {name}\",\n    \"components.TableDelete.delete\": \"Padam semua\",\n    \"components.TableDelete.deleteSelected\": \"Padam yang dipilih\",\n    \"components.TableEmpty.withFilters\": \"Tiada {contentType} dalam tapisan...\",\n    \"components.TableEmpty.withSearch\": \"Tiada {contentType} yang sesuai dengan carian ({search})...\",\n    \"components.TableEmpty.withoutFilter\": \"Tiada {contentType}...\",\n    \"components.empty-repeatable\": \"Belum ada entri. Klik pada butang di bawah untuk tambah satu\",\n    \"components.notification.info.maximum-requirement\": \"Anda sudah mencapai bilangan ruang maksimum\",\n    \"components.notification.info.minimum-requirement\": \"Ruang telah ditambahkan untuk memenuhi syarat minimum\",\n    \"components.reset-entry\": \"Tetapkan semula entri\",\n    \"components.uid.apply\": \"Gunakan\",\n    \"components.uid.available\": \"Tersedia\",\n    \"components.uid.regenerate\": \"jana semula\",\n    \"components.uid.suggested\": \"yang dicadangkan\",\n    \"components.uid.unavailable\": \"tidak tersedia\",\n    \"containers.Edit.Link.Layout\": \"Konfigurasikan susun atur\",\n    \"containers.Edit.Link.Model\": \"Edit jenis koleksi\",\n    \"containers.Edit.addAnItem\": \"Tambah item...\",\n    \"containers.Edit.clickToJump\": \"Klik untuk pergi ke entri\",\n    \"containers.Edit.delete\": \"Padam\",\n    \"containers.Edit.editing\": \"Menyunting...\",\n    \"containers.Edit.pluginHeader.title.new\": \"Buat entri\",\n    \"containers.Edit.reset\": \"Semula\",\n    \"containers.Edit.returnList\": \"Kembali ke senarai\",\n    \"containers.Edit.seeDetails\": \"Butiran\",\n    \"containers.Edit.submit\": \"Simpan\",\n    \"containers.EditSettingsView.modal-form.edit-field\": \"Edit ruang\",\n    \"containers.EditView.notification.errors\": \"Borang mengandungi beberapa kesalahan\",\n    \"containers.Home.introduction\": \"Untuk mengedit entri anda, pergi ke pautan yang ditetapkan di menu sebelah kiri. Plugin ini tidak mempunyai cara yang betul untuk mengedit tetapan dan masih dalam pembangunan yang aktif\",\n    \"containers.Home.pluginHeaderDescription\": \"Uruskan entri anda melalui antaramuka yang hebat dan cantik.\",\n    \"containers.Home.pluginHeaderTitle\": \"Pengurus Kandungan\",\n    \"containers.List.errorFetchRecords\": \"Ralat\",\n    \"containers.list.displayedFields\": \"Ruang yang dipamirkan\",\n    \"containers.ListSettingsView.modal-form.edit-label\": \"Edit label\",\n    \"containers.SettingPage.add.field\": \"Tambah ruang lain\",\n    \"containers.SettingPage.attributes\": \"Ruang ciri-ciri\",\n    \"containers.SettingPage.attributes.description\": \"Tentukan tertib untuk ciri-ciri\",\n    \"containers.SettingPage.editSettings.description\": \"Tarik & Lepas ruang untuk menyusun atur\",\n    \"containers.SettingPage.editSettings.entry.title\": \"Pilihan nama entri\",\n    \"containers.SettingPage.editSettings.entry.title.description\": \"Pilih ruang yang akan dipaparkan sebagai nama untuk entri anda.\",\n    \"containers.SettingPage.editSettings.title\": \"Edit paparan (tetapan)\",\n    \"containers.SettingPage.layout\": \"Susun atur\",\n    \"containers.SettingPage.listSettings.description\": \"Tetapkan pilihan yang ada untuk jenis koleksi ini\",\n    \"containers.SettingPage.listSettings.title\": \"Paparan senarai (tetapan)\",\n    \"containers.SettingPage.pluginHeaderDescription\": \"Tetapkan tetapan khusus untuk Jenis Koleksi ini\",\n    \"containers.SettingPage.settings\": \"Tetapan\",\n    \"containers.SettingPage.view\": \"Sususnan\",\n    \"containers.SettingViewModel.pluginHeader.title\": \"Pengurus Kandungan - {name}\",\n    \"containers.SettingsPage.Block.contentType.description\": \"Tetapkan tetapan tertentu\",\n    \"containers.SettingsPage.Block.contentType.title\": \"Jenis Koleksi(Collection Types)\",\n    \"containers.SettingsPage.Block.generalSettings.description\": \"Tetapkan pilihan lalai untuk Jenis Koleksi anda\",\n    \"containers.SettingsPage.Block.generalSettings.title\": \"Am(General)\",\n    \"containers.SettingsPage.pluginHeaderDescription\": \"Tetapkan tetapan untuk semua Jenis koleksi dan Kumpulan anda\",\n    \"containers.SettingsView.list.subtitle\": \"Tetapkan susun atur dan paparan Jenis koleksi dan kumpulan anda\",\n    \"containers.SettingsView.list.title\": \"Paparan konfigurasi\",\n    \"emptyAttributes.button\": \"Pergi ke pembina jenis koleksi\",\n    \"emptyAttributes.description\": \"Tambah ruang pertama anda ke Jenis Koleksi anda\",\n    \"emptyAttributes.title\": \"Belum ada ruang\",\n    \"error.attribute.key.taken\": \"Nilai ini sudah ada\",\n    \"error.attribute.sameKeyAndName\": \"Tidak boleh sama\",\n    \"error.attribute.taken\": \"Nama ruang ini sudah ada\",\n    \"error.contentTypeName.taken\": \"Nama ini sudah ada\",\n    \"error.model.fetch\": \"Ralat berlaku sewaktu mendapatkan tetapan model \",\n    \"error.record.create\": \"Ralat berlaku sewaktu membuat rekod.\",\n    \"error.record.delete\": \"Ralat berlaku sewaktu penghapusan rekod.\",\n    \"error.record.fetch\": \"Ralat berlaku sewaktu mendapatkan rekod.\",\n    \"error.record.update\": \"Ralat berlaku sewaktu mengemaskini rekod.\",\n    \"error.records.count\": \"Ralat berlaku sewaktu mendapatkan kiraan rekod.\",\n    \"error.records.fetch\": \"Ralat berlaku sewaktu mendapatkan rekod.\",\n    \"error.schema.generation\": \"Ralat berlaku sewaktu penghasilan skema.\",\n    \"error.validation.json\": \"Ini bukan JSON(This is not a JSON)\",\n    \"error.validation.max\": \"Nilai isinya terlalu tinggi.\",\n    \"error.validation.maxLength\": \"Panjang isinya terlalu panjang.\",\n    \"error.validation.min\": \"Nilai isinya terlalu rendah.\",\n    \"error.validation.minLength\": \"Panjang isinya terlalu pendek.\",\n    \"error.validation.minSupMax\": \"Tidak boleh lebih tinggi\",\n    \"error.validation.regex\": \"Nilai isinya tidak sepadan dengan regex.\",\n    \"error.validation.required\": \"Nilai input ini adalah wajib\",\n    \"form.Input.bulkActions\": \"Benarkan tindakan pukal\",\n    \"form.Input.defaultSort\": \"Ciri tertib yang lalai\",\n    \"form.Input.description\": \"Penerangan\",\n    \"form.Input.description.placeholder\": \"Paparkan nama dalam profil\",\n    \"form.Input.editable\": \"Ruang yang boleh diedit\",\n    \"form.Input.filters\": \"Benarkan tapisan\",\n    \"form.Input.label\": \"Label\",\n    \"form.Input.label.inputDescription\": \"Isinya ini menggantikan label yang dipaparkan di tajuk jadual\",\n    \"form.Input.pageEntries\": \"Entri setiap halaman\",\n    \"form.Input.pageEntries.inputDescription\": \"Catatan: Anda boleh menukarkan nilai ini di halaman tetapan Jenis Koleksi\",\n    \"form.Input.placeholder\": \"Pemegang Tempat\",\n    \"form.Input.placeholder.placeholder\": \"My awesome value\",\n    \"form.Input.search\": \"Benarkan carian\",\n    \"form.Input.search.field\": \"Benarkan carian di ruang ini\",\n    \"form.Input.sort.field\": \"Benarkan penyusunan di ruang ini\",\n    \"form.Input.wysiwyg\": \"Paparkan sebagai WYSIWYG\",\n    \"global.displayedFields\": \"Ruang yang dipaparkan\",\n    groups: groups,\n    \"groups.numbered\": \"Kumpulan ({number})\",\n    models: models,\n    \"models.numbered\": \"Jenis Koleksi ({number})\",\n    \"notification.error.displayedFields\": \"Anda perlukan sekurang-kurangnya satu ruang yang dipaparkan\",\n    \"notification.error.relationship.fetch\": \"Ralat berlaku sewaktu mendapatkan perhubungan data.\",\n    \"notification.info.SettingPage.disableSort\": \"Anda mesti mempunyai satu attribute dengan penertib yang telah dibenarkan(You need to have one attribute with the sorting allowed)\",\n    \"notification.info.minimumFields\": \"Anda mesti memaparkan sekurang-kurangnya satu ruang\",\n    \"notification.upload.error\": \"Berlaku ralat sewaktu memuat naik fail anda\",\n    pageNotFound: pageNotFound,\n    \"plugin.description.long\": \"Cara cepat untuk melihat, mengedit dan menghapus data dalam pangkalan data anda.\",\n    \"plugin.description.short\": \"Cara cepat untuk melihat, mengedit dan menghapus data dalam pangkalan data anda.\",\n    \"popUpWarning.bodyMessage.contentType.delete\": \"Adakah anda pasti mahu memadam entri ini?\",\n    \"popUpWarning.bodyMessage.contentType.delete.all\": \"Adakah anda pasti mahu memadam entri ini?\",\n    \"popUpWarning.warning.cancelAllSettings\": \"Adakah anda pasti mahu membatalkan pengubahsuaian anda?\",\n    \"popUpWarning.warning.updateAllSettings\": \"Ini akan mengubah semua tetapan anda\",\n    \"success.record.delete\": \"Telah Dipadam\",\n    \"success.record.save\": \"Disimpan\"\n};\n\nexport { ms as default, groups, models, pageNotFound };\n//# sourceMappingURL=ms.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,SAAS;AACb,IAAI,SAAS;AACb,IAAI,eAAe;AACnB,IAAI,KAAK;AAAA,EACL,uBAAuB;AAAA,EACvB,UAAU;AAAA,EACV,+BAA+B;AAAA,EAC/B,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,4DAA4D;AAAA,EAC5D,+DAA+D;AAAA,EAC/D,0DAA0D;AAAA,EAC1D,2DAA2D;AAAA,EAC3D,sCAAsC;AAAA,EACtC,uCAAuC;AAAA,EACvC,iCAAiC;AAAA,EACjC,yEAAyE;AAAA,EACzE,yEAAyE;AAAA,EACzE,qDAAqD;AAAA,EACrD,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,uCAAuC;AAAA,EACvC,+BAA+B;AAAA,EAC/B,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,6BAA6B;AAAA,EAC7B,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA,EAC9B,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,6BAA6B;AAAA,EAC7B,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,0CAA0C;AAAA,EAC1C,yBAAyB;AAAA,EACzB,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,qDAAqD;AAAA,EACrD,2CAA2C;AAAA,EAC3C,gCAAgC;AAAA,EAChC,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,qDAAqD;AAAA,EACrD,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,+DAA+D;AAAA,EAC/D,6CAA6C;AAAA,EAC7C,iCAAiC;AAAA,EACjC,mDAAmD;AAAA,EACnD,6CAA6C;AAAA,EAC7C,kDAAkD;AAAA,EAClD,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,kDAAkD;AAAA,EAClD,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,6DAA6D;AAAA,EAC7D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,yCAAyC;AAAA,EACzC,sCAAsC;AAAA,EACtC,0BAA0B;AAAA,EAC1B,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,qCAAqC;AAAA,EACrC,0BAA0B;AAAA,EAC1B,2CAA2C;AAAA,EAC3C,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B;AAAA,EACA,mBAAmB;AAAA,EACnB;AAAA,EACA,mBAAmB;AAAA,EACnB,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B;AAAA,EACA,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,+CAA+C;AAAA,EAC/C,mDAAmD;AAAA,EACnD,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,yBAAyB;AAAA,EACzB,uBAAuB;AAC3B;", "names": []}