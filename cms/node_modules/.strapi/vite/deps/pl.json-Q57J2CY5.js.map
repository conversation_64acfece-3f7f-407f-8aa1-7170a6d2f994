{"version": 3, "sources": ["../../../@strapi/content-manager/dist/admin/translations/pl.json.mjs"], "sourcesContent": ["var groups = \"Grupy\";\nvar models = \"<PERSON><PERSON>\";\nvar pageNotFound = \"Strona nie znaleziona\";\nvar pl = {\n    \"App.schemas.data-loaded\": \"Schematy zostały poprawnie załadowane\",\n    \"ListViewTable.relation-loaded\": \"Relacje zostały załadowane\",\n    \"ListViewTable.relation-loading\": \"Trwa ładowanie relacji\",\n    \"ListViewTable.relation-more\": \"Ta relacja zwiera więcej wartości nież wyświetlana\",\n    \"EditRelations.title\": \"Relacje\",\n    \"HeaderLayout.button.label-add-entry\": \"Dodaj nowy wpis\",\n    \"api.id\": \"API ID\",\n    \"components.AddFilterCTA.add\": \"Filtry\",\n    \"components.AddFilterCTA.hide\": \"Filtry\",\n    \"components.DragHandle-label\": \"Przenieś\",\n    \"components.DraggableAttr.edit\": \"Kliknij by ed<PERSON><PERSON><PERSON>\",\n    \"components.DraggableCard.delete.field\": \"Usuń {item}\",\n    \"components.DraggableCard.edit.field\": \"Edytuj {item}\",\n    \"components.DraggableCard.move.field\": \"Przenieś {item}\",\n    \"components.ListViewTable.row-line\": \"rząd {number}\",\n    \"components.DynamicZone.ComponentPicker-label\": \"Wybierz komponent\",\n    \"components.DynamicZone.add-component\": \"Dodaj komponent do {componentName}\",\n    \"components.DynamicZone.delete-label\": \"Usuń {name}\",\n    \"components.DynamicZone.error-message\": \"Komponent zawiera błąd/błędy\",\n    \"components.DynamicZone.missing-components\": \"Brakuje {number, plural, =0 {# komponentów} one {# komponentu} other {# komponentów}}\",\n    \"components.DynamicZone.move-down-label\": \"Przesuń niżej\",\n    \"components.DynamicZone.move-up-label\": \"Przesuń wyżej\",\n    \"components.DynamicZone.pick-compo\": \"Wybierz jeden komponent\",\n    \"components.DynamicZone.required\": \"Komponent jest wymagany\",\n    \"components.EmptyAttributesBlock.button\": \"Przejdź do ustawień\",\n    \"components.EmptyAttributesBlock.description\": \"Możesz zmienić ustawienia\",\n    \"components.FieldItem.linkToComponentLayout\": \"Ustaw układ komponentu\",\n    \"components.FieldSelect.label\": \"Dodaj pole\",\n    \"components.FilterOptions.button.apply\": \"Zastosuj\",\n    \"components.FiltersPickWrapper.PluginHeader.actions.apply\": \"Zastosuj\",\n    \"components.FiltersPickWrapper.PluginHeader.actions.clearAll\": \"Wyczyść wszystko\",\n    \"components.FiltersPickWrapper.PluginHeader.description\": \"Ustawianie warunków filtrowania elementów.\",\n    \"components.FiltersPickWrapper.PluginHeader.title.filter\": \"Filtry\",\n    \"components.FiltersPickWrapper.hide\": \"Ukryj\",\n    \"components.LeftMenu.Search.label\": \"Szukaj\",\n    \"components.LeftMenu.collection-types\": \"Typy kolekcji\",\n    \"components.LeftMenu.single-types\": \"Pojedynczy typ\",\n    \"components.LimitSelect.itemsPerPage\": \"Elementów na stronę\",\n    \"components.NotAllowedInput.text\": \"Brak uprawnień do zobaczenia tego pola\",\n    \"components.RepeatableComponent.error-message\": \"Komponent zawiera błąd/błędy\",\n    \"components.Search.placeholder\": \"Szukaj elementu...\",\n    \"components.Select.draft-info-title\": \"Stan: Szkic\",\n    \"components.Select.publish-info-title\": \"Stan: Opublikowany\",\n    \"components.SettingsViewWrapper.pluginHeader.description.edit-settings\": \"Dostosuj wygląd widoku edycji.\",\n    \"components.SettingsViewWrapper.pluginHeader.description.list-settings\": \"Zdefiniuj ustawienia widoku listy.\",\n    \"components.SettingsViewWrapper.pluginHeader.title\": \"Skonfiguruj widok - {name}\",\n    \"components.TableDelete.delete\": \"Usuń\",\n    \"components.TableDelete.deleteSelected\": \"Usuń zaznaczone\",\n    \"components.TableDelete.label\": \"{number, plural, one {# wpis zaznaczony} other {# wpisy zaznaczone}}\",\n    \"components.TableEmpty.withFilters\": \"Nie istnieją elementy {contentType} zgodne z zastosowanymi filtrami...\",\n    \"components.TableEmpty.withSearch\": \"Nie istnieją elementy {contentType} zgodne z wyszukiwaną frazą ({search})...\",\n    \"components.TableEmpty.withoutFilter\": \"Nie istnieją jeszcze elementy związane z {contentType}... Stwórz pierwszy jak najszybciej!\",\n    \"components.empty-repeatable\": \"Jeszcze nie ma wpisu. Kliknij przycisk poniżej, aby go dodać.\",\n    \"components.notification.info.maximum-requirement\": \"Osiągięto maksymalną liczbę pól\",\n    \"components.notification.info.minimum-requirement\": \"Dodano pole spełniające minimalne wymagania\",\n    \"components.repeatable.reorder.error\": \"Wystąpił błąd podczas zmiany pozycji komponentu, spróbuj raz jeszcze\",\n    \"components.reset-entry\": \"Zresetuj wpis\",\n    \"components.uid.apply\": \"zastostuj\",\n    \"components.uid.available\": \"Dostępny\",\n    \"components.uid.regenerate\": \"Odnów\",\n    \"components.uid.suggested\": \"zasugerowany\",\n    \"components.uid.unavailable\": \"Niedostępny\",\n    \"containers.Edit.Link.Layout\": \"Skonfiguruj układ\",\n    \"containers.Edit.Link.Model\": \"Edytuj typ kolekcji\",\n    \"containers.Edit.addAnItem\": \"Dodaj element...\",\n    \"containers.Edit.clickToJump\": \"Kliknij aby przejść do elementu\",\n    \"containers.Edit.delete\": \"Usuń\",\n    \"containers.Edit.delete-entry\": \"Usuń ten wpis\",\n    \"containers.Edit.editing\": \"Edytowanie...\",\n    \"containers.Edit.information\": \"Informacje\",\n    \"containers.Edit.information.by\": \"Przez\",\n    \"containers.Edit.information.created\": \"Stworzony\",\n    \"containers.Edit.information.draftVersion\": \"wersja szkicu\",\n    \"containers.Edit.information.editing\": \"Edytowanie\",\n    \"containers.Edit.information.lastUpdate\": \"Ostatnia aktualizacja\",\n    \"containers.Edit.information.publishedVersion\": \"wersja publikacji\",\n    \"containers.Edit.pluginHeader.title.new\": \"Nowy wpis\",\n    \"containers.Edit.reset\": \"Wyczyść\",\n    \"containers.Edit.returnList\": \"Wróć do listy\",\n    \"containers.Edit.seeDetails\": \"Szczegóły\",\n    \"containers.Edit.submit\": \"Prześlij\",\n    \"containers.EditSettingsView.modal-form.edit-field\": \"Edytuj pole\",\n    \"containers.EditView.add.new-entry\": \"Dodaj wpis\",\n    \"containers.EditView.notification.errors\": \"Formularz zawiera błędy\",\n    \"containers.Home.introduction\": \"Aby edytować wpisy przejdź do odpowiedniego linku w menu po lewej. Ten plugin nie ma odpowiedniego sposobu na edytowanie ustawień i nadal jest w trakcie rozwijania.\",\n    \"containers.Home.pluginHeaderDescription\": \"Zarządzaj swoimi danymi za pomocą potężnego i pięknego interfejsu.\",\n    \"containers.Home.pluginHeaderTitle\": \"Treści\",\n    \"containers.List.draft\": \"Szkic\",\n    \"containers.List.errorFetchRecords\": \"Błąd\",\n    \"containers.List.published\": \"Opublikowany\",\n    \"containers.list.displayedFields\": \"Wyświetlone atrybuty\",\n    \"containers.list.items\": \"{number, plural, =0 {items} one {item} other {items}}\",\n    \"containers.list.table-headers.publishedAt\": \"Stan\",\n    \"containers.ListSettingsView.modal-form.edit-label\": \"Edytuj etykietę\",\n    \"containers.SettingPage.add.field\": \"Wstaw inne pole\",\n    \"containers.SettingPage.attributes\": \"Pola atrybutów\",\n    \"containers.SettingPage.attributes.description\": \"Zdefiniuj kolejność atrybutów\",\n    \"containers.SettingPage.editSettings.description\": \"Przeciągnij i upuś pola by zbudować układ\",\n    \"containers.SettingPage.editSettings.entry.title\": \"Tytuł wpisu\",\n    \"containers.SettingPage.editSettings.entry.title.description\": \"Ustaw wyświetlane pole swojego wpisu\",\n    \"containers.SettingPage.editSettings.relation-field.description\": \"Ustaw wyświetlane pole w obydwu widokach listy\",\n    \"containers.SettingPage.editSettings.title\": \"Edycja (ustawienia)\",\n    \"containers.SettingPage.layout\": \"Układ\",\n    \"containers.SettingPage.listSettings.description\": \"Skonfiguruj opcje dla tego modelu\",\n    \"containers.SettingPage.listSettings.title\": \"Lista (ustawienia)\",\n    \"containers.SettingPage.pluginHeaderDescription\": \"Skonfiguruj konkretne ustawienia tego modelu\",\n    \"containers.SettingPage.settings\": \"Ustawienia\",\n    \"containers.SettingPage.view\": \"Widok\",\n    \"containers.SettingViewModel.pluginHeader.title\": \"Menedżer treści  - {name}\",\n    \"containers.SettingsPage.Block.contentType.description\": \"Skonfiguruj konkretne ustawienia\",\n    \"containers.SettingsPage.Block.contentType.title\": \"Typy Kolekcji\",\n    \"containers.SettingsPage.Block.generalSettings.description\": \"Skonfiguruj domyślne opcje dla twoich typów kolekcji\",\n    \"containers.SettingsPage.Block.generalSettings.title\": \"Ogólne\",\n    \"containers.SettingsPage.pluginHeaderDescription\": \"Skonfiguruj domyślne opcje wszystkich twoich modeli\",\n    \"containers.SettingsView.list.subtitle\": \"Skonfiguruj układ i wyświetlanie modeli i grup\",\n    \"containers.SettingsView.list.title\": \"Wyświetl ustawienia\",\n    \"edit-settings-view.link-to-ctb.components\": \"Edytuj komponent\",\n    \"edit-settings-view.link-to-ctb.content-types\": \"Edytuj\",\n    \"emptyAttributes.button\": \"Przejdź do konstruktora modeli\",\n    \"emptyAttributes.description\": \"Dodaj swoje pierwszy atrybut do modelu\",\n    \"emptyAttributes.title\": \"Nie ma jeszcze żadnych atrybutów\",\n    \"error.attribute.key.taken\": \"Ta wartość już istnieje\",\n    \"error.attribute.sameKeyAndName\": \"Nie mogą być takie same\",\n    \"error.attribute.taken\": \"Atrybut o tej nazwie już istnieje\",\n    \"error.contentTypeName.taken\": \"Ta nazwa już istnieje\",\n    \"error.model.fetch\": \"Wystąpił błąd podczas pobierania konfiguracji modelów.\",\n    \"error.record.create\": \"Wystąpił błąd podczas tworzenia rekordu.\",\n    \"error.record.delete\": \"Wystąpił błąd podczas usuwania rekordu.\",\n    \"error.record.fetch\": \"Wystąpił błąd podczas pobierania rekordu.\",\n    \"error.record.update\": \"Wystąpił błąd podczas zmiany rekordu.\",\n    \"error.records.count\": \"Wystąpił błąd podczas liczenia rekordów.\",\n    \"error.records.fetch\": \"Wystąpił błąd podczas pobierania rekordów.\",\n    \"error.schema.generation\": \"Wystąpił błąd podczas generowania schematu.\",\n    \"error.validation.json\": \"To nie jest JSON\",\n    \"error.validation.max\": \"Wartość jest za wysoka.\",\n    \"error.validation.maxLength\": \"Wartość jest za długa.\",\n    \"error.validation.min\": \"Wartość jest za niska.\",\n    \"error.validation.minLength\": \"Wartość jest za krótka.\",\n    \"error.validation.minSupMax\": \"Nie może być większa\",\n    \"error.validation.regex\": \"Wartość nie jest zgodna z wymaganym wzorcem.\",\n    \"error.validation.required\": \"Wpisanie wartości dla tego atrybutu jest wymagane.\",\n    \"form.Input.bulkActions\": \"Włącz akcje masowe\",\n    \"form.Input.defaultSort\": \"Domyślny atrybut sortowania\",\n    \"form.Input.description\": \"Opis\",\n    \"form.Input.description.placeholder\": \"Nazwa wyświetlana\",\n    \"form.Input.editable\": \"Edytowalne pole\",\n    \"form.Input.filters\": \"Włącz filtry\",\n    \"form.Input.label\": \"Etykieta\",\n    \"form.Input.label.inputDescription\": \"Ta wartość nadpisuje etykietę wyświetlaną w nagłówku tabeli\",\n    \"form.Input.pageEntries\": \"Wpisy na stronę\",\n    \"form.Input.pageEntries.inputDescription\": \"Uwaga: Możesz zmienić tę wartość na stronie ustawień modeli.\",\n    \"form.Input.placeholder\": \"Placeholder\",\n    \"form.Input.placeholder.placeholder\": \"Moja wartość\",\n    \"form.Input.search\": \"Włącz wyszukiwanie\",\n    \"form.Input.search.field\": \"Włącz wyszukiwanie po tym polu\",\n    \"form.Input.sort.field\": \"Włącz sortowanie po tym polu\",\n    \"form.Input.sort.order\": \"Domyślne sortowanie\",\n    \"form.Input.wysiwyg\": \"Wyświetl jako edytor WYSIWYG\",\n    \"global.displayedFields\": \"Wyświetlane pola\",\n    groups: groups,\n    \"groups.numbered\": \"Grupy ({number})\",\n    \"header.name\": \"Zawartość\",\n    \"link-to-ctb\": \"Edytuj model\",\n    models: models,\n    \"models.numbered\": \"Modele ({number})\",\n    \"notification.error.displayedFields\": \"Co najmniej jedno pole musi być wyświetlane\",\n    \"notification.error.relationship.fetch\": \"Wystąpił błąd podczas pobierania relacji.\",\n    \"notification.info.SettingPage.disableSort\": \"Co najmniej jeden atrybut musi mieć włączoną możliwość sortowania\",\n    \"notification.info.minimumFields\": \"Musisz wyświetlić przynajmniej jedno pole\",\n    \"notification.upload.error\": \"Wystąpił bład podczas przesyłania plików\",\n    pageNotFound: pageNotFound,\n    \"pages.ListView.header-subtitle\": \"{number, plural, =0 {#} one {# } other {# }} znaleziono\",\n    \"pages.NoContentType.button\": \"Stwórz pierszy Content-Type\",\n    \"pages.NoContentType.text\": \"Nie masz jeszcze żadnej zawartości. Polecamy stworzyć pierwszy Content-Type.\",\n    \"permissions.not-allowed.create\": \"Brak uprawnień do stworzenia dokumentu\",\n    \"permissions.not-allowed.update\": \"Brak uprawnień do odczytu dokumentu\",\n    \"plugin.description.long\": \"Szybki sposób na przeglądanie, zmianę i usuwanie elementów z twojej bazy danych.\",\n    \"plugin.description.short\": \"Szybki sposób na przeglądanie, zmianę i usuwanie elementów z twojej bazy danych.\",\n    \"popover.display-relations.label\": \"Wyświetl powiązania\",\n    \"success.record.delete\": \"Usunięto\",\n    \"success.record.publish\": \"Opublikowano\",\n    \"success.record.save\": \"Zapisano\",\n    \"success.record.unpublish\": \"Cofnięto publikację\",\n    \"utils.data-loaded\": \"Udało się załadować wpis/wpisy.\",\n    \"apiError.This attribute must be unique\": \"{field} musi być unikalne\",\n    \"popUpWarning.warning.publish-question\": \"Czy nadal chcesz to opublikować?\",\n    \"popUpwarning.warning.has-draft-relations.button-confirm\": \"Tak, opublikuj\"\n};\n\nexport { pl as default, groups, models, pageNotFound };\n//# sourceMappingURL=pl.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,SAAS;AACb,IAAI,SAAS;AACb,IAAI,eAAe;AACnB,IAAI,KAAK;AAAA,EACL,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,uCAAuC;AAAA,EACvC,UAAU;AAAA,EACV,+BAA+B;AAAA,EAC/B,gCAAgC;AAAA,EAChC,+BAA+B;AAAA,EAC/B,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,gDAAgD;AAAA,EAChD,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,wCAAwC;AAAA,EACxC,6CAA6C;AAAA,EAC7C,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,gCAAgC;AAAA,EAChC,yCAAyC;AAAA,EACzC,4DAA4D;AAAA,EAC5D,+DAA+D;AAAA,EAC/D,0DAA0D;AAAA,EAC1D,2DAA2D;AAAA,EAC3D,sCAAsC;AAAA,EACtC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,uCAAuC;AAAA,EACvC,mCAAmC;AAAA,EACnC,gDAAgD;AAAA,EAChD,iCAAiC;AAAA,EACjC,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,yEAAyE;AAAA,EACzE,yEAAyE;AAAA,EACzE,qDAAqD;AAAA,EACrD,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,gCAAgC;AAAA,EAChC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,uCAAuC;AAAA,EACvC,+BAA+B;AAAA,EAC/B,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,uCAAuC;AAAA,EACvC,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,6BAA6B;AAAA,EAC7B,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA,EAC9B,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,6BAA6B;AAAA,EAC7B,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,kCAAkC;AAAA,EAClC,uCAAuC;AAAA,EACvC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,yBAAyB;AAAA,EACzB,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,qDAAqD;AAAA,EACrD,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,gCAAgC;AAAA,EAChC,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,6CAA6C;AAAA,EAC7C,qDAAqD;AAAA,EACrD,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,+DAA+D;AAAA,EAC/D,kEAAkE;AAAA,EAClE,6CAA6C;AAAA,EAC7C,iCAAiC;AAAA,EACjC,mDAAmD;AAAA,EACnD,6CAA6C;AAAA,EAC7C,kDAAkD;AAAA,EAClD,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,kDAAkD;AAAA,EAClD,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,6DAA6D;AAAA,EAC7D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,yCAAyC;AAAA,EACzC,sCAAsC;AAAA,EACtC,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,0BAA0B;AAAA,EAC1B,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,qCAAqC;AAAA,EACrC,0BAA0B;AAAA,EAC1B,2CAA2C;AAAA,EAC3C,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B;AAAA,EACA,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,eAAe;AAAA,EACf;AAAA,EACA,mBAAmB;AAAA,EACnB,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B;AAAA,EACA,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,4BAA4B;AAAA,EAC5B,qBAAqB;AAAA,EACrB,0CAA0C;AAAA,EAC1C,yCAAyC;AAAA,EACzC,2DAA2D;AAC/D;", "names": []}