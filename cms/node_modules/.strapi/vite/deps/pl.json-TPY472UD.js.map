{"version": 3, "sources": ["../../../@strapi/admin/dist/admin/admin/src/translations/pl.json.mjs"], "sourcesContent": ["var Analytics = \"Analityka\";\nvar Documentation = \"Dokumentacja\";\nvar Email = \"E-mail\";\nvar Password = \"Hasło\";\nvar Provider = \"Dostawca\";\nvar ResetPasswordToken = \"Token odzyskiwania hasła\";\nvar Role = \"Rola\";\nvar Username = \"Nazwa użytkownika\";\nvar Users = \"Użytkownicy\";\nvar anErrorOccurred = \"Ups! Coś poszło nie tak. Spróbuj ponownie.\";\nvar clearLabel = \"Wyczyść\";\nvar or = \"LUB\";\nvar skipToContent = \"Przeskocz do zawartości\";\nvar submit = \"Wyślij\";\nvar pl = {\n    Analytics: Analytics,\n    \"Auth.components.Oops.text\": \"Twoje konto zostało zablokowane\",\n    \"Auth.components.Oops.text.admin\": \"Jeśli to błąd, skontaktuj się z administratorem.\",\n    \"Auth.components.Oops.title\": \"Ups...\",\n    \"Auth.form.button.forgot-password\": \"Wyślij e-mail\",\n    \"Auth.form.button.go-home\": \"PRZEJDŹ DO STRONY GŁÓWNEJ\",\n    \"Auth.form.button.login\": \"Zaloguj się\",\n    \"Auth.form.button.login.providers.error\": \"Nie możemy połączyć Cię za pośrednictwem wybranego dostawcy.\",\n    \"Auth.form.button.login.strapi\": \"Zaloguj się przez Strapi\",\n    \"Auth.form.button.password-recovery\": \"Odzyskiwanie hasła\",\n    \"Auth.form.button.register\": \"Zaczynajmy\",\n    \"Auth.form.confirmPassword.label\": \"Potwierdź hasło\",\n    \"Auth.form.currentPassword.label\": \"Obecne hasło\",\n    \"Auth.form.email.label\": \"E-mail\",\n    \"Auth.form.email.placeholder\": \"np. <EMAIL>\",\n    \"Auth.form.error.blocked\": \"Twoje konto zostało zablokowane przez administratora.\",\n    \"Auth.form.error.code.provide\": \"Podano nieprawidłowy kod.\",\n    \"Auth.form.error.confirmed\": \"Adres e-mail Twojego konta nie został potwierdzony.\",\n    \"Auth.form.error.email.invalid\": \"Ten e-mail jest nieprawidłowy.\",\n    \"Auth.form.error.email.provide\": \"Podaj swoją nazwę użytkownika lub adres e-mail.\",\n    \"Auth.form.error.email.taken\": \"Adres e-mail jest już zajęty.\",\n    \"Auth.form.error.invalid\": \"Identyfikator lub hasło jest nieprawidłowe.\",\n    \"Auth.form.error.params.provide\": \"Podano nieprawidłowe parametry.\",\n    \"Auth.form.error.password.format\": \"Twoje hasło nie może zawierać symbolu `$` więcej niż trzy razy.\",\n    \"Auth.form.error.password.local\": \"Ten użytkownik nigdy nie ustawił hasła lokalnego, zaloguj się za pośrednictwem dostawcy użytego podczas tworzenia konta.\",\n    \"Auth.form.error.password.matching\": \"Hasła różnią się od siebie.\",\n    \"Auth.form.error.password.provide\": \"Proszę podać swoje hasło.\",\n    \"Auth.form.error.ratelimit\": \"Zbyt wiele prób, spróbuj ponownie za minutę.\",\n    \"Auth.form.error.user.not-exist\": \"Ten adres e-mail nie istnieje.\",\n    \"Auth.form.error.username.taken\": \"Nazwa użytkownika jest już zajęta.\",\n    \"Auth.form.firstname.label\": \"Imię\",\n    \"Auth.form.firstname.placeholder\": \"Jan\",\n    \"Auth.form.forgot-password.email.label\": \"Wpisz swój e-mail\",\n    \"Auth.form.forgot-password.email.label.success\": \"E-mail pomyślnie wysłany do\",\n    \"Auth.form.lastname.label\": \"Nazwisko\",\n    \"Auth.form.lastname.placeholder\": \"Kowalski\",\n    \"Auth.form.password.hide-password\": \"Ukryj hasło\",\n    \"Auth.form.password.hint\": \"Minimum 8 znaków, 1 z dużej litery, 1 z małej litery i 1 cyfra\",\n    \"Auth.form.password.show-password\": \"Pokaż hasło\",\n    \"Auth.form.register.news.label\": \"Informuj mnie na bieżąco o nowych funkcjach i nadchodzących ulepszeniach (robiąc to, akceptujesz postanowienia zawarte w niniejszych dokumentach - {terms} i {policy}).\",\n    \"Auth.form.register.subtitle\": \"Dane logowania uzywane są tylko do uwierzytelniania w Strapi. Wszystkie zapisane dane będą przechowywane w twojej bazie danych.\",\n    \"Auth.form.rememberMe.label\": \"Zapamiętaj mnie\",\n    \"Auth.form.username.label\": \"Nazwa użytkownika\",\n    \"Auth.form.username.placeholder\": \"Jan Kowalski\",\n    \"Auth.form.welcome.subtitle\": \"Zaloguj się do swojego konta\",\n    \"Auth.form.welcome.title\": \"Witaj w Strapi!\",\n    \"Auth.link.forgot-password\": \"Zapomniałeś hasła?\",\n    \"Auth.link.ready\": \"Chcesz się zalogować?\",\n    \"Auth.link.signin\": \"Zaloguj\",\n    \"Auth.link.signin.account\": \"Posiadasz już konto?\",\n    \"Auth.login.sso.divider\": \"Lub zaloguj się za pomocą\",\n    \"Auth.login.sso.loading\": \"Ładowanie dostawców...\",\n    \"Auth.login.sso.subtitle\": \"Zaloguj się do twojego konta za pomocą SSO\",\n    \"Auth.privacy-policy-agreement.policy\": \"polityka prywatności\",\n    \"Auth.privacy-policy-agreement.terms\": \"warunki\",\n    \"Auth.reset-password.title\": \"Zresetuj hasło\",\n    \"Content Manager\": \"Menedżer treści\",\n    \"Content Type Builder\": \"Kreator typów treści\",\n    Documentation: Documentation,\n    Email: Email,\n    \"Files Upload\": \"Przesyłanie plików\",\n    \"HomePage.head.title\": \"Strona główna\",\n    \"HomePage.roadmap\": \"Zobacz naszą roadmapę\",\n    \"HomePage.welcome.congrats\": \"Gratulacje!\",\n    \"HomePage.welcome.congrats.content\": \"Jesteś zalogowany jako pierwszy administrator. Aby odkryć potężne funkcje oferowane przez Strapi,\",\n    \"HomePage.welcome.congrats.content.bold\": \"zachęcamy do utworzenia pierwszego typu kolekcji.\",\n    \"Media Library\": \"Biblioteka mediów\",\n    \"New entry\": \"Nowy wpis\",\n    Password: Password,\n    Provider: Provider,\n    ResetPasswordToken: ResetPasswordToken,\n    Role: Role,\n    \"Roles & Permissions\": \"Role i Uprawnienia\",\n    \"Roles.ListPage.notification.delete-all-not-allowed\": \"Niektórych ról nie można usunąć, ponieważ są powiązane z użytkownikami\",\n    \"Roles.ListPage.notification.delete-not-allowed\": \"Nie można usunąć roli, jeśli jest powiązana z użytkownikami\",\n    \"Roles.RoleRow.select-all\": \"Wybierz {name} do grupowych czynności\",\n    \"Roles.RoleRow.user-count\": \"{number, plural, =0 {#  } one {# } other {# }}\",\n    \"Roles.components.List.empty.withSearch\": \"Brak roli odpowiadającej wyszukiwaniu ({search}) ...\",\n    \"Settings.PageTitle\": \"Ustawienia - {name}\",\n    \"Settings.apiTokens.addFirstToken\": \"Dodaj pierwszy API Token\",\n    \"Settings.apiTokens.addNewToken\": \"Dodaj nowy API Token\",\n    \"Settings.tokens.copy.editMessage\": \"Dla bezpieczeństwa, możesz tylko raz zobaczyć swój token.\",\n    \"Settings.tokens.copy.editTitle\": \"Ten token nie jest już dostępny.\",\n    \"Settings.tokens.copy.lastWarning\": \"Pamiętaj żeby skopiować token, nie będziesz w stanie kolejny raz go zobaczyć!\",\n    \"Settings.apiTokens.create\": \"Stwórz nowy API Token\",\n    \"Settings.apiTokens.description\": \"Lista wygenerowanych tokenów pozwalających korzystać z API\",\n    \"Settings.apiTokens.emptyStateLayout\": \"Brak zawartości...\",\n    \"Settings.tokens.notification.copied\": \"Token skopiowany do schowka\",\n    \"Settings.apiTokens.title\": \"API Tokeny\",\n    \"Settings.tokens.types.full-access\": \"Full access\",\n    \"Settings.tokens.types.read-only\": \"Read-only\",\n    \"Settings.application.description\": \"Globalne informacje dotyczące panelu administratora\",\n    \"Settings.application.edition-title\": \"obecny wydanie\",\n    \"Settings.application.get-help\": \"Uzyskaj pomoc\",\n    \"Settings.application.link-pricing\": \"Zobacz cennik\",\n    \"Settings.application.link-upgrade\": \"Aktualizuj panel admina\",\n    \"Settings.application.node-version\": \"wersja node\",\n    \"Settings.application.strapi-version\": \"wersja strapi\",\n    \"Settings.application.strapiVersion\": \"wersja strapi\",\n    \"Settings.application.title\": \"Ogólne\",\n    \"Settings.error\": \"Błąd\",\n    \"Settings.global\": \"Ustawienia Globalne\",\n    \"Settings.permissions\": \"Panel administracyjny\",\n    \"Settings.permissions.category\": \"Ustawienia uprawnień dla {category}\",\n    \"Settings.permissions.category.plugins\": \"Ustawienia uprawnień dla pluginu {category} \",\n    \"Settings.permissions.conditions.anytime\": \"W dowolnym momencie\",\n    \"Settings.permissions.conditions.apply\": \"Zastosuj\",\n    \"Settings.permissions.conditions.can\": \"Może\",\n    \"Settings.permissions.conditions.conditions\": \"Definiować warunki\",\n    \"Settings.permissions.conditions.links\": \"Linki\",\n    \"Settings.permissions.conditions.no-actions\": \"Najpierw musisz wybrać akcje (tworzenie, odczytywanie, aktualizowanie, ...) przed zdefiniowaniem dla nich warunków.\",\n    \"Settings.permissions.conditions.none-selected\": \"W dowolnym momencie\",\n    \"Settings.permissions.conditions.or\": \"LUB\",\n    \"Settings.permissions.conditions.when\": \"Kiedy\",\n    \"Settings.permissions.select-all-by-permission\": \"Wybierz wszystkie {label} uprawnienia\",\n    \"Settings.permissions.select-by-permission\": \"Wybierz {label} uprawnienie\",\n    \"Settings.permissions.users.create\": \"Utwórz nowego użytkownika\",\n    \"Settings.permissions.users.email\": \"E-mail\",\n    \"Settings.permissions.users.firstname\": \"Imię\",\n    \"Settings.permissions.users.lastname\": \"Nazwisko\",\n    \"Settings.permissions.users.form.sso\": \"Połącz z logowaniem jednokrotnym (SSO)\",\n    \"Settings.permissions.users.form.sso.description\": \"Po włączeniu (ON) użytkownicy mogą logować się za pomocą logowania jednokrotnego (SSO)\",\n    \"Settings.permissions.users.listview.header.subtitle\": \"Wszyscy użytkownicy posiadający dostęp do panelu admina\",\n    \"Settings.permissions.users.tabs.label\": \"Uprawnienia\",\n    \"Settings.profile.form.notify.data.loaded\": \"Dane twojego profilu zostały załadowane\",\n    \"Settings.profile.form.section.experience.clear.select\": \"Usuń wybór języka interfejsu\",\n    \"Settings.profile.form.section.experience.here\": \"tutaj\",\n    \"Settings.profile.form.section.experience.interfaceLanguage\": \"Język aplikacji\",\n    \"Settings.profile.form.section.experience.interfaceLanguage.hint\": \"Wyświetla aplikację w wybranym języku.\",\n    \"Settings.profile.form.section.experience.interfaceLanguageHelp\": \"Zmiany preferencji będą miały zastosowanie tylko do tego profilu. Więcej informacji {tutaj}.\",\n    \"Settings.profile.form.section.experience.mode.label\": \"Motyw aplikacji\",\n    \"Settings.profile.form.section.experience.mode.hint\": \"Wyświetla aplikację w wybranym motywie.\",\n    \"Settings.profile.form.section.experience.mode.option-label\": \"{name} mode\",\n    \"Settings.profile.form.section.experience.title\": \"Korzystanie\",\n    \"Settings.profile.form.section.head.title\": \"Profil użytkownika\",\n    \"Settings.profile.form.section.profile.page.title\": \"Strona profilu\",\n    \"Settings.roles.create.description\": \"Zdefiniuj uprawnienia nadane roli\",\n    \"Settings.roles.create.title\": \"Utwórz rolę\",\n    \"Settings.roles.created\": \"Utworzono rolę\",\n    \"Settings.roles.edit.title\": \"Edytuj rolę\",\n    \"Settings.roles.form.button.users-with-role\": \"Użytkownicy z tą rolą\",\n    \"Settings.roles.form.created\": \"Utworzono\",\n    \"Settings.roles.form.description\": \"Nazwa i opis roli\",\n    \"Settings.roles.form.permission.property-label\": \"Uprawnienia dla {label}\",\n    \"Settings.roles.form.permissions.attributesPermissions\": \"Uprawnienia do pól\",\n    \"Settings.roles.form.permissions.create\": \"Tworzenie\",\n    \"Settings.roles.form.permissions.delete\": \"Usuwanie\",\n    \"Settings.roles.form.permissions.publish\": \"Publikowanie\",\n    \"Settings.roles.form.permissions.read\": \"Odczyt\",\n    \"Settings.roles.form.permissions.update\": \"Aktualizowanie\",\n    \"Settings.roles.list.button.add\": \"Dodaj nową rolę\",\n    \"Settings.roles.list.description\": \"Lista ról\",\n    \"Settings.roles.title.singular\": \"rola\",\n    \"Settings.sso.description\": \"Skonfiguruj ustawienia funkcji logowania jednokrotnego (SSO).\",\n    \"Settings.sso.form.defaultRole.description\": \"Połączy to nowego uwierzytelnionego użytkownika do wybranej roli\",\n    \"Settings.sso.form.defaultRole.description-not-allowed\": \"Musisz mieć uprawnienia do odczytu ról administratora\",\n    \"Settings.sso.form.defaultRole.label\": \"Domyślna rola\",\n    \"Settings.sso.form.registration.description\": \"Utwórz nowego użytkownika przy logowaniu SSO, jeśli konto nie istnieje\",\n    \"Settings.sso.form.registration.label\": \"Automatyczna rejestracja\",\n    \"Settings.sso.title\": \"Jednokrotne logowanie (SSO)\",\n    \"Settings.webhooks.create\": \"Utwórz webhook\",\n    \"Settings.webhooks.create.header\": \"Utwórz nowy nagłówek\",\n    \"Settings.webhooks.created\": \"Utworzono webhook\",\n    \"Settings.webhooks.event.publish-tooltip\": \"To zdarzenie istnieje tylko dla treści z włączonym systemem wersji roboczej/publikacji\",\n    \"Settings.webhooks.events.create\": \"Utwórz\",\n    \"Settings.webhooks.events.update\": \"Edytuj\",\n    \"Settings.webhooks.form.events\": \"Zdarzenia\",\n    \"Settings.webhooks.form.headers\": \"Nagłówki\",\n    \"Settings.webhooks.form.url\": \"URL\",\n    \"Settings.webhooks.headers.remove\": \"Usuń rząd {number}\",\n    \"Settings.webhooks.key\": \"Klucz\",\n    \"Settings.webhooks.list.button.add\": \"Dodaj nowy webhook\",\n    \"Settings.webhooks.list.description\": \"Otrzymaj powiadomienia o zmianach POST.\",\n    \"Settings.webhooks.list.empty.description\": \"Nie ma jeszcze żadnych webhooków\",\n    \"Settings.webhooks.list.empty.link\": \"Zobacz naszą dokumentację\",\n    \"Settings.webhooks.list.empty.title\": \"Nie ma jeszcze żadnych webhooków\",\n    \"Settings.webhooks.list.th.actions\": \"akcje\",\n    \"Settings.webhooks.list.th.status\": \"stan\",\n    \"Settings.webhooks.singular\": \"webhook\",\n    \"Settings.webhooks.title\": \"Webhooki\",\n    \"Settings.webhooks.to.delete\": \"{webhooksToDeleteLength, plural, one {# } other {# }} wybrano\",\n    \"Settings.webhooks.trigger\": \"Uruchom\",\n    \"Settings.webhooks.trigger.cancel\": \"Anuluj wyzwalacz\",\n    \"Settings.webhooks.trigger.pending\": \"W oczekiwaniu...\",\n    \"Settings.webhooks.trigger.save\": \"Zapisz, aby uruchomić\",\n    \"Settings.webhooks.trigger.success\": \"Powodzenie!\",\n    \"Settings.webhooks.trigger.success.label\": \"Uruchomiono poprawnie\",\n    \"Settings.webhooks.trigger.test\": \"Testowy-wyzwalacz\",\n    \"Settings.webhooks.trigger.title\": \"Zapisz przed wyzwoleniem\",\n    \"Settings.webhooks.value\": \"Wartość\",\n    \"Usecase.back-end\": \"Back-end developer\",\n    \"Usecase.button.skip\": \"Pomiń to pytanie\",\n    \"Usecase.content-creator\": \"Content Creator\",\n    \"Usecase.front-end\": \"Front-end developer\",\n    \"Usecase.full-stack\": \"Full-stack developer\",\n    \"Usecase.input.work-type\": \"Jaki rodzaj pracy wykonujesz?\",\n    \"Usecase.notification.success.project-created\": \"Projekt został utworzony\",\n    \"Usecase.other\": \"Inny\",\n    \"Usecase.title\": \"Opowiedz nam trochę o sobie\",\n    Username: Username,\n    Users: Users,\n    \"Users & Permissions\": \"Użytkownicy i Uprawnienia\",\n    \"Users.components.List.empty\": \"Brak użytkowników...\",\n    \"Users.components.List.empty.withFilters\": \"Brak użytkowników z zastosowanymi filtrami...\",\n    \"Users.components.List.empty.withSearch\": \"Brak użytkowników odpowiadających wyszukiwaniu ({search})...\",\n    \"admin.pages.MarketPlacePage.head\": \"Sklep - Pluginy\",\n    \"admin.pages.MarketPlacePage.offline.title\": \"Jesteś offline\",\n    \"admin.pages.MarketPlacePage.offline.subtitle\": \"Musisz być połączony z internetem żeby skorzystać ze sklepu Strapi.\",\n    \"admin.pages.MarketPlacePage.plugin.copy\": \"Skopiuj instalację\",\n    \"admin.pages.MarketPlacePage.plugin.copy.success\": \"Polecenie instalacji gotowe do użycia w twoim terminalu\",\n    \"admin.pages.MarketPlacePage.plugin.info\": \"Dowiedz się więcej\",\n    \"admin.pages.MarketPlacePage.plugin.info.label\": \"Dowiedz się więcej o {pluginName}\",\n    \"admin.pages.MarketPlacePage.plugin.info.text\": \"Informacje\",\n    \"admin.pages.MarketPlacePage.plugin.installed\": \"Zainstalowano\",\n    \"admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi\": \"Stworzony przez Strapi\",\n    \"admin.pages.MarketPlacePage.plugin.tooltip.verified\": \"Zweryfikowany przez Strapi\",\n    \"admin.pages.MarketPlacePage.search.clear\": \"Wyczyść wyszukiwarkę\",\n    \"admin.pages.MarketPlacePage.search.empty\": \"Brak wyników dla \\\"{target}\\\"\",\n    \"admin.pages.MarketPlacePage.search.placeholder\": \"Szukaj pluginu\",\n    \"admin.pages.MarketPlacePage.submit.plugin.link\": \"Wyślij swój plugin\",\n    \"admin.pages.MarketPlacePage.subtitle\": \"Wykorzystaj Strapi lepiej\",\n    \"admin.pages.MarketPlacePage.missingPlugin.title\": \"Brakuje pluginu?\",\n    \"admin.pages.MarketPlacePage.missingPlugin.description\": \"Powiedz nam jakiego pluginu szukasz, a my damy znać o tym naszym developerom w razie gdyby szukali inspiracji!\",\n    anErrorOccurred: anErrorOccurred,\n    \"app.component.CopyToClipboard.label\": \"Skopiuj do schowka\",\n    \"app.component.search.label\": \"Szukaj {target}\",\n    \"app.component.table.duplicate\": \"Duplikuj {target}\",\n    \"app.component.table.edit\": \"Edytuj {target}\",\n    \"app.component.table.select.one-entry\": \"Wybierz {target}\",\n    \"app.components.BlockLink.blog\": \"Blog\",\n    \"app.components.BlockLink.blog.content\": \"Czytaj najnowsze wiadomości na temat Strapi.\",\n    \"app.components.BlockLink.code\": \"Przykłady\",\n    \"app.components.BlockLink.code.content\": \"Ucz się poprzez testowanie prawdziwych projektów tworzonych przez naszą społeczność.\",\n    \"app.components.BlockLink.documentation.content\": \"Odkryj kluczowe pojęcia, wskazówki i instrukcje.\",\n    \"app.components.BlockLink.tutorial\": \"Tutoriale\",\n    \"app.components.BlockLink.tutorial.content\": \"Podążaj za instrukcjami krok po kroku żeby użyć i dostosować Strapi.\",\n    \"app.components.Button.cancel\": \"Anuluj\",\n    \"app.components.Button.confirm\": \"Potwierdź\",\n    \"app.components.Button.reset\": \"Resetuj\",\n    \"app.components.ComingSoonPage.comingSoon\": \"Wkrótce\",\n    \"app.components.ConfirmDialog.title\": \"Potwierdzenie\",\n    \"app.components.DownloadInfo.download\": \"Pobieranie w toku...\",\n    \"app.components.DownloadInfo.text\": \"Może to chwilę potrwać. Dziękujemy za cierpliwość.\",\n    \"app.components.EmptyAttributes.title\": \"Nie ma jeszcze żadnych pól\",\n    \"app.components.EmptyStateLayout.content-document\": \"Brak zawartości\",\n    \"app.components.EmptyStateLayout.content-permissions\": \"Nie masz dostępu do tej zawartości\",\n    \"app.components.GuidedTour.CM.create.content\": \"<p>Twórz i zarządzaj zawartością tutaj w Menedżerze Treści.</p><p>Przykład: Weźmy pod uwagę przykład z blogiem, możesz napisać aktykuł, zapisać i opublikować go kiedy tylko chcesz.</p><p>💡 Szybka wskazówka - Nie zapomnij kliknąć opublikuj, w treści którą tworzysz.</p>\",\n    \"app.components.GuidedTour.CM.create.title\": \"⚡️ Stwórz treść\",\n    \"app.components.GuidedTour.CM.success.content\": \"<p>Super, został ostatni krok!</p><b>🚀 Zobacz materiały w praktyce</b>\",\n    \"app.components.GuidedTour.CM.success.cta.title\": \"Przetestuj API\",\n    \"app.components.GuidedTour.CM.success.title\": \"Krok 2: Ukończony ✅\",\n    \"app.components.GuidedTour.CTB.create.content\": \"<p>Kolekcje pomagają zarządzać wieloma pozycjami, a pojedyncze typy są odpowiednie do zarządzania tylko jednym wpisem.</p> <p>Przykład: Wyobraź sobie stronę z blogiem. Tam artykuły byłyby kolekcjami, a strona główna byłaby pojedynczym typem.</p>\",\n    \"app.components.GuidedTour.CTB.create.cta.title\": \"Stwórz kolekcję\",\n    \"app.components.GuidedTour.CTB.create.title\": \"🧠 Stwórz swoją pierwszą kolekcję\",\n    \"app.components.GuidedTour.CTB.success.content\": \"<p>Niezła robota!</p><b>⚡️ Czym chciałbyś się podzielić ze światem?</b>\",\n    \"app.components.GuidedTour.CTB.success.title\": \"Krok 1: Ukończony ✅\",\n    \"app.components.GuidedTour.apiTokens.create.content\": \"<p>Wygeneruj token aby otrzymać dostęp do treści, którą stworzyłeś.</p>\",\n    \"app.components.GuidedTour.apiTokens.create.cta.title\": \"Wygeneruj API Token\",\n    \"app.components.GuidedTour.apiTokens.create.title\": \"🚀 Zobacz materiały w praktyce\",\n    \"app.components.GuidedTour.apiTokens.success.content\": \"<p>Przetestuj treść wykonując żądanie HTTP:</p><ul><li><p>Pod URL: <light>https://'<'YOUR_DOMAIN'>'/api/'<'YOUR_CT'>'</light></p></li><li><p>Przy użyciu nagłówka: <light>Authorization: bearer '<'YOUR_API_TOKEN'>'</light></p></li></ul><p>Zerknij na <documentationLink>dokumentację</documentationLink> by poznać więcej sposobów na interakcję z treścią.</p>\",\n    \"app.components.GuidedTour.apiTokens.success.cta.title\": \"Powrót na stronę główną\",\n    \"app.components.GuidedTour.apiTokens.success.title\": \"Krok 3: Ukończony ✅\",\n    \"app.components.GuidedTour.create-content\": \"Stwórz zawartość\",\n    \"app.components.GuidedTour.home.CM.title\": \"⚡️ Czym chciałbyś się podzielić ze światem?\",\n    \"app.components.GuidedTour.home.CTB.cta.title\": \"Przejdź do kreatora typów treści\",\n    \"app.components.GuidedTour.home.CTB.title\": \"🧠 Stwórz strukturę treści\",\n    \"app.components.GuidedTour.home.apiTokens.cta.title\": \"Przetestuj API\",\n    \"app.components.GuidedTour.skip\": \"Pomiń\",\n    \"app.components.GuidedTour.title\": \"3 kroki żeby zacząć\",\n    \"app.components.HomePage.button.blog\": \"Zobacz więcej na blogu\",\n    \"app.components.HomePage.community\": \"Dołącz do społeczności\",\n    \"app.components.HomePage.community.content\": \"Porozmawiaj z członkami zespołu, współtwórcami i programistami na różnych kanałach.\",\n    \"app.components.HomePage.create\": \"Utwórz swój pierwszy typ zawartości\",\n    \"app.components.HomePage.roadmap\": \"Zobacz naszą roadmapę\",\n    \"app.components.HomePage.welcome\": \"Witaj na pokładzie 👋!\",\n    \"app.components.HomePage.welcome.again\": \"Witaj 👋\",\n    \"app.components.HomePage.welcomeBlock.content\": \"Cieszymy się, że jesteś częścią społeczności. Nieustannie poszukujemy opinii, więc zachęcamy do wysyłania nam wiadomości\",\n    \"app.components.HomePage.welcomeBlock.content.again\": \"Mamy nadzieję, że robisz postępy w swoim projekcie! Zachęcamy do zapoznania się z najnowszymi wiadomościami o Strapi. Dokładamy wszelkich starań, aby ulepszyć produkt w oparciu o Wasze opinie.\",\n    \"app.components.HomePage.welcomeBlock.content.issues\": \"problemy.\",\n    \"app.components.HomePage.welcomeBlock.content.raise\": \" lub wskazać \",\n    \"app.components.ImgPreview.hint\": \"Przeciągnij i upuść plik w tym obszarze lub {browse}, aby przesłać plik\",\n    \"app.components.ImgPreview.hint.browse\": \"przeglądaj\",\n    \"app.components.InputFile.newFile\": \"Dodaj nowy plik\",\n    \"app.components.InputFileDetails.open\": \"Otwórz w nowej karcie\",\n    \"app.components.InputFileDetails.originalName\": \"Oryginalna nazwa:\",\n    \"app.components.InputFileDetails.remove\": \"Usuń ten plik\",\n    \"app.components.InputFileDetails.size\": \"Rozmiar:\",\n    \"app.components.InstallPluginPage.Download.description\": \"Pobranie i zainstalowanie pluginu może zająć kilka sekund.\",\n    \"app.components.InstallPluginPage.Download.title\": \"Pobieranie...\",\n    \"app.components.InstallPluginPage.description\": \"Rozszerz swoją aplikację bez wysiłku.\",\n    \"app.components.LeftMenu.collapse\": \"Zwiń nawigację\",\n    \"app.components.LeftMenu.expand\": \"Rozszerz nawigację\",\n    \"app.components.LeftMenu.logout\": \"Wyloguj\",\n    \"app.components.LeftMenu.navbrand.title\": \"Strapi Dashboard\",\n    \"app.components.LeftMenu.navbrand.workplace\": \"Workplace\",\n    \"app.components.LeftMenu.trialCountdown\": \"Twoja próba się skończy {date}.\",\n    \"app.components.LeftMenuFooter.help\": \"Wsparcie\",\n    \"app.components.LeftMenuFooter.poweredBy\": \"Obsługiwane przez \",\n    \"app.components.LeftMenuLinkContainer.collectionTypes\": \"Typy kolekcji\",\n    \"app.components.LeftMenuLinkContainer.configuration\": \"Ustawienia\",\n    \"app.components.LeftMenuLinkContainer.general\": \"Ogólne\",\n    \"app.components.LeftMenuLinkContainer.noPluginsInstalled\": \"Nie zainstalowano jeszcze żadnych pluginów\",\n    \"app.components.LeftMenuLinkContainer.plugins\": \"Pluginy\",\n    \"app.components.LeftMenuLinkContainer.singleTypes\": \"Pojedyncze typy\",\n    \"app.components.ListPluginsPage.deletePlugin.description\": \"Odinstalowanie pluginu może zająć kilka sekund.\",\n    \"app.components.ListPluginsPage.deletePlugin.title\": \"Odinstalowywanie\",\n    \"app.components.ListPluginsPage.description\": \"Lista zainstalowanych pluginów.\",\n    \"app.components.ListPluginsPage.head.title\": \"Lista pluginów\",\n    \"app.components.Logout.logout\": \"Wyloguj\",\n    \"app.components.Logout.profile\": \"Profil\",\n    \"app.components.MarketplaceBanner\": \"Odkryj pluginy tworzone przez społeczność oraz wiele więcej rzeczy żeby odpalić projekt, używając Strapi.\",\n    \"app.components.MarketplaceBanner.image.alt\": \"strapi logo\",\n    \"app.components.MarketplaceBanner.link\": \"Sprawdź\",\n    \"app.components.NotFoundPage.back\": \"Powrót na stronę główną\",\n    \"app.components.NotFoundPage.description\": \"Nie znaleziono\",\n    \"app.components.Official\": \"Oficjalne\",\n    \"app.components.Onboarding.help.button\": \"Pomoc\",\n    \"app.components.Onboarding.label.completed\": \"% ukończono\",\n    \"app.components.Onboarding.title\": \"Odpal filmy szkoleniowe\",\n    \"app.components.PluginCard.Button.label.download\": \"Pobierz\",\n    \"app.components.PluginCard.Button.label.install\": \"Już zainstalowane\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed\": \"Funkcja autoReload musi być włączona. Uruchom aplikację za pomocą polecenia `yarn develop`.\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.confirm\": \"Rozumiem!\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.environment\": \"Ze względów bezpieczeństwa plugin można pobrać tylko w środowisku programistycznym.\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.title\": \"Pobieranie jest niemożliwe\",\n    \"app.components.PluginCard.compatible\": \"Zgodny z Twoją aplikacją\",\n    \"app.components.PluginCard.compatibleCommunity\": \"Zgodny ze społecznością\",\n    \"app.components.PluginCard.more-details\": \"Więcej szczegółów\",\n    \"app.components.ToggleCheckbox.off-label\": \"Nie\",\n    \"app.components.ToggleCheckbox.on-label\": \"Tak\",\n    \"app.components.Users.MagicLink.connect\": \"Wyślij ten link do użytkownika, aby mógł się połączyć.\",\n    \"app.components.Users.MagicLink.connect.sso\": \"Wyślij ten link do użytkownika, pierwsze logowanie można wykonać za pośrednictwem dostawcy SSO\",\n    \"app.components.Users.ModalCreateBody.block-title.details\": \"Szczegóły\",\n    \"app.components.Users.ModalCreateBody.block-title.roles\": \"Role użytkownika\",\n    \"app.components.Users.ModalCreateBody.block-title.roles.description\": \"Twój użytkownik może mieć jedną lub kilka ról\",\n    \"app.components.Users.SortPicker.button-label\": \"Sortuj według\",\n    \"app.components.Users.SortPicker.sortby.email_asc\": \"E-mail (od A do Z)\",\n    \"app.components.Users.SortPicker.sortby.email_desc\": \"E-mail (od Z do A)\",\n    \"app.components.Users.SortPicker.sortby.firstname_asc\": \"Imię (od A do Z)\",\n    \"app.components.Users.SortPicker.sortby.firstname_desc\": \"Imię (od Z do A)\",\n    \"app.components.Users.SortPicker.sortby.lastname_asc\": \"Nazwisko (od A do Z)\",\n    \"app.components.Users.SortPicker.sortby.lastname_desc\": \"Nazwisko (od Z do A)\",\n    \"app.components.Users.SortPicker.sortby.username_asc\": \"Nazwa użytkownika (od A do Z)\",\n    \"app.components.Users.SortPicker.sortby.username_desc\": \"Nazwa użytkownika (od Z do A)\",\n    \"app.components.listPlugins.button\": \"Dodaj nowy plugin\",\n    \"app.components.listPlugins.title.none\": \"Brak zainstalowanych pluginów\",\n    \"app.components.listPluginsPage.deletePlugin.error\": \"Wystąpił błąd podczas odinstalowywania pluginu\",\n    \"app.containers.App.notification.error.init\": \"Wystąpił błąd podczas żądania interfejsu API\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin\": \"Jeśli nie otrzymasz tego łącza, skontaktuj się z administratorem.\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.text.email\": \"Do otrzymania linku do odzyskiwania hasła może minąć kilka minut.\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.title\": \"E-mail wysłany\",\n    \"app.containers.Users.EditPage.form.active.label\": \"Aktywne\",\n    \"app.containers.Users.EditPage.header.label\": \"Edytuj {name}\",\n    \"app.containers.Users.EditPage.header.label-loading\": \"Edytuj użytkownika\",\n    \"app.containers.Users.EditPage.roles-bloc-title\": \"Role przypisane\",\n    \"app.containers.Users.ModalForm.footer.button-success\": \"Stwórz użytkownika\",\n    \"app.links.configure-view\": \"Skonfiguruj widok\",\n    \"app.page.not.found\": \"Ups! Nie możemy znaleźć strony, której szukasz...\",\n    \"app.static.links.cheatsheet\": \"Ściąga\",\n    \"app.utils.SelectOption.defaultMessage\": \" \",\n    \"app.utils.add-filter\": \"Dodaj filtr\",\n    \"app.utils.close-label\": \"Zamknij\",\n    \"app.utils.defaultMessage\": \" \",\n    \"app.utils.duplicate\": \"Duplikuj\",\n    \"app.utils.edit\": \"Edytuj\",\n    \"app.utils.errors.file-too-big.message\": \"Plik jest za duży\",\n    \"app.utils.filter-value\": \"Filtr\",\n    \"app.utils.filters\": \"Filtry\",\n    \"app.utils.notify.data-loaded\": \"{target} został załadowany\",\n    \"app.utils.placeholder.defaultMessage\": \" \",\n    \"app.utils.publish\": \"Opublikuj\",\n    \"app.utils.select-all\": \"Zaznacz wszystko\",\n    \"app.utils.select-field\": \"Zaznacz pole\",\n    \"app.utils.select-filter\": \"Zaznacz filtr\",\n    \"app.utils.unpublish\": \"Cofnij publikację\",\n    clearLabel: clearLabel,\n    \"coming.soon\": \"Ta zawartość jest aktualnie w trakcie budowy i wróci za jakiś czas!\",\n    \"component.Input.error.validation.integer\": \"Wartość ta musi być liczbą całkowitą\",\n    \"components.AutoReloadBlocker.description\": \"Uruchom Strapi za pomocą jednego z następujących poleceń:\",\n    \"components.AutoReloadBlocker.header\": \"Ten plugin wymaga funkcji przeładowania.\",\n    \"components.ErrorBoundary.title\": \"Coś poszło nie tak...\",\n    \"components.FilterOptions.FILTER_TYPES.$contains\": \"zawiera\",\n    \"components.FilterOptions.FILTER_TYPES.$containsi\": \"zawiera (wielkość liter nie ma znaczenia)\",\n    \"components.FilterOptions.FILTER_TYPES.$endsWith\": \"kończy się\",\n    \"components.FilterOptions.FILTER_TYPES.$endsWithi\": \"kończy się (wielkość liter nie ma znaczenia)\",\n    \"components.FilterOptions.FILTER_TYPES.$eq\": \"kończy się na\",\n    \"components.FilterOptions.FILTER_TYPES.$eqi\": \"kończy się na (wielkość liter nie ma znaczenia)\",\n    \"components.FilterOptions.FILTER_TYPES.$gt\": \"jest większa niż\",\n    \"components.FilterOptions.FILTER_TYPES.$gte\": \"jest większe lub równe\",\n    \"components.FilterOptions.FILTER_TYPES.$lt\": \"jest mniejsze niż\",\n    \"components.FilterOptions.FILTER_TYPES.$lte\": \"jest mniejsze lub równe\",\n    \"components.FilterOptions.FILTER_TYPES.$ne\": \"nie jest\",\n    \"components.FilterOptions.FILTER_TYPES.$nei\": \"nie jest (wielkość liter nie ma znaczenia)\",\n    \"components.FilterOptions.FILTER_TYPES.$notContains\": \"nie zawiera\",\n    \"components.FilterOptions.FILTER_TYPES.$notContainsi\": \"nie zawiera (wielkość liter nie ma znaczenia)\",\n    \"components.FilterOptions.FILTER_TYPES.$notNull\": \"nie jest null\",\n    \"components.FilterOptions.FILTER_TYPES.$null\": \"jest null\",\n    \"components.FilterOptions.FILTER_TYPES.$startsWith\": \"zaczyna się na\",\n    \"components.FilterOptions.FILTER_TYPES.$startsWithi\": \"zaczyna się na (wielkość liter nie ma znaczenia)\",\n    \"components.Input.error.attribute.key.taken\": \"Ta wartość już istnieje\",\n    \"components.Input.error.attribute.sameKeyAndName\": \"Nie mogą być równe\",\n    \"components.Input.error.attribute.taken\": \"Ta nazwa pola już istnieje\",\n    \"components.Input.error.contain.lowercase\": \"Hasło musi zawierać co najmniej jedną małą literę\",\n    \"components.Input.error.contain.number\": \"Hasło musi zawierać co najmniej jedną cyfrę\",\n    \"components.Input.error.contain.uppercase\": \"Hasło musi zawierać co najmniej jedną wielką literę\",\n    \"components.Input.error.contentTypeName.taken\": \"Ta nazwa już istnieje\",\n    \"components.Input.error.custom-error\": \"{errorMessage} \",\n    \"components.Input.error.password.noMatch\": \"Hasła nie pasują do siebie\",\n    \"components.Input.error.validation.email\": \"To nie jest e-mail\",\n    \"components.Input.error.validation.json\": \"To nie pasuje do formatu JSON\",\n    \"components.Input.error.validation.lowercase\": \"Wartość musi być zapisana małymi literami\",\n    \"components.Input.error.validation.max\": \"Wartość jest za wysoka {max}.\",\n    \"components.Input.error.validation.maxLength\": \"Wartość jest za długa {max}.\",\n    \"components.Input.error.validation.min\": \"Wartość jest za mała {min}.\",\n    \"components.Input.error.validation.minLength\": \"Wartość jest za krótka {min}.\",\n    \"components.Input.error.validation.minSupMax\": \"Wartość nie może być większa\",\n    \"components.Input.error.validation.regex\": \"Wartość nie jest zgodna z wyrażeniem regularnym.\",\n    \"components.Input.error.validation.required\": \"To pole jest wymagane.\",\n    \"components.Input.error.validation.unique\": \"Ta wartość jest już używana.\",\n    \"components.InputSelect.option.placeholder\": \"Wybierz tutaj\",\n    \"components.ListRow.empty\": \"Brak danych do wyświetlenia.\",\n    \"components.NotAllowedInput.text\": \"Brak uprawnień do wyświetlania tego pola\",\n    \"components.OverlayBlocker.description\": \"Używasz funkcji, która wymaga ponownego uruchomienia serwera. Poczekaj, aż serwer się uruchomi.\",\n    \"components.OverlayBlocker.description.serverError\": \"Serwer powinien się już zrestartować, sprawdź swoje logi w terminalu.\",\n    \"components.OverlayBlocker.title\": \"Czekam na ponowne uruchomienie...\",\n    \"components.OverlayBlocker.title.serverError\": \"Ponowne uruchomienie trwa dłużej niż oczekiwano\",\n    \"components.PageFooter.select\": \"wpisów na stronie\",\n    \"components.ProductionBlocker.description\": \"Ze względów bezpieczeństwa musimy wyłączyć ten plugin w innych środowiskach.\",\n    \"components.ProductionBlocker.header\": \"Ten plugin jest dostępna tylko w trybie deweloperskim.\",\n    \"components.Search.placeholder\": \"Szukaj...\",\n    \"components.TableHeader.sort\": \"Sortuj {label}\",\n    \"components.Wysiwyg.ToggleMode.markdown-mode\": \"Tryb Markdown\",\n    \"components.Wysiwyg.ToggleMode.preview-mode\": \"Tryb podglądu\",\n    \"components.Wysiwyg.collapse\": \"Zwiń\",\n    \"components.Wysiwyg.selectOptions.H1\": \"Tytuł H1\",\n    \"components.Wysiwyg.selectOptions.H2\": \"Tytuł H2\",\n    \"components.Wysiwyg.selectOptions.H3\": \"Tytuł H3\",\n    \"components.Wysiwyg.selectOptions.H4\": \"Tytuł H4\",\n    \"components.Wysiwyg.selectOptions.H5\": \"Tytuł H5\",\n    \"components.Wysiwyg.selectOptions.H6\": \"Tytuł H6\",\n    \"components.Wysiwyg.selectOptions.title\": \"Dodaj tytuł\",\n    \"components.WysiwygBottomControls.charactersIndicators\": \"znaków\",\n    \"components.WysiwygBottomControls.fullscreen\": \"Rozszerz\",\n    \"components.WysiwygBottomControls.uploadFiles\": \"Przeciągnij i upuść pliki, wklej ze schowka lub {browse}.\",\n    \"components.WysiwygBottomControls.uploadFiles.browse\": \"wybierz je\",\n    \"components.pagination.go-to\": \"Idź do {page}\",\n    \"components.pagination.go-to-next\": \"Idź do następnej strony\",\n    \"components.pagination.go-to-previous\": \"Idź do poprzedniej strony\",\n    \"components.pagination.remaining-links\": \"Dodaj {number} inne linki\",\n    \"components.popUpWarning.button.cancel\": \"Nie, anuluj\",\n    \"components.popUpWarning.button.confirm\": \"Tak, potwierdź\",\n    \"components.popUpWarning.message\": \"Czy na pewno chcesz to usunąć?\",\n    \"components.popUpWarning.title\": \"Proszę potwierdzić\",\n    \"form.button.continue\": \"Dalej\",\n    \"form.button.done\": \"Gotowe\",\n    \"global.actions\": \"Akcje\",\n    \"global.back\": \"Powrót\",\n    \"global.change-password\": \"Zmień hasło\",\n    \"global.content-manager\": \"Menedżer treści\",\n    \"global.continue\": \"Dalej\",\n    \"global.delete\": \"Usuń\",\n    \"global.delete-target\": \"Usuń {target}\",\n    \"global.description\": \"Opis\",\n    \"global.details\": \"Szczegóły\",\n    \"global.disabled\": \"Wyłączony\",\n    \"global.documentation\": \"Dokumentacja\",\n    \"global.enabled\": \"Włączony\",\n    \"global.finish\": \"Zapisz\",\n    \"global.marketplace\": \"Sklep\",\n    \"global.name\": \"Nazwa\",\n    \"global.none\": \"None\",\n    \"global.password\": \"Hasło\",\n    \"global.plugins\": \"Pluginy\",\n    \"global.profile\": \"Profil\",\n    \"global.prompt.unsaved\": \"Czy na pewno chcesz opuścić tę stronę? Wszystkie twoje modyfikacje zostaną utracone\",\n    \"global.reset-password\": \"Zresetuj hasło\",\n    \"global.roles\": \"Role\",\n    \"global.save\": \"Zapisz\",\n    \"global.see-more\": \"Zobacz więcej\",\n    \"global.select\": \"Wybierz\",\n    \"global.select-all-entries\": \"Wybierz wszystkie wpisy\",\n    \"global.settings\": \"Ustawienia\",\n    \"global.type\": \"Typ\",\n    \"global.users\": \"Użytkownicy\",\n    \"notification.contentType.relations.conflict\": \"Typ treści ma sprzeczne relacje\",\n    \"notification.default.title\": \"Informacja:\",\n    \"notification.error\": \"Wystąpił bład\",\n    \"notification.error.layout\": \"Nie udało się pobrać układu\",\n    \"notification.form.error.fields\": \"Ten formularz zawiera błędy\",\n    \"notification.form.success.fields\": \"Zapisano zmiany\",\n    \"notification.link-copied\": \"Link został skopiowany do schowka\",\n    \"notification.permission.not-allowed-read\": \"Nie masz uprawnień, by zobaczyć ten dokument\",\n    \"notification.success.delete\": \"Pozycja została usunięta\",\n    \"notification.success.saved\": \"Zapisano\",\n    \"notification.success.title\": \"Udało się:\",\n    \"notification.version.update.message\": \"Dostępna jest nowa wersja Strapi!\",\n    \"notification.warning.title\": \"Ostrzeżenie:\",\n    or: or,\n    \"request.error.model.unknown\": \"Ten model nie istnieje\",\n    skipToContent: skipToContent,\n    submit: submit\n};\n\nexport { Analytics, Documentation, Email, Password, Provider, ResetPasswordToken, Role, Username, Users, anErrorOccurred, clearLabel, pl as default, or, skipToContent, submit };\n//# sourceMappingURL=pl.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,YAAY;AAChB,IAAI,gBAAgB;AACpB,IAAI,QAAQ;AACZ,IAAI,WAAW;AACf,IAAI,WAAW;AACf,IAAI,qBAAqB;AACzB,IAAI,OAAO;AACX,IAAI,WAAW;AACf,IAAI,QAAQ;AACZ,IAAI,kBAAkB;AACtB,IAAI,aAAa;AACjB,IAAI,KAAK;AACT,IAAI,gBAAgB;AACpB,IAAI,SAAS;AACb,IAAI,KAAK;AAAA,EACL;AAAA,EACA,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0CAA0C;AAAA,EAC1C,iCAAiC;AAAA,EACjC,sCAAsC;AAAA,EACtC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,iDAAiD;AAAA,EACjD,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,2BAA2B;AAAA,EAC3B,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA,EAChB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,6BAA6B;AAAA,EAC7B,qCAAqC;AAAA,EACrC,0CAA0C;AAAA,EAC1C,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,4BAA4B;AAAA,EAC5B,4BAA4B;AAAA,EAC5B,0CAA0C;AAAA,EAC1C,sBAAsB;AAAA,EACtB,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,4BAA4B;AAAA,EAC5B,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,oCAAoC;AAAA,EACpC,sCAAsC;AAAA,EACtC,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,sCAAsC;AAAA,EACtC,8BAA8B;AAAA,EAC9B,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,yDAAyD;AAAA,EACzD,iDAAiD;AAAA,EACjD,8DAA8D;AAAA,EAC9D,mEAAmE;AAAA,EACnE,kEAAkE;AAAA,EAClE,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,8DAA8D;AAAA,EAC9D,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,oDAAoD;AAAA,EACpD,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,mCAAmC;AAAA,EACnC,iDAAiD;AAAA,EACjD,yDAAyD;AAAA,EACzD,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,wCAAwC;AAAA,EACxC,0CAA0C;AAAA,EAC1C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,6CAA6C;AAAA,EAC7C,yDAAyD;AAAA,EACzD,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,wCAAwC;AAAA,EACxC,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,2CAA2C;AAAA,EAC3C,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,4CAA4C;AAAA,EAC5C,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,2BAA2B;AAAA,EAC3B,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,qBAAqB;AAAA,EACrB,sBAAsB;AAAA,EACtB,2BAA2B;AAAA,EAC3B,gDAAgD;AAAA,EAChD,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,+BAA+B;AAAA,EAC/B,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,2CAA2C;AAAA,EAC3C,mDAAmD;AAAA,EACnD,2CAA2C;AAAA,EAC3C,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,uDAAuD;AAAA,EACvD,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,kDAAkD;AAAA,EAClD,kDAAkD;AAAA,EAClD,wCAAwC;AAAA,EACxC,mDAAmD;AAAA,EACnD,yDAAyD;AAAA,EACzD;AAAA,EACA,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,wCAAwC;AAAA,EACxC,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,4CAA4C;AAAA,EAC5C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,+CAA+C;AAAA,EAC/C,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,+CAA+C;AAAA,EAC/C,sDAAsD;AAAA,EACtD,wDAAwD;AAAA,EACxD,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,yDAAyD;AAAA,EACzD,qDAAqD;AAAA,EACrD,4CAA4C;AAAA,EAC5C,2CAA2C;AAAA,EAC3C,gDAAgD;AAAA,EAChD,4CAA4C;AAAA,EAC5C,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,0CAA0C;AAAA,EAC1C,sCAAsC;AAAA,EACtC,2CAA2C;AAAA,EAC3C,wDAAwD;AAAA,EACxD,sDAAsD;AAAA,EACtD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,2DAA2D;AAAA,EAC3D,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,oCAAoC;AAAA,EACpC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,2CAA2C;AAAA,EAC3C,2BAA2B;AAAA,EAC3B,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,+EAA+E;AAAA,EAC/E,qEAAqE;AAAA,EACrE,yEAAyE;AAAA,EACzE,mEAAmE;AAAA,EACnE,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,4DAA4D;AAAA,EAC5D,0DAA0D;AAAA,EAC1D,sEAAsE;AAAA,EACtE,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,qDAAqD;AAAA,EACrD,wDAAwD;AAAA,EACxD,yDAAyD;AAAA,EACzD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,qCAAqC;AAAA,EACrC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,oEAAoE;AAAA,EACpE,4DAA4D;AAAA,EAC5D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,wDAAwD;AAAA,EACxD,4BAA4B;AAAA,EAC5B,sBAAsB;AAAA,EACtB,+BAA+B;AAAA,EAC/B,yCAAyC;AAAA,EACzC,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,4BAA4B;AAAA,EAC5B,uBAAuB;AAAA,EACvB,kBAAkB;AAAA,EAClB,yCAAyC;AAAA,EACzC,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,gCAAgC;AAAA,EAChC,wCAAwC;AAAA,EACxC,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB;AAAA,EACA,eAAe;AAAA,EACf,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,+CAA+C;AAAA,EAC/C,qDAAqD;AAAA,EACrD,sDAAsD;AAAA,EACtD,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,0CAA0C;AAAA,EAC1C,4CAA4C;AAAA,EAC5C,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,gDAAgD;AAAA,EAChD,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,yDAAyD;AAAA,EACzD,+CAA+C;AAAA,EAC/C,gDAAgD;AAAA,EAChD,uDAAuD;AAAA,EACvD,+BAA+B;AAAA,EAC/B,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,yCAAyC;AAAA,EACzC,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,wBAAwB;AAAA,EACxB,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,eAAe;AAAA,EACf,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,wBAAwB;AAAA,EACxB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,sBAAsB;AAAA,EACtB,eAAe;AAAA,EACf,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,yBAAyB;AAAA,EACzB,yBAAyB;AAAA,EACzB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,+CAA+C;AAAA,EAC/C,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,4CAA4C;AAAA,EAC5C,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B;AAAA,EACA,+BAA+B;AAAA,EAC/B;AAAA,EACA;AACJ;", "names": []}