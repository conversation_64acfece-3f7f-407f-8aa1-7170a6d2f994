{"version": 3, "sources": ["../../../@strapi/upload/dist/admin/translations/pt.json.mjs"], "sourcesContent": ["var pt = {\n    \"button.next\": \"<PERSON>r<PERSON>xi<PERSON>\",\n    \"checkControl.crop-duplicate\": \"Duplicar e recortar o ficheiro\",\n    \"checkControl.crop-original\": \"Recortar o ficheiro original\",\n    \"control-card.add\": \"Adicionar\",\n    \"control-card.cancel\": \"Cancelar\",\n    \"control-card.copy-link\": \"Copiar link\",\n    \"control-card.crop\": \"Recortar\",\n    \"control-card.download\": \"Transferir\",\n    \"control-card.edit\": \"Editar\",\n    \"control-card.replace-media\": \"Substituir Média\",\n    \"control-card.save\": \"Guardar\",\n    \"filter.add\": \"Adicionar filtro\",\n    \"form.button.replace-media\": \"Substituir média\",\n    \"form.input.description.file-alt\": \"Esse texto será mostrado se a imagem não puder ser carregada.\",\n    \"form.input.label.file-alt\": \"Texto alternativo\",\n    \"form.input.label.file-caption\": \"<PERSON>a\",\n    \"form.input.label.file-name\": \"Nome do ficheiro\",\n    \"form.upload-url.error.url.invalid\": \"Uma URL é inválida\",\n    \"form.upload-url.error.url.invalids\": \"{number} URLs são inválidas\",\n    \"header.actions.upload-assets\": \"Enviar ficheiros\",\n    \"header.content.assets\": \"{numberFolders, plural, one {1 folder} other {# folders}} - {numberAssets, plural, one {1 ficheiro} other {# ficheiros}}\",\n    \"input.button.label\": \"Navegue pelos ficheiros\",\n    \"input.label-bold\": \"Arraste e solte\",\n    \"input.label-normal\": \"para enviar ou\",\n    \"input.placeholder\": \"Clique para selecionar um ficheiro ou arraste e solte um ficheiro nessa área\",\n    \"input.url.description\": \"Insira cada URL em uma linha.\",\n    \"input.url.label\": \"URL\",\n    \"list.assets-empty.subtitle\": \"Adicione o primeiro à lista.\",\n    \"list.assets-empty.title\": \"Ainda não existem ficheiros\",\n    \"list.assets-empty.title-withSearch\": \"Não existem ficheiros com os filtros aplicados\",\n    \"list.assets.type-not-allowed\": \"Esse tipo de ficheiro não é permitido.\",\n    \"modal.file-details.date\": \"Data\",\n    \"modal.file-details.dimensions\": \"Dimensões\",\n    \"modal.file-details.extension\": \"Extensão\",\n    \"modal.file-details.size\": \"Tamanho\",\n    \"modal.header.browse\": \"Enviar ficheiros\",\n    \"modal.header.file-detail\": \"Detalhes\",\n    \"modal.header.pending-assets\": \"ficheiros pendentes\",\n    \"modal.header.select-files\": \"ficheiros selecionados\",\n    \"modal.nav.browse\": \"navegue\",\n    \"modal.nav.computer\": \"do computador\",\n    \"modal.nav.selected\": \"selecionado\",\n    \"modal.nav.url\": \"de uma url\",\n    \"modal.selected-list.sub-header-subtitle\": \"Arraste e solte para reordenar os ficheiros no campo\",\n    \"modal.upload-list.sub-header-subtitle\": \"Administre os ficheiros antes de adicioná-los à biblioteca\",\n    \"modal.upload-list.sub-header.button\": \"Adicionar mais ficheiros\",\n    \"plugin.description.long\": \"Gestão de ficheiros de média.\",\n    \"plugin.description.short\": \"Gestão de ficheiros de média.\",\n    \"plugin.name\": \"Biblioteca de Média\",\n    \"search.placeholder\": \"Pesquise por um ficheiro...\",\n    \"settings.form.autoOrientation.description\": \"Rode a imagem automaticamente de acordo com a etiqueta de orientação do EXIF\",\n    \"settings.form.autoOrientation.label\": \"Habilitar orientação automática\",\n    \"settings.form.responsiveDimensions.description\": \"Serão gerados automaticamente múltiplos formatos (grande, médio, pequeno) dos ficheiros enviados\",\n    \"settings.form.responsiveDimensions.label\": \"Habilitar envios responsivos\",\n    \"settings.form.sizeOptimization.label\": \"Habilitar otimização do tamanho (sem perca de qualidade)\",\n    \"settings.form.videoPreview.description\": \"Será gerado uma pré-visualização de seis segundos do vídeo (GIF)\",\n    \"settings.form.videoPreview.label\": \"Pré-visualização\",\n    \"settings.header.label\": \"Biblioteca de Média - Configurações\",\n    \"settings.section.image.label\": \"IMAGEM\",\n    \"settings.section.video.label\": \"VÍDEO\",\n    \"settings.sub-header.label\": \"Ajuste as configurações para a biblioteca de média\",\n    \"sort.created_at_asc\": \"Envios mais antigos\",\n    \"sort.created_at_desc\": \"Envios mais recentes\",\n    \"sort.label\": \"Organizar por\",\n    \"sort.name_asc\": \"Ordem alfabética (A ao Z)\",\n    \"sort.name_desc\": \"Ordem alfabética reversa (Z ao A)\",\n    \"sort.updated_at_asc\": \"Atualizações mais antigas\",\n    \"sort.updated_at_desc\": \"Atualizações mais recentes\",\n    \"window.confirm.close-modal.file\": \"Tem a certeza? As suas alterações serão perdidas.\",\n    \"window.confirm.close-modal.files\": \"Tem a certeza? Alguns ficheiros ainda não foram enviados.\"\n};\n\nexport { pt as default };\n//# sourceMappingURL=pt.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,KAAK;AAAA,EACL,eAAe;AAAA,EACf,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,qBAAqB;AAAA,EACrB,8BAA8B;AAAA,EAC9B,qBAAqB;AAAA,EACrB,cAAc;AAAA,EACd,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,gCAAgC;AAAA,EAChC,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,mBAAmB;AAAA,EACnB,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,sCAAsC;AAAA,EACtC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,6BAA6B;AAAA,EAC7B,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,iBAAiB;AAAA,EACjB,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,eAAe;AAAA,EACf,sBAAsB;AAAA,EACtB,6CAA6C;AAAA,EAC7C,uCAAuC;AAAA,EACvC,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,wCAAwC;AAAA,EACxC,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,yBAAyB;AAAA,EACzB,gCAAgC;AAAA,EAChC,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,cAAc;AAAA,EACd,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,mCAAmC;AAAA,EACnC,oCAAoC;AACxC;", "names": []}