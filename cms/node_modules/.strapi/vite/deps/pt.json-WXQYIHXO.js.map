{"version": 3, "sources": ["../../../@strapi/content-type-builder/dist/admin/translations/pt.json.mjs"], "sourcesContent": ["var from = \"de\";\nvar pt = {\n    \"attribute.boolean\": \"Booleano\",\n    \"attribute.date\": \"Data\",\n    \"attribute.email\": \"Email\",\n    \"attribute.enumeration\": \"Enumeração\",\n    \"attribute.json\": \"JSON\",\n    \"attribute.media\": \"Media\",\n    \"attribute.password\": \"<PERSON>lavra-passe\",\n    \"attribute.relation\": \"Rela<PERSON>\",\n    \"attribute.text\": \"Texto\",\n    \"form.attribute.item.customColumnName\": \"Nomes de colunas personalizadas\",\n    \"form.attribute.item.customColumnName.description\": \"Isto é útil para renomear os nomes das colunas da base de dados num formato mais abrangente para as respostas da API\",\n    \"form.attribute.item.defineRelation.fieldName\": \"Nome do campo\",\n    \"form.attribute.item.enumeration.graphql\": \"Substituição de nome para o GraphQL\",\n    \"form.attribute.item.enumeration.graphql.description\": \"Permite-lhe a substituição do nome predefinido para o GraphQL\",\n    \"form.attribute.item.enumeration.placeholder\": \"Ex:\\nmanhã\\ntarde\\nnoite\",\n    \"form.attribute.item.enumeration.rules\": \"Valores (um valor por linha)\",\n    \"form.attribute.item.maximum\": \"Valor máximo\",\n    \"form.attribute.item.maximumLength\": \"Comprimento máximo\",\n    \"form.attribute.item.minimum\": \"Valor mínimo\",\n    \"form.attribute.item.minimumLength\": \"Comprimento mínimo\",\n    \"form.attribute.item.number.type\": \"Formato numérico\",\n    \"form.attribute.item.number.type.decimal\": \"decimal (ex: 2.22)\",\n    \"form.attribute.item.number.type.float\": \"real (ex: 3.33333333)\",\n    \"form.attribute.item.number.type.integer\": \"inteiro (ex: 10)\",\n    \"form.attribute.item.requiredField\": \"Campo obrigatório\",\n    \"form.attribute.item.requiredField.description\": \"Não será capaz de criar uma entrada se este campo estiver vazio\",\n    \"form.attribute.item.uniqueField\": \"Campo único\",\n    \"form.attribute.item.uniqueField.description\": \"Não será capaz de criar uma entrada se houver uma entrada existente com conteúdo idêntico\",\n    \"form.attribute.settings.default\": \"Valor predefinido\",\n    \"form.button.cancel\": \"Cancelar\",\n    from: from,\n    \"modelPage.attribute.relationWith\": \"Relação com\",\n    \"plugin.description.long\": \"Modele a estrutura de dados da sua API. Crie novos campos e relações em apenas um minuto. Os ficheiros são automaticamente criados e actualizados no seu projecto.\",\n    \"plugin.description.short\": \"Modele a estrutura de dados da sua API.\",\n    \"popUpForm.navContainer.advanced\": \"Definições Avançadas\",\n    \"popUpForm.navContainer.base\": \"Definições Básicas\",\n    \"popUpWarning.bodyMessage.contentType.delete\": \"Tem a certeza que pretende apagar este Tipo de Conteúdo?\",\n    \"relation.attributeName.placeholder\": \"Ex: autor, categoria, tag\",\n    \"relation.manyToMany\": \"tem e pertence a vários\",\n    \"relation.manyToOne\": \"tem vários\",\n    \"relation.oneToMany\": \"pertence a vários\",\n    \"relation.oneToOne\": \"tem e pertence a um\",\n    \"relation.oneWay\": \"tem um\"\n};\n\nexport { pt as default, from };\n//# sourceMappingURL=pt.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,OAAO;AACX,IAAI,KAAK;AAAA,EACL,qBAAqB;AAAA,EACrB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,gDAAgD;AAAA,EAChD,2CAA2C;AAAA,EAC3C,uDAAuD;AAAA,EACvD,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+BAA+B;AAAA,EAC/B,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,mCAAmC;AAAA,EACnC,sBAAsB;AAAA,EACtB;AAAA,EACA,oCAAoC;AAAA,EACpC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,+CAA+C;AAAA,EAC/C,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,qBAAqB;AAAA,EACrB,mBAAmB;AACvB;", "names": []}