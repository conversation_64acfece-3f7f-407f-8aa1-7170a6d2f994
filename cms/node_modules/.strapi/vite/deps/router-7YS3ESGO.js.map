{"version": 3, "sources": ["../../../@strapi/review-workflows/admin/src/router.tsx"], "sourcesContent": ["/* eslint-disable check-file/filename-naming-convention */\nimport { lazy } from 'react';\n\nimport { Routes, Route, PathRouteProps } from 'react-router-dom';\n\nconst ProtectedListPage = lazy(() =>\n  import('./routes/settings').then((mod) => ({ default: mod.ProtectedListPage }))\n);\nconst ProtectedEditPage = lazy(() =>\n  import('./routes/settings/id').then((mod) => ({ default: mod.ProtectedEditPage }))\n);\n\nconst routes: PathRouteProps[] = [\n  {\n    path: '/',\n    Component: ProtectedListPage,\n  },\n  {\n    path: ':id',\n    Component: ProtectedEditPage,\n  },\n];\n\nconst Router = () => (\n  <Routes>\n    {routes.map((route) => (\n      <Route key={route.path} {...route} />\n    ))}\n  </Routes>\n);\n\nexport { Router };\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAKA,IAAMA,wBAAoBC,mBAAK,MAC7B,OAAO,wBAAA,EAAqBC,KAAK,CAACC,SAAS;EAAEC,SAASD,IAAIH;EAAkB,CAAA;AAE9E,IAAMK,wBAAoBJ,mBAAK,MAC7B,OAAO,kBAAA,EAAwBC,KAAK,CAACC,SAAS;EAAEC,SAASD,IAAIE;EAAkB,CAAA;AAGjF,IAAMC,SAA2B;EAC/B;IACEC,MAAM;IACNC,WAAWR;EACb;EACA;IACEO,MAAM;IACNC,WAAWH;EACb;AACD;AAEKI,IAAAA,SAAS,UACbC,wBAACC,QAAAA;EACEL,UAAAA,OAAOM,IAAI,CAACC,cACXH,wBAACI,OAAAA;IAAwB,GAAGD;EAAhBA,GAAAA,MAAMN,IAAI,CAAA;;", "names": ["ProtectedListPage", "lazy", "then", "mod", "default", "ProtectedEditPage", "routes", "path", "Component", "Router", "_jsx", "Routes", "map", "route", "Route"]}