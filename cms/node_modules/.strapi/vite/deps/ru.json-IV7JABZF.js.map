{"version": 3, "sources": ["../../../@strapi/plugin-cloud/dist/admin/translations/ru.json.mjs"], "sourcesContent": ["var ru = {\n    \"Plugin.name\": \"Развертывание\",\n    \"Homepage.title\": \"Полностью управляемый облачный хостинг для вашего проекта Strapi\",\n    \"Homepage.subTitle\": \"Выполните этот двухэтапный процесс, чтобы получить все необходимое для запуска Strapi в production.\",\n    \"Homepage.githubBox.title.versioned\": \"Проект перенесен на GitHub\",\n    \"Homepage.githubBox.title.not-versioned\": \"Разместите свой проект на GitHub\",\n    \"Homepage.githubBox.subTitle.versioned\": \"Ты сделал это! Вы всего в одном шаге от того, чтобы разместить свой проект в Интернете.\",\n    \"Homepage.githubBox.subTitle.not-versioned\": \"Перед развертыванием в Strapi Cloud ваш проект должен быть версирован на GitHub.\",\n    \"Homepage.githubBox.buttonText\": \"Загрузить на GitHub\",\n    \"Homepage.cloudBox.title\": \"Развертывание в Strapi Cloud\",\n    \"Homepage.cloudBox.subTitle\": \"Наслаждайтесь оптимизированным для Strapi стеком, включающим базу данных, поставщика услуг электронной почты и CDN.\",\n    \"Homepage.cloudBox.buttonText\": \"Развернуть в Strapi Cloud\",\n    \"Homepage.textBox.label.versioned\": \"Попробуйте Strapi Cloud бесплатно!\",\n    \"Homepage.textBox.label.not-versioned\": \"Зачем загружать мой проект на GitHub?\",\n    \"Homepage.textBox.text.versioned\": \"Strapi Cloud предлагает Вам бесплатную пробную версию на 14 дней, чтобы Вы могли поэкспериментировать со своим проектом в облаке, включая все функции.\",\n    \"Homepage.textBox.text.not-versioned\": \"Strapi Cloud загрузит и развернет Ваш проект из Вашего репозитория на GitHub. Это лучший способ создания версии, управления и развертывания вашего проекта. Следуйте инструкциям на GitHub, чтобы успешно загрузить его.\"\n};\n\nexport { ru as default };\n//# sourceMappingURL=ru.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,KAAK;AAAA,EACL,eAAe;AAAA,EACf,kBAAkB;AAAA,EAClB,qBAAqB;AAAA,EACrB,sCAAsC;AAAA,EACtC,0CAA0C;AAAA,EAC1C,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,iCAAiC;AAAA,EACjC,2BAA2B;AAAA,EAC3B,8BAA8B;AAAA,EAC9B,gCAAgC;AAAA,EAChC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,mCAAmC;AAAA,EACnC,uCAAuC;AAC3C;", "names": []}