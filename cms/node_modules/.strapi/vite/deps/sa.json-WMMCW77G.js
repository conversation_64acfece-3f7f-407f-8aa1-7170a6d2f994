import "./chunk-PLDDJCW6.js";

// node_modules/@strapi/content-manager/dist/admin/translations/sa.json.mjs
var groups = "समूहाः";
var models = "संग्रह प्रकार";
var pageNotFound = "पृष्ठं न प्राप्तम्";
var sa = {
  "App.schemas.data-loaded": "योजनानि सफलतया लोड् कृतानि",
  "ListViewTable.relation-loaded": "सम्बन्धाः लोड् कृताः",
  "ListViewTable.relation-loading": "सम्बन्धाः लोड् भवन्ति",
  "ListViewTable.relation-more": "अस्मिन् सम्बन्धे प्रदर्शितापेक्षया अधिकानि सत्तानि सन्ति",
  "EditRelations.title": "सम्बन्धित डेटा",
  "HeaderLayout.button.label-add-entry": "नवीन प्रविष्टि रचयतु",
  "api.id": "एपीआई आईडी",
  "components.AddFilterCTA.add": "छिद्रकाः",
  "components.AddFilterCTA.hide": "फ़िल्टर",
  "components.DragHandle-label": "आकर्षति",
  "components.DraggableAttr.edit": "सम्पादनार्थं क्लिक् कुर्वन्तु",
  "components.DraggableCard.delete.field": "{आइटम} को हटाएँ",
  "components.DraggableCard.edit.field": "{आइटम} सम्पादित करें",
  "components.DraggableCard.move.field": "{आइटम} को ले जाएँ",
  "components.ListViewTable.row-line": "मदपङ्क्ति {संख्या}",
  "components.DynamicZone.ComponentPicker-label": "एकं घटकं चिनुत",
  "components.DynamicZone.add-component": "{घटकनाम} मध्ये एकं घटकं योजयन्तु",
  "components.DynamicZone.delete-label": "{नाम} को हटाएँ",
  "components.DynamicZone.error-message": "घटकस्य त्रुटिः (दोषाः) सन्ति",
  "components.DynamicZone.missing-components": "तत्र {संख्या, बहुवचनम्, =0 {घटकाः # अनुपलब्धाः सन्ति} एकः {घटकः # अनुपलब्धः अस्ति} अन्यः {घटकाः # अनुपलब्धाः सन्ति}}",
  "components.DynamicZone.move-down-label": "घटकं अधः स्थापयतु",
  "components.DynamicZone.move-up-label": "घटकं उपरि चालयन्तु",
  "components.DynamicZone.pick-compo": "एकं घटकं चिनुत",
  "components.DynamicZone.required": "घटकम् आवश्यकम्",
  "components.EmptyAttributesBlock.button": "सेटिंग्स् पृष्ठं प्रति गच्छतु",
  "components.EmptyAttributesBlock.description": "भवन्तः स्वसेटिंग्स् परिवर्तयितुं शक्नुवन्ति",
  "components.FieldItem.linkToComponentLayout": "घटकस्य लेआउट् सेट् कुर्वन्तु",
  "components.FieldSelect.label": "क्षेत्रं योजयन्तु",
  "components.FilterOptions.button.apply": "अनुप्रयोग करें",
  "components.FiltersPickWrapper.PluginHeader.actions.apply": "अनुप्रयोग करें",
  "components.FiltersPickWrapper.PluginHeader.actions.clearAll": "सर्वं स्वच्छं कुरुत",
  "components.FiltersPickWrapper.PluginHeader.description": "प्रविष्टीनां फ़िल्टर कर्तुं प्रयोक्तुं शर्ताः सेट् कुर्वन्तु",
  "components.FiltersPickWrapper.PluginHeader.title.filter": "छिद्रक",
  "components.FiltersPickWrapper.hide": "गोपयन्तु",
  "components.LeftMenu.Search.label": "सामग्रीप्रकारं अन्वेष्टुम्",
  "components.LeftMenu.collection-types": "संग्रह प्रकार",
  "components.LeftMenu.single-types": "एकल प्रकार",
  "components.LimitSelect.itemsPerPage": "प्रति पृष्ठ मद",
  "components.NotAllowedInput.text": "एतत् क्षेत्रं द्रष्टुं कोऽपि अनुमतिः नास्ति",
  "components.RepeatableComponent.error-message": "घटक(घटकों) में त्रुटि (घटक) होती है",
  "components.Search.placeholder": "प्रविष्टिं अन्वेष्टुम्...",
  "components.Select.draft-info-title": "राज्यम्: मसौदा",
  "components.Select.publish-info-title": "राज्यम्: प्रकाशितम्",
  "components.SettingsViewWrapper.pluginHeader.description.edit-settings": "सम्पादनदृश्यं कथं दृश्यते इति अनुकूलितं कुरुत।",
  "components.SettingsViewWrapper.pluginHeader.description.list-settings": "सूचीदृश्यस्य सेटिंग्स् परिभाषयतु।",
  "components.SettingsViewWrapper.pluginHeader.title": "दृश्यं विन्यस्यताम् - {नाम}",
  "components.TableDelete.delete": "सर्वं विलोपयतु",
  "components.TableDelete.deleteSelected": "चयनितं विलोपयतु",
  "components.TableDelete.label": "{संख्या, बहुवचनम्, एकः {# प्रविष्टिः} अन्ये {# प्रविष्टयः}} चयनिताः",
  "components.TableEmpty.withFilters": "प्रयुक्तैः फ़िल्टरैः सह {contentType} नास्ति...",
  "components.TableEmpty.withSearch": "अन्वेषणस्य ({search}) अनुरूपं {contentType} नास्ति।",
  "components.TableEmpty.withoutFilter": "{contentType} नास्ति...",
  "components.empty-repeatable": "अद्यापि प्रविष्टिः नास्ति। एकं योजयितुं अधोलिखितं बटनं नुदन्तु।",
  "components.notification.info.maximum-requirement": "भवन्तः पूर्वमेव अधिकतमक्षेत्रसङ्ख्यां प्राप्तवन्तः",
  "components.notification.info.minimum-requirement": "न्यूनतम आवश्यकतायाः अनुरूपं क्षेत्रं योजितम्",
  "components.repeatable.reorder.error": "भवतः घटकस्य क्षेत्रस्य पुनः क्रमणं कुर्वन् त्रुटिः अभवत्, कृपया पुनः प्रयासं कुर्वन्तु",
  "components.reset-entry": "प्रविष्टिं पुनः सेट् कुर्वन्तु",
  "components.uid.apply": "अनुप्रयोग",
  "components.uid.available": "उपलब्धम्",
  "components.uid.regenerate": "पुनर्जन्म",
  "components.uid.suggested": "सुझाता",
  "components.uid.unavailable": "अनुपलब्धम्",
  "containers.Edit.Link.Layout": "विन्यासं विन्यस्यताम्",
  "containers.Edit.Link.Model": "संग्रह-प्रकारं सम्पादयतु",
  "containers.Edit.addAnItem": "एकं द्रव्यं योजयन्तु...",
  "containers.Edit.clickToJump": "प्रविष्टिं प्रति कूर्दितुं क्लिक् कुर्वन्तु",
  "containers.Edit.delete": "विलोपनम्",
  "containers.Edit.delete-entry": "एतत् प्रविष्टिं विलोपयतु",
  "containers.Edit.editing": "सम्पादनम्...",
  "containers.Edit.information": "सूचना",
  "containers.Edit.information.by": "द्वारा",
  "containers.Edit.information.created": "निर्मितम्",
  "containers.Edit.information.draftVersion": "मसौदा संस्करण",
  "containers.Edit.information.editing": "सम्पादनम्",
  "containers.Edit.information.lastUpdate": "अन्तिम अद्यतनम्",
  "containers.Edit.information.publishedVersion": "प्रकाशित संस्करण",
  "containers.Edit.pluginHeader.title.new": "प्रविष्टिं रचयतु",
  "containers.Edit.reset": "पुनर्स्थापनम्",
  "containers.Edit.returnList": "सूचीं प्रति प्रत्यागच्छतु",
  "containers.Edit.seeDetails": "विवरणम्",
  "containers.Edit.submit": "रक्षतु",
  "containers.EditSettingsView.modal-form.edit-field": "क्षेत्रं सम्पादयतु",
  "containers.EditView.add.new-entry": "प्रविष्टिं योजयन्तु",
  "containers.EditView.notification.errors": "प्रपत्रे केचन त्रुटयः सन्ति",
  "containers.Home.introduction": "भवतः प्रविष्टीनां सम्पादनार्थं वाममेनूमध्ये विशिष्टलिङ्कं गच्छन्तु। अस्मिन् प्लगिन् मध्ये सेटिंग्स् सम्पादयितुं समुचितः उपायः नास्ति तथा च अद्यापि सक्रियविकासस्य अधीनम् अस्ति।",
  "containers.Home.pluginHeaderDescription": "शक्तिशालिनः सुन्दरस्य च अन्तरफलकस्य माध्यमेन स्वप्रविष्टीः प्रबन्धयन्तु।",
  "containers.Home.pluginHeaderTitle": "सामग्री प्रबन्धक",
  "containers.List.draft": "मसौदा",
  "containers.List.errorFetchRecords": "त्रुटि",
  "containers.List.published": "प्रकाशित",
  "containers.list.displayedFields": "प्रदर्शितक्षेत्राणि",
  "containers.list.items": "{संख्या, बहुवचन, =0 {आइटम} एक {आइटम} अन्य {आइटम}}",
  "containers.list.table-headers.publishedAt": "राज्यम्",
  "containers.ListSettingsView.modal-form.edit-label": "{क्षेत्रनाम} सम्पादित करें",
  "containers.SettingPage.add.field": "अन्यं क्षेत्रं सम्मिलितं कुर्वन्तु",
  "containers.SettingPage.attributes": "विशेषता क्षेत्राणि",
  "containers.SettingPage.attributes.description": "विशेषतानां क्रमं परिभाषयतु",
  "containers.SettingPage.editSettings.description": "विन्यासस्य निर्माणार्थं क्षेत्राणि कर्षयतु & पातयतु",
  "containers.SettingPage.editSettings.entry.title": "प्रविष्टि शीर्षक",
  "containers.SettingPage.editSettings.entry.title.description": "स्वप्रविष्टेः प्रदर्शितं क्षेत्रं सेट् कुर्वन्तु",
  "containers.SettingPage.editSettings.relation-field.description": "सम्पादन-सूचीदृश्ययोः द्वयोः अपि प्रदर्शितं क्षेत्रं सेट् कुर्वन्तु",
  "containers.SettingPage.editSettings.title": "दृश्यं (सेटिंग्स्) सम्पादयतु",
  "containers.SettingPage.layout": "लेआउट",
  "containers.SettingPage.listSettings.description": "अस्य संग्रहप्रकारस्य विकल्पान् विन्यस्यताम्",
  "containers.SettingPage.listSettings.title": "सूची दृश्य (सेटिंग्स्)",
  "containers.SettingPage.pluginHeaderDescription": "अस्य संग्रहप्रकारस्य विशिष्टानि सेटिंग्स् विन्यस्यताम्",
  "containers.SettingPage.settings": "सेटिंग्स्",
  "containers.SettingViewModel": "दृश्यम्",
  "containers.SettingViewModel.pluginHeader.title": "सामग्री प्रबन्धक - {नाम}",
  "containers.SettingsPage.Block.contentType.description": "विशिष्टानि सेटिंग्स् विन्यस्यताम्",
  "containers.SettingsPage.Block.contentType.title": "संग्रह प्रकार",
  "containers.SettingsPage.Block.generalSettings.description": "स्वसंग्रहप्रकारानाम् पूर्वनिर्धारितविकल्पान् विन्यस्यताम्",
  "containers.SettingsPage.Block.generalSettings.title": "सामान्य",
  "containers.SettingsPage.pluginHeaderDescription": "स्वस्य सर्वेषां संग्रहप्रकारस्य समूहानां च सेटिंग्स् विन्यस्यताम्",
  "containers.SettingsView.list.subtitle": "स्वस्य संग्रहप्रकारस्य समूहानां च विन्यासं प्रदर्शनं च विन्यस्यताम्",
  "containers.SettingsView.list.title": "विन्यासानि प्रदर्शयतु",
  "edit-settings-view.link-to-ctb.components": "घटकं सम्पादयतु",
  "edit-settings-view.link-to-ctb.content-types": "सामग्रीप्रकारं सम्पादयतु",
  "emptyAttributes.button": "संग्रह प्रकार निर्माता पर जाएँ",
  "emptyAttributes.description": "स्वस्य प्रथमं क्षेत्रं स्वस्य संग्रहप्रकारे योजयन्तु",
  "emptyAttributes.title": "अद्यापि क्षेत्राणि नास्ति",
  "error.attribute.key.taken": "एतत् मूल्यं पूर्वमेव अस्ति",
  "error.attribute.sameKeyAndName": "समानं न भवितुम् अर्हति",
  "error.attribute.taken": "एतत् क्षेत्रनाम पूर्वमेव अस्ति",
  "error.contentTypeName.taken": "एतत् नाम पूर्वमेव अस्ति",
  "error.model.fetch": "models config fetch इत्यस्य समये त्रुटिः अभवत्।",
  "error.record.create": "अभिलेखनिर्माणकाले त्रुटिः अभवत्।",
  "error.record.delete": "अभिलेखविलोपनस्य समये त्रुटिः अभवत्।",
  "error.record.fetch": "अभिलेखस्य आनयनस्य समये त्रुटिः अभवत्।",
  "error.record.update": "अभिलेख अद्यतनस्य समये त्रुटिः अभवत्।",
  "error.records.count": "गणना अभिलेखान् आनयनस्य समये त्रुटिः अभवत्।",
  "error.records.fetch": "अभिलेखानां आनयनस्य समये त्रुटिः अभवत्।",
  "error.schema.generation": "स्कीमा जननस्य समये त्रुटिः अभवत्।",
  "error.validation.json": "एतत् JSON नास्ति",
  "error.validation.max": "मूल्यं बहु उच्चम् अस्ति।",
  "error.validation.maxLength": "मूल्यं बहु दीर्घम् अस्ति।",
  "error.validation.min": "मूल्यं बहु न्यूनम् अस्ति।",
  "error.validation.minLength": "मूल्यम् अतीव लघु अस्ति।",
  "error.validation.minSupMax": "श्रेष्ठं न भवितुम् अर्हति",
  "error.validation.regex": "मूल्यं regex इत्यनेन सह न मेलति।",
  "error.validation.required": "एतत् मूल्यनिवेशम् आवश्यकम्।",
  "form.Input.bulkActions": "बल्क क्रियाएँ सक्षम करें",
  "form.Input.defaultSort": "पूर्वनिर्धारित क्रमबद्धता विशेषता",
  "form.Input.description": "विवरण",
  "form.Input.description.placeholder": "प्रोफाइले नाम प्रदर्शयतु",
  "form.Input.editable": "सम्पादन योग्य क्षेत्र",
  "form.Input.filters": "छिद्रकं सक्षमं कुर्वन्तु",
  "form.Input.label": "लेबल",
  "form.Input.label.inputDescription": "एतत् मूल्यं सारणीयाः शिरसि प्रदर्शितं लेबलं अधिलिखति",
  "form.Input.pageEntries": "प्रति पृष्ठ प्रविष्टियाँ",
  "form.Input.pageEntries.inputDescription": "टिप्पणी: भवान् संग्रहप्रकारसेटिंग्स् पृष्ठे एतत् मूल्यं अधिलिखितुं शक्नोति।",
  "form.Input.placeholder": "स्थानधारक",
  "form.Input.placeholder.placeholder": "मम भयानकं मूल्यम्",
  "form.Input.search": "अन्वेषणं सक्षमं कुर्वन्तु",
  "form.Input.search.field": "अस्मिन् क्षेत्रे अन्वेषणं सक्षमं कुर्वन्तु",
  "form.Input.sort.field": "अस्मिन् क्षेत्रे क्रमणं सक्षमं कुर्वन्तु",
  "form.Input.sort.order": "पूर्वनिर्धारित क्रमबद्धता क्रम",
  "form.Input.wysiwyg": "WYSIWYG के रूप में प्रदर्शित करें",
  "global.displayedFields": "प्रदर्शितक्षेत्राणि",
  groups,
  "groups.numbered": "समूहाः ({संख्या})",
  "header.name": "सामग्री",
  "link-to-ctb": "प्रतिरूपं सम्पादयतु",
  models,
  "models.numbered": "संग्रह प्रकार ({संख्या})",
  "notification.error.displayedFields": "भवतः न्यूनातिन्यूनम् एकं प्रदर्शितं क्षेत्रं आवश्यकम्",
  "notification.error.relationship.fetch": "संयोजनं आनयितुं त्रुटिः अभवत्।",
  "notification.info.SettingPage.disableSort": "भवतः क्रमणानुमत्या सह विशेषता आवश्यकी",
  "notification.info.minimumFields": "भवता न्यूनातिन्यूनम् एकं क्षेत्रं प्रदर्शयितव्यम्",
  "notification.upload.error": "भवतः सञ्चिकाः अपलोड् करणसमये त्रुटिः अभवत्",
  pageNotFound,
  "pages.ListView.header-subtitle": "{संख्या, बहुवचनम्, =0 {# प्रविष्टयः} एकः {# प्रविष्टिः} अन्ये {# प्रविष्टयः}} प्राप्ताः",
  "pages.NoContentType.button": "स्वस्य प्रथमं सामग्री-प्रकारं रचयतु",
  "pages.NoContentType.text": "भवतः समीपे अद्यापि किमपि सामग्री नास्ति, वयं भवन्तं प्रथमं सामग्री-प्रकारं निर्मातुं अनुशंसयामः।",
  "permissions.not-allowed.create": "भवतः दस्तावेजस्य निर्माणस्य अनुमतिः नास्ति",
  "permissions.not-allowed.update": "भवतः एतत् दस्तावेजं द्रष्टुं न अनुमतम्",
  "plugin.description.long": "भवतः दत्तांशकोशे दत्तांशं द्रष्टुं, सम्पादयितुं, विलोपयितुं च द्रुतमार्गः।",
  "plugin.description.short": "भवतः दत्तांशकोशे दत्तांशं द्रष्टुं, सम्पादयितुं, विलोपयितुं च द्रुतमार्गः।",
  "popover.display-relations.label": "सम्बन्धान् प्रदर्शयतु",
  "success.record.delete": "विलोपितं",
  "success.record.publish": "प्रकाशित",
  "success.record.save": "रक्षितम्",
  "success.record.unpublish": "अप्रकाशित",
  "utils.data-loaded": "{संख्या, बहुवचनम्, =1 {प्रविष्टिः} अन्ये {प्रविष्टयः}} सफलतया लोडिताः सन्ति",
  "apiError.This feature must be unique": "{क्षेत्रम्‌} अद्वितीयं भवितुमर्हति",
  "popUpWarning.warning.publish-question": "किं भवान् अद्यापि प्रकाशयितुम् इच्छति?",
  "popUpwarning.warning.has-draft-relations.button-confirm": "हाँ, प्रकाशित करें",
  "popUpwarning.warning.has-draft-relations.message": "<b>{count, plural, =0 { भवतः सामग्रीसम्बन्धानां} एकः { भवतः सामग्रीसम्बन्धानां} अन्यः { भवतः सामग्रीसम्बन्धः अस्ति। are}}</b> अद्यापि प्रकाशिताः न सन्ति।<br></br>एतत् भवतः परियोजनायां भग्नलिङ्कानि त्रुटयः च जनयितुं शक्नोति।"
};
export {
  sa as default,
  groups,
  models,
  pageNotFound
};
//# sourceMappingURL=sa.json-WMMCW77G.js.map
