{"version": 3, "sources": ["../../../@strapi/content-manager/dist/admin/translations/sk.json.mjs"], "sourcesContent": ["var groups = \"Skupiny\";\nvar models = \"Kolekcie\";\nvar pageNotFound = \"Stránka nebola nájdená\";\nvar sk = {\n    \"App.schemas.data-loaded\": \"Schéma bola úspešne načítaná\",\n    \"ListViewTable.relation-loaded\": \"Prepojenia boli úspešne načítané\",\n    \"ListViewTable.relation-loading\": \"Prepojenia sa načítavajú\",\n    \"ListViewTable.relation-more\": \"Toto prepojenie obsahuje viac <PERSON>ov, ako je zobrazených\",\n    \"EditRelations.title\": \"Relačné dáta\",\n    \"HeaderLayout.button.label-add-entry\": \"Nový záznam\",\n    \"api.id\": \"API ID\",\n    \"components.AddFilterCTA.add\": \"Filtre\",\n    \"components.AddFilterCTA.hide\": \"Filtre\",\n    \"components.DragHandle-label\": \"Pretiahnuť\",\n    \"components.DraggableAttr.edit\": \"Kliknutím upravte\",\n    \"components.DraggableCard.delete.field\": \"Odstrániť {item}\",\n    \"components.DraggableCard.edit.field\": \"Upraviť {item}\",\n    \"components.DraggableCard.move.field\": \"Presunúť {item}\",\n    \"components.ListViewTable.row-line\": \"riadok {number}\",\n    \"components.DynamicZone.ComponentPicker-label\": \"Vyberte komponent\",\n    \"components.DynamicZone.add-component\": \"Pridať komponent do {componentName}\",\n    \"components.DynamicZone.delete-label\": \"Odstrániť {name}\",\n    \"components.DynamicZone.error-message\": \"Komponent obsahuje chybu(y)\",\n    \"components.DynamicZone.missing-components\": \"{number, plural, one {Chýba # komponent} few {Chýbajú # komponenty} other {Chýba # komponentov}}\",\n    \"components.DynamicZone.move-down-label\": \"Posunúť komponent nižšie\",\n    \"components.DynamicZone.move-up-label\": \"Posunúť komponent vyššie\",\n    \"components.DynamicZone.pick-compo\": \"Vyberte jeden komponent\",\n    \"components.DynamicZone.required\": \"Komponent je povinný\",\n    \"components.EmptyAttributesBlock.button\": \"Prejsť do nastavení\",\n    \"components.EmptyAttributesBlock.description\": \"Môžte upravovať nastavenia\",\n    \"components.FieldItem.linkToComponentLayout\": \"Nastaviť rozloženie komponenty\",\n    \"components.FieldSelect.label\": \"Pridať políčko\",\n    \"components.FilterOptions.button.apply\": \"Použiť\",\n    \"components.FiltersPickWrapper.PluginHeader.actions.apply\": \"Použiť\",\n    \"components.FiltersPickWrapper.PluginHeader.actions.clearAll\": \"Zmazať všetko\",\n    \"components.FiltersPickWrapper.PluginHeader.description\": \"Nastaviť podmienky pre filtrovanie záznamov\",\n    \"components.FiltersPickWrapper.PluginHeader.title.filter\": \"Filtre\",\n    \"components.FiltersPickWrapper.hide\": \"Skryť\",\n    \"components.LeftMenu.Search.label\": \"Vyhľadávať obsahový typ\",\n    \"components.LeftMenu.collection-types\": \"Collection Types\",\n    \"components.LeftMenu.single-types\": \"Single Types\",\n    \"components.LimitSelect.itemsPerPage\": \"Položky na stránku\",\n    \"components.NotAllowedInput.text\": \"Nemáte oprávnenia na zobrazenie tohto políčka\",\n    \"components.RepeatableComponent.error-message\": \"Jeden alebo viac komponentov obsahuje chybu(y)\",\n    \"components.Search.placeholder\": \"Hľadať záznam...\",\n    \"components.Select.draft-info-title\": \"Stav: Návrh\",\n    \"components.Select.publish-info-title\": \"Stav: Publikované\",\n    \"components.SettingsViewWrapper.pluginHeader.description.edit-settings\": \"Upravte vzhľad zobrazenia úprav.\",\n    \"components.SettingsViewWrapper.pluginHeader.description.list-settings\": \"Zvoľte nastavenia zobrazenia zoznamu.\",\n    \"components.SettingsViewWrapper.pluginHeader.title\": \"Nastavenia zobrazenia - {name}\",\n    \"components.TableDelete.delete\": \"Zmazať všetko\",\n    \"components.TableDelete.deleteSelected\": \"Odstrániť vyznačené\",\n    \"components.TableDelete.label\": \"{number, plural, one {# vybraný záznam} few {# vybrané záznamy} other {# vybraných záznamov}}\",\n    \"components.TableEmpty.withFilters\": \"Nenašiel sa žiaden {contentType} pre dané filtre...\",\n    \"components.TableEmpty.withSearch\": \"Nenašiel sa žiaden {contentType} spĺňujúci výraz ({search})...\",\n    \"components.TableEmpty.withoutFilter\": \"Žiadne záznamy\",\n    \"components.empty-repeatable\": \"Zatiaľ žiadne záznamy. Pridajte nový kliknutím na tlačidlo nižšie.\",\n    \"components.notification.info.maximum-requirement\": \"Dosiahli ste maximálny počet políčok\",\n    \"components.notification.info.minimum-requirement\": \"Políčko bolo pridané aby spĺňalo minimálne požiadavky\",\n    \"components.repeatable.reorder.error\": \"Počas preusporiadavania došlo k chybe, prosím skúste znovu\",\n    \"components.reset-entry\": \"Zrušiť záznam\",\n    \"components.uid.apply\": \"použiť\",\n    \"components.uid.available\": \"dostupné\",\n    \"components.uid.regenerate\": \"pregenerovať\",\n    \"components.uid.suggested\": \"odporúčané\",\n    \"components.uid.unavailable\": \"nedostupné\",\n    \"containers.Edit.Link.Layout\": \"Upraviť rozloženie\",\n    \"containers.Edit.Link.Model\": \"Upraviť obrahový typ\",\n    \"containers.Edit.addAnItem\": \"Pridať položku...\",\n    \"containers.Edit.clickToJump\": \"Kliknutím zobrazte položku\",\n    \"containers.Edit.delete\": \"Zmazať\",\n    \"containers.Edit.delete-entry\": \"Zmazať túto položku\",\n    \"containers.Edit.editing\": \"Úprava...\",\n    \"containers.Edit.information\": \"Informácie\",\n    \"containers.Edit.information.by\": \"Autor\",\n    \"containers.Edit.information.created\": \"Vytvorené\",\n    \"containers.Edit.information.draftVersion\": \"verzia návrhu\",\n    \"containers.Edit.information.editing\": \"Upravuje sa\",\n    \"containers.Edit.information.lastUpdate\": \"Naposledy upravené\",\n    \"containers.Edit.information.publishedVersion\": \"publikovaná verzia\",\n    \"containers.Edit.pluginHeader.title.new\": \"Vytvoriť záznam\",\n    \"containers.Edit.reset\": \"Zrušiť\",\n    \"containers.Edit.returnList\": \"Návrat do zoznamu\",\n    \"containers.Edit.seeDetails\": \"Detaily\",\n    \"containers.Edit.submit\": \"Uložiť\",\n    \"containers.EditSettingsView.modal-form.edit-field\": \"Upraviť políčko\",\n    \"containers.EditView.add.new-entry\": \"Pridať záznam\",\n    \"containers.EditView.notification.errors\": \"Formulár obsahuje chyby\",\n    \"containers.Home.introduction\": \"Pre úpravu záznamov kliknite na konkrétny link v ľavom menu.\",\n    \"containers.Home.pluginHeaderDescription\": \"Spravujte obsah cez robustné a intuitívne rozhranie.\",\n    \"containers.Home.pluginHeaderTitle\": \"Správca obsahu\",\n    \"containers.List.draft\": \"Návrh\",\n    \"containers.List.errorFetchRecords\": \"Chyba\",\n    \"containers.List.published\": \"Publikované\",\n    \"containers.list.displayedFields\": \"Zobrazené políčka\",\n    \"containers.list.items\": \"{number, plural, =0 {položiek} one {položka} few {položky} other {položiek}}\",\n    \"containers.list.table-headers.publishedAt\": \"Stav\",\n    \"containers.ListSettingsView.modal-form.edit-label\": \"Upraviť popis\",\n    \"containers.SettingPage.add.field\": \"Vložiť ďalšie políčko\",\n    \"containers.SettingPage.attributes\": \"Atribúty políčok\",\n    \"containers.SettingPage.attributes.description\": \"Nastavte poradie atribútov\",\n    \"containers.SettingPage.editSettings.description\": \"Potiahnutím nastavte rozloženie políčok\",\n    \"containers.SettingPage.editSettings.entry.title\": \"Názov záznamu\",\n    \"containers.SettingPage.editSettings.entry.title.description\": \"Nastavte zobrazené políčko záznamu\",\n    \"containers.SettingPage.editSettings.relation-field.description\": \"Nastaviť pole na zobrazenie v oboch zobrazeniach (zoznam, upravenie)\",\n    \"containers.SettingPage.editSettings.title\": \"Upraviť zobrazenie (nastavenia)\",\n    \"containers.SettingPage.layout\": \"Rozloženie\",\n    \"containers.SettingPage.listSettings.description\": \"Konfigurovať možnosti pre tento typ kolekcie\",\n    \"containers.SettingPage.listSettings.title\": \"Zobrazenie zoznamu (nastavenia)\",\n    \"containers.SettingPage.pluginHeaderDescription\": \"Konfigurovať špecifické nastavenia pre tento typ kolekcie\",\n    \"containers.SettingPage.settings\": \"Nastavenia\",\n    \"containers.SettingPage.view\": \"Vzhľad\",\n    \"containers.SettingViewModel.pluginHeader.title\": \"Správca obsahu - {name}\",\n    \"containers.SettingsPage.Block.contentType.description\": \"Nastavte špecifické vlastnosti\",\n    \"containers.SettingsPage.Block.contentType.title\": \"Kolekcie\",\n    \"containers.SettingsPage.Block.generalSettings.description\": \"Konfigurovať predvolené možnosti pre Vaše kolekcie\",\n    \"containers.SettingsPage.Block.generalSettings.title\": \"Všeobecné\",\n    \"containers.SettingsPage.pluginHeaderDescription\": \"Konfigurovať nastavenia pre všetky Vaše kolekcie a skupiny\",\n    \"containers.SettingsView.list.subtitle\": \"Konfigurovať rozloženie a zobrazenie stránky Vaších kolekcií a skupín\",\n    \"containers.SettingsView.list.title\": \"Zobraziť nastavenia\",\n    \"edit-settings-view.link-to-ctb.components\": \"Upraviť komponent\",\n    \"edit-settings-view.link-to-ctb.content-types\": \"Upraviť obsahový typ\",\n    \"emptyAttributes.button\": \"Ísť do tvorcu kolekcií\",\n    \"emptyAttributes.description\": \"Pridať prvé políčko do Vašej kolekcie\",\n    \"emptyAttributes.title\": \"Zatiaľ tu nie sú žiadne políčka\",\n    \"error.attribute.key.taken\": \"Táto hodnota už existuje\",\n    \"error.attribute.sameKeyAndName\": \"Hodnoty nesmú byť rovnaké\",\n    \"error.attribute.taken\": \"Toto políčko už existuje\",\n    \"error.contentTypeName.taken\": \"Toto meno už existuje\",\n    \"error.model.fetch\": \"Nastala chyba pri načitávaní nastavení modelov.\",\n    \"error.record.create\": \"Nastala chyba pri vytváraní záznamu.\",\n    \"error.record.delete\": \"Nastala chyba pri vymazávaní záznamu.\",\n    \"error.record.fetch\": \"Nastala chyba pri načitávaní záznamu.\",\n    \"error.record.update\": \"Nastala chyba pri upravovaní záznamu.\",\n    \"error.records.count\": \"Nastala chyba pri načitávaní počtu záznamov.\",\n    \"error.records.fetch\": \"Nastala chyba pri načitávaní záznamov.\",\n    \"error.schema.generation\": \"Nastala chyba pri vytváraní návrhu.\",\n    \"error.validation.json\": \"Toto nie je JSON formát\",\n    \"error.validation.max\": \"Táto hodnota je príliš vysoká.\",\n    \"error.validation.maxLength\": \"Táto hodnota je príliš dlhá.\",\n    \"error.validation.min\": \"Táto hodnota je príliš nízka.\",\n    \"error.validation.minLength\": \"Táto hodnota je príliš krátka.\",\n    \"error.validation.minSupMax\": \"Nemôže byť nadriadený\",\n    \"error.validation.regex\": \"Táto hodnota nespĺňa požadovaný vzor\",\n    \"error.validation.required\": \"Táto hodnota je povinná.\",\n    \"form.Input.bulkActions\": \"Povoliť hromadné akcie\",\n    \"form.Input.defaultSort\": \"Predvolený atribút pre zoradenie\",\n    \"form.Input.description\": \"Popis políčka\",\n    \"form.Input.description.placeholder\": \"Zobraziť meno v profile\",\n    \"form.Input.editable\": \"Editovateľné políčko\",\n    \"form.Input.filters\": \"Povoliť filtre\",\n    \"form.Input.label\": \"Názov\",\n    \"form.Input.label.inputDescription\": \"Táto hodnota prepisuje popis zobrazený v hlavičke tabuľky\",\n    \"form.Input.pageEntries\": \"Záznamy na stránku\",\n    \"form.Input.pageEntries.inputDescription\": \"Poznámka: Túto hodnotu môžete prepísať na stránke nastavení typu kolekcie.\",\n    \"form.Input.placeholder\": \"Placeholder\",\n    \"form.Input.placeholder.placeholder\": \"Hodnota\",\n    \"form.Input.search\": \"Povoliť vyhľadávanie\",\n    \"form.Input.search.field\": \"Povoliť vyhľadávanie na tomto políčku\",\n    \"form.Input.sort.field\": \"Povoliť zoradenie na tomto políčku\",\n    \"form.Input.sort.order\": \"Predvolené zoradenie\",\n    \"form.Input.wysiwyg\": \"Zobraziť ako WYSIWYG\",\n    \"global.displayedFields\": \"Zobrazené políčka\",\n    groups: groups,\n    \"groups.numbered\": \"Skupiny ({number})\",\n    \"header.name\": \"Obsah\",\n    \"link-to-ctb\": \"Upraviť model\",\n    models: models,\n    \"models.numbered\": \"Kolekcie ({number})\",\n    \"notification.error.displayedFields\": \"Aspoň jedno políčko musí byť zobrazené\",\n    \"notification.error.relationship.fetch\": \"Nastala chyba pri načitávaní vzťahu.\",\n    \"notification.info.SettingPage.disableSort\": \"Aspoň jedno políčko musí mať nastavené zoradenie\",\n    \"notification.info.minimumFields\": \"Aspoň jedno políčko musí byť zobrazené\",\n    \"notification.upload.error\": \"Nastala chyba pri nahrávaní súborov\",\n    pageNotFound: pageNotFound,\n    \"pages.ListView.header-subtitle\": \"{number, plural, =0 {# nájdených záznamov} one {# nájdený záznam} few {# nájdené záznamy} other {# nájdených záznamov}}\",\n    \"pages.NoContentType.button\": \"Vytvore svoj prvý obsahový typ\",\n    \"pages.NoContentType.text\": \"Nemáte vytvorené žiadne obsahové type. Vytvorte svoj prvý obsahový typ.\",\n    \"permissions.not-allowed.create\": \"Nemáte oprávnenia na vytvorenie dokumentu\",\n    \"permissions.not-allowed.update\": \"Nemáte oprávnenia na čítanie dokumentu\",\n    \"plugin.description.long\": \"Jednoduchý spôsob zobrazenia a úpravy dát v databáze.\",\n    \"plugin.description.short\": \"Jednoduchý spôsob zobrazenia a úpravy dát v databáze.\",\n    \"popover.display-relations.label\": \"Zobraziť prepojenia\",\n    \"select.currently.selected\": \"{count} aktuálne vybrané\",\n    \"success.record.delete\": \"Zmazané\",\n    \"success.record.publish\": \"Publikované\",\n    \"success.record.save\": \"Uložené\",\n    \"success.record.unpublish\": \"Nepublikované\",\n    \"utils.data-loaded\": \"{number, plural, =1 {Záznam bol úspešne načítaný} other {Záznamy boli úspešne načítané}}\",\n    \"apiError.This attribute must be unique\": \"Pole {field} musí byť jedinečné\",\n    \"popUpWarning.warning.has-draft-relations.title\": \"Potvrdenie\",\n    \"popUpWarning.warning.publish-question\": \"Stále si prajete publikovať?\",\n    \"popUpwarning.warning.has-draft-relations.button-confirm\": \"Áno, publikovať\",\n    \"popUpwarning.warning.has-draft-relations.message\": \"<b>{count, plural, one { prepojenie nie je publikované } few { prepojenia nie sú publikované } other { prepojení nie je publikovaných } }</b>, čo môže viesť k neočakávanému správaniu.\"\n};\n\nexport { sk as default, groups, models, pageNotFound };\n//# sourceMappingURL=sk.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,SAAS;AACb,IAAI,SAAS;AACb,IAAI,eAAe;AACnB,IAAI,KAAK;AAAA,EACL,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,uCAAuC;AAAA,EACvC,UAAU;AAAA,EACV,+BAA+B;AAAA,EAC/B,gCAAgC;AAAA,EAChC,+BAA+B;AAAA,EAC/B,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,gDAAgD;AAAA,EAChD,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,wCAAwC;AAAA,EACxC,6CAA6C;AAAA,EAC7C,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,gCAAgC;AAAA,EAChC,yCAAyC;AAAA,EACzC,4DAA4D;AAAA,EAC5D,+DAA+D;AAAA,EAC/D,0DAA0D;AAAA,EAC1D,2DAA2D;AAAA,EAC3D,sCAAsC;AAAA,EACtC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,uCAAuC;AAAA,EACvC,mCAAmC;AAAA,EACnC,gDAAgD;AAAA,EAChD,iCAAiC;AAAA,EACjC,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,yEAAyE;AAAA,EACzE,yEAAyE;AAAA,EACzE,qDAAqD;AAAA,EACrD,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,gCAAgC;AAAA,EAChC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,uCAAuC;AAAA,EACvC,+BAA+B;AAAA,EAC/B,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,uCAAuC;AAAA,EACvC,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,6BAA6B;AAAA,EAC7B,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA,EAC9B,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,6BAA6B;AAAA,EAC7B,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,kCAAkC;AAAA,EAClC,uCAAuC;AAAA,EACvC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,yBAAyB;AAAA,EACzB,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,qDAAqD;AAAA,EACrD,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,gCAAgC;AAAA,EAChC,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,6CAA6C;AAAA,EAC7C,qDAAqD;AAAA,EACrD,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,+DAA+D;AAAA,EAC/D,kEAAkE;AAAA,EAClE,6CAA6C;AAAA,EAC7C,iCAAiC;AAAA,EACjC,mDAAmD;AAAA,EACnD,6CAA6C;AAAA,EAC7C,kDAAkD;AAAA,EAClD,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,kDAAkD;AAAA,EAClD,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,6DAA6D;AAAA,EAC7D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,yCAAyC;AAAA,EACzC,sCAAsC;AAAA,EACtC,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,0BAA0B;AAAA,EAC1B,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,qCAAqC;AAAA,EACrC,0BAA0B;AAAA,EAC1B,2CAA2C;AAAA,EAC3C,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B;AAAA,EACA,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,eAAe;AAAA,EACf;AAAA,EACA,mBAAmB;AAAA,EACnB,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B;AAAA,EACA,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,4BAA4B;AAAA,EAC5B,qBAAqB;AAAA,EACrB,0CAA0C;AAAA,EAC1C,kDAAkD;AAAA,EAClD,yCAAyC;AAAA,EACzC,2DAA2D;AAAA,EAC3D,oDAAoD;AACxD;", "names": []}