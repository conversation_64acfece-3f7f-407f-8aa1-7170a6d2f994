{"version": 3, "sources": ["../../../@strapi/plugin-users-permissions/dist/admin/translations/sk.json.mjs"], "sourcesContent": ["var sk = {\n    \"BoundRoute.title\": \"URL endpoint naviazan<PERSON> k\",\n    \"EditForm.inputSelect.description.role\": \"<PERSON>ridá rolu k používateľovi.\",\n    \"EditForm.inputSelect.label.role\": \"Predvolená rola pre autorizovaných používateľov\",\n    \"EditForm.inputToggle.description.email\": \"Zakázať používateľovi vytvoriť viac účtov s rovnakou e-mailovou adresou pre rôznych poskytovateľov.\",\n    \"EditForm.inputToggle.description.email-confirmation\": \"Ak je povolené (ON), registrovaní používatelia dostanú potvrdzovací e-mail.\",\n    \"EditForm.inputToggle.description.email-confirmation-redirection\": \"URL na ktorú bude používateľ presmerovaný po potvrdení e-mailu.\",\n    \"EditForm.inputToggle.description.email-reset-password\": \"URL pre nastavenie nového hesla\",\n    \"EditForm.inputToggle.description.sign-up\": \"Ak je <PERSON> (OFF), registrácie nebudú povolené. Nikto sa nebude môcť registrovať bez ohľadu na zvoleného poskytovateľa.\",\n    \"EditForm.inputToggle.label.email\": \"Iba jedno konto pre jednu e-mailovú adresu\",\n    \"EditForm.inputToggle.label.email-confirmation\": \"Povoliť potvrdzovanie e-mailových adries\",\n    \"EditForm.inputToggle.label.email-confirmation-redirection\": \"URL pre potvrdenie e-mailovej adresy\",\n    \"EditForm.inputToggle.label.email-reset-password\": \"URL pre obnovu hesla\",\n    \"EditForm.inputToggle.label.sign-up\": \"Povoliť registrácie\",\n    \"HeaderNav.link.advancedSettings\": \"Pokročilé nastavenia\",\n    \"HeaderNav.link.emailTemplates\": \"Šablóny emailov\",\n    \"HeaderNav.link.providers\": \"Poskytovatelia\",\n    \"Plugin.permissions.plugins.description\": \"Zvoľte akcie, ktoré majú byť povolené pre plugin {name}.\",\n    \"Plugins.header.description\": \"Zobrazujú sa iba akcie naviazané na URL endpoint.\",\n    \"Plugins.header.title\": \"Oprávnenia\",\n    \"Policies.header.hint\": \"Vyberte akciu a kliknite na ikonku nastavení pre zobrazenie naviazanej URL\",\n    \"Policies.header.title\": \"Pokročilé nastavenia\",\n    \"PopUpForm.Email.email_templates.inputDescription\": \"Ak si nie ste istý ako používať premenné, {link}\",\n    \"PopUpForm.Email.options.from.email.label\": \"E-mail odosielateľa\",\n    \"PopUpForm.Email.options.from.email.placeholder\": \"<EMAIL>\",\n    \"PopUpForm.Email.options.from.name.label\": \"Meno odosielateľa\",\n    \"PopUpForm.Email.options.from.name.placeholder\": \"Janko Hraško\",\n    \"PopUpForm.Email.options.message.label\": \"Obsah e-mailu\",\n    \"PopUpForm.Email.options.object.label\": \"Predmet\",\n    \"PopUpForm.Email.options.response_email.label\": \"Odpovedať na e-mail\",\n    \"PopUpForm.Email.options.response_email.placeholder\": \"<EMAIL>\",\n    \"PopUpForm.Providers.enabled.description\": \"Ak je zakázané, používatelia nebudú môcť použiť tohto poskytovateľa.\",\n    \"PopUpForm.Providers.enabled.label\": \"Povoliť\",\n    \"PopUpForm.Providers.key.label\": \"Client ID\",\n    \"PopUpForm.Providers.key.placeholder\": \"TEXT\",\n    \"PopUpForm.Providers.redirectURL.front-end.label\": \"URL presmerovania do vašej aplikácie\",\n    \"PopUpForm.Providers.secret.label\": \"Client Secret\",\n    \"PopUpForm.Providers.secret.placeholder\": \"TEXT\",\n    \"PopUpForm.Providers.subdomain.label\": \"Host URI (Subdomain)\",\n    \"PopUpForm.Providers.subdomain.placeholder\": \"my.subdomain.com\",\n    \"PopUpForm.header.edit.email-templates\": \"Upraviť šablóny e-mailov\",\n    \"notification.success.submit\": \"Nastavenia boli uložené\",\n    \"plugin.description.long\": \"Zabezpečte vaše API pomocou JWT tokenov. Tento plugin taktiež podporuje ACL záznamy, ktoré umožňujú spravovať oprávnenia v rámci skupín používateľov.\",\n    \"plugin.description.short\": \"Zabezpečte vaše API pomocou JWT tokenov\",\n    \"plugin.name\": \"Roly a oprávnenia\"\n};\n\nexport { sk as default };\n//# sourceMappingURL=sk.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,KAAK;AAAA,EACL,oBAAoB;AAAA,EACpB,yCAAyC;AAAA,EACzC,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,uDAAuD;AAAA,EACvD,mEAAmE;AAAA,EACnE,yDAAyD;AAAA,EACzD,4CAA4C;AAAA,EAC5C,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,6DAA6D;AAAA,EAC7D,mDAAmD;AAAA,EACnD,sCAAsC;AAAA,EACtC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,0CAA0C;AAAA,EAC1C,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,oDAAoD;AAAA,EACpD,4CAA4C;AAAA,EAC5C,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,iDAAiD;AAAA,EACjD,yCAAyC;AAAA,EACzC,wCAAwC;AAAA,EACxC,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,iCAAiC;AAAA,EACjC,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,oCAAoC;AAAA,EACpC,0CAA0C;AAAA,EAC1C,uCAAuC;AAAA,EACvC,6CAA6C;AAAA,EAC7C,yCAAyC;AAAA,EACzC,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,eAAe;AACnB;", "names": []}