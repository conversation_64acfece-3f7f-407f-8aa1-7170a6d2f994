{"version": 3, "sources": ["../../../@strapi/content-manager/dist/admin/translations/sv.json.mjs"], "sourcesContent": ["var groups = \"Grupper\";\nvar models = \"Samlingstyper\";\nvar pageNotFound = \"Sidan hittas inte\";\nvar sv = {\n    \"App.schemas.data-loaded\": \"Scheman har lästs in\",\n    \"ListViewTable.relation-loaded\": \"Relationer har laddats\",\n    \"ListViewTable.relation-loading\": \"Relationer laddas\",\n    \"ListViewTable.relation-more\": \"Denna relation innehåller mer data än vad som visas\",\n    \"EditRelations.title\": \"Relationsdata\",\n    \"HeaderLayout.button.label-add-entry\": \"Skapa innehåll\",\n    \"api.id\": \"API-id\",\n    \"components.AddFilterCTA.add\": \"Filter\",\n    \"components.AddFilterCTA.hide\": \"Filter\",\n    \"components.DragHandle-label\": \"Drag\",\n    \"components.DraggableAttr.edit\": \"Klicka för att redigera\",\n    \"components.DraggableCard.delete.field\": \"Ta bort {item}\",\n    \"components.DraggableCard.edit.field\": \"<PERSON>igera {item}\",\n    \"components.DraggableCard.move.field\": \"Flytta {item}\",\n    \"components.ListViewTable.row-line\": \"artikelrad {nummer}\",\n    \"components.DynamicZone.ComponentPicker-label\": \"Välj en komponent\",\n    \"components.DynamicZone.add-component\": \"Lägg till en komponent till {componentName}\",\n    \"components.DynamicZone.delete-label\": \"Ta bort {name}\",\n    \"components.DynamicZone.error-message\": \"Komponenten innehåller fel\",\n    \"components.DynamicZone.missing-components\": \"Det {number, plural, =0 {# saknas komponenter} one {# saknas en komponent} other {are # saknas komponenter}}\",\n    \"components.DynamicZone.move-down-label\": \"Flytta ned komponenten\",\n    \"components.DynamicZone.move-up-label\": \"Flytta upp komponenten\",\n    \"components.DynamicZone.pick-compo\": \"Välj en komponent\",\n    \"components.DynamicZone.required\": \"Komponent krävs\",\n    \"components.EmptyAttributesBlock.button\": \"Gå till inställningssidan\",\n    \"components.EmptyAttributesBlock.description\": \"Du kan ändra dina inställningar\",\n    \"components.FieldItem.linkToComponentLayout\": \"Ställ in komponentens layout\",\n    \"components.FieldSelect.label\": \"Lägg till ett fält\",\n    \"components.FilterOptions.button.apply\": \"Verkställ\",\n    \"components.FiltersPickWrapper.PluginHeader.actions.apply\": \"Verkställ\",\n    \"components.FiltersPickWrapper.PluginHeader.actions.clearAll\": \"Rensa alla\",\n    \"components.FiltersPickWrapper.PluginHeader.description\": \"Ställ in de villkor som ska gälla för att filtrera posterna\",\n    \"components.FiltersPickWrapper.PluginHeader.title.filter\": \"Filter\",\n    \"components.FiltersPickWrapper.hide\": \"Göm\",\n    \"components.LeftMenu.Search.label\": \"Sök efter en innehållstyp\",\n    \"components.LeftMenu.collection-types\": \"Samlingstyper\",\n    \"components.LeftMenu.single-types\": \"Engångstyper\",\n    \"components.LimitSelect.itemsPerPage\": \"Objekt per sida\",\n    \"components.NotAllowedInput.text\": \"Ingen behörighet att se detta fält\",\n    \"components.RepeatableComponent.error-message\": \"Komponenten/komponenterna innehåller fel\",\n    \"components.Search.placeholder\": \"Sök efter en post...\",\n    \"components.Select.draft-info-title\": \"Status: Utkast\",\n    \"components.Select.publish-info-title\": \"Status: Publicerat\",\n    \"components.SettingsViewWrapper.pluginHeader.description.edit-settings\": \"Anpassa hur redigeringsvyn kommer att se ut.\",\n    \"components.SettingsViewWrapper.pluginHeader.description.list-settings\": \"Definiera inställningarna för listvyn.\",\n    \"components.SettingsViewWrapper.pluginHeader.title\": \"Konfigurera vyn - {name}\",\n    \"components.TableDelete.delete\": \"Ta bort allt\",\n    \"components.TableDelete.deleteSelected\": \"Ta bort valda\",\n    \"components.TableDelete.label\": \"{number, plural, one {# post} other {# poster}} valda\",\n    \"components.TableEmpty.withFilters\": \"Det finns inga {contentType} med de valda filtren...\",\n    \"components.TableEmpty.withSearch\": \"Det finns inga {contentType} som motsvarar sökningen ({search})...\",\n    \"components.TableEmpty.withoutFilter\": \"Det finns inga {contentType}...\",\n    \"components.empty-repeatable\": \"Inga poster än. Klicka på knappen nedan för att lägga till en.\",\n    \"components.notification.info.maximum-requirement\": \"Du har redan nått det maximala antalet fält\",\n    \"components.notification.info.minimum-requirement\": \"Ett fält har lagts till för att matcha minimikravet\",\n    \"components.repeatable.reorder.error\": \"Ett fel uppstod när du ändrade ordningen på komponentens fält, försök igen\",\n    \"components.reset-entry\": \"Återställ posten\",\n    \"components.uid.apply\": \"verkställ\",\n    \"components.uid.available\": \"Tillgänglig\",\n    \"components.uid.regenerate\": \"Återgenerera\",\n    \"components.uid.suggested\": \"föreslaget\",\n    \"components.uid.unavailable\": \"Otillgänglig\",\n    \"containers.Edit.Link.Layout\": \"Konfigurera layouten\",\n    \"containers.Edit.Link.Model\": \"Redigera samlingstypen\",\n    \"containers.Edit.addAnItem\": \"Lägg till ett objekt...\",\n    \"containers.Edit.clickToJump\": \"Klicka för att gå till posten\",\n    \"containers.Edit.delete\": \"Ta bort\",\n    \"containers.Edit.delete-entry\": \"Ta bort denna posten\",\n    \"containers.Edit.editing\": \"Redigerar...\",\n    \"containers.Edit.information\": \"Information\",\n    \"containers.Edit.information.by\": \"Av\",\n    \"containers.Edit.information.created\": \"Skapad\",\n    \"containers.Edit.information.draftVersion\": \"utkastsversion\",\n    \"containers.Edit.information.editing\": \"Redigerar\",\n    \"containers.Edit.information.lastUpdate\": \"Senast uppdatering\",\n    \"containers.Edit.information.publishedVersion\": \"publicerad version\",\n    \"containers.Edit.pluginHeader.title.new\": \"Skapa nytt innehåll\",\n    \"containers.Edit.reset\": \"Återställ\",\n    \"containers.Edit.returnList\": \"Tillbaka till listan\",\n    \"containers.Edit.seeDetails\": \"Detaljer\",\n    \"containers.Edit.submit\": \"Spara\",\n    \"containers.EditSettingsView.modal-form.edit-field\": \"Redigera fältet\",\n    \"containers.EditView.add.new-entry\": \"Lägg till en post\",\n    \"containers.EditView.notification.errors\": \"Formuläret innehåller några fel\",\n    \"containers.Home.introduction\": \"För att redigera dina poster gå till postens länk i vänstermenyn. Detta plugin har inte ett fungerande sätt att redigera inställningar och är fortfarande under aktiv utveckling.\",\n    \"containers.Home.pluginHeaderDescription\": \"Hantera poster genom ett kraftfullt och vackert gränssnitt.\",\n    \"containers.Home.pluginHeaderTitle\": \"Innehållshanteraren\",\n    \"containers.List.draft\": \"Utkast\",\n    \"containers.List.errorFetchRecords\": \"Fel\",\n    \"containers.List.published\": \"Publicerad\",\n    \"containers.list.displayedFields\": \"Visade fält\",\n    \"containers.list.items\": \"{number, plural, =0 {poster} one {post} other {poster}}\",\n    \"containers.list.table-headers.publishedAt\": \"State\",\n    \"containers.ListSettingsView.modal-form.edit-label\": \"Redigera {fieldName}\",\n    \"containers.SettingPage.add.field\": \"Infoga ett annat fält\",\n    \"containers.SettingPage.attributes\": \"Attributfält\",\n    \"containers.SettingPage.attributes.description\": \"Definiera ordningen på attributen\",\n    \"containers.SettingPage.editSettings.description\": \"Dra och släpp fälten för att skapa layouten\",\n    \"containers.SettingPage.editSettings.entry.title\": \"Posttitel\",\n    \"containers.SettingPage.editSettings.entry.title.description\": \"Ställ in det uppvisade fältet för din post\",\n    \"containers.SettingPage.editSettings.relation-field.description\": \"Ställ in det uppvisade fältet i både redigerings- och listvyn\",\n    \"containers.SettingPage.editSettings.title\": \"Redigera vy (inställningar)\",\n    \"containers.SettingPage.layout\": \"Layout\",\n    \"containers.SettingPage.listSettings.description\": \"Konfigurera alternativen för denna samlingstyp\",\n    \"containers.SettingPage.listSettings.title\": \"Listvy (inställningar)\",\n    \"containers.SettingPage.pluginHeaderDescription\": \"Konfigurera de specifika inställningarna för denna samlingstyp\",\n    \"containers.SettingPage.settings\": \"Inställningar\",\n    \"containers.SettingPage.view\": \"Visa\",\n    \"containers.SettingViewModel.pluginHeader.title\": \"Innehållshanterare - {name}\",\n    \"containers.SettingsPage.Block.contentType.description\": \"Konfigurera de specificerade inställningarna\",\n    \"containers.SettingsPage.Block.contentType.title\": \"Samlingstyper\",\n    \"containers.SettingsPage.Block.generalSettings.description\": \"Konfigurera standardalternativen för dina samlingstyper\",\n    \"containers.SettingsPage.Block.generalSettings.title\": \"Allmänt\",\n    \"containers.SettingsPage.pluginHeaderDescription\": \"Konfigurera inställningarna för alla dina samlingstyper och grupper\",\n    \"containers.SettingsView.list.subtitle\": \"Konfigurera layouten och visningen av dina samlingstyper och grupper\",\n    \"containers.SettingsView.list.title\": \"Visa konfigurationer\",\n    \"edit-settings-view.link-to-ctb.components\": \"Redigera komponenten\",\n    \"edit-settings-view.link-to-ctb.content-types\": \"Redigera innehållstypen\",\n    \"emptyAttributes.button\": \"Gå till samlingstypskaparen\",\n    \"emptyAttributes.description\": \"Lägg till ditt första fält i din samlingstyp\",\n    \"emptyAttributes.title\": \"Det finns inga fält ännu\",\n    \"error.attribute.key.taken\": \"Detta värde finns redan\",\n    \"error.attribute.sameKeyAndName\": \"Måste vara unika\",\n    \"error.attribute.taken\": \"Detta fältnamn finns redan\",\n    \"error.contentTypeName.taken\": \"Detta namn finns redan\",\n    \"error.model.fetch\": \"Ett fel inträffade under hämtning av modellkonfigurationen.\",\n    \"error.record.create\": \"Ett fel uppstod under skapandet av posten.\",\n    \"error.record.delete\": \"Ett fel uppstod under raderingen av posten.\",\n    \"error.record.fetch\": \"Ett fel uppstod under posthämtningen.\",\n    \"error.record.update\": \"Ett fel uppstod under poständringen.\",\n    \"error.records.count\": \"Ett fel uppstod under räkning av posthämtningen\",\n    \"error.records.fetch\": \"Ett fel uppstod under hämtning av poster.\",\n    \"error.schema.generation\": \"Ett fel uppstod under schemagenerering.\",\n    \"error.validation.json\": \"Det här är inte JSON\",\n    \"error.validation.max\": \"Värdet är för högt.\",\n    \"error.validation.maxLength\": \"Värdet är för långt.\",\n    \"error.validation.min\": \"Värdet är för lågt.\",\n    \"error.validation.minLength\": \"Värdet är för kort.\",\n    \"error.validation.minSupMax\": \"Kan inte vara överstigande\",\n    \"error.validation.regex\": \"Värdet matchar inte regexet.\",\n    \"error.validation.required\": \"Detta inputvärde krävs.\",\n    \"form.Input.bulkActions\": \"Aktivera massåtgärder\",\n    \"form.Input.defaultSort\": \"Standardsorteringsattribut\",\n    \"form.Input.description\": \"Beskrivning\",\n    \"form.Input.description.placeholder\": \"Visa namn i profilen\",\n    \"form.Input.editable\": \"Redigerbart fält\",\n    \"form.Input.filters\": \"Aktivera filter\",\n    \"form.Input.label\": \"Etikett\",\n    \"form.Input.label.inputDescription\": \"Detta värde överskrider etiketten som visas i tabellhuvudet\",\n    \"form.Input.pageEntries\": \"Poster per sida\",\n    \"form.Input.pageEntries.inputDescription\": \"Obs! Du kan överskrida det här värdet på inställningssidan för samlingstypen.\",\n    \"form.Input.placeholder\": \"Placeholder\",\n    \"form.Input.placeholder.placeholder\": \"Mitt fantastiska värde\",\n    \"form.Input.search\": \"Aktivera sökning\",\n    \"form.Input.search.field\": \"Aktivera sökning av det här fältet\",\n    \"form.Input.sort.field\": \"Aktivera sortering av det här fältet\",\n    \"form.Input.sort.order\": \"Standardsorteringsordning\",\n    \"form.Input.wysiwyg\": \"Visa som WYSIWYG\",\n    \"global.displayedFields\": \"Visade fält\",\n    groups: groups,\n    \"groups.numbered\": \"Grupper ({number})\",\n    \"header.name\": \"Innehåll\",\n    \"link-to-ctb\": \"Redigera modellen\",\n    models: models,\n    \"models.numbered\": \"Samlingstyper ({number})\",\n    \"notification.error.displayedFields\": \"Du behöver minst ett fält som visas upp\",\n    \"notification.error.relationship.fetch\": \"Ett fel uppstod under relationshämtning.\",\n    \"notification.info.SettingPage.disableSort\": \"Du måste ha ett attribut med sortering tillåtet\",\n    \"notification.info.minimumFields\": \"Du måste ha minst ett fält uppvisat\",\n    \"notification.upload.error\": \"Ett fel uppstod när dina filer laddades upp\",\n    pageNotFound: pageNotFound,\n    \"pages.ListView.header-subtitle\": \"{number, plural, =0 {# poster} one {# post} other {# poster}} hittade\",\n    \"pages.NoContentType.button\": \"Skapa din första innehållstyp\",\n    \"pages.NoContentType.text\": \"Du har inget innehåll än, vi föreslår att du skapar din första innehållstyp.\",\n    \"permissions.not-allowed.create\": \"Du får inte skapa ett dokument\",\n    \"permissions.not-allowed.update\": \"Du får inte se detta dokument\",\n    \"plugin.description.long\": \"Snabbt sätt att se, redigera och ta bort data i din databas.\",\n    \"plugin.description.short\": \"Snabbt sätt att se, redigera och ta bort data i din databas.\",\n    \"popover.display-relations.label\": \"Visa relationer\",\n    \"select.currently.selected\": \"{count} valda\",\n    \"success.record.delete\": \"Borttagna\",\n    \"success.record.publish\": \"Publicerade\",\n    \"success.record.save\": \"Sparade\",\n    \"success.record.unpublish\": \"Opublicerade\",\n    \"utils.data-loaded\": \"{number, plural, =1 {Posten} other {Posterna}} har laddats\",\n    \"apiError.This attribute must be unique\": \"{field} måste vara unikt\",\n    \"popUpWarning.warning.has-draft-relations.title\": \"Bekräfta\",\n    \"popUpWarning.warning.publish-question\": \"Vill du fortfarande publicera?\",\n    \"popUpwarning.warning.has-draft-relations.button-confirm\": \"Ja, publicera\",\n    \"popUpwarning.warning.has-draft-relations.message\": \"<b>{count, plural, =0 { av dina innehållsrelationer är} one { av dina innehållsrelationer är} other { av dina innehållsrelationer är}}</b> inte publicerade ännu.<br></br>Det kan skapa trasiga länkar och fel i ditt projekt.\"\n};\n\nexport { sv as default, groups, models, pageNotFound };\n//# sourceMappingURL=sv.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,SAAS;AACb,IAAI,SAAS;AACb,IAAI,eAAe;AACnB,IAAI,KAAK;AAAA,EACL,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,uCAAuC;AAAA,EACvC,UAAU;AAAA,EACV,+BAA+B;AAAA,EAC/B,gCAAgC;AAAA,EAChC,+BAA+B;AAAA,EAC/B,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,gDAAgD;AAAA,EAChD,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,wCAAwC;AAAA,EACxC,6CAA6C;AAAA,EAC7C,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,gCAAgC;AAAA,EAChC,yCAAyC;AAAA,EACzC,4DAA4D;AAAA,EAC5D,+DAA+D;AAAA,EAC/D,0DAA0D;AAAA,EAC1D,2DAA2D;AAAA,EAC3D,sCAAsC;AAAA,EACtC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,uCAAuC;AAAA,EACvC,mCAAmC;AAAA,EACnC,gDAAgD;AAAA,EAChD,iCAAiC;AAAA,EACjC,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,yEAAyE;AAAA,EACzE,yEAAyE;AAAA,EACzE,qDAAqD;AAAA,EACrD,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,gCAAgC;AAAA,EAChC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,uCAAuC;AAAA,EACvC,+BAA+B;AAAA,EAC/B,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,uCAAuC;AAAA,EACvC,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,6BAA6B;AAAA,EAC7B,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA,EAC9B,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,6BAA6B;AAAA,EAC7B,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,kCAAkC;AAAA,EAClC,uCAAuC;AAAA,EACvC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,yBAAyB;AAAA,EACzB,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,qDAAqD;AAAA,EACrD,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,gCAAgC;AAAA,EAChC,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,6CAA6C;AAAA,EAC7C,qDAAqD;AAAA,EACrD,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,+DAA+D;AAAA,EAC/D,kEAAkE;AAAA,EAClE,6CAA6C;AAAA,EAC7C,iCAAiC;AAAA,EACjC,mDAAmD;AAAA,EACnD,6CAA6C;AAAA,EAC7C,kDAAkD;AAAA,EAClD,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,kDAAkD;AAAA,EAClD,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,6DAA6D;AAAA,EAC7D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,yCAAyC;AAAA,EACzC,sCAAsC;AAAA,EACtC,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,0BAA0B;AAAA,EAC1B,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,qCAAqC;AAAA,EACrC,0BAA0B;AAAA,EAC1B,2CAA2C;AAAA,EAC3C,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B;AAAA,EACA,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,eAAe;AAAA,EACf;AAAA,EACA,mBAAmB;AAAA,EACnB,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B;AAAA,EACA,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,4BAA4B;AAAA,EAC5B,qBAAqB;AAAA,EACrB,0CAA0C;AAAA,EAC1C,kDAAkD;AAAA,EAClD,yCAAyC;AAAA,EACzC,2DAA2D;AAAA,EAC3D,oDAAoD;AACxD;", "names": []}