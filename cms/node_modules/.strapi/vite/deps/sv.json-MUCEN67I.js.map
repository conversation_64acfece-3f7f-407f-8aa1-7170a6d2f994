{"version": 3, "sources": ["../../../@strapi/plugin-users-permissions/dist/admin/translations/sv.json.mjs"], "sourcesContent": ["var sv = {\n    \"BoundRoute.title\": \"Bind rutt till\",\n    \"EditForm.inputSelect.description.role\": \"Den bifogar den nya autentiserade användaren till den valda rollen.\",\n    \"EditForm.inputSelect.label.role\": \"Standardroll för autentiserade användare\",\n    \"EditForm.inputToggle.description.email\": \"Tillåt ej användaren att skapa flera konton med samma e-postadress med olika autentiseringstjänster.\",\n    \"EditForm.inputToggle.description.email-confirmation\": \"När aktiverat (PÅ) får nya registrerade användare ett bekräftelsemeddelande.\",\n    \"EditForm.inputToggle.description.email-confirmation-redirection\": \"När du har bekräftat din e-post väljer du var du ska omdirigeras.\",\n    \"EditForm.inputToggle.description.email-reset-password\": \"URL:en till din applikations sida för återställning av lösenord\",\n    \"EditForm.inputToggle.description.sign-up\": \"<PERSON>är inaktiverad (AV) är registreringsprocessen förbjuden. Ingen kan prenumerera längre oavsett vilken autentiseringstjänst som används.\",\n    \"EditForm.inputToggle.label.email\": \"Ett konto per e-postadress\",\n    \"EditForm.inputToggle.label.email-confirmation\": \"Aktivera e-postbekräftelse\",\n    \"EditForm.inputToggle.label.email-confirmation-redirection\": \"Omdirigerings-url\",\n    \"EditForm.inputToggle.label.email-reset-password\": \"Återställ lösenordssidan\",\n    \"EditForm.inputToggle.label.sign-up\": \"Tillåt registreringar\",\n    \"EditForm.inputToggle.placeholder.email-confirmation-redirection\": \"t.ex: https://yourfrontend.com/email-confirmation-redirection\",\n    \"EditForm.inputToggle.placeholder.email-reset-password\": \"t.ex: https://yourfrontend.com/reset-password\",\n    \"EditPage.form.roles\": \"Rolldetaljer\",\n    \"Email.template.data.loaded\": \"E-postmallar har laddats\",\n    \"Email.template.email_confirmation\": \"Bekräftelse av e-postadress\",\n    \"Email.template.form.edit.label\": \"Redigera en mall\",\n    \"Email.template.table.action.label\": \"händelse\",\n    \"Email.template.table.icon.label\": \"ikon\",\n    \"Email.template.table.name.label\": \"namn\",\n    \"Form.advancedSettings.data.loaded\": \"Data för avancerade inställningar har laddats\",\n    \"HeaderNav.link.advancedSettings\": \"Avancerade inställningar\",\n    \"HeaderNav.link.emailTemplates\": \"E-postmall\",\n    \"HeaderNav.link.providers\": \"Autentiseringstjänster\",\n    \"Plugin.permissions.plugins.description\": \"Definiera alla tillåtna åtgärder för {name} plugin.\",\n    \"Plugins.header.description\": \"Endast åtgärder som är bundna av en rutt listas nedan.\",\n    \"Plugins.header.title\": \"Behörigheter\",\n    \"Policies.header.hint\": \"Välj programmets åtgärder eller plugins åtgärder och klicka på kugghjulsikonen för att visa den bundna rutten\",\n    \"Policies.header.title\": \"Avancerade inställningar\",\n    \"PopUpForm.Email.email_templates.inputDescription\": \"Om du är osäker på hur du använder variabler, {link}\",\n    \"PopUpForm.Email.link.documentation\": \"Kolla in vår dokumentation.\",\n    \"PopUpForm.Email.options.from.email.label\": \"Avsändarens e-postadress\",\n    \"PopUpForm.Email.options.from.email.placeholder\": \"<EMAIL>\",\n    \"PopUpForm.Email.options.from.name.label\": \"Avsändarens namn\",\n    \"PopUpForm.Email.options.from.name.placeholder\": \"Kai Doe\",\n    \"PopUpForm.Email.options.message.label\": \"Meddelande\",\n    \"PopUpForm.Email.options.object.label\": \"Ämne\",\n    \"PopUpForm.Email.options.object.placeholder\": \"Bekräfta din e-postadress för %APP_NAME%\",\n    \"PopUpForm.Email.options.response_email.label\": \"Svarsmail\",\n    \"PopUpForm.Email.options.response_email.placeholder\": \"<EMAIL>\",\n    \"PopUpForm.Providers.enabled.description\": \"Om inaktiverat kan användarna inte använda den här tjänsten.\",\n    \"PopUpForm.Providers.enabled.label\": \"Tillåt\",\n    \"PopUpForm.Providers.key.label\": \"Klient ID\",\n    \"PopUpForm.Providers.key.placeholder\": \"TEXT\",\n    \"PopUpForm.Providers.redirectURL.front-end.label\": \"Omdirigerings-URL: en till din front-end-app\",\n    \"PopUpForm.Providers.redirectURL.label\": \"Webbadressen för omdirigering som ska läggas till i {Provider} applikationskonfigurationer\",\n    \"PopUpForm.Providers.secret.label\": \"Klient hemlighet\",\n    \"PopUpForm.Providers.secret.placeholder\": \"TEXT\",\n    \"PopUpForm.Providers.subdomain.label\": \"Host URI (Subdomän)\",\n    \"PopUpForm.Providers.subdomain.placeholder\": \"min.subdoman.com\",\n    \"PopUpForm.header.edit.email-templates\": \"Redigera e-postmallar\",\n    \"PopUpForm.header.edit.providers\": \"Redigera tjänst\",\n    \"Providers.data.loaded\": \"Tjänster har laddats in\",\n    \"Providers.status\": \"Status\",\n    \"Roles.empty\": \"Du har inga roller än.\",\n    \"Roles.empty.search\": \"Inga roller matchar sökningen.\",\n    \"Settings.roles.deleted\": \"Roll borttagen\",\n    \"Settings.roles.edited\": \"Roll redigerad\",\n    \"Settings.section-label\": \"Roller och behörigheter\",\n    \"components.Input.error.validation.email\": \"E-postadressen är ogiltig\",\n    \"components.Input.error.validation.json\": \"Detta är inte giltig JSON\",\n    \"components.Input.error.validation.max\": \"Värdet är för högt.\",\n    \"components.Input.error.validation.maxLength\": \"Värdet är för långt.\",\n    \"components.Input.error.validation.min\": \"Värdet är för lågt.\",\n    \"components.Input.error.validation.minLength\": \"Värdet är för kort.\",\n    \"components.Input.error.validation.minSupMax\": \"Minsta värdet är större än maximalt värde.\",\n    \"components.Input.error.validation.regex\": \"Värdet matchar inte regex-mönstret.\",\n    \"components.Input.error.validation.required\": \"Värdet är obligatoriskt.\",\n    \"components.Input.error.validation.unique\": \"Detta värdet är redan använt.\",\n    \"notification.success.submit\": \"Inställningar har uppdaterats\",\n    \"page.title\": \"Inställningar - Roller\",\n    \"plugin.description.long\": \"Skydda ditt API med en fullständig autentiseringsprocess baserad på JWT. Detta plugin har också en ACL-strategi som låter dig hantera behörigheterna mellan användargrupperna.\",\n    \"plugin.description.short\": \"Skydda ditt API med en fullständig autentiseringsprocess baserad på JWT\",\n    \"plugin.name\": \"Roller och behörigheter\",\n    \"popUpWarning.button.cancel\": \"Avbryt\",\n    \"popUpWarning.button.confirm\": \"Bekräfta\",\n    \"popUpWarning.title\": \"Var god bekräfta\",\n    \"popUpWarning.warning.cancel\": \"Är du säker att du vill avbryta dina ändringar?\"\n};\n\nexport { sv as default };\n//# sourceMappingURL=sv.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,KAAK;AAAA,EACL,oBAAoB;AAAA,EACpB,yCAAyC;AAAA,EACzC,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,uDAAuD;AAAA,EACvD,mEAAmE;AAAA,EACnE,yDAAyD;AAAA,EACzD,4CAA4C;AAAA,EAC5C,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,6DAA6D;AAAA,EAC7D,mDAAmD;AAAA,EACnD,sCAAsC;AAAA,EACtC,mEAAmE;AAAA,EACnE,yDAAyD;AAAA,EACzD,uBAAuB;AAAA,EACvB,8BAA8B;AAAA,EAC9B,qCAAqC;AAAA,EACrC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,0CAA0C;AAAA,EAC1C,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,oDAAoD;AAAA,EACpD,sCAAsC;AAAA,EACtC,4CAA4C;AAAA,EAC5C,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,iDAAiD;AAAA,EACjD,yCAAyC;AAAA,EACzC,wCAAwC;AAAA,EACxC,8CAA8C;AAAA,EAC9C,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,iCAAiC;AAAA,EACjC,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,0CAA0C;AAAA,EAC1C,uCAAuC;AAAA,EACvC,6CAA6C;AAAA,EAC7C,yCAAyC;AAAA,EACzC,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,oBAAoB;AAAA,EACpB,eAAe;AAAA,EACf,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,4CAA4C;AAAA,EAC5C,+BAA+B;AAAA,EAC/B,cAAc;AAAA,EACd,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,eAAe;AAAA,EACf,8BAA8B;AAAA,EAC9B,+BAA+B;AAAA,EAC/B,sBAAsB;AAAA,EACtB,+BAA+B;AACnC;", "names": []}