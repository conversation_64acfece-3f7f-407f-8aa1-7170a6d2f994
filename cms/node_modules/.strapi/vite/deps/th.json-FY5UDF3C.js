import "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/admin/src/translations/th.json.mjs
var Analytics = "การวิเคราะห์";
var Documentation = "เอกสารคู่มือ";
var Email = "อีเมล";
var Password = "รหัสผ่าน";
var Provider = "ผู้ให้บริการ";
var ResetPasswordToken = "โทเค็นสำหรับรีเซ็ตรหัสผ่าน";
var Role = "บทบาท";
var Username = "ชื่อผู้ใช้";
var Users = "Users";
var th = {
  Analytics,
  "Auth.components.Oops.text": "แอคเคาต์ของคุณถูกระงับชั่วคราว",
  "Auth.form.button.forgot-password": "ส่งอีเมล",
  "Auth.form.button.go-home": "กลับไปยังหน้าหลัก",
  "Auth.form.button.login": "ล็อกอิน",
  "Auth.form.button.register": "มาเริ่มกันเถอะ",
  "Auth.form.confirmPassword.label": "ยืนยันรหัสผ่าน",
  "Auth.form.email.label": "อีเมล",
  "Auth.form.email.placeholder": "<EMAIL>",
  "Auth.form.error.blocked": "แอคเคาต์ของคุณถูกบล็อกโดยผู้ดูแลระบบ",
  "Auth.form.error.code.provide": "มีโค้ดที่ไม่ถูกต้อง",
  "Auth.form.error.confirmed": "อีเมลแอคเคาต์ของคุณไม่ได้รับการยืนยัน",
  "Auth.form.error.email.invalid": "อีเมลนี้ไม่ถูกต้อง",
  "Auth.form.error.email.provide": "โปรดป้อนชื่อผู้ใช้หรืออีเมลของคุณ",
  "Auth.form.error.email.taken": "อีเมลถูกใช้ไปแล้ว",
  "Auth.form.error.invalid": "ตัวระบุหรือรหัสผ่านไม่ถูกต้อง",
  "Auth.form.error.params.provide": "มีพารามิเตอร์ที่ระบุไม่ถูกต้อง",
  "Auth.form.error.password.format": "รหัสผ่านของคุณไม่สามารถมีสัญลักษณ์ `$` ได้มากกว่าสามครั้ง",
  "Auth.form.error.password.local": "ผู้ใช้นี้ไม่เคยตั้งค่ารหัสผ่าน, โปรดล็อกอินผ่านผู้ให้บริการที่ใช้ในระหว่างการสร้างแอคเคาต์",
  "Auth.form.error.password.matching": "รหัสผ่านไม่ตรงกัน",
  "Auth.form.error.password.provide": "โปรดป้อนรหัสผ่านของคุณ",
  "Auth.form.error.ratelimit": "มีความพยายามมากเกินไป โปรดลองใหม่อีกครั้งในนาทีถัดไป",
  "Auth.form.error.user.not-exist": "ไม่มีอีเมลนี้อยู่",
  "Auth.form.error.username.taken": "ชื่อผู้ใช้ถูกใช้อยู่แล้ว",
  "Auth.form.firstname.label": "ชื่อ",
  "Auth.form.firstname.placeholder": "Kai",
  "Auth.form.forgot-password.email.label": "ป้อนอีเมลของคุณ",
  "Auth.form.forgot-password.email.label.success": "ส่งอีเมลไปยัง",
  "Auth.form.lastname.label": "นามสกุล",
  "Auth.form.lastname.placeholder": "Doe",
  "Auth.form.register.news.label": "อัพเดตให้ฉันเกี่ยวกับคุณลักษณะใหม่และการปรับปรุงที่กำลังจะเกิดขึ้น (โดยการทำสิ่งนี้คุณยอมรับ {terms} และ {policy})",
  "Auth.form.rememberMe.label": "จำฉันไว้",
  "Auth.form.username.label": "ชื่อผู้ใช้",
  "Auth.form.username.placeholder": "Kai Doe",
  "Auth.link.forgot-password": "ลืมรหัสผ่านของคุณ?",
  "Auth.link.ready": "พร้อมที่จะลงชื่อเข้าใช้?",
  "Auth.link.signin": "ลงชื่อเข้าใช้",
  "Auth.link.signin.account": "มีแอคเคาต์แล้วหรือไม่?",
  "Auth.privacy-policy-agreement.policy": "นโยบายความเป็นส่วนตัว",
  "Auth.privacy-policy-agreement.terms": "เงื่อนไข",
  "Content Manager": "ตัวจัดการเนื้อหา",
  "Content Type Builder": "ตัวสร้าง Content-Types",
  Documentation,
  Email,
  "Files Upload": "อัพโหลดไฟล์",
  "HomePage.head.title": "โฮมเพจ",
  "HomePage.roadmap": "ดูโรดแม็พของเรา",
  "HomePage.welcome.congrats": "ยินดีด้วย!",
  "HomePage.welcome.congrats.content": "คุณได้ล็อกอินเป็นผู้ดูแลระบบคนแรก เพื่อค้นพบคุณสมบัติอันทรงพลังที่ให้ไว้กับ Strapi,",
  "HomePage.welcome.congrats.content.bold": "เราแนะนำให้คุณสร้างชนิดคอลเล็กชัน-ชนิดแรกของคุณ",
  "Media Library": "ไลบรารีสื่อ",
  "New entry": "สร้างรายการใหม่",
  Password,
  Provider,
  ResetPasswordToken,
  Role,
  "Roles & Permissions": "บทบาท & การอนุญาต",
  "Roles.ListPage.notification.delete-all-not-allowed": "ไม่สามารถลบบางบทบาทได้เนื่องจากบทบาทถูกเชื่อมโยงกับผู้ใช้",
  "Roles.ListPage.notification.delete-not-allowed": "บทบาทไม่สามารถถูกลบได้ถ้าเชื่อมโยงกับผู้ใช้",
  "Roles.components.List.empty.withSearch": "ไม่มีบทบาทที่สอดคล้องกับการค้นหา ({search})...",
  "Settings.PageTitle": "ค่าติดตั้ง - {name}",
  "Settings.error": "ข้อผิดพลาด",
  "Settings.global": "ค่าติดตั้งโกลบอล",
  "Settings.permissions": "พาเนลการบริหารระบบ",
  "Settings.permissions.category": "ค่าติดตั้งสิทธิ์สำหรับ {category}",
  "Settings.permissions.conditions.anytime": "ทุกที่ทุกเวลา",
  "Settings.permissions.conditions.apply": "นำไปใช้",
  "Settings.permissions.conditions.can": "สามารถ",
  "Settings.permissions.conditions.conditions": "กำหนดเงื่อนไข",
  "Settings.permissions.conditions.links": "ลิงก์",
  "Settings.permissions.conditions.no-actions": "ไม่มีการดำเนินการ",
  "Settings.permissions.conditions.or": "หรือ",
  "Settings.permissions.conditions.when": "เมื่อ",
  "Settings.permissions.users.create": "สร้างผู้ใช้ใหม่",
  "Settings.permissions.users.email": "อีเมล",
  "Settings.permissions.users.firstname": "ชื่อ",
  "Settings.permissions.users.lastname": "นามสกุล",
  "Settings.roles.create.description": "กำหนดสิทธิที่กำหนดให้กับบทบาท",
  "Settings.roles.create.title": "สร้างบทบาท",
  "Settings.roles.created": "บทบาทที่สร้าง",
  "Settings.roles.edit.title": "แก้ไขบทบาท",
  "Settings.roles.form.button.users-with-role": "ผู้ใช้ที่มีบทบาทนี้",
  "Settings.roles.form.created": "ที่สร้างขึ้น",
  "Settings.roles.form.description": "ชื่อและคำอธิบายของบทบาท",
  "Settings.roles.form.permissions.attributesPermissions": "สิทธิ์ในฟิลด์",
  "Settings.roles.form.permissions.create": "สร้าง",
  "Settings.roles.form.permissions.delete": "ลบ",
  "Settings.roles.form.permissions.read": "อ่าน",
  "Settings.roles.form.permissions.update": "อัพเดต",
  "Settings.roles.list.button.add": "เพิ่มบทบาทใหม่",
  "Settings.roles.list.description": "รายการบทบาท",
  "Settings.roles.title.singular": "บทบาท",
  "Settings.webhooks.create": "สร้างเว็บฮุค",
  "Settings.webhooks.create.header": "สร้างส่วนหัวใหม่",
  "Settings.webhooks.created": "เว็บฮุคถูกสร้างขึ้น",
  "Settings.webhooks.events.create": "สร้าง",
  "Settings.webhooks.form.events": "เหตุการณ์",
  "Settings.webhooks.form.headers": "ส่วนหัว",
  "Settings.webhooks.form.url": "Url",
  "Settings.webhooks.key": "คีย์",
  "Settings.webhooks.list.button.add": "เพิ่มเว็บฮุคใหม่",
  "Settings.webhooks.list.description": "รับการแจ้งเตือนการเปลี่ยนแปลงผ่าน POST",
  "Settings.webhooks.list.empty.description": "เพิ่มรายการแรกของคุณลงในรายการนี้",
  "Settings.webhooks.list.empty.link": "ดูเอกสารของเรา",
  "Settings.webhooks.list.empty.title": "ยังไม่มีการเชื่อมเว็บฮุค",
  "Settings.webhooks.singular": "เว็บฮุค",
  "Settings.webhooks.title": "เว็บฮุค",
  "Settings.webhooks.trigger": "ทริกเกอร์",
  "Settings.webhooks.trigger.cancel": "ยกเลิกทริกเกอร์",
  "Settings.webhooks.trigger.pending": "ค้างอยู่ ...",
  "Settings.webhooks.trigger.save": "โปรดบันทึกเพื่อทริกเกอร์",
  "Settings.webhooks.trigger.success": "สำเร็จ!",
  "Settings.webhooks.trigger.success.label": "ทริกเกอร์สำเร็จ",
  "Settings.webhooks.trigger.test": "ทริกเกอร์ทดสอบ",
  "Settings.webhooks.trigger.title": "บันทึกก่อนทริกเกอร์",
  "Settings.webhooks.value": "ค่า",
  Username,
  Users,
  "Users & Permissions": "ผู้ใช้ & การอนุญาต",
  "Users.components.List.empty": "ไม่มีผู้ใช้...",
  "Users.components.List.empty.withFilters": "ไม่มีผู้ใช้ตามตัวกรองที่ใช้ ...",
  "Users.components.List.empty.withSearch": "ไม่มีผู้ใช้ที่สอดคล้องกับการค้นหา ({search})...",
  "app.components.BlockLink.code": "ตัวอย่างโค้ด",
  "app.components.Button.cancel": "ยกเลิก",
  "app.components.Button.reset": "รีเซ็ต",
  "app.components.ComingSoonPage.comingSoon": "เร็วๆนี้",
  "app.components.DownloadInfo.download": "กำลังดำเนินการดาวน์โหลด ...",
  "app.components.DownloadInfo.text": "อาจใช้เวลาหนึ่งสักครู่ ขอบคุณสำหรับความอดทนของคุณ",
  "app.components.EmptyAttributes.title": "ยังไม่มีฟิลด์",
  "app.components.HomePage.button.blog": "ดูข้อมูลเพิ่มเติมบนบล็อก",
  "app.components.HomePage.community": "ค้นหาชุมชนบนเว็บ",
  "app.components.HomePage.community.content": "ปรึกษากับสมาชิกของทีมผู้ให้การสนับสนุนและผู้พัฒนาบนอีกช่องทาง",
  "app.components.HomePage.create": "สร้างชนิดเนื้อหาแรกของคุณ",
  "app.components.HomePage.welcome": "ยินดีต้อนรับสู่บอร์ด!",
  "app.components.HomePage.welcome.again": "ยินดีต้อนรับ ",
  "app.components.HomePage.welcomeBlock.content": "เรายินดีที่จะมีคุณเป็นส่วนหนึ่งของชุมชน. คุณรู้ไหม? เรากำลังมองหาผลตอบรับอย่างต่อเนื่อง ดังนั้นให้คุณสบายใจที่จะส่งข้อความโดยตรงให้กับเรา ",
  "app.components.HomePage.welcomeBlock.content.again": "เราหวังว่าคุณจะดำเนินการตามโครงการของคุณ ... อ่านข่าวล่าสุดเกี่ยวกับ Strapi เรากำลังให้สิ่งที่ดีที่สุดแก่เราเพื่อปรับปรุงผลิตภัณฑ์โดยยึดตามความคิดเห็นของคุณ",
  "app.components.HomePage.welcomeBlock.content.issues": "ปัญหา.",
  "app.components.HomePage.welcomeBlock.content.raise": " หรือเพิ่มเติม ",
  "app.components.ImgPreview.hint": "ลาก & ปล่อยไฟล์ของคุณลงในพื้นที่นี้ หรือ{browse}เพื่อหาไฟล์ที่จะอัพโหลด",
  "app.components.ImgPreview.hint.browse": "เรียกดู",
  "app.components.InputFile.newFile": "เพิ่มไฟล์ใหม่",
  "app.components.InputFileDetails.open": "เปิดในแท็บใหม่",
  "app.components.InputFileDetails.originalName": "ชื่อเดิม:",
  "app.components.InputFileDetails.remove": "ลบไฟล์นี้",
  "app.components.InputFileDetails.size": "ขนาด:",
  "app.components.InstallPluginPage.Download.description": "อาจใช้เวลาสองสามวินาทีในการดาวน์โหลดและติดตั้งปลั๊กอิน",
  "app.components.InstallPluginPage.Download.title": "กำลังดาวน์โหลด...",
  "app.components.InstallPluginPage.description": "เพิ่มความสามารถแอปพลิเคชันของคุณได้อย่างง่ายดาย",
  "app.components.LeftMenuFooter.help": "วิธีใช้",
  "app.components.LeftMenuFooter.poweredBy": "สนับสนุนโดย ",
  "app.components.LeftMenuLinkContainer.collectionTypes": "ชนิดของคอลเล็กชัน",
  "app.components.LeftMenuLinkContainer.configuration": "ค่าติดตั้ง",
  "app.components.LeftMenuLinkContainer.general": "ทั่วไป",
  "app.components.LeftMenuLinkContainer.noPluginsInstalled": "ยังไม่ได้ติดตั้งปลั๊กอิน",
  "app.components.LeftMenuLinkContainer.plugins": "ปลั๊กอิน",
  "app.components.LeftMenuLinkContainer.singleTypes": "ชนิดเดียว",
  "app.components.ListPluginsPage.deletePlugin.description": "อาจใช้เวลาสองสามวินาทีเพื่อถอนการติดตั้งปลั๊กอิน",
  "app.components.ListPluginsPage.deletePlugin.title": "การถอนติดตั้ง",
  "app.components.ListPluginsPage.description": "รายการของปลั๊กอินที่ติดตั้งไว้ในโปรเจ็กต์",
  "app.components.ListPluginsPage.head.title": "รายการปลั๊กอิน",
  "app.components.Logout.logout": "ล็อกเอาต์",
  "app.components.Logout.profile": "โปรไฟล์",
  "app.components.NotFoundPage.back": "กลับไปยังโฮมเพจ",
  "app.components.NotFoundPage.description": "ไม่พบ",
  "app.components.Official": "อย่างเป็นทางการ",
  "app.components.Onboarding.label.completed": "% สมบูรณ์",
  "app.components.Onboarding.title": "วิดีโอการเริ่มต้นใช้งาน",
  "app.components.PluginCard.Button.label.download": "ดาวน์โหลด",
  "app.components.PluginCard.Button.label.install": "ติดตั้งแล้ว",
  "app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed": "คุณลักษณะ autoReload ต้องถูกเปิดใช้งาน โปรดเริ่มต้นแอปของคุณโดยใช้ `yarn develop`",
  "app.components.PluginCard.PopUpWarning.install.impossible.confirm": "ฉันเข้าใจ!",
  "app.components.PluginCard.PopUpWarning.install.impossible.environment": "ด้วยเหตุผลด้านความปลอดภัยปลั๊กอินสามารถดาวน์โหลดได้ในสภาวะแวดล้อมการพัฒนาเท่านั้น",
  "app.components.PluginCard.PopUpWarning.install.impossible.title": "การดาวน์โหลดเป็นไปไม่ได้",
  "app.components.PluginCard.compatible": "เข้ากันได้กับแอปของคุณ",
  "app.components.PluginCard.compatibleCommunity": "ทำงานร่วมกันได้กับเวอร์ชั่นชุมชน",
  "app.components.PluginCard.more-details": "รายละเอียดเพิ่มเติม",
  "app.components.Users.MagicLink.connect": "ส่งลิงก์นี้ไปยังผู้ใช้เพื่อให้ผู้ใช้สามารถเชื่อมต่อ",
  "app.components.Users.ModalCreateBody.block-title.details": "รายละเอียด",
  "app.components.Users.ModalCreateBody.block-title.roles": "บทบาทของผู้ใช้",
  "app.components.Users.SortPicker.button-label": "เรียงตาม",
  "app.components.Users.SortPicker.sortby.email_asc": "อีเมล (A ถึง Z)",
  "app.components.Users.SortPicker.sortby.email_desc": "อีเมล (Z ถึง A)",
  "app.components.Users.SortPicker.sortby.firstname_asc": "ชื่อ (A ถึง Z)",
  "app.components.Users.SortPicker.sortby.firstname_desc": "ชื่อ (Z ถึง A)",
  "app.components.Users.SortPicker.sortby.lastname_asc": "นามสกุล (A ถึง Z)",
  "app.components.Users.SortPicker.sortby.lastname_desc": "นามสกุล (Z ถึง A)",
  "app.components.Users.SortPicker.sortby.username_asc": "ชื่อผู้ใช้ (A ถึง Z)",
  "app.components.Users.SortPicker.sortby.username_desc": "ชื่อผู้ใช้ (Z ถึง A)",
  "app.components.listPlugins.button": "เพิ่มปลั๊กอินใหม่",
  "app.components.listPlugins.title.none": "ไม่มีการติดตั้งปลั๊กอิน",
  "app.components.listPluginsPage.deletePlugin.error": "มีข้อผิดพลาดเกิดขึ้นขณะถอนการติดตั้งปลั๊กอิน",
  "app.containers.App.notification.error.init": "เกิดข้อผิดพลาดขึ้นขณะร้องขอ API",
  "app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin": "ถ้าคุณไม่ได้รับลิงก์นี้โปรดติดต่อผู้ดูแลระบบของคุณ",
  "app.containers.AuthPage.ForgotPasswordSuccess.text.email": "อาจใช้เวลาสองสามนาทีเพื่อรับลิงก์การกู้คืนรหัสผ่านของคุณ",
  "app.containers.AuthPage.ForgotPasswordSuccess.title": "ส่งอีเมลแล้ว",
  "app.containers.Users.EditPage.form.active.label": "เปิดใช้งาน",
  "app.containers.Users.EditPage.header.label": "แก้ไข {name}",
  "app.containers.Users.EditPage.header.label-loading": "แก้ไขผู้ใช้",
  "app.containers.Users.EditPage.roles-bloc-title": "บทบาทที่แอ็ตทริบิวต์",
  "app.containers.Users.ModalForm.footer.button-success": "สร้างผู้ใช้",
  "app.links.configure-view": "กำหนดคอนฟิกมุมมอง",
  "app.static.links.cheatsheet": "ชีทชีท",
  "app.utils.SelectOption.defaultMessage": " ",
  "app.utils.add-filter": "เพิ่มตัวกรอง",
  "app.utils.defaultMessage": " ",
  "app.utils.errors.file-too-big.message": "ไฟล์มีขนาดใหญ่เกินไป",
  "app.utils.filters": "ตัวกรอง",
  "app.utils.placeholder.defaultMessage": " ",
  "app.utils.select-all": "เลือกทั้งหมด",
  "component.Input.error.validation.integer": "ค่านี้ต้องเป็นจำนวนเต็ม",
  "components.AutoReloadBlocker.description": "รัน Strapi ด้วยหนึ่งในคำสั่งต่อไปนี้:",
  "components.AutoReloadBlocker.header": "รีโหลดคุณลักษณะที่จำเป็นสำหรับปลั๊กอินนี้",
  "components.ErrorBoundary.title": "มีบางอย่างผิดพลาด...",
  "components.Input.error.attribute.key.taken": "ค่านี้มีอยู่แล้ว",
  "components.Input.error.attribute.sameKeyAndName": "ไม่เท่ากับ",
  "components.Input.error.attribute.taken": "ชื่อฟิลด์นี้มีอยู่แล้ว",
  "components.Input.error.contain.lowercase": "รหัสผ่านต้องมีอักขระตัวพิมพ์เล็กอย่างน้อยหนึ่งตัว",
  "components.Input.error.contain.number": "รหัสผ่านต้องมีอย่างน้อยหนึ่งตัวเลข",
  "components.Input.error.contain.uppercase": "รหัสผ่านต้องมีอักขระตัวพิมพ์ใหญ่อย่างน้อยหนึ่งตัว",
  "components.Input.error.contentTypeName.taken": "ชื่อนี้มีอยู่แล้ว",
  "components.Input.error.custom-error": "{errorMessage} ",
  "components.Input.error.password.noMatch": "รหัสผ่านไม่ตรงกัน",
  "components.Input.error.validation.email": "นี่มันไม่ใช่อีเมล",
  "components.Input.error.validation.json": "ค่านี้ไม่ตรงกับรูปแบบ JSON",
  "components.Input.error.validation.max": "ค่านั้นสูงเกินไป {max}.",
  "components.Input.error.validation.maxLength": "ค่ามีขนาดยาวเกินไป {max}",
  "components.Input.error.validation.min": "ค่านี้ต่ำเกินไป {min}.",
  "components.Input.error.validation.minLength": "ค่านี้สั้นเกินไป {min}.",
  "components.Input.error.validation.minSupMax": "ไม่สามารถมากกว่า",
  "components.Input.error.validation.regex": "ค่าไม่ตรงกับ regex",
  "components.Input.error.validation.required": "ค่านี้เป็นค่าที่จำเป็น",
  "components.Input.error.validation.unique": "ค่านี้ถูกใช้อยู่แล้ว",
  "components.InputSelect.option.placeholder": "เลือกที่นี่",
  "components.ListRow.empty": "ไม่มีข้อมูลที่ต้องถูกแสดง.",
  "components.OverlayBlocker.description": "คุณกำลังใช้คุณลักษณะที่ต้องมีการรีสตาร์ทเซิร์ฟเวอร์ โปรดรอจนกว่าเซิร์ฟเวอร์จะเริ่มทำงาน",
  "components.OverlayBlocker.description.serverError": "เซิร์ฟเวอร์ควรถูกรีสตาร์ท โปรดตรวจสอบบันทึกการทำงานของคุณในเทอร์มินัล",
  "components.OverlayBlocker.title": "กำลังรอการรีสตาร์ท...",
  "components.OverlayBlocker.title.serverError": "การรีสตาร์ทใช้เวลานานกว่าที่คาดไว้",
  "components.PageFooter.select": "รายการต่อหน้า",
  "components.ProductionBlocker.description": "เพื่อความปลอดภัยเราต้องปิดใช้งานปลั๊กอินนี้ในสภาวะแวดล้อมอื่น",
  "components.ProductionBlocker.header": "ปลั๊กอินนี้พร้อมใช้งานในการพัฒนาเท่านั้น",
  "components.Search.placeholder": "ค้นหา...",
  "components.Wysiwyg.collapse": "ยุบ",
  "components.Wysiwyg.selectOptions.H1": "หัวเรื่อง H1",
  "components.Wysiwyg.selectOptions.H2": "หัวเรื่อง H2",
  "components.Wysiwyg.selectOptions.H3": "หัวเรื่อง H3",
  "components.Wysiwyg.selectOptions.H4": "หัวเรื่อง H4",
  "components.Wysiwyg.selectOptions.H5": "หัวเรื่อง H5",
  "components.Wysiwyg.selectOptions.H6": "หัวเรื่อง H6",
  "components.Wysiwyg.selectOptions.title": "เพิ่มหัวเรื่อง",
  "components.WysiwygBottomControls.charactersIndicators": "อักขระ",
  "components.WysiwygBottomControls.fullscreen": "ขยาย",
  "components.WysiwygBottomControls.uploadFiles": "ลาก & ปล่อยไฟล์วางจากคลิปบอร์ดหรือ {browse}",
  "components.WysiwygBottomControls.uploadFiles.browse": "เลือกรายการ",
  "components.popUpWarning.button.cancel": "ยกเลิก",
  "components.popUpWarning.button.confirm": "ยืนยัน",
  "components.popUpWarning.message": "คุณแน่ใจว่าต้องการลบสิ่งนี้หรือไม่?",
  "components.popUpWarning.title": "โปรดยืนยัน",
  "form.button.done": "เสร็จแล้ว",
  "global.prompt.unsaved": "คุณแน่ใจว่าต้องการออกจากหน้านี้หรือไม่? การแก้ไขของคุณทั้งหมดจะหายไป",
  "notification.contentType.relations.conflict": "ชนิดเนื้อหามีความขัดแย้งกัน",
  "notification.error": "เกิดข้อผิดพลาด",
  "notification.error.layout": "ไม่สามารถดึงข้อมูลโครงร่าง",
  "notification.form.error.fields": "ฟอร์มมีข้อผิดพลาดบางอย่าง",
  "notification.form.success.fields": "การเปลี่ยนแปลงถูกบันทึก",
  "notification.link-copied": "คัดลอกลิงก์ไปยังคลิปบอร์ด",
  "notification.success.delete": "ไอเท็มถูกลบแล้ว",
  "notification.success.saved": "ถูกบันทึก",
  "request.error.model.unknown": "โมเดลนี้ไม่มีอยู่"
};
export {
  Analytics,
  Documentation,
  Email,
  Password,
  Provider,
  ResetPasswordToken,
  Role,
  Username,
  Users,
  th as default
};
//# sourceMappingURL=th.json-FY5UDF3C.js.map
