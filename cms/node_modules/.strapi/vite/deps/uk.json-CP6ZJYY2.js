import "./chunk-PLDDJCW6.js";

// node_modules/@strapi/content-type-builder/dist/admin/translations/uk.json.mjs
var configurations = "налаштування";
var from = "з";
var uk = {
  "attribute.boolean": "Boolean",
  "attribute.boolean.description": "Так чи ні, 1 чи 0, правда чи брехня",
  "attribute.component": "Компонент",
  "attribute.component.description": "Група полей, які ви можете повторювати",
  "attribute.customField": "Спеціальне поле",
  "attribute.date": "Date",
  "attribute.date.description": "Елемент вибору дати та часу",
  "attribute.datetime": "Дата та час",
  "attribute.dynamiczone": "Динамічна зона",
  "attribute.dynamiczone.description": "Динамічний вибір компонентів підчас редагування контенту",
  "attribute.email": "Email",
  "attribute.email.description": "Електронна пошта з перевіркою формату",
  "attribute.enumeration": "Enumeration",
  "attribute.enumeration.description": "Перелік значень, вибирається одне",
  "attribute.json": "JSON",
  "attribute.json.description": "Інформація у форматі JSON",
  "attribute.media": "Media",
  "attribute.media.description": "Файли, як-то картинки, відео тощо",
  "attribute.null": " ",
  "attribute.number": "Number",
  "attribute.number.description": "Числа (integer, float, decimal)",
  "attribute.password": "Password",
  "attribute.password.description": "Поле паролю з шифруванням",
  "attribute.relation": "Relation",
  "attribute.relation.description": "Зв'язок з Типом Колекції",
  "attribute.richtext": "Rich text",
  "attribute.richtext.description": "Текст з можливістю форматування",
  "attribute.blocks": "Багатий текст (блоки)",
  "attribute.blocks.description": "Новий редактор тексту на основі JSON",
  "attribute.text": "Text",
  "attribute.text.description": "Короткий або довгий текст, як заголовок чи опис",
  "attribute.time": "Time",
  "attribute.timestamp": "Мітка часу",
  "attribute.uid": "UID",
  "attribute.uid.description": "Унікальний ідентифікатор",
  "button.attributes.add.another": "Додате ще одне поле",
  "button.component.add": "Додати компонент",
  "button.component.create": "Створити новий компонент",
  "button.model.create": "Створити Тип Колекції",
  "button.single-types.create": "Створити Тип Одиниць",
  "component.repeatable": "(повторюваний)",
  "components.SelectComponents.displayed-value": "Вибрано {number, plural, =0 {# компонентів} one {# компонент} many {# конпонентів} other {# конпоненти}}",
  "components.componentSelect.no-component-available": "Ви вже добавили всі компоненти",
  "components.componentSelect.no-component-available.with-search": "Немає компонентів, які відповідають вашому запиту",
  "components.componentSelect.value-component": "{number} вибраних компонентів (напишіть для пошуку)",
  "components.componentSelect.value-components": "{number} вибраних компонентів",
  configurations,
  "contentType.apiId-plural.description": "Множинний API ID",
  "contentType.apiId-plural.label": "API ID (Множина)",
  "contentType.apiId-singular.description": "UID використовується для генерації маршрутів API та таблиць/колекцій баз даних",
  "contentType.apiId-singular.label": "API ID (Однина)",
  "contentType.collectionName.description": "Корисно, коли назва вашого Типу Вмісту та вашої таблиці різні",
  "contentType.collectionName.label": "Назва колекції",
  "contentType.displayName.label": "Назва для відображення",
  "contentType.kind.change.warning": "Ви тільки що змінили Тип Вмісту: API буде перезавантажене (маршрути, контролери та сервіси будуть переписані).",
  "error.attributeName.reserved-name": "Ця назва не може буди використана для вашого Типу Вмісту, так як вона може зламати іншу функціональність",
  "error.contentType.pluralName-used": "Це значення не може співпадати зі словом у однині",
  "error.contentType.singularName-used": "Це значення не може співпадати зі словом у множині",
  "error.contentType.singularName-equals-pluralName": "Це значення не може співпадати з Множинним API ID іншого типу вмісту.",
  "error.contentType.pluralName-equals-singularName": "Це значення не може співпадати з Singular API ID іншого типу вмісту.",
  "error.contentType.pluralName-equals-collectionName": "Це значення вже використовується іншим типом вмісту.",
  "error.contentTypeName.reserved-name": "Ця назва не може буди використана у вашому проекті, так як вона може зламати іншу функціональність",
  "error.validation.enum-duplicate": "Значення не можуть повторюватись",
  "error.validation.enum-empty-string": "Порожні рядки не допускаються",
  "error.validation.enum-regex": "Принаймні одне значення недійсне. Значення повинні мати принаймні один алфавітний символ перед першою появою числа.",
  "error.validation.minSupMax": "Не може бути більше",
  "error.validation.positive": "Числом повинно бути додатнім",
  "error.validation.regex": "Неправильний регулярний вираз",
  "error.validation.relation.targetAttribute-taken": "Ця назва вже існує в цільовій моделі",
  "form.attribute.component.option.add": "Додати компонент",
  "form.attribute.component.option.create": "Додати новий компонент",
  "form.attribute.component.option.create.description": "Компонент використовується в типах та інших компонентах, він буде доступний всюди.",
  "form.attribute.component.option.repeatable": "Повторюваний компонент",
  "form.attribute.component.option.repeatable.description": "Підходить для множинних об'єктів (масиву), наприклад, інгридієнтів, метатегів тощо...",
  "form.attribute.component.option.reuse-existing": "Використати існуючий компонент",
  "form.attribute.component.option.reuse-existing.description": "Використовуйте створений вами компонент, щоб підтримувати узгодженність данних серед різних Типів Вмісту.",
  "form.attribute.component.option.single": "Одиничний компонент",
  "form.attribute.component.option.single.description": "Підходить для групування полей, наприклад, повна адреса, основна інформація тощо...",
  "form.attribute.item.customColumnName": "Власні назви стовпців",
  "form.attribute.item.customColumnName.description": "Корисно для перейменування назв стовпців у базі даних для підтримки більш зрозумілого формату відповідей API",
  "form.attribute.item.date.type.date": "date (нп: 01/01/{currentYear})",
  "form.attribute.item.date.type.datetime": "datetime (нп: 01/01/{currentYear} 00:00)",
  "form.attribute.item.date.type.time": "time (нп: 00:00)",
  "form.attribute.item.defineRelation.fieldName": "Назва поля",
  "form.attribute.item.enumeration.graphql": "Назва поля для GraphQL",
  "form.attribute.item.enumeration.graphql.description": "Дозволяє перейменувати згенеровану для GraphQL назву поля",
  "form.attribute.item.enumeration.placeholder": "Наприклад:\nранок\nдень\nвечір",
  "form.attribute.item.enumeration.rules": "Значення (одне на рядок)",
  "form.attribute.item.maximum": "Максимальне значення",
  "form.attribute.item.maximumLength": "Максимальна довжина",
  "form.attribute.item.minimum": "Мінімальне значення",
  "form.attribute.item.minimumLength": "Мінімальна довжина",
  "form.attribute.item.number.type": "Формат числа",
  "form.attribute.item.number.type.biginteger": "big integer (нп: 123456789)",
  "form.attribute.item.number.type.decimal": "decimal (нп: 2.22)",
  "form.attribute.item.number.type.float": "float (нп: 3.33333333)",
  "form.attribute.item.number.type.integer": "integer (нп: 10)",
  "form.attribute.item.privateField": "Приватне поле",
  "form.attribute.item.privateField.description": "Це поле не буде відображатися у відповіді API",
  "form.attribute.item.requiredField": "Обов'язкове поле",
  "form.attribute.item.requiredField.description": "Ви не зможете створити запис якщо не заповните це поле",
  "form.attribute.item.text.regex": "Регулярний вираз (RegExp)",
  "form.attribute.item.text.regex.description": "Шаблон регулярного виразу.",
  "form.attribute.item.uniqueField": "Унікальне поле",
  "form.attribute.item.uniqueField.description": "Ви не зможете створити запис, якщо вже існує запис із таким самим значенням поля",
  "form.attribute.item.uniqueField.v5.willBeDisabled'": "Унікальні поля наразі не працюють належним чином у компонентах. Якщо ви вимкнете цю функцію, поле буде вимкнено до виправлення.",
  "form.attribute.item.uniqueField.v5.disabled": "Унікальні поля наразі не працюють належним чином у компонентах. Це поле було вимкнено до виправлення.",
  "form.attribute.media.allowed-types": "Виберіть дозволені типи медіа",
  "form.attribute.media.allowed-types.option-files": "Файли",
  "form.attribute.media.allowed-types.option-images": "Картинки",
  "form.attribute.media.allowed-types.option-videos": "Відео",
  "form.attribute.media.option.multiple": "Множинні медіа",
  "form.attribute.media.option.multiple.description": "Підходить для слайдерів, каруселей або завантаження кількох файлів",
  "form.attribute.media.option.single": "Одиничне медіа",
  "form.attribute.media.option.single.description": "Підходить для аватарок, картинок профіля або обкладинок",
  "form.attribute.settings.default": "Значення за замовчуванням",
  "form.attribute.text.option.long-text": "Довгий текст",
  "form.attribute.text.option.long-text.description": "Підходить для описів, тексту про себе. Точний пошук вимкнено.",
  "form.attribute.text.option.short-text": "Короткий текст",
  "form.attribute.text.option.short-text.description": "Підходить для назв, імен, посиалань (URL). Дозволяє точний пошук по цьому полю.",
  "form.button.add-components-to-dynamiczone": "Додати компоненти у зону.",
  "form.button.add-field": "Додати ще одне поле",
  "form.button.add-first-field-to-created-component": "Додати перше поле компоненту",
  "form.button.add.field.to.collectionType": "Додати ще одне поле до цього Типу Колекції",
  "form.button.add.field.to.component": "Додати ще одне поле до цього компоненту",
  "form.button.add.field.to.contentType": "Додати ще одне поле до цього Типу Вмісту",
  "form.button.add.field.to.singleType": "Додати ще одне поле до цього Типу Одиниць",
  "form.button.cancel": "Скасувати",
  "form.button.collection-type.description": "Підходить для множинних об'єктів, як-то дописи, товари, коментарі тощо.",
  "form.button.collection-type.name": "Collection Type",
  "form.button.configure-component": "Налаштувати компонент",
  "form.button.configure-view": "Налаштувати вигляд",
  "form.button.select-component": "Вибрати компонент",
  "form.button.single-type.description": "Підходить для поодиноких об'єктів, як-то домашня сторінка, про нас тощо",
  "form.button.single-type.name": "Тип Одиниці",
  from,
  "listView.headerLayout.description": "Створіть архітектуру даних вашого вмісту",
  "menu.section.components.name": "Компоненти",
  "menu.section.models.name": "Типи колекцій",
  "menu.section.single-types.name": "Типи одиниць",
  "modalForm.attribute.form.base.name.description": "Для назви атрибута не допускається пробілів",
  "modalForm.attribute.form.base.name.placeholder": "наприклад, slug, seoUrl, canonicalUrl",
  "modalForm.attribute.target-field": "Пов'язане поле",
  "modalForm.attributes.select-component": "Виберіть компонент",
  "modalForm.attributes.select-components": "Виберіть компоненти",
  "modalForm.collectionType.header-create": "Створіть тип колекції",
  "modalForm.component.header-create": "Створити компонент",
  "modalForm.components.create-component.category.label": "Виберіть категорію або введіть назву для створення нової",
  "modalForm.components.icon.label": "Іконка",
  "modalForm.empty.button": "Додати користувацькі поля",
  "modalForm.empty.heading": "Тут ще нічого немає.",
  "modalForm.empty.sub-heading": "Знайдіть те, що шукаєте, за допомогою широкого спектру розширень.",
  "modalForm.editCategory.base.name.description": "Для назви категорії не допускається пробілів",
  "modalForm.header-edit": "Редагувати {name}",
  "modalForm.header.categories": "Категорії",
  "modalForm.header.back": "Назад",
  "modalForm.singleType.header-create": "Створити Тип Одиниць",
  "modalForm.sub-header.addComponentToDynamicZone": "Додати новий компонент до динамічної зони",
  "modalForm.sub-header.attribute.create": "Додати нове поле {type}",
  "modalForm.sub-header.attribute.create.step": "Додати новий компонент ({step}/2)",
  "modalForm.sub-header.attribute.edit": "Редагувати {name}",
  "modalForm.sub-header.chooseAttribute.collectionType": "Виберіть поле для вашого Типу Колекції",
  "modalForm.sub-header.chooseAttribute.component": "Виберіть поле для вашого компоненту",
  "modalForm.sub-header.chooseAttribute.singleType": "Виберіть поле для вашого Тип Одиниць",
  "modalForm.custom-fields.advanced.settings.extended": "Extended settings",
  "modalForm.tabs.custom": "Custom",
  "modalForm.tabs.custom.howToLink": "How to add custom fields",
  "modalForm.tabs.default": "Default",
  "modalForm.tabs.label": "Default and Custom types tabs",
  "modelPage.attribute.relation-polymorphic": "Зв'язок (поліморфний)",
  "modelPage.attribute.relationWith": "Зв'язок з",
  "notification.error.dynamiczone-min.validation": "Принаймні один компонент потрібен у динамічній зоні, щоб мати можливість зберегти тип вмісту",
  "notification.info.autoreaload-disable": "Функція autoReload має буте включена. Будь ласка, запустіть свій додаток вікористовуючи `strapi develop`.",
  "notification.info.creating.notSaved": "Будь ласка, збережіть ваші зміни перед тим як створювати новий компонент або Тип Колекції",
  "plugin.description.long": "Моделюйте структуру данних для вашого API. Створюйте нові поля та зв'язки за хвилину. Файли будуть автоматично створені та оновлені в вашему проекту.",
  "plugin.description.short": "Моделюйте структуру данних для вашого API.",
  "plugin.name": "Будівельник типу вмісту",
  "popUpForm.navContainer.advanced": "Розширені налаштування",
  "popUpForm.navContainer.base": "Основне",
  "popUpWarning.bodyMessage.cancel-modifications": "Ви впевнені, що хочете скасувати свої зміни?",
  "popUpWarning.bodyMessage.cancel-modifications.with-components": "Ви впевнені, що хочете скасувати свої зміни? Деякі компоненти були змінені, або створені нові...",
  "popUpWarning.bodyMessage.category.delete": "Ви впевнені, що хочете видалити цю категорію? Всі компоненти також будуть видалені.",
  "popUpWarning.bodyMessage.component.delete": "Ви впевнені, що хочете видалити цей компонент?",
  "popUpWarning.bodyMessage.contentType.delete": "Ви впевнені, що хочете видалити цей типу Колекції?",
  "popUpWarning.draft-publish.button.confirm": "Так, вимкнути",
  "popUpWarning.draft-publish.message": "Якщо ви вимкнете 'Чернетка та публікація', ваші чернетки будуть видалені.",
  "popUpWarning.draft-publish.second-message": "Ви впевнені, що хочете вимкнути це?",
  "prompt.unsaved": "Ви впевнені що хочете залишити сторінку? Всі виші зміни будуть втарчені.",
  "relation.attributeName.placeholder": "Нп: автор, категорія, тег",
  "relation.manyToMany": "містить і належить багатьом",
  "relation.manyToOne": "містить багато",
  "relation.manyWay": "містить багато",
  "relation.oneToMany": "належить до багатьох",
  "relation.oneToOne": "містить і належить до однієї",
  "relation.oneWay": "містить одне",
  "table.button.no-fields": "Add new field",
  "table.content.create-first-content-type": "Створіть свій перший Тип колекції",
  "table.content.no-fields.collection-type": "Додайте своє перше поле до цього Типу колекції",
  "table.content.no-fields.component": "Додайте своє перше поле до цього компонента",
  "IconPicker.search.placeholder.label": "Пошук іконки",
  "IconPicker.search.clear.label": "Очистити пошук іконок",
  "IconPicker.search.button.label": "Кнопка пошуку іконки",
  "IconPicker.remove.tooltip": "Вилучити обрану іконку",
  "IconPicker.remove.button": "Вилучити кнопку обраної іконки",
  "IconPicker.emptyState.label": "Іконка не знайдена",
  "IconPicker.icon.label": "Виберіть іконку {icon}"
};
export {
  configurations,
  uk as default,
  from
};
//# sourceMappingURL=uk.json-CP6ZJYY2.js.map
