import "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/admin/src/translations/uk.json.mjs
var Analytics = "Аналітика";
var Documentation = "Документація";
var Email = "Email";
var Password = "Пароль";
var Provider = "Провайдер";
var ResetPasswordToken = "Скинути токен паролю";
var Role = "Роль";
var Username = "Ім'я користувача";
var Users = "Користувачі";
var anErrorOccurred = "Ой! Щось пішло не так. Будь ласка, спробуйте ще раз.";
var clearLabel = "Очистити";
var dark = "Темний";
var light = "Світлий";
var or = "АБО";
var selectButtonTitle = "Вибрати";
var skipToContent = "Перейти до вмісту";
var submit = "Відправити";
var uk = {
  Analytics,
  "Auth.components.Oops.text": "Ваш обліковий запис призупинено.",
  "Auth.components.Oops.text.admin": "Якщо це помилка, будь ласка, зв'яжіться з адміністратором.",
  "Auth.components.Oops.title": "Ой...",
  "Auth.form.active.label": "Активний",
  "Auth.form.button.forgot-password": "Відправити Email",
  "Auth.form.button.go-home": "НАЗАД НА ГОЛОВНУ",
  "Auth.form.button.login": "Увійти",
  "Auth.form.button.login.providers.error": "Ми не можемо підключити вас через вибраного постачальника.",
  "Auth.form.button.login.strapi": "Увійти через stapi",
  "Auth.form.button.password-recovery": "Відновлення пароля",
  "Auth.form.button.register": "Готовий почати",
  "Auth.form.confirmPassword.label": "Підтвердити пароль",
  "Auth.form.currentPassword.label": "Поточний пароль",
  "Auth.form.email.label": "Email",
  "Auth.form.email.placeholder": "н.п. <EMAIL>",
  "Auth.form.error.blocked": "Ваш обліковий запис був заблокований адміністратором.",
  "Auth.form.error.code.provide": "Надано неправильний код.",
  "Auth.form.error.confirmed": "Ваш email не підтверджений.",
  "Auth.form.error.email.invalid": "Це не схоже на email.",
  "Auth.form.error.email.provide": "Вкажіть своє ім'я користувача або email.",
  "Auth.form.error.email.taken": "Цей email вже використовується.",
  "Auth.form.error.invalid": "Неправильний логін або пароль.",
  "Auth.form.error.params.provide": "Надано неправильні параметри.",
  "Auth.form.error.password.format": "У паролі не може бути більше ніж три символи `$`",
  "Auth.form.error.password.local": "Цей користувач ніколи не встановлював пароль, будь ласка увійдіть через постачальника, який ви вікористовували під час реєстрації.",
  "Auth.form.error.password.matching": "Паролі не співпадають.",
  "Auth.form.error.password.provide": "Вкажіть свій пароль, будь ласка.",
  "Auth.form.error.ratelimit": "Забагато спроб, будь ласка, спробуйте знову через хвилинку.",
  "Auth.form.error.user.not-exist": "Цього email не існує.",
  "Auth.form.error.username.taken": "Це ім'я користувача вже використовується.",
  "Auth.form.firstname.label": "Імʼя",
  "Auth.form.firstname.placeholder": "н.п. Тарас",
  "Auth.form.forgot-password.email.label": "Вкажіть свій email",
  "Auth.form.forgot-password.email.label.success": "Ми надіслали вам листа до",
  "Auth.form.lastname.label": "Прізвище",
  "Auth.form.lastname.placeholder": "e.g. Шевченко",
  "Auth.form.password.hide-password": "Сховати пароль",
  "Auth.form.password.hint": "Повинно бути щонайменше 8 символів, 1 велика літера, 1 мала літера та 1 число",
  "Auth.form.password.show-password": "Показати пароль",
  "Auth.form.register.news.label": "Тримайте мене в курсі нових можливостей та майбутніх вдосконалень (тим самим ви приймаєте {terms} та {policy})",
  "Auth.form.register.subtitle": "Дані для входу використовуються лише для аутентифікації в Strapi. Усі збережені дані будуть зберігатися у вашій базі даних.",
  "Auth.form.rememberMe.label": "Нагадати мені",
  "Auth.form.username.label": "Імʼя користувача",
  "Auth.form.username.placeholder": "н.п. Taras_Shevchenko",
  "Auth.form.welcome.subtitle": "Увійдіть у свій обліковий запис Strapi",
  "Auth.form.welcome.title": "Ласкаво просимо до Strapi!",
  "Auth.link.forgot-password": "Забули пароль?",
  "Auth.link.ready": "Готові увійти?",
  "Auth.link.signin": "Увійти",
  "Auth.link.signin.account": "Уже є обліковий запис?",
  "Auth.login.sso.divider": "Або увійти за допомогою",
  "Auth.login.sso.loading": "Завантаження постачальників...",
  "Auth.login.sso.subtitle": "Увійдіть у свій обліковий запис через SSO",
  "Auth.privacy-policy-agreement.policy": "політику конфіденційності",
  "Auth.privacy-policy-agreement.terms": "умови",
  "Auth.reset-password.title": "Скиньте пароль",
  "Content Manager": "Рекдактор контенту",
  "Content Type Builder": "Конструктор типів вмісту",
  Documentation,
  Email,
  "Files Upload": "Завантаження файлів",
  "HomePage.head.title": "Головна",
  "HomePage.roadmap": "Наша дорожня карта",
  "HomePage.welcome.congrats": "Вітаємо!",
  "HomePage.welcome.congrats.content": "Ви увійшли як перший адміністратор. Щоб відкрити для себе потужні можливості Strapi,",
  "HomePage.welcome.congrats.content.bold": "ми рекомендуємо створити свій перший Collection-Type.",
  "Media Library": "Медіатека",
  "New entry": "Новий запис",
  Password,
  Provider,
  ResetPasswordToken,
  Role,
  "Roles & Permissions": "Ролі й доступи",
  "Roles.ListPage.notification.delete-all-not-allowed": "Деякі ролі не можна було видалити, оскільки вони пов'язані з користувачем",
  "Roles.ListPage.notification.delete-not-allowed": "Роль не може бути видалена, якщо пов’язана з користувачами",
  "Roles.RoleRow.select-all": "Виберіть {name} для масових дій",
  "Roles.RoleRow.user-count": "{number, plural, =0 {# користувачів} one {# користувач} few{# користувачів} many {# користувачів} other {# користувачі}}",
  "Roles.components.List.empty.withSearch": "Немає ролі, що відповідає пошуку ({search})...",
  "Settings.PageTitle": "Налаштування — {name}",
  "Settings.apiTokens.ListView.headers.createdAt": "Створено",
  "Settings.apiTokens.ListView.headers.description": "Опис",
  "Settings.apiTokens.ListView.headers.lastUsedAt": "Востаннє використовувалося",
  "Settings.apiTokens.ListView.headers.name": "Назва",
  "Settings.apiTokens.ListView.headers.type": "Тип токена",
  "Settings.apiTokens.addFirstToken": "Додайте свій перший токен API",
  "Settings.apiTokens.addNewToken": "Додайте новий токен API",
  "Settings.apiTokens.create": "Створити новий токен API",
  "Settings.apiTokens.createPage.BoundRoute.title": "Пов'язати шлях з",
  "Settings.apiTokens.createPage.permissions.description": "Нижче наведено лише дії, пов'язані з маршрутом.",
  "Settings.apiTokens.createPage.permissions.header.hint": "Виберіть дії програми або дії плаґіна та натисніть на піктограму гвинтика, щоб відобразити пов'язаний маршрут",
  "Settings.apiTokens.createPage.permissions.header.title": "Розширені налаштування",
  "Settings.apiTokens.createPage.permissions.title": "Дозволи",
  "Settings.apiTokens.createPage.title": "Створити токен API",
  "Settings.apiTokens.description": "Список генерованих токенів для використання API",
  "Settings.apiTokens.emptyStateLayout": "У вас ще немає вмісту...",
  "Settings.apiTokens.regenerate": "Регенерувати",
  "Settings.apiTokens.title": "токени API",
  "Settings.apiTokens.lastHour": "минула година",
  "Settings.application.customization": "Налаштування",
  "Settings.application.customization.auth-logo.carousel-hint": "Замініть логотип на сторінках аутентифікації",
  "Settings.application.customization.carousel-hint": "Змінити логотип панелі адміністратора (Максимальний розмір: {dimension}x{dimension}, Максимальний розмір файлу: {size}KB)",
  "Settings.application.customization.carousel-slide.label": "Слайд логотипу",
  "Settings.application.customization.carousel.auth-logo.title": "Логотип аутентифікації",
  "Settings.application.customization.carousel.change-action": "Змінити логотип",
  "Settings.application.customization.carousel.menu-logo.title": "Логотип меню",
  "Settings.application.customization.carousel.reset-action": "Скидання логотипу",
  "Settings.application.customization.carousel.title": "Логотип",
  "Settings.application.customization.menu-logo.carousel-hint": "Замініть логотип у меню",
  "Settings.application.customization.modal.cancel": "Скасувати",
  "Settings.application.customization.modal.pending": "Логотип готовий до завантаження",
  "Settings.application.customization.modal.pending.card-badge": "зображення",
  "Settings.application.customization.modal.pending.choose-another": "Вибрати інший логотип",
  "Settings.application.customization.modal.pending.subtitle": "Керуйте обраним логотипом перед його завантаженням",
  "Settings.application.customization.modal.pending.title": "Логотип, готовий до завантаження",
  "Settings.application.customization.modal.pending.upload": "Завантажити логотип",
  "Settings.application.customization.modal.tab.label": "Як ви хочете завантажити свої файли?",
  "Settings.application.customization.modal.upload": "Завантажити логотип",
  "Settings.application.customization.modal.upload.cta.browse": "Переглянути файли",
  "Settings.application.customization.modal.upload.drag-drop": "Перетягніть файл сюди або",
  "Settings.application.customization.modal.upload.error-format": "Неправильний завантажений формат файлу (підтримуються лише формати: jpeg, jpg, png, svg).",
  "Settings.application.customization.modal.upload.error-network": "Помилка мережі.",
  "Settings.application.customization.modal.upload.error-size": "Завантажений файл занадто великий (максимальний розмір: {dimension}x{dimension}, максимальний розмір файлу: {size}KB)",
  "Settings.application.customization.modal.upload.file-validation": "Максимальний розмір: {dimension}x{dimension}, Максимальний розмір файлу: {size}KB",
  "Settings.application.customization.modal.upload.from-computer": "З комп’ютера",
  "Settings.application.customization.modal.upload.from-url": "З URL -адреси",
  "Settings.application.customization.modal.upload.from-url.input-label": "URL-адреса",
  "Settings.application.customization.modal.upload.next": "Далі",
  "Settings.application.customization.size-details": "Максимальний розмір: {dimension}×{dimension}, Максимальний розмір файлу: {size}KB",
  "Settings.application.description": "Глобальна інформація панелі адміністрації",
  "Settings.application.edition-title": "поточний план",
  "Settings.application.ee-or-ce": "{communityEdition, select, true {Community Edition} other {Enterprise Edition}}",
  "Settings.application.ee.admin-seats.add-seats": "Керування місця",
  "Settings.application.ee.admin-seats.support": "Зв'яжіться з відділом продажів",
  "Settings.application.ee.admin-seats.at-limit-tooltip": "На вичерпанні ліміту: додайте місця, щоб запросити більше користувачів",
  "Settings.application.ee.admin-seats.count": "<text>{enforcementUserCount}</text>/{permittedSeats}",
  "Settings.application.get-help": "Отримати допомогу",
  "Settings.application.link-pricing": "Дивіться всі тарифні плани",
  "Settings.application.link-upgrade": "Оновіть панель адміністратора",
  "Settings.application.node-version": "Версія Node.js",
  "Settings.application.strapi-version": "Версія Strapi",
  "Settings.application.strapiVersion": "Версія Strapi",
  "Settings.application.title": "Огляд",
  "Settings.error": "Помилка",
  "Settings.global": "Загальні налаштування",
  "Settings.permissions": "Панель адміністрації",
  "Settings.permissions.auditLogs.action": "Дія",
  "Settings.permissions.auditLogs.admin.auth.success": "Вхід адміністратора",
  "Settings.permissions.auditLogs.admin.logout": "Вихід адміністратора",
  "Settings.permissions.auditLogs.component.create": "Створити компонент",
  "Settings.permissions.auditLogs.component.delete": "Видалити компонент",
  "Settings.permissions.auditLogs.component.update": "Оновити компонент",
  "Settings.permissions.auditLogs.content-type.create": "Створити тип вмісту",
  "Settings.permissions.auditLogs.content-type.delete": "Видалити тип вмісту",
  "Settings.permissions.auditLogs.content-type.update": "Оновити тип вмісту",
  "Settings.permissions.auditLogs.date": "Дата",
  "Settings.permissions.auditLogs.details": "Деталі журналу",
  "Settings.permissions.auditLogs.entry.create": "Створити запис{model, select, undefined {} other { ({model})}}",
  "Settings.permissions.auditLogs.entry.delete": "Видалити запис{model, select, undefined {} other { ({model})}}",
  "Settings.permissions.auditLogs.entry.publish": "Опублікувати запис{model, select, undefined {} other {({model})}}",
  "Settings.permissions.auditLogs.entry.unpublish": "Відмінити публікацію запису{model, select, undefined {} other { ({model})}}",
  "Settings.permissions.auditLogs.entry.update": "Оновити запис{model, select, undefined {} other { ({model})}}",
  "Settings.permissions.auditLogs.filters.combobox.aria-label": "Пошук та вибір опції для фільтрації",
  "Settings.permissions.auditLogs.listview.header.subtitle": "Журнали всіх дій, що відбулися у вашому оточенні",
  "Settings.permissions.auditLogs.not-available": "Журнали аудиту доступні лише як частина платного плану. Оновіть, щоб отримати пошук та фільтрування всіх дій.",
  "Settings.permissions.auditLogs.media.create": "Створити медіа",
  "Settings.permissions.auditLogs.media.delete": "Видалити медіа",
  "Settings.permissions.auditLogs.media.update": "Оновіть медіа",
  "Settings.permissions.auditLogs.payload": "Корисне навантаження",
  "Settings.permissions.auditLogs.permission.create": "Створити дозвіл",
  "Settings.permissions.auditLogs.permission.delete": "Видалити дозвіл",
  "Settings.permissions.auditLogs.permission.update": "Оновити дозвіл",
  "Settings.permissions.auditLogs.role.create": "Створити роль",
  "Settings.permissions.auditLogs.role.delete": "Видалити роль",
  "Settings.permissions.auditLogs.role.update": "Оновити роль",
  "Settings.permissions.auditLogs.user": "Користувач",
  "Settings.permissions.auditLogs.user.create": "Створити користувача",
  "Settings.permissions.auditLogs.user.delete": "Видалити користувача",
  "Settings.permissions.auditLogs.user.fullname": "{firstname} {lastname}",
  "Settings.permissions.auditLogs.user.update": "Оновити користувача",
  "Settings.permissions.auditLogs.userId": "ID користувача",
  "Settings.permissions.category": "Налаштування дозволів для {category}",
  "Settings.permissions.category.plugins": "Налаштування дозволів для {category} плаґінів",
  "Settings.permissions.conditions.anytime": "Будь-коли",
  "Settings.permissions.conditions.apply": "Застосувати",
  "Settings.permissions.conditions.can": "Може",
  "Settings.permissions.conditions.conditions": "Умови",
  "Settings.permissions.conditions.define-conditions": "Визначте умови",
  "Settings.permissions.conditions.links": "Посилання",
  "Settings.permissions.conditions.no-actions": "Спочатку потрібно вибрати дії (створити, читати, оновлювати, ...), перш ніж визначити умови на них.",
  "Settings.permissions.conditions.none-selected": "Будь-коли",
  "Settings.permissions.conditions.or": "АБО",
  "Settings.permissions.conditions.when": "Коли",
  "Settings.permissions.select-all-by-permission": "Вибрати всі {label} дозволи",
  "Settings.permissions.select-by-permission": "Вибрати {label} дозвіл",
  "Settings.permissions.users.active": "Активний",
  "Settings.permissions.users.create": "запросити користувача",
  "Settings.permissions.users.email": "Email",
  "Settings.permissions.users.firstname": "Ім'я",
  "Settings.permissions.users.form.sso": "Підключіться з SSO",
  "Settings.permissions.users.sso.provider.error": "Під час запиту налаштувань SSO сталася помилка",
  "Settings.permissions.users.form.sso.description": "Якщо ввімкнено (ON), користувачі можуть увійти через SSO",
  "Settings.permissions.users.inactive": "Неактивний",
  "Settings.permissions.users.lastname": "Прізвище",
  "Settings.permissions.users.listview.header.subtitle": "Усі користувачі, які мають доступ до панелі адміністратора Strapi",
  "Settings.permissions.users.roles": "Ролі",
  "Settings.permissions.users.strapi-author": "Автор",
  "Settings.permissions.users.strapi-editor": "Редактор",
  "Settings.permissions.users.strapi-super-admin": "Супер-адміністратор",
  "Settings.permissions.users.tabs.label": "Дозволи вкладки",
  "Settings.permissions.users.user-status": "Статус користувача",
  "Settings.permissions.users.username": "Ім'я користувача",
  "Settings.profile.form.notify.data.loaded": "Ваші дані профілю завантажені",
  "Settings.profile.form.section.experience.clear.select": "Очистіть вибрану мову інтерфейсу",
  "Settings.profile.form.section.experience.here": "тут",
  "Settings.profile.form.section.experience.interfaceLanguage": "Мова інтерфейсу",
  "Settings.profile.form.section.experience.interfaceLanguage.hint": "Це відображатиме ваш власний інтерфейс обраною мовою.",
  "Settings.profile.form.section.experience.interfaceLanguageHelp": "Зміни переваг застосовуватимуться лише до вас. Додаткова інформація доступна {here}.",
  "Settings.profile.form.section.experience.mode.hint": "Відображає ваш інтерфейс у вибраному режимі.",
  "Settings.profile.form.section.experience.mode.label": "Режим відображення",
  "Settings.profile.form.section.experience.mode.option-label": "{name} режим",
  "Settings.profile.form.section.experience.mode.option-system-label": "Використовуйте налаштування системи",
  "Settings.profile.form.section.experience.title": "Досвід",
  "Settings.profile.form.section.head.title": "Профіль користувача",
  "Settings.profile.form.section.profile.page.title": "Профіль",
  "Settings.roles.create.description": "Визначте права, надані ролі",
  "Settings.roles.create.title": "Створити роль",
  "Settings.roles.created": "Роль створено",
  "Settings.roles.edit.title": "Редагувати роль",
  "Settings.roles.form.button.users-with-role": "{number, plural, =0 {# користувачів} one {# користувач} many {# користувачів} other {# користувачі}} з цією роллю",
  "Settings.roles.form.created": "Створено",
  "Settings.roles.form.description": "Назва та опис ролі",
  "Settings.roles.form.permission.property-label": "{label} дозволи",
  "Settings.roles.form.permissions.attributesPermissions": "Дозволи на поля",
  "Settings.roles.form.permissions.create": "Створити",
  "Settings.roles.form.permissions.delete": "Видалити",
  "Settings.roles.form.permissions.publish": "Опублікувати",
  "Settings.roles.form.permissions.read": "Читати",
  "Settings.roles.form.permissions.update": "Оновити",
  "Settings.roles.list.button.add": "Додати нову роль",
  "Settings.roles.list.description": "Список ролей",
  "Settings.roles.title.singular": "роль",
  "Settings.sso.description": "Налаштуйте параметри для функції єдиного входу (Single Sign-On).",
  "Settings.sso.form.defaultRole.description": "Приєднає нового автентифікованого користувача до вибраної ролі",
  "Settings.sso.form.defaultRole.description-not-allowed": "Ви повинні мати дозвіл на читання адміністраторських ролей",
  "Settings.sso.form.defaultRole.label": "Роль за замовчуванням",
  "Settings.sso.form.localAuthenticationLock.label": "Блокування локальної автентифікації",
  "Settings.sso.form.localAuthenticationLock.description": "Виберіть ролі, для яких ви хочете вимкнути локальну автентифікацію",
  "Settings.sso.form.registration.description": "Створювати нового користувача при вході через SSO, якщо обліковий запис не існує",
  "Settings.sso.form.registration.label": "Автоматичне реєстрування",
  "Settings.sso.title": "Єдиний вхід (Single Sign-On)",
  "Settings.sso.not-available": "SSO доступний лише як частина платного плану. Оновіть, щоб налаштувати додаткові методи входу та реєстрації для вашої панелі адміністрування.",
  "Settings.tokens.Button.cancel": "Скасувати",
  "Settings.tokens.Button.regenerate": "Перегенерувати",
  "Settings.tokens.ListView.headers.createdAt": "Створено о",
  "Settings.tokens.ListView.headers.description": "Опис",
  "Settings.tokens.ListView.headers.lastUsedAt": "Останнє використання",
  "Settings.tokens.ListView.headers.name": "Назва",
  "Settings.tokens.RegenerateDialog.title": "Перегенерувати токен",
  "Settings.tokens.copy.editMessage": "З міркувань безпеки ви можете бачити свій токен лише один раз.",
  "Settings.tokens.copy.editTitle": "Цей токен більше недоступний.",
  "Settings.tokens.copy.lastWarning": "Переконайтеся, що ви скопіювали цей токен, ви не зможете побачити його знову!",
  "Settings.tokens.duration.30-days": "30 днів",
  "Settings.tokens.duration.7-days": "7 днів",
  "Settings.tokens.duration.90-days": "90 днів",
  "Settings.tokens.duration.expiration-date": "Дата закінчення",
  "Settings.tokens.duration.unlimited": "Необмежено",
  "Settings.tokens.form.description": "Опис",
  "Settings.tokens.form.duration": "Тривалість токена",
  "Settings.tokens.form.name": "Назва",
  "Settings.tokens.form.type": "Тип токена",
  "Settings.tokens.notification.copied": "Токен скопійовано в буфер обміну.",
  "Settings.tokens.popUpWarning.message": "Ви впевнені, що хочете перегенерувати цей токен?",
  "Settings.tokens.regenerate": "Перегенерувати",
  "Settings.tokens.types.custom": "Користувацький",
  "Settings.tokens.types.full-access": "Повний доступ",
  "Settings.tokens.types.read-only": "Тільки для читання",
  "Settings.transferTokens.ListView.headers.type": "Тип токена",
  "Settings.transferTokens.addFirstToken": "Додати ваш перший токен передачі",
  "Settings.transferTokens.addNewToken": "Додати новий токен передачі",
  "Settings.transferTokens.create": "Створити новий токен передачі",
  "Settings.transferTokens.createPage.title": "Створити токен передачі",
  "Settings.transferTokens.description": "Список згенерованих токенів передачі",
  "Settings.transferTokens.emptyStateLayout": "У вас ще немає жодного вмісту...",
  "Settings.transferTokens.title": "Токени передачі",
  "Settings.webhooks.create": "Створити вебхук",
  "Settings.webhooks.create.header": "Додати новий заголовок",
  "Settings.webhooks.created": "Вебхук створено",
  "Settings.webhooks.event.publish-tooltip": "Ця подія існує тільки для вмістів з увімкненою системою Чернетка/Публікація",
  "Settings.webhooks.event.select": "Вибрати подію",
  "Settings.webhooks.events.isLoading": "Події завантажуються",
  "Settings.webhooks.events.create": "Створити",
  "Settings.webhooks.events.update": "Оновити",
  "Settings.webhooks.events.delete": "Видалити вебхук",
  "Settings.webhooks.form.events": "Події",
  "Settings.webhooks.form.headers": "Заголовки",
  "Settings.webhooks.form.url": "URL",
  "Settings.webhooks.headers.remove": "Зніміть ряд заголовка {number}",
  "Settings.webhooks.key": "Ключ",
  "Settings.webhooks.list.button.add": "Додати новий вебхук",
  "Settings.webhooks.list.description": "Отримуйте POST-сповіщення про зміни.",
  "Settings.webhooks.list.empty.description": "Додайте свій перший вебхук у цей список.",
  "Settings.webhooks.list.empty.link": "Переглянути документацію",
  "Settings.webhooks.list.empty.title": "Поки що немає вебхуків",
  "Settings.webhooks.list.th.actions": "дії",
  "Settings.webhooks.list.th.status": "статус",
  "Settings.webhooks.list.loading.success": "вебхуки завантажені",
  "Settings.webhooks.singular": "вебхук",
  "Settings.webhooks.title": "вебхуки",
  "Settings.webhooks.to.delete": "{webhooksToDeleteLength, plural, one {# вебхук} other {# вебхуки}} selected",
  "Settings.webhooks.trigger": "Тригер",
  "Settings.webhooks.trigger.cancel": "Скасувати виклик",
  "Settings.webhooks.trigger.pending": "Очікування…",
  "Settings.webhooks.trigger.save": "Будь ласка, збережіть тригер",
  "Settings.webhooks.trigger.success": "Успіх!",
  "Settings.webhooks.trigger.success.label": "Виклик вдався",
  "Settings.webhooks.trigger.test": "Випробувальний виклик",
  "Settings.webhooks.trigger.title": "Зберегти перед викликом",
  "Settings.webhooks.value": "Значення",
  "Settings.webhooks.validation.name.required": "Назва є обов'язковою",
  "Settings.webhooks.validation.name.regex": "Назва повинна починатися з літери та містити тільки літери, числа, пробіли та підкреслення",
  "Settings.webhooks.validation.url.required": "URL є обов'язковим",
  "Settings.webhooks.validation.url.regex": "Значення повинно бути дійсним URL",
  "Settings.webhooks.validation.key": "Ключ є обов'язковим",
  "Settings.webhooks.validation.value": "Значення є обов'язковим",
  "Usecase.back-end": "Back-end розробник",
  "Usecase.button.skip": "Пропустити це питання",
  "Usecase.content-creator": "Контент-кріейтор",
  "Usecase.front-end": "Front-end розробник",
  "Usecase.full-stack": "Full-stack розробник",
  "Usecase.input.work-type": "Який тип роботи ви виконуєте?",
  "Usecase.notification.success.project-created": "Проект успішно створено",
  "Usecase.other": "Інше",
  "Usecase.title": "Розкажіть трохи більше про себе",
  Username,
  "Users & Permissions": "Користувачі і доступи",
  Users,
  "Users.components.List.empty": "Немає користувачів...",
  "Users.components.List.empty.withFilters": "Немає користувачів з застосованими фільтрами...",
  "Users.components.List.empty.withSearch": "Немає користувачів, що відповідають пошуку ({search})...",
  "admin.pages.MarketPlacePage.sort.label": "Сортувати за",
  "admin.pages.MarketPlacePage.filters.categories": "Категорії",
  "admin.pages.MarketPlacePage.filters.categoriesSelected": "Вибрано {count, plural, =0 {Немає категорій} one {# категорія} many {# категорій}} other {# категорії}}",
  "admin.pages.MarketPlacePage.filters.collections": "Колекції",
  "admin.pages.MarketPlacePage.filters.collectionsSelected": "Вибрано {count, plural, =0 {Немає Колекцій} one {# Колекція} other {# Колекції}}",
  "admin.pages.MarketPlacePage.head": "Маркетплейс — Плаґіни",
  "admin.pages.MarketPlacePage.missingPlugin.description": "Скажіть нам, який плаґін ви шукаєте, і ми повідомимо наших розробників плаґінів спільноти, якщо вони шукають натхнення!",
  "admin.pages.MarketPlacePage.missingPlugin.title": "Відсутній плаґін?",
  "admin.pages.MarketPlacePage.offline.subtitle": "Ви повинні бути підключені до Інтернету, щоб отримати доступ до Маркетплейсу Strapi.",
  "admin.pages.MarketPlacePage.offline.title": "Ви офлайн",
  "admin.pages.MarketPlacePage.plugin.copy": "Скопіювати команду встановлення",
  "admin.pages.MarketPlacePage.plugin.copy.success": "Команда встановлення готова для вставки у ваш термінал",
  "admin.pages.MarketPlacePage.plugin.downloads": "Цей плаґін має {downloadsCount} завантажень щотижня",
  "admin.pages.MarketPlacePage.plugin.githubStars": "Цей плаґін отримав {starsCount} зірок на GitHub",
  "admin.pages.MarketPlacePage.plugin.info": "Дізнатися більше",
  "admin.pages.MarketPlacePage.plugin.info.label": "Дізнатися більше про {pluginName}",
  "admin.pages.MarketPlacePage.plugin.info.text": "Більше",
  "admin.pages.MarketPlacePage.plugin.installed": "Встановлено",
  "admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi": "Створено Strapi",
  "admin.pages.MarketPlacePage.plugin.tooltip.verified": "Плаґін перевірено Strapi",
  "admin.pages.MarketPlacePage.plugin.version": 'Оновіть вашу версію Strapi з "{strapiAppVersion}" до: "{versionRange}"',
  "admin.pages.MarketPlacePage.plugin.version.null": 'Не вдалося перевірити сумісність з вашою версією Strapi: "{strapiAppVersion}"',
  "admin.pages.MarketPlacePage.plugins": "Плаґіни",
  "admin.pages.MarketPlacePage.provider.downloads": "Цей провайдер має {downloadsCount} завантажень щотижня",
  "admin.pages.MarketPlacePage.provider.githubStars": "Цей провайдер отримав {starsCount} зірок на GitHub",
  "admin.pages.MarketPlacePage.providers": "Провайдери",
  "admin.pages.MarketPlacePage.search.clear": "Очистити пошук",
  "admin.pages.MarketPlacePage.search.empty": 'Немає результатів для "{target}"',
  "admin.pages.MarketPlacePage.search.placeholder": "Пошук",
  "admin.pages.MarketPlacePage.sort.alphabetical": "Алфавітний порядок",
  "admin.pages.MarketPlacePage.sort.alphabetical.selected": "Сортувати за алфавітним порядком",
  "admin.pages.MarketPlacePage.sort.githubStars": "Кількість зірок GitHub",
  "admin.pages.MarketPlacePage.sort.githubStars.selected": "Сортувати за зірками GitHub",
  "admin.pages.MarketPlacePage.sort.newest": "Найновіші",
  "admin.pages.MarketPlacePage.sort.newest.selected": "Сортувати за найновішими",
  "admin.pages.MarketPlacePage.sort.npmDownloads": "Кількість завантажень",
  "admin.pages.MarketPlacePage.sort.npmDownloads.selected": "Сортувати за завантаженнями npm",
  "admin.pages.MarketPlacePage.submit.plugin.link": "Подати плаґін",
  "admin.pages.MarketPlacePage.submit.provider.link": "Подати провайдера",
  "admin.pages.MarketPlacePage.subtitle": "Отримайте більше від Strapi",
  "admin.pages.MarketPlacePage.tab-group.label": "Плаґіни та провайдери для Strapi",
  anErrorOccurred,
  "app.component.CopyToClipboard.label": "Скопіювати в буфер обміну",
  "app.component.search.label": "Пошук за {target}",
  "app.component.table.duplicate": "Дублювати {target}",
  "app.component.table.edit": "Редагувати {target}",
  "app.component.table.read": "Читати {target}",
  "app.component.table.select.one-entry": "Вибрати {target}",
  "app.component.table.view": "Деталі {target}",
  "app.components.BlockLink.blog": "Блог",
  "app.components.BlockLink.blog.content": "Читайте останні новини про Strapi та екосистему.",
  "app.components.BlockLink.cloud": "Strapi Хмара",
  "app.components.BlockLink.cloud.content": "Повністю кероване хмарне хостингування для вашого проекту Strapi.",
  "app.components.BlockLink.code": "Приклади коду",
  "app.components.BlockLink.code.content": "Вчіться, тестуючи реальні проєкти, розроблені спільнотою.",
  "app.components.BlockLink.documentation.content": "Відкрийте для себе основні концепції, посібники та інструкції.",
  "app.components.BlockLink.tutorial": "Посібники",
  "app.components.BlockLink.tutorial.content": "Дотримуйтесь покрокових інструкцій для використання та налаштування Strapi.",
  "app.components.Button.cancel": "Скасувати",
  "app.components.Button.confirm": "Підтвердити",
  "app.components.Button.reset": "Скинути",
  "app.components.ComingSoonPage.comingSoon": "Незабаром",
  "app.components.ConfirmDialog.title": "Підтвердження",
  "app.components.DownloadInfo.download": "Завантажується...",
  "app.components.DownloadInfo.text": "Це може зайняти хвилинку. Дякуємо за терпіння.",
  "app.components.EmptyAttributes.title": "Поки немає полей",
  "app.components.EmptyStateLayout.content-document": "Не знайдено вміст",
  "app.components.EmptyStateLayout.content-permissions": "У вас немає дозволів на доступ до цього вмісту",
  "app.components.GuidedTour.CM.create.content": "<p>Створюйте та керуйте всіма вмістами тут у Менеджері вмісту.</p><p>Наприклад, продовжуючи приклад блогу, ви можете написати статтю, зберегти її та опублікувати, як вам подобається.</p><p>💡 Порада — не забудьте натиснути «Опублікувати» на створеному вмісті.</p>",
  "app.components.GuidedTour.CM.create.title": "⚡️ Створити вміст",
  "app.components.GuidedTour.CM.success.content": "<p>Чудово, залишився останній крок!</p><b>🚀 Перегляньте вміст в дії</b>",
  "app.components.GuidedTour.CM.success.cta.title": "Тестувати API",
  "app.components.GuidedTour.CM.success.title": "Крок 2: Завершено ✅",
  "app.components.GuidedTour.CTB.create.content": "<p>Типи Колекцій допомагають керувати кількома записами, а Типи Одиниць підходять для керування лише одним записом.</p> <p>Наприклад, для сайту блогу статті будуть типом колекції, тоді як головна сторінка буде типом одиниці.</p>",
  "app.components.GuidedTour.CTB.create.cta.title": "Створити Тип Колекції",
  "app.components.GuidedTour.CTB.create.title": "🧠 Створіть перший Тип Колекції",
  "app.components.GuidedTour.CTB.success.content": "<p>Добре йде!</p><b>⚡️ Чим би ви хотіли поділитися зі світом?</b>",
  "app.components.GuidedTour.CTB.success.title": "Крок 1: Завершено ✅",
  "app.components.GuidedTour.apiTokens.create.content": "<p>Згенеруйте токен автентифікації тут та отримайте вміст, який ви щойно створили.</p>",
  "app.components.GuidedTour.apiTokens.create.cta.title": "Згенерувати API токен",
  "app.components.GuidedTour.apiTokens.create.title": "🚀 Перегляньте вміст в дії",
  "app.components.GuidedTour.apiTokens.success.content": "<p>Перегляньте вміст в дії, зробивши HTTP-запит:</p><ul><li><p>На цю URL: <light>https://'<'YOUR_DOMAIN'>'/api/'<'YOUR_CT'>'</light></p></li><li><p>З заголовком (header): <light>Authorization: bearer '<'YOUR_API_TOKEN'>'</light></p></li></ul><p>Щоб дізнатись більше способів взаємодії з вмістом, перегляньте <documentationLink>документацію</documentationLink>.</p>",
  "app.components.GuidedTour.apiTokens.success.cta.title": "Повернутися на головну сторінку",
  "app.components.GuidedTour.apiTokens.success.title": "Крок 3: Завершено ✅",
  "app.components.GuidedTour.create-content": "Створити вміст",
  "app.components.GuidedTour.home.CM.title": "⚡️ Чим би ви хотіли поділитися зі світом?",
  "app.components.GuidedTour.home.CTB.cta.title": "Перейти до Конструктора типів вмісту",
  "app.components.GuidedTour.home.CTB.title": "🧠 Побудуйте структуру вмісту",
  "app.components.GuidedTour.home.apiTokens.cta.title": "Тестувати API",
  "app.components.GuidedTour.skip": "Пропустити тур",
  "app.components.GuidedTour.title": "3 кроки, щоб почати",
  "app.components.HomePage.button.blog": "Дивіться більше у блозі",
  "app.components.HomePage.community": "Знайдіть спільноту в інтернеті",
  "app.components.HomePage.community.content": "Обговорюйте з членами команди, контрибʼюторами та розробниками через різні канали зв'язку.",
  "app.components.HomePage.create": "Створити свій перший Тип Вмісту",
  "app.components.HomePage.roadmap": "Переглянути нашу дорожню карту",
  "app.components.HomePage.welcome": "Вітаємо на борту!",
  "app.components.HomePage.welcome.again": "Вітаємо ",
  "app.components.HomePage.welcomeBlock.content": "Ми дуже раді, що ви приєдналися до нашої спільноти. Нам завжди потрібен зворотній зв'язок, тому не вагайтесь писати нам на ",
  "app.components.HomePage.welcomeBlock.content.again": "Ми сподіваємось, що ви робите успіхи у вашому проекті... Слідкуйте за останніми новинами Strapi. Ми робимо все можливе, щоб покращити продукт завдяки вашим відгукам.",
  "app.components.HomePage.welcomeBlock.content.issues": "проблеми.",
  "app.components.HomePage.welcomeBlock.content.raise": " або повідомте про ",
  "app.components.ImgPreview.hint": "Перетягніть ваш файл в цю область або {browse} файл для завантаження",
  "app.components.ImgPreview.hint.browse": "оберіть",
  "app.components.InputFile.newFile": "Додати новий файл",
  "app.components.InputFileDetails.open": "Відкрити в новій вкладці",
  "app.components.InputFileDetails.originalName": "Оригінальна назва:",
  "app.components.InputFileDetails.remove": "Видалити цей файл",
  "app.components.InputFileDetails.size": "Розмір:",
  "app.components.InstallPluginPage.Download.description": "Завантаження та встановлення плаґіна може зайняти кілька секунд.",
  "app.components.InstallPluginPage.Download.title": "Завантаження...",
  "app.components.InstallPluginPage.description": "Розширюйте свій проект без зусиль.",
  "app.components.LeftMenu.collapse": "Згорнути навігаційну панель",
  "app.components.LeftMenu.expand": "Розгорнути навігаційну панель",
  "app.components.LeftMenu.general": "Загальні",
  "app.components.LeftMenu.logo.alt": "Логотип застосунку",
  "app.components.LeftMenu.logout": "Вийти",
  "app.components.LeftMenu.navbrand.title": "Панель управління Strapi",
  "app.components.LeftMenu.navbrand.workplace": "Робоче місце",
  "app.components.LeftMenu.plugins": "Плаґіни",
  "app.components.LeftMenu.trialCountdown": "Ваш пробний період закінчується {date}.",
  "app.components.LeftMenuFooter.help": "Допомога",
  "app.components.LeftMenuFooter.poweredBy": "Працює на ",
  "app.components.LeftMenuLinkContainer.collectionTypes": "Типи Колекцій",
  "app.components.LeftMenuLinkContainer.configuration": "Налаштування",
  "app.components.LeftMenuLinkContainer.general": "Загальні",
  "app.components.LeftMenuLinkContainer.noPluginsInstalled": "Немає встановлених плаґінів",
  "app.components.LeftMenuLinkContainer.plugins": "Плаґін",
  "app.components.LeftMenuLinkContainer.singleTypes": "Типи Одиниць",
  "app.components.ListPluginsPage.deletePlugin.description": "Видалення плаґіна може зайняти кілька секунд.",
  "app.components.ListPluginsPage.deletePlugin.title": "Видалення",
  "app.components.ListPluginsPage.description": "Список встановленних плаґінів у проекті.",
  "app.components.ListPluginsPage.head.title": "Список плаґінів",
  "app.components.Logout.logout": "Вийти",
  "app.components.Logout.profile": "Профіль",
  "app.components.MarketplaceBanner": "Відкрийте для себе плаґіни, створені спільнотою, та багато інших чудових речей, щоб розпочати ваш проект на Маркетплейсі Strapi.",
  "app.components.MarketplaceBanner.image.alt": "Логотип ракети Strapi",
  "app.components.MarketplaceBanner.link": "Перевірте зараз",
  "app.components.NotFoundPage.back": "Повернутися на головну",
  "app.components.NotFoundPage.description": "Не знайдено",
  "app.components.NpsSurvey.banner-title": "Наскільки ймовірно, що ви порекомендуєте Strapi своєму другу або колезі?",
  "app.components.NpsSurvey.feedback-response": "Дуже дякуємо за ваш відгук!",
  "app.components.NpsSurvey.feedback-question": "Чи маєте ви пропозиції щодо покращення?",
  "app.components.NpsSurvey.submit-feedback": "Надіслати відгук",
  "app.components.NpsSurvey.dismiss-survey-label": "Закрити опитування",
  "app.components.NpsSurvey.no-recommendation": "Зовсім малоймовірно",
  "app.components.NpsSurvey.happy-to-recommend": "Надзвичайно ймовірно",
  "app.components.Official": "Офіційний",
  "app.components.Onboarding.help.button": "Кнопка допомоги",
  "app.components.Onboarding.label.completed": "% завершено",
  "app.components.Onboarding.link.build-content": "Побудуйте архітектуру вмісту",
  "app.components.Onboarding.link.manage-content": "Додати та керувати вмістом",
  "app.components.Onboarding.link.manage-media": "Керувати медіа",
  "app.components.Onboarding.link.more-videos": "Дивитися більше відео",
  "app.components.Onboarding.title": "Вступні відео",
  "app.components.PluginCard.Button.label.download": "Завантажити",
  "app.components.PluginCard.Button.label.install": "Вже встановлено",
  "app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed": "Функція autoReload має буте включена. Будь ласка, запустіть свій додаток використовуючи `yarn develop`.",
  "app.components.PluginCard.PopUpWarning.install.impossible.confirm": "Я розумію!",
  "app.components.PluginCard.PopUpWarning.install.impossible.environment": "З міркувань безпеки плаґін можна завантажити тільки в середовищі розробки.",
  "app.components.PluginCard.PopUpWarning.install.impossible.title": "Завантаження неможливе",
  "app.components.PluginCard.compatible": "Сумісно з вашим додатком",
  "app.components.PluginCard.compatibleCommunity": "Сумісно зі спільнотою",
  "app.components.PluginCard.more-details": "Докладніше",
  "app.components.ToggleCheckbox.off-label": "Ні",
  "app.components.ToggleCheckbox.on-label": "Так",
  "app.components.Users.MagicLink.connect": "Скопіюйте та поділіться цим посиланням, щоб надати доступ цьому користувачу",
  "app.components.Users.MagicLink.connect.sso": "Надішліть це посилання користувачу, перший вхід може бути здійснений через постачальника SSO",
  "app.components.Users.ModalCreateBody.block-title.details": "Деталі користувача",
  "app.components.Users.ModalCreateBody.block-title.roles": "Ролі користувача",
  "app.components.Users.ModalCreateBody.block-title.roles.description": "Користувач може мати одну або декілька ролей",
  "app.components.Users.SortPicker.button-label": "Сортувати за",
  "app.components.Users.SortPicker.sortby.email_asc": "Електронна пошта (А до Я)",
  "app.components.Users.SortPicker.sortby.email_desc": "Електронна пошта (Я до А)",
  "app.components.Users.SortPicker.sortby.firstname_asc": "Ім'я (А до Я)",
  "app.components.Users.SortPicker.sortby.firstname_desc": "Ім'я (Я до А)",
  "app.components.Users.SortPicker.sortby.lastname_asc": "Прізвище (А до Я)",
  "app.components.Users.SortPicker.sortby.lastname_desc": "Прізвище (Я до А)",
  "app.components.Users.SortPicker.sortby.username_asc": "Ім'я користувача (А до Я)",
  "app.components.Users.SortPicker.sortby.username_desc": "Ім'я користувача (Я до А)",
  "app.components.listPlugins.button": "Додати новий плаґін",
  "app.components.listPlugins.title.none": "Немає встановлених плаґінів",
  "app.components.listPluginsPage.deletePlugin.error": "Під час видалення плаґіну сталася помилка",
  "app.containers.App.notification.error.init": "Сталася помилка під час виклику API",
  "app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin": "Якщо ви не отримали це посилання, будь ласка, зверніться до вашого адміністратора.",
  "app.containers.AuthPage.ForgotPasswordSuccess.text.email": "Це може зайняти декілька хвилин, щоб отримати посилання для відновлення пароля.",
  "app.containers.AuthPage.ForgotPasswordSuccess.title": "Електронна пошта відправлена",
  "app.containers.Users.EditPage.form.active.label": "Активний",
  "app.containers.Users.EditPage.header.label": "Редагувати {name}",
  "app.containers.Users.EditPage.header.label-loading": "Редагувати користувача",
  "app.containers.Users.EditPage.roles-bloc-title": "Призначені ролі",
  "app.containers.Users.ModalForm.footer.button-success": "Запросити користувача",
  "app.links.configure-view": "Налаштувати вигляд",
  "app.page.not.found": "Ой! Ми не можемо знайти сторінку, яку ви шукаєте...",
  "app.static.links.cheatsheet": "Шпаргалка",
  "app.utils.SelectOption.defaultMessage": " ",
  "app.utils.add-filter": "Додати фільтр",
  "app.utils.close-label": "Закрити",
  "app.utils.defaultMessage": " ",
  "app.utils.delete": "Видалити",
  "app.utils.duplicate": "Дублювати",
  "app.utils.edit": "Редагувати",
  "app.utils.errors.file-too-big.message": "Файл занадто великий",
  "app.utils.filter-value": "Значення фільтра",
  "app.utils.filters": "Фільтри",
  "app.utils.notify.data-loaded": "{target} завантажено",
  "app.utils.placeholder.defaultMessage": " ",
  "app.utils.publish": "Опублікувати",
  "app.utils.refresh": "Оновити",
  "app.utils.select-all": "Вибрати все",
  "app.utils.select-field": "Вибрати поле",
  "app.utils.select-filter": "Вибрати фільтр",
  "app.utils.unpublish": "Відмінити публікацію",
  "app.utils.published": "Опубліковано",
  "app.utils.ready-to-publish": "Готово до публікації",
  "app.utils.ready-to-publish-changes": "Готово до публікації змін",
  "app.utils.ready-to-unpublish-changes": "Готово до відміни публікації",
  "app.confirm.body": "Ви впевнені?",
  clearLabel,
  "coming.soon": "Цей вміст наразі на стадії розробки і повернеться через кілька тижнів!",
  "component.Input.error.validation.integer": "Значення має бути цілим числом",
  "components.AutoReloadBlocker.description": "Запустіть Strapi однією з наступних команд:",
  "components.AutoReloadBlocker.header": "Для цього плаґіну необхідна функція перезапуску (reload).",
  "components.ErrorBoundary.title": "Щось пішло не так...",
  "components.FilterOptions.FILTER_TYPES.$contains": "містить",
  "components.FilterOptions.FILTER_TYPES.$containsi": "містить (без урахування регістру)",
  "components.FilterOptions.FILTER_TYPES.$endsWith": "закінчується на",
  "components.FilterOptions.FILTER_TYPES.$endsWithi": "закінчується на (без урахування регістру)",
  "components.FilterOptions.FILTER_TYPES.$eq": "дорівнює",
  "components.FilterOptions.FILTER_TYPES.$eqi": "дорівнює (без урахування регістру)",
  "components.FilterOptions.FILTER_TYPES.$gt": "більше ніж",
  "components.FilterOptions.FILTER_TYPES.$gte": "більше або дорівнює",
  "components.FilterOptions.FILTER_TYPES.$lt": "менше ніж",
  "components.FilterOptions.FILTER_TYPES.$lte": "менше або дорівнює",
  "components.FilterOptions.FILTER_TYPES.$ne": "не дорівнює",
  "components.FilterOptions.FILTER_TYPES.$nei": "не дорівнює (без урахування регістру)",
  "components.FilterOptions.FILTER_TYPES.$notContains": "не містить",
  "components.FilterOptions.FILTER_TYPES.$notContainsi": "не містить (без урахування регістру)",
  "components.FilterOptions.FILTER_TYPES.$notNull": "не є порожнім",
  "components.FilterOptions.FILTER_TYPES.$null": "є порожнім",
  "components.FilterOptions.FILTER_TYPES.$startsWith": "починається з",
  "components.FilterOptions.FILTER_TYPES.$startsWithi": "починається з (без урахування регістру)",
  "components.Input.error.attribute.key.taken": "Це значення вже існує",
  "components.Input.error.attribute.sameKeyAndName": "Не може співпадати",
  "components.Input.error.attribute.taken": "Поле з такою назвою вже існує",
  "components.Input.error.contain.lowercase": "Пароль повинен містити принаймні одну малу літеру",
  "components.Input.error.contain.number": "Пароль повинен містити принаймні одну цифру",
  "components.Input.error.contain.uppercase": "Пароль повинен містити принаймні одну велику літеру",
  "components.Input.error.contentTypeName.taken": "Ця назва вже існує",
  "components.Input.error.custom-error": "{errorMessage} ",
  "components.Input.error.password.noMatch": "Паролі не співпадають.",
  "components.Input.error.validation.email": "Це не адреса електронної пошти",
  "components.Input.error.validation.json": "Це не відпоідає формату JSON",
  "components.Input.error.validation.lowercase": "Це має бути рядком з малими літерами",
  "components.Input.error.validation.max": "Значення занадто велике {max}.",
  "components.Input.error.validation.maxLength": "Значення занадто довге {max}.",
  "components.Input.error.validation.min": "Значення занадто мале {min}.",
  "components.Input.error.validation.minLength": "Значення занадто коротке {min}.",
  "components.Input.error.validation.minSupMax": "Не може бути більше",
  "components.Input.error.validation.regex": "Значення не відповідає регулярному виразу.",
  "components.Input.error.validation.string": "Це має бути рядок.",
  "components.Input.error.validation.required": "Це обов'язкове поле.",
  "components.Input.error.validation.unique": "Це значення вже використовується.",
  "components.Input.error.validation.email.withField": "{field} є недійсною електронною поштою",
  "components.Input.error.validation.json.withField": "Значення {field} не відповідає формату JSON",
  "components.Input.error.validation.lowercase.withField": "{field} повинен бути рядком з малими літерами",
  "components.Input.error.validation.max.withField": "Значення {field} надто велике.",
  "components.Input.error.validation.maxLength.withField": "Значення {field} надто довге.",
  "components.Input.error.validation.min.withField": "Значення {field} надто мале.",
  "components.Input.error.validation.minLength.withField": "Значення {field} надто коротке.",
  "components.Input.error.validation.minSupMax.withField": "Значення {field} не може бути більшим",
  "components.Input.error.validation.regex.withField": "Значення {field} не відповідає регулярному виразу.",
  "components.Input.error.validation.required.withField": "Значення {field} є обов'язковим.",
  "components.Input.error.validation.unique.withField": "{field} вже використовується.",
  "components.InputSelect.option.placeholder": "Виберіть тут",
  "components.ListRow.empty": "Немає даних для відображення.",
  "components.NotAllowedInput.text": "Ви не маєте дозволу на редагування цього поля.",
  "components.OverlayBlocker.description": "Ви скористалися функціоналом, яки потребує перезавантаження серверу. Будь ласка зачекайте поки сервер запускається.",
  "components.OverlayBlocker.description.serverError": "Сервер мав перезавантажитись, будь ласка, перевірте свої журнали у терміналі.",
  "components.OverlayBlocker.title": "Чекаємо на перезавантаження...",
  "components.OverlayBlocker.title.serverError": "Перезавантаження триває довше ніж очікувалось.",
  "components.PageFooter.select": "записів на сторінці",
  "components.ProductionBlocker.description": "З міркувань безпеки ми маємо відключити цей плаґін в інших середовищах.",
  "components.ProductionBlocker.header": "Цей плаґін доступний лише в середовищі розробки.",
  "components.ViewSettings.tooltip": "Налаштування перегляду",
  "components.TableHeader.sort": "Сортувати за {label}",
  "components.Blocks.modifiers.bold": "Жирний",
  "components.Blocks.modifiers.italic": "Курсив",
  "components.Blocks.modifiers.underline": "Підкреслений",
  "components.Blocks.modifiers.strikethrough": "Перекреслений",
  "components.Blocks.modifiers.code": "Вбудований код",
  "components.Blocks.link": "Посилання",
  "components.Blocks.expand": "Розгорнути",
  "components.Blocks.collapse": "Згорнути",
  "components.Blocks.popover.text": "Текст",
  "components.Blocks.popover.text.placeholder": "Введіть текст посилання",
  "components.Blocks.popover.link": "Посилання",
  "components.Blocks.popover.link.placeholder": "Вставте посилання",
  "components.Blocks.popover.link.error": "Будь ласка, введіть дійсне посилання",
  "components.Blocks.popover.save": "Зберегти",
  "components.Blocks.popover.cancel": "Скасувати",
  "components.Blocks.popover.remove": "Видалити",
  "components.Blocks.popover.edit": "Редагувати",
  "components.Blocks.blocks.selectBlock": "Виберіть блок",
  "components.Blocks.blocks.text": "Текст",
  "components.Blocks.blocks.heading1": "Заголовок 1",
  "components.Blocks.blocks.heading2": "Заголовок 2",
  "components.Blocks.blocks.heading3": "Заголовок 3",
  "components.Blocks.blocks.heading4": "Заголовок 4",
  "components.Blocks.blocks.heading5": "Заголовок 5",
  "components.Blocks.blocks.heading6": "Заголовок 6",
  "components.Blocks.blocks.code": "Блок коду",
  "components.Blocks.blocks.quote": "Цитата",
  "components.Blocks.blocks.image": "Зображення",
  "components.Blocks.blocks.unorderedList": "Маркерований список",
  "components.Blocks.blocks.orderedList": "Нумерований список",
  "components.Blocks.blocks.code.languageLabel": "Виберіть мову",
  "components.Blocks.dnd.instruction": "Щоб змінити порядок блоків, натисніть Command або Control разом із Shift і клавішами зі стрілками вгору або вниз",
  "components.Blocks.dnd.reorder": "{item}, переміщено. Нова позиція в редакторі: {position}.",
  "components.Wysiwyg.ToggleMode.markdown-mode": "Режим Markdown",
  "components.Wysiwyg.ToggleMode.preview-mode": "Режим перегляду",
  "components.Wysiwyg.collapse": "Згорнути",
  "components.Wysiwyg.selectOptions.H1": "Заголовок H1",
  "components.Wysiwyg.selectOptions.H2": "Заголовок H2",
  "components.Wysiwyg.selectOptions.H3": "Заголовок H3",
  "components.Wysiwyg.selectOptions.H4": "Заголовок H4",
  "components.Wysiwyg.selectOptions.H5": "Заголовок H5",
  "components.Wysiwyg.selectOptions.H6": "Заголовок H6",
  "components.Wysiwyg.selectOptions.title": "Додати заголовок",
  "components.WysiwygBottomControls.charactersIndicators": "символів",
  "components.WysiwygBottomControls.fullscreen": "Розгорнути",
  "components.WysiwygBottomControls.uploadFiles": "Перетягніть файли сюди, вставте з буфера обміну або {browse}.",
  "components.WysiwygBottomControls.uploadFiles.browse": "обрати їх",
  "components.pagination.go-to": "Перейти на сторінку {page}",
  "components.pagination.go-to-next": "Перейти на наступну сторінку",
  "components.pagination.go-to-previous": "Перейти на попередню сторінку",
  "components.pagination.remaining-links": "І ще {number} посилань",
  "components.popUpWarning.button.cancel": "Ні, скасувати",
  "components.popUpWarning.button.confirm": "Так, підтвердити",
  "components.popUpWarning.message": "Ви впевнені, що хочете це видалити?",
  "components.popUpWarning.title": "Підтвердіть, будь ласка",
  dark,
  "form.button.continue": "Продовжити",
  "form.button.done": "Готово",
  "global.actions": "Дії",
  "global.auditLogs": "Аудиторські журнали",
  "global.back": "Назад",
  "global.cancel": "Скасувати",
  "global.change-password": "Змінити пароль",
  "global.close": "Закрити",
  "global.content-manager": "Менеджер вмісту",
  "global.home": "Головна",
  "global.continue": "Продовжити",
  "global.delete": "Видалити",
  "global.delete-target": "Видалити {target}",
  "global.description": "Опис",
  "global.details": "Деталі",
  "global.disabled": "Вимкнено",
  "global.documentation": "Документація",
  "global.enabled": "Увімкнено",
  "global.finish": "Завершити",
  "global.marketplace": "Маркетплейс",
  "global.name": "Назва",
  "global.new": "Новий",
  "global.none": "Немає",
  "global.password": "Пароль",
  "global.plugins": "Плаґіни",
  "global.plugins.content-manager": "Менеджер вмісту",
  "global.plugins.content-manager.description": "Швидкий спосіб перегляду, редагування та видалення даних у вашій базі даних.",
  "global.plugins.content-type-builder": "Конструктор типів вмісту",
  "global.plugins.content-type-builder.description": "Моделюйте структуру даних вашого API. Створюйте нові поля та зв’язки лише за хвилину. Файли автоматично створюються та оновлюються у вашому проекті.",
  "global.plugins.documentation": "Документація",
  "global.plugins.documentation.description": "Створіть документ OpenAPI та візуалізуйте ваш API за допомогою інтерфейсу SWAGGER UI.",
  "global.plugins.email": "Електронна пошта",
  "global.plugins.email.description": "Налаштуйте ваш застосунок для відправки електронних листів.",
  "global.plugins.graphql": "GraphQL",
  "global.plugins.graphql.description": "Додає кінцеву точку GraphQL з методами API за замовчуванням.",
  "global.plugins.i18n": "Інтернаціоналізація",
  "global.plugins.i18n.description": "Цей плаґін дозволяє створювати, читати та оновлювати вміст на різних мовах як з панелі адміністрування, так і через API.",
  "global.plugins.sentry": "Sentry",
  "global.plugins.sentry.description": "Відправляйте події помилок Strapi до Sentry.",
  "global.plugins.upload": "Медіабібліотека",
  "global.plugins.upload.description": "Керування медіа-файлами.",
  "global.plugins.users-permissions": "Ролі та дозволи",
  "global.plugins.users-permissions.description": "Захистіть ваш API повним процесом автентифікації на основі JWT. Цей плаґін також має стратегію ACL, що дозволяє керувати дозволами між групами користувачів.",
  "global.profile": "Профіль",
  "global.prompt.unsaved": "Ви впевнені, що хочете залишити цю сторінку? Всі ваші зміни будуть втрачені",
  "global.reset-password": "Скинути пароль",
  "global.roles": "Ролі",
  "global.save": "Зберегти",
  "global.search": "Пошук",
  "global.see-more": "Дивитися більше",
  "global.select": "Вибрати",
  "global.select-all-entries": "Вибрати всі записи",
  "global.settings": "Налаштування",
  "global.type": "Тип",
  "global.users": "Користувачі",
  "global.fullname": "{firstname} {lastname}",
  "global.learn-more": "Дізнатися більше",
  light,
  "notification.contentType.relations.conflict": "Тип вмісту має конфлікт зв'язків",
  "notification.default.title": "Інформація:",
  "notification.ee.warning.at-seat-limit.title": "{licenseLimitStatus, select, OVER_LIMIT {Перевищено} AT_LIMIT {Досягнуто}} ліміт місць ({currentUserCount}/{permittedSeats})",
  "notification.ee.warning.over-.message": "Додайте місця для {licenseLimitStatus, select, OVER_LIMIT {залучення} AT_LIMIT {повторної активації}} користувачів. Якщо ви вже це зробили, але зміни ще не відображаються в Strapi, переконайтеся, що ви перезапустили ваш застосунок.",
  "notification.error": "Сталася помилка",
  "notification.error.invalid.configuration": "У вас неправильна конфігурація, перевірте журнал сервера для отримання додаткової інформації.",
  "notification.error.layout": "Не вдалося отримати макет",
  "notification.error.tokennamenotunique": "Назва вже призначена іншому токену",
  "notification.form.error.fields": "Форма містить деякі помилки",
  "notification.form.success.fields": "Зміни збережено",
  "notification.link-copied": "Посилання скопійовано в буфер обміну",
  "notification.permission.not-allowed-read": "Вам не дозволено переглядати цей документ",
  "notification.success.apitokencreated": "API токен успішно створено",
  "notification.success.apitokenedited": "API токен успішно оновлено",
  "notification.success.delete": "Цей елемент був видалений",
  "notification.success.saved": "Зміни збережено",
  "notification.success.title": "Успіх:",
  "notification.success.transfertokencreated": "Токен передачі успішно створено",
  "notification.success.transfertokenedited": "Токен передачі успішно оновлено",
  "notification.version.update.message": "Доступна нова версія Strapi!",
  "notification.warning.404": "404 - Не знайдено",
  "notification.warning.title": "Увага:",
  or,
  "request.error.model.unknown": "Цієї моделі данних не існує",
  selectButtonTitle,
  skipToContent,
  submit,
  "components.Search.placeholder": "Пошук..."
};
export {
  Analytics,
  Documentation,
  Email,
  Password,
  Provider,
  ResetPasswordToken,
  Role,
  Username,
  Users,
  anErrorOccurred,
  clearLabel,
  dark,
  uk as default,
  light,
  or,
  selectButtonTitle,
  skipToContent,
  submit
};
//# sourceMappingURL=uk.json-SXBWOQYX.js.map
