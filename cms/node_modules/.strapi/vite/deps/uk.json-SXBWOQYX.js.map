{"version": 3, "sources": ["../../../@strapi/admin/dist/admin/admin/src/translations/uk.json.mjs"], "sourcesContent": ["var Analytics = \"Аналітика\";\nvar Documentation = \"Документація\";\nvar Email = \"Email\";\nvar Password = \"Пароль\";\nvar Provider = \"Провайдер\";\nvar ResetPasswordToken = \"Скинути токен паролю\";\nvar Role = \"Роль\";\nvar Username = \"Ім'я користувача\";\nvar Users = \"Користувачі\";\nvar anErrorOccurred = \"Ой! Щось пішло не так. Будь ласка, спробуйте ще раз.\";\nvar clearLabel = \"Очистити\";\nvar dark = \"Темний\";\nvar light = \"Світлий\";\nvar or = \"АБО\";\nvar selectButtonTitle = \"Вибрати\";\nvar skipToContent = \"Перейти до вмісту\";\nvar submit = \"Відправити\";\nvar uk = {\n    Analytics: Analytics,\n    \"Auth.components.Oops.text\": \"Ваш обліковий запис призупинено.\",\n    \"Auth.components.Oops.text.admin\": \"Якщо це помилка, будь ласка, зв'яжіться з адміністратором.\",\n    \"Auth.components.Oops.title\": \"Ой...\",\n    \"Auth.form.active.label\": \"Активний\",\n    \"Auth.form.button.forgot-password\": \"Відправити Email\",\n    \"Auth.form.button.go-home\": \"НАЗАД НА ГОЛОВНУ\",\n    \"Auth.form.button.login\": \"Увійти\",\n    \"Auth.form.button.login.providers.error\": \"Ми не можемо підключити вас через вибраного постачальника.\",\n    \"Auth.form.button.login.strapi\": \"Увійти через stapi\",\n    \"Auth.form.button.password-recovery\": \"Відновлення пароля\",\n    \"Auth.form.button.register\": \"Готовий почати\",\n    \"Auth.form.confirmPassword.label\": \"Підтвердити пароль\",\n    \"Auth.form.currentPassword.label\": \"Поточний пароль\",\n    \"Auth.form.email.label\": \"Email\",\n    \"Auth.form.email.placeholder\": \"н.п. <EMAIL>\",\n    \"Auth.form.error.blocked\": \"Ваш обліковий запис був заблокований адміністратором.\",\n    \"Auth.form.error.code.provide\": \"Надано неправильний код.\",\n    \"Auth.form.error.confirmed\": \"Ваш email не підтверджений.\",\n    \"Auth.form.error.email.invalid\": \"Це не схоже на email.\",\n    \"Auth.form.error.email.provide\": \"Вкажіть своє ім'я користувача або email.\",\n    \"Auth.form.error.email.taken\": \"Цей email вже використовується.\",\n    \"Auth.form.error.invalid\": \"Неправильний логін або пароль.\",\n    \"Auth.form.error.params.provide\": \"Надано неправильні параметри.\",\n    \"Auth.form.error.password.format\": \"У паролі не може бути більше ніж три символи `$`\",\n    \"Auth.form.error.password.local\": \"Цей користувач ніколи не встановлював пароль, будь ласка увійдіть через постачальника, який ви вікористовували під час реєстрації.\",\n    \"Auth.form.error.password.matching\": \"Паролі не співпадають.\",\n    \"Auth.form.error.password.provide\": \"Вкажіть свій пароль, будь ласка.\",\n    \"Auth.form.error.ratelimit\": \"Забагато спроб, будь ласка, спробуйте знову через хвилинку.\",\n    \"Auth.form.error.user.not-exist\": \"Цього email не існує.\",\n    \"Auth.form.error.username.taken\": \"Це ім'я користувача вже використовується.\",\n    \"Auth.form.firstname.label\": \"Імʼя\",\n    \"Auth.form.firstname.placeholder\": \"н.п. Тарас\",\n    \"Auth.form.forgot-password.email.label\": \"Вкажіть свій email\",\n    \"Auth.form.forgot-password.email.label.success\": \"Ми надіслали вам листа до\",\n    \"Auth.form.lastname.label\": \"Прізвище\",\n    \"Auth.form.lastname.placeholder\": \"e.g. Шевченко\",\n    \"Auth.form.password.hide-password\": \"Сховати пароль\",\n    \"Auth.form.password.hint\": \"Повинно бути щонайменше 8 символів, 1 велика літера, 1 мала літера та 1 число\",\n    \"Auth.form.password.show-password\": \"Показати пароль\",\n    \"Auth.form.register.news.label\": \"Тримайте мене в курсі нових можливостей та майбутніх вдосконалень (тим самим ви приймаєте {terms} та {policy})\",\n    \"Auth.form.register.subtitle\": \"Дані для входу використовуються лише для аутентифікації в Strapi. Усі збережені дані будуть зберігатися у вашій базі даних.\",\n    \"Auth.form.rememberMe.label\": \"Нагадати мені\",\n    \"Auth.form.username.label\": \"Імʼя користувача\",\n    \"Auth.form.username.placeholder\": \"н.п. Taras_Shevchenko\",\n    \"Auth.form.welcome.subtitle\": \"Увійдіть у свій обліковий запис Strapi\",\n    \"Auth.form.welcome.title\": \"Ласкаво просимо до Strapi!\",\n    \"Auth.link.forgot-password\": \"Забули пароль?\",\n    \"Auth.link.ready\": \"Готові увійти?\",\n    \"Auth.link.signin\": \"Увійти\",\n    \"Auth.link.signin.account\": \"Уже є обліковий запис?\",\n    \"Auth.login.sso.divider\": \"Або увійти за допомогою\",\n    \"Auth.login.sso.loading\": \"Завантаження постачальників...\",\n    \"Auth.login.sso.subtitle\": \"Увійдіть у свій обліковий запис через SSO\",\n    \"Auth.privacy-policy-agreement.policy\": \"політику конфіденційності\",\n    \"Auth.privacy-policy-agreement.terms\": \"умови\",\n    \"Auth.reset-password.title\": \"Скиньте пароль\",\n    \"Content Manager\": \"Рекдактор контенту\",\n    \"Content Type Builder\": \"Конструктор типів вмісту\",\n    Documentation: Documentation,\n    Email: Email,\n    \"Files Upload\": \"Завантаження файлів\",\n    \"HomePage.head.title\": \"Головна\",\n    \"HomePage.roadmap\": \"Наша дорожня карта\",\n    \"HomePage.welcome.congrats\": \"Вітаємо!\",\n    \"HomePage.welcome.congrats.content\": \"Ви увійшли як перший адміністратор. Щоб відкрити для себе потужні можливості Strapi,\",\n    \"HomePage.welcome.congrats.content.bold\": \"ми рекомендуємо створити свій перший Collection-Type.\",\n    \"Media Library\": \"Медіатека\",\n    \"New entry\": \"Новий запис\",\n    Password: Password,\n    Provider: Provider,\n    ResetPasswordToken: ResetPasswordToken,\n    Role: Role,\n    \"Roles & Permissions\": \"Ролі й доступи\",\n    \"Roles.ListPage.notification.delete-all-not-allowed\": \"Деякі ролі не можна було видалити, оскільки вони пов'язані з користувачем\",\n    \"Roles.ListPage.notification.delete-not-allowed\": \"Роль не може бути видалена, якщо пов’язана з користувачами\",\n    \"Roles.RoleRow.select-all\": \"Виберіть {name} для масових дій\",\n    \"Roles.RoleRow.user-count\": \"{number, plural, =0 {# користувачів} one {# користувач} few{# користувачів} many {# користувачів} other {# користувачі}}\",\n    \"Roles.components.List.empty.withSearch\": \"Немає ролі, що відповідає пошуку ({search})...\",\n    \"Settings.PageTitle\": \"Налаштування — {name}\",\n    \"Settings.apiTokens.ListView.headers.createdAt\": \"Створено\",\n    \"Settings.apiTokens.ListView.headers.description\": \"Опис\",\n    \"Settings.apiTokens.ListView.headers.lastUsedAt\": \"Востаннє використовувалося\",\n    \"Settings.apiTokens.ListView.headers.name\": \"Назва\",\n    \"Settings.apiTokens.ListView.headers.type\": \"Тип токена\",\n    \"Settings.apiTokens.addFirstToken\": \"Додайте свій перший токен API\",\n    \"Settings.apiTokens.addNewToken\": \"Додайте новий токен API\",\n    \"Settings.apiTokens.create\": \"Створити новий токен API\",\n    \"Settings.apiTokens.createPage.BoundRoute.title\": \"Пов'язати шлях з\",\n    \"Settings.apiTokens.createPage.permissions.description\": \"Нижче наведено лише дії, пов'язані з маршрутом.\",\n    \"Settings.apiTokens.createPage.permissions.header.hint\": \"Виберіть дії програми або дії плаґіна та натисніть на піктограму гвинтика, щоб відобразити пов'язаний маршрут\",\n    \"Settings.apiTokens.createPage.permissions.header.title\": \"Розширені налаштування\",\n    \"Settings.apiTokens.createPage.permissions.title\": \"Дозволи\",\n    \"Settings.apiTokens.createPage.title\": \"Створити токен API\",\n    \"Settings.apiTokens.description\": \"Список генерованих токенів для використання API\",\n    \"Settings.apiTokens.emptyStateLayout\": \"У вас ще немає вмісту...\",\n    \"Settings.apiTokens.regenerate\": \"Регенерувати\",\n    \"Settings.apiTokens.title\": \"токени API\",\n    \"Settings.apiTokens.lastHour\": \"минула година\",\n    \"Settings.application.customization\": \"Налаштування\",\n    \"Settings.application.customization.auth-logo.carousel-hint\": \"Замініть логотип на сторінках аутентифікації\",\n    \"Settings.application.customization.carousel-hint\": \"Змінити логотип панелі адміністратора (Максимальний розмір: {dimension}x{dimension}, Максимальний розмір файлу: {size}KB)\",\n    \"Settings.application.customization.carousel-slide.label\": \"Слайд логотипу\",\n    \"Settings.application.customization.carousel.auth-logo.title\": \"Логотип аутентифікації\",\n    \"Settings.application.customization.carousel.change-action\": \"Змінити логотип\",\n    \"Settings.application.customization.carousel.menu-logo.title\": \"Логотип меню\",\n    \"Settings.application.customization.carousel.reset-action\": \"Скидання логотипу\",\n    \"Settings.application.customization.carousel.title\": \"Логотип\",\n    \"Settings.application.customization.menu-logo.carousel-hint\": \"Замініть логотип у меню\",\n    \"Settings.application.customization.modal.cancel\": \"Скасувати\",\n    \"Settings.application.customization.modal.pending\": \"Логотип готовий до завантаження\",\n    \"Settings.application.customization.modal.pending.card-badge\": \"зображення\",\n    \"Settings.application.customization.modal.pending.choose-another\": \"Вибрати інший логотип\",\n    \"Settings.application.customization.modal.pending.subtitle\": \"Керуйте обраним логотипом перед його завантаженням\",\n    \"Settings.application.customization.modal.pending.title\": \"Логотип, готовий до завантаження\",\n    \"Settings.application.customization.modal.pending.upload\": \"Завантажити логотип\",\n    \"Settings.application.customization.modal.tab.label\": \"Як ви хочете завантажити свої файли?\",\n    \"Settings.application.customization.modal.upload\": \"Завантажити логотип\",\n    \"Settings.application.customization.modal.upload.cta.browse\": \"Переглянути файли\",\n    \"Settings.application.customization.modal.upload.drag-drop\": \"Перетягніть файл сюди або\",\n    \"Settings.application.customization.modal.upload.error-format\": \"Неправильний завантажений формат файлу (підтримуються лише формати: jpeg, jpg, png, svg).\",\n    \"Settings.application.customization.modal.upload.error-network\": \"Помилка мережі.\",\n    \"Settings.application.customization.modal.upload.error-size\": \"Завантажений файл занадто великий (максимальний розмір: {dimension}x{dimension}, максимальний розмір файлу: {size}KB)\",\n    \"Settings.application.customization.modal.upload.file-validation\": \"Максимальний розмір: {dimension}x{dimension}, Максимальний розмір файлу: {size}KB\",\n    \"Settings.application.customization.modal.upload.from-computer\": \"З комп’ютера\",\n    \"Settings.application.customization.modal.upload.from-url\": \"З URL -адреси\",\n    \"Settings.application.customization.modal.upload.from-url.input-label\": \"URL-адреса\",\n    \"Settings.application.customization.modal.upload.next\": \"Далі\",\n    \"Settings.application.customization.size-details\": \"Максимальний розмір: {dimension}×{dimension}, Максимальний розмір файлу: {size}KB\",\n    \"Settings.application.description\": \"Глобальна інформація панелі адміністрації\",\n    \"Settings.application.edition-title\": \"поточний план\",\n    \"Settings.application.ee-or-ce\": \"{communityEdition, select, true {Community Edition} other {Enterprise Edition}}\",\n    \"Settings.application.ee.admin-seats.add-seats\": \"Керування місця\",\n    \"Settings.application.ee.admin-seats.support\": \"Зв'яжіться з відділом продажів\",\n    \"Settings.application.ee.admin-seats.at-limit-tooltip\": \"На вичерпанні ліміту: додайте місця, щоб запросити більше користувачів\",\n    \"Settings.application.ee.admin-seats.count\": \"<text>{enforcementUserCount}</text>/{permittedSeats}\",\n    \"Settings.application.get-help\": \"Отримати допомогу\",\n    \"Settings.application.link-pricing\": \"Дивіться всі тарифні плани\",\n    \"Settings.application.link-upgrade\": \"Оновіть панель адміністратора\",\n    \"Settings.application.node-version\": \"Версія Node.js\",\n    \"Settings.application.strapi-version\": \"Версія Strapi\",\n    \"Settings.application.strapiVersion\": \"Версія Strapi\",\n    \"Settings.application.title\": \"Огляд\",\n    \"Settings.error\": \"Помилка\",\n    \"Settings.global\": \"Загальні налаштування\",\n    \"Settings.permissions\": \"Панель адміністрації\",\n    \"Settings.permissions.auditLogs.action\": \"Дія\",\n    \"Settings.permissions.auditLogs.admin.auth.success\": \"Вхід адміністратора\",\n    \"Settings.permissions.auditLogs.admin.logout\": \"Вихід адміністратора\",\n    \"Settings.permissions.auditLogs.component.create\": \"Створити компонент\",\n    \"Settings.permissions.auditLogs.component.delete\": \"Видалити компонент\",\n    \"Settings.permissions.auditLogs.component.update\": \"Оновити компонент\",\n    \"Settings.permissions.auditLogs.content-type.create\": \"Створити тип вмісту\",\n    \"Settings.permissions.auditLogs.content-type.delete\": \"Видалити тип вмісту\",\n    \"Settings.permissions.auditLogs.content-type.update\": \"Оновити тип вмісту\",\n    \"Settings.permissions.auditLogs.date\": \"Дата\",\n    \"Settings.permissions.auditLogs.details\": \"Деталі журналу\",\n    \"Settings.permissions.auditLogs.entry.create\": \"Створити запис{model, select, undefined {} other { ({model})}}\",\n    \"Settings.permissions.auditLogs.entry.delete\": \"Видалити запис{model, select, undefined {} other { ({model})}}\",\n    \"Settings.permissions.auditLogs.entry.publish\": \"Опублікувати запис{model, select, undefined {} other {({model})}}\",\n    \"Settings.permissions.auditLogs.entry.unpublish\": \"Відмінити публікацію запису{model, select, undefined {} other { ({model})}}\",\n    \"Settings.permissions.auditLogs.entry.update\": \"Оновити запис{model, select, undefined {} other { ({model})}}\",\n    \"Settings.permissions.auditLogs.filters.combobox.aria-label\": \"Пошук та вибір опції для фільтрації\",\n    \"Settings.permissions.auditLogs.listview.header.subtitle\": \"Журнали всіх дій, що відбулися у вашому оточенні\",\n    \"Settings.permissions.auditLogs.not-available\": \"Журнали аудиту доступні лише як частина платного плану. Оновіть, щоб отримати пошук та фільтрування всіх дій.\",\n    \"Settings.permissions.auditLogs.media.create\": \"Створити медіа\",\n    \"Settings.permissions.auditLogs.media.delete\": \"Видалити медіа\",\n    \"Settings.permissions.auditLogs.media.update\": \"Оновіть медіа\",\n    \"Settings.permissions.auditLogs.payload\": \"Корисне навантаження\",\n    \"Settings.permissions.auditLogs.permission.create\": \"Створити дозвіл\",\n    \"Settings.permissions.auditLogs.permission.delete\": \"Видалити дозвіл\",\n    \"Settings.permissions.auditLogs.permission.update\": \"Оновити дозвіл\",\n    \"Settings.permissions.auditLogs.role.create\": \"Створити роль\",\n    \"Settings.permissions.auditLogs.role.delete\": \"Видалити роль\",\n    \"Settings.permissions.auditLogs.role.update\": \"Оновити роль\",\n    \"Settings.permissions.auditLogs.user\": \"Користувач\",\n    \"Settings.permissions.auditLogs.user.create\": \"Створити користувача\",\n    \"Settings.permissions.auditLogs.user.delete\": \"Видалити користувача\",\n    \"Settings.permissions.auditLogs.user.fullname\": \"{firstname} {lastname}\",\n    \"Settings.permissions.auditLogs.user.update\": \"Оновити користувача\",\n    \"Settings.permissions.auditLogs.userId\": \"ID користувача\",\n    \"Settings.permissions.category\": \"Налаштування дозволів для {category}\",\n    \"Settings.permissions.category.plugins\": \"Налаштування дозволів для {category} плаґінів\",\n    \"Settings.permissions.conditions.anytime\": \"Будь-коли\",\n    \"Settings.permissions.conditions.apply\": \"Застосувати\",\n    \"Settings.permissions.conditions.can\": \"Може\",\n    \"Settings.permissions.conditions.conditions\": \"Умови\",\n    \"Settings.permissions.conditions.define-conditions\": \"Визначте умови\",\n    \"Settings.permissions.conditions.links\": \"Посилання\",\n    \"Settings.permissions.conditions.no-actions\": \"Спочатку потрібно вибрати дії (створити, читати, оновлювати, ...), перш ніж визначити умови на них.\",\n    \"Settings.permissions.conditions.none-selected\": \"Будь-коли\",\n    \"Settings.permissions.conditions.or\": \"АБО\",\n    \"Settings.permissions.conditions.when\": \"Коли\",\n    \"Settings.permissions.select-all-by-permission\": \"Вибрати всі {label} дозволи\",\n    \"Settings.permissions.select-by-permission\": \"Вибрати {label} дозвіл\",\n    \"Settings.permissions.users.active\": \"Активний\",\n    \"Settings.permissions.users.create\": \"запросити користувача\",\n    \"Settings.permissions.users.email\": \"Email\",\n    \"Settings.permissions.users.firstname\": \"Ім'я\",\n    \"Settings.permissions.users.form.sso\": \"Підключіться з SSO\",\n    \"Settings.permissions.users.sso.provider.error\": \"Під час запиту налаштувань SSO сталася помилка\",\n    \"Settings.permissions.users.form.sso.description\": \"Якщо ввімкнено (ON), користувачі можуть увійти через SSO\",\n    \"Settings.permissions.users.inactive\": \"Неактивний\",\n    \"Settings.permissions.users.lastname\": \"Прізвище\",\n    \"Settings.permissions.users.listview.header.subtitle\": \"Усі користувачі, які мають доступ до панелі адміністратора Strapi\",\n    \"Settings.permissions.users.roles\": \"Ролі\",\n    \"Settings.permissions.users.strapi-author\": \"Автор\",\n    \"Settings.permissions.users.strapi-editor\": \"Редактор\",\n    \"Settings.permissions.users.strapi-super-admin\": \"Супер-адміністратор\",\n    \"Settings.permissions.users.tabs.label\": \"Дозволи вкладки\",\n    \"Settings.permissions.users.user-status\": \"Статус користувача\",\n    \"Settings.permissions.users.username\": \"Ім'я користувача\",\n    \"Settings.profile.form.notify.data.loaded\": \"Ваші дані профілю завантажені\",\n    \"Settings.profile.form.section.experience.clear.select\": \"Очистіть вибрану мову інтерфейсу\",\n    \"Settings.profile.form.section.experience.here\": \"тут\",\n    \"Settings.profile.form.section.experience.interfaceLanguage\": \"Мова інтерфейсу\",\n    \"Settings.profile.form.section.experience.interfaceLanguage.hint\": \"Це відображатиме ваш власний інтерфейс обраною мовою.\",\n    \"Settings.profile.form.section.experience.interfaceLanguageHelp\": \"Зміни переваг застосовуватимуться лише до вас. Додаткова інформація доступна {here}.\",\n    \"Settings.profile.form.section.experience.mode.hint\": \"Відображає ваш інтерфейс у вибраному режимі.\",\n    \"Settings.profile.form.section.experience.mode.label\": \"Режим відображення\",\n    \"Settings.profile.form.section.experience.mode.option-label\": \"{name} режим\",\n    \"Settings.profile.form.section.experience.mode.option-system-label\": \"Використовуйте налаштування системи\",\n    \"Settings.profile.form.section.experience.title\": \"Досвід\",\n    \"Settings.profile.form.section.head.title\": \"Профіль користувача\",\n    \"Settings.profile.form.section.profile.page.title\": \"Профіль\",\n    \"Settings.roles.create.description\": \"Визначте права, надані ролі\",\n    \"Settings.roles.create.title\": \"Створити роль\",\n    \"Settings.roles.created\": \"Роль створено\",\n    \"Settings.roles.edit.title\": \"Редагувати роль\",\n    \"Settings.roles.form.button.users-with-role\": \"{number, plural, =0 {# користувачів} one {# користувач} many {# користувачів} other {# користувачі}} з цією роллю\",\n    \"Settings.roles.form.created\": \"Створено\",\n    \"Settings.roles.form.description\": \"Назва та опис ролі\",\n    \"Settings.roles.form.permission.property-label\": \"{label} дозволи\",\n    \"Settings.roles.form.permissions.attributesPermissions\": \"Дозволи на поля\",\n    \"Settings.roles.form.permissions.create\": \"Створити\",\n    \"Settings.roles.form.permissions.delete\": \"Видалити\",\n    \"Settings.roles.form.permissions.publish\": \"Опублікувати\",\n    \"Settings.roles.form.permissions.read\": \"Читати\",\n    \"Settings.roles.form.permissions.update\": \"Оновити\",\n    \"Settings.roles.list.button.add\": \"Додати нову роль\",\n    \"Settings.roles.list.description\": \"Список ролей\",\n    \"Settings.roles.title.singular\": \"роль\",\n    \"Settings.sso.description\": \"Налаштуйте параметри для функції єдиного входу (Single Sign-On).\",\n    \"Settings.sso.form.defaultRole.description\": \"Приєднає нового автентифікованого користувача до вибраної ролі\",\n    \"Settings.sso.form.defaultRole.description-not-allowed\": \"Ви повинні мати дозвіл на читання адміністраторських ролей\",\n    \"Settings.sso.form.defaultRole.label\": \"Роль за замовчуванням\",\n    \"Settings.sso.form.localAuthenticationLock.label\": \"Блокування локальної автентифікації\",\n    \"Settings.sso.form.localAuthenticationLock.description\": \"Виберіть ролі, для яких ви хочете вимкнути локальну автентифікацію\",\n    \"Settings.sso.form.registration.description\": \"Створювати нового користувача при вході через SSO, якщо обліковий запис не існує\",\n    \"Settings.sso.form.registration.label\": \"Автоматичне реєстрування\",\n    \"Settings.sso.title\": \"Єдиний вхід (Single Sign-On)\",\n    \"Settings.sso.not-available\": \"SSO доступний лише як частина платного плану. Оновіть, щоб налаштувати додаткові методи входу та реєстрації для вашої панелі адміністрування.\",\n    \"Settings.tokens.Button.cancel\": \"Скасувати\",\n    \"Settings.tokens.Button.regenerate\": \"Перегенерувати\",\n    \"Settings.tokens.ListView.headers.createdAt\": \"Створено о\",\n    \"Settings.tokens.ListView.headers.description\": \"Опис\",\n    \"Settings.tokens.ListView.headers.lastUsedAt\": \"Останнє використання\",\n    \"Settings.tokens.ListView.headers.name\": \"Назва\",\n    \"Settings.tokens.RegenerateDialog.title\": \"Перегенерувати токен\",\n    \"Settings.tokens.copy.editMessage\": \"З міркувань безпеки ви можете бачити свій токен лише один раз.\",\n    \"Settings.tokens.copy.editTitle\": \"Цей токен більше недоступний.\",\n    \"Settings.tokens.copy.lastWarning\": \"Переконайтеся, що ви скопіювали цей токен, ви не зможете побачити його знову!\",\n    \"Settings.tokens.duration.30-days\": \"30 днів\",\n    \"Settings.tokens.duration.7-days\": \"7 днів\",\n    \"Settings.tokens.duration.90-days\": \"90 днів\",\n    \"Settings.tokens.duration.expiration-date\": \"Дата закінчення\",\n    \"Settings.tokens.duration.unlimited\": \"Необмежено\",\n    \"Settings.tokens.form.description\": \"Опис\",\n    \"Settings.tokens.form.duration\": \"Тривалість токена\",\n    \"Settings.tokens.form.name\": \"Назва\",\n    \"Settings.tokens.form.type\": \"Тип токена\",\n    \"Settings.tokens.notification.copied\": \"Токен скопійовано в буфер обміну.\",\n    \"Settings.tokens.popUpWarning.message\": \"Ви впевнені, що хочете перегенерувати цей токен?\",\n    \"Settings.tokens.regenerate\": \"Перегенерувати\",\n    \"Settings.tokens.types.custom\": \"Користувацький\",\n    \"Settings.tokens.types.full-access\": \"Повний доступ\",\n    \"Settings.tokens.types.read-only\": \"Тільки для читання\",\n    \"Settings.transferTokens.ListView.headers.type\": \"Тип токена\",\n    \"Settings.transferTokens.addFirstToken\": \"Додати ваш перший токен передачі\",\n    \"Settings.transferTokens.addNewToken\": \"Додати новий токен передачі\",\n    \"Settings.transferTokens.create\": \"Створити новий токен передачі\",\n    \"Settings.transferTokens.createPage.title\": \"Створити токен передачі\",\n    \"Settings.transferTokens.description\": \"Список згенерованих токенів передачі\",\n    \"Settings.transferTokens.emptyStateLayout\": \"У вас ще немає жодного вмісту...\",\n    \"Settings.transferTokens.title\": \"Токени передачі\",\n    \"Settings.webhooks.create\": \"Створити вебхук\",\n    \"Settings.webhooks.create.header\": \"Додати новий заголовок\",\n    \"Settings.webhooks.created\": \"Вебхук створено\",\n    \"Settings.webhooks.event.publish-tooltip\": \"Ця подія існує тільки для вмістів з увімкненою системою Чернетка/Публікація\",\n    \"Settings.webhooks.event.select\": \"Вибрати подію\",\n    \"Settings.webhooks.events.isLoading\": \"Події завантажуються\",\n    \"Settings.webhooks.events.create\": \"Створити\",\n    \"Settings.webhooks.events.update\": \"Оновити\",\n    \"Settings.webhooks.events.delete\": \"Видалити вебхук\",\n    \"Settings.webhooks.form.events\": \"Події\",\n    \"Settings.webhooks.form.headers\": \"Заголовки\",\n    \"Settings.webhooks.form.url\": \"URL\",\n    \"Settings.webhooks.headers.remove\": \"Зніміть ряд заголовка {number}\",\n    \"Settings.webhooks.key\": \"Ключ\",\n    \"Settings.webhooks.list.button.add\": \"Додати новий вебхук\",\n    \"Settings.webhooks.list.description\": \"Отримуйте POST-сповіщення про зміни.\",\n    \"Settings.webhooks.list.empty.description\": \"Додайте свій перший вебхук у цей список.\",\n    \"Settings.webhooks.list.empty.link\": \"Переглянути документацію\",\n    \"Settings.webhooks.list.empty.title\": \"Поки що немає вебхуків\",\n    \"Settings.webhooks.list.th.actions\": \"дії\",\n    \"Settings.webhooks.list.th.status\": \"статус\",\n    \"Settings.webhooks.list.loading.success\": \"вебхуки завантажені\",\n    \"Settings.webhooks.singular\": \"вебхук\",\n    \"Settings.webhooks.title\": \"вебхуки\",\n    \"Settings.webhooks.to.delete\": \"{webhooksToDeleteLength, plural, one {# вебхук} other {# вебхуки}} selected\",\n    \"Settings.webhooks.trigger\": \"Тригер\",\n    \"Settings.webhooks.trigger.cancel\": \"Скасувати виклик\",\n    \"Settings.webhooks.trigger.pending\": \"Очікування…\",\n    \"Settings.webhooks.trigger.save\": \"Будь ласка, збережіть тригер\",\n    \"Settings.webhooks.trigger.success\": \"Успіх!\",\n    \"Settings.webhooks.trigger.success.label\": \"Виклик вдався\",\n    \"Settings.webhooks.trigger.test\": \"Випробувальний виклик\",\n    \"Settings.webhooks.trigger.title\": \"Зберегти перед викликом\",\n    \"Settings.webhooks.value\": \"Значення\",\n    \"Settings.webhooks.validation.name.required\": \"Назва є обов'язковою\",\n    \"Settings.webhooks.validation.name.regex\": \"Назва повинна починатися з літери та містити тільки літери, числа, пробіли та підкреслення\",\n    \"Settings.webhooks.validation.url.required\": \"URL є обов'язковим\",\n    \"Settings.webhooks.validation.url.regex\": \"Значення повинно бути дійсним URL\",\n    \"Settings.webhooks.validation.key\": \"Ключ є обов'язковим\",\n    \"Settings.webhooks.validation.value\": \"Значення є обов'язковим\",\n    \"Usecase.back-end\": \"Back-end розробник\",\n    \"Usecase.button.skip\": \"Пропустити це питання\",\n    \"Usecase.content-creator\": \"Контент-кріейтор\",\n    \"Usecase.front-end\": \"Front-end розробник\",\n    \"Usecase.full-stack\": \"Full-stack розробник\",\n    \"Usecase.input.work-type\": \"Який тип роботи ви виконуєте?\",\n    \"Usecase.notification.success.project-created\": \"Проект успішно створено\",\n    \"Usecase.other\": \"Інше\",\n    \"Usecase.title\": \"Розкажіть трохи більше про себе\",\n    Username: Username,\n    \"Users & Permissions\": \"Користувачі і доступи\",\n    Users: Users,\n    \"Users.components.List.empty\": \"Немає користувачів...\",\n    \"Users.components.List.empty.withFilters\": \"Немає користувачів з застосованими фільтрами...\",\n    \"Users.components.List.empty.withSearch\": \"Немає користувачів, що відповідають пошуку ({search})...\",\n    \"admin.pages.MarketPlacePage.sort.label\": \"Сортувати за\",\n    \"admin.pages.MarketPlacePage.filters.categories\": \"Категорії\",\n    \"admin.pages.MarketPlacePage.filters.categoriesSelected\": \"Вибрано {count, plural, =0 {Немає категорій} one {# категорія} many {# категорій}} other {# категорії}}\",\n    \"admin.pages.MarketPlacePage.filters.collections\": \"Колекції\",\n    \"admin.pages.MarketPlacePage.filters.collectionsSelected\": \"Вибрано {count, plural, =0 {Немає Колекцій} one {# Колекція} other {# Колекції}}\",\n    \"admin.pages.MarketPlacePage.head\": \"Маркетплейс — Плаґіни\",\n    \"admin.pages.MarketPlacePage.missingPlugin.description\": \"Скажіть нам, який плаґін ви шукаєте, і ми повідомимо наших розробників плаґінів спільноти, якщо вони шукають натхнення!\",\n    \"admin.pages.MarketPlacePage.missingPlugin.title\": \"Відсутній плаґін?\",\n    \"admin.pages.MarketPlacePage.offline.subtitle\": \"Ви повинні бути підключені до Інтернету, щоб отримати доступ до Маркетплейсу Strapi.\",\n    \"admin.pages.MarketPlacePage.offline.title\": \"Ви офлайн\",\n    \"admin.pages.MarketPlacePage.plugin.copy\": \"Скопіювати команду встановлення\",\n    \"admin.pages.MarketPlacePage.plugin.copy.success\": \"Команда встановлення готова для вставки у ваш термінал\",\n    \"admin.pages.MarketPlacePage.plugin.downloads\": \"Цей плаґін має {downloadsCount} завантажень щотижня\",\n    \"admin.pages.MarketPlacePage.plugin.githubStars\": \"Цей плаґін отримав {starsCount} зірок на GitHub\",\n    \"admin.pages.MarketPlacePage.plugin.info\": \"Дізнатися більше\",\n    \"admin.pages.MarketPlacePage.plugin.info.label\": \"Дізнатися більше про {pluginName}\",\n    \"admin.pages.MarketPlacePage.plugin.info.text\": \"Більше\",\n    \"admin.pages.MarketPlacePage.plugin.installed\": \"Встановлено\",\n    \"admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi\": \"Створено Strapi\",\n    \"admin.pages.MarketPlacePage.plugin.tooltip.verified\": \"Плаґін перевірено Strapi\",\n    \"admin.pages.MarketPlacePage.plugin.version\": \"Оновіть вашу версію Strapi з \\\"{strapiAppVersion}\\\" до: \\\"{versionRange}\\\"\",\n    \"admin.pages.MarketPlacePage.plugin.version.null\": \"Не вдалося перевірити сумісність з вашою версією Strapi: \\\"{strapiAppVersion}\\\"\",\n    \"admin.pages.MarketPlacePage.plugins\": \"Плаґіни\",\n    \"admin.pages.MarketPlacePage.provider.downloads\": \"Цей провайдер має {downloadsCount} завантажень щотижня\",\n    \"admin.pages.MarketPlacePage.provider.githubStars\": \"Цей провайдер отримав {starsCount} зірок на GitHub\",\n    \"admin.pages.MarketPlacePage.providers\": \"Провайдери\",\n    \"admin.pages.MarketPlacePage.search.clear\": \"Очистити пошук\",\n    \"admin.pages.MarketPlacePage.search.empty\": \"Немає результатів для \\\"{target}\\\"\",\n    \"admin.pages.MarketPlacePage.search.placeholder\": \"Пошук\",\n    \"admin.pages.MarketPlacePage.sort.alphabetical\": \"Алфавітний порядок\",\n    \"admin.pages.MarketPlacePage.sort.alphabetical.selected\": \"Сортувати за алфавітним порядком\",\n    \"admin.pages.MarketPlacePage.sort.githubStars\": \"Кількість зірок GitHub\",\n    \"admin.pages.MarketPlacePage.sort.githubStars.selected\": \"Сортувати за зірками GitHub\",\n    \"admin.pages.MarketPlacePage.sort.newest\": \"Найновіші\",\n    \"admin.pages.MarketPlacePage.sort.newest.selected\": \"Сортувати за найновішими\",\n    \"admin.pages.MarketPlacePage.sort.npmDownloads\": \"Кількість завантажень\",\n    \"admin.pages.MarketPlacePage.sort.npmDownloads.selected\": \"Сортувати за завантаженнями npm\",\n    \"admin.pages.MarketPlacePage.submit.plugin.link\": \"Подати плаґін\",\n    \"admin.pages.MarketPlacePage.submit.provider.link\": \"Подати провайдера\",\n    \"admin.pages.MarketPlacePage.subtitle\": \"Отримайте більше від Strapi\",\n    \"admin.pages.MarketPlacePage.tab-group.label\": \"Плаґіни та провайдери для Strapi\",\n    anErrorOccurred: anErrorOccurred,\n    \"app.component.CopyToClipboard.label\": \"Скопіювати в буфер обміну\",\n    \"app.component.search.label\": \"Пошук за {target}\",\n    \"app.component.table.duplicate\": \"Дублювати {target}\",\n    \"app.component.table.edit\": \"Редагувати {target}\",\n    \"app.component.table.read\": \"Читати {target}\",\n    \"app.component.table.select.one-entry\": \"Вибрати {target}\",\n    \"app.component.table.view\": \"Деталі {target}\",\n    \"app.components.BlockLink.blog\": \"Блог\",\n    \"app.components.BlockLink.blog.content\": \"Читайте останні новини про Strapi та екосистему.\",\n    \"app.components.BlockLink.cloud\": \"Strapi Хмара\",\n    \"app.components.BlockLink.cloud.content\": \"Повністю кероване хмарне хостингування для вашого проекту Strapi.\",\n    \"app.components.BlockLink.code\": \"Приклади коду\",\n    \"app.components.BlockLink.code.content\": \"Вчіться, тестуючи реальні проєкти, розроблені спільнотою.\",\n    \"app.components.BlockLink.documentation.content\": \"Відкрийте для себе основні концепції, посібники та інструкції.\",\n    \"app.components.BlockLink.tutorial\": \"Посібники\",\n    \"app.components.BlockLink.tutorial.content\": \"Дотримуйтесь покрокових інструкцій для використання та налаштування Strapi.\",\n    \"app.components.Button.cancel\": \"Скасувати\",\n    \"app.components.Button.confirm\": \"Підтвердити\",\n    \"app.components.Button.reset\": \"Скинути\",\n    \"app.components.ComingSoonPage.comingSoon\": \"Незабаром\",\n    \"app.components.ConfirmDialog.title\": \"Підтвердження\",\n    \"app.components.DownloadInfo.download\": \"Завантажується...\",\n    \"app.components.DownloadInfo.text\": \"Це може зайняти хвилинку. Дякуємо за терпіння.\",\n    \"app.components.EmptyAttributes.title\": \"Поки немає полей\",\n    \"app.components.EmptyStateLayout.content-document\": \"Не знайдено вміст\",\n    \"app.components.EmptyStateLayout.content-permissions\": \"У вас немає дозволів на доступ до цього вмісту\",\n    \"app.components.GuidedTour.CM.create.content\": \"<p>Створюйте та керуйте всіма вмістами тут у Менеджері вмісту.</p><p>Наприклад, продовжуючи приклад блогу, ви можете написати статтю, зберегти її та опублікувати, як вам подобається.</p><p>💡 Порада — не забудьте натиснути «Опублікувати» на створеному вмісті.</p>\",\n    \"app.components.GuidedTour.CM.create.title\": \"⚡️ Створити вміст\",\n    \"app.components.GuidedTour.CM.success.content\": \"<p>Чудово, залишився останній крок!</p><b>🚀 Перегляньте вміст в дії</b>\",\n    \"app.components.GuidedTour.CM.success.cta.title\": \"Тестувати API\",\n    \"app.components.GuidedTour.CM.success.title\": \"Крок 2: Завершено ✅\",\n    \"app.components.GuidedTour.CTB.create.content\": \"<p>Типи Колекцій допомагають керувати кількома записами, а Типи Одиниць підходять для керування лише одним записом.</p> <p>Наприклад, для сайту блогу статті будуть типом колекції, тоді як головна сторінка буде типом одиниці.</p>\",\n    \"app.components.GuidedTour.CTB.create.cta.title\": \"Створити Тип Колекції\",\n    \"app.components.GuidedTour.CTB.create.title\": \"🧠 Створіть перший Тип Колекції\",\n    \"app.components.GuidedTour.CTB.success.content\": \"<p>Добре йде!</p><b>⚡️ Чим би ви хотіли поділитися зі світом?</b>\",\n    \"app.components.GuidedTour.CTB.success.title\": \"Крок 1: Завершено ✅\",\n    \"app.components.GuidedTour.apiTokens.create.content\": \"<p>Згенеруйте токен автентифікації тут та отримайте вміст, який ви щойно створили.</p>\",\n    \"app.components.GuidedTour.apiTokens.create.cta.title\": \"Згенерувати API токен\",\n    \"app.components.GuidedTour.apiTokens.create.title\": \"🚀 Перегляньте вміст в дії\",\n    \"app.components.GuidedTour.apiTokens.success.content\": \"<p>Перегляньте вміст в дії, зробивши HTTP-запит:</p><ul><li><p>На цю URL: <light>https://'<'YOUR_DOMAIN'>'/api/'<'YOUR_CT'>'</light></p></li><li><p>З заголовком (header): <light>Authorization: bearer '<'YOUR_API_TOKEN'>'</light></p></li></ul><p>Щоб дізнатись більше способів взаємодії з вмістом, перегляньте <documentationLink>документацію</documentationLink>.</p>\",\n    \"app.components.GuidedTour.apiTokens.success.cta.title\": \"Повернутися на головну сторінку\",\n    \"app.components.GuidedTour.apiTokens.success.title\": \"Крок 3: Завершено ✅\",\n    \"app.components.GuidedTour.create-content\": \"Створити вміст\",\n    \"app.components.GuidedTour.home.CM.title\": \"⚡️ Чим би ви хотіли поділитися зі світом?\",\n    \"app.components.GuidedTour.home.CTB.cta.title\": \"Перейти до Конструктора типів вмісту\",\n    \"app.components.GuidedTour.home.CTB.title\": \"🧠 Побудуйте структуру вмісту\",\n    \"app.components.GuidedTour.home.apiTokens.cta.title\": \"Тестувати API\",\n    \"app.components.GuidedTour.skip\": \"Пропустити тур\",\n    \"app.components.GuidedTour.title\": \"3 кроки, щоб почати\",\n    \"app.components.HomePage.button.blog\": \"Дивіться більше у блозі\",\n    \"app.components.HomePage.community\": \"Знайдіть спільноту в інтернеті\",\n    \"app.components.HomePage.community.content\": \"Обговорюйте з членами команди, контрибʼюторами та розробниками через різні канали зв'язку.\",\n    \"app.components.HomePage.create\": \"Створити свій перший Тип Вмісту\",\n    \"app.components.HomePage.roadmap\": \"Переглянути нашу дорожню карту\",\n    \"app.components.HomePage.welcome\": \"Вітаємо на борту!\",\n    \"app.components.HomePage.welcome.again\": \"Вітаємо \",\n    \"app.components.HomePage.welcomeBlock.content\": \"Ми дуже раді, що ви приєдналися до нашої спільноти. Нам завжди потрібен зворотній зв'язок, тому не вагайтесь писати нам на \",\n    \"app.components.HomePage.welcomeBlock.content.again\": \"Ми сподіваємось, що ви робите успіхи у вашому проекті... Слідкуйте за останніми новинами Strapi. Ми робимо все можливе, щоб покращити продукт завдяки вашим відгукам.\",\n    \"app.components.HomePage.welcomeBlock.content.issues\": \"проблеми.\",\n    \"app.components.HomePage.welcomeBlock.content.raise\": \" або повідомте про \",\n    \"app.components.ImgPreview.hint\": \"Перетягніть ваш файл в цю область або {browse} файл для завантаження\",\n    \"app.components.ImgPreview.hint.browse\": \"оберіть\",\n    \"app.components.InputFile.newFile\": \"Додати новий файл\",\n    \"app.components.InputFileDetails.open\": \"Відкрити в новій вкладці\",\n    \"app.components.InputFileDetails.originalName\": \"Оригінальна назва:\",\n    \"app.components.InputFileDetails.remove\": \"Видалити цей файл\",\n    \"app.components.InputFileDetails.size\": \"Розмір:\",\n    \"app.components.InstallPluginPage.Download.description\": \"Завантаження та встановлення плаґіна може зайняти кілька секунд.\",\n    \"app.components.InstallPluginPage.Download.title\": \"Завантаження...\",\n    \"app.components.InstallPluginPage.description\": \"Розширюйте свій проект без зусиль.\",\n    \"app.components.LeftMenu.collapse\": \"Згорнути навігаційну панель\",\n    \"app.components.LeftMenu.expand\": \"Розгорнути навігаційну панель\",\n    \"app.components.LeftMenu.general\": \"Загальні\",\n    \"app.components.LeftMenu.logo.alt\": \"Логотип застосунку\",\n    \"app.components.LeftMenu.logout\": \"Вийти\",\n    \"app.components.LeftMenu.navbrand.title\": \"Панель управління Strapi\",\n    \"app.components.LeftMenu.navbrand.workplace\": \"Робоче місце\",\n    \"app.components.LeftMenu.plugins\": \"Плаґіни\",\n    \"app.components.LeftMenu.trialCountdown\": \"Ваш пробний період закінчується {date}.\",\n    \"app.components.LeftMenuFooter.help\": \"Допомога\",\n    \"app.components.LeftMenuFooter.poweredBy\": \"Працює на \",\n    \"app.components.LeftMenuLinkContainer.collectionTypes\": \"Типи Колекцій\",\n    \"app.components.LeftMenuLinkContainer.configuration\": \"Налаштування\",\n    \"app.components.LeftMenuLinkContainer.general\": \"Загальні\",\n    \"app.components.LeftMenuLinkContainer.noPluginsInstalled\": \"Немає встановлених плаґінів\",\n    \"app.components.LeftMenuLinkContainer.plugins\": \"Плаґін\",\n    \"app.components.LeftMenuLinkContainer.singleTypes\": \"Типи Одиниць\",\n    \"app.components.ListPluginsPage.deletePlugin.description\": \"Видалення плаґіна може зайняти кілька секунд.\",\n    \"app.components.ListPluginsPage.deletePlugin.title\": \"Видалення\",\n    \"app.components.ListPluginsPage.description\": \"Список встановленних плаґінів у проекті.\",\n    \"app.components.ListPluginsPage.head.title\": \"Список плаґінів\",\n    \"app.components.Logout.logout\": \"Вийти\",\n    \"app.components.Logout.profile\": \"Профіль\",\n    \"app.components.MarketplaceBanner\": \"Відкрийте для себе плаґіни, створені спільнотою, та багато інших чудових речей, щоб розпочати ваш проект на Маркетплейсі Strapi.\",\n    \"app.components.MarketplaceBanner.image.alt\": \"Логотип ракети Strapi\",\n    \"app.components.MarketplaceBanner.link\": \"Перевірте зараз\",\n    \"app.components.NotFoundPage.back\": \"Повернутися на головну\",\n    \"app.components.NotFoundPage.description\": \"Не знайдено\",\n    \"app.components.NpsSurvey.banner-title\": \"Наскільки ймовірно, що ви порекомендуєте Strapi своєму другу або колезі?\",\n    \"app.components.NpsSurvey.feedback-response\": \"Дуже дякуємо за ваш відгук!\",\n    \"app.components.NpsSurvey.feedback-question\": \"Чи маєте ви пропозиції щодо покращення?\",\n    \"app.components.NpsSurvey.submit-feedback\": \"Надіслати відгук\",\n    \"app.components.NpsSurvey.dismiss-survey-label\": \"Закрити опитування\",\n    \"app.components.NpsSurvey.no-recommendation\": \"Зовсім малоймовірно\",\n    \"app.components.NpsSurvey.happy-to-recommend\": \"Надзвичайно ймовірно\",\n    \"app.components.Official\": \"Офіційний\",\n    \"app.components.Onboarding.help.button\": \"Кнопка допомоги\",\n    \"app.components.Onboarding.label.completed\": \"% завершено\",\n    \"app.components.Onboarding.link.build-content\": \"Побудуйте архітектуру вмісту\",\n    \"app.components.Onboarding.link.manage-content\": \"Додати та керувати вмістом\",\n    \"app.components.Onboarding.link.manage-media\": \"Керувати медіа\",\n    \"app.components.Onboarding.link.more-videos\": \"Дивитися більше відео\",\n    \"app.components.Onboarding.title\": \"Вступні відео\",\n    \"app.components.PluginCard.Button.label.download\": \"Завантажити\",\n    \"app.components.PluginCard.Button.label.install\": \"Вже встановлено\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed\": \"Функція autoReload має буте включена. Будь ласка, запустіть свій додаток використовуючи `yarn develop`.\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.confirm\": \"Я розумію!\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.environment\": \"З міркувань безпеки плаґін можна завантажити тільки в середовищі розробки.\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.title\": \"Завантаження неможливе\",\n    \"app.components.PluginCard.compatible\": \"Сумісно з вашим додатком\",\n    \"app.components.PluginCard.compatibleCommunity\": \"Сумісно зі спільнотою\",\n    \"app.components.PluginCard.more-details\": \"Докладніше\",\n    \"app.components.ToggleCheckbox.off-label\": \"Ні\",\n    \"app.components.ToggleCheckbox.on-label\": \"Так\",\n    \"app.components.Users.MagicLink.connect\": \"Скопіюйте та поділіться цим посиланням, щоб надати доступ цьому користувачу\",\n    \"app.components.Users.MagicLink.connect.sso\": \"Надішліть це посилання користувачу, перший вхід може бути здійснений через постачальника SSO\",\n    \"app.components.Users.ModalCreateBody.block-title.details\": \"Деталі користувача\",\n    \"app.components.Users.ModalCreateBody.block-title.roles\": \"Ролі користувача\",\n    \"app.components.Users.ModalCreateBody.block-title.roles.description\": \"Користувач може мати одну або декілька ролей\",\n    \"app.components.Users.SortPicker.button-label\": \"Сортувати за\",\n    \"app.components.Users.SortPicker.sortby.email_asc\": \"Електронна пошта (А до Я)\",\n    \"app.components.Users.SortPicker.sortby.email_desc\": \"Електронна пошта (Я до А)\",\n    \"app.components.Users.SortPicker.sortby.firstname_asc\": \"Ім'я (А до Я)\",\n    \"app.components.Users.SortPicker.sortby.firstname_desc\": \"Ім'я (Я до А)\",\n    \"app.components.Users.SortPicker.sortby.lastname_asc\": \"Прізвище (А до Я)\",\n    \"app.components.Users.SortPicker.sortby.lastname_desc\": \"Прізвище (Я до А)\",\n    \"app.components.Users.SortPicker.sortby.username_asc\": \"Ім'я користувача (А до Я)\",\n    \"app.components.Users.SortPicker.sortby.username_desc\": \"Ім'я користувача (Я до А)\",\n    \"app.components.listPlugins.button\": \"Додати новий плаґін\",\n    \"app.components.listPlugins.title.none\": \"Немає встановлених плаґінів\",\n    \"app.components.listPluginsPage.deletePlugin.error\": \"Під час видалення плаґіну сталася помилка\",\n    \"app.containers.App.notification.error.init\": \"Сталася помилка під час виклику API\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin\": \"Якщо ви не отримали це посилання, будь ласка, зверніться до вашого адміністратора.\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.text.email\": \"Це може зайняти декілька хвилин, щоб отримати посилання для відновлення пароля.\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.title\": \"Електронна пошта відправлена\",\n    \"app.containers.Users.EditPage.form.active.label\": \"Активний\",\n    \"app.containers.Users.EditPage.header.label\": \"Редагувати {name}\",\n    \"app.containers.Users.EditPage.header.label-loading\": \"Редагувати користувача\",\n    \"app.containers.Users.EditPage.roles-bloc-title\": \"Призначені ролі\",\n    \"app.containers.Users.ModalForm.footer.button-success\": \"Запросити користувача\",\n    \"app.links.configure-view\": \"Налаштувати вигляд\",\n    \"app.page.not.found\": \"Ой! Ми не можемо знайти сторінку, яку ви шукаєте...\",\n    \"app.static.links.cheatsheet\": \"Шпаргалка\",\n    \"app.utils.SelectOption.defaultMessage\": \" \",\n    \"app.utils.add-filter\": \"Додати фільтр\",\n    \"app.utils.close-label\": \"Закрити\",\n    \"app.utils.defaultMessage\": \" \",\n    \"app.utils.delete\": \"Видалити\",\n    \"app.utils.duplicate\": \"Дублювати\",\n    \"app.utils.edit\": \"Редагувати\",\n    \"app.utils.errors.file-too-big.message\": \"Файл занадто великий\",\n    \"app.utils.filter-value\": \"Значення фільтра\",\n    \"app.utils.filters\": \"Фільтри\",\n    \"app.utils.notify.data-loaded\": \"{target} завантажено\",\n    \"app.utils.placeholder.defaultMessage\": \" \",\n    \"app.utils.publish\": \"Опублікувати\",\n    \"app.utils.refresh\": \"Оновити\",\n    \"app.utils.select-all\": \"Вибрати все\",\n    \"app.utils.select-field\": \"Вибрати поле\",\n    \"app.utils.select-filter\": \"Вибрати фільтр\",\n    \"app.utils.unpublish\": \"Відмінити публікацію\",\n    \"app.utils.published\": \"Опубліковано\",\n    \"app.utils.ready-to-publish\": \"Готово до публікації\",\n    \"app.utils.ready-to-publish-changes\": \"Готово до публікації змін\",\n    \"app.utils.ready-to-unpublish-changes\": \"Готово до відміни публікації\",\n    \"app.confirm.body\": \"Ви впевнені?\",\n    clearLabel: clearLabel,\n    \"coming.soon\": \"Цей вміст наразі на стадії розробки і повернеться через кілька тижнів!\",\n    \"component.Input.error.validation.integer\": \"Значення має бути цілим числом\",\n    \"components.AutoReloadBlocker.description\": \"Запустіть Strapi однією з наступних команд:\",\n    \"components.AutoReloadBlocker.header\": \"Для цього плаґіну необхідна функція перезапуску (reload).\",\n    \"components.ErrorBoundary.title\": \"Щось пішло не так...\",\n    \"components.FilterOptions.FILTER_TYPES.$contains\": \"містить\",\n    \"components.FilterOptions.FILTER_TYPES.$containsi\": \"містить (без урахування регістру)\",\n    \"components.FilterOptions.FILTER_TYPES.$endsWith\": \"закінчується на\",\n    \"components.FilterOptions.FILTER_TYPES.$endsWithi\": \"закінчується на (без урахування регістру)\",\n    \"components.FilterOptions.FILTER_TYPES.$eq\": \"дорівнює\",\n    \"components.FilterOptions.FILTER_TYPES.$eqi\": \"дорівнює (без урахування регістру)\",\n    \"components.FilterOptions.FILTER_TYPES.$gt\": \"більше ніж\",\n    \"components.FilterOptions.FILTER_TYPES.$gte\": \"більше або дорівнює\",\n    \"components.FilterOptions.FILTER_TYPES.$lt\": \"менше ніж\",\n    \"components.FilterOptions.FILTER_TYPES.$lte\": \"менше або дорівнює\",\n    \"components.FilterOptions.FILTER_TYPES.$ne\": \"не дорівнює\",\n    \"components.FilterOptions.FILTER_TYPES.$nei\": \"не дорівнює (без урахування регістру)\",\n    \"components.FilterOptions.FILTER_TYPES.$notContains\": \"не містить\",\n    \"components.FilterOptions.FILTER_TYPES.$notContainsi\": \"не містить (без урахування регістру)\",\n    \"components.FilterOptions.FILTER_TYPES.$notNull\": \"не є порожнім\",\n    \"components.FilterOptions.FILTER_TYPES.$null\": \"є порожнім\",\n    \"components.FilterOptions.FILTER_TYPES.$startsWith\": \"починається з\",\n    \"components.FilterOptions.FILTER_TYPES.$startsWithi\": \"починається з (без урахування регістру)\",\n    \"components.Input.error.attribute.key.taken\": \"Це значення вже існує\",\n    \"components.Input.error.attribute.sameKeyAndName\": \"Не може співпадати\",\n    \"components.Input.error.attribute.taken\": \"Поле з такою назвою вже існує\",\n    \"components.Input.error.contain.lowercase\": \"Пароль повинен містити принаймні одну малу літеру\",\n    \"components.Input.error.contain.number\": \"Пароль повинен містити принаймні одну цифру\",\n    \"components.Input.error.contain.uppercase\": \"Пароль повинен містити принаймні одну велику літеру\",\n    \"components.Input.error.contentTypeName.taken\": \"Ця назва вже існує\",\n    \"components.Input.error.custom-error\": \"{errorMessage} \",\n    \"components.Input.error.password.noMatch\": \"Паролі не співпадають.\",\n    \"components.Input.error.validation.email\": \"Це не адреса електронної пошти\",\n    \"components.Input.error.validation.json\": \"Це не відпоідає формату JSON\",\n    \"components.Input.error.validation.lowercase\": \"Це має бути рядком з малими літерами\",\n    \"components.Input.error.validation.max\": \"Значення занадто велике {max}.\",\n    \"components.Input.error.validation.maxLength\": \"Значення занадто довге {max}.\",\n    \"components.Input.error.validation.min\": \"Значення занадто мале {min}.\",\n    \"components.Input.error.validation.minLength\": \"Значення занадто коротке {min}.\",\n    \"components.Input.error.validation.minSupMax\": \"Не може бути більше\",\n    \"components.Input.error.validation.regex\": \"Значення не відповідає регулярному виразу.\",\n    \"components.Input.error.validation.string\": \"Це має бути рядок.\",\n    \"components.Input.error.validation.required\": \"Це обов'язкове поле.\",\n    \"components.Input.error.validation.unique\": \"Це значення вже використовується.\",\n    \"components.Input.error.validation.email.withField\": \"{field} є недійсною електронною поштою\",\n    \"components.Input.error.validation.json.withField\": \"Значення {field} не відповідає формату JSON\",\n    \"components.Input.error.validation.lowercase.withField\": \"{field} повинен бути рядком з малими літерами\",\n    \"components.Input.error.validation.max.withField\": \"Значення {field} надто велике.\",\n    \"components.Input.error.validation.maxLength.withField\": \"Значення {field} надто довге.\",\n    \"components.Input.error.validation.min.withField\": \"Значення {field} надто мале.\",\n    \"components.Input.error.validation.minLength.withField\": \"Значення {field} надто коротке.\",\n    \"components.Input.error.validation.minSupMax.withField\": \"Значення {field} не може бути більшим\",\n    \"components.Input.error.validation.regex.withField\": \"Значення {field} не відповідає регулярному виразу.\",\n    \"components.Input.error.validation.required.withField\": \"Значення {field} є обов'язковим.\",\n    \"components.Input.error.validation.unique.withField\": \"{field} вже використовується.\",\n    \"components.InputSelect.option.placeholder\": \"Виберіть тут\",\n    \"components.ListRow.empty\": \"Немає даних для відображення.\",\n    \"components.NotAllowedInput.text\": \"Ви не маєте дозволу на редагування цього поля.\",\n    \"components.OverlayBlocker.description\": \"Ви скористалися функціоналом, яки потребує перезавантаження серверу. Будь ласка зачекайте поки сервер запускається.\",\n    \"components.OverlayBlocker.description.serverError\": \"Сервер мав перезавантажитись, будь ласка, перевірте свої журнали у терміналі.\",\n    \"components.OverlayBlocker.title\": \"Чекаємо на перезавантаження...\",\n    \"components.OverlayBlocker.title.serverError\": \"Перезавантаження триває довше ніж очікувалось.\",\n    \"components.PageFooter.select\": \"записів на сторінці\",\n    \"components.ProductionBlocker.description\": \"З міркувань безпеки ми маємо відключити цей плаґін в інших середовищах.\",\n    \"components.ProductionBlocker.header\": \"Цей плаґін доступний лише в середовищі розробки.\",\n    \"components.ViewSettings.tooltip\": \"Налаштування перегляду\",\n    \"components.TableHeader.sort\": \"Сортувати за {label}\",\n    \"components.Blocks.modifiers.bold\": \"Жирний\",\n    \"components.Blocks.modifiers.italic\": \"Курсив\",\n    \"components.Blocks.modifiers.underline\": \"Підкреслений\",\n    \"components.Blocks.modifiers.strikethrough\": \"Перекреслений\",\n    \"components.Blocks.modifiers.code\": \"Вбудований код\",\n    \"components.Blocks.link\": \"Посилання\",\n    \"components.Blocks.expand\": \"Розгорнути\",\n    \"components.Blocks.collapse\": \"Згорнути\",\n    \"components.Blocks.popover.text\": \"Текст\",\n    \"components.Blocks.popover.text.placeholder\": \"Введіть текст посилання\",\n    \"components.Blocks.popover.link\": \"Посилання\",\n    \"components.Blocks.popover.link.placeholder\": \"Вставте посилання\",\n    \"components.Blocks.popover.link.error\": \"Будь ласка, введіть дійсне посилання\",\n    \"components.Blocks.popover.save\": \"Зберегти\",\n    \"components.Blocks.popover.cancel\": \"Скасувати\",\n    \"components.Blocks.popover.remove\": \"Видалити\",\n    \"components.Blocks.popover.edit\": \"Редагувати\",\n    \"components.Blocks.blocks.selectBlock\": \"Виберіть блок\",\n    \"components.Blocks.blocks.text\": \"Текст\",\n    \"components.Blocks.blocks.heading1\": \"Заголовок 1\",\n    \"components.Blocks.blocks.heading2\": \"Заголовок 2\",\n    \"components.Blocks.blocks.heading3\": \"Заголовок 3\",\n    \"components.Blocks.blocks.heading4\": \"Заголовок 4\",\n    \"components.Blocks.blocks.heading5\": \"Заголовок 5\",\n    \"components.Blocks.blocks.heading6\": \"Заголовок 6\",\n    \"components.Blocks.blocks.code\": \"Блок коду\",\n    \"components.Blocks.blocks.quote\": \"Цитата\",\n    \"components.Blocks.blocks.image\": \"Зображення\",\n    \"components.Blocks.blocks.unorderedList\": \"Маркерований список\",\n    \"components.Blocks.blocks.orderedList\": \"Нумерований список\",\n    \"components.Blocks.blocks.code.languageLabel\": \"Виберіть мову\",\n    \"components.Blocks.dnd.instruction\": \"Щоб змінити порядок блоків, натисніть Command або Control разом із Shift і клавішами зі стрілками вгору або вниз\",\n    \"components.Blocks.dnd.reorder\": \"{item}, переміщено. Нова позиція в редакторі: {position}.\",\n    \"components.Wysiwyg.ToggleMode.markdown-mode\": \"Режим Markdown\",\n    \"components.Wysiwyg.ToggleMode.preview-mode\": \"Режим перегляду\",\n    \"components.Wysiwyg.collapse\": \"Згорнути\",\n    \"components.Wysiwyg.selectOptions.H1\": \"Заголовок H1\",\n    \"components.Wysiwyg.selectOptions.H2\": \"Заголовок H2\",\n    \"components.Wysiwyg.selectOptions.H3\": \"Заголовок H3\",\n    \"components.Wysiwyg.selectOptions.H4\": \"Заголовок H4\",\n    \"components.Wysiwyg.selectOptions.H5\": \"Заголовок H5\",\n    \"components.Wysiwyg.selectOptions.H6\": \"Заголовок H6\",\n    \"components.Wysiwyg.selectOptions.title\": \"Додати заголовок\",\n    \"components.WysiwygBottomControls.charactersIndicators\": \"символів\",\n    \"components.WysiwygBottomControls.fullscreen\": \"Розгорнути\",\n    \"components.WysiwygBottomControls.uploadFiles\": \"Перетягніть файли сюди, вставте з буфера обміну або {browse}.\",\n    \"components.WysiwygBottomControls.uploadFiles.browse\": \"обрати їх\",\n    \"components.pagination.go-to\": \"Перейти на сторінку {page}\",\n    \"components.pagination.go-to-next\": \"Перейти на наступну сторінку\",\n    \"components.pagination.go-to-previous\": \"Перейти на попередню сторінку\",\n    \"components.pagination.remaining-links\": \"І ще {number} посилань\",\n    \"components.popUpWarning.button.cancel\": \"Ні, скасувати\",\n    \"components.popUpWarning.button.confirm\": \"Так, підтвердити\",\n    \"components.popUpWarning.message\": \"Ви впевнені, що хочете це видалити?\",\n    \"components.popUpWarning.title\": \"Підтвердіть, будь ласка\",\n    dark: dark,\n    \"form.button.continue\": \"Продовжити\",\n    \"form.button.done\": \"Готово\",\n    \"global.actions\": \"Дії\",\n    \"global.auditLogs\": \"Аудиторські журнали\",\n    \"global.back\": \"Назад\",\n    \"global.cancel\": \"Скасувати\",\n    \"global.change-password\": \"Змінити пароль\",\n    \"global.close\": \"Закрити\",\n    \"global.content-manager\": \"Менеджер вмісту\",\n    \"global.home\": \"Головна\",\n    \"global.continue\": \"Продовжити\",\n    \"global.delete\": \"Видалити\",\n    \"global.delete-target\": \"Видалити {target}\",\n    \"global.description\": \"Опис\",\n    \"global.details\": \"Деталі\",\n    \"global.disabled\": \"Вимкнено\",\n    \"global.documentation\": \"Документація\",\n    \"global.enabled\": \"Увімкнено\",\n    \"global.finish\": \"Завершити\",\n    \"global.marketplace\": \"Маркетплейс\",\n    \"global.name\": \"Назва\",\n    \"global.new\": \"Новий\",\n    \"global.none\": \"Немає\",\n    \"global.password\": \"Пароль\",\n    \"global.plugins\": \"Плаґіни\",\n    \"global.plugins.content-manager\": \"Менеджер вмісту\",\n    \"global.plugins.content-manager.description\": \"Швидкий спосіб перегляду, редагування та видалення даних у вашій базі даних.\",\n    \"global.plugins.content-type-builder\": \"Конструктор типів вмісту\",\n    \"global.plugins.content-type-builder.description\": \"Моделюйте структуру даних вашого API. Створюйте нові поля та зв’язки лише за хвилину. Файли автоматично створюються та оновлюються у вашому проекті.\",\n    \"global.plugins.documentation\": \"Документація\",\n    \"global.plugins.documentation.description\": \"Створіть документ OpenAPI та візуалізуйте ваш API за допомогою інтерфейсу SWAGGER UI.\",\n    \"global.plugins.email\": \"Електронна пошта\",\n    \"global.plugins.email.description\": \"Налаштуйте ваш застосунок для відправки електронних листів.\",\n    \"global.plugins.graphql\": \"GraphQL\",\n    \"global.plugins.graphql.description\": \"Додає кінцеву точку GraphQL з методами API за замовчуванням.\",\n    \"global.plugins.i18n\": \"Інтернаціоналізація\",\n    \"global.plugins.i18n.description\": \"Цей плаґін дозволяє створювати, читати та оновлювати вміст на різних мовах як з панелі адміністрування, так і через API.\",\n    \"global.plugins.sentry\": \"Sentry\",\n    \"global.plugins.sentry.description\": \"Відправляйте події помилок Strapi до Sentry.\",\n    \"global.plugins.upload\": \"Медіабібліотека\",\n    \"global.plugins.upload.description\": \"Керування медіа-файлами.\",\n    \"global.plugins.users-permissions\": \"Ролі та дозволи\",\n    \"global.plugins.users-permissions.description\": \"Захистіть ваш API повним процесом автентифікації на основі JWT. Цей плаґін також має стратегію ACL, що дозволяє керувати дозволами між групами користувачів.\",\n    \"global.profile\": \"Профіль\",\n    \"global.prompt.unsaved\": \"Ви впевнені, що хочете залишити цю сторінку? Всі ваші зміни будуть втрачені\",\n    \"global.reset-password\": \"Скинути пароль\",\n    \"global.roles\": \"Ролі\",\n    \"global.save\": \"Зберегти\",\n    \"global.search\": \"Пошук\",\n    \"global.see-more\": \"Дивитися більше\",\n    \"global.select\": \"Вибрати\",\n    \"global.select-all-entries\": \"Вибрати всі записи\",\n    \"global.settings\": \"Налаштування\",\n    \"global.type\": \"Тип\",\n    \"global.users\": \"Користувачі\",\n    \"global.fullname\": \"{firstname} {lastname}\",\n    \"global.learn-more\": \"Дізнатися більше\",\n    light: light,\n    \"notification.contentType.relations.conflict\": \"Тип вмісту має конфлікт зв'язків\",\n    \"notification.default.title\": \"Інформація:\",\n    \"notification.ee.warning.at-seat-limit.title\": \"{licenseLimitStatus, select, OVER_LIMIT {Перевищено} AT_LIMIT {Досягнуто}} ліміт місць ({currentUserCount}/{permittedSeats})\",\n    \"notification.ee.warning.over-.message\": \"Додайте місця для {licenseLimitStatus, select, OVER_LIMIT {залучення} AT_LIMIT {повторної активації}} користувачів. Якщо ви вже це зробили, але зміни ще не відображаються в Strapi, переконайтеся, що ви перезапустили ваш застосунок.\",\n    \"notification.error\": \"Сталася помилка\",\n    \"notification.error.invalid.configuration\": \"У вас неправильна конфігурація, перевірте журнал сервера для отримання додаткової інформації.\",\n    \"notification.error.layout\": \"Не вдалося отримати макет\",\n    \"notification.error.tokennamenotunique\": \"Назва вже призначена іншому токену\",\n    \"notification.form.error.fields\": \"Форма містить деякі помилки\",\n    \"notification.form.success.fields\": \"Зміни збережено\",\n    \"notification.link-copied\": \"Посилання скопійовано в буфер обміну\",\n    \"notification.permission.not-allowed-read\": \"Вам не дозволено переглядати цей документ\",\n    \"notification.success.apitokencreated\": \"API токен успішно створено\",\n    \"notification.success.apitokenedited\": \"API токен успішно оновлено\",\n    \"notification.success.delete\": \"Цей елемент був видалений\",\n    \"notification.success.saved\": \"Зміни збережено\",\n    \"notification.success.title\": \"Успіх:\",\n    \"notification.success.transfertokencreated\": \"Токен передачі успішно створено\",\n    \"notification.success.transfertokenedited\": \"Токен передачі успішно оновлено\",\n    \"notification.version.update.message\": \"Доступна нова версія Strapi!\",\n    \"notification.warning.404\": \"404 - Не знайдено\",\n    \"notification.warning.title\": \"Увага:\",\n    or: or,\n    \"request.error.model.unknown\": \"Цієї моделі данних не існує\",\n    selectButtonTitle: selectButtonTitle,\n    skipToContent: skipToContent,\n    submit: submit,\n    \"components.Search.placeholder\": \"Пошук...\"\n};\n\nexport { Analytics, Documentation, Email, Password, Provider, ResetPasswordToken, Role, Username, Users, anErrorOccurred, clearLabel, dark, uk as default, light, or, selectButtonTitle, skipToContent, submit };\n//# sourceMappingURL=uk.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,YAAY;AAChB,IAAI,gBAAgB;AACpB,IAAI,QAAQ;AACZ,IAAI,WAAW;AACf,IAAI,WAAW;AACf,IAAI,qBAAqB;AACzB,IAAI,OAAO;AACX,IAAI,WAAW;AACf,IAAI,QAAQ;AACZ,IAAI,kBAAkB;AACtB,IAAI,aAAa;AACjB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,KAAK;AACT,IAAI,oBAAoB;AACxB,IAAI,gBAAgB;AACpB,IAAI,SAAS;AACb,IAAI,KAAK;AAAA,EACL;AAAA,EACA,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0CAA0C;AAAA,EAC1C,iCAAiC;AAAA,EACjC,sCAAsC;AAAA,EACtC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,iDAAiD;AAAA,EACjD,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,2BAA2B;AAAA,EAC3B,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA,EAChB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,6BAA6B;AAAA,EAC7B,qCAAqC;AAAA,EACrC,0CAA0C;AAAA,EAC1C,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,4BAA4B;AAAA,EAC5B,4BAA4B;AAAA,EAC5B,0CAA0C;AAAA,EAC1C,sBAAsB;AAAA,EACtB,iDAAiD;AAAA,EACjD,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,kDAAkD;AAAA,EAClD,yDAAyD;AAAA,EACzD,yDAAyD;AAAA,EACzD,0DAA0D;AAAA,EAC1D,mDAAmD;AAAA,EACnD,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,uCAAuC;AAAA,EACvC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,sCAAsC;AAAA,EACtC,8DAA8D;AAAA,EAC9D,oDAAoD;AAAA,EACpD,2DAA2D;AAAA,EAC3D,+DAA+D;AAAA,EAC/D,6DAA6D;AAAA,EAC7D,+DAA+D;AAAA,EAC/D,4DAA4D;AAAA,EAC5D,qDAAqD;AAAA,EACrD,8DAA8D;AAAA,EAC9D,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,+DAA+D;AAAA,EAC/D,mEAAmE;AAAA,EACnE,6DAA6D;AAAA,EAC7D,0DAA0D;AAAA,EAC1D,2DAA2D;AAAA,EAC3D,sDAAsD;AAAA,EACtD,mDAAmD;AAAA,EACnD,8DAA8D;AAAA,EAC9D,6DAA6D;AAAA,EAC7D,gEAAgE;AAAA,EAChE,iEAAiE;AAAA,EACjE,8DAA8D;AAAA,EAC9D,mEAAmE;AAAA,EACnE,iEAAiE;AAAA,EACjE,4DAA4D;AAAA,EAC5D,wEAAwE;AAAA,EACxE,wDAAwD;AAAA,EACxD,mDAAmD;AAAA,EACnD,oCAAoC;AAAA,EACpC,sCAAsC;AAAA,EACtC,iCAAiC;AAAA,EACjC,iDAAiD;AAAA,EACjD,+CAA+C;AAAA,EAC/C,wDAAwD;AAAA,EACxD,6CAA6C;AAAA,EAC7C,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,sCAAsC;AAAA,EACtC,8BAA8B;AAAA,EAC9B,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,+CAA+C;AAAA,EAC/C,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,sDAAsD;AAAA,EACtD,sDAAsD;AAAA,EACtD,sDAAsD;AAAA,EACtD,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,+CAA+C;AAAA,EAC/C,8DAA8D;AAAA,EAC9D,2DAA2D;AAAA,EAC3D,gDAAgD;AAAA,EAChD,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,0CAA0C;AAAA,EAC1C,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,8CAA8C;AAAA,EAC9C,8CAA8C;AAAA,EAC9C,8CAA8C;AAAA,EAC9C,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,8CAA8C;AAAA,EAC9C,gDAAgD;AAAA,EAChD,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,qDAAqD;AAAA,EACrD,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,iDAAiD;AAAA,EACjD,mDAAmD;AAAA,EACnD,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,oCAAoC;AAAA,EACpC,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,iDAAiD;AAAA,EACjD,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,uCAAuC;AAAA,EACvC,4CAA4C;AAAA,EAC5C,yDAAyD;AAAA,EACzD,iDAAiD;AAAA,EACjD,8DAA8D;AAAA,EAC9D,mEAAmE;AAAA,EACnE,kEAAkE;AAAA,EAClE,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,8DAA8D;AAAA,EAC9D,qEAAqE;AAAA,EACrE,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,oDAAoD;AAAA,EACpD,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,mCAAmC;AAAA,EACnC,iDAAiD;AAAA,EACjD,yDAAyD;AAAA,EACzD,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,wCAAwC;AAAA,EACxC,0CAA0C;AAAA,EAC1C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,6CAA6C;AAAA,EAC7C,yDAAyD;AAAA,EACzD,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,yDAAyD;AAAA,EACzD,8CAA8C;AAAA,EAC9C,wCAAwC;AAAA,EACxC,sBAAsB;AAAA,EACtB,8BAA8B;AAAA,EAC9B,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,8CAA8C;AAAA,EAC9C,gDAAgD;AAAA,EAChD,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,oCAAoC;AAAA,EACpC,mCAAmC;AAAA,EACnC,oCAAoC;AAAA,EACpC,4CAA4C;AAAA,EAC5C,sCAAsC;AAAA,EACtC,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,uCAAuC;AAAA,EACvC,wCAAwC;AAAA,EACxC,8BAA8B;AAAA,EAC9B,gCAAgC;AAAA,EAChC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,iDAAiD;AAAA,EACjD,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,4CAA4C;AAAA,EAC5C,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,2CAA2C;AAAA,EAC3C,kCAAkC;AAAA,EAClC,sCAAsC;AAAA,EACtC,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,4CAA4C;AAAA,EAC5C,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,0CAA0C;AAAA,EAC1C,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,2BAA2B;AAAA,EAC3B,8CAA8C;AAAA,EAC9C,2CAA2C;AAAA,EAC3C,6CAA6C;AAAA,EAC7C,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,sCAAsC;AAAA,EACtC,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,qBAAqB;AAAA,EACrB,sBAAsB;AAAA,EACtB,2BAA2B;AAAA,EAC3B,gDAAgD;AAAA,EAChD,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB;AAAA,EACA,uBAAuB;AAAA,EACvB;AAAA,EACA,+BAA+B;AAAA,EAC/B,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,kDAAkD;AAAA,EAClD,0DAA0D;AAAA,EAC1D,mDAAmD;AAAA,EACnD,2DAA2D;AAAA,EAC3D,oCAAoC;AAAA,EACpC,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,6CAA6C;AAAA,EAC7C,2CAA2C;AAAA,EAC3C,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,uDAAuD;AAAA,EACvD,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,uCAAuC;AAAA,EACvC,kDAAkD;AAAA,EAClD,oDAAoD;AAAA,EACpD,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,kDAAkD;AAAA,EAClD,iDAAiD;AAAA,EACjD,0DAA0D;AAAA,EAC1D,gDAAgD;AAAA,EAChD,yDAAyD;AAAA,EACzD,2CAA2C;AAAA,EAC3C,oDAAoD;AAAA,EACpD,iDAAiD;AAAA,EACjD,0DAA0D;AAAA,EAC1D,kDAAkD;AAAA,EAClD,oDAAoD;AAAA,EACpD,wCAAwC;AAAA,EACxC,+CAA+C;AAAA,EAC/C;AAAA,EACA,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,4BAA4B;AAAA,EAC5B,wCAAwC;AAAA,EACxC,4BAA4B;AAAA,EAC5B,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,kCAAkC;AAAA,EAClC,0CAA0C;AAAA,EAC1C,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,4CAA4C;AAAA,EAC5C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,+CAA+C;AAAA,EAC/C,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,+CAA+C;AAAA,EAC/C,sDAAsD;AAAA,EACtD,wDAAwD;AAAA,EACxD,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,yDAAyD;AAAA,EACzD,qDAAqD;AAAA,EACrD,4CAA4C;AAAA,EAC5C,2CAA2C;AAAA,EAC3C,gDAAgD;AAAA,EAChD,4CAA4C;AAAA,EAC5C,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,sCAAsC;AAAA,EACtC,2CAA2C;AAAA,EAC3C,wDAAwD;AAAA,EACxD,sDAAsD;AAAA,EACtD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,2DAA2D;AAAA,EAC3D,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,oCAAoC;AAAA,EACpC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,8CAA8C;AAAA,EAC9C,4CAA4C;AAAA,EAC5C,iDAAiD;AAAA,EACjD,8CAA8C;AAAA,EAC9C,+CAA+C;AAAA,EAC/C,2BAA2B;AAAA,EAC3B,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,iDAAiD;AAAA,EACjD,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,mCAAmC;AAAA,EACnC,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,+EAA+E;AAAA,EAC/E,qEAAqE;AAAA,EACrE,yEAAyE;AAAA,EACzE,mEAAmE;AAAA,EACnE,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,4DAA4D;AAAA,EAC5D,0DAA0D;AAAA,EAC1D,sEAAsE;AAAA,EACtE,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,qDAAqD;AAAA,EACrD,wDAAwD;AAAA,EACxD,yDAAyD;AAAA,EACzD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,qCAAqC;AAAA,EACrC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,oEAAoE;AAAA,EACpE,4DAA4D;AAAA,EAC5D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,wDAAwD;AAAA,EACxD,4BAA4B;AAAA,EAC5B,sBAAsB;AAAA,EACtB,+BAA+B;AAAA,EAC/B,yCAAyC;AAAA,EACzC,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,4BAA4B;AAAA,EAC5B,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,kBAAkB;AAAA,EAClB,yCAAyC;AAAA,EACzC,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,gCAAgC;AAAA,EAChC,wCAAwC;AAAA,EACxC,qBAAqB;AAAA,EACrB,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,8BAA8B;AAAA,EAC9B,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,oBAAoB;AAAA,EACpB;AAAA,EACA,eAAe;AAAA,EACf,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,+CAA+C;AAAA,EAC/C,qDAAqD;AAAA,EACrD,sDAAsD;AAAA,EACtD,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,0CAA0C;AAAA,EAC1C,4CAA4C;AAAA,EAC5C,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,gDAAgD;AAAA,EAChD,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,2CAA2C;AAAA,EAC3C,4CAA4C;AAAA,EAC5C,8CAA8C;AAAA,EAC9C,4CAA4C;AAAA,EAC5C,qDAAqD;AAAA,EACrD,oDAAoD;AAAA,EACpD,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,yDAAyD;AAAA,EACzD,yDAAyD;AAAA,EACzD,qDAAqD;AAAA,EACrD,wDAAwD;AAAA,EACxD,sDAAsD;AAAA,EACtD,6CAA6C;AAAA,EAC7C,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,oCAAoC;AAAA,EACpC,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,oCAAoC;AAAA,EACpC,0BAA0B;AAAA,EAC1B,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA,EAC9B,kCAAkC;AAAA,EAClC,8CAA8C;AAAA,EAC9C,kCAAkC;AAAA,EAClC,8CAA8C;AAAA,EAC9C,wCAAwC;AAAA,EACxC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,wCAAwC;AAAA,EACxC,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,+CAA+C;AAAA,EAC/C,qCAAqC;AAAA,EACrC,iCAAiC;AAAA,EACjC,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,yDAAyD;AAAA,EACzD,+CAA+C;AAAA,EAC/C,gDAAgD;AAAA,EAChD,uDAAuD;AAAA,EACvD,+BAA+B;AAAA,EAC/B,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,yCAAyC;AAAA,EACzC,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC;AAAA,EACA,wBAAwB;AAAA,EACxB,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,0BAA0B;AAAA,EAC1B,gBAAgB;AAAA,EAChB,0BAA0B;AAAA,EAC1B,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,wBAAwB;AAAA,EACxB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,sBAAsB;AAAA,EACtB,eAAe;AAAA,EACf,cAAc;AAAA,EACd,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,kCAAkC;AAAA,EAClC,8CAA8C;AAAA,EAC9C,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,wBAAwB;AAAA,EACxB,oCAAoC;AAAA,EACpC,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,gDAAgD;AAAA,EAChD,kBAAkB;AAAA,EAClB,yBAAyB;AAAA,EACzB,yBAAyB;AAAA,EACzB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB;AAAA,EACA,+CAA+C;AAAA,EAC/C,8BAA8B;AAAA,EAC9B,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,sBAAsB;AAAA,EACtB,4CAA4C;AAAA,EAC5C,6BAA6B;AAAA,EAC7B,yCAAyC;AAAA,EACzC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,4CAA4C;AAAA,EAC5C,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,6CAA6C;AAAA,EAC7C,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA,EAC9B;AAAA,EACA,+BAA+B;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA,iCAAiC;AACrC;", "names": []}