{"version": 3, "sources": ["../../../@strapi/plugin-users-permissions/dist/admin/translations/zh-Hans.json.mjs"], "sourcesContent": ["var zhHans = {\n    \"BoundRoute.title\": \"绑定路由到\",\n    \"EditForm.inputSelect.description.role\": \"新验证身份的用户将被赋予所选角色。\",\n    \"EditForm.inputSelect.label.role\": \"认证用户的默认角色\",\n    \"EditForm.inputToggle.description.email\": \"不允许用户使用不同的认证提供商但相同的电子邮件地址来创建多个账户。\",\n    \"EditForm.inputToggle.description.email-confirmation\": \"启用（开）后，新注册的用户会收到一封确认电子邮件。\",\n    \"EditForm.inputToggle.description.email-confirmation-redirection\": \"确认您的电子邮件后，选择将您重定向到的位置。\",\n    \"EditForm.inputToggle.description.email-reset-password\": \"应用程序的重置密码页面的网址\",\n    \"EditForm.inputToggle.description.sign-up\": \"当禁用（关）时，注册过程将被禁止。任何人无论使用任何的供应商都不可以订阅。\",\n    \"EditForm.inputToggle.label.email\": \"每个电子邮件地址对应一个账户\",\n    \"EditForm.inputToggle.label.email-confirmation\": \"启用电子邮件确认\",\n    \"EditForm.inputToggle.label.email-confirmation-redirection\": \"重定向网址\",\n    \"EditForm.inputToggle.label.email-reset-password\": \"重置密码页面网址\",\n    \"EditForm.inputToggle.label.sign-up\": \"启用注册\",\n    \"EditForm.inputToggle.placeholder.email-confirmation-redirection\": \"例如: https://yourfrontend.com/email-confirmation-redirection\",\n    \"EditForm.inputToggle.placeholder.email-reset-password\": \"例如: https://yourfrontend.com/reset-password\",\n    \"EditPage.form.roles\": \"角色详情\",\n    \"Email.template.data.loaded\": \"电子邮件模板已加载\",\n    \"Email.template.email_confirmation\": \"邮箱地址确认\",\n    \"Email.template.form.edit.label\": \"编辑模板\",\n    \"Email.template.table.action.label\": \"操作\",\n    \"Email.template.table.icon.label\": \"图标\",\n    \"Email.template.table.name.label\": \"名称\",\n    \"Form.advancedSettings.data.loaded\": \"高级设置数据已加载\",\n    \"HeaderNav.link.advancedSettings\": \"高级设置\",\n    \"HeaderNav.link.emailTemplates\": \"电子邮件模板\",\n    \"HeaderNav.link.providers\": \"提供商\",\n    \"Plugin.permissions.plugins.description\": \"定义 {name} 插件所有允许的操作。\",\n    \"Plugins.header.description\": \"下面只列出路由绑定的操作。\",\n    \"Plugins.header.title\": \"权限\",\n    \"Policies.header.hint\": \"选择应用程序或插件对应的操作，然后点击齿轮图标显示绑定的路由\",\n    \"Policies.header.title\": \"高级设置\",\n    \"PopUpForm.Email.email_templates.inputDescription\": \"如果你不确定如何使用变量， {link}\",\n    \"PopUpForm.Email.link.documentation\": \"查看我们的文档\",\n    \"PopUpForm.Email.options.from.email.label\": \"发件人地址\",\n    \"PopUpForm.Email.options.from.email.placeholder\": \"<EMAIL>\",\n    \"PopUpForm.Email.options.from.name.label\": \"发件人名称\",\n    \"PopUpForm.Email.options.from.name.placeholder\": \"Kai Doe\",\n    \"PopUpForm.Email.options.message.label\": \"消息\",\n    \"PopUpForm.Email.options.object.label\": \"主题\",\n    \"PopUpForm.Email.options.object.placeholder\": \"请为%APP_NAME%确认邮箱地址\",\n    \"PopUpForm.Email.options.response_email.label\": \"回复邮件\",\n    \"PopUpForm.Email.options.response_email.placeholder\": \"<EMAIL>\",\n    \"PopUpForm.Providers.enabled.description\": \"如果禁用，用户将无法使用此供应商。\",\n    \"PopUpForm.Providers.enabled.label\": \"启用\",\n    \"PopUpForm.Providers.key.label\": \"客户端 ID\",\n    \"PopUpForm.Providers.key.placeholder\": \"文本\",\n    \"PopUpForm.Providers.redirectURL.front-end.label\": \"重定向网址\",\n    \"PopUpForm.Providers.redirectURL.label\": \"添加到{provider}应用配置的跳转网址\",\n    \"PopUpForm.Providers.secret.label\": \"客户端秘钥\",\n    \"PopUpForm.Providers.secret.placeholder\": \"文本\",\n    \"PopUpForm.Providers.subdomain.label\": \"主机URI（子域名）\",\n    \"PopUpForm.Providers.subdomain.placeholder\": \"my.subdomain.com\",\n    \"PopUpForm.header.edit.email-templates\": \"编辑电子邮件模版\",\n    \"PopUpForm.header.edit.providers\": \"编辑提供商\",\n    \"Providers.data.loaded\": \"提供商已加载\",\n    \"Providers.status\": \"状态\",\n    \"Roles.empty\": \"您还没有任何角色。\",\n    \"Roles.empty.search\": \"没有与搜索相匹配的角色。\",\n    \"Settings.roles.deleted\": \"角色已被删除\",\n    \"Settings.roles.edited\": \"角色编辑完成\",\n    \"Settings.section-label\": \"用户及权限插件\",\n    \"components.Input.error.validation.email\": \"这是一个无效的电子邮件\",\n    \"components.Input.error.validation.json\": \"这不符合JSON格式\",\n    \"components.Input.error.validation.max\": \"值过高。\",\n    \"components.Input.error.validation.maxLength\": \"值过长。\",\n    \"components.Input.error.validation.min\": \"值太低。\",\n    \"components.Input.error.validation.minLength\": \"值太短。\",\n    \"components.Input.error.validation.minSupMax\": \"不能超过上限\",\n    \"components.Input.error.validation.regex\": \"该值不符合正则表达式。\",\n    \"components.Input.error.validation.required\": \"该值为必填项。\",\n    \"components.Input.error.validation.unique\": \"该值已被使用。\",\n    \"notification.success.submit\": \"设置已被更新\",\n    \"page.title\": \"设置 - 角色\",\n    \"plugin.description.long\": \"使用基于 JWT 的完整身份验证过程来保护 API。这个插件还有一个 ACL 策略，允许你管理用户组之间的权限。\",\n    \"plugin.description.short\": \"使用基于 JWT 的完整身份验证过程保护 API\",\n    \"plugin.name\": \"用户及权限插件\",\n    \"popUpWarning.button.cancel\": \"取消\",\n    \"popUpWarning.button.confirm\": \"确认\",\n    \"popUpWarning.title\": \"请确认\",\n    \"popUpWarning.warning.cancel\": \"你确定你要取消你的修改？\"\n};\n\nexport { zhHans as default };\n//# sourceMappingURL=zh-Hans.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,SAAS;AAAA,EACT,oBAAoB;AAAA,EACpB,yCAAyC;AAAA,EACzC,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,uDAAuD;AAAA,EACvD,mEAAmE;AAAA,EACnE,yDAAyD;AAAA,EACzD,4CAA4C;AAAA,EAC5C,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,6DAA6D;AAAA,EAC7D,mDAAmD;AAAA,EACnD,sCAAsC;AAAA,EACtC,mEAAmE;AAAA,EACnE,yDAAyD;AAAA,EACzD,uBAAuB;AAAA,EACvB,8BAA8B;AAAA,EAC9B,qCAAqC;AAAA,EACrC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,0CAA0C;AAAA,EAC1C,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,oDAAoD;AAAA,EACpD,sCAAsC;AAAA,EACtC,4CAA4C;AAAA,EAC5C,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,iDAAiD;AAAA,EACjD,yCAAyC;AAAA,EACzC,wCAAwC;AAAA,EACxC,8CAA8C;AAAA,EAC9C,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,iCAAiC;AAAA,EACjC,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,0CAA0C;AAAA,EAC1C,uCAAuC;AAAA,EACvC,6CAA6C;AAAA,EAC7C,yCAAyC;AAAA,EACzC,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,oBAAoB;AAAA,EACpB,eAAe;AAAA,EACf,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,4CAA4C;AAAA,EAC5C,+BAA+B;AAAA,EAC/B,cAAc;AAAA,EACd,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,eAAe;AAAA,EACf,8BAA8B;AAAA,EAC9B,+BAA+B;AAAA,EAC/B,sBAAsB;AAAA,EACtB,+BAA+B;AACnC;", "names": []}