{"version": 3, "sources": ["../../../@strapi/content-manager/dist/admin/translations/zh-Hans.json.mjs"], "sourcesContent": ["var Analytics = \"分析\";\nvar Documentation = \"文档\";\nvar Email = \"电子邮件\";\nvar Password = \"密码\";\nvar Provider = \"提供商\";\nvar ResetPasswordToken = \"重置密码令牌\";\nvar Role = \"角色\";\nvar Username = \"用户名\";\nvar Users = \"用户\";\nvar anErrorOccurred = \"出错了!发生了一些问题,请重试。\";\nvar clearLabel = \"清除\";\nvar dark = \"深色\";\nvar light = \"浅色\";\nvar or = \"或\";\nvar selectButtonTitle = \"选择\";\nvar skipToContent = \"跳至内容\";\nvar submit = \"提交\";\nvar zhHans = {\n    Analytics: Analytics,\n    \"Auth.components.Oops.text\": \"您的账户已被暂停。\",\n    \"Auth.components.Oops.text.admin\": \"如果这是一个错误,请联系您的管理员。\",\n    \"Auth.components.Oops.title\": \"糟糕...\",\n    \"Auth.form.active.label\": \"激活\",\n    \"Auth.form.button.forgot-password\": \"发送电子邮件\",\n    \"Auth.form.button.go-home\": \"返回主页\",\n    \"Auth.form.button.login\": \"登录\",\n    \"Auth.form.button.login.providers.error\": \"我们无法通过所选提供商连接您。\",\n    \"Auth.form.button.login.strapi\": \"通过Strapi登录\",\n    \"Auth.form.button.password-recovery\": \"密码恢复\",\n    \"Auth.form.button.register\": \"让我们开始吧\",\n    \"Auth.form.confirmPassword.label\": \"确认密码\",\n    \"Auth.form.currentPassword.label\": \"当前密码\",\n    \"Auth.form.email.label\": \"电子邮件\",\n    \"Auth.form.email.placeholder\": \"例如: <EMAIL>\",\n    \"Auth.form.error.blocked\": \"您的账户已被管理员阻止。\",\n    \"Auth.form.error.code.provide\": \"提供的验证码不正确。\",\n    \"Auth.form.error.confirmed\": \"您的账户邮箱未确认。\",\n    \"Auth.form.error.email.invalid\": \"此电子邮件无效。\",\n    \"Auth.form.error.email.provide\": \"请提供您的用户名或电子邮件。\",\n    \"Auth.form.error.email.taken\": \"电子邮件已被占用。\",\n    \"Auth.form.error.invalid\": \"标识符或密码无效。\",\n    \"Auth.form.error.params.provide\": \"提供的参数不正确。\",\n    \"Auth.form.error.password.format\": \"您的密码不能包含超过3个 $ 符号。\",\n    \"Auth.form.error.password.local\": \"此用户从未设置本地密码,请通过账户创建时使用的提供商登录。\",\n    \"Auth.form.error.password.matching\": \"密码不匹配。\",\n    \"Auth.form.error.password.provide\": \"请提供您的密码。\",\n    \"Auth.form.error.ratelimit\": \"尝试次数过多,请在1分钟后重试。\",\n    \"Auth.form.error.user.not-exist\": \"此电子邮件不存在。\",\n    \"Auth.form.error.username.taken\": \"用户名已被占用。\",\n    \"Auth.form.firstname.label\": \"名字\",\n    \"Auth.form.firstname.placeholder\": \"例如:明\",\n    \"Auth.form.forgot-password.email.label\": \"输入您的电子邮件\",\n    \"Auth.form.forgot-password.email.label.success\": \"重置密码链接已成功发送至\",\n    \"Auth.form.lastname.label\": \"姓氏\",\n    \"Auth.form.lastname.placeholder\": \"例如:李\",\n    \"Auth.form.password.hide-password\": \"隐藏密码\",\n    \"Auth.form.password.hint\": \"必须至少8个字符,包含1个大写字母,1个小写字母和1个数字\",\n    \"Auth.form.password.show-password\": \"显示密码\",\n    \"Auth.form.register.news.label\": \"关于新功能和即将到来的改进保持更新(这样做即表示您接受{terms}和{policy})。\",\n    \"Auth.form.register.subtitle\": \"凭证仅用于在Strapi中进行身份验证。所有保存的数据都将存储在您的数据库中。\",\n    \"Auth.form.rememberMe.label\": \"记住我\",\n    \"Auth.form.username.label\": \"用户名\",\n    \"Auth.form.username.placeholder\": \"例如: 李明\",\n    \"Auth.form.welcome.subtitle\": \"登录您的Strapi账户\",\n    \"Auth.form.welcome.title\": \"欢迎使用Strapi!\",\n    \"Auth.link.forgot-password\": \"忘记密码?\",\n    \"Auth.link.ready\": \"准备好登录了吗?\",\n    \"Auth.link.signin\": \"登录\",\n    \"Auth.link.signin.account\": \"已经有账户了?\",\n    \"Auth.login.sso.divider\": \"或使用登录\",\n    \"Auth.login.sso.loading\": \"加载中提供商...\",\n    \"Auth.login.sso.subtitle\": \"通过SSO登录您的帐户\",\n    \"Auth.privacy-policy-agreement.policy\": \"隐私政策\",\n    \"Auth.privacy-policy-agreement.terms\": \"条款\",\n    \"Auth.reset-password.title\": \"重置密码\",\n    \"Content Manager\": \"内容管理器\",\n    \"Content Type Builder\": \"内容类型构建器\",\n    Documentation: Documentation,\n    Email: Email,\n    \"Files Upload\": \"文件上传\",\n    \"HomePage.helmet.title\": \"主页\",\n    \"HomePage.roadmap\": \"查看路线图\",\n    \"HomePage.welcome.congrats\": \"恭喜!\",\n    \"HomePage.welcome.congrats.content\": \"您已登录为第一个管理员。为了发现Strapi提供的强大功能,\",\n    \"HomePage.welcome.congrats.content.bold\": \"我们建议您创建您的第一个集合类型。\",\n    \"Media Library\": \"媒体库\",\n    \"New entry\": \"新条目\",\n    Password: Password,\n    Provider: Provider,\n    ResetPasswordToken: ResetPasswordToken,\n    Role: Role,\n    \"Roles & Permissions\": \"角色和权限\",\n    \"Roles.ListPage.notification.delete-all-not-allowed\": \"由于某些角色与用户关联,因此无法删除它们\",\n    \"Roles.ListPage.notification.delete-not-allowed\": \"如果与用户关联则角色不能被删除\",\n    \"Roles.RoleRow.select-all\": \"选择{name} 进行批量操作\",\n    \"Roles.RoleRow.user-count\": \"{number, plural, =0 {无用户} one {1 个用户} other {# 个用户}}\",\n    \"Roles.components.List.empty.withSearch\": \"没有与搜索({search})对应的角色...\",\n    \"Settings.PageTitle\": \"设置 —— {name}\",\n    \"Settings.apiTokens.ListView.headers.createdAt\": \"创建时间\",\n    \"Settings.apiTokens.ListView.headers.description\": \"描述\",\n    \"Settings.apiTokens.ListView.headers.lastUsedAt\": \"上次使用\",\n    \"Settings.apiTokens.ListView.headers.name\": \"名称\",\n    \"Settings.apiTokens.ListView.headers.type\": \"令牌类型\",\n    \"Settings.apiTokens.addFirstToken\": \"添加您的第一个API令牌\",\n    \"Settings.apiTokens.addNewToken\": \"添加新的API令牌\",\n    \"Settings.apiTokens.create\": \"创建新的API令牌\",\n    \"Settings.apiTokens.createPage.BoundRoute.title\": \"绑定路由到\",\n    \"Settings.apiTokens.createPage.permissions.description\": \"仅列出下面绑定了路由的操作。\",\n    \"Settings.apiTokens.createPage.permissions.header.hint\": \"选择应用程序的操作或插件的操作,然后单击齿轮图标以显示绑定的路由\",\n    \"Settings.apiTokens.createPage.permissions.header.title\": \"高级设置\",\n    \"Settings.apiTokens.createPage.permissions.title\": \"权限\",\n    \"Settings.apiTokens.createPage.title\": \"创建API令牌\",\n    \"Settings.apiTokens.description\": \"生成的令牌列表以使用API\",\n    \"Settings.apiTokens.emptyStateLayout\": \"您还没有任何内容...\",\n    \"Settings.apiTokens.regenerate\": \"重新生成\",\n    \"Settings.apiTokens.title\": \"API令牌\",\n    \"Settings.apiTokens.lastHour\": \"最近1小时\",\n    \"Settings.application.customization\": \"定制\",\n    \"Settings.application.customization.auth-logo.carousel-hint\": \"替换认证页面中的logo\",\n    \"Settings.application.customization.carousel-hint\": \"更改管理面板logo(最大尺寸:{dimension}x{dimension},最大文件大小:{size}KB)\",\n    \"Settings.application.customization.carousel-slide.label\": \"Logo幻灯片\",\n    \"Settings.application.customization.carousel.auth-logo.title\": \"认证logo\",\n    \"Settings.application.customization.carousel.change-action\": \"更改logo\",\n    \"Settings.application.customization.carousel.menu-logo.title\": \"菜单logo\",\n    \"Settings.application.customization.carousel.reset-action\": \"重置logo\",\n    \"Settings.application.customization.carousel.title\": \"Logo\",\n    \"Settings.application.customization.menu-logo.carousel-hint\": \"替换主导航中的logo\",\n    \"Settings.application.customization.modal.cancel\": \"取消\",\n    \"Settings.application.customization.modal.pending\": \"待处理的logo\",\n    \"Settings.application.customization.modal.pending.card-badge\": \"图像\",\n    \"Settings.application.customization.modal.pending.choose-another\": \"选择其他logo\",\n    \"Settings.application.customization.modal.pending.subtitle\": \"在上传之前管理所选的logo\",\n    \"Settings.application.customization.modal.pending.title\": \"准备上传的logo\",\n    \"Settings.application.customization.modal.pending.upload\": \"上传logo\",\n    \"Settings.application.customization.modal.tab.label\": \"您希望如何上传资产?\",\n    \"Settings.application.customization.modal.upload\": \"上传logo\",\n    \"Settings.application.customization.modal.upload.cta.browse\": \"浏览文件\",\n    \"Settings.application.customization.modal.upload.drag-drop\": \"拖放到此区域或\",\n    \"Settings.application.customization.modal.upload.error-format\": \"上传了错误的格式(仅接受:jpeg、jpg、png、svg)。\",\n    \"Settings.application.customization.modal.upload.error-network\": \"网络错误\",\n    \"Settings.application.customization.modal.upload.error-size\": \"上传的文件太大(最大尺寸:{dimension}x{dimension},最大文件大小:{size}KB)\",\n    \"Settings.application.customization.modal.upload.file-validation\": \"最大尺寸:{dimension}x{dimension},最大大小:{size}KB\",\n    \"Settings.application.customization.modal.upload.from-computer\": \"从计算机\",\n    \"Settings.application.customization.modal.upload.from-url\": \"从URL\",\n    \"Settings.application.customization.modal.upload.from-url.input-label\": \"URL\",\n    \"Settings.application.customization.modal.upload.next\": \"下一步\",\n    \"Settings.application.customization.size-details\": \"最大尺寸:{dimension}×{dimension},最大文件大小:{size}KB\",\n    \"Settings.application.description\": \"管理面板的全局信息\",\n    \"Settings.application.edition-title\": \"当前计划\",\n    \"Settings.application.ee-or-ce\": \"{communityEdition, select, true {社区版} other {企业版}}\",\n    \"Settings.application.ee.admin-seats.add-seats\": \"管理座位\",\n    \"Settings.application.ee.admin-seats.support\": \"请联系支持团队\",\n    \"Settings.application.ee.admin-seats.at-limit-tooltip\": \"已达上限:添加座位以邀请更多用户\",\n    \"Settings.application.ee.admin-seats.count\": \"<text>{enforcementUserCount}</text>/{permittedSeats}\",\n    \"Settings.application.get-help\": \"获取帮助\",\n    \"Settings.application.link-pricing\": \"查看所有定价计划\",\n    \"Settings.application.link-upgrade\": \"升级您的管理面板\",\n    \"Settings.application.node-version\": \"节点版本\",\n    \"Settings.application.strapi-version\": \"Strapi版本\",\n    \"Settings.application.strapiVersion\": \"Strapi版本\",\n    \"Settings.application.title\": \"概览\",\n    \"Settings.error\": \"错误\",\n    \"Settings.global\": \"全局设置\",\n    \"Settings.permissions\": \"管理面板\",\n    \"Settings.permissions.auditLogs.action\": \"操作\",\n    \"Settings.permissions.auditLogs.admin.auth.success\": \"管理员登录\",\n    \"Settings.permissions.auditLogs.admin.logout\": \"管理员登出\",\n    \"Settings.permissions.auditLogs.component.create\": \"创建组件\",\n    \"Settings.permissions.auditLogs.component.delete\": \"删除组件\",\n    \"Settings.permissions.auditLogs.component.update\": \"更新组件\",\n    \"Settings.permissions.auditLogs.content-type.create\": \"创建内容类型\",\n    \"Settings.permissions.auditLogs.content-type.delete\": \"删除内容类型\",\n    \"Settings.permissions.auditLogs.content-type.update\": \"更新内容类型\",\n    \"Settings.permissions.auditLogs.date\": \"日期\",\n    \"Settings.permissions.auditLogs.details\": \"日志详情\",\n    \"Settings.permissions.auditLogs.entry.create\": \"创建条目{model, select, undefined {} other { ({model})}}\",\n    \"Settings.permissions.auditLogs.entry.delete\": \"删除条目{model, select, undefined {} other { ({model})}}\",\n    \"Settings.permissions.auditLogs.entry.publish\": \"发布条目 {model, select, undefined {} other {({model})}}\",\n    \"Settings.permissions.auditLogs.entry.unpublish\": \"取消发布条目{model, select, undefined {} other { ({model})}}\",\n    \"Settings.permissions.auditLogs.entry.update\": \"更新条目{model, select, undefined {} other { ({model})}}\",\n    \"Settings.permissions.auditLogs.filters.combobox.aria-label\": \"搜索并选择一个选项以筛选\",\n    \"Settings.permissions.auditLogs.listview.header.subtitle\": \"环境中发生的所有活动的日志\",\n    \"Settings.permissions.auditLogs.media.create\": \"创建媒体\",\n    \"Settings.permissions.auditLogs.media.delete\": \"删除媒体\",\n    \"Settings.permissions.auditLogs.media.update\": \"更新媒体\",\n    \"Settings.permissions.auditLogs.payload\": \"负载\",\n    \"Settings.permissions.auditLogs.permission.create\": \"创建权限\",\n    \"Settings.permissions.auditLogs.permission.delete\": \"删除权限\",\n    \"Settings.permissions.auditLogs.permission.update\": \"更新权限\",\n    \"Settings.permissions.auditLogs.role.create\": \"创建角色\",\n    \"Settings.permissions.auditLogs.role.delete\": \"删除角色\",\n    \"Settings.permissions.auditLogs.role.update\": \"更新角色\",\n    \"Settings.permissions.auditLogs.user\": \"用户\",\n    \"Settings.permissions.auditLogs.user.create\": \"创建用户\",\n    \"Settings.permissions.auditLogs.user.delete\": \"删除用户\",\n    \"Settings.permissions.auditLogs.user.fullname\": \"{firstname} {lastname}\",\n    \"Settings.permissions.auditLogs.user.update\": \"更新用户\",\n    \"Settings.permissions.auditLogs.userId\": \"用户ID\",\n    \"Settings.permissions.category\": \"{category}的权限设置\",\n    \"Settings.permissions.category.plugins\": \"{category}插件的权限设置\",\n    \"Settings.permissions.conditions.anytime\": \"任何时间\",\n    \"Settings.permissions.conditions.apply\": \"应用\",\n    \"Settings.permissions.conditions.can\": \"可以\",\n    \"Settings.permissions.conditions.conditions\": \"条件\",\n    \"Settings.permissions.conditions.define-conditions\": \"定义条件\",\n    \"Settings.permissions.conditions.links\": \"链接\",\n    \"Settings.permissions.conditions.no-actions\": \"您首先需要选择操作(创建、读取、更新等)才能对其定义条件。\",\n    \"Settings.permissions.conditions.none-selected\": \"任何时间\",\n    \"Settings.permissions.conditions.or\": \"或\",\n    \"Settings.permissions.conditions.when\": \"何时\",\n    \"Settings.permissions.select-all-by-permission\": \"选择所有{label}权限\",\n    \"Settings.permissions.select-by-permission\": \"选择{label}权限\",\n    \"Settings.permissions.users.active\": \"激活\",\n    \"Settings.permissions.users.create\": \"邀请新用户\",\n    \"Settings.permissions.users.email\": \"电子邮箱\",\n    \"Settings.permissions.users.firstname\": \"名字\",\n    \"Settings.permissions.users.form.sso\": \"通过SSO连接\",\n    \"Settings.permissions.users.sso.provider.error\": \"请求SSO设置时发生错误\",\n    \"Settings.permissions.users.form.sso.description\": \"启用(开)时,用户可以通过SSO登录\",\n    \"Settings.permissions.users.inactive\": \"未激活\",\n    \"Settings.permissions.users.lastname\": \"姓氏\",\n    \"Settings.permissions.users.listview.header.subtitle\": \"所有能够访问Strapi管理面板的用户\",\n    \"Settings.permissions.users.roles\": \"角色\",\n    \"Settings.permissions.users.strapi-author\": \"作者\",\n    \"Settings.permissions.users.strapi-editor\": \"编辑者\",\n    \"Settings.permissions.users.strapi-super-admin\": \"超级管理员\",\n    \"Settings.permissions.users.tabs.label\": \"选项卡权限\",\n    \"Settings.permissions.users.user-status\": \"用户状态\",\n    \"Settings.permissions.users.username\": \"用户名\",\n    \"Settings.profile.form.notify.data.loaded\": \"您的个人资料数据已加载\",\n    \"Settings.profile.form.section.experience.clear.select\": \"清除所选的界面语言\",\n    \"Settings.profile.form.section.experience.here\": \"这里\",\n    \"Settings.profile.form.section.experience.interfaceLanguage\": \"界面语言\",\n    \"Settings.profile.form.section.experience.interfaceLanguage.hint\": \"这只会在所选语言中显示您自己的界面。\",\n    \"Settings.profile.form.section.experience.interfaceLanguageHelp\": \"偏好设置更改仅适用于您。更多信息请访问{here}。\",\n    \"Settings.profile.form.section.experience.mode.hint\": \"以所选模式显示界面。\",\n    \"Settings.profile.form.section.experience.mode.label\": \"界面模式\",\n    \"Settings.profile.form.section.experience.mode.option-label\": \"{name}模式\",\n    \"Settings.profile.form.section.experience.title\": \"体验\",\n    \"Settings.profile.form.section.helmet.title\": \"用户简介\",\n    \"Settings.profile.form.section.profile.page.title\": \"简介页面\",\n    \"Settings.roles.create.description\": \"定义角色的权限\",\n    \"Settings.roles.create.title\": \"创建角色\",\n    \"Settings.roles.created\": \"角色已创建\",\n    \"Settings.roles.edit.title\": \"编辑角色\",\n    \"Settings.roles.form.button.users-with-role\": \"{number, plural, =0 {无用户} one {1个用户} other {#个用户}}拥有此角色\",\n    \"Settings.roles.form.created\": \"已创建\",\n    \"Settings.roles.form.description\": \"角色的名称和描述\",\n    \"Settings.roles.form.permission.property-label\": \"{label}权限\",\n    \"Settings.roles.form.permissions.attributesPermissions\": \"字段权限\",\n    \"Settings.roles.form.permissions.create\": \"创建\",\n    \"Settings.roles.form.permissions.delete\": \"删除\",\n    \"Settings.roles.form.permissions.publish\": \"发布\",\n    \"Settings.roles.form.permissions.read\": \"读取\",\n    \"Settings.roles.form.permissions.update\": \"更新\",\n    \"Settings.roles.list.button.add\": \"添加新角色\",\n    \"Settings.roles.list.description\": \"角色列表\",\n    \"Settings.roles.title.singular\": \"角色\",\n    \"Settings.sso.description\": \"配置单点登录功能的设置。\",\n    \"Settings.sso.form.defaultRole.description\": \"它会将新的认证用户连接到所选角色\",\n    \"Settings.sso.form.defaultRole.description-not-allowed\": \"您需要具有读取管理角色的权限\",\n    \"Settings.sso.form.defaultRole.label\": \"默认角色\",\n    \"Settings.sso.form.localAuthenticationLock.label\": \"本地认证锁定\",\n    \"Settings.sso.form.localAuthenticationLock.description\": \"选择要禁用本地认证的角色\",\n    \"Settings.sso.form.registration.description\": \"如果没有账户,则在SSO登录时创建新用户\",\n    \"Settings.sso.form.registration.label\": \"自动注册\",\n    \"Settings.sso.title\": \"单点登录\",\n    \"Settings.tokens.Button.cancel\": \"取消\",\n    \"Settings.tokens.Button.regenerate\": \"重新生成\",\n    \"Settings.tokens.ListView.headers.createdAt\": \"创建时间\",\n    \"Settings.tokens.ListView.headers.description\": \"描述\",\n    \"Settings.tokens.ListView.headers.lastUsedAt\": \"上次使用\",\n    \"Settings.tokens.ListView.headers.name\": \"名称\",\n    \"Settings.tokens.RegenerateDialog.title\": \"重新生成令牌\",\n    \"Settings.tokens.copy.editMessage\": \"出于安全考虑,您只能看一次令牌。\",\n    \"Settings.tokens.copy.editTitle\": \"此令牌不再可访问。\",\n    \"Settings.tokens.copy.lastWarning\": \"请务必复制此令牌,您将无法再次查看它!\",\n    \"Settings.tokens.duration.30-days\": \"30天\",\n    \"Settings.tokens.duration.7-days\": \"7天\",\n    \"Settings.tokens.duration.90-days\": \"90天\",\n    \"Settings.tokens.duration.expiration-date\": \"到期日期\",\n    \"Settings.tokens.duration.unlimited\": \"无限期\",\n    \"Settings.tokens.form.description\": \"描述\",\n    \"Settings.tokens.form.duration\": \"令牌持续时间\",\n    \"Settings.tokens.form.name\": \"名称\",\n    \"Settings.tokens.form.type\": \"令牌类型\",\n    \"Settings.tokens.notification.copied\": \"令牌已复制到剪贴板。\",\n    \"Settings.tokens.popUpWarning.message\": \"您确定要重新生成此令牌吗?\",\n    \"Settings.tokens.regenerate\": \"重新生成\",\n    \"Settings.tokens.types.custom\": \"自定义\",\n    \"Settings.tokens.types.full-access\": \"完全访问\",\n    \"Settings.tokens.types.read-only\": \"只读\",\n    \"Settings.transferTokens.ListView.headers.type\": \"令牌类型\",\n    \"Settings.transferTokens.addFirstToken\": \"添加您的第一个传输令牌\",\n    \"Settings.transferTokens.addNewToken\": \"添加新的传输令牌\",\n    \"Settings.transferTokens.create\": \"创建新的传输令牌\",\n    \"Settings.transferTokens.createPage.title\": \"创建传输令牌\",\n    \"Settings.transferTokens.description\": \"生成的传输令牌列表\",\n    \"Settings.transferTokens.emptyStateLayout\": \"您还没有任何内容...\",\n    \"Settings.transferTokens.title\": \"传输令牌\",\n    \"Settings.webhooks.create\": \"创建webhook\",\n    \"Settings.webhooks.create.header\": \"创建新标头\",\n    \"Settings.webhooks.created\": \"Webhook已创建\",\n    \"Settings.webhooks.event.publish-tooltip\": \"此事件仅存在于启用了草稿/发布系统的内容中\",\n    \"Settings.webhooks.event.select\": \"选择事件\",\n    \"Settings.webhooks.events.isLoading\": \"事件加载中\",\n    \"Settings.webhooks.events.create\": \"创建\",\n    \"Settings.webhooks.events.update\": \"更新\",\n    \"Settings.webhooks.events.delete\": \"删除webhook\",\n    \"Settings.webhooks.form.events\": \"事件\",\n    \"Settings.webhooks.form.headers\": \"标头\",\n    \"Settings.webhooks.form.url\": \"网址\",\n    \"Settings.webhooks.headers.remove\": \"移除标头行{number}\",\n    \"Settings.webhooks.key\": \"密钥\",\n    \"Settings.webhooks.list.button.add\": \"创建新webhook\",\n    \"Settings.webhooks.list.description\": \"获取POST更改通知\",\n    \"Settings.webhooks.list.empty.description\": \"找不到webhook\",\n    \"Settings.webhooks.list.empty.link\": \"请参阅我们的文档\",\n    \"Settings.webhooks.list.empty.title\": \"还没有webhook\",\n    \"Settings.webhooks.list.th.actions\": \"操作\",\n    \"Settings.webhooks.list.th.status\": \"状态\",\n    \"Settings.webhooks.list.loading.success\": \"Webhook已加载\",\n    \"Settings.webhooks.singular\": \"webhook\",\n    \"Settings.webhooks.title\": \"Webhook\",\n    \"Settings.webhooks.to.delete\": \"选择了 {webhooksToDeleteLength, plural, one {# 个webhook} other {# 个webhooks}}\",\n    \"Settings.webhooks.trigger\": \"触发器\",\n    \"Settings.webhooks.trigger.cancel\": \"取消触发\",\n    \"Settings.webhooks.trigger.pending\": \"待定...\",\n    \"Settings.webhooks.trigger.save\": \"请先保存以触发\",\n    \"Settings.webhooks.trigger.success\": \"成功!\",\n    \"Settings.webhooks.trigger.success.label\": \"触发成功\",\n    \"Settings.webhooks.trigger.test\": \"测试触发\",\n    \"Settings.webhooks.trigger.title\": \"保存前触发\",\n    \"Settings.webhooks.value\": \"值\",\n    \"Settings.webhooks.validation.name.required\": \"名称是必需的\",\n    \"Settings.webhooks.validation.name.regex\": \"名称必须以字母开头,并且只能包含字母、数字、空格和下划线\",\n    \"Settings.webhooks.validation.url.required\": \"网址为必填\",\n    \"Settings.webhooks.validation.url.regex\": \"值必须为有效的网址\",\n    \"Settings.webhooks.validation.key\": \"密钥是必需的\",\n    \"Settings.webhooks.validation.value\": \"值是必需的\",\n    \"Usecase.back-end\": \"后端开发者\",\n    \"Usecase.button.skip\": \"跳过此问题\",\n    \"Usecase.content-creator\": \"内容创建者\",\n    \"Usecase.front-end\": \"前端开发者\",\n    \"Usecase.full-stack\": \"全栈开发者\",\n    \"Usecase.input.work-type\": \"您的工作类型是什么?\",\n    \"Usecase.notification.success.project-created\": \"项目已成功创建\",\n    \"Usecase.other\": \"其他\",\n    \"Usecase.title\": \"告诉我们更多关于您自己的信息\",\n    Username: Username,\n    \"Users & Permissions\": \"用户和权限\",\n    Users: Users,\n    \"Users.components.List.empty\": \"没有用户...\",\n    \"Users.components.List.empty.withFilters\": \"使用应用的筛选条件没有用户...\",\n    \"Users.components.List.empty.withSearch\": \"没有与搜索({search})对应的用户...\",\n    \"admin.pages.MarketPlacePage.sort.label\": \"排序方式\",\n    \"admin.pages.MarketPlacePage.filters.categories\": \"类别\",\n    \"admin.pages.MarketPlacePage.filters.categoriesSelected\": \"选择了{count, plural, =0 {无类别} one {# 个类别} other {# 个类别}}\",\n    \"admin.pages.MarketPlacePage.filters.collections\": \"集合\",\n    \"admin.pages.MarketPlacePage.filters.collectionsSelected\": \"选择了{count, plural, =0 {无集合} one {# 个集合} other {# 个集合}}\",\n    \"admin.pages.MarketPlacePage.helmet\": \"市场 - 插件\",\n    \"admin.pages.MarketPlacePage.missingPlugin.description\": \"告诉我们您正在寻找的插件,这样如果社区的插件开发者也在寻找灵感,我们可以让他们知道!\",\n    \"admin.pages.MarketPlacePage.missingPlugin.title\": \"缺少插件?\",\n    \"admin.pages.MarketPlacePage.offline.subtitle\": \"您需要连接到互联网才能访问Strapi市场。\",\n    \"admin.pages.MarketPlacePage.offline.title\": \"您处于离线状态\",\n    \"admin.pages.MarketPlacePage.plugin.copy\": \"复制安装命令\",\n    \"admin.pages.MarketPlacePage.plugin.copy.success\": \"安装命令已准备好粘贴到终端中\",\n    \"admin.pages.MarketPlacePage.plugin.downloads\": \"此插件的每周下载量为{downloadsCount}\",\n    \"admin.pages.MarketPlacePage.plugin.githubStars\": \"此插件在GitHub上获得了{starsCount}星\",\n    \"admin.pages.MarketPlacePage.plugin.info\": \"了解更多\",\n    \"admin.pages.MarketPlacePage.plugin.info.label\": \"了解更多关于{pluginName}的信息\",\n    \"admin.pages.MarketPlacePage.plugin.info.text\": \"更多\",\n    \"admin.pages.MarketPlacePage.plugin.installed\": \"已安装\",\n    \"admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi\": \"由Strapi制作\",\n    \"admin.pages.MarketPlacePage.plugin.tooltip.verified\": \"插件经过Strapi验证\",\n    \"admin.pages.MarketPlacePage.plugin.version\": \"请更新Strapi版本: \\\"{strapiAppVersion}\\\" 至 \\\"{versionRange}\\\"\",\n    \"admin.pages.MarketPlacePage.plugin.version.null\": \"无法验证与Strapi版本的兼容性: \\\"{strapiAppVersion}\\\"\",\n    \"admin.pages.MarketPlacePage.plugins\": \"插件\",\n    \"admin.pages.MarketPlacePage.provider.downloads\": \"此提供商有{downloadsCount}次每周下载\",\n    \"admin.pages.MarketPlacePage.provider.githubStars\": \"此提供商在GitHub上获得了{starsCount}星\",\n    \"admin.pages.MarketPlacePage.providers\": \"提供商\",\n    \"admin.pages.MarketPlacePage.search.clear\": \"清除搜索\",\n    \"admin.pages.MarketPlacePage.search.empty\": \"没有\\\"{target}\\\"的结果\",\n    \"admin.pages.MarketPlacePage.search.placeholder\": \"搜索\",\n    \"admin.pages.MarketPlacePage.sort.alphabetical\": \"字母顺序\",\n    \"admin.pages.MarketPlacePage.sort.alphabetical.selected\": \"按字母顺序排序\",\n    \"admin.pages.MarketPlacePage.sort.githubStars\": \"GitHub星数\",\n    \"admin.pages.MarketPlacePage.sort.githubStars.selected\": \"按GitHub星数排序\",\n    \"admin.pages.MarketPlacePage.sort.newest\": \"最新\",\n    \"admin.pages.MarketPlacePage.sort.newest.selected\": \"按最新排序\",\n    \"admin.pages.MarketPlacePage.sort.npmDownloads\": \"下载次数\",\n    \"admin.pages.MarketPlacePage.sort.npmDownloads.selected\": \"按npm下载量排序\",\n    \"admin.pages.MarketPlacePage.submit.plugin.link\": \"提交插件\",\n    \"admin.pages.MarketPlacePage.submit.provider.link\": \"提交提供商\",\n    \"admin.pages.MarketPlacePage.subtitle\": \"轻松扩展您的应用程序\",\n    \"admin.pages.MarketPlacePage.tab-group.label\": \"适用于Strapi的插件和提供商\",\n    anErrorOccurred: anErrorOccurred,\n    \"app.component.CopyToClipboard.label\": \"复制到剪贴板\",\n    \"app.component.search.label\": \"搜索{target}\",\n    \"app.component.table.duplicate\": \"重复{target}\",\n    \"app.component.table.edit\": \"编辑{target}\",\n    \"app.component.table.read\": \"读取{target}\",\n    \"app.component.table.select.one-entry\": \"选择{target}\",\n    \"app.component.table.view\": \"{target} 详情\",\n    \"app.components.BlockLink.blog\": \"博客\",\n    \"app.components.BlockLink.blog.content\": \"阅读关于Strapi和生态系统的最新新闻。\",\n    \"app.components.BlockLink.cloud\": \"Strapi云\",\n    \"app.components.BlockLink.cloud.content\": \"一个完全可组合且协作的平台,可提高团队的工作效率。\",\n    \"app.components.BlockLink.code\": \"代码示例\",\n    \"app.components.BlockLink.code.content\": \"通过测试社区开发的真实项目来学习。\",\n    \"app.components.BlockLink.documentation.content\": \"发现基本概念、指南和说明。\",\n    \"app.components.BlockLink.tutorial\": \"教程\",\n    \"app.components.BlockLink.tutorial.content\": \"按照步骤说明使用和自定义Strapi。\",\n    \"app.components.Button.cancel\": \"取消\",\n    \"app.components.Button.confirm\": \"确认\",\n    \"app.components.Button.reset\": \"重置\",\n    \"app.components.ComingSoonPage.comingSoon\": \"即将推出\",\n    \"app.components.ConfirmDialog.title\": \"确认\",\n    \"app.components.DownloadInfo.download\": \"下载进行中...\",\n    \"app.components.DownloadInfo.text\": \"这可能需要一分钟。感谢您的耐心等待。\",\n    \"app.components.EmptyAttributes.title\": \"还没有字段\",\n    \"app.components.EmptyStateLayout.content-document\": \"找不到内容\",\n    \"app.components.EmptyStateLayout.content-permissions\": \"您没有权限访问该内容\",\n    \"app.components.GuidedTour.CM.create.content\": \"<p>在此处的内容管理器中创建和管理所有内容。</p><p>例如:进一步考虑博客网站的例子,可以根据喜好编写、保存和发布文章。</p><p>💡 快速提示 — 不要忘记发布您创建的内容。</p>\",\n    \"app.components.GuidedTour.CM.create.title\": \"⚡️ 创建内容\",\n    \"app.components.GuidedTour.CM.success.content\": \"<p>太棒了,还剩最后一步!</p><b>🚀 查看实际内容</b>\",\n    \"app.components.GuidedTour.CM.success.cta.title\": \"测试API\",\n    \"app.components.GuidedTour.CM.success.title\": \"第2步:完成 ✅\",\n    \"app.components.GuidedTour.CTB.create.content\": \"<p>集合类型有助于管理多个条目,单个类型适合管理仅一个条目。</p> <p>例如:对于博客网站,文章将是集合类型,而首页将是单个类型。</p>\",\n    \"app.components.GuidedTour.CTB.create.cta.title\": \"构建一个集合类型\",\n    \"app.components.GuidedTour.CTB.create.title\": \"🧠 创建第一个集合类型\",\n    \"app.components.GuidedTour.CTB.success.content\": \"<p>干得好!</p><b>⚡️ 您想与世界分享什么?</b>\",\n    \"app.components.GuidedTour.CTB.success.title\": \"第1步:完成 ✅\",\n    \"app.components.GuidedTour.apiTokens.create.content\": \"<p>在此生成认证令牌并检索您刚创建的内容。</p>\",\n    \"app.components.GuidedTour.apiTokens.create.cta.title\": \"生成API令牌\",\n    \"app.components.GuidedTour.apiTokens.create.title\": \"🚀 查看实际内容\",\n    \"app.components.GuidedTour.apiTokens.success.content\": \"<p>通过发出HTTP请求查看实际内容:</p><ul><li><p>网址: <light>https://'<'YOUR_DOMAIN'>'/api/'<'YOUR_CT'>'</light></p></li><li><p>标头: <light>Authorization: bearer '<'YOUR_API_TOKEN'>'</light></p></li></ul><p>有关与内容交互的更多方式,请参阅<documentationLink>文档</documentationLink>。</p>\",\n    \"app.components.GuidedTour.apiTokens.success.cta.title\": \"返回主页\",\n    \"app.components.GuidedTour.apiTokens.success.title\": \"第3步:完成 ✅\",\n    \"app.components.GuidedTour.create-content\": \"创建内容\",\n    \"app.components.GuidedTour.home.CM.title\": \"⚡️ 您想与世界分享什么?\",\n    \"app.components.GuidedTour.home.CTB.cta.title\": \"转到内容类型构建器\",\n    \"app.components.GuidedTour.home.CTB.title\": \"🧠 构建内容结构\",\n    \"app.components.GuidedTour.home.apiTokens.cta.title\": \"测试API\",\n    \"app.components.GuidedTour.skip\": \"跳过导览\",\n    \"app.components.GuidedTour.title\": \"开始的3个步骤\",\n    \"app.components.HomePage.button.blog\": \"在博客上查看更多信息\",\n    \"app.components.HomePage.community\": \"加入社区\",\n    \"app.components.HomePage.community.content\": \"在不同的渠道中与团队成员、贡献者和开发人员进行讨论。\",\n    \"app.components.HomePage.create\": \"创建您的第一个内容类型\",\n    \"app.components.HomePage.roadmap\": \"查看路线图\",\n    \"app.components.HomePage.welcome\": \"欢迎登机 👋\",\n    \"app.components.HomePage.welcome.again\": \"欢迎 👋\",\n    \"app.components.HomePage.welcomeBlock.content\": \"恭喜您!您已登录为第一个管理员。为了发现Strapi提供的强大功能,我们建议您创建您的第一个内容类型!\",\n    \"app.components.HomePage.welcomeBlock.content.again\": \"我们希望您在项目上取得进展!请随时阅读关于Strapi的最新新闻。我们正根据您的反馈尽最大努力改进产品。\",\n    \"app.components.HomePage.welcomeBlock.content.issues\": \"问题。\",\n    \"app.components.HomePage.welcomeBlock.content.raise\": \" 或提出 \",\n    \"app.components.ImgPreview.hint\": \"将文件拖放到此区域或{browse}上传文件\",\n    \"app.components.ImgPreview.hint.browse\": \"浏览\",\n    \"app.components.InputFile.newFile\": \"添加新文件\",\n    \"app.components.InputFileDetails.open\": \"在新标签页中打开\",\n    \"app.components.InputFileDetails.originalName\": \"原始名称:\",\n    \"app.components.InputFileDetails.remove\": \"删除此文件\",\n    \"app.components.InputFileDetails.size\": \"大小:\",\n    \"app.components.InstallPluginPage.Download.description\": \"下载和安装插件可能需要几秒钟。\",\n    \"app.components.InstallPluginPage.Download.title\": \"正在下载...\",\n    \"app.components.InstallPluginPage.description\": \"轻松扩展您的应用程序。\",\n    \"app.components.LeftMenu.collapse\": \"折叠导航栏\",\n    \"app.components.LeftMenu.expand\": \"展开导航栏\",\n    \"app.components.LeftMenu.general\": \"常规\",\n    \"app.components.LeftMenu.logo.alt\": \"应用程序徽标\",\n    \"app.components.LeftMenu.logout\": \"登出\",\n    \"app.components.LeftMenu.navbrand.title\": \"Strapi控制面板\",\n    \"app.components.LeftMenu.navbrand.workplace\": \"工作区\",\n    \"app.components.LeftMenu.plugins\": \"插件\",\n    \"app.components.LeftMenuFooter.help\": \"帮助\",\n    \"app.components.LeftMenuFooter.poweredBy\": \"技术支持 \",\n    \"app.components.LeftMenuLinkContainer.collectionTypes\": \"集合类型\",\n    \"app.components.LeftMenuLinkContainer.configuration\": \"配置\",\n    \"app.components.LeftMenuLinkContainer.general\": \"常规\",\n    \"app.components.LeftMenuLinkContainer.noPluginsInstalled\": \"还未安装插件\",\n    \"app.components.LeftMenuLinkContainer.plugins\": \"插件\",\n    \"app.components.LeftMenuLinkContainer.singleTypes\": \"单个类型\",\n    \"app.components.ListPluginsPage.deletePlugin.description\": \"卸载插件可能需要几秒钟。\",\n    \"app.components.ListPluginsPage.deletePlugin.title\": \"卸载中\",\n    \"app.components.ListPluginsPage.description\": \"项目中安装的插件列表。\",\n    \"app.components.ListPluginsPage.helmet.title\": \"插件列表\",\n    \"app.components.Logout.logout\": \"登出\",\n    \"app.components.Logout.profile\": \"简介\",\n    \"app.components.MarketplaceBanner\": \"在Strapi市场上发现社区构建的插件,以及许多其他很酷的东西来启动您的项目。\",\n    \"app.components.MarketplaceBanner.image.alt\": \"一个Strapi火箭标志\",\n    \"app.components.MarketplaceBanner.link\": \"立即查看\",\n    \"app.components.NotFoundPage.back\": \"返回主页\",\n    \"app.components.NotFoundPage.description\": \"未找到\",\n    \"app.components.Official\": \"官方\",\n    \"app.components.Onboarding.help.button\": \"帮助按钮\",\n    \"app.components.Onboarding.label.completed\": \"% 已完成\",\n    \"app.components.Onboarding.link.build-content\": \"构建内容架构\",\n    \"app.components.Onboarding.link.manage-content\": \"添加和管理内容\",\n    \"app.components.Onboarding.link.manage-media\": \"管理媒体\",\n    \"app.components.Onboarding.link.more-videos\": \"观看更多视频\",\n    \"app.components.Onboarding.title\": \"入门视频\",\n    \"app.components.PluginCard.Button.label.download\": \"下载\",\n    \"app.components.PluginCard.Button.label.install\": \"已安装\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed\": \"需要启用autoReload功能。请使用 yarn develop 启动您的应用程序。\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.confirm\": \"我明白!\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.environment\": \"出于安全考虑,只能在开发环境中下载插件。\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.title\": \"无法下载\",\n    \"app.components.PluginCard.compatible\": \"与您的应用程序兼容\",\n    \"app.components.PluginCard.compatibleCommunity\": \"与社区兼容\",\n    \"app.components.PluginCard.more-details\": \"更多详情\",\n    \"app.components.ToggleCheckbox.off-label\": \"否\",\n    \"app.components.ToggleCheckbox.on-label\": \"是\",\n    \"app.components.Users.MagicLink.connect\": \"复制并共享此链接以授予此用户访问权限\",\n    \"app.components.Users.MagicLink.connect.sso\": \"将此链接发送给用户,首次登录可以通过SSO提供商完成\",\n    \"app.components.Users.ModalCreateBody.block-title.details\": \"用户详情\",\n    \"app.components.Users.ModalCreateBody.block-title.roles\": \"用户的角色\",\n    \"app.components.Users.ModalCreateBody.block-title.roles.description\": \"用户可以拥有一个或多个角色\",\n    \"app.components.Users.SortPicker.button-label\": \"排序方式\",\n    \"app.components.Users.SortPicker.sortby.email_asc\": \"电子邮件(A至Z)\",\n    \"app.components.Users.SortPicker.sortby.email_desc\": \"电子邮件(Z至A)\",\n    \"app.components.Users.SortPicker.sortby.firstname_asc\": \"名字(A至Z)\",\n    \"app.components.Users.SortPicker.sortby.firstname_desc\": \"名字(Z至A)\",\n    \"app.components.Users.SortPicker.sortby.lastname_asc\": \"姓氏(A至Z)\",\n    \"app.components.Users.SortPicker.sortby.lastname_desc\": \"姓氏(Z至A)\",\n    \"app.components.Users.SortPicker.sortby.username_asc\": \"用户名(A至Z)\",\n    \"app.components.Users.SortPicker.sortby.username_desc\": \"用户名(Z至A)\",\n    \"app.components.listPlugins.button\": \"新增插件\",\n    \"app.components.listPlugins.title.none\": \"未安装插件\",\n    \"app.components.listPluginsPage.deletePlugin.error\": \"卸载插件时出错\",\n    \"app.containers.App.notification.error.init\": \"请求API时出错\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin\": \"如果您没有收到此链接,请联系您的管理员。\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.text.email\": \"重置密码链接需要几分钟才能收到。\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.title\": \"电子邮件已发送\",\n    \"app.containers.Users.EditPage.form.active.label\": \"激活\",\n    \"app.containers.Users.EditPage.header.label\": \"编辑 {name}\",\n    \"app.containers.Users.EditPage.header.label-loading\": \"编辑用户\",\n    \"app.containers.Users.EditPage.roles-bloc-title\": \"分配的角色\",\n    \"app.containers.Users.ModalForm.footer.button-success\": \"邀请用户\",\n    \"app.links.configure-view\": \"配置视图\",\n    \"app.page.not.found\": \"哎呀!我们似乎找不到您要查找的页面...\",\n    \"app.static.links.cheatsheet\": \"速查表\",\n    \"app.utils.SelectOption.defaultMessage\": \" \",\n    \"app.utils.add-filter\": \"添加筛选器\",\n    \"app.utils.close-label\": \"关闭\",\n    \"app.utils.defaultMessage\": \" \",\n    \"app.utils.delete\": \"删除\",\n    \"app.utils.duplicate\": \"重复\",\n    \"app.utils.edit\": \"编辑\",\n    \"app.utils.errors.file-too-big.message\": \"文件太大\",\n    \"app.utils.filter-value\": \"筛选值\",\n    \"app.utils.filters\": \"筛选器\",\n    \"app.utils.notify.data-loaded\": \"{target}已加载\",\n    \"app.utils.placeholder.defaultMessage\": \" \",\n    \"app.utils.publish\": \"发布\",\n    \"app.utils.select-all\": \"全选\",\n    \"app.utils.select-field\": \"选择字段\",\n    \"app.utils.select-filter\": \"选择筛选器\",\n    \"app.utils.unpublish\": \"取消发布\",\n    clearLabel: clearLabel,\n    \"coming.soon\": \"此内容目前正在建设中,将在几周内重新上线!\",\n    \"component.Input.error.validation.integer\": \"值必须为整数\",\n    \"components.AutoReloadBlocker.description\": \"使用以下命令之一运行Strapi:\",\n    \"components.AutoReloadBlocker.header\": \"此插件需要重载功能。\",\n    \"components.ErrorBoundary.title\": \"出了些问题...\",\n    \"components.FilterOptions.FILTER_TYPES.$contains\": \"包含(区分大小写)\",\n    \"components.FilterOptions.FILTER_TYPES.$endsWith\": \"结尾为\",\n    \"components.FilterOptions.FILTER_TYPES.$eq\": \"等于\",\n    \"components.FilterOptions.FILTER_TYPES.$gt\": \"大于\",\n    \"components.FilterOptions.FILTER_TYPES.$gte\": \"大于或等于\",\n    \"components.FilterOptions.FILTER_TYPES.$lt\": \"小于\",\n    \"components.FilterOptions.FILTER_TYPES.$lte\": \"小于或等于\",\n    \"components.FilterOptions.FILTER_TYPES.$ne\": \"不等于\",\n    \"components.FilterOptions.FILTER_TYPES.$notContains\": \"不包含(区分大小写)\",\n    \"components.FilterOptions.FILTER_TYPES.$notNull\": \"不为空\",\n    \"components.FilterOptions.FILTER_TYPES.$null\": \"为空\",\n    \"components.FilterOptions.FILTER_TYPES.$startsWith\": \"开头为\",\n    \"components.Input.error.attribute.key.taken\": \"此值已存在\",\n    \"components.Input.error.attribute.sameKeyAndName\": \"不能相等\",\n    \"components.Input.error.attribute.taken\": \"此字段名称已存在\",\n    \"components.Input.error.contain.lowercase\": \"密码必须包含至少一个小写字符\",\n    \"components.Input.error.contain.number\": \"密码必须包含至少一个数字\",\n    \"components.Input.error.contain.uppercase\": \"密码必须包含至少一个大写字符\",\n    \"components.Input.error.contentTypeName.taken\": \"此名称已被使用\",\n    \"components.Input.error.custom-error\": \"{errorMessage} \",\n    \"components.Input.error.password.noMatch\": \"密码不匹配\",\n    \"components.Input.error.validation.email\": \"这是一个无效的电子邮件\",\n    \"components.Input.error.validation.json\": \"这与JSON格式不匹配\",\n    \"components.Input.error.validation.lowercase\": \"值必须为小写字符串\",\n    \"components.Input.error.validation.max\": \"值太大(最大值:{max})。\",\n    \"components.Input.error.validation.maxLength\": \"值太长(最大长度:{max})。\",\n    \"components.Input.error.validation.min\": \"值太小(最小值:{min})。\",\n    \"components.Input.error.validation.minLength\": \"值太短(最小长度:{min})。\",\n    \"components.Input.error.validation.minSupMax\": \"不能高于\",\n    \"components.Input.error.validation.regex\": \"值与正则表达式不匹配。\",\n    \"components.Input.error.validation.required\": \"此值是必需的。\",\n    \"components.Input.error.validation.unique\": \"此值已被使用。\",\n    \"components.InputSelect.option.placeholder\": \"请选择\",\n    \"components.ListRow.empty\": \"没有要显示的数据。\",\n    \"components.NotAllowedInput.text\": \"无权查看此字段\",\n    \"components.OverlayBlocker.description\": \"您正在使用需要服务器重启的功能。请等到服务器启动。\",\n    \"components.OverlayBlocker.description.serverError\": \"服务器本应重启,请在终端中检查日志。\",\n    \"components.OverlayBlocker.title\": \"等待重启...\",\n    \"components.OverlayBlocker.title.serverError\": \"重启花费的时间比预期更长\",\n    \"components.PageFooter.select\": \"每页条目数\",\n    \"components.ProductionBlocker.description\": \"出于安全考虑,我们必须在其他环境中禁用此插件。\",\n    \"components.ProductionBlocker.header\": \"此插件仅在开发环境中可用。\",\n    \"components.Search.placeholder\": \"搜索...\",\n    \"components.TableHeader.sort\": \"按{label}排序\",\n    \"components.Wysiwyg.ToggleMode.markdown-mode\": \"Markdown模式\",\n    \"components.Wysiwyg.ToggleMode.preview-mode\": \"预览模式\",\n    \"components.Wysiwyg.collapse\": \"折叠\",\n    \"components.Wysiwyg.selectOptions.H1\": \"标题 H1\",\n    \"components.Wysiwyg.selectOptions.H2\": \"标题 H2\",\n    \"components.Wysiwyg.selectOptions.H3\": \"标题 H3\",\n    \"components.Wysiwyg.selectOptions.H4\": \"标题 H4\",\n    \"components.Wysiwyg.selectOptions.H5\": \"标题 H5\",\n    \"components.Wysiwyg.selectOptions.H6\": \"标题 H6\",\n    \"components.Wysiwyg.selectOptions.title\": \"添加标题\",\n    \"components.WysiwygBottomControls.charactersIndicators\": \"字符\",\n    \"components.WysiwygBottomControls.fullscreen\": \"展开\",\n    \"components.WysiwygBottomControls.uploadFiles\": \"拖放文件、从剪贴板粘贴或 {browse}。\",\n    \"components.WysiwygBottomControls.uploadFiles.browse\": \"选择它们\",\n    \"components.pagination.go-to\": \"转到第{page}页\",\n    \"components.pagination.go-to-next\": \"转到下一页\",\n    \"components.pagination.go-to-previous\": \"转到上一页\",\n    \"components.pagination.remaining-links\": \"还有 {number} 个其它链接\",\n    \"components.popUpWarning.button.cancel\": \"不,取消\",\n    \"components.popUpWarning.button.confirm\": \"是的,确认\",\n    \"components.popUpWarning.message\": \"您确定要删除此内容吗?\",\n    \"components.popUpWarning.title\": \"请确认\",\n    \"content-manager.App.schemas.data-loaded\": \"模式已成功加载\",\n    \"content-manager.DynamicTable.relation-loaded\": \"关系已加载\",\n    \"content-manager.DynamicTable.relation-loading\": \"关系正在加载\",\n    \"content-manager.DynamicTable.relation-more\": \"此关系包含比显示的更多实体\",\n    \"content-manager.EditRelations.title\": \"关系数据\",\n    \"content-manager.HeaderLayout.button.label-add-entry\": \"创建新条目\",\n    \"content-manager.api.id\": \"API ID\",\n    \"content-manager.apiError.This attribute must be unique\": \"{field}必须唯一\",\n    \"content-manager.components.AddFilterCTA.add\": \"筛选器\",\n    \"content-manager.components.AddFilterCTA.hide\": \"筛选器\",\n    \"content-manager.components.DragHandle-label\": \"拖动\",\n    \"content-manager.components.DraggableAttr.edit\": \"点击编辑\",\n    \"content-manager.components.DraggableCard.delete.field\": \"删除 {item}\",\n    \"content-manager.components.DraggableCard.edit.field\": \"编辑 {item}\",\n    \"content-manager.components.DraggableCard.move.field\": \"移动 {item}\",\n    \"content-manager.components.DynamicTable.row-line\": \"{number} 行\",\n    \"content-manager.components.DynamicZone.ComponentPicker-label\": \"选择一个组件\",\n    \"content-manager.components.DynamicZone.add-component\": \"向 {componentName} 添加组件\",\n    \"content-manager.components.DynamicZone.delete-label\": \"删除 {name}\",\n    \"content-manager.components.DynamicZone.error-message\": \"组件包含错误\",\n    \"content-manager.components.DynamicZone.missing-components\": \"缺少 {number, plural, =0 {# 个组件} other {# 个组件}}\",\n    \"content-manager.components.DynamicZone.move-down-label\": \"向下移动组件\",\n    \"content-manager.components.DynamicZone.move-up-label\": \"向上移动组件\",\n    \"content-manager.components.DynamicZone.pick-compo\": \"选择一个组件\",\n    \"content-manager.components.DynamicZone.required\": \"组件是必需的\",\n    \"content-manager.components.EmptyAttributesBlock.button\": \"转到设置页面\",\n    \"content-manager.components.EmptyAttributesBlock.description\": \"您可以更改设置\",\n    \"content-manager.components.FieldItem.linkToComponentLayout\": \"设置组件布局\",\n    \"content-manager.components.FieldSelect.label\": \"添加字段\",\n    \"content-manager.components.FilterOptions.button.apply\": \"应用\",\n    \"content-manager.components.FiltersPickWrapper.PluginHeader.actions.apply\": \"应用\",\n    \"content-manager.components.FiltersPickWrapper.PluginHeader.actions.clearAll\": \"全部清除\",\n    \"content-manager.components.FiltersPickWrapper.PluginHeader.description\": \"设置用于筛选条目的条件\",\n    \"content-manager.components.FiltersPickWrapper.PluginHeader.title.filter\": \"筛选器\",\n    \"content-manager.components.FiltersPickWrapper.hide\": \"隐藏\",\n    \"content-manager.components.LeftMenu.Search.label\": \"搜索内容类型\",\n    \"content-manager.components.LeftMenu.collection-types\": \"集合类型\",\n    \"content-manager.components.LeftMenu.single-types\": \"单个类型\",\n    \"content-manager.components.LimitSelect.itemsPerPage\": \"每页条目数\",\n    \"content-manager.components.NotAllowedInput.text\": \"无权查看此字段\",\n    \"content-manager.components.RelationInput.icon-button-aria-label\": \"拖动\",\n    \"content-manager.components.RepeatableComponent.error-message\": \"组件包含错误\",\n    \"content-manager.components.Search.placeholder\": \"搜索条目...\",\n    \"content-manager.components.Select.draft-info-title\": \"状态:草稿\",\n    \"content-manager.components.Select.publish-info-title\": \"状态:已发布\",\n    \"content-manager.components.SettingsViewWrapper.pluginHeader.description.edit-settings\": \"自定义编辑视图的外观。\",\n    \"content-manager.components.SettingsViewWrapper.pluginHeader.description.list-settings\": \"为此集合类型定义列表视图的设置。\",\n    \"content-manager.components.SettingsViewWrapper.pluginHeader.title\": \"配置视图 —— {name}\",\n    \"content-manager.components.TableDelete.delete\": \"全部删除\",\n    \"content-manager.components.TableDelete.deleteSelected\": \"删除所选\",\n    \"content-manager.components.TableDelete.label\": \"选择了 {number, plural, one {1条} other {#条}} \",\n    \"content-manager.components.TableEmpty.withFilters\": \"应用筛选条件后没有 {contentType}...\",\n    \"content-manager.components.TableEmpty.withSearch\": \"没有与搜索 ({search}) 对应的 {contentType}...\",\n    \"content-manager.components.TableEmpty.withoutFilter\": \"没有 {contentType}...\",\n    \"content-manager.components.empty-repeatable\": \"还没有条目。点击下面的按钮添加。\",\n    \"content-manager.components.notification.info.maximum-requirement\": \"您已达到字段的最大数量\",\n    \"content-manager.components.notification.info.minimum-requirement\": \"已添加字段以匹配最低要求\",\n    \"content-manager.components.repeatable.reorder.error\": \"重新排序组件字段时出错,请重试\",\n    \"content-manager.components.reset-entry\": \"重置条目\",\n    \"content-manager.components.uid.apply\": \"应用\",\n    \"content-manager.components.uid.available\": \"可用\",\n    \"content-manager.components.uid.regenerate\": \"重新生成\",\n    \"content-manager.components.uid.suggested\": \"建议\",\n    \"content-manager.components.uid.unavailable\": \"不可用\",\n    \"content-manager.containers.Edit.Link.Layout\": \"配置布局\",\n    \"content-manager.containers.Edit.Link.Model\": \"编辑集合类型\",\n    \"content-manager.containers.Edit.addAnItem\": \"添加项目...\",\n    \"content-manager.containers.Edit.clickToJump\": \"点击跳转到条目\",\n    \"content-manager.containers.Edit.delete\": \"删除\",\n    \"content-manager.containers.Edit.delete-entry\": \"删除此条目\",\n    \"content-manager.containers.Edit.editing\": \"编辑中...\",\n    \"content-manager.containers.Edit.information\": \"信息\",\n    \"content-manager.containers.Edit.information.by\": \"由\",\n    \"content-manager.containers.Edit.information.created\": \"已创建\",\n    \"content-manager.containers.Edit.information.draftVersion\": \"草稿版本\",\n    \"content-manager.containers.Edit.information.editing\": \"编辑\",\n    \"content-manager.containers.Edit.information.lastUpdate\": \"最后更新\",\n    \"content-manager.containers.Edit.information.publishedVersion\": \"已发布版本\",\n    \"content-manager.containers.Edit.pluginHeader.title.new\": \"创建条目\",\n    \"content-manager.containers.Edit.reset\": \"重置\",\n    \"content-manager.containers.Edit.returnList\": \"返回列表\",\n    \"content-manager.containers.Edit.seeDetails\": \"详情\",\n    \"content-manager.containers.Edit.submit\": \"保存\",\n    \"content-manager.containers.EditSettingsView.modal-form.edit-field\": \"编辑字段\",\n    \"content-manager.containers.EditView.add.new-entry\": \"添加条目\",\n    \"content-manager.containers.EditView.notification.errors\": \"表单包含一些错误\",\n    \"content-manager.containers.Home.introduction\": \"要编辑条目,请转到左侧菜单中的特定链接。此插件没有适当的方式来编辑设置,它仍在积极开发中。\",\n    \"content-manager.containers.Home.pluginHeaderDescription\": \"通过强大、美观的界面管理条目。\",\n    \"content-manager.containers.Home.pluginHeaderTitle\": \"内容管理器\",\n    \"content-manager.containers.List.draft\": \"草稿\",\n    \"content-manager.containers.List.errorFetchRecords\": \"错误\",\n    \"content-manager.containers.List.published\": \"已发布\",\n    \"content-manager.containers.list.displayedFields\": \"显示的字段\",\n    \"content-manager.containers.list.items\": \"{number, plural, =0 {项目} one {项目} other {项目}}\",\n    \"content-manager.containers.list.table-headers.publishedAt\": \"状态\",\n    \"content-manager.containers.ListSettingsView.modal-form.edit-label\": \"编辑 {fieldName}\",\n    \"content-manager.containers.SettingPage.add.field\": \"插入其他字段\",\n    \"content-manager.containers.SettingPage.add.relational-field\": \"插入其他相关字段\",\n    \"content-manager.containers.SettingPage.attributes\": \"属性字段\",\n    \"content-manager.containers.SettingPage.attributes.description\": \"定义属性顺序\",\n    \"content-manager.containers.SettingPage.editSettings.description\": \"拖放字段构建布局\",\n    \"content-manager.containers.SettingPage.editSettings.entry.title\": \"条目标题\",\n    \"content-manager.containers.SettingPage.editSettings.entry.title.description\": \"设置条目显示的字段\",\n    \"content-manager.containers.SettingPage.editSettings.relation-field.description\": \"在编辑视图和列表视图中设置显示的字段\",\n    \"content-manager.containers.SettingPage.editSettings.title\": \"编辑视图(设置)\",\n    \"content-manager.containers.SettingPage.layout\": \"布局\",\n    \"content-manager.containers.SettingPage.listSettings.description\": \"为此集合类型配置选项\",\n    \"content-manager.containers.SettingPage.listSettings.title\": \"列表视图(设置)\",\n    \"content-manager.containers.SettingPage.pluginHeaderDescription\": \"为此集合类型配置特定设置\",\n    \"content-manager.containers.SettingPage.relations\": \"相关字段\",\n    \"content-manager.containers.SettingPage.settings\": \"设置\",\n    \"content-manager.containers.SettingPage.view\": \"视图\",\n    \"content-manager.containers.SettingViewModel.pluginHeader.title\": \"内容管理器 —— {name}\",\n    \"content-manager.containers.SettingsPage.Block.contentType.description\": \"配置特定设置\",\n    \"content-manager.containers.SettingsPage.Block.contentType.title\": \"集合类型\",\n    \"content-manager.containers.SettingsPage.Block.generalSettings.description\": \"为您的集合类型配置默认选项\",\n    \"content-manager.containers.SettingsPage.Block.generalSettings.title\": \"常规\",\n    \"content-manager.containers.SettingsPage.pluginHeaderDescription\": \"为您的所有集合类型和分组配置设置\",\n    \"content-manager.containers.SettingsView.list.subtitle\": \"配置集合类型和分组的布局和显示\",\n    \"content-manager.containers.SettingsView.list.title\": \"显示配置\",\n    \"content-manager.dnd.cancel-item\": \"{item}已放弃。重新排序已取消。\",\n    \"content-manager.dnd.drop-item\": \"{item}已放置。列表中的最终位置:{position}。\",\n    \"content-manager.dnd.grab-item\": \"{item}已抓取。当前列表位置:{position}。按上下箭头更改位置,空格键放置,Esc键取消。\",\n    \"content-manager.dnd.instructions\": \"按空格键抓取和重新排序\",\n    \"content-manager.dnd.reorder\": \"{item}已移动。新列表位置:{position}。\",\n    \"content-manager.edit-settings-view.link-to-ctb.components\": \"编辑组件\",\n    \"content-manager.edit-settings-view.link-to-ctb.content-types\": \"编辑内容类型\",\n    \"content-manager.emptyAttributes.button\": \"转到集合类型构建器\",\n    \"content-manager.emptyAttributes.description\": \"向您的集合类型添加第一个字段\",\n    \"content-manager.emptyAttributes.title\": \"还没有字段\",\n    \"content-manager.error.attribute.key.taken\": \"此值已存在\",\n    \"content-manager.error.attribute.sameKeyAndName\": \"不能相等\",\n    \"content-manager.error.attribute.taken\": \"此字段名称已存在\",\n    \"content-manager.error.contentTypeName.taken\": \"此名称已被使用\",\n    \"content-manager.error.model.fetch\": \"配置模型时发生错误。\",\n    \"content-manager.error.record.create\": \"创建记录时发生错误。\",\n    \"content-manager.error.record.delete\": \"删除记录时发生错误。\",\n    \"content-manager.error.record.fetch\": \"获取记录时发生错误。\",\n    \"content-manager.error.record.update\": \"更新记录时发生错误。\",\n    \"content-manager.error.records.count\": \"获取记录计数时发生错误。\",\n    \"content-manager.error.records.fetch\": \"获取记录时发生错误。\",\n    \"content-manager.error.schema.generation\": \"生成模式时发生错误。\",\n    \"content-manager.error.validation.json\": \"这不是JSON\",\n    \"content-manager.error.validation.max\": \"值太大(最大值:{max})。\",\n    \"content-manager.error.validation.maxLength\": \"值太长(最大长度:{max})。\",\n    \"content-manager.error.validation.min\": \"值太小(最小值:{min})。\",\n    \"content-manager.error.validation.minLength\": \"值太短(最小长度:{min})。\",\n    \"content-manager.error.validation.minSupMax\": \"不能高于\",\n    \"content-manager.error.validation.regex\": \"值与正则表达式不匹配。\",\n    \"content-manager.error.validation.required\": \"此值是必需的。\",\n    \"content-manager.error.validation.unique\": \"此值已被使用。\",\n    \"content-manager.form.Input.bulkActions\": \"启用批量操作\",\n    \"content-manager.form.Input.defaultSort\": \"默认排序属性\",\n    \"content-manager.form.Input.description\": \"描述\",\n    \"content-manager.form.Input.description.placeholder\": \"个人资料中的显示名称\",\n    \"content-manager.form.Input.editable\": \"可编辑字段\",\n    \"content-manager.form.Input.filters\": \"启用筛选器\",\n    \"content-manager.form.Input.hint.character.unit\": \"{maxValue, plural, one { 个字符} other { 个字符}}\",\n    \"content-manager.form.Input.hint.minMaxDivider\": \" / \",\n    \"content-manager.form.Input.hint.text\": \"{min, select, undefined {} other {最小. {min}}}{divider}{max, select, undefined {} other {最大. {max}}}{unit}{br}{description}\",\n    \"content-manager.form.Input.label\": \"标签\",\n    \"content-manager.form.Input.label.inputDescription\": \"此值会覆盖表头中显示的标签\",\n    \"content-manager.form.Input.pageEntries\": \"每页条目数\",\n    \"content-manager.form.Input.pageEntries.inputDescription\": \"注意:您可以在集合类型设置页面中覆盖此值。\",\n    \"content-manager.form.Input.placeholder\": \"占位符\",\n    \"content-manager.form.Input.placeholder.placeholder\": \"我的超赞值\",\n    \"content-manager.form.Input.search\": \"启用搜索\",\n    \"content-manager.form.Input.search.field\": \"在此字段上启用搜索\",\n    \"content-manager.form.Input.sort.field\": \"在此字段上启用排序\",\n    \"content-manager.form.Input.sort.order\": \"默认排序顺序\",\n    \"content-manager.form.Input.wysiwyg\": \"显示为所见即所得编辑器\",\n    \"content-manager.global.displayedFields\": \"显示的字段\",\n    \"content-manager.groups\": \"分组\",\n    \"content-manager.groups.numbered\": \"分组 ({number})\",\n    \"content-manager.header.name\": \"内容\",\n    \"content-manager.link-to-ctb\": \"编辑模型\",\n    \"content-manager.models\": \"集合类型\",\n    \"content-manager.models.numbered\": \"集合类型 ({number})\",\n    \"content-manager.notification.error.displayedFields\": \"您需要至少一个显示的字段\",\n    \"content-manager.notification.error.relationship.fetch\": \"获取关系时发生错误。\",\n    \"content-manager.notification.info.SettingPage.disableSort\": \"您需要具有至少一个允许排序的属性\",\n    \"content-manager.notification.info.minimumFields\": \"您至少需要显示一个字段\",\n    \"content-manager.notification.upload.error\": \"上传文件时发生错误\",\n    \"content-manager.pageNotFound\": \"页面未找到\",\n    \"content-manager.pages.ListView.header-subtitle\": \"找到 {number, plural, =0 {# 条} one {# 条} other {# 条}} \",\n    \"content-manager.pages.NoContentType.button\": \"创建您的第一个内容类型\",\n    \"content-manager.pages.NoContentType.text\": \"您还没有任何内容,我们建议您创建第一个内容类型。\",\n    \"content-manager.permissions.not-allowed.create\": \"您无权创建文档\",\n    \"content-manager.permissions.not-allowed.update\": \"您无权查看此文档\",\n    \"content-manager.plugin.description.long\": \"快速查看、编辑和删除数据库中的数据的方法。\",\n    \"content-manager.plugin.description.short\": \"快速查看、编辑和删除数据库中的数据的方法。\",\n    \"content-manager.popUpWarning.bodyMessage.contentType.delete\": \"是否确定删除内容类型?\",\n    \"content-manager.popUpWarning.bodyMessage.contentType.delete.all\": \"是否确定要删除这些条目?\",\n    \"content-manager.popUpWarning.bodyMessage.contentType.publish.all\": \"是否确定要发布这些条目?\",\n    \"content-manager.popUpWarning.bodyMessage.contentType.unpublish.all\": \"是否确定要取消发布这些条目?\",\n    \"content-manager.popUpWarning.warning.has-draft-relations.title\": \"确认\",\n    \"content-manager.popUpWarning.warning.publish-question\": \"您仍要发布吗?\",\n    \"content-manager.popUpWarning.warning.unpublish\": \"如果不发布此内容,它将自动变为草稿。\",\n    \"content-manager.popUpWarning.warning.unpublish-question\": \"您确定不想发布它吗?\",\n    \"content-manager.popUpWarning.warning.updateAllSettings\": \"这将修改您的所有设置\",\n    \"content-manager.popUpwarning.warning.has-draft-relations.button-confirm\": \"是的,发布\",\n    \"content-manager.popUpwarning.warning.has-draft-relations.message\": \"<b>{count, plural, one { 个关系} other { 个关系} }</b> 尚未发布,可能会导致意外行为。\",\n    \"content-manager.relation.add\": \"添加关系\",\n    \"content-manager.relation.disconnect\": \"移除\",\n    \"content-manager.relation.isLoading\": \"关系正在加载\",\n    \"content-manager.relation.loadMore\": \"加载更多\",\n    \"content-manager.relation.notAvailable\": \"没有可用关系\",\n    \"content-manager.relation.publicationState.draft\": \"草稿\",\n    \"content-manager.relation.publicationState.published\": \"已发布\",\n    \"content-manager.reviewWorkflows.stage.label\": \"审阅阶段\",\n    \"content-manager.select.currently.selected\": \"当前选择 {count} 条\",\n    \"content-manager.success.record.delete\": \"已删除\",\n    \"content-manager.success.record.publish\": \"已发布\",\n    \"content-manager.success.record.save\": \"已保存\",\n    \"content-manager.success.record.unpublish\": \"已取消发布\",\n    \"content-manager.utils.data-loaded\": \"{number, plural, =1 {条目} other {条目}}已成功加载\",\n    \"content-manager.listView.validation.errors.title\": \"需要操作\",\n    \"content-manager.listView.validation.errors.message\": \"请确保所有字段在发布之前都有效(必填字段、最小/最大字符数限制等)\",\n    dark: dark,\n    \"form.button.continue\": \"继续\",\n    \"form.button.done\": \"完成\",\n    \"global.actions\": \"操作\",\n    \"global.auditLogs\": \"审核日志\",\n    \"global.back\": \"返回\",\n    \"global.cancel\": \"取消\",\n    \"global.change-password\": \"更改密码\",\n    \"global.close\": \"关闭\",\n    \"global.content-manager\": \"内容管理器\",\n    \"global.continue\": \"继续\",\n    \"global.delete\": \"删除\",\n    \"global.delete-target\": \"删除 {target}\",\n    \"global.description\": \"描述\",\n    \"global.details\": \"详情\",\n    \"global.disabled\": \"已禁用\",\n    \"global.documentation\": \"文档\",\n    \"global.enabled\": \"已启用\",\n    \"global.finish\": \"完成\",\n    \"global.marketplace\": \"市场\",\n    \"global.name\": \"名称\",\n    \"global.none\": \"无\",\n    \"global.password\": \"密码\",\n    \"global.plugins\": \"插件\",\n    \"global.plugins.content-manager\": \"内容管理器\",\n    \"global.plugins.content-manager.description\": \"快速查看、编辑和删除数据库中的数据的方法。\",\n    \"global.plugins.content-type-builder\": \"内容类型构建器\",\n    \"global.plugins.content-type-builder.description\": \"为您的 API 建模数据结构。在短短一分钟内创建新的字段和关系。文件会自动在您的项目中创建和更新。\",\n    \"global.plugins.documentation\": \"文档\",\n    \"global.plugins.documentation.description\": \"创建 OpenAPI 文档并通过 SWAGGER UI 可视化您的 API。\",\n    \"global.plugins.email\": \"电子邮件\",\n    \"global.plugins.email.description\": \"配置您的应用程序以发送电子邮件。\",\n    \"global.plugins.graphql\": \"GraphQL\",\n    \"global.plugins.graphql.description\": \"添加具有默认 API 方法的 GraphQL 端点。\",\n    \"global.plugins.i18n\": \"国际化\",\n    \"global.plugins.i18n.description\": \"此插件可让您使用不同语言在管理面板和 API 中创建、读取和更新内容。\",\n    \"global.plugins.sentry\": \"Sentry\",\n    \"global.plugins.sentry.description\": \"将 Strapi 错误事件发送到 Sentry。\",\n    \"global.plugins.upload\": \"媒体库\",\n    \"global.plugins.upload.description\": \"媒体文件管理。\",\n    \"global.plugins.users-permissions\": \"角色和权限\",\n    \"global.plugins.users-permissions.description\": \"基于 JWT 的完整认证流程来保护您的 API。此插件还带有一个 ACL 策略,允许您在用户组之间管理权限。\",\n    \"global.profile\": \"简介\",\n    \"global.prompt.unsaved\": \"您确定要离开此页面吗?您的所有修改都将丢失\",\n    \"global.reset-password\": \"重置密码\",\n    \"global.roles\": \"角色\",\n    \"global.save\": \"保存\",\n    \"global.search\": \"搜索\",\n    \"global.see-more\": \"查看更多\",\n    \"global.select\": \"选择\",\n    \"global.select-all-entries\": \"全选条目\",\n    \"global.settings\": \"设置\",\n    \"global.type\": \"类型\",\n    \"global.users\": \"用户\",\n    light: light,\n    \"notification.contentType.relations.conflict\": \"内容类型存在冲突关系\",\n    \"notification.default.title\": \"信息:\",\n    \"notification.ee.warning.at-seat-limit.title\": \"已达 {licenseLimitStatus, select, OVER_LIMIT {超出} AT_LIMIT {到达}} 座位限制 ({currentUserCount}/{permittedSeats})\",\n    \"notification.ee.warning.over-.message\": \"添加座位以 {licenseLimitStatus, select, OVER_LIMIT {邀请} AT_LIMIT {重新启用}} 用户。如果您已经这样做,但 Strapi 尚未反映出来,请确保重新启动您的应用程序。\",\n    \"notification.error\": \"发生错误\",\n    \"notification.error.invalid.configuration\": \"您的配置无效,请在服务器日志中查看更多信息。\",\n    \"notification.error.layout\": \"无法检索布局\",\n    \"notification.error.tokennamenotunique\": \"名称已被分配给其他令牌\",\n    \"notification.form.error.fields\": \"表单包含一些错误\",\n    \"notification.form.success.fields\": \"更改已保存\",\n    \"notification.link-copied\": \"链接已复制到剪贴板\",\n    \"notification.permission.not-allowed-read\": \"您无权查看此文档\",\n    \"notification.success.apitokencreated\": \"API令牌已成功创建\",\n    \"notification.success.apitokenedited\": \"API令牌已成功编辑\",\n    \"notification.success.delete\": \"项目已被删除\",\n    \"notification.success.saved\": \"已保存\",\n    \"notification.success.title\": \"成功:\",\n    \"notification.success.transfertokencreated\": \"传输令牌已成功创建\",\n    \"notification.success.transfertokenedited\": \"传输令牌已成功编辑\",\n    \"notification.version.update.message\": \"有新版本的Strapi可用!\",\n    \"notification.warning.404\": \"404 - 未找到\",\n    \"notification.warning.title\": \"警告:\",\n    or: or,\n    \"request.error.model.unknown\": \"此模型不存在\",\n    selectButtonTitle: selectButtonTitle,\n    skipToContent: skipToContent,\n    submit: submit\n};\n\nexport { Analytics, Documentation, Email, Password, Provider, ResetPasswordToken, Role, Username, Users, anErrorOccurred, clearLabel, dark, zhHans as default, light, or, selectButtonTitle, skipToContent, submit };\n//# sourceMappingURL=zh-Hans.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,YAAY;AAChB,IAAI,gBAAgB;AACpB,IAAI,QAAQ;AACZ,IAAI,WAAW;AACf,IAAI,WAAW;AACf,IAAI,qBAAqB;AACzB,IAAI,OAAO;AACX,IAAI,WAAW;AACf,IAAI,QAAQ;AACZ,IAAI,kBAAkB;AACtB,IAAI,aAAa;AACjB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,KAAK;AACT,IAAI,oBAAoB;AACxB,IAAI,gBAAgB;AACpB,IAAI,SAAS;AACb,IAAI,SAAS;AAAA,EACT;AAAA,EACA,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0CAA0C;AAAA,EAC1C,iCAAiC;AAAA,EACjC,sCAAsC;AAAA,EACtC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,iDAAiD;AAAA,EACjD,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,2BAA2B;AAAA,EAC3B,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA,EAChB,yBAAyB;AAAA,EACzB,oBAAoB;AAAA,EACpB,6BAA6B;AAAA,EAC7B,qCAAqC;AAAA,EACrC,0CAA0C;AAAA,EAC1C,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,4BAA4B;AAAA,EAC5B,4BAA4B;AAAA,EAC5B,0CAA0C;AAAA,EAC1C,sBAAsB;AAAA,EACtB,iDAAiD;AAAA,EACjD,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,kDAAkD;AAAA,EAClD,yDAAyD;AAAA,EACzD,yDAAyD;AAAA,EACzD,0DAA0D;AAAA,EAC1D,mDAAmD;AAAA,EACnD,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,uCAAuC;AAAA,EACvC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,sCAAsC;AAAA,EACtC,8DAA8D;AAAA,EAC9D,oDAAoD;AAAA,EACpD,2DAA2D;AAAA,EAC3D,+DAA+D;AAAA,EAC/D,6DAA6D;AAAA,EAC7D,+DAA+D;AAAA,EAC/D,4DAA4D;AAAA,EAC5D,qDAAqD;AAAA,EACrD,8DAA8D;AAAA,EAC9D,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,+DAA+D;AAAA,EAC/D,mEAAmE;AAAA,EACnE,6DAA6D;AAAA,EAC7D,0DAA0D;AAAA,EAC1D,2DAA2D;AAAA,EAC3D,sDAAsD;AAAA,EACtD,mDAAmD;AAAA,EACnD,8DAA8D;AAAA,EAC9D,6DAA6D;AAAA,EAC7D,gEAAgE;AAAA,EAChE,iEAAiE;AAAA,EACjE,8DAA8D;AAAA,EAC9D,mEAAmE;AAAA,EACnE,iEAAiE;AAAA,EACjE,4DAA4D;AAAA,EAC5D,wEAAwE;AAAA,EACxE,wDAAwD;AAAA,EACxD,mDAAmD;AAAA,EACnD,oCAAoC;AAAA,EACpC,sCAAsC;AAAA,EACtC,iCAAiC;AAAA,EACjC,iDAAiD;AAAA,EACjD,+CAA+C;AAAA,EAC/C,wDAAwD;AAAA,EACxD,6CAA6C;AAAA,EAC7C,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,sCAAsC;AAAA,EACtC,8BAA8B;AAAA,EAC9B,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,+CAA+C;AAAA,EAC/C,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,sDAAsD;AAAA,EACtD,sDAAsD;AAAA,EACtD,sDAAsD;AAAA,EACtD,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,+CAA+C;AAAA,EAC/C,8DAA8D;AAAA,EAC9D,2DAA2D;AAAA,EAC3D,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,0CAA0C;AAAA,EAC1C,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,8CAA8C;AAAA,EAC9C,8CAA8C;AAAA,EAC9C,8CAA8C;AAAA,EAC9C,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,8CAA8C;AAAA,EAC9C,gDAAgD;AAAA,EAChD,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,qDAAqD;AAAA,EACrD,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,iDAAiD;AAAA,EACjD,mDAAmD;AAAA,EACnD,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,oCAAoC;AAAA,EACpC,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,iDAAiD;AAAA,EACjD,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,uCAAuC;AAAA,EACvC,4CAA4C;AAAA,EAC5C,yDAAyD;AAAA,EACzD,iDAAiD;AAAA,EACjD,8DAA8D;AAAA,EAC9D,mEAAmE;AAAA,EACnE,kEAAkE;AAAA,EAClE,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,8DAA8D;AAAA,EAC9D,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,oDAAoD;AAAA,EACpD,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,mCAAmC;AAAA,EACnC,iDAAiD;AAAA,EACjD,yDAAyD;AAAA,EACzD,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,wCAAwC;AAAA,EACxC,0CAA0C;AAAA,EAC1C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,6CAA6C;AAAA,EAC7C,yDAAyD;AAAA,EACzD,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,yDAAyD;AAAA,EACzD,8CAA8C;AAAA,EAC9C,wCAAwC;AAAA,EACxC,sBAAsB;AAAA,EACtB,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,8CAA8C;AAAA,EAC9C,gDAAgD;AAAA,EAChD,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,oCAAoC;AAAA,EACpC,mCAAmC;AAAA,EACnC,oCAAoC;AAAA,EACpC,4CAA4C;AAAA,EAC5C,sCAAsC;AAAA,EACtC,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,uCAAuC;AAAA,EACvC,wCAAwC;AAAA,EACxC,8BAA8B;AAAA,EAC9B,gCAAgC;AAAA,EAChC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,iDAAiD;AAAA,EACjD,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,4CAA4C;AAAA,EAC5C,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,2CAA2C;AAAA,EAC3C,kCAAkC;AAAA,EAClC,sCAAsC;AAAA,EACtC,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,4CAA4C;AAAA,EAC5C,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,0CAA0C;AAAA,EAC1C,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,2BAA2B;AAAA,EAC3B,8CAA8C;AAAA,EAC9C,2CAA2C;AAAA,EAC3C,6CAA6C;AAAA,EAC7C,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,sCAAsC;AAAA,EACtC,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,qBAAqB;AAAA,EACrB,sBAAsB;AAAA,EACtB,2BAA2B;AAAA,EAC3B,gDAAgD;AAAA,EAChD,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB;AAAA,EACA,uBAAuB;AAAA,EACvB;AAAA,EACA,+BAA+B;AAAA,EAC/B,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,kDAAkD;AAAA,EAClD,0DAA0D;AAAA,EAC1D,mDAAmD;AAAA,EACnD,2DAA2D;AAAA,EAC3D,sCAAsC;AAAA,EACtC,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,6CAA6C;AAAA,EAC7C,2CAA2C;AAAA,EAC3C,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,uDAAuD;AAAA,EACvD,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,uCAAuC;AAAA,EACvC,kDAAkD;AAAA,EAClD,oDAAoD;AAAA,EACpD,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,kDAAkD;AAAA,EAClD,iDAAiD;AAAA,EACjD,0DAA0D;AAAA,EAC1D,gDAAgD;AAAA,EAChD,yDAAyD;AAAA,EACzD,2CAA2C;AAAA,EAC3C,oDAAoD;AAAA,EACpD,iDAAiD;AAAA,EACjD,0DAA0D;AAAA,EAC1D,kDAAkD;AAAA,EAClD,oDAAoD;AAAA,EACpD,wCAAwC;AAAA,EACxC,+CAA+C;AAAA,EAC/C;AAAA,EACA,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,4BAA4B;AAAA,EAC5B,wCAAwC;AAAA,EACxC,4BAA4B;AAAA,EAC5B,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,kCAAkC;AAAA,EAClC,0CAA0C;AAAA,EAC1C,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,4CAA4C;AAAA,EAC5C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,+CAA+C;AAAA,EAC/C,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,+CAA+C;AAAA,EAC/C,sDAAsD;AAAA,EACtD,wDAAwD;AAAA,EACxD,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,yDAAyD;AAAA,EACzD,qDAAqD;AAAA,EACrD,4CAA4C;AAAA,EAC5C,2CAA2C;AAAA,EAC3C,gDAAgD;AAAA,EAChD,4CAA4C;AAAA,EAC5C,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,2CAA2C;AAAA,EAC3C,wDAAwD;AAAA,EACxD,sDAAsD;AAAA,EACtD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,2DAA2D;AAAA,EAC3D,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,+CAA+C;AAAA,EAC/C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,oCAAoC;AAAA,EACpC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,2CAA2C;AAAA,EAC3C,2BAA2B;AAAA,EAC3B,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,iDAAiD;AAAA,EACjD,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,mCAAmC;AAAA,EACnC,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,+EAA+E;AAAA,EAC/E,qEAAqE;AAAA,EACrE,yEAAyE;AAAA,EACzE,mEAAmE;AAAA,EACnE,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,4DAA4D;AAAA,EAC5D,0DAA0D;AAAA,EAC1D,sEAAsE;AAAA,EACtE,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,qDAAqD;AAAA,EACrD,wDAAwD;AAAA,EACxD,yDAAyD;AAAA,EACzD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,qCAAqC;AAAA,EACrC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,oEAAoE;AAAA,EACpE,4DAA4D;AAAA,EAC5D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,wDAAwD;AAAA,EACxD,4BAA4B;AAAA,EAC5B,sBAAsB;AAAA,EACtB,+BAA+B;AAAA,EAC/B,yCAAyC;AAAA,EACzC,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,4BAA4B;AAAA,EAC5B,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,kBAAkB;AAAA,EAClB,yCAAyC;AAAA,EACzC,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,gCAAgC;AAAA,EAChC,wCAAwC;AAAA,EACxC,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB;AAAA,EACA,eAAe;AAAA,EACf,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,6CAA6C;AAAA,EAC7C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,+CAA+C;AAAA,EAC/C,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,0CAA0C;AAAA,EAC1C,4CAA4C;AAAA,EAC5C,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,gDAAgD;AAAA,EAChD,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,yDAAyD;AAAA,EACzD,+CAA+C;AAAA,EAC/C,gDAAgD;AAAA,EAChD,uDAAuD;AAAA,EACvD,+BAA+B;AAAA,EAC/B,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,yCAAyC;AAAA,EACzC,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,2CAA2C;AAAA,EAC3C,gDAAgD;AAAA,EAChD,iDAAiD;AAAA,EACjD,8CAA8C;AAAA,EAC9C,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,0BAA0B;AAAA,EAC1B,0DAA0D;AAAA,EAC1D,+CAA+C;AAAA,EAC/C,gDAAgD;AAAA,EAChD,+CAA+C;AAAA,EAC/C,iDAAiD;AAAA,EACjD,yDAAyD;AAAA,EACzD,uDAAuD;AAAA,EACvD,uDAAuD;AAAA,EACvD,oDAAoD;AAAA,EACpD,gEAAgE;AAAA,EAChE,wDAAwD;AAAA,EACxD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,6DAA6D;AAAA,EAC7D,0DAA0D;AAAA,EAC1D,wDAAwD;AAAA,EACxD,qDAAqD;AAAA,EACrD,mDAAmD;AAAA,EACnD,0DAA0D;AAAA,EAC1D,+DAA+D;AAAA,EAC/D,8DAA8D;AAAA,EAC9D,gDAAgD;AAAA,EAChD,yDAAyD;AAAA,EACzD,4EAA4E;AAAA,EAC5E,+EAA+E;AAAA,EAC/E,0EAA0E;AAAA,EAC1E,2EAA2E;AAAA,EAC3E,sDAAsD;AAAA,EACtD,oDAAoD;AAAA,EACpD,wDAAwD;AAAA,EACxD,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,mEAAmE;AAAA,EACnE,gEAAgE;AAAA,EAChE,iDAAiD;AAAA,EACjD,sDAAsD;AAAA,EACtD,wDAAwD;AAAA,EACxD,yFAAyF;AAAA,EACzF,yFAAyF;AAAA,EACzF,qEAAqE;AAAA,EACrE,iDAAiD;AAAA,EACjD,yDAAyD;AAAA,EACzD,gDAAgD;AAAA,EAChD,qDAAqD;AAAA,EACrD,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,+CAA+C;AAAA,EAC/C,oEAAoE;AAAA,EACpE,oEAAoE;AAAA,EACpE,uDAAuD;AAAA,EACvD,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,4CAA4C;AAAA,EAC5C,8CAA8C;AAAA,EAC9C,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,+CAA+C;AAAA,EAC/C,0CAA0C;AAAA,EAC1C,gDAAgD;AAAA,EAChD,2CAA2C;AAAA,EAC3C,+CAA+C;AAAA,EAC/C,kDAAkD;AAAA,EAClD,uDAAuD;AAAA,EACvD,4DAA4D;AAAA,EAC5D,uDAAuD;AAAA,EACvD,0DAA0D;AAAA,EAC1D,gEAAgE;AAAA,EAChE,0DAA0D;AAAA,EAC1D,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,8CAA8C;AAAA,EAC9C,0CAA0C;AAAA,EAC1C,qEAAqE;AAAA,EACrE,qDAAqD;AAAA,EACrD,2DAA2D;AAAA,EAC3D,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,qDAAqD;AAAA,EACrD,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,6CAA6C;AAAA,EAC7C,mDAAmD;AAAA,EACnD,yCAAyC;AAAA,EACzC,6DAA6D;AAAA,EAC7D,qEAAqE;AAAA,EACrE,oDAAoD;AAAA,EACpD,+DAA+D;AAAA,EAC/D,qDAAqD;AAAA,EACrD,iEAAiE;AAAA,EACjE,mEAAmE;AAAA,EACnE,mEAAmE;AAAA,EACnE,+EAA+E;AAAA,EAC/E,kFAAkF;AAAA,EAClF,6DAA6D;AAAA,EAC7D,iDAAiD;AAAA,EACjD,mEAAmE;AAAA,EACnE,6DAA6D;AAAA,EAC7D,kEAAkE;AAAA,EAClE,oDAAoD;AAAA,EACpD,mDAAmD;AAAA,EACnD,+CAA+C;AAAA,EAC/C,kEAAkE;AAAA,EAClE,yEAAyE;AAAA,EACzE,mEAAmE;AAAA,EACnE,6EAA6E;AAAA,EAC7E,uEAAuE;AAAA,EACvE,mEAAmE;AAAA,EACnE,yDAAyD;AAAA,EACzD,sDAAsD;AAAA,EACtD,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,oCAAoC;AAAA,EACpC,+BAA+B;AAAA,EAC/B,6DAA6D;AAAA,EAC7D,gEAAgE;AAAA,EAChE,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,kDAAkD;AAAA,EAClD,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,sCAAsC;AAAA,EACtC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,wCAAwC;AAAA,EACxC,8CAA8C;AAAA,EAC9C,wCAAwC;AAAA,EACxC,8CAA8C;AAAA,EAC9C,8CAA8C;AAAA,EAC9C,0CAA0C;AAAA,EAC1C,6CAA6C;AAAA,EAC7C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,sDAAsD;AAAA,EACtD,uCAAuC;AAAA,EACvC,sCAAsC;AAAA,EACtC,kDAAkD;AAAA,EAClD,iDAAiD;AAAA,EACjD,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,qDAAqD;AAAA,EACrD,0CAA0C;AAAA,EAC1C,2DAA2D;AAAA,EAC3D,0CAA0C;AAAA,EAC1C,sDAAsD;AAAA,EACtD,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,yCAAyC;AAAA,EACzC,sCAAsC;AAAA,EACtC,0CAA0C;AAAA,EAC1C,0BAA0B;AAAA,EAC1B,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,mCAAmC;AAAA,EACnC,sDAAsD;AAAA,EACtD,yDAAyD;AAAA,EACzD,6DAA6D;AAAA,EAC7D,mDAAmD;AAAA,EACnD,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,4CAA4C;AAAA,EAC5C,kDAAkD;AAAA,EAClD,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,4CAA4C;AAAA,EAC5C,+DAA+D;AAAA,EAC/D,mEAAmE;AAAA,EACnE,oEAAoE;AAAA,EACpE,sEAAsE;AAAA,EACtE,kEAAkE;AAAA,EAClE,yDAAyD;AAAA,EACzD,kDAAkD;AAAA,EAClD,2DAA2D;AAAA,EAC3D,0DAA0D;AAAA,EAC1D,2EAA2E;AAAA,EAC3E,oEAAoE;AAAA,EACpE,gCAAgC;AAAA,EAChC,uCAAuC;AAAA,EACvC,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,yCAAyC;AAAA,EACzC,mDAAmD;AAAA,EACnD,uDAAuD;AAAA,EACvD,+CAA+C;AAAA,EAC/C,6CAA6C;AAAA,EAC7C,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,uCAAuC;AAAA,EACvC,4CAA4C;AAAA,EAC5C,qCAAqC;AAAA,EACrC,oDAAoD;AAAA,EACpD,sDAAsD;AAAA,EACtD;AAAA,EACA,wBAAwB;AAAA,EACxB,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,0BAA0B;AAAA,EAC1B,gBAAgB;AAAA,EAChB,0BAA0B;AAAA,EAC1B,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,wBAAwB;AAAA,EACxB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,sBAAsB;AAAA,EACtB,eAAe;AAAA,EACf,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,kCAAkC;AAAA,EAClC,8CAA8C;AAAA,EAC9C,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,wBAAwB;AAAA,EACxB,oCAAoC;AAAA,EACpC,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,gDAAgD;AAAA,EAChD,kBAAkB;AAAA,EAClB,yBAAyB;AAAA,EACzB,yBAAyB;AAAA,EACzB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB;AAAA,EACA,+CAA+C;AAAA,EAC/C,8BAA8B;AAAA,EAC9B,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,sBAAsB;AAAA,EACtB,4CAA4C;AAAA,EAC5C,6BAA6B;AAAA,EAC7B,yCAAyC;AAAA,EACzC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,4CAA4C;AAAA,EAC5C,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,6CAA6C;AAAA,EAC7C,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA,EAC9B;AAAA,EACA,+BAA+B;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AACJ;", "names": []}