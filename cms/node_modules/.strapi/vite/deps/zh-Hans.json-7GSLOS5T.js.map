{"version": 3, "sources": ["../../../@strapi/content-type-builder/dist/admin/translations/zh-Hans.json.mjs"], "sourcesContent": ["var configurations = \"配置\";\nvar from = \"来自\";\nvar zhHans = {\n    \"attribute.boolean\": \"布尔类型\",\n    \"attribute.boolean.description\": \"Yes 或 no, 1 或 0, true 或 false\",\n    \"attribute.component\": \"组件\",\n    \"attribute.component.description\": \"您可以重复或重复使用的字段组\",\n    \"attribute.date\": \"日期选择器\",\n    \"attribute.date.description\": \"带有小时，分钟和秒的日期选择器\",\n    \"attribute.datetime\": \"日期时间选择器\",\n    \"attribute.dynamiczone\": \"动态区域\",\n    \"attribute.dynamiczone.description\": \"编辑内容时动态选择组件\",\n    \"attribute.email\": \"电子邮件\",\n    \"attribute.email.description\": \"带有格式验证的电子邮件字段\",\n    \"attribute.enumeration\": \"列举\",\n    \"attribute.enumeration.description\": \"值的列表，然后可以从中选择一个\",\n    \"attribute.json\": \"JSON\",\n    \"attribute.json.description\": \"JSON 格式的数据\",\n    \"attribute.media\": \"媒体文件\",\n    \"attribute.media.description\": \"图片，视频等文件\",\n    \"attribute.null\": \" \",\n    \"attribute.number\": \"数字类型\",\n    \"attribute.number.description\": \"数字类型 (integer, float, decimal)\",\n    \"attribute.password\": \"密码输入框\",\n    \"attribute.password.description\": \"密码字段，会隐藏字符\",\n    \"attribute.relation\": \"引用\",\n    \"attribute.relation.description\": \"引用一个 Content Type\",\n    \"attribute.richtext\": \"富文本编辑器\",\n    \"attribute.richtext.description\": \"具有格式选项的富文本编辑器\",\n    \"attribute.text\": \"文本\",\n    \"attribute.text.description\": \"较短或较长的文字，例如标题或说明\",\n    \"attribute.time\": \"时间\",\n    \"attribute.uid\": \"UID\",\n    \"attribute.uid.description\": \"唯一标识符\",\n    \"button.attributes.add.another\": \"添加一个新字段\",\n    \"button.component.add\": \"添加组件\",\n    \"button.component.create\": \"创建新组件\",\n    \"button.model.create\": \"创建一个新的 Content Type\",\n    \"component.repeatable\": \"(可重复的)\",\n    \"components.componentSelect.no-component-available\": \"您已经添加了所有组件\",\n    \"components.componentSelect.no-component-available.with-search\": \"没有与您的搜索相匹配的组件\",\n    \"components.componentSelect.value-component\": \"{number} 个组件被选择 (输入以搜索组件)\",\n    \"components.componentSelect.value-components\": \"{number} 个组件被选择\",\n    configurations: configurations,\n    \"contentType.collectionName.description\": \"当 Content Type 的名称和表名称不同时会很有用\",\n    \"contentType.collectionName.label\": \"集合名称\",\n    \"contentType.displayName.label\": \"显示名称\",\n    \"contentType.apiId-plural.description\": \"API ID（复数形式）\",\n    \"contentType.apiId-plural.label\": \"API ID（复数形式）\",\n    \"contentType.apiId-singular.description\": \"UID 用于生成 API 路由和数据库表/集合。\",\n    \"contentType.apiId-singular.label\": \"API ID（单数形式）\",\n    \"contentType.kind.change.warning\": \"您刚刚更改了内容类型的种类：API 将被重置（路由、控制器和服务将被覆盖）。\",\n    \"error.contentTypeName.reserved-name\": \"此名称不能在项目中使用，因为它可能会破坏其他功能\",\n    \"error.validation.minSupMax\": \"不能更高\",\n    \"form.attribute.component.option.add\": \"添加一个组件\",\n    \"form.attribute.component.option.create\": \"创建一个新的组件\",\n    \"form.attribute.component.option.create.description\": \"组件在类型和组件之间共享，它将随处可用。\",\n    \"form.attribute.component.option.repeatable\": \"可重复组件\",\n    \"form.attribute.component.option.repeatable.description\": \"最适合的成分，元标记等的多个实例（数组）。\",\n    \"form.attribute.component.option.reuse-existing\": \"使用一个已存在的组件\",\n    \"form.attribute.component.option.reuse-existing.description\": \"重用已经创建的组件，以使您的数据在内容类型之间保持一致。\",\n    \"form.attribute.component.option.single\": \"单一组件\",\n    \"form.attribute.component.option.single.description\": \"最适合的对完整地址，主要信息等字段进行分组...\",\n    \"form.attribute.item.customColumnName\": \"自定义列名称\",\n    \"form.attribute.item.customColumnName.description\": \"修改数据库列名，使得API返回更容易理解。\",\n    \"form.attribute.item.defineRelation.fieldName\": \"字段名称\",\n    \"form.attribute.item.enumeration.graphql\": \"GraphQL 的名称重写\",\n    \"form.attribute.item.enumeration.graphql.description\": \"允许您覆盖 GraphQL 的默认生成名称\",\n    \"form.attribute.item.enumeration.placeholder\": \"例如:\\nmorning\\nnoon\\nevening\",\n    \"form.attribute.item.enumeration.rules\": \"值（每个值占一行）\",\n    \"form.attribute.item.maximum\": \"最大值\",\n    \"form.attribute.item.maximumLength\": \"最大长度\",\n    \"form.attribute.item.minimum\": \"最小值\",\n    \"form.attribute.item.minimumLength\": \"最小长度\",\n    \"form.attribute.item.number.type\": \"数字格式\",\n    \"form.attribute.item.number.type.biginteger\": \"big integer (例如: 123456789)\",\n    \"form.attribute.item.number.type.decimal\": \"decimal (例如: 2.22)\",\n    \"form.attribute.item.number.type.float\": \"float (例如: 3.33333333)\",\n    \"form.attribute.item.number.type.integer\": \"integer (例如: 10)\",\n    \"form.attribute.item.privateField\": \"私有字段\",\n    \"form.attribute.item.privateField.description\": \"该字段不会显示在 API 请求的响应中\",\n    \"form.attribute.item.requiredField\": \"必须的\",\n    \"form.attribute.item.requiredField.description\": \"如果此字段为空，则无法创建字段。\",\n    \"form.attribute.item.uniqueField\": \"唯一的\",\n    \"form.attribute.item.uniqueField.description\": \"如果存在具有相同内容的现有条目，则无法创建条目。\",\n    \"form.attribute.media.option.multiple\": \"多种媒体\",\n    \"form.attribute.media.option.multiple.description\": \"最适合的滑块，转盘或多个文件下载\",\n    \"form.attribute.media.option.single\": \"单一的媒体\",\n    \"form.attribute.media.option.single.description\": \"最适合的头像，个人资料图片或封面\",\n    \"form.attribute.settings.default\": \"默认值\",\n    \"form.attribute.text.option.long-text\": \"较长的文字\",\n    \"form.attribute.text.option.long-text.description\": \"最适合的描述，传记。\\u2028精确搜索已禁用。\",\n    \"form.attribute.text.option.short-text\": \"较短的文字\",\n    \"form.attribute.text.option.short-text.description\": \"最适合的标题，名称，链接（URL）。它还可以在字段进行精确搜索。\",\n    \"form.button.add-components-to-dynamiczone\": \"将组件添加到区域\",\n    \"form.button.add-field\": \"添加另一个字段\",\n    \"form.button.add-first-field-to-created-component\": \"添加第一个字段到这个组件\",\n    \"form.button.add.field.to.component\": \"向该组件添加另一个字段\",\n    \"form.button.cancel\": \"取消\",\n    \"form.button.configure-component\": \"配置组件\",\n    \"form.button.configure-view\": \"配置视图\",\n    \"form.button.select-component\": \"选择一个组件\",\n    from: from,\n    \"modalForm.attribute.form.base.name.description\": \"属性名称不允许使用空格\",\n    \"modalForm.attributes.select-component\": \"选择一个组件\",\n    \"modalForm.attributes.select-components\": \"选择组件\",\n    \"modalForm.component.header-create\": \"创建一个组件\",\n    \"modalForm.components.create-component.category.label\": \"选择一个类别或输入名称以创建一个新类别\",\n    \"modalForm.components.icon.label\": \"图标\",\n    \"modalForm.editCategory.base.name.description\": \"类别名称不允许有空格\",\n    \"modalForm.header-edit\": \"编辑 {name}\",\n    \"modalForm.header.categories\": \"类别\",\n    \"modalForm.header.back\": \"后退\",\n    \"modalForm.sub-header.addComponentToDynamicZone\": \"将新组件添加到动态区域\",\n    \"modalForm.sub-header.attribute.create\": \"添加新的 {type} 字段\",\n    \"modalForm.sub-header.attribute.create.step\": \"添加新的组件 ({step}/2)\",\n    \"modalForm.sub-header.attribute.edit\": \"编辑 {name}\",\n    \"modalForm.sub-header.chooseAttribute.collectionType\": \"为您的 Content Type 选择一个字段\",\n    \"modalForm.sub-header.chooseAttribute.component\": \"为您的组件选择一个字段\",\n    \"modalForm.collectionType.header-create\": \"创建一个集合类型\",\n    \"modelPage.attribute.relationWith\": \"关联\",\n    \"notification.info.autoreaload-disable\": \"要使用此插件，需要自动重载功能。请使用 `strapi develop` 启动服务\",\n    \"notification.info.creating.notSaved\": \"在创建新的内容类型或组件之前，请保存您的工作\",\n    \"plugin.description.long\": \"给你的 API 的数据结构建模. 快速的创造新的字段(fields)和关系(relations)。将会自动在项目中创建和更新文件。\",\n    \"plugin.description.short\": \"给你的 API 的数据结构建模\",\n    \"popUpForm.navContainer.advanced\": \"高级设置\",\n    \"popUpForm.navContainer.base\": \"基础设置\",\n    \"popUpWarning.bodyMessage.cancel-modifications\": \"您确定要取消修改吗？\",\n    \"popUpWarning.bodyMessage.cancel-modifications.with-components\": \"您确定要取消修改吗？某些组件已创建或已修改了...\",\n    \"popUpWarning.bodyMessage.category.delete\": \"您确定要删除此类别吗？所有组件也将被删除。\",\n    \"popUpWarning.bodyMessage.component.delete\": \"您确定要删除此组件吗？\",\n    \"popUpWarning.bodyMessage.contentType.delete\": \"您确定要删除此 Content Type 吗?\",\n    \"prompt.unsaved\": \"您确定要离开吗？您所有的修改都将丢失。\",\n    \"relation.attributeName.placeholder\": \"例如: author, category, tag\",\n    \"relation.manyToMany\": \"有并属于许多\",\n    \"relation.manyToOne\": \"有很多\",\n    \"relation.manyWay\": \"有很多\",\n    \"relation.oneToMany\": \"属于很多\",\n    \"relation.oneToOne\": \"有一个\",\n    \"relation.oneWay\": \"有一个\"\n};\n\nexport { configurations, zhHans as default, from };\n//# sourceMappingURL=zh-Hans.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,iBAAiB;AACrB,IAAI,OAAO;AACX,IAAI,SAAS;AAAA,EACT,qBAAqB;AAAA,EACrB,iCAAiC;AAAA,EACjC,uBAAuB;AAAA,EACvB,mCAAmC;AAAA,EACnC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,mBAAmB;AAAA,EACnB,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,mBAAmB;AAAA,EACnB,+BAA+B;AAAA,EAC/B,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,gCAAgC;AAAA,EAChC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,wBAAwB;AAAA,EACxB,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,qDAAqD;AAAA,EACrD,iEAAiE;AAAA,EACjE,8CAA8C;AAAA,EAC9C,+CAA+C;AAAA,EAC/C;AAAA,EACA,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,wCAAwC;AAAA,EACxC,kCAAkC;AAAA,EAClC,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,mCAAmC;AAAA,EACnC,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,sDAAsD;AAAA,EACtD,8CAA8C;AAAA,EAC9C,0DAA0D;AAAA,EAC1D,kDAAkD;AAAA,EAClD,8DAA8D;AAAA,EAC9D,0CAA0C;AAAA,EAC1C,sDAAsD;AAAA,EACtD,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,gDAAgD;AAAA,EAChD,2CAA2C;AAAA,EAC3C,uDAAuD;AAAA,EACvD,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+BAA+B;AAAA,EAC/B,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,8CAA8C;AAAA,EAC9C,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,oCAAoC;AAAA,EACpC,gDAAgD;AAAA,EAChD,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,sCAAsC;AAAA,EACtC,kDAAkD;AAAA,EAClD,mCAAmC;AAAA,EACnC,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,6CAA6C;AAAA,EAC7C,yBAAyB;AAAA,EACzB,oDAAoD;AAAA,EACpD,sCAAsC;AAAA,EACtC,sBAAsB;AAAA,EACtB,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,gCAAgC;AAAA,EAChC;AAAA,EACA,kDAAkD;AAAA,EAClD,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,qCAAqC;AAAA,EACrC,wDAAwD;AAAA,EACxD,mCAAmC;AAAA,EACnC,gDAAgD;AAAA,EAChD,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,kDAAkD;AAAA,EAClD,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,iDAAiD;AAAA,EACjD,iEAAiE;AAAA,EACjE,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,+CAA+C;AAAA,EAC/C,kBAAkB;AAAA,EAClB,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,qBAAqB;AAAA,EACrB,mBAAmB;AACvB;", "names": []}