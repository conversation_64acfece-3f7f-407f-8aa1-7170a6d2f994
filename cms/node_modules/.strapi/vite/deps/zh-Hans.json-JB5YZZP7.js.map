{"version": 3, "sources": ["../../../@strapi/i18n/dist/admin/translations/zh-Hans.json.mjs"], "sourcesContent": ["var zhHans = {\n    \"CMEditViewCopyLocale.copy-failure\": \"语言复制失败\",\n    \"CMEditViewCopyLocale.copy-success\": \"语言已被复制\",\n    \"CMEditViewCopyLocale.copy-text\": \"从另一个语言获取内容\",\n    \"CMEditViewCopyLocale.submit-text\": \"是的, 填入\",\n    \"CheckboxConfirmation.Modal.body\": \"你要关闭它吗?\",\n    \"CheckboxConfirmation.Modal.button-confirm\": \"是的, 关闭\",\n    \"CheckboxConfirmation.Modal.content\": \"关闭国际化将会导致除和默认语言关联的内容之外的所有内容被删除\",\n    \"Field.localized\": \"该值对于选中语言是唯一的\",\n    \"Field.not-localized\": \"该值对所有语言通用\",\n    \"Settings.list.actions.add\": \"添加语言\",\n    \"Settings.list.actions.delete\": \"删除语言\",\n    \"Settings.list.actions.deleteAdditionalInfos\": \"这将会删除可用的语言版本<em>(来自国际化)</em>\",\n    \"Settings.list.actions.edit\": \"编辑语言\",\n    \"Settings.list.description\": \"国际化插件设置\",\n    \"Settings.list.empty.description\": \"这不是一个常规的动作，您动手修改了数据库。为了能正确使用Strapi，请确保至少保存一种语言在您的数据库中\",\n    \"Settings.list.empty.title\": \"暂无语言\",\n    \"Settings.locales.modal.advanced\": \"高级设置\",\n    \"Settings.locales.modal.advanced.setAsDefault\": \"设置为默认语言\",\n    \"Settings.locales.modal.advanced.setAsDefault.hint\": \"系统中必须有一个默认语言，可以通过选中另一个非默认语言来设置\",\n    \"Settings.locales.modal.advanced.settings\": \"设置\",\n    \"Settings.locales.modal.base\": \"基本设置\",\n    \"Settings.locales.modal.create.alreadyExist\": \"该语言已存在\",\n    \"Settings.locales.modal.create.defaultLocales.loading\": \"正在加载可用语言...\",\n    \"Settings.locales.modal.create.success\": \"该语言已成功添加\",\n    \"Settings.locales.modal.create.tab.label\": \"将会在国际化基础设置及高级设置之前切换\",\n    \"Settings.locales.modal.delete.confirm\": \"是的, 删除\",\n    \"Settings.locales.modal.delete.message\": \"删除该语言将会删除所有与之相关的内容。如果你想保留某些内容，请确保先将其保存到另一种语言。\",\n    \"Settings.locales.modal.delete.secondMessage\": \"您要删除该语言？\",\n    \"Settings.locales.modal.delete.success\": \"该语言已成功删除\",\n    \"Settings.locales.modal.edit.confirmation\": \"完成\",\n    \"Settings.locales.modal.create.code.label\": \"语言\",\n    \"Settings.locales.modal.edit.success\": \"该语言已完成编辑\",\n    \"Settings.locales.modal.edit.tab.label\": \"将会在国际化基础设置及高级设置之前切换\",\n    \"Settings.locales.modal.create.name.label\": \"语言显示名称\",\n    \"Settings.locales.modal.create.name.label.description\": \"管理面板中语言将会以该名显示\",\n    \"Settings.locales.modal.create.name.label.error\": \"语言的显示名称不能少于50个字符\",\n    \"Settings.locales.modal.locales.label\": \"语言\",\n    \"Settings.locales.modal.title\": \"配置\",\n    \"Settings.locales.row.default-locale\": \"默认语言\",\n    \"Settings.permissions.loading\": \"加载权限中\",\n    \"Settings.permissions.read.denied.description\": \"为了能够阅读该内容，你可以和系统管理员取得联系\",\n    \"Settings.permissions.read.denied.title\": \"你没有获取该内容的权限\",\n    \"components.Select.locales.not-available\": \"无可用内容\",\n    \"plugin.description.long\": \"开启此插件后，可以在管理面板或是API中创建，查询及更新不同语言的内容\",\n    \"plugin.description.short\": \"在管理面板或是API中创建，查询及更新不同语言的内容\",\n    \"plugin.name\": \"国际化\",\n    \"plugin.schema.i18n.localized.description-content-type\": \"允许您为内容设置不同的语言\",\n    \"plugin.schema.i18n.localized.description-field\": \"该字段在不同的语言环境中有不同的值\",\n    \"plugin.schema.i18n.localized.label-content-type\": \"为该内容类型开启本地化\",\n    \"plugin.schema.i18n.localized.label-field\": \"为该字段开启本地化\"\n};\n\nexport { zhHans as default };\n//# sourceMappingURL=zh-Hans.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,SAAS;AAAA,EACT,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,mCAAmC;AAAA,EACnC,6CAA6C;AAAA,EAC7C,sCAAsC;AAAA,EACtC,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,+CAA+C;AAAA,EAC/C,8BAA8B;AAAA,EAC9B,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,gDAAgD;AAAA,EAChD,qDAAqD;AAAA,EACrD,4CAA4C;AAAA,EAC5C,+BAA+B;AAAA,EAC/B,8CAA8C;AAAA,EAC9C,wDAAwD;AAAA,EACxD,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,wDAAwD;AAAA,EACxD,kDAAkD;AAAA,EAClD,wCAAwC;AAAA,EACxC,gCAAgC;AAAA,EAChC,uCAAuC;AAAA,EACvC,gCAAgC;AAAA,EAChC,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,eAAe;AAAA,EACf,yDAAyD;AAAA,EACzD,kDAAkD;AAAA,EAClD,mDAAmD;AAAA,EACnD,4CAA4C;AAChD;", "names": []}