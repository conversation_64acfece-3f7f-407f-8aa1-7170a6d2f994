'use strict';

/**
 * blog-post controller
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::blog-post.blog-post', ({ strapi }) => ({
  // Custom find method to include published posts only for public API
  async find(ctx) {
    // Call the default core action
    const { data, meta } = await super.find(ctx);
    
    // Add any custom logic here if needed
    return { data, meta };
  },

  // Custom findOne method
  async findOne(ctx) {
    const { data, meta } = await super.findOne(ctx);
    
    // Add any custom logic here if needed
    return { data, meta };
  },

  // Method to find posts by slug
  async findBySlug(ctx) {
    const { slug } = ctx.params;
    
    const entity = await strapi.entityService.findMany('api::blog-post.blog-post', {
      filters: { slug },
      populate: ['featuredImage'],
    });

    if (!entity || entity.length === 0) {
      return ctx.notFound('Post not found');
    }

    return entity[0];
  }
}));
