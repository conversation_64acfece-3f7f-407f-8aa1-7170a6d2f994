'use strict';

/**
 * blog-post router
 */

const { createCoreRouter } = require('@strapi/strapi').factories;

const defaultRouter = createCoreRouter('api::blog-post.blog-post');

// Custom routes
const customRoutes = {
  routes: [
    {
      method: 'GET',
      path: '/blog-posts/slug/:slug',
      handler: 'blog-post.findBySlug',
      config: {
        auth: false,
      },
    },
  ],
};

// Merge default routes with custom routes
module.exports = {
  routes: [
    ...defaultRouter.routes,
    ...customRoutes.routes,
  ],
};
