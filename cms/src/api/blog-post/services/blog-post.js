'use strict';

/**
 * blog-post service
 */

const { createCoreService } = require('@strapi/strapi').factories;

module.exports = createCoreService('api::blog-post.blog-post', ({ strapi }) => ({
  // Custom service methods can be added here
  
  async findPublished(params = {}) {
    return await strapi.entityService.findMany('api::blog-post.blog-post', {
      ...params,
      filters: {
        ...params.filters,
        publishedAt: {
          $notNull: true,
        },
      },
      populate: ['featuredImage'],
      sort: { publishedAt: 'desc' },
    });
  },

  async findBySlug(slug) {
    const posts = await strapi.entityService.findMany('api::blog-post.blog-post', {
      filters: { slug },
      populate: ['featuredImage'],
    });

    return posts[0] || null;
  },

  async calculateReadingTime(content) {
    // Simple reading time calculation (average 200 words per minute)
    const wordsPerMinute = 200;
    const textContent = content.replace(/<[^>]*>/g, ''); // Remove HTML tags
    const wordCount = textContent.split(/\s+/).length;
    return Math.ceil(wordCount / wordsPerMinute);
  }
}));
