const axios = require('axios');

async function debugStrapiAPI() {
  const STRAPI_URL = 'http://localhost:1337';
  
  console.log('🔍 Debugging Strapi API...\n');
  
  try {
    // Test different possible endpoints
    const endpoints = [
      '/api/blog-posts',
      '/api/blog-post',
      '/api/blogposts',
      '/api/blogpost',
      '/blog-posts',
      '/blog-post'
    ];
    
    console.log('Testing different endpoint variations:');
    for (const endpoint of endpoints) {
      try {
        const response = await axios.get(`${STRAPI_URL}${endpoint}`);
        console.log(`✅ ${endpoint} - Status: ${response.status}`);
        if (response.data && response.data.data) {
          console.log(`   Found ${response.data.data.length} items`);
        }
      } catch (error) {
        console.log(`❌ ${endpoint} - Status: ${error.response?.status || 'No response'}`);
      }
    }
    
    console.log('\n🔧 Checking Strapi admin endpoints:');
    try {
      const adminResponse = await axios.get(`${STRAPI_URL}/admin/content-manager/collection-types/api::blog-post.blog-post`);
      console.log('✅ Admin content manager accessible');
    } catch (error) {
      console.log('❌ Admin content manager not accessible:', error.response?.status);
    }
    
    console.log('\n📋 Checking users-permissions:');
    try {
      const permissionsResponse = await axios.get(`${STRAPI_URL}/users-permissions/roles`);
      console.log('✅ Users permissions accessible');
      console.log('   Roles:', permissionsResponse.data.roles?.map(r => r.name));
    } catch (error) {
      console.log('❌ Users permissions not accessible:', error.response?.status);
    }
    
  } catch (error) {
    console.error('Error during debugging:', error.message);
  }
}

debugStrapiAPI().catch(console.error);
