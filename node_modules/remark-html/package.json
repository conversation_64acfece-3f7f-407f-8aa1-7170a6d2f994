{"name": "remark-html", "version": "16.0.1", "description": "remark plugin to compile Markdown to HTML", "license": "MIT", "keywords": ["compile", "html", "markdown", "mdast", "plugin", "remark", "remark-plugin", "stringify", "unified"], "repository": "remarkjs/remark-html", "bugs": "https://github.com/remarkjs/remark-html/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "sideEffects": false, "type": "module", "exports": "./index.js", "files": ["lib/", "index.d.ts", "index.js"], "dependencies": {"@types/mdast": "^4.0.0", "hast-util-sanitize": "^5.0.0", "mdast-util-to-hast": "^13.0.0", "hast-util-to-html": "^9.0.0", "unified": "^11.0.0"}, "devDependencies": {"@types/hast": "^3.0.0", "@types/node": "^20.0.0", "c8": "^8.0.0", "commonmark.json": "^0.30.0", "hast-util-from-html": "^2.0.0", "prettier": "^3.0.0", "remark-cli": "^11.0.0", "remark-frontmatter": "^5.0.0", "remark-gfm": "^4.0.0", "remark-github": "^12.0.0", "remark-parse": "^11.0.0", "remark-preset-wooorm": "^9.0.0", "remark-slug": "^7.0.0", "remark-toc": "^9.0.0", "type-coverage": "^2.0.0", "typescript": "^5.0.0", "vfile": "^6.0.0", "xo": "^0.56.0"}, "scripts": {"build": "tsc --build --clean && tsc --build && type-coverage", "format": "remark . --frail --output --quiet && prettier . --log-level warn --write && xo --fix", "prepack": "npm run build && npm run format", "test": "npm run build && npm run format && npm run test-coverage", "test-api": "node --conditions development test/index.js", "test-coverage": "c8 --100 --reporter lcov npm run test-api"}, "prettier": {"bracketSpacing": false, "singleQuote": true, "semi": false, "tabWidth": 2, "trailingComma": "none", "useTabs": false}, "remarkConfig": {"plugins": ["remark-preset-wooorm"]}, "typeCoverage": {"atLeast": 100, "detail": true, "ignoreCatch": true, "strict": true}, "xo": {"overrides": [{"files": ["**/*.ts"], "rules": {"@typescript-eslint/ban-types": "off"}}, {"files": ["test/**/*.js"], "rules": {"no-await-in-loop": "off"}}], "prettier": true, "rules": {"unicorn/prefer-at": "off"}}}