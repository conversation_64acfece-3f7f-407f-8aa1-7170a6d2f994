{"name": "remark", "version": "15.0.1", "description": "markdown processor powered by plugins part of the unified collective", "license": "MIT", "keywords": ["abstract", "ast", "compile", "markdown", "mdast", "parse", "process", "remark", "serialize", "stringify", "syntax", "tree", "unified"], "homepage": "https://remark.js.org", "repository": "https://github.com/remarkjs/remark/tree/main/packages/remark", "bugs": "https://github.com/remarkjs/remark/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "sideEffects": false, "type": "module", "exports": "./index.js", "files": ["index.d.ts", "index.js"], "dependencies": {"@types/mdast": "^4.0.0", "remark-parse": "^11.0.0", "remark-stringify": "^11.0.0", "unified": "^11.0.0"}, "scripts": {}, "typeCoverage": {"atLeast": 100, "detail": true, "ignoreCatch": true, "strict": true}, "xo": {"overrides": [{"files": ["**/*.ts"], "rules": {"@typescript-eslint/triple-slash-reference": "off"}}], "prettier": true}}