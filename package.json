{"name": "manu_blog", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@types/node": "^24.0.10", "@types/react": "^19.1.8", "autoprefixer": "^10.4.21", "axios": "^1.10.0", "eslint": "^9.30.1", "eslint-config-next": "^15.3.4", "gray-matter": "^4.0.3", "next": "^15.3.4", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "remark": "^15.0.1", "remark-html": "^16.0.1", "tailwindcss": "^4.1.11", "typescript": "^5.8.3"}}