import { getBlogPostBySlug, getStrapiMediaUrl } from '@/lib/strapi';
import Image from 'next/image';
import { notFound } from 'next/navigation';

export default async function BlogPost({ params }: { params: { slug: string } }) {
  const post = await getBlogPostBySlug(params.slug);
  
  if (!post) {
    notFound();
  }

  return (
    <article className="max-w-4xl mx-auto px-4 py-8">
      {/* Featured Image */}
      {post.attributes.featuredImage?.data && (
        <div className="mb-8">
          <Image
            src={getStrapiMediaUrl(post.attributes.featuredImage.data.attributes.url)}
            alt={post.attributes.featuredImage.data.attributes.alternativeText || post.attributes.title}
            width={800}
            height={400}
            className="rounded-lg object-cover w-full h-64 md:h-96"
            priority
          />
        </div>
      )}

      {/* Article Header */}
      <header className="mb-8">
        <h1 className="text-4xl md:text-5xl font-bold mb-4">
          {post.attributes.title}
        </h1>
        
        {post.attributes.excerpt && (
          <p className="text-xl text-gray-600 mb-6">
            {post.attributes.excerpt}
          </p>
        )}

        <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500 border-b border-gray-200 pb-6">
          {post.attributes.author && (
            <span className="flex items-center">
              <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
              </svg>
              By {post.attributes.author}
            </span>
          )}
          
          <span className="flex items-center">
            <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
            </svg>
            {new Date(post.attributes.publishedAt).toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            })}
          </span>

          {post.attributes.readingTime && (
            <span className="flex items-center">
              <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
              </svg>
              {post.attributes.readingTime} min read
            </span>
          )}
        </div>
      </header>

      {/* Article Content */}
      <div 
        className="prose prose-lg max-w-none prose-headings:text-gray-900 prose-p:text-gray-700 prose-a:text-blue-600 prose-strong:text-gray-900"
        dangerouslySetInnerHTML={{ __html: post.attributes.content }}
      />

      {/* Tags */}
      {post.attributes.tags && post.attributes.tags.length > 0 && (
        <div className="mt-12 pt-8 border-t border-gray-200">
          <h3 className="text-sm font-semibold text-gray-900 mb-3">Tags</h3>
          <div className="flex flex-wrap gap-2">
            {post.attributes.tags.map((tag, index) => (
              <span
                key={index}
                className="inline-block bg-gray-100 text-gray-800 text-sm px-3 py-1 rounded-full"
              >
                {tag}
              </span>
            ))}
          </div>
        </div>
      )}
    </article>
  );
}

// Generate metadata for SEO
export async function generateMetadata({ params }: { params: { slug: string } }) {
  const post = await getBlogPostBySlug(params.slug);
  
  if (!post) {
    return {
      title: 'Post Not Found',
    };
  }

  return {
    title: post.attributes.metaTitle || post.attributes.title,
    description: post.attributes.metaDescription || post.attributes.excerpt,
    openGraph: {
      title: post.attributes.title,
      description: post.attributes.excerpt,
      type: 'article',
      publishedTime: post.attributes.publishedAt,
      authors: post.attributes.author ? [post.attributes.author] : undefined,
      images: post.attributes.featuredImage?.data ? [
        {
          url: getStrapiMediaUrl(post.attributes.featuredImage.data.attributes.url),
          width: post.attributes.featuredImage.data.attributes.width,
          height: post.attributes.featuredImage.data.attributes.height,
          alt: post.attributes.featuredImage.data.attributes.alternativeText || post.attributes.title,
        }
      ] : undefined,
    },
  };
}
