@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom global styles */
html {
  scroll-behavior: smooth;
}

body {
  font-feature-settings: "rlig" 1, "calt" 1;
}

/* Code blocks styling */
pre {
  overflow-x: auto;
  padding: 1rem;
  border-radius: 0.5rem;
  background-color: #1f2937;
}

code {
  font-family: 'Fira Code', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
}

/* Link hover effects */
a {
  transition: color 0.2s ease-in-out;
}

/* Focus styles for accessibility */
*:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}
