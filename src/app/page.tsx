import Link from "next/link";
import { getBlogPosts, getStrapiMediaUrl } from '@/lib/strapi';
import Image from 'next/image';

export default async function Home() {
  let recentPosts = [];

  try {
    const response = await getBlogPosts(1, 3); // Get 3 most recent posts
    recentPosts = response.data;
  } catch (error) {
    console.error('Failed to fetch recent posts:', error);
  }

  return (
    <div className="max-w-6xl mx-auto px-4 py-8">
      {/* Hero Section */}
      <div className="text-center mb-16">
        <h1 className="text-5xl md:text-6xl font-bold mb-6">Welcome to My Blog</h1>
        <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
          I write about web development, programming, and other things I'm passionate about.
        </p>
        <Link
          href="/blog"
          className="inline-block bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
        >
          Read the Blog
        </Link>
      </div>

      {/* Recent Posts Section */}
      {recentPosts.length > 0 && (
        <section>
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-3xl font-bold">Recent Posts</h2>
            <Link
              href="/blog"
              className="text-blue-600 hover:text-blue-700 font-medium"
            >
              View all posts →
            </Link>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {recentPosts.map((post) => (
              <article key={post.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                <Link href={`/blog/${post.attributes.slug}`}>
                  {post.attributes.featuredImage?.data && (
                    <div className="aspect-video relative">
                      <Image
                        src={getStrapiMediaUrl(post.attributes.featuredImage.data.attributes.url)}
                        alt={post.attributes.featuredImage.data.attributes.alternativeText || post.attributes.title}
                        fill
                        className="object-cover"
                      />
                    </div>
                  )}
                  <div className="p-6">
                    <h3 className="text-xl font-bold mb-2 hover:text-blue-600 transition-colors">
                      {post.attributes.title}
                    </h3>
                    {post.attributes.excerpt && (
                      <p className="text-gray-600 mb-4 line-clamp-3">
                        {post.attributes.excerpt}
                      </p>
                    )}
                    <div className="flex items-center justify-between text-sm text-gray-500">
                      <span>{new Date(post.attributes.publishedAt).toLocaleDateString()}</span>
                      {post.attributes.readingTime && (
                        <span>{post.attributes.readingTime} min read</span>
                      )}
                    </div>
                  </div>
                </Link>
              </article>
            ))}
          </div>
        </section>
      )}

      {/* Empty State */}
      {recentPosts.length === 0 && (
        <section className="text-center py-16">
          <div className="max-w-md mx-auto">
            <h2 className="text-2xl font-bold mb-4">No Posts Yet</h2>
            <p className="text-gray-600 mb-6">
              Start creating amazing content by adding your first blog post in the Strapi admin panel.
            </p>
            <a
              href="http://localhost:1337/admin"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-block bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors"
            >
              Open Strapi Admin
            </a>
          </div>
        </section>
      )}
    </div>
  );
}
