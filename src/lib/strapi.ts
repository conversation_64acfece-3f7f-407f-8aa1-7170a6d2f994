import axios from 'axios';

const STRAPI_URL = process.env.NEXT_PUBLIC_STRAPI_URL || 'http://localhost:1337';

const strapiApi = axios.create({
  baseURL: `${STRAPI_URL}/api`,
  headers: {
    'Content-Type': 'application/json',
  },
});

export interface BlogPost {
  id: number;
  attributes: {
    title: string;
    slug: string;
    excerpt?: string;
    content: string;
    author?: string;
    tags?: string[];
    metaTitle?: string;
    metaDescription?: string;
    readingTime?: number;
    publishedAt: string;
    updatedAt: string;
    featuredImage?: {
      data?: {
        id: number;
        attributes: {
          name: string;
          url: string;
          alternativeText?: string;
          caption?: string;
          width: number;
          height: number;
        };
      };
    };
  };
}

export interface StrapiResponse<T> {
  data: T;
  meta: {
    pagination?: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

// Get all published blog posts
export async function getBlogPosts(page = 1, pageSize = 10): Promise<StrapiResponse<BlogPost[]>> {
  try {
    const response = await strapiApi.get('/blog-posts', {
      params: {
        'pagination[page]': page,
        'pagination[pageSize]': pageSize,
        'populate': 'featuredImage',
        'sort': 'publishedAt:desc',
        'filters[publishedAt][$notNull]': true,
      },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching blog posts:', error);
    throw error;
  }
}

// Get a single blog post by slug
export async function getBlogPostBySlug(slug: string): Promise<BlogPost | null> {
  try {
    const response = await strapiApi.get(`/blog-posts/slug/${slug}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching blog post with slug ${slug}:`, error);
    return null;
  }
}

// Get a single blog post by ID
export async function getBlogPostById(id: number): Promise<StrapiResponse<BlogPost>> {
  try {
    const response = await strapiApi.get(`/blog-posts/${id}`, {
      params: {
        'populate': 'featuredImage',
      },
    });
    return response.data;
  } catch (error) {
    console.error(`Error fetching blog post with ID ${id}:`, error);
    throw error;
  }
}

// Helper function to get the full URL for Strapi media
export function getStrapiMediaUrl(url: string): string {
  if (!url) return '';
  if (url.startsWith('http')) return url;
  return `${STRAPI_URL}${url}`;
}

export default strapiApi;
