const axios = require('axios');

async function testStrapiAPI() {
  const STRAPI_URL = 'http://localhost:1337';
  
  console.log('🧪 Testing Strapi API connection...\n');
  
  try {
    // Test 1: Check if Strapi is running
    console.log('1. Testing Strapi server connection...');
    const healthCheck = await axios.get(`${STRAPI_URL}/_health`);
    console.log('✅ Strapi server is running\n');
  } catch (error) {
    console.log('❌ Strapi server is not accessible');
    console.log('   Make sure Strapi is running on http://localhost:1337\n');
    return;
  }
  
  try {
    // Test 2: Check blog-posts API endpoint
    console.log('2. Testing blog-posts API endpoint...');
    const response = await axios.get(`${STRAPI_URL}/api/blog-posts`);
    console.log('✅ Blog posts API is accessible');
    console.log(`   Found ${response.data.data.length} blog posts\n`);
    
    if (response.data.data.length > 0) {
      console.log('📝 Sample blog post:');
      const post = response.data.data[0];
      console.log(`   Title: ${post.attributes.title}`);
      console.log(`   Slug: ${post.attributes.slug}`);
      console.log(`   Published: ${post.attributes.publishedAt}\n`);
    }
  } catch (error) {
    if (error.response?.status === 404) {
      console.log('❌ Blog posts API endpoint not found (404)');
      console.log('   This usually means:');
      console.log('   - The content type is not properly registered');
      console.log('   - API permissions are not set up');
      console.log('   - The content type name might be different\n');
    } else if (error.response?.status === 403) {
      console.log('❌ Blog posts API access forbidden (403)');
      console.log('   Please set up API permissions:');
      console.log('   1. Go to Strapi Admin → Settings → Users & Permissions → Roles → Public');
      console.log('   2. Enable "find" and "findOne" for Blog-post');
      console.log('   3. Save the changes\n');
    } else {
      console.log('❌ Unexpected error:', error.message);
    }
  }
  
  try {
    // Test 3: List all available API endpoints
    console.log('3. Checking available content types...');
    const contentTypes = await axios.get(`${STRAPI_URL}/api/content-type-builder/content-types`);
    console.log('✅ Available content types in Strapi:');
    
    if (contentTypes.data.data) {
      contentTypes.data.data.forEach(ct => {
        if (ct.uid.startsWith('api::')) {
          console.log(`   - ${ct.uid}`);
        }
      });
    }
  } catch (error) {
    console.log('ℹ️  Could not fetch content types list');
  }
  
  console.log('\n🔧 Next steps:');
  console.log('1. Open Strapi Admin: http://localhost:1337/admin');
  console.log('2. Set up API permissions for public access');
  console.log('3. Create and publish a test blog post');
  console.log('4. Refresh your Next.js site: http://localhost:3001');
}

testStrapiAPI().catch(console.error);
